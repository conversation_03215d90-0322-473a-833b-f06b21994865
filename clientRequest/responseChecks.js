'use strict';

const browserstack = require('../browserstack');
const pubSub = require('../pubSub');
const helper = require('../helper');
const HubLogger = require('../log');
const { isCDP } = require('../socketManagers/validations');
const { LOG_LEVEL: LL, global_registry: registry, sessionTimeout } = require('../constants');

/**
  * Checks if the response socket is connected or not. If it is not connected
  * and the session is a CDP session then mark the session as ABRUPT_CLOSE
  * behavior.
  *
  * @param {Object} keyObject - Session state object
  * @param {chunkedResponse} response - Outgong message response object wrapped
  * in chunkedResponse class.
  * @return {Boolean} Should skip the response write.
 */
function checkResponseOnline(keyObject, response) {
  if (isCDP(keyObject) && !response.clientOnline()) {
    HubLogger.miscLogger('Hub', `Response is not online closing the session for ${keyObject.rails_session_id}`, LL.INFO);
    const { stopUrl, stopPostParams } = helper.buildRailsStopUrlParams(
      keyObject,
      registry[keyObject.rails_session_id],
      'ABRUPT_CLOSE'
    );

    browserstack.postBrowserStack(stopUrl, stopPostParams, undefined, undefined, () => {
      HubLogger.addStopToRawLogs(keyObject, keyObject.rails_session_id, 'ABRUPT_CLOSE', 1, false, undefined, keyObject.errorMessage);
      helper.pingDataToStats(keyObject);
      helper.sessionRemovedFromRegionHook(keyObject);
      pubSub.publish(sessionTimeout, keyObject.rails_session_id);
    });
    return true;
  }
  return false;
}

module.exports = {
  checkResponseOnline
};
