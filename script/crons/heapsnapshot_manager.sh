#!/bin/bash
echo "----------------------------------------"
# Set up directory paths
ROOT_DIR="/ebs/heapsnapshot"
MEMORY_FILE="$ROOT_DIR/memory.txt"
BEFORE_FOLDER="$ROOT_DIR/before"
EVENT_FOLDER="$ROOT_DIR/event"
ARCHIVE_FOLDER="$ROOT_DIR/archive"

# Create necessary directories
mkdir -p "$ROOT_DIR"
mkdir -p "$BEFORE_FOLDER"
mkdir -p "$EVENT_FOLDER"
mkdir -p "$ARCHIVE_FOLDER"

maintain_archive_folder_size() {
    echo "$(date) - Maintaining 'archive' folder..."

    # Calculate the current size of the archive folder
    CURRENT_SIZE=$(du -s "$ARCHIVE_FOLDER" | awk '{print $1}')
    echo "$(date) - Current size of the 'archive' folder: $CURRENT_SIZE KB"

    # Check if the current size is greater than 2GB
    if (( CURRENT_SIZE > 2048000 )); then
        # Remove files in ascending order of their moved time
        find "$ARCHIVE_FOLDER" -type f -printf '%T@ %p\n' | sort -n | awk '{print $2}' | while read -r file; do
            rm "$file"
            CURRENT_SIZE=$(du -s "$ARCHIVE_FOLDER" | awk '{print $1}')
            if (( CURRENT_SIZE <= 2048000 )); then
                break
            fi
        done
    fi
}

# Function to move files to the 'archive' folder
move_to_archive_folder() {
    echo "$(date) - Moving files to the 'archive' folder..."
    mv "$BEFORE_FOLDER"/* "$ARCHIVE_FOLDER"
}

# Function to move files to the 'before' folder
move_to_before_folder() {
    echo "$(date) - Moving files to the 'before' folder..."
    move_to_archive_folder
    mv /ebs/*.heapsnapshot "$BEFORE_FOLDER"
}

# Function to move files to the 'event' folder
move_to_event_folder() {
    echo "$(date) - Moving files to the 'event' folder..."
    RANDOM_NUM=$(date +%s | sha256sum | base64 | head -c 10)
    mkdir -p "$EVENT_FOLDER/event-$RANDOM_NUM"
    mv /ebs/*.heapsnapshot "$EVENT_FOLDER/event-$RANDOM_NUM"
    mv "$BEFORE_FOLDER"/* "$EVENT_FOLDER/event-$RANDOM_NUM"
}

# Get total heap memory consumption
echo "$(date) - Calculating total heap memory consumption..."
TOTAL_HUB_MEM=$(ps aux | grep -v "RSS" | grep "cluster" | grep "SeleniumHub" | grep "x64" | awk '{ sum=sum+$6 } END { print sum }')
TOTAL_HUB_MEM_GB=$(echo "scale=2; $TOTAL_HUB_MEM / (1024 * 1024)" | bc)
echo "$(date) - Total heap memory consumption: $TOTAL_HUB_MEM_GB GB"

# Trigger heap dump if memory consumption is greater than 4 GB
if (( $(echo "$TOTAL_HUB_MEM_GB > 3" | bc -l) )); then
    echo "$(date) - Triggering heap dump..."
    su - app -c "touch SeleniumHub/shared/tmp/triggerHeapDump.txt"
    sleep 15
fi

# Initialize previous total heap memory consumption
PREV_TOTAL_HUB_MEM=0

# Check if the memory file exists
if [ -f "$MEMORY_FILE" ]; then
    # Read previous total heap memory consumption from memory file
    PREV_TOTAL_HUB_MEM=$(cat "$MEMORY_FILE")
fi

# Check if the previous total heap memory consumption is zero
if [ -z "$PREV_TOTAL_HUB_MEM" ]; then
    # Set previous total heap memory consumption equal to the current total memory consumption
    PREV_TOTAL_HUB_MEM="$TOTAL_HUB_MEM"
fi

# Calculate the difference in memory consumption
DIFF=$(echo "$TOTAL_HUB_MEM_GB - $PREV_TOTAL_HUB_MEM" | bc)

# Check if the difference is greater than or equal to 0.5 GB
if (( $(echo "$DIFF >= 0.5" | bc -l) )); then
    move_to_event_folder
else
    move_to_before_folder
fi

# Save current memory consumption for future comparison
echo "$(date) - Saving current memory consumption to memory file..."
echo "$TOTAL_HUB_MEM_GB" > "$MEMORY_FILE"

maintain_archive_folder_size
echo "$(date) - heapsnapshot cron completed successfully."
echo "----------------------------------------"
