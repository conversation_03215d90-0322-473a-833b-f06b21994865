#!/bin/bash
APP_USER=app
CDP_RESTART_TOUCH_FILE=/home/<USER>/ws-reconnect-proxy/current/tmp/restart.txt
CDP_HEALTH_URL='localhost:9696'
LOG_FILE=/ebs/root_cron.log

log() {
  echo "$(date -u) CDP_MONITOR_CRON $1" >> $LOG_FILE
}

touch_file() {
  su - $APP_USER -c "touch $CDP_RESTART_TOUCH_FILE"
}

send_alert() {
  curl -XPOST -d people="automate" -d message="[$HOSTNAME] " -d subject="[$HOSTNAME] $1" -d priority=P2 -d mobile=true -d title="[$HOSTNAME] $1" "https://alert-external.browserstack.com/alert"
}

send_email_alert() {
  curl -XPOST -d people="automate" -d message="[$HOSTNAME] $1" -d subject="[$HOSTNAME] $1" -d priority=P2 -d mobile=false -d title="[$HOSTNAME] $1" "https://alert-external.browserstack.com/alert"
}

log "Starting cdp monitor cron"
health_check=$(curl -Is "$CDP_HEALTH_URL" | head -n 1)
if [ -z "$health_check" ]; then
  log "Restarting CDP as its not running"
  echo "{\"category\":\"ws-reconnect-proxy\",\"kind\":\"cdp-restarted\",\"machine\":\"`hostname`\",\"timestamp\":\"$(date +%s)\",\"region\":\"`hostname | sed 's/node-auto-//'`\"}" | nc -u -w 1 pager.browserstack.com 8553
  touch_file
  send_email_alert "CDP workers are down, restarting..."
  send_alert "CDP workers are down, restarting..."
else
  log "Connection to $CDP_HEALTH_URL successful"
fi
