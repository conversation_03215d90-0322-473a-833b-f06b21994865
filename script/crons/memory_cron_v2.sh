#!/bin/bash

APP_USER=app
LOG_FILE=/ebs/root_cron.log
HUB_RESTART_TOUCH_FILE=/home/<USER>/SeleniumHub/current/tmp/restart.txt

NODE_MEM=`ps aux | grep cluster.js | grep x64 | grep SeleniumHub | awk '{print $6}' | awk '$0>x{x=$0};END{print x}'`
NODE_CPU_PERCENT=`ps aux | grep cluster.js | grep x64 | grep SeleniumHub | awk '{print $3}' | awk '$0>x{x=$0};END{print x}'`
MACHINE_PER_MIN_LOAD=`uptime | awk '{print $(NF-2)}' | tr -d ','`

MAX_NODE_MEM=`ps aux | grep cluster.js | grep x64 | grep SeleniumHub | awk '{print $6}' | awk '$0>x{x=$0};END{print x}'`
TOTAL_HUB_MEM=`ps aux | grep -v "RSS" | grep "cluster" | grep "SeleniumHub" | grep "x64" | awk '{ sum=sum+$6 } END { print sum }'`
MAX_NODE_MEM_PID=`ps aux | grep cluster.js | grep x64 | grep SeleniumHub | awk '{ if(max<$6) { max=$6; pid=$2 } } END { print pid }'`
MAX_NODE_CPU_PERCENT_PID=`ps aux | grep cluster.js | grep x64 | grep SeleniumHub | awk '{ if(max<$3) { max=$3; pid=$2 } } END { print pid }'`
HUB_PROCESS_COUNT=`ps aux | grep -v "RSS" | grep "cluster" | grep "SeleniumHub" | grep "x64" | wc -l`

NODE_MEM_TOTAL_THRESHOLD=4400000
NODE_MEM_PER_PROCESS_CRITICAL_THRESHOLD=1894400

NODE_MEM_THRESHOLD=1468000
NODE_CPU_PERCENT_THRESHOLD=97.0
MACHINE_PER_MIN_LOAD_THRESHOLD=4.5
if [[ $HOSTNAME == *"use3b"* ]]; then
  NODE_MEM_TOTAL_THRESHOLD=4800000
  NODE_MEM_THRESHOLD=1650000
fi

log() {
  echo "$(date -u) $1" >> $LOG_FILE
}
log "Starting v2 cron"

touch_file() {
  su - $APP_USER -c "touch $HUB_RESTART_TOUCH_FILE"
}

# args: [reason_category] [reason_value] [reason_threshold] [node_process_pid] [message]
restart_hub() {
  log "$1 $2 is more than $3 for pid $4, reason = $5"
  FILE=`find $HUB_RESTART_TOUCH_FILE -mmin -16`
  if [ "$FILE" == "$HUB_RESTART_TOUCH_FILE" ] ||  [ $HUB_PROCESS_COUNT -gt 3 ]; then # if modified in last 11 min or already more than 3 process do not do touch tmp
    log "v2 memory cron - reason: $5, not restarting as within 11 min or more than 3 hub process"
    exit 0
  fi

  log "Restarting ..."
  echo "{\"category\":\"$1\",\"kind\":\"hub-restart-v2\",\"data\":\"$2\",\"machine\":\"`hostname`\",\"timestamp\":\"$(date +%s)\",\"region\":\"`hostname | sed 's/node-auto-//'`\"}" | nc -u -w 1 pager.browserstack.com 8553
  touch_file
  exit 0
}

export LOG_FILE
export -f log

log "v2 memory cron = TOTAL_HUB_MEM - $TOTAL_HUB_MEM, MAX_NODE_MEM - $MAX_NODE_MEM, MAX_NODE_MEM_PID - $MAX_NODE_MEM_PID, HUB_PROCESS_COUNT - $HUB_PROCESS_COUNT"

if [ 1 -eq `echo "$NODE_CPU_PERCENT > $NODE_CPU_PERCENT_THRESHOLD" | bc` ]; then
  MESSAGE="cpu breached critical value"
  restart_hub "node_cpu" "$NODE_CPU_PERCENT" "$NODE_CPU_PERCENT_THRESHOLD" "$MAX_NODE_CPU_PERCENT_PID" "$MESSAGE"
elif [ 1 -eq `echo "$MACHINE_PER_MIN_LOAD > $MACHINE_PER_MIN_LOAD_THRESHOLD" | bc` ]; then
  MESSAGE="machine load breached critical value"
  restart_hub "machine_load" "$MACHINE_PER_MIN_LOAD" "$MACHINE_PER_MIN_LOAD_THRESHOLD" "$MAX_NODE_CPU_PERCENT_PID" "$MESSAGE"
elif [ $TOTAL_HUB_MEM -gt $NODE_MEM_TOTAL_THRESHOLD ]; then
  MESSAGE="total memory breached consecutively"
  restart_hub "node_memory_total" "$TOTAL_HUB_MEM" "$NODE_MEM_TOTAL_THRESHOLD" "$MAX_NODE_MEM_PID" "$MESSAGE"
elif [ $MAX_NODE_MEM -gt $NODE_MEM_PER_PROCESS_CRITICAL_THRESHOLD ]; then
  MESSAGE="per process memory breached critical value"
  restart_hub "node_memory_process" "$MAX_NODE_MEM" "$NODE_MEM_PER_PROCESS_CRITICAL_THRESHOLD" "$MAX_NODE_MEM_PID" "$MESSAGE"
fi

exit 0
