#!/bin/bash

# Need to run as root for gcore

APP_USER=app
LOG_FILE=/ebs/root_cron.log
APPLICATION_RESTART_TOUCH_FILE=/home/<USER>/SeleniumHub/current/tmp/restart-uploader.txt

NODE_MEM=`ps aux | grep kafkaUploader.js | awk '{print $6}' | awk '$0>x{x=$0};END{print x}'`
NODE_CPU_PERCENT=`ps aux | grep kafkaUploader.js | awk '{print $3}' | awk '$0>x{x=$0};END{print x}'`
MACHINE_PER_MIN_LOAD=`uptime | awk '{print $(NF-2)}' | tr -d ','`
MAX_NODE_MEM_PID=`ps aux | grep kafkaUploader.js | awk '{ if(max<$6) { max=$6; pid=$2 } } END { print pid }'`
MAX_NODE_CPU_PERCENT_PID=`ps aux | grep kafkaUploader.js | awk '{ if(max<$3) { max=$3; pid=$2 } } END { print pid }'`

NODE_MEM_THRESHOLD=1468000
NODE_CPU_PERCENT_THRESHOLD=97.0
MACHINE_PER_MIN_LOAD_THRESHOLD=4.5

# args: string log to write
log() {
  echo "$(date -u) $1" >> $LOG_FILE
}
log "Starting cron"

# args: [reason_category] [reason_value] [reason_threshold] [node_process_pid]
restart_application() {
  log "$1 $2 is more than $3 for pid $4, restarting ..."
  echo "{\"category\":\"$1\",\"kind\":\"hub-kafka-uploader-restart\",\"data\":\"$2\",\"timestamp\":\"$(date +%s)\",\"region\":\"`hostname`\"}" | nc -u -w 1 pager.browserstack.com 8553
  su - $APP_USER -c "touch $APPLICATION_RESTART_TOUCH_FILE"
  exit 0
}

export LOG_FILE
export -f log

if [ $NODE_MEM -gt $NODE_MEM_THRESHOLD ]; then
  restart_application "node_memory" "$NODE_MEM" "$NODE_MEM_THRESHOLD" "$MAX_NODE_MEM_PID"
fi

if [ 1 -eq `echo "$NODE_CPU_PERCENT > $NODE_CPU_PERCENT_THRESHOLD" | bc` ]; then
  restart_application "node_cpu" "$NODE_CPU_PERCENT" "$NODE_CPU_PERCENT_THRESHOLD" "$MAX_NODE_CPU_PERCENT_PID"
fi

if [ 1 -eq `echo "$MACHINE_PER_MIN_LOAD > $MACHINE_PER_MIN_LOAD_THRESHOLD" | bc` ]; then
  restart_application "machine_load" "$MACHINE_PER_MIN_LOAD" "$MACHINE_PER_MIN_LOAD_THRESHOLD" "$MAX_NODE_CPU_PERCENT_PID"
fi

exit 0
