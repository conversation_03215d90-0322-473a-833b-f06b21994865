#!/bin/bash

# Need to run as root for gcore

APP_USER=app
LOG_FILE=/ebs/root_cron.log
APPLICATION_RESTART_TOUCH_FILE=/home/<USER>/SeleniumHub/current/tmp/restart-uploader.txt

DISK_USAGE=`df -P /ebs/kafka/saving | grep /ebs | awk '{ print $5 }' | sed 's/%//g' | tr -d '\n'`
DISK_USAGE_THRESHOLD=85

# args: string log to write
log() {
  echo "$(date -u) $1" >> $LOG_FILE
}
log "Starting disk check cron"

# args: [reason_category] [reason_value] [reason_threshold]
trigger_failover() {
  log "$1 $2 is more than $3, triggering failover ..."
  echo "{\"category\":\"$1\",\"kind\":\"hub-kafka-uploader-failover\",\"data\":\"$2\",\"timestamp\":\"$(date +%s)\",\"region\":\"`hostname`\"}" | nc -u -w 1 pager.browserstack.com 8553
  su - $APP_USER -c "touch $APPLICATION_RESTART_TOUCH_FILE"
  curl -d people=automate -d subject="KAFKA_UPLOADER `hostname` needs failover" -d message="$1 $2 is more than $3. I will try to restart uploaders on `hostname`" -d mobile=true -d priority="P2" -d grouping="false" https://alert-external.browserstack.com/alert
  exit 0
}

export LOG_FILE
export -f log

if [ $DISK_USAGE -ge $DISK_USAGE_THRESHOLD ]; then
  trigger_failover "disk_usage" "$DISK_USAGE" "$DISK_USAGE_THRESHOLD"
fi

exit 0
