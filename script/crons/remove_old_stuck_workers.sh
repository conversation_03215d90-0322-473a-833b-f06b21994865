send_alert() {
  curl -XPOST -d people="automate" -d message="[$HOSTNAME] " -d subject="[$HOSTNAME] $1" -d priority=P3 -d mobile=true -d title="[$HOSTNAME] $1" "https://alert-external.browserstack.com/alert"
}
HUB_RESTART_TOUCH_FILE=/home/<USER>/SeleniumHub/current/tmp/restart.txt
FILE=`find $HUB_RESTART_TOUCH_FILE -mmin -180`

if [ "$FILE" == "$HUB_RESTART_TOUCH_FILE" ]; then # if modified in last 180 min do not kill as more then 3 workers are due to touch tmp
  echo "not killing as touched in last 180 min"
  exit 0
fi

# sort hub workers based on start time and kill workers which are not active workers and running for more than 5 hours
OLD_STUCK_HUB_WORKERS=`ps aux | grep cluster.js | grep x64 | grep SeleniumHub | sort -k 10n | awk 'NR > 3 {split($9,time,":"); now = systime(); process_time = now - (time[1]*3600 + time[2]*60); if(process_time >= 18000) print $2}'`

if [ -z "$OLD_STUCK_HUB_WORKERS" ]; then
  echo "No old stuck hub workers found"
else
  for pid in $OLD_STUCK_HUB_WORKERS; do
    echo "Killing old stuck hub worker with pid $pid"
    # kill -9 $pid
    send_alert "Please check and kill old stuck hub worker with pid $pid"
  done
fi
