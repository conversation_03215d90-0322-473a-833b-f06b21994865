-- Runs on timeoutManger consumer context
-- KEYS:
-- 1: KeyName of redis list to check
-- ARGVS:
-- 1: Current pid of application.
local masterKey = KEYS[1];
local currentPid = ARGV[1];

local masterKeyExists = redis.call("EXISTS", masterKey);

if masterKeyExists == 1 then
  local redisPid = redis.call("HGET", masterKey, "pid");

  if redisPid ~= nil and redisPid ~= false and currentPid + 0 == redisPid + 0 then
    redis.call("DEL", masterKey);
    return "Removed from redis";
  end
end

return "I was not master";
