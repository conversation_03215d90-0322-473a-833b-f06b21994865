-- Goal: Perform arithmetic operations on a redis string

-- KEYS:
-- 1: redis_key: redis Key to perform action on and save to itself
-- ARGVS:
-- 1: action: keyword of the action that is needed to be performed. supported: multiply. divide
-- 2: factor: additional value to perform action on
-- 3: min_value: lowest value the key can have
-- 4: max_value: largest value the key can have

local redis_key = KEYS[1];

local action = ARGV[1];
local factor = ARGV[2];
local min_value = ARGV[3];
local max_value = ARGV[4];

local current_value = tonumber(redis.call('GET', redis_key) or 0);
local save_value;

if action == 'multiply' then
  save_value = math.ceil(current_value * factor)
elseif action == 'divide' then
  save_value = math.ceil(current_value / factor)
else
  return { "operation " .. action .. " is not supported", current_value, current_value };
end

if max_value ~= nil and save_value > tonumber(max_value) then
  save_value = tonumber(max_value)
end

if min_value ~= nil and save_value < tonumber(min_value) then
  save_value = tonumber(min_value)
end

redis.call('SET', redis_key, save_value)

return { "done", current_value, save_value };
