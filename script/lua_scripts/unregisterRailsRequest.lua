-- Needed to unregister a request to rails from hub
-- KEYS:
-- 1: key name the set which keeps track of ids of requests in the system
-- ARGVS:
-- 1: id of the request that was made
-- RETURNS:
-- isCurrentlyTracked , trackSetSize

local requestTrackSetKey = KEYS[1];

local requestId = ARGV[1];

local isCurrentlyTracked = redis.call("SREM", requestTrackSetKey, requestId);

local requestTrackSetSize = tonumber(redis.call("SCARD",requestTrackSetKey)) or -1;

return {isCurrentlyTracked, requestTrackSetSize};
