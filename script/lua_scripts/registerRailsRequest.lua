-- Needed to register a request to rails from hub
-- KEYS:
-- 1: key name for max count of rails requests
-- 2: key name the set which keeps track of ids of requests in the system
-- ARGVS:
-- 1: id of the request that we will be made
-- RETURNS:
-- needToQueueRequest, maxRequestCount, requestTrackSetSize
-- Note: boolean is returned as 0 (false) / 1 (true) in node

local maxRequestCountKey = KEYS[1];
local requestTrackSetKey = KEYS[2];

local requestId = ARGV[1];

local maxRequestCount = tonumber(redis.call("GET", maxRequestCountKey)) or 100;

local requestTrackSetSize = tonumber(redis.call("SCARD",requestTrackSetKey)) or 0;

local needToQueue = 1;

if requestTrackSetSize < maxRequestCount then
  redis.call("SADD", requestTrackSetKey, requestId);
  needToQueue = 0;
end

if needToQueue == 0 then
  requestTrackSetSize = requestTrackSetSize + 1;
end

return {needToQueue, maxRequestCount, requestTrackSetSize};
