-- Runs on timeoutManger consumer context
-- KEYS:
-- 1: key name for determining master for a particular CONSUMER_ID. Format: masterName_0
-- ARGVS:
-- 1: Integer Current Timestamp from application. must be integer epoch in ms
-- 2: Integer Current pid of application.
-- 3: Integer Time in ms within which ack is required.
-- 4: String  Hostname of the machine trying to become master.
local masterKey = KEYS[1];
local currentTimestamp = ARGV[1];
local currentPid = ARGV[2];
local minAckPeriod = ARGV[3];
local hostname = ARGV[4];

local masterKeyExists = redis.call("EXISTS", masterKey);

local isKafkaUploader = false;

if string.find(masterKey, "kafka") then
  isKafkaUploader = true;
end

if masterKeyExists == 1 then
  local timestamp = redis.call("HGET", masterKey, "timestamp");
  local pid = redis.call("HGET", masterKey, "pid");
  local masterHostname = redis.call("GET", masterKey .. "__hostname");

  if pid == nil or pid == false then
    -- I can still become master as the key should always have pid and is in illegal state
    redis.call("DEL", masterKey);
  elseif currentTimestamp - timestamp < minAckPeriod + 0 then
    --  cases for monitoringService and timeoutManager
    if not isKafkaUploader then
      return "Everything Running Fine. Current master pid: " .. pid;
    -- case for kafkaUploader
    elseif isKafkaUploader and masterHostname ~= hostname then
      return "Everything Running Fine. Current master pid: " .. pid .. ", Current master hostname: " .. masterHostname;
    end
  end
end

-- I can become master
redis.call("HMSET", masterKey, "timestamp", currentTimestamp, "pid", currentPid);
if hostname then
  redis.call("SET", masterKey .. "__hostname", hostname);
end
local returnMessage = "I can become master. Current master pid: " .. currentPid;
if isKafkaUploader then
  returnMessage = returnMessage .. ", Current master hostname: " .. hostname;
end
return returnMessage;
