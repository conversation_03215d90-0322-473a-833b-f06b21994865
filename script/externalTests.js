'use strict';

const { Builder, By } = require('selenium-webdriver');
const exec = require('child_process').exec;
const os = require('os');
const udp = require('dgram').createSocket('udp4');

const HOSTNAME = os.hostname();
const REGION = HOSTNAME.match(/use|euw|usw|apse|aps/) && HOSTNAME.match(/use|euw|usw|apse|aps/)[0];
const CURRENT_DATE_STRING = new Date().toISOString();
const TTL = 5 * 60 * 1000;
const HUBS = ['hub', 'hub-cloud', 'hub-use', 'hub-usw', 'hub-euw', 'hub-aps', 'hub-apse', 'hub-use-1a-internal', 'hub-use-only', 'hub-euw-1a-internal'];

// ************ -> huswnginx3
// ************* -> huswnginx4
const EXPERIMENTS = ['hub-use-1a-node-5','hub-use-1a-node-6','hub-use-1a-node-7','hub-euw-1a-node-3','hub-euw-1a-node-4'];
const ALERT_RECEIVERS = ['automate'];
const EXCEPTIONS = [];
const KIND = 'external-monitoring-failed';
const ZOMBIE_PORT = 8000;
const ZOMBIE_SERVER = 'zombie.browserstack.com'

let currentTestingHub = 'NONE';

const log = message => console.log(`[${new Date()}] ${message}`);

const sendAlert = (subject, message, mobile, cb) => {
  const commandToExec = `curl -d people="${ALERT_RECEIVERS.join(',')}" -d subject="${subject}" -d mobile=${mobile} -d message="${message}" https://alert-external.browserstack.com/alert`;
  try {
    log(`Sending alert - ${subject} - ${message}`);
    //exec(commandToExec);
  } catch (e) {
    log(`Exception occured while sending alert: ${e}`);
  } finally {
    if (typeof cb === 'function') {
      cb();
    }
  }
};

const executeNetworkTest = async function(driver, browserName, sessionId, hub) {
  try {
    await driver.get('https://www.fast.com');
    await driver.findElement(By.id('speed-value'));
    await driver.getTitle();
    await driver.quit();
  } catch (error) {
    sendDataToZombie(sessionId, hub, browserName, `Network Test Failed ${error.name}  ${error.message.split(':')[0]}`);
    EXCEPTIONS.push({
      hub,
      error,
      sessionId
    });
  }
};

const logUDPFailure = function (error, _bytes) {
  if (error) {
    log(`Error posting udp data: ${error.toString()}`);
  }
};

const pingZombie = (dataJsonObject) => {
  const dataStringified = JSON.stringify(dataJsonObject);
  log(`Zombie service:${dataStringified}`);

  try {
    const byteData = Buffer.from(dataStringified);
    udp.send(
      byteData, 0, byteData.length,
      ZOMBIE_PORT, ZOMBIE_SERVER, logUDPFailure,
    );
  } catch (err) {
    log(`Exception ${err.toString()} while sending data: ${dataStringified}`);
  }
}

const sendDataToZombie = (sessionId, hub, browser, error) => {
  pingZombie({
    "timestamp": (Math.round(new Date().getTime() / 1000).toString()),
    "session_id": sessionId,
    "browser": browser,
    "region" : REGION,
    "kind": KIND,
    "data": hub,
    "error" : error
  });
}

const executeTest = (hub, expirement, extraCaps) => new Promise(async (resolve) => {
  currentTestingHub = hub;
  const hubUrl = hub.split('.').length !== 4 ? `http://${hub}.browserstack.com/wd/hub` : `http://${hub}/wd/hub`;
  let sessionId = null;
  let driver = null;
  let browserName = null;
  let logIdentifier = '';
  if (!extraCaps) {
    extraCaps = {
      browserName: 'Chrome'
    }
  }
  try {
    const capabilities = {
      browserName: 'Chrome',
      'browserstack.user': '<bstackuser>',
      'browserstack.key': '<bstackkey>',
      build: `External monitoring - ${REGION} - ${CURRENT_DATE_STRING.slice(0, 10)}`,
      name: `Start time - ${CURRENT_DATE_STRING} - ${hub}`,
      'browserstack.debug': true,
      'browserstack.console': 'verbose'
    };
    Object.assign(capabilities,extraCaps)
    driver = await new Builder().usingServer(hubUrl).withCapabilities(capabilities).build();
    sessionId = (await driver.getSession()).getId();
    logIdentifier = sessionId;
    browserName = capabilities.browserName;
    await driver.get('https://www.google.com');
    try {
      await driver.findElement(By.name('nosuchelement'));
    } catch (error) {
      log('Catching exception while finding the element by name');
    }
    await driver.getTitle();
    await driver.quit();
    await driver.executeScript(`
      for(let i=0; i<50; i++) {
        console.log("Browserstack console log test");
      }
    `);
    log(`Finished external monitoring test for ${hub}`);
  } catch (error) {
    log(`${logIdentifier} Exception occured while executing test for ${hub}: ${error}`);
    sendDataToZombie(sessionId, hub, browserName, `${error.name}  ${error.message.split(':')[0]}`);
    EXCEPTIONS.push({
      hub,
      error,
      sessionId,
      expirement
    });
    if (error.name.match(/NoSuchElementError/))
    {
     await executeNetworkTest(driver, browserName, sessionId, hub);
    }
  } finally {
    resolve();
  }
});

const start = async () => {
  log('Starting external monitoring test...');
  let hubsCompleted = false
  await Promise.all(HUBS.map(hub => executeTest(hub, false)));
  hubsCompleted = true;
  const experimentCaps = [
    null
  ]
  for (const extraCaps of experimentCaps) {
     await Promise.all(EXPERIMENTS.map(hub => executeTest(hub, true, extraCaps)));
  }

  if (EXCEPTIONS.length > 0) {
    let mobile = EXCEPTIONS.some(e => e.expirement == false) ? 'critical' : 'false'
    sendAlert(
      `External Monitoring Broke in ${REGION} for ${EXCEPTIONS.map(e => e.hub).join(',')}`,
      `Running in ${REGION} region and broke for ${EXCEPTIONS.map(e => e.hub).join(',')}.<br/>${EXCEPTIONS.map(({ hub, error, sessionId }) => `Session: ${sessionId}<br/>Exception in ${hub}<br/>${error}<br/>${'-'.repeat(80)}<br/>`)}`,
      mobile
    );
  }

  udp.close();

  setTimeout(() => {
    sendDataToZombie(null, null, null, `Test Timedout for ${currentTestingHub} running in ${HOSTNAME}.`);
    if(!hubsCompleted) {
      sendAlert(
        `External Monitoring Broke running in ${REGION} region`,
        `Test Timedout for ${currentTestingHub} running in ${HOSTNAME} at ${CURRENT_DATE_STRING}.`,
        true,
        () => {
          process.exit(1);
        }
      );
    }
  }, TTL).unref();
};

if (require.main === module) {
  start();
}
