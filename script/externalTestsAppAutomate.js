// nvm use default 16.20.0
// npm install webdriverio 7
// npm i node-domexception
'use strict';
const wdio = require('webdriverio');
const exec = require('child_process').exec;
const os = require('os');
const udp = require('dgram').createSocket('udp4');

const HOSTNAME = os.hostname();
const REGION = HOSTNAME.match(/use|euw|usw|apse|aps/) && HOSTNAME.match(/use|euw|usw|apse|aps/)[0];
const CURRENT_DATE_STRING = new Date().toISOString();
const TTL = 5 * 60 * 1000;
// const HUBS = ['hub', 'hub-cloud', 'hub-use', 'hub-usw', 'hub-euw', 'hub-aps', 'hub-apse', 'hub-use-1a-internal', 'hub-use-only', 'hub-euw-1a-internal'];
const HUBS = ['hub', 'hub-cloud', 'hub-use', 'hub-usw', 'hub-euw', 'hub-aps', 'hub-apse', 'hub-use-only'];
// ************ -> huswnginx3
// ************* -> huswnginx4
// const EXPERIMENTS = ['hub-use-1a-node-7','hub-euw-1a-node-3','hub-euw-1a-node-4'];
const EXPERIMENTS = [];
const ALERT_RECEIVERS = ['app-automate', 'dheeraj'];
const EXCEPTIONS = [];
const KIND = 'external-monitoring-failed';
const ZOMBIE_PORT = 8000;
const ZOMBIE_SERVER = 'zombie.browserstack.com'
const USERNAME = '<username>'
const ACCESS_KEY = '<accessKey>'
const USER_ID = <user_id>
const APP_ID = {
  'android' : 'bs://c700ce60cf13ae8ed97705a55b8e022f13c5827c',
  'ios' : 'bs://444bd0308813ae0dc236f8cd461c02d3afa7901d'
}
const IS_DDA=false
const REGEX_PATTERNS = {
  'android' : { device: '.*', osVersion: '^((?!12|13|14)[0-9]*)' },
  'ios' : { device: '.*', osVersion: '^((?!15|16|11|10)[0-9]*)' },
}
const DEVICES = {
  'android' : ['Samsung Galaxy S10-9.0', 'Samsung Galaxy A10-9.0', 'Oppo Reno 3 Pro-10.0', 'Samsung Galaxy S20-10.0'],
  'ios' : ['iPhone 11-13.0', 'iPhone 11-15.0', 'iPhone XS-12.0', 'iPad Mini 2019-13.0'],
}
const ANDROID_OS_REGEX = '.*'
const ANDROID_OS_VERSION_REGEX = '^((?!12|13|14)[0-9]*)$'
const IOS_OS_REGEX = '.*'
const IOS_OS_VERSION_REGEX = '^((?!15|16)[0-9]*)$'

let currentTestingHub = 'NONE';

const log = message => console.log(`[${new Date()}] ${message}`);

const sendAlert = (subject, message, mobile, cb) => {
  const commandToExec = `curl -d people="${ALERT_RECEIVERS.join(',')}" -d subject="${subject}" -d mobile=${mobile} -d message="${encodeURIComponent(message)}" https://alert-external.browserstack.com/alert`;
  try {
    log(`Sending alert - ${subject} - ${message}`);
    exec(commandToExec);
  } catch (e) {
    log(`Exception occured while sending alert: ${e}`);
  } finally {
    if (typeof cb === 'function') {
      cb();
    }
  }
};

const executeNetworkTest = async function(driver, opts, hub, sessionId) {
  try {
    driver = await wdio.remote(opts);
    const sessionId = await driver.sessionId;
    await driver.getPageSource();
    await driver.deleteSession();
  } catch (error) {
    sendDataToZombie(sessionId, hub, os, `AppAutomate Network Test Failed ${error.name}  ${error.message.split(':')[0]}`);
    EXCEPTIONS.push({
      hub,
      error,
      sessionId
    });
  }
};

const logUDPFailure = function (error, _bytes) {
  if (error) {
    log(`Error posting udp data: ${error.toString()}`);
  }
};

const pingZombie = (dataJsonObject) => {
  const dataStringified = JSON.stringify(dataJsonObject);
  log(`Zombie service:${dataStringified}`);

  try {
    const byteData = Buffer.from(dataStringified);
    // udp.send(
    //   byteData, 0, byteData.length,
    //   ZOMBIE_PORT, ZOMBIE_SERVER, logUDPFailure,
    // );
  } catch (err) {
    log(`Exception ${err.toString()} while sending data: ${dataStringified}`);
  }
}

const sendDataToZombie = (sessionId, hub, os, error) => {
  pingZombie({
    "timestamp": (Math.round(new Date().getTime() / 1000).toString()),
    "session_id": sessionId,
    "os": os,
    "region" : REGION,
    "kind": KIND,
    "data": hub,
    "error" : error
  });
}

const executeTest = (hub, expirement, extraCaps) => new Promise(async (resolve) => {
  currentTestingHub = hub;
  const hubUrl = hub.split('.').length !== 4 ? `${hub}.browserstack.com` : `${hub}`;
  let sessionId = null;
  let driver = null;
  let logIdentifier = '';
  const osArray = ['android', 'android', 'ios'];
  const os = osArray[Math.floor(Math.random()*osArray.length)];
  let device;
  let osVersion;
  if (IS_DDA === true) {
    device = REGEX_PATTERNS[os].device;
    osVersion = REGEX_PATTERNS[os].osVersion;
  } else {
    const value = DEVICES[os][Math.floor(Math.random()*DEVICES[os].length)];
    device = value.split('-')[0];
    osVersion = value.split('-')[1];
  }
  const appURL = APP_ID[os];
    const capabilities = {
      'appium:deviceName': device,
      'appium:platformVersion': osVersion,
      'platformName': os,
      'bstack:options': {
        buildName: `AppAutomateExternalMonitoring-${REGION}-${CURRENT_DATE_STRING.slice(0, 10)}`,
        sessionName: `Start time - ${CURRENT_DATE_STRING} - ${hub}`,
        debug: false,
        appiumLogs: false,
        deviceLogs: false,
        video: false
      },
      'appium:app': appURL
    };
    const opts = {
      path: '/wd/hub',
      port: 80,
      hostname: hubUrl,
      protocol: 'http',
      user: USERNAME,
      key: ACCESS_KEY,
      logLevel: 'silent',
      capabilities: capabilities,
      waitForTimeout: 240000,
      connectionRetryCount: 0,
      connectionRetryTimeout: 900000
    };
    try {
      let driver = await wdio.remote(opts);
      sessionId = await driver.sessionId;
      logIdentifier = sessionId;
      await driver.getPageSource();
      await driver.deleteSession();
      log(`Finished AppAutomate external monitoring test for ${hub}`);
    } catch (error) {
      log(`${logIdentifier} Exception occured while executing test for AppAutomate ${hub}: ${error}`);
      sendDataToZombie(sessionId, hub, os, `${error.name}  ${error.message.split(':')[0]}`);
      await executeNetworkTest(driver, opts, hub, sessionId);
    } finally {
      resolve();
    }
});

const start = async () => {
  log('Starting AppAutomate external monitoring test...');
  let hubsCompleted = false
  await Promise.all(HUBS.map(hub => executeTest(hub, false)));
  hubsCompleted = true;
  const experimentCaps = [
    null
  ]
  for (const extraCaps of experimentCaps) {
     await Promise.all(EXPERIMENTS.map(hub => executeTest(hub, true, extraCaps)));
  }

  if (EXCEPTIONS.length > 0) {
    let mobile = EXCEPTIONS.some(e => e.expirement == false) ? 'critical' : 'false';
    const buildURL = `https://app-automate.browserstack.com/dashboard/v2/search?query=AppAutomateExternalMonitoring-${REGION}-${CURRENT_DATE_STRING.slice(0, 10)}&type=builds&user_id=${USER_ID}`;
    sendAlert(
      `AppAutomate External Monitoring Broke in ${REGION} for ${EXCEPTIONS.map(e => e.hub).join(',')}`,
      `AppAutomate Running in ${REGION} region and broke for ${EXCEPTIONS.map(e => e.hub).join(',')}.<br/>${EXCEPTIONS.map(({ hub, error, sessionId }) => `BuildURL: ${buildURL}<br/>Session: ${sessionId}<br/>Exception in ${hub}<br/>${error}<br/>${'-'.repeat(80)}<br/>`)}`,
      mobile
    );
  }

  udp.close();

  setTimeout(() => {
    sendDataToZombie(null, null, null, `AppAutomate Test Timedout for ${currentTestingHub} running in ${HOSTNAME}.`);
    if(!hubsCompleted) {
      sendAlert(
        `AppAutomate External Monitoring Broke running in ${REGION} region`,
        `AppAutomate Test Timedout for ${currentTestingHub} running in ${HOSTNAME} at ${CURRENT_DATE_STRING}.`,
        true,
        () => {
          process.exit(1);
        }
      );
    }
  }, TTL).unref();
};

if (require.main === module) {
  start();
}
