# ---- DC MACHINES ----

alias hapsenginx3='ssh 173.234.104.2'
alias hapsenginx5='ssh 173.234.104.3'
alias hapseredis5='ssh 173.234.104.13'
alias hapseredis6='ssh 173.234.104.14'
alias hapse4='ssh 173.234.104.40'
alias hapse6='ssh 173.234.104.41'
alias hapseservices5='ssh 173.234.104.23' #services-auto-005-dcp-apse2a-prod.browserstack.com
alias hapseservices4='ssh 173.234.104.24' #services-auto-004-dcp-apse2a-prod.browserstack.com
alias hapseredis8='ssh 173.234.104.48' # ai redis
alias hapseredis7='ssh 173.234.104.49' # ai redis

alias hapsnginx5='ssh 103.126.7.240'
alias hapsnginx4='ssh 103.126.7.239'
alias hapsredis6='ssh 103.126.7.234'
alias hapsredis7='ssh 103.126.7.235'
alias haps5='ssh 103.126.7.237'
alias haps8='ssh 103.126.7.238'
alias hapsservices3='ssh 103.126.7.56' #services-automate-001-dcp-aps1a-prod.browserstack.com
alias hapsservices4='ssh 103.126.7.57' #services-automate-002-dcp-aps1a-prod.browserstack.com
alias hapsredis9='ssh 103.126.7.75' # ai redis
alias hapsredis8='ssh 103.126.7.73' # ai redis

alias huse3bnginx1='ssh 74.50.105.103'
alias huse3bnginx2='ssh 74.50.105.109'
alias huse3bnginx3='ssh 74.50.105.111'
alias huse3bnginx4='ssh 74.50.105.112'
alias huse3bnginx5='ssh 74.50.105.66'
alias huse3bnginx6='ssh 74.50.105.100'
alias huse3bnginx7='ssh 74.50.105.101'
alias huse3bredis1='ssh 74.50.105.115'
alias huse3bredis2='ssh 74.50.105.108'
alias huse3bredis4='ssh 74.50.105.218'
alias huse3bredis7='ssh 74.50.105.225'
alias huse3bredis6='ssh 74.50.105.224' # ai redis
alias huse3bredis5='ssh 74.50.105.223' # ai redis
alias huse3b2='ssh 74.50.105.80'
alias huse3b3='ssh 74.50.105.89'
alias huse3b4='ssh 74.50.105.104'
alias huse3b5='ssh 74.50.105.105'
alias huse3b6='ssh 74.50.105.106'
alias huse3b7='ssh 74.50.105.107'
alias huse3b8='ssh 74.50.105.110'
alias huse3b9='ssh 74.50.105.113'
alias huse3b10='ssh 74.50.105.81'
alias huse3b11='ssh 74.50.105.82'
alias huse3b12='ssh 74.50.105.69'
alias huse3b13='ssh 74.50.105.70'
alias huse3b14='ssh 74.50.105.72'
alias huse3b15='ssh 74.50.105.75'
alias huse3b16='ssh 74.50.105.219'
alias huse3b18='ssh 74.50.105.220'
alias huse3b19='ssh 74.50.105.98'
alias huse3bservices2='ssh 74.50.105.114' #services-auto-002-dcp-use3b-prod.browserstack.com
alias huse3bservices3='ssh 74.50.105.67' #services-auto-003-dcp-use3b-prod.browserstack.com
alias huse3bservices4='ssh 74.50.105.73' #services-auto-004-dcp-use3b-prod.browserstack.com
alias huse3bservices5='ssh 74.50.105.226' #services-auto-005-dcp-use3b-prod.browserstack.com

alias husw2anginx1='ssh 23.83.161.26'
alias husw2anginx2='ssh 23.83.161.28'
alias husw2aredis1='ssh 23.83.161.32'
alias husw2aredis2='ssh 23.83.161.34'
alias husw2a1='ssh 23.83.161.31'
alias husw2a2='ssh 23.83.161.33'
alias husw2a3='ssh 23.83.161.35'
alias husw2a4='ssh 23.83.161.41'
alias husw2aservices1='ssh 23.83.161.30' #services-auto-001-dcp-usw2a-prod.browserstack.com
alias husw2aservices2='ssh 23.83.161.40' #services-auto-001-dcp-usw2a-prod.browserstack.com
alias husw2aredis4='ssh 23.83.161.49' # ai redis
alias husw2aredis3='ssh 23.83.161.48' # ai redis

alias heuwnginx3='ssh 185.44.131.206'
alias heuwnginx4='ssh 185.44.131.207'
alias heuwnginx5='ssh 185.44.131.208'
alias heuwnginx6='ssh 45.148.169.175' # update from infra config
alias heuwredis6='ssh 45.148.168.10'
alias heuwredis9='ssh 45.148.168.45'
alias heuwredis7='ssh 45.148.168.35'
alias heuwredis8='ssh 45.148.168.41'
alias heuw17='ssh 45.148.168.33'
alias heuw21='ssh 45.148.169.187'
alias heuw5='ssh 45.148.168.20'
alias heuw6='ssh 45.148.168.21'
alias heuw18='ssh 45.148.168.22'
alias heuw9='ssh 45.148.168.23'
alias heuw10='ssh 45.148.169.189'
alias heuw11='ssh 45.148.168.25'
alias heuw12='ssh 45.148.168.26'
alias heuw19='ssh 45.148.168.36'
alias heuw14='ssh 45.148.168.37'
alias heuw15='ssh 45.148.168.38'
alias heuw20='ssh 45.148.169.190'
alias heuwservices3='ssh 45.148.168.7' #services-auto-003-dcp-euw1a-prod.browserstack.com
alias heuwservices9='ssh 45.148.168.48' #services-auto-009-dcp-euw1a-prod.browserstack.com
alias heuwservices7='ssh 45.148.168.40' #services-auto-007-dcp-euw1a-prod.browserstack.com
alias heuwservices8='ssh 45.148.169.170' #services-auto-008-dcp-euw1a-prod.browserstack.com
alias heuwredis11='ssh 45.148.168.34' # ai redis
alias heuwredis10='ssh 45.148.169.167' # ai redis


# Kafka & Zookeeper
alias keuw3='ssh 45.148.168.6' #kafkab-automate-003-dcp-euw1a-prod.browserstack.com
alias keuw4='ssh 45.148.168.12' #kafkab-automate-004-dcp-euw1a-prod.browserstack.com
alias keuw5='ssh 45.148.168.39' #kafkab-automate-004-dcp-euw1a-prod.browserstack.com
alias zeuw='ssh 45.148.168.17' #zookeeper-auto-002-dcp-euw1a-prod.browserstack.com

alias kaps3='ssh 103.126.7.55' #kafkab-automate-003-dcp-aps1a-prod.browserstack.com
alias kaps6='ssh 103.126.7.60' #kafkab-automate-006-dcp-aps1a-prod.browserstack.com
alias zaps='ssh 103.126.7.59' #zookeeper-automate-003-dcp-aps1a-prod.browserstack.com

alias kapse6='ssh 173.234.104.20' #kafkab-auto-006-dcp-apse2a-prod.browserstack.com
alias kapse7='ssh 173.234.104.21' #kafkab-auto-007-dcp-apse2a-prod.browserstack.com
alias zapse='ssh 173.234.104.28' #zookeeper-auto-002-dcp-apse2a-prod.browserstack.com

alias kuse3b1='ssh 74.50.105.116' #kafkab-auto-001-dcp-use3b-prod.browserstack.com
alias kuse3b2='ssh 74.50.105.117' #kafkab-auto-002-dcp-use3b-prod.browserstack.com
alias kuse3b3='ssh 74.50.105.118' #kafkab-auto-003-dcp-use3b-prod.browserstack.com
alias kuse3b4='ssh 74.50.105.68' #kafkab-auto-004-dcp-use3b-prod.browserstack.com
alias kuse3b5='ssh 74.50.105.74' #kafkab-auto-005-dcp-use3b-prod.browserstack.com

alias kusw2a1='ssh 23.83.161.25' #kafkab-auto-001-dcp-usw2a-prod.browserstack.com
alias kusw2a2='ssh 23.83.161.27' #kafkab-auto-002-dcp-usw2a-prod.browserstack.com
alias zusw2a='ssh 23.83.161.29' #zookeeper-auto-001-dcp-usw2a-prod.browserstack.com

# ---- AWS MACHINES ----

# development machines
alias dev='ssh 172.31.41.134' #devautomate.bsstag.com
alias nginx-build='ssh 172.31.14.211' # automate nginx (rails) build machine

# elastic search machines
alias elastic5='ssh 10.0.3.66'
alias elastic7='ssh 10.0.4.26'
alias elastic9='ssh 10.0.1.216'
alias elastic12='ssh 10.0.3.31'

# new es 7 machines
alias elastic13='ssh 10.64.2.32'
alias elastic14='ssh 10.64.9.240'
alias elastic15='ssh 10.64.19.133'
alias elastic16='ssh 10.64.26.14'

# logs
alias zombie='ssh <EMAIL>' # crash / health reports
alias tailf='ssh 172.16.1.180' # railsApp logs
alias tailf2='ssh 172.16.1.87' # services machine / Kakfa b machine logs
alias fuselogs='ssh 10.64.139.147' # hub node logs
