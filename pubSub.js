'use strict';

const redis = require('ioredis');
const HubLogger = require('./log');
const constants = require('./constants');
const helper = require('./helper');
const { isHash, isEmptyJson } = require('./typeSanity');
const hoothootUse = require('hoothoot')('use');

const LL = constants.LOG_LEVEL;
const TAG = 'PUBSUB';

class PubSubClient {
  constructor() {
    this._subscriber = null;
    this._subscriberSecond = null;
    this._publisher = null;
    this._publisherSecond = null;
    this.switchToSecond = false;
    this.setupClients();
  }

  setupClients() {
    this._subscriber = PubSubClient.createSubscriberClient(constants.redisNewConfig);
    this._publisher = PubSubClient.createPublisherClient(constants.redisNewConfig);
    if (constants.multiRedisClusterSetup) {
      this._subscriberSecond = PubSubClient.createSubscriberClient(constants.redisConfigSecond);
      this._publisherSecond = PubSubClient.createPublisherClient(constants.redisConfigSecond);
    }
  }

  static createSubscriberClient(redisConf) {
    const client = redis.createClient(redisConf);
    client.on('connect', () => {
      HubLogger.miscLogger(TAG, 'REDIS CONNECTED', LL.INFO);
    });
    client.on('error', (e) => {
      HubLogger.exceptionLogger(`Redis Error in pubSub(subscriber): ${e ? e.toString() : ''}`);
      hoothootUse.emit('automate_miscellaneous_errors', 1, {
        event_type: 'redis_connection_error_ps', process_id: process.pid, product: 'automate', hub_region: constants.region, machine: constants.osHostName
      });
    });
    client.on('close', () => {
      HubLogger.exceptionLogger('Redis subscriber connection closed');
      const kind = 'redis-connection-closed';
      hoothootUse.emit('automate_miscellaneous_errors', 1, {
        event_type: 'redis_connection_closed_ps', process_id: process.pid, product: 'automate', hub_region: constants.region, machine: constants.osHostName
      });
      helper.PingZombie({
        kind,
        data: `subscriber pid ${process.pid}`,
        region: constants.region,
        machine: constants.osHostName,
      });
    });
    return client;
  }

  static createPublisherClient(redisConf) {
    const client = redis.createClient(redisConf);
    client.on('error', (e) => {
      HubLogger.exceptionLogger(`Redis Error in pubSub(publisher): ${e ? e.toString() : ''}`);
    });
    client.on('close', () => {
      HubLogger.exceptionLogger('Redis publisher connection closed');
      const kind = 'redis-connection-closed';
      helper.PingZombie({
        kind,
        data: `publisher pid ${process.pid}`,
        region: constants.region,
        machine: constants.osHostName,
      });
    });
    return client;
  }

  get publisher() {
    return this._publisher;
  }

  get publisherSecond() {
    return this._publisherSecond;
  }

  get finalPublisher() {
    // Second cluster in case of multi cluster setup, else use the same client
    if (constants.multiRedisClusterSetup) {
      return this._publisherSecond;
    } else {
      return this._publisher;
    }
  }

  get subscriber() {
    return this._subscriber;
  }

  get subscriberSecond() {
    return this._subscriberSecond;
  }
}

const pubSubClient = new PubSubClient();
const subscriptions = {};
const subscriber = pubSubClient.subscriber;
const subscriberSecond = pubSubClient.subscriberSecond;
const publisher = pubSubClient.publisher;

try {
  // need this so that the config is applied since deploy.
  publisher.hgetall('kafka_workers_flags').then((workerKey) => {
    constants.kafkaWorkerFlags = isEmptyJson(workerKey) ? constants.kafkaWorkerFlags : workerKey;
  }).catch((e) => {
    HubLogger.exceptionLogger(`Redis Error default key fetch ${e ? e.toString() : ''}`);
  });
  publisher.get('instrumentation_mechanism_flag').then((value) => {
    constants.instrumentationMechanismFlag = parseInt(value || constants.instrumentationMechanismFlag, 10);
  }).catch((e) => {
    HubLogger.exceptionLogger(`Redis Error default key fetch ${e ? e.toString() : ''}`);
  });
  publisher.get('rails_delete_keys').then((value) => {
    constants.railsDeleteKeys = constants.railsDeleteKeys.concat(value);
  }).catch((e) => {
    HubLogger.exceptionLogger(`Redis Error default key fetch ${e ? e.toString() : ''}`);
  });
  publisher.get('enable_generic_rails_error_caching').then((value) => {
    constants.ENABLE_GENERIC_CACHING = parseInt(value || constants.ENABLE_GENERIC_CACHING, 10);
  }).catch((e) => {
    HubLogger.exceptionLogger(`Redis Error default key fetch ${e ? e.toString() : ''}`);
  });
  publisher.get('ai_use_execute_async').then((value) => {
    constants.USE_SELENIUM_EXECUTE_ASYNC_IN_AI = Boolean(value);
  }).catch((e) => {
    HubLogger.exceptionLogger(`Redis Error default key fetch ${e ? e.toString() : ''}`);
  });
  publisher.get('ai_do_extension_call_async').then((value) => {
    constants.DO_CALL_TO_AI_EXTENSION_IN_ASYNC = Boolean(value);
  }).catch((e) => {
    HubLogger.exceptionLogger(`Redis Error default key fetch ${e ? e.toString() : ''}`);
  });
  publisher.get('aws_user_split_enabled_groups').then((value) => {
    if (value && value.length > 0) {
      constants.AWS_NEW_USER_ENABLED_GROUPS = [...new Set(constants.AWS_NEW_USER_ENABLED_GROUPS.concat(value.split(',')))];
    }
  }).catch((e) => {
    HubLogger.exceptionLogger(`Redis Error AWS new user key fetch ${e ? e.toString() : ''}`);
  });
  publisher.get('aws_user_split_enabled_all').then((value) => {
    constants.AWS_NEW_USER_ENABLED_ALL = Boolean(value) || constants.AWS_NEW_USER_ENABLED_ALL;
  }).catch((e) => {
    HubLogger.exceptionLogger(`Redis Error AWS new user enabled all key fetch ${e ? e.toString() : ''}`);
  });
  publisher.get('enable_safari_start_flow').then((value) => {
    constants.ENABLE_SAFARI_START_FLOW = Boolean(value) || constants.ENABLE_SAFARI_START_FLOW;
  }).catch((e) => {
    HubLogger.exceptionLogger(`Redis Error AWS new user enabled all key fetch ${e ? e.toString() : ''}`);
  });
  publisher.get('instrument_execute_click').then((value) => {
    constants.INSTRUMENT_EXECUTE_CLICK = Boolean(value);
  }).catch((e) => {
    HubLogger.exceptionLogger(`Redis Error default key fetch ${e ? e.toString() : ''}`);
  });
  publisher.get('send_raw_logs_both').then((value) => {
    constants.SEND_RAW_LOGS_BOTH = Boolean(value);
  }).catch((e) => {
    HubLogger.exceptionLogger(`Redis Error default key fetch ${e ? e.toString() : ''}`);
  });
} catch (e) {
  HubLogger.exceptionLogger(`Redis Error default key fetch ${e ? e.toString() : ''}`);
}

setInterval(async () => {
  constants.instrumentationMechanismFlag = parseInt((await publisher.get('instrumentation_mechanism_flag')) || constants.instrumentationMechanismFlag, 10);
  constants.mobileHubTerminalKeepAlive = parseInt((await publisher.get('mobile_hub_terminal_keep_alive')) || constants.mobileHubTerminalKeepAlive, 10);
  const workerKey = await publisher.hgetall('kafka_workers_flags');
  constants.kafkaWorkerFlags = isEmptyJson(workerKey) ? constants.kafkaWorkerFlags : workerKey;
  constants.ENABLE_GENERIC_CACHING = parseInt((await publisher.get('enable_generic_rails_error_caching')) || constants.ENABLE_GENERIC_CACHING, 10);
  constants.USE_SELENIUM_EXECUTE_ASYNC_IN_AI = Boolean(await publisher.get('ai_use_execute_async'));
  constants.DO_CALL_TO_AI_EXTENSION_IN_ASYNC = Boolean(await publisher.get('ai_do_extension_call_async'));
  constants.AWS_NEW_USER_ENABLED_ALL = Boolean(await publisher.get('aws_user_split_enabled_all'));
  constants.ENABLE_SAFARI_START_FLOW = Boolean(await publisher.get('enable_safari_start_flow'));
  constants.INSTRUMENT_EXECUTE_CLICK = Boolean(await publisher.get('instrument_execute_click'));
  constants.SEND_RAW_LOGS_BOTH = Boolean(await publisher.get('send_raw_logs_both'));
  const awsNewFlowGroupsFromRedis = await publisher.get('aws_user_split_enabled_groups');
  constants.AWS_NEW_USER_ENABLED_GROUPS = (awsNewFlowGroupsFromRedis && awsNewFlowGroupsFromRedis.length > 0) ? [...new Set(constants.AWS_NEW_USER_ENABLED_GROUPS.concat(awsNewFlowGroupsFromRedis.split(',')))] : constants.AWS_NEW_USER_ENABLED_GROUPS;
}, 60000);

const convertKeysToCodes = (data) => {
  const newData = {
    s: data.session,
    c: {}
  };

  Object.keys(data.changed).forEach((key) => {
    newData.c[constants.publishKeyCodes[key] || key] = data.changed[key];
  });

  return newData;
};

subscriber.on('message', (channel, message) => {
  if (
    channel !== constants.updateKeyObject &&
    channel !== constants.callbackStopDone
  ) {
    HubLogger.miscLogger(
      TAG,
      `Recieved ${JSON.stringify(message)} on ${channel}`,
      LL.DEBUG
    );
  }
  const callback = subscriptions[channel];
  if (callback) {
    callback(message);
  }
  helper.instrumentRedisCommand(`subscribe_${channel}`, 0);
});


if (constants.multiRedisClusterSetup) {
  subscriberSecond.on('message', (channel, message) => {
    if (
      channel !== constants.updateKeyObject &&
      channel !== constants.callbackStopDone
    ) {
      HubLogger.miscLogger(
        TAG,
        `Recieved ${JSON.stringify(message)} on ${channel}`,
        LL.DEBUG
      );
    }
    const callback = subscriptions[channel];
    if (callback) {
      callback(message);
    }
    helper.instrumentRedisCommand(`subscribe_${channel}`, 0);
  });
}


const subscribe = (channel, callback) => {
  subscriber.subscribe(channel);
  if (constants.multiRedisClusterSetup) {
    subscriberSecond.subscribe(channel);
  }
  subscriptions[channel] = callback;
  HubLogger.miscLogger(TAG, `Registered to channel ${channel}`, LL.INFO);
};

const unsubscribe = (channel) => {
  subscriber.unsubscribe(channel);
  if (constants.multiRedisClusterSetup) {
    subscriberSecond.unsubscribe(channel);
  }
  delete subscriptions[channel];
  HubLogger.miscLogger(TAG, `Unregistered to channel ${channel}`, LL.INFO);
};

const publish = (channel, data, keyObject = null, publishFromTimeoutManager = false, isStartRequest = false) => {
  try {
    if (!publishFromTimeoutManager) {
      const instrumentationMechanismFlag = constants.instrumentationMechanismFlag;
      const kafkaData = {};
      if (channel === constants.updateKeyObject && (instrumentationMechanismFlag === 0 || instrumentationMechanismFlag === 1) && data.changed) {
        Object.keys(data.changed).forEach((key) => {
          if (helper.shouldSendInstrumentationThroughKafka(key, data.changed[key])) {
            kafkaData[key] = data.changed[key];
            if (instrumentationMechanismFlag === 0) {
              if (constants.pubSubKafkaKeys[key] === 'Y' && (key.toString() !== 'lastResponseTime' || !isStartRequest)) {
                delete data.changed[key];
              }
            }
          }
        });
        if (Object.keys(kafkaData).length) {
          helper.sendDataToInstrumentationService(constants.global_registry[data.session], null, kafkaData);
        }
        if (JSON.stringify(data.changed) === '{}' || Object.keys(data.changed).length === 0) {
          return;
        }
      } else if (channel === constants.sessionDeletionChannel && instrumentationMechanismFlag !== 2) {
        if (constants.global_registry[data]) {
          helper.sendDataToInstrumentationService(constants.global_registry[data], 'STOP_SESSION', constants.global_registry[data], false);
        } else {
          helper.hilErrorInstrumentation(data, false);
          HubLogger.exceptionLogger(`[FAILED SESSION DELETION LOG - INSTRUMENTATION CONSUMER] : sessionId : ${data}`);
        }
      } else if (channel === constants.sessionTimeout && instrumentationMechanismFlag !== 2) {
        if (constants.global_registry[data]) {
          helper.sendDataToInstrumentationService(constants.global_registry[data], 'STOP_SESSION', constants.global_registry[data], true);
        } else if (keyObject) {
          helper.sendDataToInstrumentationService(keyObject, 'STOP_SESSION', keyObject, true);
        } else {
          helper.hilErrorInstrumentation(data, true);
          HubLogger.exceptionLogger(`[FAILED SESSION TIMEOUT LOG - INSTRUMENTATION CONSUMER] : sessionId : ${data}`);
        }
      }
    }
  } catch (err) {
    helper.hilErrorInstrumentation(data.session || data, null, err.toString().substring(0, 1024));
    HubLogger.exceptionLogger(`[pubSub Kafka Processing Error] : ${err.toString().substring(0, 1024)}`);
  }

  let elapsedTime = 0;
  if (data && channel !== constants.updateKeyObject) {
    HubLogger.miscLogger(
      TAG,
      `publishing: ${JSON.stringify(data)} to ${channel}`,
      LL.DEBUG
    );
  }

  if (isHash(data)) {
    if (channel === constants.updateKeyObject) {
      data = convertKeysToCodes(data);
    }

    data = JSON.stringify(data);
  }
  try {
    const current = process.hrtime();
    pubSubClient.finalPublisher.publish(channel, data).then(() => {
      elapsedTime = process.hrtime(current);
      helper.instrumentRedisCommand(`publish_${channel}`, parseInt((elapsedTime[0] * 1000) + (elapsedTime[1] / 1000000), 10));
    });
  } catch (e) {
    HubLogger.exceptionLogger(`Error in redis pubSub(publish): ${
      e ? e.toString() : ''
    } channel: ${channel}`);
  }
};

module.exports = {
  subscribe,
  unsubscribe,
  publish,
  convertKeysToCodes,
  pubSubClient,
};
