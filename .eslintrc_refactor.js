module.exports = {
  'extends': 'airbnb',
  'parserOptions': {
    'sourceType': 'script',
    'ecmaVersion': 9
  },
  'plugins': ['avoid-explicit-extension'],
  'rules': {
    // needed as node >= 5 treats all modules as strict mode default
    'strict': [
      'error',
      'global'
    ],
    'complexity': ['error', 15],
    'max-lines-per-function': ['error', { 'max': 30, 'skipBlankLines': true, 'skipComments': true }],
    'max-lines': ['error', { 'max': 1100, 'skipBlankLines': true, 'skipComments': true }],
    'quotes': ['error', 'single'],
    'avoid-explicit-extension/no-js-ext': 'error',
    'no-plusplus': [2, { allowForLoopAfterthoughts: true }],
    // Trailing commas in function declarations and function calls
    // are valid syntax since ECMAScript 2017
    'comma-dangle': 0,
    // Node 4.5 does not support the new syntax
    'prefer-destructuring': ['error', {
      'object': false,
      'array': false
    }],
    'no-param-reassign': 0,
    'max-len': 1,
    'import/order': 0,
    'class-methods-use-this': 0,
    'operator-linebreak': 0,
    'implicit-arrow-linebreak': 0,
    'no-underscore-dangle': 0,
    // TODO (yashLadha): change to error after fixing warning
    'no-else-return': ['warn', { allowElseIf: false }],
  },
  'env': {
    'node': true
  }
};
