'use strict';

const constants = require('./constants');
const v8 = require('v8');
const pusage = require('pidusage');
const helper = require('./helper');
const redisClient = require('./redisUtils').redisClient;
const util = require('util');
const { isNotUndefined } = require('./typeSanity');
const cls = require('cls-hooked');

const processStat = util.promisify(pusage.stat);

const alertOnThreshold = (listName, data) => {
  if (data && data > constants.timeoutManager.backlogThresholdAlertLimit) {
    helper.PingZombie({
      error: listName,
      data,
      region: constants.hubName,
      kind: 'consumerBackLogExceeded',
      timestamp: (Math.round(new Date().getTime() / 1000).toString()),
    });
  }
};

const getV8HeapMetrics = () => {
  const heapSpaceStatsArray = v8.getHeapSpaceStatistics();
  // Lis of object to object
  const heapSpaceStatsObject = heapSpaceStatsArray.reduce((acc, current) => {
    acc[current.space_name] = current;
    delete acc[current.space_name].space_name;
    return acc;
  }, {});

  const getHeapSpaceStatistics = v8.getHeapStatistics();
  const getHeapCodeStatistics = v8.getHeapCodeStatistics();

  return { ...heapSpaceStatsObject, getHeapSpaceStatistics, getHeapCodeStatistics };
};

const determineSystemStats = async (workerStats) => {
  let stat;
  let data;
  try {
    stat = await processStat(process.pid);
  } catch (err) {
    stat = undefined;
  }

  if (isNotUndefined(stat)) {
    const memoryUsage = process.memoryUsage();
    Object.keys(memoryUsage).forEach((v) => {
      memoryUsage[v] = memoryUsage[v] / (1024 * 1024) || 0;
    });

    const chitraguptaNamespace = cls.getNamespace('chitragupta');
    /* eslint-disable no-underscore-dangle */
    const chitraguptaContextSize = chitraguptaNamespace ? chitraguptaNamespace._contexts.size : -1;

    const currentHubProcessingRegistryData = helper.resetHubProcessingRegistry();

    data = {
      // Number of sessions in the current Hub
      total_sessions: Object.keys(constants.sessions_registry).length,
      // Number of sessions in the Global registry (Sessions from other Hubs)
      total_global: Object.keys(constants.global_registry).length,
      // Number of websocket connections
      total_ws: Object.keys(constants.global_ws_registry).length,
      // Memory usage of the Node process
      memory: memoryUsage,
      // % CPU Usage of the Node process
      cpu: ((stat && !Number.isNaN(stat.cpu)) ? stat.cpu : 0.0),
      // Chitragupta CLS Context size
      chitragupta_cls_size: chitraguptaContextSize,
      // Number of Selenium commands/requests to current Hub (Since instrumenting hub processing time)
      hub_request_count: currentHubProcessingRegistryData.hubRequestCount,
      // Processing time in the current Hub
      hub_processing_time: currentHubProcessingRegistryData.hubProcessingTime,
      // Delay between request leaving nginx and getting picked by hub
      nginx_to_hub_time: currentHubProcessingRegistryData.nginxToHubTime,
      // Delay between user and hub
      user_to_nginx_time: currentHubProcessingRegistryData.userToNginxTime,
      // Time taken between request leaving for terminal and response received.
      jar_time: currentHubProcessingRegistryData.jarTime
    };
    data = { ...data, ...workerStats };
  }

  return data;
};

const cleanHootHootRegistry = () => {
  Object.keys(constants.pushToHootHootRegistry).forEach((hootHootKey) => {
    helper.pushStatsToHootHoot(
      hootHootKey,
      constants.pushToHootHootRegistry[hootHootKey],
      process.pid
    );
  });
  constants.pushToHootHootRegistry = {};
};

const processRegionList = (iter) => {
  const processList = helper.timeoutManagerGetProcessList(iter);
  const backupList = helper.timeoutManagerGetBackupList(iter);
  const regionLists = (iter === 'v3' ? [processList] : [processList, backupList]);
  const pendingHootHootPromises = regionLists.map(async (redisList) => {
    try {
      const data = await (iter === 'v3' ? redisClient.zcard(redisList) : redisClient.llen(redisList));
      const hoothootData = {};
      hoothootData[redisList] = data;
      helper.pushStatsToHootHoot(constants.timeoutManager.statsKey, hoothootData);

      if (redisList === processList && iter !== 'v3') {
        alertOnThreshold(processList, data);
      }
    } catch (err) { // eslint-disable-next-line no-empty
    }
  });
  return Promise.all(pendingHootHootPromises);
};

const ownerCallBack = async () => {
  // Need this here to track stats when consumer is down
  const regionTrackStats = Array.from({ length: constants.timeoutManager.totalCountThisRegion })
    .map((_, iter) => processRegionList(iter));

  regionTrackStats.push(processRegionList('v3'));

  await Promise.all(regionTrackStats);
};

exports.getMetricsForHootHoot = async (workerStats) => {
  const systemStats = await determineSystemStats(workerStats);
  const v8HeapMetrics = getV8HeapMetrics();

  if (isNotUndefined(systemStats) && isNotUndefined(v8HeapMetrics)) {
    helper.pushStatsToHootHoot(constants.hoothootHubNodeStatsKey, { ...systemStats, ...v8HeapMetrics }, process.pid);
  } else if (isNotUndefined(systemStats)) {
    helper.pushStatsToHootHoot(constants.hoothootHubNodeStatsKey, systemStats, process.pid);
  }

  const ownsRedisMetrics = await helper.checkOwnRedisMetrics();
  if (ownsRedisMetrics) {
    await ownerCallBack();
  }
  cleanHootHootRegistry();
};
