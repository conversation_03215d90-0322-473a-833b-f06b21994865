{"nodeTimeout": 60000, "startNodeTimeout": 60000, "snapshotJarTimeout": 40000, "idleTimeout": 90000, "bsTimeout": 60000, "bsRetryTimeout": 100, "sameRetryTimeout": 50, "bs_host": "localhost", "bs_port": 3000, "bs_app_host": "localhost", "bs_app_port": 3001, "hub_host": "localhost", "hub_port": 8082, "redis_server": "localhost", "redis_port": "6379", "log_post_interval": 2000, "env_name": "testing", "aws_key": "********************", "aws_secret": "nyj2z7Un8biUmTThj/OGNGmczM/c5ioO9dirUv3L", "hubUrlSubRegionMapping": {"localhost": ["us-east-1a", "us-east-1b", "us-east-1c", "us-east-1d", "us-east-2a", "us-east-3b", "us-west-1c", "us-west-1e", "us-west-1c", "us-west-1d", "us-west-2a", "us-west-3a", "eu-west-1a", "eu-west-1b", "eu-west-1c", "eu-west-1d", "eu-central-1a", "ap-south-1a", "ap-south-1c", "ap-southeast-2a", "ap-southeast-2b", "ap-southeast-2c"]}, "cdpUrlSubRegionMapping": {"localhost": ["us-east-1a", "us-east-1b", "us-east-1c", "us-east-1d", "us-east-2a", "us-east-3b", "us-west-1c", "us-west-1e", "us-west-1c", "us-west-1d", "us-west-2a", "us-west-3a", "eu-west-1a", "eu-west-1b", "eu-west-1c", "eu-west-1d", "eu-central-1a", "ap-south-1a", "ap-south-1c", "ap-southeast-2a", "ap-southeast-2b", "ap-southeast-2c"]}, "worker_exit_hard_timeout": 6500, "blocked_ips": [], "blocked_ips_regex": "", "blocked_users": ["blocked_user"], "blocked_user_timeout": 1, "mail_to": ["<EMAIL>"], "ha_server": "127.0.0.1", "ha_port": "11211", "rails_request_random_delay_scale": 1, "logkey": "heya", "railstoken": "selautomate", "user_start_response_delay": 10, "hub_name": "hub1", "not_allowed_requests": ["POST:maximize"], "browserstack.region": "us-east-1", "non_pipe_urls": ["POST:value"], "other_hub_servers": ["hub1.bsstag.com", "hub2.bsstag.com"], "checkPageLoadWait": 25, "zombie_server": "zombiestaging.browserstack.com", "zombie_port": 8000, "eds_server": "localhost", "eds_port": 8553, "eds_key": "123456", "screenshot_count_log_file": ".", "percy_screenshot_uploader_cert": "eyJuYW1lIjoicGVyY3lDZXJ0cyJ9", "rails_redacted_caps": ["firefox_profile", "firefoxProfile"], "safari_driver_port": 8884, "edge_auth_timeout": 100, "ie11w10_file_upload_timeout": 50, "secondary_states": {"SUCCESS": "success", "1": "tunnel-required", "2": "tunnel-required", "3": "tunnel-required", "4": "tunnel-required", "401": {"Basic realm=\"Application\"": "basic-auth", "NTLM": "ntlm-auth"}}, "relaxed_http_timeout": 45000, "relaxed_start_timeout": 30000, "hub_status_data": {"status": 0, "sessionId": null, "value": {"build": {"version": "2.45.0", "revision": "5017cb8", "time": "2015-02-26 23:59:50"}}, "state": "success"}, "railsPipeline": {"pollQueuedDelay": 1, "pollEmptyDelay": 1}, "terminal_cmd_exec_max_time": 30000, "page_load_timeout": 120000, "queue_request_delay": 300, "queue_max_wait": 4500, "exponential_default_retry_delay": [300, 500, 800], "exponential_nta_retry_delay": [300, 500, 800], "sessionQueuePrefix": "testQueue", "sessionParallelPrefix": "test<PERSON><PERSON><PERSON><PERSON>", "sessionQueueResetTime": 60, "hub_log_level": 4, "cls_port": 41234, "safari_cancel_timeout": 100, "cls_host": "logs.bsstag.com", "disableLogs": true, "forceCheckTitleAfterOpenUrl": true, "sessionTimeout": "TEST_sessionTimeout", "sessionDeletionChannel": "TEST_sessionDeletionChannel", "userQueuePop": "TEST_userQueuePop", "timeoutReinitialization": "TEST_timeoutReinitialization", "updateKeyObject": "TEST_updateKeyObject", "callbackStopDone": "TEST_callbackStopDone", "default_basic_auth_ios_timeout": 10, "default_basic_auth_ios_response_timeout": 101, "kafkaConfig": {"raw_logs_topic": "raw_logs", "raw_extended_duration_logs_topic": "raw_extended_duration_logs", "console_logs_topic": "console_logs", "producer": {"__COMMENT__": "CONFIGS REQUIRED BY PRODUCERS(HUB) TO IDENTIFY WHICH PARTITIONS TO READ FROM. EACH REGION CAN HAVE MULTIPLE PRODUCERS", "RAW_LOGS_SERVICE": "redis", "RAW_EXTENDED_DURATION_LOGS_SERVICE": "redis", "regions": ["us-east-1", "us-west-1", "eu-west-1", "us-east-3b"], "us-east-3b": {"startPartition": 0, "endPartition": 39, "endPartitionExtended": 9, "startPartitionExtended": 0, "brokers": ["localhost:9092"]}, "us-east-1": {"startPartition": 0, "endPartition": 39, "endPartitionExtended": 9, "startPartitionExtended": 0, "brokers": ["localhost:9092"]}, "us-west-1": {"startPartition": 0, "endPartition": 39, "endPartitionExtended": 9, "startPartitionExtended": 0, "brokers": ["localhost:9092"]}, "eu-west-1": {"startPartition": 0, "endPartition": 39, "endPartitionExtended": 9, "startPartitionExtended": 0, "brokers": ["localhost:9092"]}}, "consumer": {"__COMMENT__": "CONFIG REQUIRED BY CONSUMERS(UPLOADERS) TO IDENTIFY WHICH PARTITIONS TO READ FROM. EACH REGION CAN HAVE MULTIPLE CONSUMERS", "RAW_LOGS_SERVICE": "redis", "RAW_EXTENDED_DURATION_LOGS_SERVICE": "redis", "autoCommitIntervalMs": 5000, "autoCommit": true, "fetchMaxWaitMs": 1000, "fetchMaxBytes": 1048576, "us-east-1": {"__COMMENT__": "THE IDS HERE SIGNIFY THE CONSUMER IDS. IN CASE WE HAVE TO ADD ANOTHER CONSUMER, WE SIMPLY ADD ANOTHER KEY '2' AND RE<PERSON><PERSON>NCE THE PARTITIONS", "1": {"partitions": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39]}}, "us-west-1": {"2": {"partitions": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39]}}, "eu-west-1": {"3": {"partitions": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39]}}, "us-east-3b": {"4": {"partitions": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39]}}}}, "pipelineManuplationValues": {"automate": {"quickRecoveryThreshold": 90, "quickRecoveryMultiplier": 1.5, "normalRecoveryMultiplier": 1.1, "penalizeMoreThreshold": 10, "penalizeLessThreshold": 1, "penalizeMoreDivisor": 4, "penalizeLessDivisor": 2, "localizedUserThreshold": 100, "topUserCount": 1}, "appAutomate": {"quickRecoveryThreshold": 90, "quickRecoveryMultiplier": 1.1, "normalRecoveryMultiplier": 1.1, "penalizeMoreThreshold": 10, "penalizeLessThreshold": 1, "penalizeMoreDivisor": 4, "penalizeLessDivisor": 4, "topUserCount": 1}}, "tcg_service": {"scheme": "https", "tcg_regions": {"us-east-1": {"endpoint": "tcg.bsstag.com", "elb": "tcg.bsstag.com", "s3_upload_endpoint": "browserstack-testcasegeneration-stag-euc1.s3.eu-central-1.amazonaws.com"}, "us-west-2": {"endpoint": "tcg-usw.bsstag.com", "elb": "tcg.bsstag.com", "s3_upload_endpoint": "browserstack-testcasegeneration-stag-euc1.s3.eu-central-1.amazonaws.com"}, "us-east-3b": {"endpoint": "tcg.bsstag.com", "elb": "tcg.bsstag.com", "s3_upload_endpoint": "browserstack-testcasegeneration-stag-euc1.s3.eu-central-1.amazonaws.com"}}, "username": "root", "password": "password"}}