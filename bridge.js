'use strict';

const Promise = require('bluebird');
const urlModule = require('url');
const util = require('util');
const constants = require('./constants');
const requestlib = require('./lib/request');
const hub = require('./hub');
const ha = require('./ha');
const isAppleOs = require('./helper').isAppleOs;
const HubLogger = require('./log');
const pubSub = require('./pubSub');
const helper = require('./helper');
const debug = require('./debug');
const supporting = require('./supporting');
const WdaClient = require('./wdaClient');
const SeleniumClient = require('./seleniumClient');
const path = require('path');
const TransitionChecker = require('./helpers/transition-check');
const queryString = require('querystring');
const { isUndefined, isNotUndefined, isTrueString } = require('./typeSanity');
const { isCDP } = require('./socketManagers/validations');
const Qig = require('./helpers/qig');
const { SELENIUM } = require('./config/socketConstants');
const { triggerLighthouse } = require('./helpers/lighthouse/helper');

const LL = constants.LOG_LEVEL;
const ERROR = constants.ERROR;

const pathMatchingRegexp = /C:\\(Users)\\(hello)\\(Desktop|Documents)\\(audio|documents|images|video)\\*\.*/;

const basicAuthPopupExists = (keyObject, params) => {
  const { key: remoteSessionID, rails_session_id: clientSessionID, secondary_state: secondaryState } = keyObject;
  const {
    hash, request, response, output, index_counter, callbacks, hostname, originalUrl
  } = params;

  if (['basic-auth_required', 'basic-auth_failed'].some(el => el === secondaryState)) {
    if (constants.timeout_registry[keyObject.rails_session_id]) {
      keyObject.timestamp = Date.now();
      keyObject.lastRequest = hash;
      ha.setData(keyObject.rails_session_id, keyObject);
      pubSub.publish(constants.updateKeyObject, {
        session: clientSessionID,
        changed: {
          timestamp: keyObject.timestamp,
          lastRequest: keyObject.lastRequest,
        },
      });
    }
    request.log_date = helper.getDate();
    request.log_data = request.log_data || '';
    HubLogger.seleniumStats('automate-basic-auth-popup', keyObject, 'Basic Auth Popup', 'A modal dialog was open, blocking this operation.', 'selenium-command-error-prevention');
    hub.processResponse(request, response, keyObject, {
      data: '{"state":"unexpected alert open","sessionId":"'+clientSessionID+'","value":{"message":"A modal dialog was open, blocking this operation."},"class":"org.openqa.selenium.remote.Response","status":26}',
      output: output || {headers:constants.CHUNKED_HEADER, statusCode: 200},
      remoteSessionID: remoteSessionID,
      clientSessionID: clientSessionID,
      index_counter: index_counter,
      callbacks: callbacks,
      hostname: hostname,
      originalUrl: originalUrl,
    });
    return true;
  }
  return false;
};
exports.basicAuthPopupExists = basicAuthPopupExists;

// This function returns boolean true/false
// true: page is not yet ready and this function will take further care of the response to client
// false: page load is complete and the callee can proceed with handling the response to client
const transitionCheck = (keyObject, params) => {

  const { key: remoteSessionID, rails_session_id: clientSessionID, autoWait, realMobile, os } = keyObject;
  const { request, response, hash, callbacks, index_counter, hostname, originalUrl } = params;
  const commonParams = { remoteSessionID, clientSessionID, index_counter, callbacks, hostname, originalUrl };

  const successCallback = () => {
    request.lastRequestLogicDone = true;
    request.transitionCheckDone = true;
    return hub.createBridgeClientAndNode(keyObject, request, response, callbacks);
  };

  const errorCallback = (err, output) => {
    if (isUndefined(constants.global_registry[clientSessionID])) {
      HubLogger.miscLogger('sessionNotFound', `Session not started or terminated for ${clientSessionID} in checkPageLoad.`, LL.WARN);
      return hub.sessionNotFound(response, keyObject, 'Session Deleted during Page Load Check on Terminal');
    }

    request.recurseCount += 1;
    if (request.recurseCount < 5 && typeof(output) !== 'undefined') {
      request.transitionCheckDone = true;
      return hub.createBridgeClientAndNode(keyObject, request, response, callbacks);
    }

    exports.loadRequestData(keyObject, { request, response, hash }).then((requestData) => {
      if(constants.timeout_registry[clientSessionID]) {
        keyObject.timestamp = new Date().getTime();
        ha.setData(clientSessionID, keyObject);
        pubSub.publish(constants.updateKeyObject, { session: clientSessionID, changed: { timestamp: keyObject.timestamp, instable: true } });
      }

      request.log_date = helper.getDate();
      request.log_data = request.log_data || requestData || '';
      output = output || { headers: constants.CHUNKED_HEADER, statusCode: 200 };
      const data = transitionChecker.generateErrorResponse();

      HubLogger.seleniumStats('automate-page-still-loading', keyObject, 'Action requested when page is still loading.', data, 'selenium-command-error-prevention');
      return hub.processResponse(request, response, keyObject, { data, output, ...commonParams });
    }).catch((loadRequestError) => {
      params.data = JSON.stringify({ value: { message: `Got error while loading request data: ${loadRequestError.message}` } });
      exports.sendErrorResponse(keyObject, params);
    });
  };

  // faster logic for Real iOS on DELETE:window command
  if (realMobile && isAppleOs(os) && keyObject.lastRequest === 'DELETE:window' && constants.preventPostWindowDeleteCommandsIOS.includes(keyObject.appium_version || constants.defaultAppiumVersionIOS)) {
    const data = { state: 'error',sessionId: clientSessionID, value: { message: 'An unknown server-side error occurred while processing the command.', error: 'unknown error' }, 'class': 'org.openqa.selenium.remote.Response', status: 13 };
    hub.processResponse(request, response, keyObject, { data: JSON.stringify(data), ...commonParams });
    return true;
  }

  // last request based logic for error prevention
  request.recurseCount = request.recurseCount || 0;
  const transitionChecker = new TransitionChecker(keyObject, params);
  const checkPageLoad = autoWait && transitionChecker.shouldHandleTransition && !request.lastRequestLogicDone;
  if (checkPageLoad) {
    // check page load on terminal if error then retry after 1 second
    supporting.checkPageLoadOnTerminal(keyObject, successCallback, () => {
      setTimeout(() => {
        supporting.checkPageLoadOnTerminal(keyObject, successCallback, errorCallback, true, transitionChecker.isTitleAndReadyStateCheck); // return error if 2nd attempt fails
      }, 1000);
    }, true, transitionChecker.isTitleAndReadyStateCheck);
  }
  return Boolean(checkPageLoad);
};
exports.transitionCheck = transitionCheck;

var activeElementCheck = function(keyObject, params) {
  var remoteSessionID = keyObject.key,
      clientSessionID = keyObject.rails_session_id,
      request = params.request,
      response = params.response,
      callbacks = params.callbacks,
      index_counter = params.index_counter,
      hostname = params.hostname,
      originalUrl = params.originalUrl,
      isIE10 = keyObject.browser == 'internet explorer' && parseInt(keyObject.browser_version) == 10,
      isIE11 = keyObject.browser == 'internet explorer' && parseInt(keyObject.browser_version) == 11,
      isFF13 = keyObject.browser == 'firefox' && parseInt(keyObject.browser_version) == 13;

  if(request.method === 'POST' && request.url.indexOf('/active') > -1 && (isIE10 || isIE11 || isFF13) && !request.checkedActive){
    request.log_date = helper.getDate();
    request.log_data = request.log_data || '';
    supporting.checkActiveElementOnTerminal(keyObject,
      function(data, output) {
        request.checkedActive = true;
        return hub.processResponse(request, response, keyObject, {
          data: JSON.stringify(data),
          output: output,
          remoteSessionID: remoteSessionID,
          clientSessionID: clientSessionID,
          index_counter: index_counter,
          callbacks: callbacks,
          hostname: hostname,
          originalUrl: originalUrl,
        });
      },
      function(err, output){
        return hub.processResponse(request, response, keyObject, {
          data: '{"state":"success","sessionId":"'+clientSessionID+'","value":{},"class":"org.openqa.selenium.remote.Response","status":0}',
          output: output,
          remoteSessionID: remoteSessionID,
          clientSessionID: clientSessionID,
          index_counter: index_counter,
          callbacks: callbacks,
          hostname: hostname,
          originalUrl: originalUrl,
        });
      });
    return true;
  }
  return false;
};
exports.activeElementCheck = activeElementCheck;

var getRequestData = function(req_data, params) {
  var retry = false,
      non_pipe_url = params.non_pipe_url,
      keyObject = params.keyObject,
      clientSessionID = params.clientSessionID,
      remoteSessionID = params.remoteSessionID;

  // if(constants.global_registry[keyObject.rails_session_id]) {
  //   pubSub.publish(constants.updateKeyObject, {
  //     session: keyObject.rails_session_id,
  //     changed: {
  //       lastRequestTime: keyObject.lastRequestTime,
  //     },
  //   }, false);
  // }
  let isTimeout = isUndefined(params.hash);
  if (params.hash) {
    const hashArr = params.hash.split(':');
    isTimeout = hashArr.length == 2 && ['timeouts', 'async_script', 'implicit_wait'].includes(hashArr[1]);
  }

  if (!isTimeout && !non_pipe_url) {
    return req_data;
  }

  var x = false;
  var log_data = {};
  if (req_data && req_data != '' && req_data != 'null') {
    x = Buffer.byteLength(req_data, 'utf8'); // req_data.length;
    var json_req_data = JSON.parse(req_data);
    log_data = json_req_data;
    //non_pipe_url = non_pipe_url && (typeof json_req_data["file"] != "undefined")
    delete json_req_data['sessionId'];
    if(json_req_data['ms'] && json_req_data['ms'] > (constants.NODE_DIED_IN - 10000))
      json_req_data['ms'] = constants.NODE_DIED_IN - 10000;
    req_data = JSON.stringify(json_req_data);
  }

  // Below only triggers on [non_pipe_url -> POST /session/:sessionId/element/:id/value]
  if(non_pipe_url && !retry && log_data && (typeof(log_data.text || log_data.value) != 'undefined')) {
    var send_key_value = '';
    try {
      send_key_value = log_data.text || (log_data.value.join ? log_data.value.join('') : log_data.value.toString());
    } catch (e) {
      HubLogger.tempExceptionLogger(`sendKeys Error clientSessionId: ${clientSessionID} logData: ${util.inspect(log_data, {depth: 0})}`, e);
    }
    if (keyObject.lastRequest === 'POST:file' || pathMatchingRegexp.test(send_key_value) || send_key_value.indexOf('upload') > -1) {
      req_data = hub.replaceSessionID(req_data, clientSessionID, remoteSessionID, log_data).toString('utf8');
      // this if block is here due to edge18 driver bug https://developer.microsoft.com/en-us/microsoft-edge/platform/issues/23483333/
      if (keyObject.browser === 'MicrosoftEdge' && keyObject.browser_version === '43.0') {
        const tempData = JSON.parse(req_data);
        if (isNotUndefined(tempData.value)) {
          delete tempData.value;
        }
        req_data = JSON.stringify(tempData);
      }
    }
  }

  if(req_data){
    var l1 = x;
    var l2 = Buffer.byteLength(req_data, 'utf8'); // req_data.length;
    for(var i = 0; i < (l1 - l2); i++) {
      req_data += ' ';
    }
  }
  return req_data;
};
exports.getRequestData = getRequestData;

var loadRequestData = function (keyObject, params) {
  var attempt = params.attempt,
      request = params.request,
      //response = params.response,
      req_data = params.req_data,
      requestData = params.requestData,
      hash = params.hash,
      clientSessionID = params.clientSessionID,
      remoteSessionID = params.remoteSessionID,
      browserstackTunnel = params.browserstackTunnel,
      payload = params.payload,
      non_pipe_url = params.non_pipe_url,
      hostname = params.hostname;
  const isExecuteSync = (hash === 'POST:sync' || hash === 'POST:execute') && !keyObject.appTesting;
  return new Promise(function(resolve, reject) {
    if (attempt == 1) {
      requestlib.readRequest(request)
      .then((data) => {
        req_data = data;
        var log_data = {};
        // HubLogger.newCGLogger('REQUEST_LOAD', `Selenium request data loaded - ${request.method}:${request.url}. Data-Size - ${req_data.length}`, LL.REQUEST, clientSessionID, false, undefined, keyObject.user_id);
        if (req_data && req_data != '' && req_data != 'null' && !isExecuteSync) {
          try {
            log_data = JSON.parse(req_data);
          } catch(e) {
            HubLogger.tempExceptionLogger(`Jsonparse error in loadRequestData  ${req_data}`, e);
            var rejectParseError = new Error('Request body is not JSON');
            rejectParseError.type = constants.customErrorTypes.userRequestNotJSON;
            reject(rejectParseError);
            return;
          }
        }
          delete log_data['file'];

          var isWinFilePath = false;
          // https://regex101.com/r/Sjvo7P/1/tests
          if (Array.isArray(log_data.value)) {
            isWinFilePath = pathMatchingRegexp.test(path.win32.normalize(log_data.value.join('')));
          }
          if (request.method === 'POST' && (request.url.indexOf('/file') > -1 || isWinFilePath) && keyObject && keyObject.browser && !keyObject.deviceName && keyObject.browser.toLowerCase() === 'internet explorer' && keyObject.browser_version.toString() === '11.0' && keyObject.os === 'win10' && !keyObject.IE11W10FileUpload) {
            if (request.url.indexOf('/file') > -1) {
              pubSub.publish(constants.updateKeyObject, {
                session: clientSessionID,
                changed: {
                  IE11W10FileUpload: true,
                },
              });
            } else if (isWinFilePath) {
              hash = 'POST:ie11w10_file_upload';
            }
          } else if (keyObject && keyObject.IE11W10FileUpload === true) {
            if (keyObject && keyObject.browser && !keyObject.deviceName && keyObject.browser.toLowerCase() === 'internet explorer' && keyObject.browser_version.toString() === '11.0' && keyObject.os === 'win10' && request.method === 'POST' && request.url.match(/\/element\/.*\/value/) !== null) {
              hash = 'POST:ie11w10_file_upload';
            }
            pubSub.publish(constants.updateKeyObject, {
              session: clientSessionID,
              changed: {
                IE11W10FileUpload: false,
              },
            });
          }

        if(request.method == 'POST'){
          if(request.url.indexOf('/url') > -1){
            request.logging_data = {url: log_data.url, start_time: (new Date()).getTime()};

            if (keyObject.lighthouseAutomate && keyObject.lighthouseAutomate.report_limit) {
              try {
                const url = log_data.url;
                HubLogger.miscLogger(`lighthouse-${SELENIUM}-trigger-start`, `session: ${keyObject.rails_session_id} url: ${url}`, LL.INFO);
                triggerLighthouse(url, keyObject, SELENIUM);
              } catch (error) {
                HubLogger.miscLogger(`lighthouse-${SELENIUM}-error`, `lh${SELENIUM} - session: ${keyObject.rails_session_id} error: ${error}`, LL.ERROR);
              }
            }

            var userUrl = null;
            try {
              userUrl = urlModule.parse(log_data.url);
              if(userUrl.href) {
                constants.global_registry[clientSessionID].lastOpenedUrl = userUrl.href;
                let changes = {
                  session: clientSessionID,
                  changed: {
                    lastOpenedUrl: userUrl.href,
                  },
                };
                if (!constants.global_registry[clientSessionID].firstOpenedUrl) {
                  constants.global_registry[clientSessionID].firstOpenedUrl = userUrl.href;
                  changes.changed.firstOpenedUrl = userUrl.href;
                }
                pubSub.publish(constants.updateKeyObject, changes);

                Qig.checkAndMarkCustomUrlUsed(keyObject, userUrl.href);
              }
            } catch(e) {
              HubLogger.tempExceptionLogger(`URLParseError POST /url request sessionId ${clientSessionID} logData ${JSON.stringify(log_data)}`, e, e.stack);
            }

            if(userUrl && userUrl.href === 'about:blank')
              hash = 'POST:url-about-blank';
            else if(userUrl && userUrl.pathname && userUrl.pathname.endsWith('.xml'))
              hash = 'POST:url-xml';
            else if(userUrl && userUrl.protocol === 'data:')
              hash = 'POST:url-data';
            else if(keyObject && keyObject.browser && keyObject.browser.toLowerCase() === 'microsoftedge' && (isNaN(parseInt(keyObject.browser_version)) || parseInt(keyObject.browser_version) < 79) && userUrl && userUrl.auth && userUrl.auth.indexOf(':') > -1)
              hash = 'POST:basic-auth-edge';
            else if(keyObject && keyObject.browser && keyObject.browser.toLowerCase() === 'safari' && ['machs', 'macmo', 'maccat', 'macbsr', 'macmty', 'macven', 'macson', 'macsqa'].includes(keyObject.os) && keyObject.certs)
              hash = 'POST:safari-11-certs';
            else if(keyObject && keyObject.browser && !keyObject.deviceName && keyObject.browser.toLowerCase() == 'safari' && userUrl && userUrl.protocol === 'https:' && keyObject.certs && userUrl.auth && userUrl.auth.indexOf(':') > -1)
              hash = 'POST:url-https-auth';
            else if(keyObject && keyObject.os && keyObject.os.toLowerCase() === 'ios' && keyObject.deviceName && userUrl && userUrl.auth && userUrl.auth.indexOf(':') > -1) {
              hash = (userUrl.protocol === 'https:' && keyObject.certs && parseInt(keyObject.deviceName.split('-')[1], 10) >= 11) ? 'POST:url-https-basic-auth-ios-gte-11' : getHashBasicAuthiOS(keyObject, userUrl);
            } else if (keyObject && keyObject.os && keyObject.os.toLowerCase() === 'ios' && keyObject.deviceName && userUrl && userUrl.protocol === 'https:' && keyObject.certs) {
              let os_version_for_accept_ssl = parseInt(keyObject.deviceName.split('-')[1], 10);
              if (os_version_for_accept_ssl >= 11) {
                // setting requestStateObj.hash value for iOS >= 11
                hash = 'POST:url-https-ios-gte-11';
              }
            }
            else if (keyObject && keyObject.os && keyObject.os.toLowerCase() === 'ios' && keyObject.deviceName && parseInt(keyObject.deviceName.split('-')[1]) >= 10 && keyObject.safari_allow_popups) {
              hash = 'POST:url-inject-ios';
            }
            else if(keyObject && keyObject.browser && !keyObject.deviceName && keyObject.browser.toLowerCase() == 'firefox' && !isNaN(parseInt(keyObject.browser_version)) && parseInt(keyObject.browser_version) > 47 && userUrl && userUrl.protocol === 'https:' && keyObject.certs)
              hash = 'POST:firefox-acceptssl';
          } else if(keyObject && keyObject.browser && !keyObject.deviceName && keyObject.browser.toLowerCase() == 'internet explorer') {
            if(keyObject.autoitSendKeys != null) {
              HubLogger.miscLogger('customSendKeys', `Using Custom Autoit SendKeys for ${clientSessionID}`, LL.DEBUG, keyObject.debugSession);
              if(request.url.match(/\/element\/.*\/value$/)) {
                hash = 'POST:ie-sendkeys';
              } else if(request.url.endsWith('keys')) {
                hash = 'POST:ie-keys';
              }
            } else if(request.url.match(/\/element\/.*\/value$/) && hash != 'POST:ie11w10_file_upload' && keyObject.jsSendKeys){
              HubLogger.miscLogger('jsSendKeys', `Using Custom SendKeys using javascript for ${clientSessionID}`, LL.DEBUG, keyObject.debugSession);
              hash = 'POST:ie-jsSendKeys';
            }
          }
          if(JSON.parse(keyObject.debug_url) && request.url.indexOf('/url') > -1 && typeof log_data.url != 'string')
            HubLogger.miscLogger('checkURLstatus', `Session: ${clientSessionID} URL: ${request.url} request:  ${JSON.stringify(log_data)}`, LL.DEBUG, keyObject.debugSession);
          if(JSON.parse(keyObject.debug_url) && request.url.indexOf('/url') > -1 && typeof log_data.url == 'string'){
            //request.checkURLTimeOut = setTimeout(() => {
            //  if (typeof constants.global_registry[clientSessionID] == "undefined") return;
            //  if (constants.global_registry[clientSessionID].secondary_state != constants.secondary_states.SUCCESS) return;
            //  supporting.checkURLStatus(log_data, clientSessionID, hostname, browserstackTunnel, undefined, request);
            //}, constants.check_url_interval);

            if(constants.global_registry[clientSessionID] && !constants.global_registry[clientSessionID].tunnel && helper.isLocalhostDomainOrIP(log_data.url)){
              constants.global_registry[clientSessionID].secondary_state = 'tunnel-required';
              pubSub.publish(constants.updateKeyObject, {
                session: clientSessionID,
                changed: {
                  secondary_state: constants.global_registry[clientSessionID].secondary_state,
                },
              });
            } else if(!keyObject.realMobile) {
              constants.global_registry[clientSessionID].checkURL_url = log_data.url;
              supporting.checkURLStatus(log_data, clientSessionID, hostname, browserstackTunnel, undefined, request);
            }
          }
          // Check if internet explorer and made set timeout request
          var splited_url = request.url.split('/');
          if(constants.global_registry[keyObject.rails_session_id] && hub.isPageLoadTimeoutBrowser(keyObject.browser) && splited_url[splited_url.length - 1] === 'timeouts' && log_data.type === 'page load'){
            if(!keyObject.tunnel){
              constants.global_registry[keyObject.rails_session_id].isMetaPageLoadTimeout = false;
            }
          }
        }

        request.log_date = helper.getDate();
        request.log_data = isExecuteSync ? req_data : JSON.stringify(log_data);
        req_data = getRequestData(req_data, {
          non_pipe_url: non_pipe_url,
          keyObject: keyObject,
          clientSessionID: clientSessionID,
          remoteSessionID: remoteSessionID,
          hash: hash,
        });

        try {
          if (hash === 'POST:timeouts' && isNotUndefined(keyObject.aiSessionDetails) && isTrueString(keyObject.aiSessionDetails.implicit_delay_enabled) && typeof log_data["implicit"] === 'number') {
            // Using log_data for comparison because log_data is a JSON however req_data is a string
            const reqDataParsed = JSON.parse(req_data);
            reqDataParsed["implicit"] = Math.max(reqDataParsed["implicit"], (keyObject.aiSessionDetails.find_element_timeout || 0)*1000);
            req_data = JSON.stringify(reqDataParsed);
            request.headers['content-length'] = req_data.length;
          }
        } catch (error) {
          HubLogger.tempExceptionLogger(`Error modifying implicit timeout for AI:`, error);
        }

        params.req_data = req_data;
        params.hash = hash;
        resolve(req_data);
      })
      .catch((err) => {
        HubLogger.tempExceptionLogger(`Error in loadRequestData  ${clientSessionID}`, err, err.stack);
        reject(err);
        //response.end(JSON.stringify({value: {message: ""}, sessionId: clientSessionID, "status": 13}));
      });
    } else {
      if(payload || !request.readable){
        req_data = payload;
        requestData = req_data;
        params.req_data = req_data;
        params.requestData = requestData;
        resolve(req_data);
      }
      else
      {
       requestlib.readRequest(request).then((data) => {
         req_data = data;
         requestData = req_data;
         params.req_data = req_data;
         params.requestData = requestData;
         resolve(req_data);
       },() => {
          req_data = payload;
          requestData = req_data;
          params.req_data = req_data;
          params.requestData = requestData;
          resolve(req_data);
       });
      }
    }
  });
};
exports.loadRequestData = loadRequestData;

const getHashBasicAuthiOS = (keyObject) => {
  let os_version_for_basic_auth = parseInt(keyObject.deviceName.split('-')[1], 10);
  if (os_version_for_basic_auth === 10) {
    return 'POST:basic-auth-ios';
  } else if (os_version_for_basic_auth >= 11) {
    return 'POST:basic-auth-ios-gte11';
  }
};

const requestError = (e, keyObject, params) => {
  var request = params.request,
      response = params.response,
      retryData = params.retryData,
      retryOutput = params.retryOutput,
      remoteSessionID = params.remoteSessionID,
      clientSessionID = params.clientSessionID,
      index_counter = params.index_counter,
      callbacks = params.callbacks,
      hostname = params.hostname,
      originalUrl = params.originalUrl,
      attempt = params.attempt,
      hash = params.hash,
      req_data = params.req_data;

  if(typeof constants.global_registry[keyObject.rails_session_id] == 'undefined'){
    HubLogger.miscLogger('sessionNotFound', `Session not started or terminated for ${keyObject.rails_session_id} for request error ${e.code} Message: ${e.toString()}`, LL.WARN);
    return hub.sessionNotFound(response, keyObject, 'Keyobject not found in hub while forwarding request to selenium');
  }
  if(typeof retryData !== 'undefined'){
    HubLogger.miscLogger('processResponseError', `sessionId: ${keyObject.rails_session_id} retryData: ${retryData}`, LL.INFO);
    return hub.processResponse(request, response, keyObject, {
      data: retryData,
      output: retryOutput,
      remoteSessionID: remoteSessionID,
      clientSessionID: clientSessionID,
      index_counter: index_counter,
      callbacks: callbacks,
      hostname: hostname,
      originalUrl: originalUrl,
    });
  }
  if (constants.NODE_RETRY_ERROR_CODES.includes(e.code) || e.type === 'TimeoutError') {

    keyObject.captureCrash = true;
    pubSub.publish(constants.updateKeyObject, {
     session: keyObject.rails_session_id,
     changed: {
       captureCrash: true,
     },
    });

    if(attempt && attempt > 1) {
      if(hash == 'DELETE:'+clientSessionID){
        return hub.processResponse(request, response, keyObject, {
          data: '{"state":"success","sessionId":"'+clientSessionID+'","value":null,"class":"org.openqa.selenium.remote.Response","status":0}',
          output: {statusCode: 200, headers: constants.CHUNKED_HEADER},
          remoteSessionID: remoteSessionID,
          clientSessionID: clientSessionID,
          index_counter: index_counter,
          callbacks: callbacks,
          hostname: hostname,
          originalUrl: originalUrl,
        });
      }
      if(attempt == 3) {
        return hub.checkPossibleToProceed(keyObject, request, response, {
          remoteSessionID: remoteSessionID,
          clientSessionID: clientSessionID,
          index_counter: index_counter,
          callbacks: callbacks,
          hostname: hostname,
          originalUrl: originalUrl,
        }, () => hub.checkFailureReason(keyObject, request, response, attempt, index_counter, ERROR.HUB_TIMEOUT, e));
      }
      debug.isPortOpen(keyObject.name, keyObject.port, function(val) {
        var check_5555 = val;
        debug.isPortOpen(keyObject.name, 4022, function(val) {
          var check_4022 = val;
          HubLogger.miscLogger('portCheck', `For session id ${keyObject.rails_session_id} 5555 ${check_5555} 4022 ${check_4022}`, LL.DEBUG, keyObject.debugSession);
          if(!check_4022 && !check_5555)
            hub.checkFailureReason(keyObject, request, response, attempt, index_counter, ERROR.NETWORK_DOWN, e);
          else if (check_4022 && !check_5555)
            hub.checkFailureReason(keyObject, request, response, attempt, index_counter, ERROR.FFFF_DOWN, e);
          else {
            if(keyObject && keyObject.user) {
              keyObject.hub_to_term_error = true;
              pubSub.publish(constants.updateKeyObject, {
                session: keyObject.rails_session_id,
                changed: {
                  hub_to_term_error: true,
                },
              });
            }
            hub.checkFailureReason(keyObject, request, response, attempt, index_counter, ERROR.CONN_FAILED_TWICE, e);
          }
        });
      });
    } else {
      keyObject.name = hostname;
      HubLogger.seleniumStats('selenium-node-failure', keyObject, 'Connection failed once', `port=${e.port},err=${JSON.stringify(e)}`, keyObject.rails_session_id || '', 'nodeError');
      setTimeout(() => hub.createBridgeClientAndNode(keyObject, request, response, callbacks, ++attempt, index_counter, req_data), 1000);
    }
  } else {
    if(!keyObject.appTesting) {
      const hhplatform = keyObject.realMobile ? 'mobile' : 'desktop';
      HubLogger.hoothoot.emit('automate_miscellaneous_errors', 1, { event_type: 'automate_so_timeout_skip_retry', platform: hhplatform, product: 'automate', hub_region: constants.region, terminal_region: keyObject.terminalSubRegion, error_code: e.code, error_type: e.type });
    }
    if(e.toString() == 'Error: Parse Error'){
      // Error when response from jar was not json
      HubLogger.miscLogger('parseError', `sessionId: ${keyObject.rails_session_id} Error: ${e.toString()}`, LL.DEBUG, keyObject.debugSession);
      return hub.processResponse(request, response, keyObject, {
        data: '{"state":"success","sessionId":"'+clientSessionID+'","value":null,"class":"org.openqa.selenium.remote.Response","status":0}',
        output: {statusCode: 200, headers: constants.CHUNKED_HEADER},
        remoteSessionID: remoteSessionID,
        clientSessionID: clientSessionID,
        index_counter: index_counter,
        callbacks: callbacks,
        hostname: hostname,
        originalUrl: originalUrl,
      });
    } else if(e.type === constants.customErrorTypes.userRequestNotJSON) {
      // Error when user's request was not json
      params.data = JSON.stringify({
        value: {
          message: 'Got error while loading request data: ' + e.message,
        },
      });
      return exports.sendErrorResponse(keyObject, params);
    } else {
      hub.checkFailureReason(keyObject, request, response, attempt, index_counter, ERROR.OTHER, e);
    }
  }
};
exports.requestError = requestError;

exports.getDebugLogs = function(keyObject, log_types, cb){
  var type = log_types.pop();
  if(type){
    supporting.getLogsAndSave(type, keyObject, function(){
      exports.getDebugLogs(keyObject, log_types, cb);
    }, cb);
  }
  else cb();
};

exports.checkDebugLogging = function(request, response, keyObject, cb){
  if(keyObject.logging && !keyObject.deviceName && keyObject.browser.match(/firefox|safari|chrome/i)){
    var log_types = [];
    if(keyObject.browser.match(/firefox/i)) log_types.push('browser');
    log_types.push('driver');
    log_types.push('client');

    if(constants.LOG_TYPES.indexOf(keyObject.logging) > -1 && log_types.indexOf(keyObject.logging) == -1)
      log_types.push(keyObject.logging);

    exports.getDebugLogs(keyObject, log_types, cb);
  }
  else
    cb();
};

const sendResponse = (keyObject, params) => {
  var request = params.request,
      response = params.response,
      //requestData = params.requestData,
      clientSessionID = params.clientSessionID,
      remoteSessionID = params.remoteSessionID,
      hostname = params.hostname,
      rproxyHost = params.rproxyHost,
      //browserstackTunnel = params.browserstackTunnel,
      data = params.data,
      retryData = params.retryData,
      output = params.output,
      retryOutput = params.retryOutput,
      index_counter = params.index_counter,
      callbacks = params.callbacks,
      parsed_data = params.parsed_data,
      originalUrl = params.originalUrl;

  if (isCDP(keyObject)) {
    const { onResolve } = params;
    if (isNotUndefined(onResolve)) {
      onResolve(data);
      return;
    }
  }

  if(typeof constants.global_registry[keyObject.rails_session_id] == 'undefined') {
    HubLogger.miscLogger('sessionNotFound', `Session not started or terminated for ${keyObject.rails_session_id} in sendResponse.`, LL.WARN);
    return hub.sessionNotFound(response, keyObject, 'Session Stopped Before Returning Command Response');
  }
  //if (constants.global_registry[keyObject.rails_session_id].secondary_state == constants.secondary_states.SUCCESS) {
  //  var log_data = (requestData && requestData != "")? JSON.parse(requestData) : {};
  //  if(log_data.url && JSON.parse(keyObject.debug_url))
  //    supporting.checkURLStatus(log_data, clientSessionID, hostname, browserstackTunnel, undefined, request);
  //}

  data = retryData || data;
  output = retryOutput || output;
  var payload = {
    data: data,
    parsed_data: parsed_data,
    output: output,
    remoteSessionID: remoteSessionID,
    clientSessionID: clientSessionID,
    index_counter: index_counter,
    callbacks: callbacks,
    hostname: hostname,
    originalUrl: originalUrl,
  };
  var parsedData;
  try {
    parsedData = parsed_data || JSON.parse(data);
  } catch (e) {
    HubLogger.tempExceptionLogger(`sendResponse: Json parse error ${data}`, e, undefined, hostname, request.url);
  }

  var androidCheck = request.url.match(/\/screenshot$/) && keyObject.browser && keyObject.browser.toLowerCase() === 'android' && parsedData && parsedData['status'] !== 0 && !keyObject.realMobile;
  var iosCheck = request.url.match(/\/screenshot$/) && keyObject.browser && (keyObject.browser.toLowerCase() === 'iphone' || keyObject.browser.toLowerCase() === 'ipad') && keyObject.os.toLowerCase() === 'macelc' && !keyObject.realMobile;
  var tab4Check = keyObject.realMobile && request.url.match(/\/screenshot$/) && keyObject.os && keyObject.os.toLowerCase() === 'android' && keyObject.deviceName && keyObject.deviceName === 'Samsung Galaxy Tab 4-4.4';

  if (iosCheck || androidCheck || tab4Check) {
    var screenshotURL;
    var deviceParams = keyObject.realMobile ? constants.realDeviceSizes[keyObject.deviceName][keyObject.deviceOrientation] : constants.emulatorDeviceSizes[keyObject.deviceName][keyObject.deviceOrientation];
    if (deviceParams) {
      screenshotURL = util.format('/custom_screenshot?sessionId=%s&x=%d&y=%d&width=%d&height=%d&orientation=%s&device=%s&os=%s',clientSessionID,
      deviceParams.x, deviceParams.y, deviceParams.width, deviceParams.height,keyObject.deviceOrientation,encodeURIComponent(keyObject.device), (androidCheck || tab4Check) ? 'android' : 'ios');
    } else {
      screenshotURL = util.format('/custom_screenshot?sessionId=%s&orientation=%s&device=%s&os=%s',clientSessionID,keyObject.deviceOrientation,encodeURIComponent(keyObject.device), (androidCheck || tab4Check) ? 'android' : 'ios');
    }

    return requestlib.call({
      method: 'GET',
      hostname: rproxyHost,
      port: 45671,
      path: screenshotURL,
      headers: requestlib.appendBStackHostHeader(hostname),
    }).then((res) => {
      payload.data = res.data;
      hub.processResponse(request, response, keyObject, payload);
    }).catch((err) => {
      HubLogger.tempExceptionLogger('Error getting custom screenshot for emulator', err, undefined, hostname, request.url);
      hub.processResponse(request, response, keyObject, payload);
    });
  } else if(request.url.match(/\/screenshot$/) && keyObject.os && keyObject.os.match(/mac/) && keyObject.desktop_screenshots) {
    HubLogger.miscLogger('customScreenshot', 'Getting ScreenShot for Mac', LL.DEBUG, keyObject.debugSession);
    return requestlib.call({
      method: 'GET',
      hostname: rproxyHost,
      port: 45671,
      path: '/desktop_screenshot?x=0&y=80&width=0&height=80',
      headers: requestlib.appendBStackHostHeader(hostname),
      recordJarTime: true
    }).then((res) => {
      helper.addToJarTime(keyObject.rails_session_id, res);

      if(res.statusCode == 200) {
        payload.data = res.data;
      }
      hub.processResponse(request, response, keyObject, payload);
    }).catch((err) => {
      HubLogger.tempExceptionLogger('Error getting custom screenshot for desktop', err, undefined, hostname, request.url);
      hub.processResponse(request, response, keyObject, payload);
    });
  } else if(keyObject.realMobile && request.url.match(/\/file$/) && keyObject.os == 'android') {
    if(!parsedData || !parsedData.value) {
      return hub.processResponse(request, response, keyObject, payload);
    }

    requestlib.call({
      method: 'GET',
      hostname: rproxyHost,
      port: 45671,
      path: '/push_file?push_file=' + encodeURIComponent(parsedData.value) +
        '&device=' + encodeURIComponent(keyObject.device),
      headers: requestlib.appendBStackHostHeader(hostname),
      recordJarTime: true
    }).then((res) => {
      helper.addToJarTime(keyObject.rails_session_id, res);

      parsedData.value = res.data;
      payload.data = JSON.stringify(parsedData);
      hub.processResponse(request, response, keyObject, payload);
    }).catch(() => {
      hub.processResponse(request, response, keyObject, payload);
    });
  } else {
    exports.checkDebugLogging(request, response, keyObject, function(){
      hub.processResponse(request, response, keyObject, payload);
    });
  }
};
exports.sendResponse = sendResponse;

var sendErrorResponse = function(keyObject, params) {
  var dataObj = {};
  try {
    dataObj = JSON.parse(params.data);
  } catch(e) {
    HubLogger.tempExceptionLogger(`sendErrorResponse: Json Parse Error: data: ${params.data}`, e);
  }
  dataObj.state = 'error';

  if( !dataObj.value || !dataObj.value.message ) {
    dataObj.value = { 'message': 'An unknown server-side error occurred while processing the command.', 'error': 'unknown error'};
  }
  dataObj.status = 13;

  if (isCDP(keyObject)) {
    const { onResolve } = params;
    if (isNotUndefined(onResolve)) {
      onResolve(JSON.stringify(dataObj));
      return;
    }
  }

  let headers = (params.retryOutput || params.output || { headers: constants.CHUNKED_HEADER, statusCode: 200 }).headers;
  headers['Source-From'] = constants.NGINX_SOURCE_FROM_CODES.JAR;
  var data = hub.replaceSessionID(JSON.stringify(dataObj), params.remoteSessionID, params.clientSessionID);
  const responseStatusCode = 500;
  params.response.writeHead(responseStatusCode, headers);
  keyObject.lastResponseStatus = `${responseStatusCode}::${dataObj.status}`;
  ha.setData(keyObject.rails_session_id, keyObject);

  helper.respondWithError(params.request, params.response, data.toString());

  keyObject.lastResponseTime = Date.now();

  if (!((keyObject || {}).appTesting)) {
    HubLogger.instrumentationStats('Error : sendErrorResponse', keyObject, '', data.toString());
  }
  helper.stopSeleniumClock(keyObject.rails_session_id, params.request.url, params.request.method, helper.isW3C(keyObject), dataObj.status, params.request.log_data);
  pubSub.publish(constants.updateKeyObject, {
    session: keyObject.rails_session_id,
    changed: {
      lastResponseTime: keyObject.lastResponseTime,
      lastRequestTime: keyObject.lastRequestTime,
      outsideBrowserstackTime: keyObject.outsideBrowserstackTime,
      userHubLatency: keyObject.userHubLatency,
      seleniumRequestsCount: keyObject.seleniumRequestsCount,
      insideHubTime: keyObject.insideHubTime,
      hubProcessingTime: keyObject.hubProcessingTime,
      userToNginxTime: keyObject.userToNginxTime,
      nginxToHubTime: keyObject.nginxToHubTime,
      jarTime: keyObject.jarTime
    },
  }, false);

  helper.timeoutManagerUpdateTimeout(keyObject.rails_session_id, keyObject);
};
exports.sendErrorResponse = sendErrorResponse;

const getWindowSize = async (keyObject, params) => {
  const sel = new SeleniumClient(keyObject);
  try {
    const res = await sel.getWindowSize();
    const { value } = res;
    params.data = JSON.stringify({ sessionId: params.clientSessionID, status: 0, value });
  } catch (err) {
    params.data = JSON.stringify({ sessionId: params.clientSessionID, status: 13, value: null });
  } finally {
    exports.sendResponse(keyObject, params);
  }
};

exports.getWindowSize = getWindowSize;

const autoItSendKeys = function(keyObject, params, options, text, successCallback, errorCallback) {
  var sessionId = params ? ( params.clientSessionID || 'not found' ) : 'not found';
  var sendKeysWaitInterval = parseInt(keyObject.autoitSendKeys);
  if(isNaN(sendKeysWaitInterval) || sendKeysWaitInterval < constants.autoitSendKeysMinSleep || sendKeysWaitInterval > constants.autoitSendKeysMaxSleep) {
    sendKeysWaitInterval = constants.autoitSendKeysSleep;
  }
  HubLogger.miscLogger('autoItSendKeys', `AutoIt SendKeys sessionId: ${sessionId} Wait between Characters: ${sendKeysWaitInterval.toString()}`, LL.DEBUG, keyObject.debugSession);
  if(text != '') {
    const { redactedText = text } = options;
    HubLogger.miscLogger('Redact Text', `Redact text for session - ${sessionId} -- ${redactedText}`, LL.DEBUG, keyObject.debugSession);
    var keysOptions = {
      method: 'GET',
      timeout: options.timeout,
      hostname: keyObject.rproxyHost,
      port: 4567,
      path: '/sendkeys?sleep=' + encodeURIComponent(sendKeysWaitInterval.toString()) + '&text=' + encodeURIComponent(text),
      headers: requestlib.appendBStackHostHeader(keyObject.name),
      recordJarTime: true
    };

    requestlib.call(keysOptions).then((res) => {
      helper.addToJarTime(keyObject.rails_session_id, res);
      HubLogger.miscLogger('autoItSendKeys', `sessionId: ${sessionId} AutoIt SendKeys response ${res.data}`, LL.DEBUG, keyObject.debugSession);
      successCallback();
    }).catch((err) => {
      HubLogger.tempExceptionLogger(`Error during autoit sendkeys sessionId: ${sessionId}`, err, err.stack);
      errorCallback();
    });
  } else {
    HubLogger.miscLogger('autoItSendKeys', `sessionId: ${sessionId} User Sent Empty Text ${text}`, LL.DEBUG, keyObject.debugSession);
    successCallback();
  }
};
exports.autoItSendKeys = autoItSendKeys;

/**
 * Executes Basic Auth on Mac using Applescript
 * @param {{}} keyObject The mighty keyObject
 * @param {String} sessionId session id
 * @param {String} username Username for the Basic Auth
 * @param {String} pass Password for the Basic Auth
 * @param {String} operation Handle or Dismiss basic auth. Should pass 'handle' or 'dismiss' only
 */
const basicAuthMac = async (keyObject, sessionId = "NOT_AVAILABLE", username, pass, operation = "handle") => {
  try{
    // Sanitized browser name is required to connect with the exact process on terminal machine
    let sanitizedBrowserName = keyObject.browser.toLowerCase().match(/chrome|firefox|edge/i);
    if (!sanitizedBrowserName) throw Error(`Unsupported Browser passed: ${keyObject.browser}`);
    if (operation.toLowerCase() === "handle" && (!username || !pass)) throw Error("Basic Auth Username or Password empty");

    const queryParams = queryString.encode({
      basic_auth_username: username || "",
      basic_auth_pass: pass || "",
      browser: sanitizedBrowserName[0] || "",
      operation: operation || ""
    });

    const options = {
      method: 'GET',
      hostname: keyObject.rproxyHost,
      port: 45671,
      path: `/mac_basic_auth?${queryParams}`,
      headers: requestlib.appendBStackHostHeader(keyObject.name)
    };

    HubLogger.miscLogger(`[basicAuthMac] sessionId: ${sessionId}, Sending Basic Auth operation: ${operation} for browser: ${keyObject.browser}`, "", LL.INFO);
    const operationResult = await requestlib.call(options);
    const responseString = operationResult.data || null;
    if (responseString == "done") {
      HubLogger.miscLogger(`[basicAuthMac] sessionId: ${sessionId}, Response: `, responseString, LL.INFO);
      return true;
    } else {
      throw Error(responseString || JSON.stringify(operationResult));
    }
  } catch (e) {
    HubLogger.tempExceptionLogger(`[basicAuthMac] sessionId: ${sessionId}`, e);
    throw Error();
  }
};

exports.basicAuthMac = basicAuthMac;

const ieClick = async (keyObject, params, options, logDataObj, successCallback, errorCallback) => {
  const sessionId = (params && params.clientSessionID) || 'not found';
  const { headers, timeout } = options;
  const { name: hostname, port, rproxyHost } = keyObject;
  const [, elementValueMatch] = params.originalUrl.match(/\/element\/(.+)\/value/) || [];

  if (!elementValueMatch) {
    HubLogger.miscLogger(`IEClick: Error during POST:click sessionId: ${sessionId}`, 'elementId is null', LL.INFO);
    return errorCallback();
  }

  requestlib.appendBStackHostHeader(hostname, headers);
  const clickOptions = { headers, timeout, hostname: rproxyHost, port, method: 'POST' };
  clickOptions.path = `/wd/hub/session/${params.remoteSessionID}/element/${elementValueMatch}/click`;
  clickOptions.body = Buffer.from('{}');
  clickOptions.recordJarTime = true;
  HubLogger.miscLogger('ieClick', `POST:click: url: ${clickOptions.path} body: ${clickOptions.body} sessionId: ${sessionId}`, LL.DEBUG, keyObject.debugSession);

  try {
    const res = await requestlib.call(clickOptions);
    helper.addToJarTime(keyObject.rails_session_id, res);

    HubLogger.miscLogger('ieClick', `IEClick: Element Clicked sessionId: ${sessionId} data: ${res.data}`, LL.DEBUG, keyObject.debugSession);
    successCallback();
  } catch (err) {
    HubLogger.tempExceptionLogger(`IEClick: Error during POST:click sessionId: ${sessionId}`, err, err.stack);
    errorCallback();
  }
};
exports.ieClick = ieClick;

const iOSBasicAuthHandler = async (keyObject, username, password, callback) => {
  if(username && password) {
    const wda = new WdaClient(keyObject.name, keyObject.wda_port, keyObject.rproxyHost);
    const sel = new SeleniumClient(keyObject);
    const usernameSelectors = ['User Name', 'Username'];
    for (let tries = 0; tries <= 5; tries += 1) {
      try {
        await wda.attach();
        let elementId;
        // Checking for both selectors since iOS 13 showed both of them and keeping
        // track of device specific selectors would be a maintenance overhead.
        for (let usernameSelector of usernameSelectors) {
          try {
            if(helper.versionCompare(keyObject.appium_version,"1.20.0") < 0){ /* Appium Version < 1.20.0 */
              elementId = await wda.findElement('accessibility id', usernameSelector);
            } else { /* Appium Version >= 1.20.0 */
              elementId = await wda.findElement('predicate string',`value == "${usernameSelector}"`);
            }
            if (elementId) break;
          } catch (err) {
            // Catching the exception thrown by findElement using one of the selectors.
            // Error will be raised if no element is found by both the usernameSelectors.
            // Not modifying the existing behaviour of findElement to keep it in sync with
            // other methods of wdaClient.
          }
        }
        if (!elementId) {
          throw new Error(`No Element found after looking for the following selectors : ${usernameSelectors}`);
        }
        const clickElementResult = await wda.clickElement(elementId);
        const sendKeysResult = await sel.sendKeys(`${username}\n${password}\n`);
        // Added logging for debug purpose. Shall be removed later. This is to understand the behaviour
        // of the basic auth handler when the URL being opened is refreshed before the
        // complete execution of basic auth handler
        HubLogger.miscLogger('iOSBasicAuthHandler', `Try: ${tries} - Result of Click Command : ${JSON.stringify(clickElementResult)} for Session : ${keyObject.rails_session_id}`, LL.DEBUG, keyObject.debugSession);
        HubLogger.miscLogger('iOSBasicAuthHandler', `Try: ${tries} - Result of Send Keys : ${JSON.stringify(sendKeysResult)} for Session : ${keyObject.rails_session_id}`, LL.DEBUG, keyObject.debugSession);
        return callback(); // success
      } catch (err) {
        HubLogger.tempExceptionLogger(`Exception in Basic Auth session: ${keyObject.rails_session_id} Device: ${keyObject.deviceName}`, err);
        await Promise.delay(2000);
      }
    }
  }
  return callback(); // failure
};
exports.iOSBasicAuthHandler = iOSBasicAuthHandler;


const blockAppiumWindowCommands = (keyObject, response) => {
  response.statusCode = 400;
  response.end(JSON.stringify(constants.INVALID_COMMAND_RESPONSE('Command not supported.')));

  keyObject.lastResponseTime = Date.now();
  pubSub.publish(constants.updateKeyObject, {
    session: keyObject.rails_session_id,
    changed: {
      lastResponseTime: keyObject.lastResponseTime,
      lastRequestTime: keyObject.lastRequestTime,
      outsideBrowserstackTime: keyObject.outsideBrowserstackTime,
      userHubLatency: keyObject.userHubLatency,
      seleniumRequestsCount: keyObject.seleniumRequestsCount,
    },
  }, false);

  helper.timeoutManagerUpdateTimeout(keyObject.rails_session_id, keyObject);
};

exports.blockAppiumWindowCommands = blockAppiumWindowCommands;

var iOSInjectAllowPopupScript = function (keyObject, successCallback) {
  var sel = new SeleniumClient(keyObject);
  var script = helper.generateAllowPopupsScript(keyObject.name, keyObject.device);

  var actionAfterPageLoad = function() {
   sel.executeScript({'script': script, 'args':[]})
   .then(function() {
      HubLogger.miscLogger('iOSInjectAllowPopupScript', `iOSInjectAllowPopupScript injected session: ${keyObject.rails_session_id}`, LL.DEBUG, keyObject.debugSession);
      successCallback();
   }).catch(function(err) {
      HubLogger.tempExceptionLogger(`iOSInjectAllowPopupScript failed session: ${keyObject.rails_session_id}`, err, err.stack);
      successCallback();
   });
  };

  supporting.waitForPageLoad(actionAfterPageLoad, actionAfterPageLoad, keyObject, constants.PAGE_LOAD_IOS11_TIMEOUT);
};

exports.iOSInjectAllowPopupScript = iOSInjectAllowPopupScript;

// This method calls reset command, and then invoke appium's default reset then launch.
const resetApp = async (keyObject, params) => {
  const sel = new SeleniumClient(keyObject);
  const { clientSessionID, hostname, rproxyHost } = params;
  const customResetAppTimeout = (keyObject.idle_timeout - (keyObject.idle_timeout > 20 ? 20 : 0)) * 1000; // newcommand timeout is mapped to idle_timeout

  const serverURL = `/reset_app?device=${encodeURIComponent(keyObject.device)}&automate_session_id=${encodeURIComponent(keyObject.rails_session_id)}`;

  const termOptions = {
    hostname: rproxyHost,
    port: 45671,
    path: serverURL,
    timeout: customResetAppTimeout,
    headers: requestlib.appendBStackHostHeader(hostname),
  };

  HubLogger.miscLogger('resetApp', `Sending resetApp request to platform session: ${keyObject.rails_session_id} device : ${keyObject.device}`, LL.DEBUG, keyObject.debugSession);

  try {
    await requestlib.call(termOptions);
    await sel.resetApp();
    params.data = JSON.stringify({ sessionId: clientSessionID, status: 0, value: null });
  } catch (err) {
    HubLogger.tempExceptionLogger('Error in resetApp from hub.js', err);
    params.data = JSON.stringify({ sessionId: clientSessionID, status: 13, value: null });
  } finally {
    exports.sendResponse(keyObject, params);
  }
};

exports.resetApp = resetApp;

const installApp = async(keyObject, params) => {
  const { clientSessionID, hostname, rproxyHost, req_data } = params;
  const customTimeout = (keyObject.idle_timeout - (keyObject.idle_timeout > 20 ? 20 : 0)) * 1000;
  const reqDataObj  = JSON.parse(req_data);
  const bsAppHashedId = reqDataObj['appPath'];
  const appHashedId = bsAppHashedId.split('://')[1];

  const serverURL = `/install_app?device=${encodeURIComponent(keyObject.device)}&automate_session_id=${encodeURIComponent(keyObject.rails_session_id)}&app_hashed_id=${appHashedId}`;

  const termOptions = {
    hostname: rproxyHost,
    port: 45671,
    path: serverURL,
    timeout: customTimeout,
    headers: requestlib.appendBStackHostHeader(hostname),
  };

  HubLogger.miscLogger('InstallApp', `Sending installApp request to platform session: ${keyObject.rails_session_id} device : ${keyObject.device}`, LL.DEBUG, keyObject.debugSession);

  try {
    const installAppResponse = await requestlib.call(termOptions);
    HubLogger.miscLogger('InstallApp', `Response for installApp from platform is ${JSON.stringify(installAppResponse)}`, LL.DEBUG, keyObject.debugSession);

    if(installAppResponse["statusCode"] === 200){
      params.data = JSON.stringify({ sessionId: clientSessionID, status: 0, value: installAppResponse["data"] });
    } else {
      params.data = JSON.stringify({ sessionId: clientSessionID, status: 13, value: { message: installAppResponse["data"] }});
      params.output = { headers: constants.CHUNKED_HEADER, statusCode: 500 };
    }
  } catch (err) {
    HubLogger.tempExceptionLogger('Error in installApp from hub.js', err);
    params.data = JSON.stringify({ sessionId: clientSessionID, status: 13, value: null });
    params.output = { headers: constants.CHUNKED_HEADER, statusCode: 500 };
  } finally {
    exports.sendResponse(keyObject, params);
  }
};

exports.installApp = installApp;

// This method calls reset command on platform which reinstalls app, and then invoke appium's default launch.
const launchApp = async (keyObject, params) => {
  const sel = new SeleniumClient(keyObject);
  const { clientSessionID, hostname, rproxyHost } = params;
  const customResetAppTimeout = (keyObject.idle_timeout - (keyObject.idle_timeout > 20 ? 20 : 0)) * 1000; // newcommand timeout is mapped to idle_timeout

  const serverURL = `/reset_app?device=${encodeURIComponent(keyObject.device)}&automate_session_id=${encodeURIComponent(keyObject.rails_session_id)}`;

  const termOptions = {
    hostname: rproxyHost,
    port: 45671,
    path: serverURL,
    timeout: customResetAppTimeout,
    headers: requestlib.appendBStackHostHeader(hostname),
  };

  HubLogger.miscLogger('launchApp', `Sending launchApp request to platform session: ${keyObject.rails_session_id} device : ${keyObject.device}`, LL.DEBUG, keyObject.debugSession);

  try {
    await requestlib.call(termOptions);
    await sel.launchApp();
    params.data = JSON.stringify({ sessionId: clientSessionID, status: 0, value: null });
  } catch (err) {
    HubLogger.tempExceptionLogger('Error in launchApp from hub.js', err);
    params.data = JSON.stringify({ sessionId: clientSessionID, status: 13, value: null });
  } finally {
    exports.sendResponse(keyObject, params);
  }
};

exports.launchApp = launchApp;
