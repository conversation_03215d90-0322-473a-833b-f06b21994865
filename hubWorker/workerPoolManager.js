'use strict';

/* eslint-disable no-prototype-builtins */

const { EventEmitter } = require('events');
const path = require('path');
const { Worker } = require('worker_threads');
const constants = require('../constants');

class WorkerPoolManager extends EventEmitter {
  /*
    Constructor for Pool Manager.
    conf - JSON Object containing conf around individual task worker setup
  */
  constructor(conf) {
    super();
    this.conf = conf;
    this.numThreads = {}; // The number of Workers in each Worker task pool
    this.workersFile = {}; // JS file for each worker
    this.workers = {}; // Array of individual task workers
    this.freeWorkers = {}; // Array of available workers
    this.tasksQueue = {}; // Array of queued task for each task
    this.workerFreeEvent = {}; // Symbols for worker free event
    this.workerPerformance = {}; // Track worker performance
    this.kTaskInfo = Symbol('kTaskInfo');
    this.kTaskPerformance = Symbol('kTaskPerformance');
    this.setupWorkers();
  }

  /*
    To setup the Manager call and add all the workers.
    conf - Setup all the queues and Workers
  */
  // eslint-disable-next-line max-lines-per-function
  setupWorkers() {
    // eslint-disable-next-line guard-for-in
    Object.keys(this.conf).forEach((key) => {
      this.numThreads[key] = this.conf[key].numberWorkers;
      this.workersFile[key] = this.conf[key].workerFile;
      this.workers[key] = [];
      this.freeWorkers[key] = [];
      this.tasksQueue[key] = [];
      this.workerPerformance[key] = {
        total: 0,
        max: 0,
        min: 0,
        count: 0
      };
      this.workerFreeEvent[key] = Symbol(`kWorkerFreedEvent_${key}`);
    });

    Object.keys(this.numThreads).forEach((key) => {
      if (this.numThreads.hasOwnProperty(key) && this.workersFile.hasOwnProperty(key)) {
        for (let i = 0; i < this.numThreads[key]; i++) this.addNewWorker(key, this.workersFile[key]);
      }
    });

    // Any time the kWorkerFreedEvent is emitted, dispatch
    // the next task pending in the queue, if any.
    Object.keys(this.workerFreeEvent).forEach((key) => {
      if (this.workerFreeEvent.hasOwnProperty(key) && this.tasksQueue.hasOwnProperty(key)) {
        this.on(this.workerFreeEvent[key], () => {
          if (this.tasksQueue[key].length > 0) {
            const tasksList = this.tasksQueue[key].splice(0, parseInt(constants.kafkaWorkerFlags.batchSize, 10) || 100);
            this.runTask(tasksList, tasksList[0].workerName, tasksList[0].callback, 'array');
          }
        });
      }
    });
  }

  /*
    Get metric around workers.
  */
  getInstrumentationMetric() {
    const metric = {};
    Object.keys(this.conf).forEach((key) => {
      metric[`worker_${key}_length`] = this.tasksQueue[key].length;
      metric[`worker_${key}_perormance_total`] = this.workerPerformance[key].total;
      metric[`worker_${key}_perormance_min`] = this.workerPerformance[key].min;
      metric[`worker_${key}_perormance_max`] = this.workerPerformance[key].max;
      metric[`worker_${key}_perormance_count`] = this.workerPerformance[key].count;
      this.workerPerformance[key] = {
        total: 0,
        max: 0,
        min: 0,
        count: 0
      };
    });
    return metric;
  }

  /*
    To add worker to a particular Worker Pool.
    workerName - Name of worker to be added
    workerFile - File which contains the worker code
  */
  addNewWorker(workerName, workerFile) {
    const worker = new Worker(path.resolve(__dirname, workerFile));
    worker.on('message', (result) => {
      // In case of success: Call the callback that was passed to `runTask`,
      // remove the `TaskInfo` associated with the Worker, and mark it as free
      // again.

      worker[this.kTaskInfo](null, result);
      worker[this.kTaskInfo] = null;
      const elapsedTime = new Date() - worker[this.kTaskPerformance];
      worker[this.kTaskPerformance] = null;
      this.freeWorkers[workerName].push(worker);
      this.emit(this.workerFreeEvent[workerName]);
      this.workerPerformance[workerName].count += 1;
      this.workerPerformance[workerName].total += elapsedTime;
      this.workerPerformance[workerName].max = Math.max(elapsedTime, this.workerPerformance[workerName].max);
      this.workerPerformance[workerName].min = Math.min(elapsedTime, this.workerPerformance[workerName].min);
    });
    worker.on('error', (err) => {
      // In case of an uncaught exception: Call the callback that was passed to
      // `runTask` with the error.
      if (worker[this.kTaskInfo]) worker[this.kTaskInfo](err, null);
      else this.emit('error', err);
      // Remove the worker from the list and start a new Worker to replace the
      // current one.
      const elapsedTime = new Date() - worker[this.kTaskPerformance];
      worker[this.kTaskPerformance] = null;
      this.workers[workerName].splice(this.workers[workerName].indexOf(worker), 1);
      this.addNewWorker(workerName, workerFile);
      this.workerPerformance[workerName].count += 1;
      this.workerPerformance[workerName].total += elapsedTime;
      this.workerPerformance[workerName].max = Math.max(elapsedTime, this.workerPerformance[workerName].ma);
      this.workerPerformance[workerName].min = Math.min(elapsedTime, this.workerPerformance[workerName].min);
    });
    this.workers[workerName].push(worker);
    this.freeWorkers[workerName].push(worker);
    this.emit(this.workerFreeEvent[workerName]);
  }

  /*
    To run a task in a given Worker Pool. It will be add to the queue if all workers are busy.
    taskParams - Task to be run i.e. params passed to worker.
    workerName - Name of worker which need to perform the task.
    callback - Callback (if any) to be called post completion of task.
  */
  runTask(taskParams, workerName, callback, workerType = 'single') {
    if (this.freeWorkers[workerName].length === 0) {
      // No free threads, wait until a worker thread becomes free.
      if (workerType === 'array') {
        this.tasksQueue[workerName] = this.tasksQueue[workerName].concat(taskParams);
      } else {
        this.tasksQueue[workerName].push({ taskParams, workerName, callback });
      }
      return;
    }

    const worker = this.freeWorkers[workerName].pop();
    worker[this.kTaskInfo] = callback;
    worker[this.kTaskPerformance] = new Date();
    if (workerType === 'array') {
      taskParams = {
        taskList: taskParams.map(task => task.taskParams),
        workerType
      };
      worker.postMessage(taskParams);
    } else {
      taskParams.workerType = workerType;
      worker.postMessage(taskParams);
    }
  }

  /*
    To terminate all workers once the task queue is full.
  */
  close() {
    let taskRemaing = false;
    Object.keys(this.tasksQueue).forEach((key) => {
      if (this.tasksQueue.hasOwnProperty(key)) {
        if (this.tasksQueue[key].length > 0) {
          taskRemaing = true;
        }
      }
    });
    if (taskRemaing) {
      setTimeout(this.close.bind(this), 10000);
      return;
    }
    Object.keys(this.workers).forEach((workerName) => {
      if (this.workers.hasOwnProperty(workerName)) {
        this.workers[workerName].forEach((worker) => {
          this.workers[workerName].splice(this.workers[workerName].indexOf(worker), 1);
          worker.terminate();
        });
      }
    });
  }
}

// Can be stored in constants or config
const workerConf = constants.workerConf;

module.exports = new WorkerPoolManager(workerConf);
