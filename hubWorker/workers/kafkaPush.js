'use strict';

const { parentPort } = require('worker_threads');
const kafkaProducer = require('../../lib/kafka/kafkaProducer');

kafkaProducer.initKafkaProducers();
parentPort.on('message', (task) => {
  if (task.workerType === 'array') {
    task.taskList.forEach((t) => {
      kafkaProducer.uploadLogPartToKafkaV1(t.requiredKeyobject, t.type, t.logdata, t.topic);
    });
  } else {
    kafkaProducer.uploadLogPartToKafkaV1(task.requiredKeyobject, task.type, task.logdata, task.topic);
  }
  parentPort.postMessage('done'); // this is necessory to free the worker so that it can pick up next task.
});
