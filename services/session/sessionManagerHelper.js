'use strict';

const constants = require('../../constants');
const util = require('util');
const ha = require('../../ha');
const instrumentation = require('../../helpers/instrumentation');
const HubLogger = require('../../log');
const pubSub = require('../../pubSub');
const helper = require('../../helper');

const LL = constants.LOG_LEVEL;

const recreateRegistry = (sessionKeyObj) => {
  const timeoutID = undefined;
  constants.timeout_registry[sessionKeyObj.rails_session_id] = {
    timeoutID,
  };
};

const removeFromMemory = (sessionKeyObj) => {
  if (constants.sessions_registry[sessionKeyObj.rails_session_id]) {
    delete constants.sessions_registry[sessionKeyObj.rails_session_id];
  }
  if (
    constants.stop_sessions_registry[sessionKeyObj.os] &&
    constants.stop_sessions_registry[sessionKeyObj.os][sessionKeyObj.rails_session_id]
  ) {
    delete constants.stop_sessions_registry[sessionKeyObj.os][sessionKeyObj.rails_session_id];
  }
  if (constants.global_registry[sessionKeyObj.rails_session_id]) {
    delete constants.global_registry[sessionKeyObj.rails_session_id];
  }
  if (constants.timeout_registry[sessionKeyObj.rails_session_id]) {
    delete constants.timeout_registry[sessionKeyObj.rails_session_id];
  }
  if (constants.execution_time_registry[sessionKeyObj.rails_session_id]) {
    delete constants.execution_time_registry[sessionKeyObj.rails_session_id];
  }
  if (constants.global_ws_registry[sessionKeyObj.rails_session_id]) {
    instrumentation.pushFeatureUsage(sessionKeyObj.rails_session_id, { ws: { closeBS: { timestamp: Date.now(), reason: 'removed from region' } } }, () => { });
    constants.global_ws_registry[sessionKeyObj.rails_session_id].terminate();
    delete constants.global_ws_registry[sessionKeyObj.rails_session_id];
  }
};

const getKeyObject = async (sessionId) => {
  let sessionKeyObj = constants.global_registry[sessionId];
  if (sessionKeyObj) {
    return sessionKeyObj;
  }
  const getDataPromisified = util.promisify(ha.getData);
  sessionKeyObj = await getDataPromisified(sessionId) || undefined;
  if (sessionKeyObj) {
    removeFromMemory(sessionKeyObj);
    constants.global_registry[sessionKeyObj.rails_session_id] = sessionKeyObj;
    recreateRegistry(sessionKeyObj);
  }
  return sessionKeyObj;
};

const refreshAndAddKeyObjectToThisHub = (key, hubAddr, responseObject) => {
  HubLogger.miscLogger(`fetchSessionDataFromOtherHub_${key}`, `Got data for ${key} from ${hubAddr} and lastRequestTime ${responseObject.lastRequestTime}`, LL.INFO);
  removeFromMemory(responseObject);
  constants.global_registry[key] = responseObject;
  helper.redisClient.del(constants.shouldPersistKeyObjectFlagPrefix + key);
  recreateRegistry(responseObject);
  responseObject.timeoutManagerIndex = helper.timeoutManagerGetIndex(key);
  if (!constants.global_registry[key].toggle) {
    constants.global_registry[key].toggle = 1;
    HubLogger.seleniumStats('hub-dns-toggle', { sessionId: key, name: constants.global_registry[key].user }, '', `Hub toggled from ${constants.region} to ${hubAddr}`, key, 'hub-health');
  } else {
    constants.global_registry[key].toggle += 1;
  }
  pubSub.publish(constants.updateKeyObject, {
    session: key,
    changed: {
      timeoutManagerIndex: responseObject.timeoutManagerIndex,
      toggle: constants.global_registry[key].toggle
    },
  });
  HubLogger.miscLogger('SESSION_TOGGLED', `Session toggled from  ${hubAddr} to this region for session ${key}`, LL.INFO);
};

module.exports = {
  getKeyObject,
  recreateRegistry,
  removeFromMemory,
  refreshAndAddKeyObjectToThisHub
};
