'use strict';

const { BaseStopService } = require('./BaseStopService');
const constants = require('../../../constants');
const requestlib = require('../../../lib/request');
const helper = require('../../../helper');
const { filterAWSKeys } = require('../../../log');
const HubLogger = require('../../../log');
const { isTrueString } = require('../../../typeSanity');

const LL = constants.LOG_LEVEL;

class StopVideoRecordingService extends BaseStopService {
  constructor(sessionKeyObj, source) {
    super();
    if (source === 'start-error') {
      // in case of 'start-error', the sessionKeyObj is actually 'options' object. Therefore, the extraction required.
      sessionKeyObj = this.getAttributesWhenBSF(sessionKeyObj);
    }
    this.sessionKeyObj = sessionKeyObj;
  }

  async handleStop() {
    if (!this.sessionKeyObj) {
      return;
    }

    helper.pushToCLS('stop_video', {
      user_id: this.sessionKeyObj.user,
      session_id: this.sessionKeyObj.rails_session_id,
      terminal: this.sessionKeyObj.hostname
    }, this.sessionKeyObj.appTesting);
    constants.stop_sessions_registry[this.sessionKeyObj.os] = constants.stop_sessions_registry[this.sessionKeyObj.os] || {};
    constants.stop_sessions_registry[this.sessionKeyObj.os][this.sessionKeyObj.rails_session_id] = 1;

    const videoRecParams = this.getStopVideoParams();
    const url = `/stop_video_rec?${requestlib.getEncodedURLParams(videoRecParams)}`;
    const terminalResponse = await this.terminalRequest(url);
    if (terminalResponse && terminalResponse.data) {
      this.handleStopVideoResponse(url, terminalResponse);
    }
  }

  /* eslint-disable max-lines-per-function */
  getStopVideoParams() {
    const videoRecParams = {
      video: this.sessionKeyObj.video,
      video_aws_keys: this.sessionKeyObj.video_aws_keys,
      video_aws_secret: this.sessionKeyObj.video_aws_secret,
      video_aws_bucket: this.sessionKeyObj.video_aws_bucket,
      video_aws_region: this.sessionKeyObj.video_aws_region,
      video_aws_storage_class: this.sessionKeyObj.video_aws_storage_class,
      logs_new_bucketing: this.sessionKeyObj.logs_new_bucketing,
      logs_aws_keys: this.sessionKeyObj.logs_aws_keys,
      logs_aws_secret: this.sessionKeyObj.logs_aws_secret,
      logs_aws_bucket: this.sessionKeyObj.logs_aws_bucket,
      logs_aws_region: this.sessionKeyObj.logs_aws_region,
      logs_aws_storage_class: this.sessionKeyObj.logs_aws_storage_class,
      video_file: this.sessionKeyObj.video_file,
      upload_fg: false,
      logHost: constants.zombie_server,
      edsHost: constants.eds_server,
      edsPort: constants.eds_port,
      edsKey: constants.eds_key,
      selenium_version: this.sessionKeyObj.selenium_version,
      session_id: this.sessionKeyObj.sessionId || this.sessionKeyObj.rails_session_id,
      uploadWebDriverLogs: this.sessionKeyObj.webDriverLogs && constants.uploadWebDriverLogs,
      webdriver_logs_key: 'selenium-logs.txt', // Decide the name here based on product requirements.
      modifyLogsForAi: this.sessionKeyObj.aiEnabledSessions || 'false',
    };
    if (this.sessionKeyObj.tunnel) videoRecParams.local = true;
    if (this.sessionKeyObj.captureCrash) videoRecParams.capture_crash = true;
    if (this.sessionKeyObj.isPlaywright) videoRecParams.genre = 'playwright';
    return videoRecParams;
  }

  /* eslint-disable max-lines-per-function */
  // NOTE: Refactor remove unnecessary code and restructure.
  async handleStopVideoResponse(url, response) {
    const getData = `${url}&terminal_ip=${this.sessionKeyObj.name}`;
    const data = response.data;
    try {
      const parsedData = JSON.parse(data);
      if (typeof parsedData.error === 'undefined') {
        HubLogger.miscLogger('terminalRequest', `${this.sessionKeyObj.sessionId} Response code while terminalRequest: ${response.statusCode} for ${filterAWSKeys(getData)} ${data}`, LL.WARN);
        if (parseFloat(parsedData.file_size) > 0 && parseFloat(parsedData.file_size) < 1 && parseFloat(parsedData.time) > 10) {
          HubLogger.seleniumStats('video-stats-large-upload-time', this.sessionKeyObj, 'None - Large Upload Time', parsedData.time, parsedData.file_size);
        } else if (parseFloat(parsedData.file_size) > 1 && parseFloat(parsedData.time) / parseFloat(parsedData.file_size) > 10) {
          HubLogger.seleniumStats('video-stats-very-large-upload-time', this.sessionKeyObj, 'None - Very Large Upload Time', parsedData.time, parsedData.file_size);
        } else if (parseFloat(parsedData.file_size) > 1 && parseFloat(parsedData.time) / parseFloat(parsedData.file_size) > 5) {
          HubLogger.seleniumStats('video-stats-large-upload-time', this.sessionKeyObj, 'None - Large Upload Time', parsedData.time, parsedData.file_size);
        }
        if (parsedData.active_window) {
          const lowerCaseActiveWindow = parsedData.active_window.toLowerCase();
          let bucket = ''; // eslint-disable-line no-unused-vars
          const bucketHash = {
            facebook: 'facebook',
            'looks like something went wrong': 'something_wrong',
            'command line server for the ie driver': 'command_line_server',
            'not responding': 'not_responding',
            'data:, - google chrome': 'chrome_data_page',
            chromedriver: 'chrome_driver',
            'choose file to upload': 'file_upload',
            'firefox stopped working': 'firefox_stopped',
            'ie stopped working': 'ie_stopped',
            'site cant be reached': 'site_unreachable',
            'site can\'t be reached': 'site_unreachable',
            'not available': 'site_unreachable',
            'save as': 'save_dialog',
            alert: 'alert'
          };
          Object.keys(bucketHash).forEach((element) => {
            if (lowerCaseActiveWindow.indexOf(element) > -1) {
              bucket = bucketHash[element];
            }
          });
          // FIXME: remove bucket calculation if not required.
        }
      } else {
        HubLogger.exceptionLogger('terminalRequest', `${this.sessionKeyObj.sessionId} Error in response for terminalRequest. Exception: ${data}`, this.sessionKeyObj.name, getData);
        HubLogger.seleniumStats('video-stats-exception', this.sessionKeyObj, parsedData.error, JSON.stringify(parsedData), 'Error uploading video');
        return;
      }
    } catch (err) {
      HubLogger.exceptionLogger('terminalRequest', `${this.sessionKeyObj.sessionId} Error while parsing and processing response in terminalRequest. Exception: ${err.toString()}`, this.sessionKeyObj.name, getData);
      HubLogger.seleniumStats('video-stats-exception', this.sessionKeyObj, err.toString(), data, 'Error in parsing response object');
    }
  }


  // NOTE: Refactor move to terminal client in future if reusable.
  async terminalRequest(url) {
    let res;
    let getData = url;
    if (url === '') return res;
    const portNo = this.sessionKeyObj.os.indexOf('win') > -1 ? '4567' : '45671';
    this.sessionKeyObj.sessionId = this.sessionKeyObj.rails_session_id;
    getData += `&terminal_ip=${this.sessionKeyObj.name}`;
    HubLogger.miscLogger('terminalRequest', `${this.sessionKeyObj.sessionId} Starting terminalRequest: for ${filterAWSKeys(getData)}`, LL.INFO);
    const terminalUrl = `http://${this.sessionKeyObj.name}:${portNo}${getData}`;
    const isAppAutomate = this.sessionKeyObj.appTesting || false;
    helper.pushToCLS('terminal_request', {
      user_id: this.sessionKeyObj.user,
      session_id: this.sessionKeyObj.sessionId,
      terminal: this.sessionKeyObj.name,
      get_url: filterAWSKeys(terminalUrl)
    }, isAppAutomate);
    try {
      res = await requestlib.call({
        hostname: this.sessionKeyObj.rproxyHost,
        port: portNo,
        path: getData,
        timeout: this.sessionKeyObj.os.indexOf('win') > -1 ? 60000 : 10000,
        headers: requestlib.appendBStackHostHeader(this.sessionKeyObj.name),
      });
      helper.pushToCLS('terminal_response', {
        user_id: this.sessionKeyObj.user,
        session_id: this.sessionKeyObj.sessionId,
        terminal: this.sessionKeyObj.name,
        response: res.data
      }, isAppAutomate);
    } catch (error) {
      HubLogger.exceptionLogger('terminalRequest Error', `${error.type} while getURL ${error.toString()}`);
      HubLogger.seleniumStats('video-stats-exception', this.sessionKeyObj, error.toString(), error.type, 'Timedout Error');
    }
    return res;
  }
  /* eslint-enable max-lines-per-function */

  getAttributesWhenBSF(options) {
    const keyObject = {
      video: (options.browserstackParams['browserstack.video'] || false).toString() === 'true',
      video_aws_keys: options.browserstackParams['browserstack.video.aws.key'],
      video_aws_secret: options.browserstackParams['browserstack.video.aws.secret'],
      video_aws_bucket: options.browserstackParams['browserstack.video.aws.s3bucket'],
      video_aws_region: options.browserstackParams['browserstack.video.aws.region'],
      video_aws_storage_class: options.browserstackParams['browserstack.video.aws.storageclass'],
      video_file: options.browserstackParams['browserstack.video.filename'] || 'video',
      logs_new_bucketing: isTrueString(options.bsCaps.new_bucketing),
      logs_aws_keys: options.browserstackParams['browserstack.logs.aws.key'],
      logs_aws_secret: options.browserstackParams['browserstack.logs.aws.secret'],
      logs_aws_bucket: options.browserstackParams['browserstack.logs.aws.s3bucket'],
      logs_aws_region: options.browserstackParams['browserstack.logs.aws.region'],
      logs_aws_storage_class: options.browserstackParams['browserstack.logs.aws.storageclass'],
      selenium_version: options.selenium_version || options.bsCaps.selenium_version || options.browserstackParams['browserstack.selenium.jar.version'] || options.browserstackParams['browserstack.selenium_version'],
      captureCrash: true,
      tunnel: (options.browserstackParams['browserstack.tunnel'] || false).toString() === 'true',
      os: options.bsCaps.orig_os,
      user: options.post_params.u,
      sessionId: options.sessionId || options.rails_session_id,
      rails_session_id: options.sessionId || options.rails_session_id,
      webDriverLogs: (options.browserstackParams['browserstack.seleniumLogs'] || false).toString() === 'true',
      name: options.host_name,
      user_id: options.user_id,
      rproxyHost: options.rproxyHost
    };
    return keyObject;
  }
}

module.exports = { StopVideoRecordingService };
