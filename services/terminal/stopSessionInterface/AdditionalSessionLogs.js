'use strict';

const { BaseStopService } = require('./BaseStopService');
const constants = require('../../../constants');
const HubLogger = require('../../../log');
const helper = require('../../../helper');
const requestlib = require('../../../lib/request');
const { isNotUndefined, isUndefined } = require('../../../typeSanity');
const util = require('util');

const LL = constants.LOG_LEVEL;

class AdditionalSessionLogs extends BaseStopService {
  constructor(sessionKeyObj) {
    super();
    this.sessionKeyObj = sessionKeyObj;
  }

  async handleStop() {
    if (this.sessionKeyObj && this.sessionKeyObj.consoleLogsEnabled) {
      // This flow should not be triggered in the case of Playwright.
      if (!this.sessionKeyObj.isPlaywright) {
        // In case of mac we send delete to jar, thus we need to get console logs in sync
        if (this.sessionKeyObj.os.match(/mac/i) || (this.sessionKeyObj.os.match(/ios/i) && !this.sessionKeyObj.appTesting)) {
          await this.retrieveAndUploadConsoleLogs();
        } else {
          this.retrieveAndUploadConsoleLogs();
        }
      }
    }
    this.generateNetworkLogs();
  }

  async retrieveAndUploadConsoleLogs() {
    const logData = await this.retrieveConsoleLogs();
    HubLogger.miscLogger('fetchConsoleLogs', `console logs fetched for session ${this.sessionKeyObj.rails_session_id}`, LL.INFO);
    if (logData === undefined) {
      return;
    }

    if (logData.status === 0 || Array.isArray(logData.value)) {
      this.appendConsoleLogs(JSON.stringify(logData.value));
    }
  }

  /* eslint-disable max-lines-per-function */
  // TODO : move to jar/selenium client in future
  async retrieveConsoleLogs() {
    const body = (this.sessionKeyObj.os.toLowerCase() === 'ios' && this.sessionKeyObj.browser.toLowerCase().match(/chromium_(iphone|ipad)/)) ? { type: 'safariConsole' } : { type: 'browser' };
    const options = {
      method: 'POST',
      hostname: this.sessionKeyObj.rproxyHost,
      port: this.sessionKeyObj.port,
      path: `/wd/hub/session/${this.sessionKeyObj.key}/log`,
      body: Buffer.from(JSON.stringify(body)),
      recordJarTime: true,
      mockPerformanceJarEndpoint: this.sessionKeyObj.mockPerformanceJarEndpoint,
    };

    /**
     * In w3c protocol /log endpoint is not supported which is used to retrieve console logs. New endpoint for console logs
     * is /wd/hub/session/<id>/se/log. This endpoint only supports chrome browsers. Refer ACE-190.
     */
    if (this.sessionKeyObj.dialect === 'W3C') {
      if (this.sessionKeyObj.os === 'android') {
        options.path = `/wd/hub/session/${this.sessionKeyObj.key}/log`;
      } else {
        options.path = `/wd/hub/session/${this.sessionKeyObj.key}/se/log`;
      }
    }

    if (this.sessionKeyObj.browser === 'firefox') {
      HubLogger.miscLogger('retrieveConsoleLogs', 'Fetching console logs from FoxDriver Server.', LL.INFO);
      options.port = constants.foxDriverServerPort;
    }

    let headers = {
      accept: 'application/json',
      'content-type': 'application/json; charset=utf-8',
      'content-length': options.body.length,
      'accept-encoding': 'gzip;q=1.0,deflate;q=0.6,identity;q=0.3',
    };
    requestlib.appendBStackHostHeader(this.sessionKeyObj.name, headers);
    headers = helper.updateHeadersForSelenium4Jars(this.sessionKeyObj, headers);

    options.headers = headers;

    try {
      const res = await requestlib.call(options);
      helper.addToJarTime(this.sessionKeyObj.rails_session_id, res);
      HubLogger.miscLogger('retrieveConsoleLogs', `Console Logs request for sessionId: ${this.sessionKeyObj.rails_session_id}`, LL.DEBUG);
      let jsonData;
      try {
        jsonData = JSON.parse(res.data);
      } catch (e) {
        helper.kafkaLogProducerErrorToZombie(constants.KAFKA_LOG_PARSING_ERROR_RETRIEVE, constants.kafkaConfig.console_logs_topic, this.sessionKeyObj.rails_session_id);
        HubLogger.miscLogger('retrieveConsoleLog', `Response is not json for sessionId: ${this.sessionKeyObj.rails_session_id} Error: ${e.message}`, LL.INFO);
        return undefined;
      }
      return jsonData;
    } catch (error) {
      helper.kafkaLogProducerErrorToZombie(constants.KAFKA_LOG_FETCHING_ERROR, constants.kafkaConfig.console_logs_topic, this.sessionKeyObj.rails_session_id);
      HubLogger.miscLogger('retrieveConsoleLogs', `Retrieving console logs failed for sessionId: ${this.sessionKeyObj.rails_session_id}`, LL.INFO);
      return undefined;
    }
  }
  /* eslint-enable max-lines-per-function */

  appendConsoleLogs(logString, logObject = undefined) {
    if (this.sessionKeyObj.appTesting) return;
    try {
      if (isUndefined(logObject)) {
        logObject = JSON.parse(logString);
      }

      // For a case where logString is null, JSON.parse gives null
      if (isUndefined(logObject) || !Array.isArray(logObject)) {
        throw new Error('logObject is undefined/null/invalid');
      }
    } catch (error) {
      helper.kafkaLogProducerErrorToZombie(constants.KAFKA_LOG_PARSING_ERROR, constants.kafkaConfig.console_logs_topic, this.sessionKeyObj.rails_session_id);
      // Print logString only when it is convertible to string
      HubLogger.exceptionLogger(`Error parsing console logs for sessionId: ${this.sessionKeyObj.rails_session_id} ${isNotUndefined(logString) ? `logString: ${logString.toString().substring(0, 100)}` : ''}`, '', '', error);
      return;
    }

    // If code reaches here that means json parsing yielded expected results(json object)
    const appendString = logObject.map((logHash) => {
      let mappedLogString = '';
      if (isNotUndefined(logHash.timestamp) || isNotUndefined(logHash.level) || isNotUndefined(logHash.message)) {
        mappedLogString = `${logHash.timestamp}:${logHash.level}:${logHash.message}\r\n`;
      }
      return mappedLogString;
    });
    HubLogger.uploadLogPartToKafka(this.sessionKeyObj, null, appendString.join(''), constants.kafkaConfig.console_logs_topic);
  }

  // TODO : move to terminal client in future
  async generateNetworkLogs() {
    if (!(this.sessionKeyObj && this.sessionKeyObj.networkLogs && this.sessionKeyObj.os === 'android') || this.sessionKeyObj.appTesting) {
      return;
    }
    try {
      const res = await requestlib.call({
        method: 'GET',
        hostname: this.sessionKeyObj.rproxyHost,
        port: constants.chrome_har_capturer_server_port_v2,
        path: util.format('/stop_capture_v2?device=%s&sessionid=%s', this.sessionKeyObj.device, this.sessionKeyObj.rails_session_id),
        headers: requestlib.appendBStackHostHeader(this.sessionKeyObj.name),
      });
      HubLogger.miscLogger(
        'STOP_HAR_RECORD_ANDROID',
        `Stopped successfully for session: ${this.sessionKeyObj.rails_session_id} responseStatusCode ${(res.statusCode || 'undefined')} data: ${(res.data || 'undefined')}`,
        LL.INFO
      );
    } catch (error) {
      HubLogger.miscLogger('STOP_HAR_RECORD_ANDROID', `HAR stop failed for session ID: ${this.sessionKeyObj.rails_session_id} error: ${error.toString()}`, LL.INFO);
    }
  }
}

module.exports = { AdditionalSessionLogs };
