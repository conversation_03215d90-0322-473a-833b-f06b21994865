'use strict';

const requestlib = require('../../lib/request');
const constants = require('../../constants');

const getAttributeTypeOptions = (attributeUrlParts, requestStateObj, options) => ({
  path: Buffer.from(attributeUrlParts.join('/'), 'utf8'),
  method: 'GET',
  hostname: requestStateObj.rproxyHost,
  port: options.port,
  headers: {
    'accept-encoding': 'gzip',
    connection: 'Keep-Alive',
    host: options.headers.host,
  },
  recordJarTime: true
});

const getAttributeType = async (requestStateObj, options, attributeUrlParts) => {
  const attributeOptions = getAttributeTypeOptions(attributeUrlParts, requestStateObj, options);
  requestlib.appendBStackHostHeader(options.headers[constants.BSTACK_HOST_HEADER], attributeOptions.headers);
  return requestlib.call(attributeOptions);
};

const getScriptForSetValue = (sendData, sessionKeyObj) => {
  if (sessionKeyObj.browser === 'internet explorer' &&
  ['6.0', '7.0', '8.0'].includes(sessionKeyObj.browser_version)) {
    // onchange, oninput is not supported <= IE8
    return `arguments[0].value += '${sendData}';
      function generateEvent(eventName) {
        var eventObj = document.createEventObject('Event');
        eventObj.eventName = eventName;
        eventObj.eventType = eventName;
        return eventObj;
      }
      arguments[0].fireEvent('onkeydown', generateEvent('keydown'));
      arguments[0].fireEvent('onkeyup', generateEvent('keyup'));
      arguments[0].click();`;
  }
  return `arguments[0].value += '${sendData}';
      function generateEvent(eventName) {
        var eventObj = document.createEvent('Event');
        eventObj.initEvent(eventName, true, true);
        return eventObj;
      }
      arguments[0].dispatchEvent(generateEvent('change'));
      arguments[0].dispatchEvent(generateEvent('input'));
      arguments[0].dispatchEvent(generateEvent('keydown'));
      arguments[0].dispatchEvent(generateEvent('keyup'));
      arguments[0].click();`;
};

const getJsSetValueOptions = (sessionKeyObj, requestStateObj, options, urlParts, sendData, elementId, useDefaultOptions = false) => {
  if (useDefaultOptions) return options;
  const jsSetValueUrlParts = urlParts.slice(0, urlParts.length - 3);
  if (sessionKeyObj.dialect === 'W3C') {
    jsSetValueUrlParts.push('execute/sync');
  } else {
    jsSetValueUrlParts.push('execute');
  }
  const jsSetValueOptions = {
    path: Buffer.from(jsSetValueUrlParts.join('/'), 'utf8'),
    method: 'POST',
    hostname: requestStateObj.rproxyHost,
    port: options.port,
    headers: {
      'accept-encoding': 'gzip',
      connection: 'Keep-Alive',
      host: options.headers.host,
    },
    body: `${JSON.stringify({
      script: getScriptForSetValue(sendData, sessionKeyObj),
      args: [{
        ELEMENT: `${elementId}`,
        'element-6066-11e4-a52e-4f735466cecf': `${elementId}`, // not some random shit: https://www.w3.org/TR/2015/WD-webdriver-20150827/
      }],
    })}`,
  };
  jsSetValueOptions.headers['content-length'] = Buffer.from(jsSetValueOptions.body).length;
  requestlib.appendBStackHostHeader(options.headers[constants.BSTACK_HOST_HEADER], jsSetValueOptions.headers);
  return jsSetValueOptions;
};

const sendKeysToElement = async (sessionKeyObj, requestStateObj, options, urlParts, sendData, elementId, requestHook, useDefaultOptions = false) => {
  const optionsToSend = getJsSetValueOptions(sessionKeyObj, requestStateObj, options, urlParts, sendData, elementId, useDefaultOptions);
  return requestlib.callWithHook(optionsToSend, 0, () => requestHook(requestStateObj));
};

module.exports = { sendKeysToElement, getAttributeType };
