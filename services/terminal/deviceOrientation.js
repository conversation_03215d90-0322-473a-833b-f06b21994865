'use strict';

const requestlib = require('../../lib/request');
const util = require('util');
const isAppleOs = require('../../helper').isAppleOs;

const setEmulatorOrientation = async (sessionKeyObj, logData) => {
  let targetOrientation = 'LANDSCAPE';
  if (logData && logData.length > 1) {
    targetOrientation = JSON.parse(logData).orientation.match(/landscape/i) ? 'LANDSCAPE' : 'PORTRAIT';
  }

  const serverURL = util.format('/switch_orientation?session_id=%s&orientation=%s&device=%s&version=5.0', sessionKeyObj.rails_session_id, targetOrientation.toLowerCase(), encodeURIComponent(sessionKeyObj.device));
  await requestlib.call({
    method: 'GET',
    hostname: sessionKeyObj.rproxyHost,
    port: 45671,
    path: serverURL,
    headers: requestlib.appendBStackHostHeader(sessionKeyObj.name),
  });
  return targetOrientation;
};

const getEmulatorOrientation = (sessionKeyObj) => {
  const orientation = sessionKeyObj.deviceOrientation || 'PORTRAIT';
  const outputHash = {
    state: 'success', value: orientation, class: 'org.openqa.selenium.remote.Response', sessionId: sessionKeyObj.rails_session_id, status: 0, hCode: 1319101000
  };
  return JSON.stringify(outputHash);
};

const getAndroidOrientation = async (sessionKeyObj, method) => {
  const params = {
    method,
    hostname: sessionKeyObj.rproxyHost,
    port: 45671,
    path: `/orientation?device=${sessionKeyObj.device}`,
    headers: requestlib.appendBStackHostHeader(sessionKeyObj.name),
  };
  const res = await requestlib.call(params);
  return res.data || 'portrait';
};

const setAndroidOrientation = async (sessionKeyObj, method, switchOrientation) => {
  let postData = `device=${sessionKeyObj.device}&orientation=${switchOrientation}`;
  if (isAppleOs(sessionKeyObj.os)) {
    postData += `&currentOrientation=${sessionKeyObj.deviceOrientation.toUpperCase()}`;
  }
  const headers = {
    'Content-Type': 'application/x-www-form-urlencoded',
    'Content-Length': Buffer.byteLength(postData)
  };
  const params = {
    method,
    hostname: sessionKeyObj.rproxyHost,
    port: 45671,
    path: '/orientation',
    body: postData,
    headers: requestlib.appendBStackHostHeader(sessionKeyObj.name, headers),
  };
  const res = await requestlib.call(params);
  return res.data || 'portrait';
};


module.exports = {
  setEmulatorOrientation, getEmulatorOrientation, setAndroidOrientation, getAndroidOrientation
};
