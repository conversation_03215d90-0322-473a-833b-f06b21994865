'use strict';

const requestlib = require('../../lib/request');
const constants = require('../../constants');

const acceptFirefoxSsl = async (sessionKeyObj, params) => {
  const reqOptions = {
    method: 'GET',
    hostname: sessionKeyObj.rproxyHost,
    port: (sessionKeyObj.os && sessionKeyObj.os.indexOf('mac') > -1) ? 45671 : 4567,
    timeout: constants.acceptSslffRequestTimeout,
    path: `/acceptssl_ff${((params !== '') ? `?${params}` : '')}`,
    headers: requestlib.appendBStackHostHeader(sessionKeyObj.name),
    recordJarTime: true
  };
  return requestlib.call(reqOptions);
};

module.exports = { acceptFirefoxSsl };
