'use strict';

const requestlib = require('../../lib/request');
const util = require('util');

const realIOSScreenshot = (sessionKeyObj) => {
  const screenshotURL = util.format('/custom_screenshot?sessionId=%s&orientation=%s&device=%s', sessionKeyObj.rails_session_id, sessionKeyObj.deviceOrientation, encodeURIComponent(sessionKeyObj.device));

  return requestlib.call({
    method: 'GET',
    hostname: sessionKeyObj.rproxyHost,
    port: 45671,
    path: screenshotURL,
    headers: requestlib.appendBStackHostHeader(sessionKeyObj.name),
  });
};

module.exports = { realIOSScreenshot };
