'use strict';

const requestlib = require('../../lib/request');
// const util = require('util');
const SeleniumClient = require('../../seleniumClient');

// This method calls reset command on platform which reinstalls app, and then invoke appium's default launch.
const launchApp = async (sessionKeyObj) => {
  const sel = new SeleniumClient(sessionKeyObj);
  const customResetAppTimeout = (sessionKeyObj.idle_timeout - (sessionKeyObj.idle_timeout > 20 ? 20 : 0)) * 1000; // newcommand timeout is mapped to idle_timeout
  const serverURL = `/reset_app?device=${encodeURIComponent(sessionKeyObj.device)}&automate_session_id=${encodeURIComponent(sessionKeyObj.rails_session_id)}`;
  const termOptions = {
    hostname: sessionKeyObj.rproxyHost,
    port: 45671,
    path: serverURL,
    timeout: customResetAppTimeout,
    headers: requestlib.appendBStackHostHeader(sessionKeyObj.name),
  };
  await requestlib.call(termOptions);
  await sel.launchApp();
};

const resetApp = async (sessionKeyObj) => {
  const sel = new SeleniumClient(sessionKeyObj);
  const customResetAppTimeout = (sessionKeyObj.idle_timeout - (sessionKeyObj.idle_timeout > 20 ? 20 : 0)) * 1000; // newcommand timeout is mapped to idle_timeout
  const serverURL = `/reset_app?device=${encodeURIComponent(sessionKeyObj.device)}&automate_session_id=${encodeURIComponent(sessionKeyObj.rails_session_id)}`;
  const termOptions = {
    hostname: sessionKeyObj.rproxyHost,
    port: 45671,
    path: serverURL,
    timeout: customResetAppTimeout,
    headers: requestlib.appendBStackHostHeader(sessionKeyObj.name),
  };
  await requestlib.call(termOptions);
  await sel.resetApp();
};

const installApp = async (sessionKeyObj, reqDataObj) => {
  const customTimeout = (sessionKeyObj.idle_timeout - (sessionKeyObj.idle_timeout > 20 ? 20 : 0)) * 1000;
  const bsAppHashedId = reqDataObj.appPath;
  const appHashedId = bsAppHashedId.split('://')[1];

  const serverURL = `/install_app?device=${encodeURIComponent(sessionKeyObj.device)}&automate_session_id=${encodeURIComponent(sessionKeyObj.rails_session_id)}&app_hashed_id=${appHashedId}`;

  const termOptions = {
    hostname: sessionKeyObj.rproxyHost,
    port: 45671,
    path: serverURL,
    timeout: customTimeout,
    headers: requestlib.appendBStackHostHeader(sessionKeyObj.name),
  };
  const res = await requestlib.call(termOptions);
  return res;
};

module.exports = {
  launchApp, resetApp, installApp
};
