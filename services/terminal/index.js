'use strict';

const HubLogger = require('../../log');
const requestlib = require('../../lib/request');
const { getAttributeType, sendKeysToElement } = require('./sendKeys');
const { realIOSScreenshot } = require('./screenshot');
const constants = require('../../constants');
const { acceptFirefoxSsl } = require('./browserSSL');
const helper = require('../../helper');

const LL = constants.LOG_LEVEL;

const getAutoItSendKeysParams = (sessionKeyObject, sendKeysWaitInterval, options, text) => ({
  method: 'GET',
  timeout: options.timeout,
  hostname: sessionKeyObject.rproxyHost,
  port: 4567,
  path: `/sendkeys?sleep=${encodeURIComponent(sendKeysWaitInterval.toString())}&text=${encodeURIComponent(text)}`,
  headers: requestlib.appendBStackHostHeader(sessionKeyObject.name),
  recordJarTime: true,
});

const sendKeysWaitIntervalNeedsUpdation = sendKeysWaitInterval => (
  // eslint-disable-next-line no-restricted-globals
  isNaN(sendKeysWaitInterval) ||
  sendKeysWaitInterval < constants.autoitSendKeysMinSleep ||
  sendKeysWaitInterval > constants.autoitSendKeysMaxSleep);

const autoItSendKeys = async (sessionKeyObject, requestStateObj, options, text) => {
  const sessionId = requestStateObj ? (requestStateObj.clientSessionID || 'not found') : 'not found';
  // eslint-disable-next-line radix
  let sendKeysWaitInterval = parseInt(sessionKeyObject.autoitSendKeys);

  // eslint-disable-next-line max-len
  if (sendKeysWaitIntervalNeedsUpdation(sendKeysWaitInterval)) sendKeysWaitInterval = constants.autoitSendKeysSleep;

  HubLogger.miscLogger('autoItSendKeys', `AutoIt SendKeys sessionId: ${sessionId} Wait between Characters: ${sendKeysWaitInterval.toString()}`, LL.DEBUG, sessionKeyObject.debugSession);
  if (text !== '') {
    const { redactedText = text } = options;
    HubLogger.miscLogger('Redact Text', `Redact text for session - ${sessionId} -- ${redactedText}`, LL.DEBUG, sessionKeyObject.debugSession);
    // eslint-disable-next-line max-len
    const keysOptions = getAutoItSendKeysParams(sessionKeyObject, sendKeysWaitInterval, options, text);
    try {
      const res = await requestlib.call(keysOptions);
      helper.addToJarTime(sessionKeyObject.rails_session_id, res);
      HubLogger.miscLogger('autoItSendKeys', `sessionId: ${sessionId} AutoIt SendKeys response ${res.data}`, LL.DEBUG, sessionKeyObject.debugSession);
      return true;
    } catch (err) {
      HubLogger.tempExceptionLogger(`Error during autoit sendkeys sessionId: ${sessionId}`, err, err.stack);
      throw err;
    }
  } else {
    HubLogger.miscLogger('autoItSendKeys', `sessionId: ${sessionId} User Sent Empty Text ${text}`, LL.DEBUG, sessionKeyObject.debugSession);
  }
  return true;
};

async function cancelSafariPopup(sessionKeyObject) {
  let res;
  try {
    res = await requestlib.call({
      method: 'GET',
      hostname: sessionKeyObject.rproxyHost,
      port: 45671,
      path: '/safari_popup_cancel',
      headers: requestlib.appendBStackHostHeader(sessionKeyObject.name),
      recordJarTime: true,
    });
  } catch (e) {
    throw (e);
  }
  return res;
}

const basicAuthEdge = async (sessionKeyObject, edgeUsername, edgePassword) => {
  const res = await requestlib.call({
    method: 'GET',
    hostname: sessionKeyObject.rproxyHost,
    port: 4567,
    path: `/basic_auth_edge?username=${encodeURIComponent(edgeUsername)}&password=${encodeURIComponent(edgePassword)}&browser_version=${encodeURIComponent(sessionKeyObject.browser_version)}`,
    headers: requestlib.appendBStackHostHeader(sessionKeyObject.name),
    recordJarTime: true,
  });
  return res;
};

const getIOSAppString = async (url, sessionKeyObj, requestStateObj) => {
  const customTimeout = (sessionKeyObj.idle_timeout - (sessionKeyObj.idle_timeout > 20 ? 20 : 0)) * 1000;
  const reqDataObj = JSON.parse(requestStateObj.req_data);

  if (helper.isMobileCommand(url, requestStateObj.req_data, 'mobile: getAppStrings')) {
    if (Array.isArray(reqDataObj.args) && reqDataObj.args[0] && Object.prototype.hasOwnProperty.call(reqDataObj.args[0], 'language')) {
      reqDataObj.language = reqDataObj.args[0].language;
      reqDataObj.stringFile = reqDataObj.args[0].stringFile || '';
    }
  }
  const language = reqDataObj.language || 'en';
  const stringFile = reqDataObj.stringFile || '';

  const serverURL = `/app_strings?device=${encodeURIComponent(sessionKeyObj.device)}&automate_session_id=${encodeURIComponent(sessionKeyObj.rails_session_id)}&language=${language}&string_file=${stringFile}`;

  const termOptions = {
    hostname: sessionKeyObj.rproxyHost,
    port: 45671,
    path: serverURL,
    timeout: customTimeout,
    headers: requestlib.appendBStackHostHeader(sessionKeyObj.name),
  };

  return requestlib.call(termOptions);
};

const edgeProxyRegCheck = async (sessionKeyObj) => {
  const res = await requestlib.call({
    hostname: sessionKeyObj.rproxyHost,
    method: 'GET',
    port: 4567,
    path: `/edge_proxy_reg_check?edgeProxyRegCheck=true&proxyPort=${sessionKeyObj.networkLogs ? 8969 : 45696}`,
    headers: requestlib.appendBStackHostHeader(sessionKeyObj.name),
    recordJarTime: true,
  });
  return res;
};

module.exports = {
  basicAuthEdge,
  cancelSafariPopup,
  autoItSendKeys,
  getAutoItSendKeysParams,
  edgeProxyRegCheck,
  acceptFirefoxSsl,
  getAttributeType,
  sendKeysToElement,
  getIOSAppString,
  realIOSScreenshot
};
