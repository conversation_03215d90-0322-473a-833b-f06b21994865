/* eslint-disable no-restricted-globals */
/* eslint-disable radix */

'use strict';

const helper = require('../../helper');
const HubLogger = require('../../log');
const constants = require('../../constants');
const requestlib = require('../../lib/request');
const pubSub = require('../../pubSub');
const { sleep } = require('../../helper');

const LL = constants.LOG_LEVEL;

const isTitleAmongIssue = (title) => {
  if (title && typeof (title) === 'string') {
    if (title.match(/Internal Server Error|502 Proxy Error|Problem loading page/i)) {
      return true;
    }
  }
  return false;
};

const generateCheckPageLoadOptions = (sessionKeyObj, scriptToExecute) => {
  const requestJson = {
    script: scriptToExecute,
    args: []
  };
  const body = JSON.stringify(requestJson);
  let headers = {
    accept: 'application/json',
    'content-type': 'application/json; charset=utf-8',
    'content-length': body.length,
    'accept-encoding': 'gzip;q=1.0,deflate;q=0.6,identity;q=0.3',
  };

  headers = helper.updateHeadersForSelenium4Jars(sessionKeyObj, headers);
  requestlib.appendBStackHostHeader(sessionKeyObj.name, headers);

  const endpoint = sessionKeyObj.dialect === 'W3C' ? '/execute/sync' : '/execute';

  return {
    hostname: sessionKeyObj.rproxyHost,
    port: sessionKeyObj.port,
    path: `/wd/hub/session/${sessionKeyObj.key}${endpoint}`,
    method: 'POST',
    body,
    headers,
    agent: helper.agent,
    timeout: isNaN(parseInt(sessionKeyObj.autoWait)) ? 10000 : sessionKeyObj.autoWait,
    recordJarTime: true
  };
};

const checkIssuesWithTitle = (data, checkTitleAndReadyState, sessionKeyObj) => {
  if (data && data.value && typeof (data.value) !== 'string' && Array.isArray(data.value)) {
    const titleData = data.value.pop(1);
    data.value = data.value.join();
    if (checkTitleAndReadyState && isTitleAmongIssue(titleData)) {
      HubLogger.miscLogger('checkPageLoadOnTerminal', `SessionId: ${sessionKeyObj.rails_session_id} Got Title ${titleData}`, LL.INFO);
      sessionKeyObj.udpKeys.local_5xx = 1;
      pubSub.publish(constants.updateKeyObject, {
        session: sessionKeyObj.rails_session_id,
        changed: {
          udpKeys: sessionKeyObj.udpKeys
        }
      });
    }
  }
};

const checkPageLoad = async (sessionKeyObj, checkLoad, checkTitleAndReadyState) => {
  HubLogger.miscLogger('checkPageLoadOnTerminal', `sessionId: ${sessionKeyObj.rails_session_id} status: in function ${sessionKeyObj.key}`, LL.DEBUG, sessionKeyObj.debugSession);
  const scriptToExecute = checkTitleAndReadyState ? 'return [document.readyState, document.title]' : 'return document.readyState';
  HubLogger.miscLogger('checkPageLoadOnTerminal', `sessionId: ${sessionKeyObj.rails_session_id} scriptToExecute ${scriptToExecute}`, LL.DEBUG, sessionKeyObj.debugSession);

  const options = generateCheckPageLoadOptions(sessionKeyObj, scriptToExecute);

  try {
    const res = await requestlib.call(options);
    helper.addToJarTime(sessionKeyObj.rails_session_id, res);

    if (constants.global_registry[sessionKeyObj.rails_session_id]) {
      constants.global_registry[sessionKeyObj.rails_session_id].isMetaPageLoadTimeout = true;
    }
    const data = JSON.parse(res.data);
    const responseObject = { data, res };
    checkIssuesWithTitle(data, checkTitleAndReadyState, sessionKeyObj);
    HubLogger.miscLogger('checkPageLoadOnTerminal', `sessionId: ${sessionKeyObj.rails_session_id} status: success ${JSON.stringify(data.value || '').substring(0, 20)}`, LL.DEBUG);

    if (data.value === 'complete' || data.value === 'interactive') return { ...responseObject, success: true };

    HubLogger.miscLogger('checkPageLoadOnTerminal', `sessionId: ${sessionKeyObj.rails_session_id} status: error ${JSON.stringify((data.value && data.value.message) || '')}`, LL.INFO);
    if (typeof (checkLoad) !== 'undefined' && data.value !== 'loading') return { ...responseObject, success: true };

    return { ...responseObject, success: false };
  } catch (err) {
    HubLogger.exceptionLogger('checkPageLoadOnTerminal', `sessionId: ${sessionKeyObj.rails_session_id} status: request error ${err}`);
    throw err;
  }
};

const waitForPageLoad = async (sessionKeyObj, maxWait) => {
  maxWait = maxWait || 10000;
  const startTime = (new Date()).getTime();
  HubLogger.miscLogger('waitForPageLoad', `sessionId: ${sessionKeyObj.rails_session_id} waiting for pageLoad to complete on terminal`, LL.INFO);

  if (maxWait < 0) {
    HubLogger.miscLogger('waitForPageLoad', `sessionId: ${sessionKeyObj.rails_session_id} timedout`, LL.INFO);
    return { success: false };
  }
  HubLogger.miscLogger('waitForPageLoad', `sessionId: ${sessionKeyObj.rails_session_id} checking page load state`, LL.INFO);
  try {
    const res = await checkPageLoad(sessionKeyObj);
    return res;
  } catch (err) {
    HubLogger.miscLogger('waitForPageLoad', `sessionId: ${sessionKeyObj.rails_session_id} Page load is not yet completed. Retrying after ${constants.checkPageLoadWait} ms`, LL.INFO);
    await sleep(constants.checkPageLoadWait);
    const res = await waitForPageLoad(sessionKeyObj, (maxWait - ((new Date()).getTime() - startTime)));
    return res;
  }
};

module.exports = { checkPageLoad, waitForPageLoad };
