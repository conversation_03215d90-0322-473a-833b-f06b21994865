'use strict';

const HubLogger = require('../../log');
const requestlib = require('../../lib/request');
const { waitForPageLoad } = require('../selenium/browserActions');

const getOpenUrl = async (keyObject) => {
  // TODO: Use selenium client
  let res;
  try {
    res = await requestlib.call({
      hostname: keyObject.rproxyHost,
      port: keyObject.port,
      path: `/wd/hub/session/${keyObject.key}/url`,
      headers: requestlib.appendBStackHostHeader(keyObject.name),
    });
  } catch (e) {
    HubLogger.exceptionLogger('GetURL Error', `${e.type} while getURL ${e.error}`);
  }
  return res;
};

const getIOSLocation = sessionKeyObj => ({
  sessionId: sessionKeyObj.rails_session_id,
  status: 13,
  value: {
    message:
      'Get geolocation command (https://appium.io/docs/en/commands/session/geolocation/get-geolocation/) is not currently supported on BrowserStack. Please reach out to the support team for any queries',
  },
});

const realIOSFileUpload = (sessionKeyObj) => {
  const outputData = JSON.stringify({
    state: 'real iOS File upload',
    sessionId: sessionKeyObj.key,
    value: { message: 'File upload is not supported on real iOS devices' },
    class: 'org.openqa.selenium.remote.Response',
    status: 13,
  });
  return {
    statusCode: 422,
    headers: {},
    data: outputData,
  };
};

const chromeMacMaximize = () => ({
  state: 'success',
  hCode: 1528908777,
  value: null,
  class: 'org.openqa.selenium.remote.Response',
  status: 0,
});

const iOSMaximize = () => ({
  state: 'success',
  hCode: 1528908777,
  value: {
    height: 664, width: 390, x: 0, y: 0
  },
  class: 'org.openqa.selenium.remote.Response',
  status: 0,
});

module.exports = {
  getOpenUrl,
  waitForPageLoad,
  getIOSLocation,
  realIOSFileUpload,
  chromeMacMaximize,
  iOSMaximize,
};
