// TODO: Need to move this module to some other directory post re-arch.

'use strict';

/* eslint no-underscore-dangle: 0 */

const { updateKeyObject, LOG_LEVEL } = require('../../constants');
const HubLogger = require('../../log');
const pubSub = require('../../pubSub');

// NOTE: Please update the following in case new checks are added
/**
 * Quick Integration Guide for Automate Dashboard
 *
 * Helps in identification of the following:
 * 1. Custom script (i.e. not our sample script): By checking the URLs used in the session
 * 2. REST API used using JS Executor
 *
 * Motive: To identify the progress where users run their custom scripts in
 * BrowserStack build itself.
 * But can be extended for futher custom checks (if reqd).
 */
class Qig {
  static _isSampleScriptUrl(visitedUrl) {
    return [/google/i, /bstackdemo/i, /duckduckgo/i].some(rgx => rgx.test(visitedUrl));
  }

  // Minimum conditions need to be statisfied for any operation
  static _minimalChecks(keyObject) {
    if (keyObject.appTesting) {
      return false;
    }

    return true;
  }

  static _publishQigData(keyObject) {
    pubSub.publish(updateKeyObject, {
      session: keyObject.rails_session_id,
      changed: {
        qig: keyObject.qig,
      },
    });
  }

  // Marks the usage of JS Executor to update session status
  static markSessionStatusExecutor(keyObject) {
    if (!Qig._minimalChecks(keyObject)) {
      return;
    }

    if (keyObject.qig && keyObject.qig.sessionStatusExecUsed === false) {
      keyObject.qig.sessionStatusExecUsed = true;
      Qig._publishQigData(keyObject);
      HubLogger.miscLogger('QIG', `Session status JS executor used for ${keyObject.rails_session_id}`, LOG_LEVEL.INFO);
    }
  }

  // Marks the usage of custom URL in the script (i.e. user is running a non sample-script)
  static checkAndMarkCustomUrlUsed(keyObject, url) {
    if (!Qig._minimalChecks(keyObject)) {
      return;
    }

    if (
      keyObject.qig &&
      keyObject.qig.sampleScriptUrl === true &&
      !Qig._isSampleScriptUrl(url)
    ) {
      keyObject.qig.sampleScriptUrl = false;
      Qig._publishQigData(keyObject);
      HubLogger.miscLogger('QIG', `Custom URL used for session ${keyObject.rails_session_id}`, LOG_LEVEL.INFO);
    }
  }

  // Decides to send the data to rails only if the user used a custom URL in the script,
  // i.e. user ran a non sample-script
  static shouldSendDataToRails(keyObject) {
    if (
      Qig._minimalChecks(keyObject) &&
      keyObject.qig &&
      keyObject.qig.sampleScriptUrl === false
    ) {
      HubLogger.miscLogger('QIG', `Sending QIG data to rails for session ${keyObject.rails_session_id}`, LOG_LEVEL.INFO);
      return true;
    }

    return false;
  }
}

module.exports = Qig;
