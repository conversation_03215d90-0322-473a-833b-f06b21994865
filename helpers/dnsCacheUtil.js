'use strict';

const deepClone = require('lodash.clone');

/*
 * Make a deep copy of the supplied object. This function reliably copies only
 * what is valid for a JSON object, array, or other element.
 */
const deepCopy = function fn(o) {
  let newArr;
  let ix;
  let newObj;
  let prop;
  let toReturn;

  if (!o || typeof o !== 'object') {
    return o;
  }

  if (Array.isArray(o)) {
    newArr = [];
    for (ix = 0; ix < o.length; ix += 1) {
      newArr.push(deepClone(o[ix]));
    }
    toReturn = newArr;
  } else {
    newObj = {};
    for (prop in o) { // eslint-disable-line no-restricted-syntax
      /* istanbul ignore else */
      if (o.hasOwnProperty(prop)) { // eslint-disable-line no-prototype-builtins
        newObj[prop] = deepClone(o[prop]);
      }
    }
    toReturn = newObj;
  }
  return toReturn;
};

module.exports = {
  deepCopy,
};
