'use strict';

const HubLogger = require('../log');
const urlModule = require('url');

const redactRequestEndpoints = [/keys$/, // keys - /keys
  /\/element\/[a-zA-Z0-9._-]+\/value$/, // sendKeys - /element/:id/value
  /\/element\/[a-zA-Z0-9._-]+\/attribute\/value$/, // attribute('value') - /element/:id/attribute/value
];

const redactResponseEndpoints = [/keys$/, // keys - /keys
  /\/element\/[a-zA-Z0-9._-]+\/value$/, // sendKeys - /element/:id/value
  /\/element\/[a-zA-Z0-9._-]+\/attribute\/value$/, // attribute('value') - /element/:id/attribute/value
  /\/element\/[a-zA-Z0-9._-]+\/text$/, // text - /element/:id/text
];

const checkRequestForRedaction = (request, redactEndpoints) =>
  redactEndpoints.reduce((result, endpoint) => result || endpoint.test(request.url), false);


const markAsSensitive = (request) => {
  if (!request || typeof request !== 'object') return;
  if (!request.headers || typeof request.headers !== 'object') {
    request.headers = {};
  }

  request.headers['X-Appium-Is-Sensitive'] = 'true';
};

/**
 * Remove sensitive information from raw logs for caps:
 * `keyObject.maskSendKeys`, `keyObject.maskBasicAuth` or `keyObject.maskResponse`
 */
module.exports = (request, keyObject, response) => {
  const redactRequest = keyObject.maskSendKeys && checkRequestForRedaction(request, redactRequestEndpoints);
  const redactResponse = keyObject.maskResponse && checkRequestForRedaction(request, redactResponseEndpoints);

  if (redactRequest) {
    request.log_data = '{"value":["[REDACTED]"]}';
  }

  if (redactResponse) {
    if (response && response.string && typeof (response.string) === 'string') {
      try {
        const parsedResponse = JSON.parse(response.string);
        if (parsedResponse.value) {
          parsedResponse.value = '[REDACTED]';
          response.string = JSON.stringify(parsedResponse);
        }
      } catch (err) {
        HubLogger.exceptionLogger(`Exception While masking response ${err.toString()}`);
      }
    }
  }

  // Masking BasicAuth
  if ((keyObject.maskBasicAuth && keyObject.maskBasicAuth.toString().toLowerCase() === 'true') && (request.url.match(/\/url$/))) {
    try {
      const url = JSON.parse(request.log_data).url;
      const parsedUrl = urlModule.parse(url);
      if (parsedUrl.auth) {
        const redactedEntry = `${parsedUrl.protocol}//[REDACTED]@${parsedUrl.host}${parsedUrl.path}`;
        request.log_data = `{"url":"${redactedEntry}"}`;
      }
    } catch (err) {
      HubLogger.exceptionLogger(`Exception While masking BasicAuth ${err.toString()}`);
    }
  }
};

module.exports.redactRequestEndpoints = redactRequestEndpoints;
module.exports.redactResponseEndpoints = redactResponseEndpoints;
module.exports.checkRequestForRedaction = checkRequestForRedaction;
module.exports.markAsSensitive = markAsSensitive;
