'use strict';

const helper = require('../helper');
const { Events } = require('browserstack-dwh');
const constants = require('../constants');
const { isNotUndefined } = require('../typeSanity');

/**
 * pushes feature usage (logs) to EDS
 * @param {string} sessionId session Id
 * @param {string} featureUsage a hash having representing feature usage to be pushed to eds
 * @param {function} callback
 */
const pushFeatureUsage = (sessionId, featureUsage, callback) => {
  helper.checkSessionAppAutomate(sessionId, (isAppAutomate) => {
    const kind = isAppAutomate ? Events.APP_AUTOMATE_TEST_SESSIONS : Events.AUTOMATE_TEST_SESSIONS;
    if (featureUsage && Object.keys(featureUsage).length > 0) {
      helper.sendToEDS({
        sessionid: sessionId,
        feature_usage: featureUsage,
        kind,
      });
    }
    callback();
  });
};

const pushProductStabilityReason = (sessionId, stabilityReason, isAppAutomate) => {
  if (isAppAutomate) {
    const product = {
      stability: {
        reason: stabilityReason,
      },
    };
    const kind = Events.APP_AUTOMATE_TEST_SESSIONS;
    helper.sendToEDS({
      sessionid: sessionId,
      product,
      kind,
    });
  }
};

const incrementHootHootStat = (kind, genre) => {
  if (isNotUndefined(kind) && isNotUndefined(genre)) {
    constants.pushToHootHootRegistry[kind] = constants.pushToHootHootRegistry[kind] ? constants.pushToHootHootRegistry[kind] : {};
    constants.pushToHootHootRegistry[kind][genre] = constants.pushToHootHootRegistry[kind][genre] ? constants.pushToHootHootRegistry[kind][genre] + 1 : 1;
  }
};

module.exports = {
  pushFeatureUsage, pushProductStabilityReason, incrementHootHootStat
};
