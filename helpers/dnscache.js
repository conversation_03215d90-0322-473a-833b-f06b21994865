'use strict';

const {
  CacheObject: cacheObject, postDNSmethodCall, setIsExpiredHitStatsForAMethod,
  setErrorStatsForAMethod, setIsDnsTimedoutForAMethod,
} = require('./dnsCacheObject');
const dns = require('dns');
const constants = require('../constants');
const { deepCopy } = require('./dnsCacheUtil');
const { isNotUndefined, isUndefined } = require('../typeSanity');
const HubLogger = require('./../log');

/*
 * Stores the list of callbacks waiting for a dns call.
 */
let callbackList = {};
// original function storage
const backupObject = {
  lookup: dns.lookup,
  resolve: dns.resolve,
  resolve4: dns.resolve4,
  resolve6: dns.resolve6,
  resolveMx: dns.resolveMx,
  resolveTxt: dns.resolveTxt,
  resolveSrv: dns.resolveSrv,
  resolveNs: dns.resolveNs,
  resolveCname: dns.resolveCname,
  reverse: dns.reverse,
};

const handleError = (err, list, key) => {
  if (isNotUndefined(callbackList[key])) {
    delete callbackList[key];
    try {
      while (list.length) {
        const cb = list.pop();
        cb(err);
      }
    } catch (concurrentError) {} // eslint-disable-line no-empty
  }
};

const sendRecord = (error, record, callback) => {
  if (Array.isArray(record)) {
    process.nextTick(callback, error, record);
    return;
  }
  process.nextTick(callback, error, record.address, record.family);
};

const exceptionLog = (identifier, message, error = '~') => {
  HubLogger.tempExceptionLogger(`${identifier}-${message}`, error);
};

// original function storage
const EnhanceDns = function fn(configuration) {
  const conf = configuration || {};
  // 0 is not allowed i.e. if ttl is set to 0, it will take the default
  conf.ttl = parseInt(conf.ttl, 10) || 300;
  conf.cachesize = parseInt(conf.cachesize, 10); // 0 is allowed but it will disable the caching
  conf.timeout = parseInt(conf.timeout, 10);
  if (isUndefined(conf.timeout) || Number.isNaN(conf.timeout)) {
    conf.timeout = constants.DNS_TIMEOUT || 5 * 1000;
  }
  if (Number.isNaN(conf.cachesize)) {
    conf.cachesize = 1000; // set default cache size to 1000 records max
  }

  if (!conf.enable || conf.cachesize <= 0) {
    delete dns.internalCache;
    for (const key in backupObject) { // eslint-disable-line no-restricted-syntax
      /* istanbul ignore else */
      if (backupObject.hasOwnProperty(key)) { // eslint-disable-line no-prototype-builtins
        dns[key] = backupObject[key];
      }
    }
    return dns;
  }

  // insert cache object to the instance
  if (!dns.internalCache) {
    /* eslint new-cap: ["error", { "newIsCap": false }] */
    dns.internalCache = conf.cache ? new conf.cache(conf) : new cacheObject(conf);
    dns.originalDNS = backupObject;
    callbackList = {};
  }
  const cache = dns.internalCache;
  // override dns.lookup method
  dns.lookup = function fnc(domain, opt, callb) {
    let family = 0;
    let hints = 0;
    let all = false;
    let callback = callb;
    let options = opt;
    if (arguments.length === 2) {
      callback = options;
      options = family;
    } else if (typeof options === 'object') {
      if (options.family) {
        family = +options.family;
        if (family !== 4 && family !== 6) {
          callback(new Error('invalid argument: `family` must be 4 or 6'));
          return;
        }
      }
      // istanbul ignore next - "hints" require node 0.12+
      if (options.hints) {
        hints = +options.hints;
      }
      all = (options.all === true);
    } else if (options) {
      family = +options;
      if (family !== 4 && family !== 6) {
        callback(new Error('invalid argument: `family` must be 4 or 6'));
        return;
      }
    }

    const key = `lookup_${domain}_${family}_${hints}_${all}`;
    cache.get(key, (error, result) => { // eslint-disable-line consistent-return
      let record;

      if (result) {
        record = result.value;
        if (record && !result.isExpired) {
          /* istanbul ignore next - "all" option require node 4+*/
          return sendRecord(error, record, callback);
        }
      }

      callbackList[key] = callbackList[key] || [];
      const list = callbackList[key];
      list.push(callback);
      if (list.length > 1) {
        return; // eslint-disable-line consistent-return
      }

      setTimeout(() => {
        if (isNotUndefined(callbackList[key])) {
          delete callbackList[key];
          try {
            while (list.length) {
              if (record) {
                sendRecord(undefined, record, list.pop());
                setIsExpiredHitStatsForAMethod('lookup');
              } else {
                const cb = list.pop();
                const timedoutError = new Error('DNS Timedout');
                exceptionLog(key, `${timedoutError.message}`);
                cb(timedoutError);
                setIsDnsTimedoutForAMethod('lookup', 1);
              }
            }
          } catch (concurrentError) { } // eslint-disable-line no-empty
        }
      }, conf.timeout);

      try {
        const start = (new Date()).getTime();
        // eslint-disable-next-line consistent-return
        backupObject.lookup(domain, options, (err, address, familyR) => {
          if (err) {
            setErrorStatsForAMethod('lookup', err);
            if (record) {
              setIsExpiredHitStatsForAMethod('lookup');
              if (isNotUndefined(callbackList[key])) {
                delete callbackList[key];
                while (list.length) {
                  sendRecord(undefined, record, list.pop());
                }
              }
              return;
            }
            handleError(err, list, key);
            // eslint-disable-line consistent-return
            return;
          }
          postDNSmethodCall(start, 'lookup');
          let value;
          /* istanbul ignore next - "all" option require node 4+ */
          if (Array.isArray(address)) {
            value = deepCopy(address);
          } else {
            value = {
              address,
              family: familyR,
            };
          }
          cache.set(key, value, () => {
            if (isNotUndefined(callbackList[key])) {
              delete callbackList[key];
              while (list.length) {
                const cb = list.pop();
                cb(err, address, familyR);
              }
            }
          });
        });
      } catch (err) {
        /* istanbul ignore next - doesn't throw in node 0.10 */
        return handleError(err, list, key);
      }
    });
  };


  const cacheWrapper = function helper(key, funcReference, funcArgs, funcName, callbackNew) {
    cache.get(key, (error, result) => { // eslint-disable-line consistent-return
      let record;

      if (result) {
        record = result.value;
        if (!result.isExpired) {
          return callbackNew(error, deepCopy(record), true);
        }
      }

      callbackList[key] = callbackList[key] || [];
      const list = callbackList[key];
      list.push(callbackNew);
      if (list.length > 1) { return; } // eslint-disable-line consistent-return

      try {
        const start = (new Date()).getTime();
        // eslint-disable-next-line consistent-return
        funcReference(...funcArgs, (err, response) => {
          if (err) {
            setErrorStatsForAMethod(funcName, err);
            if (record) {
              setIsExpiredHitStatsForAMethod(funcName);
              list.forEach((cb) => {
                cb(undefined, deepCopy(record), true);
              });
              delete callbackList[key];
              return;
            }
            handleError(err, list, key);
            return; // eslint-disable-line consistent-return
          }
          postDNSmethodCall(start, funcName);
          cache.set(key, response, () => {
            list.forEach((cb) => {
              cb(err, deepCopy(response), false);
            });
            delete callbackList[key];
          });
        });
      } catch (err) {
        /* istanbul ignore next - doesn't throw in node 0.10 */
        callbackNew(err);
      }
    });
  };

  // override dns.resolve method
  dns.resolve = function fnc(domain, type, callback) {
    let typeNew;
    let callbackNew;

    if (typeof type === 'string') {
      typeNew = type;
      callbackNew = callback;
    } else {
      typeNew = 'A';
      callbackNew = type;
    }

    const key = `resolve_${domain}_${typeNew}`;
    cacheWrapper(key, backupObject.resolve, [domain, typeNew], 'resolve', callbackNew);
  };

  // override dns.resolve4 method
  dns.resolve4 = function fnc(domain, callback) {
    const key = `resolve4_${domain}`;
    cacheWrapper(key, backupObject.resolve4, [domain], 'resolve4', callback);
  };

  // override dns.resolve6 method
  dns.resolve6 = function fnc(domain, callback) {
    const key = `resolve6_${domain}`;
    cacheWrapper(key, backupObject.resolve6, [domain], 'resolve6', callback);
  };

  // override dns.resolveMx method
  dns.resolveMx = function fnc(domain, callback) {
    const key = `resolveMx_${domain}`;
    cacheWrapper(key, backupObject.resolveMx, [domain], 'resolveMx', callback);
  };

  // override dns.resolveTxt method
  dns.resolveTxt = function fnc(domain, callback) {
    const key = `resolveTxt_${domain}`;
    cacheWrapper(key, backupObject.resolveTxt, [domain], 'resolveTxt', callback);
  };

  // override dns.resolveSrv method
  dns.resolveSrv = function fnc(domain, callback) {
    const key = `resolveSrv_${domain}`;
    cacheWrapper(key, backupObject.resolveSrv, [domain], 'resolveSrv', callback);
  };

  // override dns.resolveNs method
  dns.resolveNs = function fnc(domain, callback) {
    const key = `resolveNs_${domain}`;
    cacheWrapper(key, backupObject.resolveNs, [domain], 'resolveNs', callback);
  };

  // override dns.resolveCname method
  dns.resolveCname = function fnc(domain, callback) {
    const key = `resolveCname_${domain}`;
    cacheWrapper(key, backupObject.resolveCname, [domain], 'resolveCname', callback);
  };

  // override dns.reverse method
  dns.reverse = function fnc(ip, callback) {
    const key = `reverse_${ip}`;
    cacheWrapper(key, backupObject.reverse, [ip], 'reverse', callback);
  };
  return dns;
};

module.exports = function fnc(conf) {
  return new EnhanceDns(conf);
};
