"use strict";

const HubLogger = require("../log");
const constants = require("../constants");
const { timeout, endpoint, buffer_timeout } = constants.LIGHTHOUSE_AUTOMATE;
const { LH_ERROR_MESSAGES, LH_SUPPORTED_BROWSERS, browserVersionStrToFloat, LH_MIN_CHROME } = constants;
const customExecutorHelper = require("./customSeleniumHandling/customExecutorHelper");
const requestlib = require("../lib/request");
const { PLAYWRIGHT_LH_PORT, REMOTE_DEBUGGER_PORT } = require("../config/socketConstants");
const { isUndefined } = require("../typeSanity");

const LH_VALIDATED_MSG = "validated";

const lighthouseHandler = (keyObject, requestStateObj, parsedCommand) => {
  const validationMessage = lighthouseValidation(keyObject);
  if (validationMessage != LH_VALIDATED_MSG) {
    requestStateObj.hash = "GET:value";
    requestStateObj.data = JSON.stringify({
      sessionId: requestStateObj.clientSessionID,
      value: { lhSuccess: "false", data: validationMessage },
    });
    customExecutorHelper.instrumentAndSendExecutorResponse(
      parsedCommand.action,
      keyObject,
      requestStateObj,
      false
    );
    return;
  }
  lighthouseExecutor(keyObject, requestStateObj, parsedCommand);
};

async function lighthouseExecutor(keyObject, requestStateObj, parsedCommand) {
  const executorType = parsedCommand.action;
  const body = parsedCommand.arguments;
  try {
    const port = keyObject["os"].indexOf("win") > -1 ? "4567" : "45671";    
    const lhOptions = {
      url: body.url,
      timeout: body.timeout || timeout,
      lhFlags: JSON.stringify({
        port: keyObject.isLaunchPersistentContext ? REMOTE_DEBUGGER_PORT : PLAYWRIGHT_LH_PORT,
        logLevel: "info",
        output: "json",
        onlyCategories: body.onlyCategories,
      }),
      lhConfig: JSON.stringify({}),
      logHost: constants.zombie_server,
      clsLogHost: constants.cls_host,
      file: "sync"
    };

    const lhQueryParams = requestlib.getEncodedURLParams(lhOptions);
    const getOptions = {
      hostname: keyObject.rproxyHost,
      port: port,
      path: endpoint + "?" + lhQueryParams,
      agent: false,
      headers: {
        Connection: "keep-alive",
      },
      timeout: timeout + buffer_timeout,
    };
    requestlib.appendBStackHostHeader(keyObject.name, getOptions.headers);
    const result = await requestlib.call(getOptions);

    if (result.statusCode != 200 || isUndefined(result.data)) {
      throw new Error(
        `Exception raised in terminal: ${JSON.stringify(result.data)}`
      );
    }

    const dataJson = JSON.parse(result.data);
    const response = { lhSuccess: dataJson.lhSuccess, data: dataJson.data };

    // Making a request object since value is null in customExecutorHelper.sendExecutorResponse
    requestStateObj.hash = "GET:value";
    requestStateObj.data = JSON.stringify({
      sessionId: requestStateObj.clientSessionID,
      value: response,
    });
    customExecutorHelper.instrumentAndSendExecutorResponse(
      executorType,
      keyObject,
      requestStateObj,
      false
    );
  } catch (e) {
    console.log(e);
    HubLogger.exceptionLogger(
      `Exception in lighthouseExecutor for in session ${keyObject.rails_session_id}`,
      e.toString()
    );
    customExecutorHelper.instrumentAndSendError(
      executorType,
      keyObject,
      requestStateObj,
      constants.JSE_SESSION_STATUS.generic_error
    );
  }
}

const lighthouseValidation = (keyObject) => {
  // Add validation for only playwright, chrome, and when lighthouse flag is enabled
  if (!keyObject.isPlaywright) {
    return LH_ERROR_MESSAGES["framework_not_supported"];
  } else if (!LH_SUPPORTED_BROWSERS.includes(keyObject.browser)) {
    return LH_ERROR_MESSAGES["browser_not_supported"];
  } else if(browserVersionStrToFloat(keyObject.browser_version) < LH_MIN_CHROME){
    return LH_ERROR_MESSAGES["chrome_version_not_supported"];
  }
  return LH_VALIDATED_MSG;
};

module.exports = { lighthouseHandler };
