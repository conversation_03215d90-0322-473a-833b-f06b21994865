// This file has functions which is use to detect if Browserstack error has occured mid session
'use strict';

const helper = require('../helper');
const constants = require('../constants');

const detectEconnrefusedMidSession = (data) => {
    let econnrefusedRegex = /original error: error: connect econnrefused/;
      if (data.toString().toLowerCase().match(econnrefusedRegex)) {
        return true;
      }
    return false;
};

const detectMissingAppIdKey = (data) => {
  let appIdKeyMissingRegex = /missing parameter: appidkey/;
    if (data && data.toString().toLowerCase().match(appIdKeyMissingRegex)) {
      return true;
    }
  return false;
};

module.exports = {
    detectEconnrefusedMidSession,
    detectMissingAppIdKey
}
