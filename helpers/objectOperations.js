'use strict';

/**
 * Validates an object for undefined values and replaces it with the given string to replace
 * @param {object} obj The object to validate the values for
 * @param {string} strToReplace String value to replace with if the specific key is undefined.
 * Default = "NOT AVAILABLE"
 * @returns {object} The validated object
 */
const validateValuesOfObject = (obj, strToReplace = 'NOT AVAILABLE') => {
  Object.keys(obj).forEach((key) => {
    obj[key] = obj[key] || strToReplace;
  });
  return obj;
};

module.exports = {
  validateValuesOfObject,
};
