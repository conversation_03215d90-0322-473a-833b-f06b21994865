// This file has helper functions related to json

'use strict';

const constants = require('../constants');
const HubLogger = require('../log');

const LL = constants.LOG_LEVEL;

const splitSingleElementJSON = (json, results, limit, isAppAutomate) => {
  const parts = Math.ceil(JSON.stringify(json).length / limit);
  const keys = Object.keys(json);
  const arrLength = json[keys[0]].d.length;
  let dtempArray;
  let stempArray;
  let mtempArray;
  let chunks = Math.ceil(arrLength / parts);
  if (arrLength <= parts) {
    chunks = 1;
  }

  for (let x = 0; x < arrLength; x += chunks) {
    dtempArray = json[keys[0]].d.slice(x, x + chunks);
    stempArray = json[keys[0]].s.slice(x, x + chunks);
    const tempJSON = {};
    if (isAppAutomate) {
      mtempArray = json[keys[0]].m.slice(x, x + chunks);
      tempJSON[keys[0]] = { d: dtempArray, s: stempArray, m: mtempArray };
    } else { tempJSON[keys[0]] = { d: dtempArray, s: stempArray }; }
    results.push(tempJSON);
  }
};

const splitJSON = (json, limit, isAppAutomate = false) => {
  const results = [];
  try {
    const strJson = JSON.stringify(json);
    if (strJson.length > limit) {
      const keys = Object.keys(json);
      if (keys.length > 1) {
        let tempJSON = {};
        // eslint-disable-next-line
        for (const [key, value] of Object.entries(json)) {
          tempJSON[key] = value;
          if (JSON.stringify(tempJSON).length > limit) {
            delete tempJSON[key];
            if (Object.keys(tempJSON).length !== 0) {
              results.push(tempJSON);
            }
            tempJSON = {};
            tempJSON[key] = value;
            // single element having more than threshold characters
            if (JSON.stringify(tempJSON).length > limit) {
              splitSingleElementJSON(tempJSON, results, limit, isAppAutomate);
              tempJSON = {};
            }
          }
        }
        if (Object.keys(tempJSON).length !== 0) {
          results.push(tempJSON);
        }
      } else { // Incase if we want to split a json having arrays in their attributes
        splitSingleElementJSON(json, results, limit);
      }
    } else {
      results.push(json);
    }
  } catch (e) {
    HubLogger.miscLogger('splitJSON', `Failed to split json ${json}`, LL.ERROR);
  }
  return results;
};

module.exports = {
  splitSingleElementJSON,
  splitJSON
};
