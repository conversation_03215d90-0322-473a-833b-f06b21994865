'use strict';

/* eslint-disable max-len */

const COMMANDS_DEPENDENT_ON_LAST_LOADING = {
  GET: /^cookie|name|source|types|window_handle(s)?$/,
  POST: /^frame|implicit_wait|maximize|size|timeouts$/,
  DELETE: /^cookie|window$/,
};

const RESPONSE_CLASS = 'org.openqa.selenium.remote.Response';
const SERVER_SIDE_ERROR_MESSAGE = 'An unknown server-side error occurred while processing the command.';
const SCRIPT_TIMEOUT_ERROR_MESSAGE = 'A script did not complete before its timeout expired.';
const ELEMENT_NOT_FOUND_ERROR_MESSAGE = 'An element could not be located on the page using the given search parameters.';

const matchBrowserVersionIfRequired = (expected, actual) => {
  const versionExists = typeof (expected) !== 'undefined';
  return !versionExists || (parseInt(actual, 10) === parseInt(expected, 10));
};

class TransitionChecker {
  constructor(keyObject, params) {
    this.keyObject = keyObject;
    this.params = params;
  }

  generateErrorResponse() {
    const { rails_session_id: sessionId } = this.keyObject;
    let data = {
      state: 'async script timeout', sessionId, value: { message: SCRIPT_TIMEOUT_ERROR_MESSAGE, error: 'script timeout' }, class: RESPONSE_CLASS, status: 28,
    };
    if (this.isCaptureScreenshotCheck || this.otherAffectedCommands) {
      data = {
        ...data, state: 'error', value: { message: SERVER_SIDE_ERROR_MESSAGE, error: 'unknown error' }, status: 13,
      };
    } else if (this.isFindingElementCheck) {
      const [state, value, status] = this.params.hash === 'POST:elements' ? ['success', [], 0] : ['error', { message: ELEMENT_NOT_FOUND_ERROR_MESSAGE, error: 'no such element' }, 7];
      data = {
        ...data, state, value, status,
      };
    } else if (this.isExecutingScriptCheck || this.isGetUrlCheck || this.isFetchingTitleCheck) {
      data = {
        ...data, state: 'success', value: null, status: 0,
      };
    }
    return JSON.stringify(data);
  }

  isEdge(browserVersion) {
    return this.keyObject.browser === 'MicrosoftEdge' && matchBrowserVersionIfRequired(browserVersion, this.keyObject.browser_version);
  }

  isInternetExplorer(browserVersion) {
    return this.keyObject.browser === 'internet explorer' && matchBrowserVersionIfRequired(browserVersion, this.keyObject.browser_version);
  }

  isFirefox(browserVersion) {
    return this.keyObject.browser === 'firefox' && matchBrowserVersionIfRequired(browserVersion, this.keyObject.browser_version);
  }

  isChrome(browserVersion) {
    return this.keyObject.browser === 'chrome' && matchBrowserVersionIfRequired(browserVersion, this.keyObject.browser_version);
  }

  isSafari(browserVersion) {
    return this.keyObject.browser === 'safari' && matchBrowserVersionIfRequired(browserVersion, this.keyObject.browser_version);
  }

  wasExecutingSyncScriptOnIE(browserVersion) {
    return this.isInternetExplorer(browserVersion) && this.wasExecuteSyncScriptCmd;
  }

  get wasExecuteSyncScriptCmd() {
    return this.keyObject.lastRequest === 'POST:execute';
  }

  get wasOpenBlankUrlCmd() {
    return this.keyObject.lastRequest === 'POST:url-about-blank';
  }

  get wasPageLoadingCommand() {
    return /^POST:(click|refresh|submit|url)$/.test(this.keyObject.lastRequest);
  }

  get wasAcceptAlertCmd() {
    return this.keyObject.lastRequest === 'POST:accept_alert';
  }

  get isGetUrlCmd() {
    return this.params.hash === 'GET:url';
  }

  get isOpenUrlCmd() {
    return this.params.hash === 'POST:url';
  }

  get isGetTitleCmd() {
    return this.params.hash === 'GET:title';
  }

  get isFindElementCmd() {
    return /^POST:element(s)?$/.test(this.params.hash);
  }

  get isExecuteScriptCmd() {
    return /^POST:execute(_async)?$/.test(this.params.hash);
  }

  get isCaptureScreenshotCmd() {
    return this.params.hash === 'GET:screenshot';
  }

  get isClickCmd() {
    return this.params.hash === 'POST:click';
  }

  get isIOSBrowser() {
    return (/iphone|ipad/i).test(this.keyObject.browser);
  }

  get isAndroidBrowser() {
    return (/android/i).test(this.keyObject.browser) || ((/android/i).test(this.keyObject.os) && (/chrome_android|samsung/i).test(this.keyObject.browser));
  }

  get ignorePreviousPageLoading() {
    const isNeitherEdgeNorBlankUrlWasOpened = (!this.isEdge() && !this.wasOpenBlankUrlCmd);
    const isNeitherDataNorXMLEndpoint = !(/^POST:url-(data|xml)$/.test(this.keyObject.lastRequest));
    return isNeitherEdgeNorBlankUrlWasOpened && isNeitherDataNorXMLEndpoint;
  }

  get previousPageIsLoading() {
    const lastRequestDeletedWindowInIOS = this.keyObject.realMobile && this.isIOSBrowser && this.keyObject.lastRequest === 'DELETE:window';
    const blankUrlWasOpenedInEdge = this.isEdge && this.wasOpenBlankUrlCmd;
    const loadingWasInitiated = this.wasPageLoadingCommand || blankUrlWasOpenedInEdge || lastRequestDeletedWindowInIOS;
    return this.keyObject.instable || (loadingWasInitiated && this.ignorePreviousPageLoading);
  }

  get isGetUrlCheck() {
    return (this.wasExecuteSyncScriptCmd || this.previousPageIsLoading) && this.isGetUrlCmd;
  }

  get otherAffectedCommands() {
    const [method, endpoint = ''] = (this.params.hash || '').split(':');
    const endpointRegex = COMMANDS_DEPENDENT_ON_LAST_LOADING[method];
    return endpointRegex && endpoint.match(endpointRegex) && this.previousPageIsLoading;
  }

  get isFetchingTitleCheck() {
    return (this.wasExecutingSyncScriptOnIE(10) || this.previousPageIsLoading) && this.isGetTitleCmd;
  }

  get isFindingElementCheck() {
    const lastCommandPending = this.wasExecutingSyncScriptOnIE(10) || this.isFirefox() || this.wasAcceptAlertCmd || this.previousPageIsLoading;
    return lastCommandPending && this.isFindElementCmd;
  }

  get isExecutingScriptCheck() {
    return this.previousPageIsLoading && this.isExecuteScriptCmd;
  }

  get isCaptureScreenshotCheck() {
    return this.previousPageIsLoading && this.isCaptureScreenshotCmd;
  }

  get isOpenUrlCheck() {
    return this.previousPageIsLoading && (this.isOpenUrlCmd || this.isClickCmd);
  }

  get isTitleAndReadyStateCheck() {
    const notLocal5XX = this.keyObject.udpKeys && !this.keyObject.udpKeys.local_5xx;
    return notLocal5XX && this.keyObject.tunnel && this.keyObject.lastRequest === 'POST:url';
  }

  get shouldHandleTransition() {
    const shouldCheckTransitionForBrowser = this.isChrome() || this.isEdge() || this.isFirefox() ||
      this.isSafari() || this.isInternetExplorer() || this.isIOSBrowser || this.isAndroidBrowser;
    const shouldCheckTransitionForCommand = this.isGetUrlCheck || this.isFindingElementCheck || this.isExecutingScriptCheck ||
      this.isCaptureScreenshotCheck || this.isFetchingTitleCheck || this.otherAffectedCommands || this.isOpenUrlCheck;
    return shouldCheckTransitionForBrowser && shouldCheckTransitionForCommand;
  }
}

module.exports = TransitionChecker;
