'use strict';

const helper = require('../../helper');

const redactCapabilities = (capabilities, capabilitiesToRedact) => {
  capabilitiesToRedact.forEach((capability) => {
    if (capabilities[capability]) {
      capabilities[capability] = '[REDACTED]';
    }
  });
  return capabilities;
};

const redactW3CCapabilities = (capabilities, capabilitiesToRedact, bstackOptsToRedact) => {
  // since in W3C appium capabilities will be prefixed with "appium:"
  const prefix = 'appium:';
  const firstMatch = helper.nestedKeyValue(capabilities, ['firstMatch']) || [];
  const alwaysMatch = helper.nestedKeyValue(capabilities, ['alwaysMatch']) || {};
  const bstackOptionsHash = helper.nestedKeyValue(capabilities, ['bstack:options']) || {};

  capabilitiesToRedact.forEach((capability) => {
    if (alwaysMatch[prefix + capability]) {
      alwaysMatch[prefix + capability] = '[REDACTED]';
    }
    if (alwaysMatch[capability]) {
      alwaysMatch[capability] = '[REDACTED]';
    }
    firstMatch.forEach((firstMatchSet) => {
      if (firstMatchSet[prefix + capability]) {
        firstMatchSet[prefix + capability] = '[REDACTED]';
      }
      if (firstMatchSet[capability]) {
        firstMatchSet[capability] = '[REDACTED]';
      }
    });
  });

  const alwaysMatchBstackOptions = alwaysMatch['bstack:options'];
  bstackOptsToRedact.forEach((option) => {
    if (helper.isHash(alwaysMatchBstackOptions) && alwaysMatchBstackOptions[option]) {
      alwaysMatchBstackOptions[option] = '[REDACTED]';
    }

    if (helper.isHash(bstackOptionsHash) && bstackOptionsHash[option]) {
      bstackOptionsHash[option] = '[REDACTED]';
    }

    firstMatch.forEach((firstMatchSet) => {
      const firstMatchBstackOptions = firstMatchSet['bstack:options'];
      if (helper.isHash(firstMatchBstackOptions) && firstMatchBstackOptions[option]) {
        firstMatchBstackOptions[option] = '[REDACTED]';
      }
    });
  });
  return capabilities;
};

module.exports = { redactCapabilities, redactW3CCapabilities };
