'use strict';

const HubLogger = require('../../log');
const { LOG_LEVEL: LL } = require('../../constants');
const helper = require('../../helper');

const REDACTABLE_COOKIE_FIELDS = new Set(['value']);
const REDACTABLE_IOS_SESSION_CAPABILITIY_FIELDS = new Set(['keychainPassword', 'keychainPath']);

/*
 * returns cookie redacted fields specified in `REDACTABLE_COOKIE_FIELDS`.
 * :params: cookie
 * :return: cookie
 */

const redactCookie = (cookie) => {
  Object.keys(cookie)
    .filter(field => REDACTABLE_COOKIE_FIELDS.has(field))
    .forEach(field => cookie[field] = '[REDACTED VALUE]'); // eslint-disable-line no-return-assign
  return cookie;
};

/**
 * returns object redacted fields specified in `redactableKeys` param.
 * @param {Object} object
 * @param {Object[]} redactableKeys
 * @returns {Object}
 */
const redactObject = (object, redactableKeys) => {
  redactableKeys
    .forEach((field) => {
      if (object[field]) {
        object[field] = '[REDACTED VALUE]';
      }
    }); // eslint-disable-line no-return-assign
  return object;
};

/*
 * returns redacted response for getCookies commands.
 * :params: response
 * :return: response
 */

const redactGetCookies = (response) => {
  const data = JSON.parse(response.string);
  data.value = Array.isArray(data.value) ? data.value.map(redactCookie) : redactCookie(data.value);
  response.string = JSON.stringify(data);
  return response;
};

const redactScreenshot = (response) => {
  const data = JSON.parse(response.string);
  data.value = '[REDACTED VALUE]';
  response.string = JSON.stringify(data);
  return response;
};

const redactiOSGetSession = (response) => {
  const data = JSON.parse(response.string);
  if (data.value.capabilities) {
    data.value = redactObject(data.value.capabilities, REDACTABLE_IOS_SESSION_CAPABILITIY_FIELDS);
  } else {
    data.value = redactObject(data.value, REDACTABLE_IOS_SESSION_CAPABILITIY_FIELDS);
  }

  response.string = JSON.stringify(data);
  return response;
};

const redactScriptValue = (response) => {
  const data = JSON.parse(response.string);
  if (isValueExcludedForExecuteScript(data)) {
    return response;
  }

  data.value = '[REDACTED VALUE]';
  response.string = JSON.stringify(data);
  return response;
};

/**
 * Method to check few exclusions to
 * maskable values for execute script.
 * 1. document.readyState values
 * 2. Boolean values
 */
const isValueExcludedForExecuteScript = (data) => {
  const val = helper.nestedKeyValue(data, ["value"]);
  const readyStateValues = ["complete", "loading", "interactive"];
  const booleanValues = [true, false, "true", "false"];
  return [...readyStateValues, ...booleanValues].includes(val);
};

/**
 * Redacts the username and password credentials passed in JS executor
 * :params: commandJsonString: Stringified JSexecutor string
 * :params: origParsedCommand: original JSON parsed object of commandJsonString
 * :return: Redacted stringified JSON object
 */
const redactCustomExecutorData = (commandJsonString, origParsedCommand) => {
  // Deepcopy the original JSON parsed object so that its credentials doesn't
  // get redacted inplace
  const parsedCommand = JSON.parse(JSON.stringify(origParsedCommand));
  if (parsedCommand.action === 'sendBasicAuth') {
    const arg = parsedCommand.arguments;
    if (arg) {
      const { username, password } = arg;
      if (username) {
        parsedCommand.arguments.username = '[REDACTED]';
      }
      if (password) {
        parsedCommand.arguments.password = '[REDACTED]';
      }
      return JSON.stringify(parsedCommand);
    }
  }
  return commandJsonString;
};

/*
 * returns redacted request for setCookies commands.
 * :params: request
 * :return: request
 */

const redactSetCookies = (request) => {
  const data = JSON.parse(request.log_data);
  data.cookie = redactCookie(data.cookie);
  request.log_data = JSON.stringify(data);
  return request;
};

/*
 * returns redacted value for getText/getValue commands.
 * :params: response
 * :return: response
 */

const redactGetValues = (response) => {
  const data = JSON.parse(response.string);
  data.value = '[REDACTED VALUE]';
  response.string = JSON.stringify(data);
  return response;
};

/*
 * returns redacted value for sendKeys/setValue commands.
 * :params: request
 * :return: request
 */

const redactSetValues = (request) => {
  const data = JSON.parse(request.log_data);
  data.text = data.text ? 'REDACTED' : undefined;
  data.value = data.value ? 'REDACTED'.split('') : undefined;
  request.log_data = JSON.stringify(data);
  return request;
};

const MASKABLE_COMMANDS = {
  getcookies: {
    method: 'GET',
    endpoint: /\/wd\/hub\/session\/[^/]+\/cookie(\/[^/]+)?$/,
    target: 'RESPONSE',
    redactors: [redactGetCookies],
  },
  setcookies: {
    method: 'POST',
    endpoint: /\/wd\/hub\/session\/[^/]+\/cookie$/,
    target: 'REQUEST',
    redactors: [redactSetCookies],
  },
  getvalues: {
    method: 'GET',
    endpoint: /\/wd\/hub\/session\/[^/]+\/element\/[^/]+\/(text|(attribute\/[^/]+))$/,
    target: 'RESPONSE',
    redactors: [redactGetValues],
  },
  setvalues: {
    method: 'POST',
    endpoint: /\/wd\/hub\/session\/[^/]+\/(appium\/)?(keys|(element\/[^/]+\/value))$/,
    target: 'REQUEST',
    redactors: [redactSetValues],
  },
  ios_getsession: {
    method: 'GET',
    endpoint: /\/wd\/hub\/session\/[^/]+$/,
    target: 'RESPONSE',
    redactors: [redactiOSGetSession],
  },
  screenshot: {
    method: 'GET',
    endpoint: /\/wd\/hub\/session\/[^/]+\/(screenshot|(element\/[^/]+\/screenshot))$/,
    target: 'RESPONSE',
    redactors: [redactScreenshot],
  }
};

const ATOM_REQUEST = {
  method: 'POST',
  endpoint: /\/wd\/hub\/session\/[^/]+\/execute\/sync$/,
  target: 'RESPONSE',
  redactors: [redactScriptValue],
};

/*
 * returns request and response masks for a particular command.
 * :params: request
 * :params: array[string] `commands to be masked`
 * :return: object[mask]
 */

const getAllMasks = (request, commandsToBeMasked, options) => {
  const requestMasks = [];
  const responseMasks = [];
  // Since w3c removed /attribute/<type> routes
  // Atoms are used by client bindings to query this
  // Hence, masking execute-script responses as well
  let addedCommands = commandsToBeMasked.some(command => checkForValueCommands(command)) ? ATOM_REQUEST : [];
  commandsToBeMasked.map(command => MASKABLE_COMMANDS[command]).concat(addedCommands)
    .filter(({ method, endpoint }) => method === request.method.toUpperCase() && request.url.match(endpoint) && !options.isBStackExecutor)
    .forEach(({ target, redactors }) => {
      const container = target === 'REQUEST' ? requestMasks : responseMasks;
      container.push(...redactors);
    });
  return { requestMasks, responseMasks };
};

const checkForValueCommands = (command) => {
  return ["getvalues", "setvalues"].includes(command);
};

/*
 * returns redacted request or response with exception handling and logging.
 * :params: mask
 * :params: request/response `target`
 * :params: url
 * :params: method
 * :return: request/response `redactedTarget`
 */

const applyMask = (mask, target, url, method) => {
  try {
    return mask(target);
  } catch (err) {
    HubLogger.miscLogger('MaskingError', `Masking failed for URL: ${url} METHOD: ${method} with ${err.toString()}`, LL.ERROR);
  }
  return target;
};

/*
 * returns redacted request and response with exception handling and logging.
 * :params: request
 * :params: response
 * :params: array[string]
 * :return: object[request & response]
 */

const redact = (request, response, maskCommands, options = {}) => {
  const { requestMasks, responseMasks } = getAllMasks(request, maskCommands || [], options);
  const reducer = (target, mask) => applyMask(mask, target, request.url, request.method);
  return {
    request: requestMasks.reduce(reducer, request),
    response: responseMasks.reduce(reducer, response),
  };
};

const redactSensitiveDataFromSessionInfo = (data) => {
  try {
    const parsedData = JSON.parse(data);
    if(helper.nestedKeyValue(parsedData, ["value", "capabilities"])) {
      redactObject(parsedData.value.capabilities, REDACTABLE_IOS_SESSION_CAPABILITIY_FIELDS);
    } else if(helper.nestedKeyValue(parsedData, ["value"])) {
      redactObject(parsedData.value, REDACTABLE_IOS_SESSION_CAPABILITIY_FIELDS);
    }
    return JSON.stringify(parsedData);
  } catch (err) {
    HubLogger.miscLogger('Error while redacting sensitive information from session info', err.toString(), LL.INFO);
    return data;
  }
};

const deletePlatformVersion = (data) => {
  try {
    const parsedData = JSON.parse(data);
    if(helper.nestedKeyValue(parsedData, ["value", "capabilities"]) && helper.isDefined(parsedData['value']['capabilities']['platformName']) && parsedData['value']['capabilities']['platformName'] === 'iOS') {
      delete parsedData['value']['capabilities']['platformVersion'];
    } else if ( helper.isDefined(parsedData['value']['platformName']) && parsedData['value']['platformName'] === 'iOS' )  {
      delete parsedData['value']['platformVersion'];
    }
    return JSON.stringify(parsedData);
  } catch (err) {
    HubLogger.miscLogger('Error while deleting platformVersion from session info', err.toString(), LL.INFO);
    return data;
  }
};

module.exports = {
  redact,
  redactCookie,
  redactScreenshot,
  redactCustomExecutorData,
  redactSensitiveDataFromSessionInfo,
  deletePlatformVersion
};
