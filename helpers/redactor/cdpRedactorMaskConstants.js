'use strict';

const REDACTED_VALUE = 'REDACTED VALUE';

const PLAYWRIGHT_MASKABLE_COMMANDS = {
  sendtype: {
    methodsToMask: {
      elementHandle: ['type'],
      locator: ['type'],
    },
    redactableFields: [
      ['params', 'text'],
      ['params', 'value']
    ],
  },
  sendpress: {
    methodsToMask: {
      elementHandle: ['press'],
      locator: ['press'],
    },
    redactableFields: [
      ['params', 'key']
    ],
  },
  sethttpcredentials: {
    methodsToMask: {
      browser: ['newContext', 'newPage'],
      apiRequest: ['newContext'],
    },
    redactableFields: [
      ['params', 'httpCredentials']
    ],
  },
  setstoragestate: {
    methodsToMask: {
      browser: ['newContext', 'newPage'],
      apiRequest: ['newContext'],
    },
    redactableFields: [
      ['params', 'storageState']
    ],
  },
  setgeolocation: {
    methodsToMask: {
      browser: ['newContext', 'newPage'],
      apiRequest: ['newContext'],
    },
    redactableFields: [
      ['params', 'geolocation']
    ],
  },
  // WebAuth.addCredential - https://chromedevtools.github.io/devtools-protocol/tot/WebAuthn/#method-addCredential - APS-12159
  setwebauthncredentials: {
    methodsToMask: {
      browser: ['newContext', 'newPage'],
      apiRequest: ['newContext']
    },
    // TODO: Currently we have added fields logic for request and response. We need to split this to optimise it as list goes big.
    // TODO: WebAuth.getCredentials return all credentials as array in the response.
    // With Current logic, the response will be redacted only if it contains single element in array. Need to fix it if multiple creds are passed in response.
    redactableFields: [
      ['params', 'params', 'authenticatorId'],
      ['params', 'params', 'credentialId'],
      ['params', 'params', 'credential', 'credentialId'],
      ['params', 'params', 'credential', 'privateKey'],
      ['params', 'params', 'properties', 'privateKey'],
      ['result', 'result', 'authenticatorId'],
      ['result', 'result', 'credentialId'],
      ['result', 'result', 'credential', 'credentialId'],
      ['result', 'result', 'credential', 'privateKey'],
      ['result', 'result', 'credentials']
    ]
  }
};

module.exports = {
  REDACTED_VALUE,
  PLAYWRIGHT_MASKABLE_COMMANDS
}
