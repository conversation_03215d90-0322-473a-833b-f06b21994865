"use strict";

const logger = require("../../logger").basicLogger;
const { PLAYWRIGHT } = require("../../config/socketConstants");
const {
  REDACTED_VALUE,
  PLAYWRIGHT_MASKABLE_COMMANDS,
} = require("./cdpRedactorMaskConstants");
const TAG = "cdpRedactor";

/**
 * Redacts the fields of data from a list of nested key values if it exists
 * @param {Object} data - The data whose fields need to be redacted
 * @param {List[String]} nestedKeyList - List of nested keys that needs to be redacted
 * @param {String} replaceValue - replacement string
 */
const setIfNested = (data, nestedKeyList, replaceValue) => {
  if (data == null) return;
  if (nestedKeyList.length === 1 && data.hasOwnProperty(nestedKeyList)) {
    data[nestedKeyList] = replaceValue;
    return;
  }
  return setIfNested(
    data[nestedKeyList[0]],
    nestedKeyList.slice(1),
    replaceValue
  );
};

/**
 * Redacts the fields of data from a list of nested key values list
 * @param {Object} data - The data whose fields need to be redacted
 * @param {List[List[String]]} maskableFieldsList - List of [list of nested keys] that needs to be redacted
 * @return {Object} Data with its fields redacted
 */
const jsonRedactor = (data, maskableFieldsList) => {
  if (
    data == null ||
    maskableFieldsList == null ||
    maskableFieldsList.length === 0
  )
    return;
  maskableFieldsList.forEach((filteredMaskableFields) => {
    setIfNested(data, filteredMaskableFields, REDACTED_VALUE);
  });
};

/**
 * Playwright specific redactor method that filters the supported/valid playwright commands
 * from passed list of commands and executes jsonRedactor on it
 * @param {Object} data - The data whose fields need to be redacted
 * @param {List[String]} maskCommandList - List of commands that need to be masked
 * @return {Object} Data with its fields redacted
 */
const playwrightRedactor = (data, maskCommandList) => {
  let maskableFields = [];
  maskCommandList
    .map((command) => PLAYWRIGHT_MASKABLE_COMMANDS[command])
    .filter((command) => {
      return command != null;
    })
    .forEach(({ _, redactableFields }) => {
      maskableFields = maskableFields.concat(redactableFields);
    });
  jsonRedactor(data, maskableFields);
  return data;
};

/**
 * Redacts the data based on commands that are passed for a particular framework.
 * Doesn't throw error in case the passed command/framework isn't supported
 *
 * @param {Object} data - The data whose fields need to be redacted
 * @param {List[string]} maskCommandList - List of supported commands that need to be masked
 * @param {string} framework - Framework i.e. playwright, puppeteer, selenium
 * @throws {Error} - Masking failed due to error in redactor methods
 * @return {Object} Data with its fields redacted
 */
const redact = (data, maskCommandList, framework) => {
  const redactor = framework == PLAYWRIGHT ? playwrightRedactor : null;
  if (
    redactor == null ||
    maskCommandList == null ||
    maskCommandList.length === 0
  )
    return data;
  try {
    return redactor(data || {}, maskCommandList || []);
  } catch (err) {
    logger.error(
      `Masking failed for data: ${JSON.stringify(
        data
      )} for commands: ${maskCommandList} for framework: ${framework} with ${err.toString()}`,
      { TAG }
    );
  }
};

module.exports = {
  redact,
};
