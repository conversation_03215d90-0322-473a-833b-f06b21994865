// This file has helper functions for supporting w3c for app automate

'use strict';

const helper = require('../helper');
const constants = require('../constants');
const jwpMap = require('../jwp_status_code_map.json');
const HubLogger = require('../log');
const { isUndefined } = require('../typeSanity');
const LL = constants.LOG_LEVEL;

// This function checks if the desiredCapabilities in rawCaps has any browserstack specific capability or not.
const doDesiredCapsHasBrowserStackCapabilities = (caps) => {
  if (helper.isHash(caps[constants.DESIRED_CAPABILTITES])) {
    const desiredCapabilities = caps[constants.DESIRED_CAPABILTITES];
    let desiredCapsKeys = Object.keys(desiredCapabilities);
    for (let i = 0; i < desiredCapsKeys.length; i++) {
      let key = desiredCapsKeys[i].toLowerCase();
      if (key !== "browserstack.use_w3c" && key !== "browserstack.usew3c" && (constants.APP_AUTOMATE_SPECIFIC_CAPS.includes(key) || (key.startsWith('browserstack.')))) {
        return true;
      }
    }
  }
  return false;
};

const getFirstMatchAndAlwaysMatch = (capabilities) => {
  const firstMatch = helper.nestedKeyValue(capabilities, ['capabilities', 'firstMatch']) || [];
  const alwaysMatch = helper.nestedKeyValue(capabilities, ['capabilities', 'alwaysMatch']) || {};
  return { firstMatch, alwaysMatch };
};

const doW3CHasCapability = (caps, w3cCapName) => {
  // only considering the first entry of firstMatch
  const { firstMatch: [firstMatch = {}], alwaysMatch } = getFirstMatchAndAlwaysMatch(caps);
  return (alwaysMatch.hasOwnProperty(w3cCapName) || firstMatch.hasOwnProperty(w3cCapName));
};

const doW3CHasNestedCapability = (caps, w3cCapParentName, w3cCapName) => {
  // only considering the first entry of firstMatch
  const { firstMatch: [firstMatch = {}], alwaysMatch } = getFirstMatchAndAlwaysMatch(caps);
  if (alwaysMatch.hasOwnProperty(w3cCapParentName) || firstMatch.hasOwnProperty(w3cCapParentName)) {
    return ((helper.isHash(alwaysMatch[w3cCapParentName]) && alwaysMatch[w3cCapParentName].hasOwnProperty(w3cCapName)) || (helper.isHash(firstMatch[w3cCapParentName]) && firstMatch[w3cCapParentName].hasOwnProperty(w3cCapName)));
  }
};
// This function checks on the basis of input raw cap, whether a w3c session for appautomate should be started or not
// Conditions are:
// 1. Capabiltiies should have "appium:app" capability.
// 2. If capabilties hash is non empty and desired caps is empty then run w3c session
// 3. If capabilities hash is empty and desired caps is non empty then run jsonwp session.
// 4. If capabilities hash has "bstack:options" cap then run w3c session
// 5. If desiredCaps do not have any browserstack specific cap thenrun w3c session.
const isW3CAppAutomateSession = (caps) => {

  // if automationName doesn't support w3c then return false
  // in case desiredCapabilities object is not present, let session be detected as w3c and further
  // handling will be done on rails.
  try {
    if (helper.isNonEmptyHash(caps[constants.DESIRED_CAPABILTITES]) &&
      caps[constants.DESIRED_CAPABILTITES]["automationName"] &&
      constants.AUTOMATION_NAMES_NOT_SUPPORTING_W3C.includes(caps[constants.DESIRED_CAPABILTITES]["automationName"].toLowerCase())) {
      return false;
    }
    // if no or empty capabilities object then return false
    if (helper.isNonEmptyHash(caps[constants.CAPABILITIES])) {
      if (!(doW3CHasCapability(caps, 'appium:app') || doW3CHasNestedCapability(caps, 'appium:options', 'app') || doW3CHasNestedCapability(caps, 'appium:options', 'appium:app'))) {
        return false;
      }

      if (!helper.isNonEmptyHash(caps[constants.DESIRED_CAPABILTITES])
        || doW3CHasCapability(caps, constants.BSTACK_OPTIONS)
        || !doDesiredCapsHasBrowserStackCapabilities(caps)
      ) {
        return true;
      }
    }
  } catch (e) {
    HubLogger.exceptionLogger(`Exception in isW3CAppAutomateSession ${e.toString()}\nCapabilities : ${JSON.stringify(caps)}`);
  }
  return false;
};

const mapW3CErrorToJSONWPStatus = (error) => {
  let statusCode = 0;

  if (isUndefined(error)) {
    return statusCode;
  }

  try {
    Object.keys(jwpMap).forEach(function (key) {
      if(error.includes(jwpMap[key])){
        statusCode = parseInt(key);
        return statusCode;
      }
   });
  } catch(e) {
    HubLogger.miscLogger('mapW3CErrorToJSONWPStatus', `Failed to fetch status code from ${error}`, LL.ERROR);
  }

  return statusCode;
};

module.exports = {
  isW3CAppAutomateSession,
  doDesiredCapsHasBrowserStackCapabilities,
  getFirstMatchAndAlwaysMatch,
  doW3CHasCapability,
  doW3CHasNestedCapability,
  mapW3CErrorToJSONWPStatus
};
