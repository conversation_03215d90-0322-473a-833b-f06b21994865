'use strict';

const iOSAcceptSSLCertHandler = require('./iOSAcceptSSLCertHandler');
const safariSSLHandler = require('./safariAcceptSSLHandler');
const customExecutorHelper = require('../customExecutorHelper');
const ERRORS_MAP = require('../../../constants').CUSTOM_EXECUTOR_ERRORS_ACCEPT_SSL;

const EXECUTOR_TYPE = 'acceptSsl';

const runAcceptSSLHandler = (keyObject, requestStateObj) => {
  let sendResponse = () => {
    customExecutorHelper.instrumentAndSendExecutorResponse(EXECUTOR_TYPE, keyObject, requestStateObj, false);
  };
  if (keyObject.os.toLowerCase() === 'ios' && keyObject.deviceName) {
    let isAcceptSslActionOnIOS11OrAbove = false;
    const osVersion = parseInt(keyObject.deviceName.split('-')[1] || keyObject.os_version, 10);
    isAcceptSslActionOnIOS11OrAbove = osVersion >= 11;
    if (!isAcceptSslActionOnIOS11OrAbove) {
      customExecutorHelper.instrumentAndSendError(EXECUTOR_TYPE, keyObject, requestStateObj, ERRORS_MAP.unsupported_on_ios10_or_below);
      return;
    }
    requestStateObj.hash = 'POST:value';
    requestStateObj.data = JSON.stringify({
      sessionId: requestStateObj.clientSessionID,
      status: 0,
      value: null,
    });
    iOSAcceptSSLCertHandler.sslHandler(keyObject, sendResponse);
  } else {
    if (/safari/i.test(keyObject.browser)) {
      requestStateObj.hash = 'POST:value';
      requestStateObj.data = JSON.stringify({
        sessionId: requestStateObj.clientSessionID,
        status: 0,
        value: null,
      });
      safariSSLHandler.sslHandler(keyObject, requestStateObj, sendResponse);
    } else {
      customExecutorHelper.instrumentAndSendError(EXECUTOR_TYPE, keyObject, requestStateObj, ERRORS_MAP.unsupported_browser);
      return;
    }
  }
};

module.exports = { runAcceptSSLHandler };
