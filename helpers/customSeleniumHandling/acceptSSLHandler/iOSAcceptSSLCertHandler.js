'use strict';

/* eslint-disable no-await-in-loop */

const Promise = require('bluebird');

const {
  PAGE_LOAD_IOS11_TIMEOUT,
  SAFARI_INSECURE_PAGE_TITLE,
  LOG_LEVEL: LL,
} = require('../../../constants');
const HubLogger = require('../../../log');
const SeleniumClient = require('../../../seleniumClient');
const supporting = require('../../../supporting');

const iOSAcceptSSLCertHandlerFailureDelay = 2000;

const sslHandler = async (keyObject, successCallback) => {
  const { rails_session_id: railsSessionId, deviceName } = keyObject;
  const sel = new SeleniumClient(keyObject);
  for (let attempt = 0; attempt <= 5; attempt += 1) {
    HubLogger.miscLogger('iOSAcceptSSLCertHandler', `Trying for session: ${railsSessionId}  Device: ${deviceName} Try : ${attempt}`, LL.INFO);
    try {
      await sel.checkInsecureWebsite(SAFARI_INSECURE_PAGE_TITLE);
      await sel.executeScript({ script: 'CertificateWarningController.visitInsecureWebsiteWithTemporaryBypass();', args: [] });
      supporting.waitForPageLoad(
        successCallback,
        successCallback,
        keyObject,
        PAGE_LOAD_IOS11_TIMEOUT
      );
      return true;
    } catch (err) {
      HubLogger.miscLogger('iOSAcceptSSLCertHandler check failed', `session: ${railsSessionId}  Device: ${deviceName} reason: ${err}`, LL.INFO);
      await Promise.delay(iOSAcceptSSLCertHandlerFailureDelay);
    }
  }
  successCallback(); // ran out of attempts
};

module.exports = { 
  sslHandler 
};
