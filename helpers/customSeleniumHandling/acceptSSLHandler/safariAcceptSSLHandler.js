'use strict';

/* eslint-disable no-await-in-loop */
const {
  PAGE_LOAD_DEFAULT_TIMEOUT,
  SAFARI_INSECURE_PAGE_TITLE,
  DISABLE_INSECURE_MAX_ATTEMPTS,
  LOG_LEVEL: LL,
} = require('../../../constants');
const HubLogger = require('../../../log');
const SeleniumClient = require('../../../seleniumClient');
const supporting = require('../../../supporting');

const TAG = 'ACCEPT_SSL_JS_EXECUTOR';

const ACCEPT_SSL_HANDLER_FAILURE_DELAY = 2000;

const isPostBigSur = os => {
  return !['macsl', 'maclion', 'macml', 'macmav', 'macyos', 'macelc', 'macsie', 'machs', 'macmo', 'maccat', 'macbsr'].includes(os);
};

const sslHandler = async (keyObject, requestStateObj, callback) => {
  const {rails_session_id: railsSessionId, browser, browser_version } = keyObject;

  // For el captain or below, js executor is not needed.
  // Just executing title check to add delay, by which time existing accept ssl logic is triggered and accepts pop up
  const sel = new SeleniumClient(keyObject);
  for (let attempt = 0; attempt <= DISABLE_INSECURE_MAX_ATTEMPTS; attempt++ ) {
    HubLogger.miscLogger(TAG, `Trying for session: ${railsSessionId}, browser ${browser} ${browser_version} Try : ${attempt}`, LL.INFO);
    try {
      await sel.checkInsecureWebsite(SAFARI_INSECURE_PAGE_TITLE);
      if (keyObject && keyObject.os && isPostBigSur(keyObject.os)) {
        await sel.executeScript({ script: 'CertificateWarningController.visitInsecureWebsite();', args: [] });
      } else {
        await sel.executeScript({ script: 'CertificateWarningController.visitInsecureWebsiteWithTemporaryBypass();', args: [] });
      }
      await sel.checkInsecureWebsite(SAFARI_INSECURE_PAGE_TITLE, false);
      supporting.waitForPageLoad(
        callback,
        callback,
        keyObject,
        PAGE_LOAD_DEFAULT_TIMEOUT
      );
      HubLogger.miscLogger(`${TAG} check successful`, `session: ${railsSessionId}`, LL.DEBUG);
      return true;
    } catch (err) {
      HubLogger.miscLogger(`${TAG} check failed`, `session: ${railsSessionId}  browser ${browser} ${browser_version} reason: ${err}`, LL.INFO);
      await new Promise(resolve => setTimeout(resolve, ACCEPT_SSL_HANDLER_FAILURE_DELAY));
    }
  }
  callback(); // ran out of attempts
};

module.exports = {
  sslHandler,
  isPostBigSur
};
