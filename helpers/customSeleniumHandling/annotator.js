'use strict';

const requestTypeCorrection = require('./requestTypeCorrection');
const customExecutorHelper = require('./customExecutorHelper');
const { isUndefined, isNotUndefined } = require('../../typeSanity');
const { ANNOTATE_LOG_LEVELS, BROWSERSTACK_EXECUTOR_PREFIX } = require('../../constants');

const ANNOTATE = 'annotate'
const ANNOTATION = "Annotation";
const DEFAULT_ANNOTATE_LOG_LEVEL = 'info'
/**
 * This method handles the request and calls
 * the internal method to log the commands from
 * different web frameworks, using the execute script
 * of selenium.
 *
 * @params keyObject : Hash containing state related to the session
 * @params requestObj : Wrapper around current request received by Hub
 * @params payload : JSON data that is being received from any plugin
 *
 * @returns {undefined}
 */
const annotate = (keyObject, requestObj, { arguments: payload }) => {
  const { type, data, level } = payload;
  if (isUndefined(requestObj.request)){
    requestObj.request = {};
  }
  requestTypeCorrection(requestObj, type);
  requestTypeCorrection(requestObj.request, type);

  const executorData = {
    action: ANNOTATE,
    arguments: {
      type: isUndefined(type) ? ANNOTATION : type,
      data, 
      level: isNotUndefined(level) && ANNOTATE_LOG_LEVELS.has(level.toLowerCase()) ? level.toLowerCase() : DEFAULT_ANNOTATE_LOG_LEVEL,
    }
  };

  const logData = {
    script: `${BROWSERSTACK_EXECUTOR_PREFIX} ${JSON.stringify(executorData)}`,
    args: []
  };
  
  requestObj.request.log_data = JSON.stringify(logData);

  requestObj.data = JSON.stringify({
    sessionId: requestObj.clientSessionID,
    status: 0,
    value: executorData.arguments.data,
  });
  customExecutorHelper.instrumentAndSendExecutorResponse(ANNOTATE, keyObject, requestObj, false);
};

module.exports = { annotate };
