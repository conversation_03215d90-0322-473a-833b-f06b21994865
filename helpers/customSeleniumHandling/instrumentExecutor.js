'use strict';

const executorStatuses = ['success', 'error'];

const getInstrumentationData = keyObject => keyObject.customExecutorInstrumentation;

// Sample custom executor instrumentation data
// {
//   "custom_executor": {
//   "biometric_custom_executor": {
//     "count": 10,
//     "success": {
//       "count": 3
//     },
//     "error": {
//       "count": 7,
//         "error_types": {
//         "invalid_action": 2,
//           "invalid_arguments": 2,
//           "popup_absent": 1,
//           "popup_not_clicked": 1,
//           "unknown": 1
//       }
//     }
//   }
// }
// }
const setInstrumentationData = (keyObject, executorType, executorStatus, executorStatusType = null) => {
  if (!executorStatuses.includes(executorStatus)) {
    return;
  }

  if (executorStatus === 'error' && !executorStatusType) {
    return;
  }

  let instruData = getInstrumentationData(keyObject);
  if (!instruData) {
    instruData = { custom_executor: {} };
    instruData.custom_executor[executorType] = { count: 0 };
  }

  if (!instruData.custom_executor[executorType]) {
    instruData.custom_executor[executorType] = { count: 0 };
  }

  if (!instruData.custom_executor[executorType][executorStatus]) {
    instruData.custom_executor[executorType][executorStatus] = { count: 0 };
    if (executorStatus === 'error') {
      instruData.custom_executor[executorType][executorStatus].error_types = {};
    }
  }

  instruData.custom_executor[executorType].count += 1;
  instruData.custom_executor[executorType][executorStatus].count += 1;

  // only setting `executorStatusType` in case of errors.
  if (executorStatus === 'error' && executorStatusType) {
    const executorStatusTypeCount = (instruData.custom_executor[executorType][executorStatus].error_types[executorStatusType] || 0) + 1;
    instruData.custom_executor[executorType][executorStatus].error_types[executorStatusType] = executorStatusTypeCount;
  }

  keyObject.customExecutorInstrumentation = instruData;
};

module.exports = {
  getInstrumentationData,
  setInstrumentationData,
};
