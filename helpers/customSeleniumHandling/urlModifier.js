'use strict';

const baseURLGenerator = (url, urlOptionsArray) => {
  if (!Array.isArray(urlOptionsArray)) {
    urlOptionsArray = [urlOptionsArray];
  }
  const urlElements = url.split('/');
  urlElements.splice(-1, 1, ...urlOptionsArray);
  return urlElements.join('/');
};

const RANDOM_ELEMENT_ID = 1234;
const genSendValueURL = (url) => {
  const elementArray = ['element', RANDOM_ELEMENT_ID, 'value'];
  return baseURLGenerator(url, elementArray);
};
const genMouseReleasedURL = url => baseURLGenerator(url, ['mouse', 'released']);

const genMousePressedURL = url => baseURLGenerator(url, ['mouse', 'pressed']);

const genMouseMovedURL = url => baseURLGenerator(url, ['mouse', 'moved']);

const genEvaluateExpression = url => baseURLGenerator(url, ['element', RANDOM_ELEMENT_ID, 'expression'])

const genFocusElement = url => baseURLGenerator(url, ['element', RANDOM_ELEMENT_ID, 'focus']);

const genClickURL = url => baseURLGenerator(url, 'click');

const genFindElementURL = url => baseURLGenerator(url, 'element');

const genAssertMatchURL = url => baseURLGenerator(url, 'assertion');

const genOpenURL = url => baseURLGenerator(url, 'url');

const genPressURL = url => baseURLGenerator(url, 'press');

const genGetCookies = url => baseURLGenerator(url, 'cookie');

const genSetGeolocation = url => baseURLGenerator(url, ['set', 'geolocation']);

const genTouch = url => baseURLGenerator(url, 'touch');

const genMouseMove = url => baseURLGenerator(url, ['move', 'mouse']);

const genTakeScreenshot = url => baseURLGenerator(url, 'screenshot');

const genSetFile = url => baseURLGenerator(url, 'file');

const genNewContext = url => baseURLGenerator(url, ['new', 'context']);

module.exports = {
  genSendValueURL,
  genClickURL,
  genFindElementURL,
  genAssertMatchURL,
  genOpenURL,
  genPressURL,
  genGetCookies,
  genSetGeolocation,
  genTouch,
  genMouseMove,
  genTakeScreenshot,
  genSetFile,
  genNewContext,
  genFocusElement,
  genEvaluateExpression,
  genMouseReleasedURL,
  genMousePressedURL,
  genMouseMovedURL
};
