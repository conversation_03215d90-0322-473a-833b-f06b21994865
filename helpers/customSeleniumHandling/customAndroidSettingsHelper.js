'use strict';

const moment = require('moment/moment');
const pubSub = require('../../pubSub');
const { constants } = require('../../log');
const instrumentAndSendError = require('../../helpers/customSeleniumHandling/customExecutorHelper').instrumentAndSendError;
const sendRequestToPlatform = require('../../helpers/customSeleniumHandling/customExecutorHelper').sendRequestToPlatform;
const instrumentAndSendExecutorResponse = require('../../helpers/customSeleniumHandling/customExecutorHelper').instrumentAndSendExecutorResponse;
const HubLogger = require('../../log');

const validateCustomDate =
  (keyObject, args, dateTimeErrors, customObject, listOfAndroidSettingsToBeUpdated) => {
    if (!keyObject.accessibleFeaturesList.includes(constants.DF_FEATURE_FENCE_CODES.CUSTOM_DATE_PLAN_CODE)) {
      customObject.error = dateTimeErrors.feature_not_available_in_current_plan_for_aa;
      return;
    }
    if (parseInt(keyObject.os_version, 10) < 6) {
      customObject.error = dateTimeErrors.incompatible_os_version;
      return;
    }
    const givenDate = args.customDate;
    try {
      if (!((/^(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)(\.)?(\w*)?(\.)?(\s*\d{0,2}\s*)(\.)?(\w*)?(\.)?(\s*\d{4})$/).test(givenDate) &&
        (moment(givenDate, 'MMM DD YYYY')
          .isValid()) &&
        parseInt(givenDate.split(' ')[2], 10) >= 2015 &&
        parseInt(givenDate.split(' ')[2], 10) <= 2037)) {
        customObject.error = dateTimeErrors.invalid_value;
        return;
      }
      customObject.date = givenDate;
      listOfAndroidSettingsToBeUpdated.add('date_time');
      customObject.executorError = dateTimeErrors.executor_internal_error;
    } catch (ex) {
      customObject.error = dateTimeErrors.invalid_value;
    }
  };

const validateCustomTime = (keyObject, args, dateTimeErrors, customObject, listOfAndroidSettingsToBeUpdated) => {
  if (!keyObject.accessibleFeaturesList.includes(constants.DF_FEATURE_FENCE_CODES.CUSTOM_TIME_PLAN_CODE)) {
    customObject.error = dateTimeErrors.feature_not_available_in_current_plan_for_aa;
    return;
  }
  if (parseInt(keyObject.os_version, 10) < 6) {
    customObject.error = dateTimeErrors.incompatible_os_version;
    return;
  }
  const givenTime = args.customTime;
  if (!/^(2[0-3]|[01]?[0-9]):([0-5][0-9])$/.test(givenTime)) {
    customObject.error = dateTimeErrors.invalid_value;
    return;
  }
  listOfAndroidSettingsToBeUpdated.add('date_time');
  customObject.time = givenTime;

  customObject.executorError = dateTimeErrors.executor_internal_error;
};

const validate12HourTime = (keyObject, args, hourFormatErrors, customObject, listOfAndroidSettingsToBeUpdated) => {
  if (!keyObject.accessibleFeaturesList.includes(constants.DF_FEATURE_FENCE_CODES.CUSTOM_TIME_PLAN_CODE)) {
    customObject.error = hourFormatErrors.feature_not_available_in_current_plan_for_aa;
    return;
  }
  if (parseInt(keyObject.os_version, 10) < 6) {
    customObject.error = hourFormatErrors.incompatible_os_version;
    return;
  }
  let givenValue = args['12HourTime'];
  if (!givenValue ||  !["on", "off"].includes(givenValue.toLowerCase())) {
    customObject.error = hourFormatErrors.invalid_value;
    return;
  }
  listOfAndroidSettingsToBeUpdated.add('date_time');
  customObject.hourFormat12 = givenValue.toLowerCase() === 'on' ? true : false;
  customObject.executorError = hourFormatErrors.executor_internal_error;
};


const setLockedForDeviceSettingsFlowAndroid = (keyObject, value) => {
  keyObject.isLockedForDeviceSettingsFlow = value;
  // makes lock persistent in case for hub toggle
  pubSub.publish(constants.updateKeyObject, {
    session: keyObject.rails_session_id,
    changed: {
      isLockedForDeviceSettingsFlow: value,
    },
  });
};

// Can add more validating conditions based on the future arguments
// As of now writing as a arrow function as have only single validation
const validateCumulativeResponseFromPlatform = parsedData => parsedData &&
  parsedData.date_time != null &&
  parsedData.date_time.status != null &&
  parsedData.date_time.status.toString() === '200';

const sendUpdateSettingsRequestToPlatform = (keyObject, requestData, requestStateObj, executorType, dateTimeErrors, customObject) => {
  const serverURL = '/update_android_settings';
  const body = Buffer.from(JSON.stringify({
    device: keyObject.device,
    app_automate_session_id: keyObject.rails_session_id,
    product: 'app_automate',
    user_id: keyObject.user,
    ...requestData
  }));

  const customTimeout = (keyObject.idle_timeout - (keyObject.idle_timeout > 20 ? 20 : 0)) * 1000;

  const customOptions = {
    method: 'POST',
    body,
    headers: {
      accept: 'application/json',
      'content-type': 'application/json; charset=utf-8',
      'content-length': body.length,
    }
  };

  const responseHandler = (responseCallBack) => {
    if (responseCallBack && responseCallBack.statusCode === 200) {
      try {
        const data = responseCallBack.data;
        const parsedData = JSON.parse(data);
        if (validateCumulativeResponseFromPlatform(parsedData)) {
          instrumentAndSendExecutorResponse(executorType, keyObject, requestStateObj);
        } else {
          instrumentAndSendError(executorType,
            keyObject,
            requestStateObj,
            customObject.executorError);
        }
      } catch (err) {
        HubLogger.tempExceptionLogger('Set_Date_Time: Error in parsing response from platform', err);
        instrumentAndSendError(executorType,
          keyObject,
          requestStateObj,
          customObject.executorError);
      }
    } else {
      instrumentAndSendError(executorType,
        keyObject,
        requestStateObj,
        customObject.executorError);
    }
    setLockedForDeviceSettingsFlowAndroid(keyObject, false);
  };

  sendRequestToPlatform(executorType, serverURL, requestStateObj, keyObject, customObject.executorError, customTimeout, customOptions, responseHandler);
};

const validateAllUpdateAndroidSettings = (keyObject, args, dateTimeErrors, hourFormatErrors, customObject, listOfAndroidSettingsToBeUpdated) => {
  Object.keys(args)
    .slice(0, 20)
    .forEach((key) => {
      switch (key) {
        case 'customTime': {
          validateCustomTime(keyObject,
            args,
            dateTimeErrors,
            customObject,
            listOfAndroidSettingsToBeUpdated);
          break;
        }
        case 'customDate': {
          validateCustomDate(keyObject,
            args,
            dateTimeErrors,
            customObject,
            listOfAndroidSettingsToBeUpdated);
          break;
        }
        case '12HourTime': {
          validate12HourTime(keyObject,
            args,
            hourFormatErrors,
            customObject,
            listOfAndroidSettingsToBeUpdated);
          break;
        }
        default:
          break;
      }
    });
};

module.exports = {
  validateAllUpdateAndroidSettings,
  setLockedForDeviceSettingsFlowAndroid,
  sendUpdateSettingsRequestToPlatform
};
