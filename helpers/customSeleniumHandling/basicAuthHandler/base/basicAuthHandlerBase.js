'use strict';

const HubLogger = require('../../../../log');
const constants = require('../../../../constants');
const { instrumentAndSendError } = require('../../customExecutorHelper');
const setInstrumentationData = require('../../instrumentExecutor').setInstrumentationData;

const LL = constants.LOG_LEVEL;
const JSE_ERRORS = constants.JSE_BASIC_AUTH;

/**
 * Base class for Basic Auth Handlers.
 * Common methods are added in here which have common implementation as well.
 */
// eslint-disable-next-line max-len
// eslint no-underscore-dangle: ["error", { "allow": ["_sendBasicAuth", "_dismissBasicAuth", "_browserSanity", "_checkArguments" ] }]
class BasicAuthHandlerBase {
  // Checks whether 'argument' field exists in the command sent by user or not.
  _checkArguments() {
    const { action, arguments: args } = this.parsedCommand;
    if (args === undefined) {
      const errorString = JSE_ERRORS.missing_arguments.message.replace(constants.JSE_EXECUTERTYPE_REGEX, action);
      HubLogger.miscLogger(`[BasicAuthHandler] sessionId: ${this.requestStateObj.clientSessionID}, Error: ${errorString}`, '', LL.INFO);
      instrumentAndSendError(action, this.keyObject, this.requestStateObj, JSE_ERRORS.missing_arguments);
      return false;
    }
    return true;
  }

  // eslint-disable-next-line max-len
  // Does a sanity of the browser being passed whether its supported or not and raises error if reqd.
  _browserSanity(regexPattern) {
    const stripBrowser = this.keyObject.browser.toLowerCase().match(regexPattern);

    if (!stripBrowser) {
      const { action } = this.parsedCommand;
      const errorString = JSE_ERRORS.unsupported_browsers.message.replace(constants.JSE_EXECUTERTYPE_REGEX, action);
      HubLogger.miscLogger(`[BasicAuthHandler] sessionId: ${this.requestStateObj.clientSessionID}, Error in browser sanity: ${errorString}`, '', LL.INFO);
      instrumentAndSendError(action, this.keyObject, this.requestStateObj, JSE_ERRORS.unsupported_browsers);
      return;
    }
    this.strippedBrowser = stripBrowser[0];
    this.browserCompatible = true;
  }

  successCallback() {
    HubLogger.miscLogger(`[BasicAuthHandler] Successfully sent data for ${this.parsedCommand.action}: ${this.requestStateObj.clientSessionID}`, '', LL.INFO);
    setInstrumentationData(this.keyObject, this.parsedCommand.action, 'success');
  }

  errorCallback(reason) {
    HubLogger.exceptionLogger(`[BasicAuthHandler] Error in executing ${this.parsedCommand.action}: ${this.requestStateObj.clientSessionID}. Reason: ${reason}`);
    // Note - not using send error, since we are not sending error response as of now
    setInstrumentationData(this.keyObject, this.parsedCommand.action, 'error', reason);
  }
}

module.exports = BasicAuthHandlerBase;
