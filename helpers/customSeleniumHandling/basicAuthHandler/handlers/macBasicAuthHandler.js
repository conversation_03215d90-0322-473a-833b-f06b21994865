'use strict';

const bridge = require('../../../../bridge');
const { sendExecutorResponse } = require('../../customExecutorHelper');
const BasicAuthHandlerBase = require('../base/basicAuthHandlerBase');

/**
 * Mac basic auth handler to perform the operations of
 * Handling and Dismissing of basic auth
 */
class MacBasicAuthHandler extends BasicAuthHandlerBase {
  constructor(keyObject, requestStateObj, parsedCommand, timeout) {
    super();
    this.keyObject = keyObject;
    this.requestStateObj = requestStateObj;
    this.parsedCommand = parsedCommand;
    this.timeout = timeout;
    this.browserCompatible = false;
    this._browserSanity(new RegExp(/chrome|firefox|edge/i));
  }

  // executes basic auth with username and password
  _sendBasicAuth() {
    const { username, password } = this.parsedCommand.arguments;

    bridge.basicAuthMac(this.keyObject, this.requestStateObj.clientSessionID, username, password)
      .then(() => {
        this.successCallback();
      })
      .catch((reason) => {
        this.errorCallback(reason);
      });
  }

  // Dismiss basic auth popup
  _dismissBasicAuth() {
    bridge.basicAuthMac(this.keyObject, this.requestStateObj.clientSessionID, '', '', 'dismiss')
      .then(() => {
        this.successCallback();
      })
      .catch((reason) => {
        this.errorCallback(reason);
      });
  }


  /**
   * Entry point for executing basic auth on mac.
   * Executes the method with the sanitized timeout
   */
  executeBasicAuth() {
    if (!this.browserCompatible || !this._checkArguments()) return;

    setTimeout(() => this._sendBasicAuth(), this.timeout);

    sendExecutorResponse(this.requestStateObj, this.keyObject);
  }

  /**
   * Entry point for dismissing basic auth on mac.
   * Executes the method with the sanitized timeout
   */
  executeDismissBasicAuth() {
    if (!this.browserCompatible) return;

    setTimeout(() => this._dismissBasicAuth(), this.timeout);

    sendExecutorResponse(this.requestStateObj, this.keyObject);
  }
}

module.exports = MacBasicAuthHandler;
