'use strict';

const HubLogger = require('../../../../log');
const constants = require('../../../../constants');
const bridge = require('../../../../bridge');
const responseHelper = require('../../customExecutorHelper');
const BasicAuthHandlerBase = require('../base/basicAuthHandlerBase');
const SeleniumClient = require('../../../../seleniumClient');
const supporting = require('../../../../supporting');
const typeSanity = require('../../../../typeSanity');

const LL = constants.LOG_LEVEL;

/* eslint no-underscore-dangle: ["error", { "allow": ["_sendBasicAuth", "_dismissBasicAuth", "_browserSanity", "_checkArguments" ] }] */
class IosBasicAuthHandler extends BasicAuthHandlerBase {
  constructor(keyObject, requestStateObj, parsedCommand, timeout) {
    super();
    this.keyObject = keyObject;
    this.requestStateObj = requestStateObj;
    this.parsedCommand = parsedCommand;
    this.timeout = timeout;
  }

  // executes basic auth with username and password
  _sendBasicAuth() {
    const { arguments: { username, password } = {} } = this.parsedCommand;
    const osVersion = parseFloat(this.keyObject.os_version);

    if (osVersion < 11) {
      this.ios10OrBelowBasicAuth(this.errorCallback.bind(this), this.successCallback.bind(this), username, password);
    } else {
      bridge.iOSBasicAuthHandler(this.keyObject, username, password, this.successCallback.bind(this));
    }
  }

  /**
   * As of now, we have only 2 devices for ios 10.
   * Ios 10 website shows phishing related alerts if website url contains basic auth.
   *
   * @param  errorCallback
   * @param  successCallback
   * @param  keyObject
   */
  async ios10OrBelowBasicAuth(errorCallback, successCallback, username, password) {
    const sel = new SeleniumClient(this.keyObject);

    let phishingPageShown = false;
    try {
      await sel.checkInsecureWebsite(constants.SAFARI_BASIC_AUTH_BELOW_IOS10_TITLE);
      phishingPageShown = true;
    } catch (_) {
      // nothing, phishingPageShown is already false
    }
    if (phishingPageShown) {
      try {
        await sel.executeScript({ script: 'PhishingAlertController.ignoreWarningSelected();', args: [] });
        supporting.waitForPageLoad(
          successCallback.bind(this),
          errorCallback.bind(this),
          this.keyObject,
          constants.PAGE_LOAD_DEFAULT_TIMEOUT
        );
        successCallback();
      } catch (err) {
        errorCallback(typeSanity.isNotUndefined(err) ? err.toString() : 'no error object');
      }
    } else {
      bridge.iOSBasicAuthHandler(this.keyObject, username, password, successCallback);
    }
  }

  /**
   * Entry point for executing basic auth on ios.
   * Executes the method with the sanitized timeout
   */
  executeBasicAuth() {
    this._checkArguments();

    setTimeout(() => this._sendBasicAuth(), this.timeout);

    responseHelper.sendExecutorResponse(this.requestStateObj, this.keyObject);
  }

  /**
   * Entry point for dismissing basic auth on ios. sends error response
   */
  executeDismissBasicAuth() {
    HubLogger.miscLogger(`[BasicAuthHandler] sessionId: ${this.requestStateObj.clientSessionID} dismiss auth called even if it is not implemented`, '', LL.INFO);
    responseHelper.sendError(this.keyObject, this.requestStateObj, 'Dismiss Basic Auth executor is not supported for iOS');
  }
}

module.exports = IosBasicAuthHandler;
