'use strict';

const constants = require('../../../../constants');
const bridge = require('../../../../bridge');
const { isString } = require('../../../../typeSanity');
const { sendExecutorResponse } = require('../../customExecutorHelper');
const BasicAuthHandlerBase = require('../base/basicAuthHandlerBase');

const AUTOIT_ESCAPE = ['!', '#', '+', '^', '{', '}'];

const escapeAutoItText = (text = '') => {
  let str = '';
  [...(text.toString())].forEach((val) => {
    str += AUTOIT_ESCAPE.indexOf(val) !== -1 ? `{${val}}` : val;
  });
  return str;
};

/**
 * Windows basic auth handler to perform the operations of
 * Handling and Dismissing of basic auth
 */
/* eslint no-underscore-dangle: ["error", { "allow": ["_sendBasicAuth", "_dismissBasicAuth", "_browserSanity", "_checkArguments" ] }] */
class WindowsBasicAuthHandler extends BasicAuthHandlerBase {
  constructor(keyObject, requestStateObj, parsedCommand, timeout) {
    super();
    this.keyObject = keyObject;
    this.requestStateObj = requestStateObj;
    this.parsedCommand = parsedCommand;
    this.timeout = timeout;
    this.browserCompatible = false;
    this._browserSanity(new RegExp(/chrome|firefox|edge|ie|explorer/i));
  }

  // executes basic auth with username and password
  _sendBasicAuth() {
    const { username, password } = this.parsedCommand.arguments;
    const autoitText = `${escapeAutoItText(username)}#-#{TAB}#-#${escapeAutoItText(password)}#-#{ENTER}#-#`;
    const redactedText = '[REDACTED]#-#{TAB}#-#[REDACTED]#-#{ENTER}#-#';

    bridge
      // eslint-disable-next-line max-len
      .autoItSendKeys(this.keyObject, this.requestStateObj, {
        timeout: constants.NODE_DIED_IN,
        redactedText,
      }, autoitText, () => {
        this.successCallback();
      }, () => {
        this.errorCallback('Error in executing');
      });
  }

  // dismisses basic auth popup
  _dismissBasicAuth() {
    let autoItText = '{ESC}#-#';
    if (isString(this.keyObject.browser) && (this.keyObject.browser.toLowerCase() === 'ie' || this.keyObject.browser.toLowerCase() === 'internet explorer')) {
      // Special handling for case of IE browser because a window security popup gets opened.
      if (this.keyObject.os === 'win7') {
        autoItText = '{TAB}#-#{TAB}#-#{TAB}#-#{TAB}#-#{ENTER}#-#';
      } else {
        autoItText = '{TAB}#-#{TAB}#-#{TAB}#-#{ENTER}#-#';
      }
    }
    bridge
      // eslint-disable-next-line max-len
      .autoItSendKeys(this.keyObject, this.requestStateObj, {
        timeout: constants.NODE_DIED_IN,
      }, autoItText, () => {
        this.successCallback();
      }, () => {
        this.errorCallback('Error in executing');
      });
  }

  /**
   * Entry point for executing basic auth on windows.
   * Executes the method with the sanitized timeout
   */
  executeBasicAuth() {
    if (!this.browserCompatible || !this._checkArguments()) return;

    setTimeout(() => this._sendBasicAuth(), this.timeout);

    sendExecutorResponse(this.requestStateObj, this.keyObject);
  }

  /**
   * Entry point for dismissing basic auth on windows.
   * Executes the method with the sanitized timeout
   */
  executeDismissBasicAuth() {
    if (!this.browserCompatible) return;

    setTimeout(() => this._dismissBasicAuth(), this.timeout);

    sendExecutorResponse(this.requestStateObj, this.keyObject);
  }
}

module.exports = WindowsBasicAuthHandler;
