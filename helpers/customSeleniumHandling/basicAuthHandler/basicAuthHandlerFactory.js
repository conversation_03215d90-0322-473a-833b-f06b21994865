const HubLogger = require('../../../log');
const constants = require('../../../constants');
const { instrumentAndSendError } = require('../customExecutorHelper');
const WindowsBasicAuthHandler = require('./handlers/windowsBasicAuthHandler');
const MacBasicAuthHandler = require('./handlers/macBasicAuthHandler');
const IosBasicAuthHandler = require('./handlers/iosBasicAuthHandler');
const NullHandler = require('./handlers/nullHandler');
const LL = constants.LOG_LEVEL;

const _HANDLER_MAPPING = {
  'mac': MacBasicAuth<PERSON>and<PERSON>,
  'win': WindowsBasicAuthHandler,
  'ios': IosBasicAuthHandler
}

/**
 * Factory class for generating basic auth handler.
 * Decides the platform based on the os. Does initial check of platform sanity
 */
class BasicAuthHandlerFactory {

  // Decide the handler based on the os
  static _decideHandler(keyObject, requestStateObj, parsedCommand) {
    const strippedOs = keyObject.os.match(/win|mac|ios/i);
    const { action } = parsedCommand;
    let errorString;

    let basicAuthHandler = _HANDLER_MAPPING[strippedOs];
    if (!basicAuthHandler) {
      basicAuthHandler = NullHandler;
      errorString = constants.JSE_BASIC_AUTH.unsupported_action.message.replace(constants.JSE_EXECUTERTYPE_REGEX, action);
      HubLogger.miscLogger(`[BasicAuthHandler] sessionId: ${requestStateObj.clientSessionID}, Error in platform sanity: ${errorString}`, "", LL.INFO);
      instrumentAndSendError(action, keyObject, requestStateObj, constants.JSE_BASIC_AUTH.unsupported_action);
    }
    return basicAuthHandler;
  }

  // Sanitizes the timeout passed and maintains it between 5s to 30s.
  static _sanitizeTimeout(parsedCommand, requestStateObj) {
    let { arguments: { timeout = 5000} = {} } = parsedCommand;
    try {
      timeout = Math.min(30000, Math.max(5000, parseInt(timeout)));
    } catch (e) {
      timeout = 5000;
    }
    HubLogger.miscLogger(`[BasicAuthHandler] sessionId: ${requestStateObj.clientSessionID}, Timeout set to: ${timeout}`, "", LL.INFO);
    return timeout;
  }

  // Factory method to generate the handler
  static getHandler(keyObject, requestStateObj, parsedCommand) {
    const timeout = BasicAuthHandlerFactory._sanitizeTimeout(parsedCommand, requestStateObj);
    const handler = BasicAuthHandlerFactory._decideHandler(keyObject, requestStateObj, parsedCommand);
    return new handler(keyObject, requestStateObj, parsedCommand, timeout);
  }
}

module.exports = BasicAuthHandlerFactory;
