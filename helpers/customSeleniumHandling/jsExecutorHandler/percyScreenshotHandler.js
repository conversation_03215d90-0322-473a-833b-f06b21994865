'use strict';

const HubLogger = require('../../../log');
const constants = require('../../../constants');
const pubSub = require('../../../pubSub');
const requestlib = require('../../../lib/request');
const browserstack = require('../../../browserstack');
const customExecutorHelper = require('../customExecutorHelper');
const { isUndefined } = require('../../../typeSanity');
const helper = require('../../../helper');
const CapabilitiesValidator = require('./percyCapabilityValidator');
const LL = constants.LOG_LEVEL;
const executorType = 'percy_screenshot_custom_executor';
const errors = constants.PERCY_SCREENSHOT_CUSTOM_EXECUTOR_ERRORS;
const getDeviceName = helper.getDeviceName;

// tag is used as log.kind in chitragupta but log.level is not indexed so we add 0/1 to tag
const TAG = 'PERCY SCREENSHOT 1';
const TAG_ERROR = 'PERCY SCREENSHOT 0';
const VALID_STATUSES = ['success', 'warning', 'failure'];
const VALID_SCREENSHOT_TYPES = ['singlepage', 'fullpage'];
const APP_PERCY_TEAM = 'app-percy';
const PERCY_PLATFORM_TEAM = 'percy-platform';

const getTeamName = keyObject => (keyObject.appTesting ? APP_PERCY_TEAM : PERCY_PLATFORM_TEAM);
const getDeviceSinatraPort = keyObject => (keyObject.os.indexOf('win') > -1 ? 4567 : 45671);
const zombieKind = (keyObject, kind) => {
  const appPercyKindPrefix = 'app_percy';
  const percyKindPrefix = 'percy_platform';
  const kindPrefix = keyObject.appTesting ? appPercyKindPrefix : percyKindPrefix;
  return `${kindPrefix}_${kind}`;
};
const excludedMetadataFunctionForFrameworks  = ['playwright']
const percyValidHostNames = ['percy.io', 'canary.percy.io', 'percy-enterprise.browserstack.com', 'staging.percy.dev', 'localhost']

function getProjectId(projectId) {
  let percyProjectId = 'percy-prod';
  if (!isUndefined(projectId)) {
    percyProjectId = projectId;
  }
  return percyProjectId;
}

async function getMetadata(keyObject) {
  const serverURL = `/percy/get_metadata?device=${encodeURIComponent(keyObject.device)}`;
  const body = Buffer.from(JSON.stringify({
    appium_session_id: keyObject.key,
    port: keyObject.port,
    device_id: keyObject.device,
  }));

  const headers = {
    accept: 'application/json',
    'content-type': 'application/json; charset=utf-8',
    'content-length': body.length,
    'accept-encoding': 'gzip;q=1.0,deflate;q=0.6,identity;q=0.3',
  };

  requestlib.appendBStackHostHeader(keyObject.name, headers);

  const termOptions = {
    method: 'POST',
    body,
    hostname: keyObject.rproxyHost,
    port: getDeviceSinatraPort(keyObject),
    timeout: 240 * 1000,
    path: serverURL,
    headers,
  };

  const current = Date.now();
  let elapsedTime = 0;
  const result = {};
  try {
    const { statusCode, data } = await requestlib.call(termOptions);
    elapsedTime = Date.now() - current;
    if (statusCode !== 200) {
      throw new Error(`Machine returned Non 200 status code: ${statusCode}`);
    }
    result.data = JSON.parse(data);
  } catch (e) {
    HubLogger.miscLogger(
      TAG_ERROR,
      `getMetadata: Calling failed ${e}`,
      LL.WARN
    );
    return {};
  }
  return result.data;
}

const comparisonCaps = async keyObject => ({
  browserName: keyObject.browser,
  browserVersion: keyObject.browser_version,
  os: keyObject.os,
  os_version: keyObject.os_version,
  deviceOrientation: keyObject.deviceOrientation,
});

const setupPercyOnAutomateTerminal = async (keyObject, percyProjectId) => {
  // To add an early exit if this was already called once.
  const serverURL = `/percy/setup_automate_session?device=${encodeURIComponent(keyObject.device)}`;
  const body = Buffer.from(JSON.stringify({
    certs: constants.PERCY_SCREENSHOT_UPLOADER_CERTS,
    project_id: percyProjectId,
    automation_session_id: keyObject.rails_session_id,
    hub_session_id: keyObject.key,
    group_id: keyObject.group_id,
  }));

  var headers = {
    "accept": "application/json",
    "content-type": "application/json; charset=utf-8",
    "content-length": body.length,
    "accept-encoding": "gzip;q=1.0,deflate;q=0.6,identity;q=0.3",
  };

  requestlib.appendBStackHostHeader(keyObject.name, headers);

  const termOptions = {
    method: 'POST',
    body,
    hostname: keyObject.rproxyHost,
    port: getDeviceSinatraPort(keyObject),
    timeout: 240 * 1000,
    path: serverURL,
    headers: headers,
  };

  let isError = false;
  let current = Date.now();
  let elapsedTime = 0;
  const result = {};
  try {
    const { statusCode, data } = await requestlib.call(termOptions);
    elapsedTime = Date.now() - current;
    if (statusCode !== 200) {
      isError = true;
      throw new Error(`Machine returned Non 200 status code: ${statusCode}`);
    }
    result.data = data;
  } catch (e) {
    HubLogger.miscLogger(
      TAG_ERROR,
      `setupPercyOnAutomateTerminal: Setup failed ${e}`,
      LL.WARN
    );
  }

  const zombieDataJson = {
    duration: elapsedTime,
    automation_session_id: keyObject.rails_session_id,
    machine: constants.osHostName,
    deviceName: getDeviceName(keyObject),
    reason: `setupPercyOnAutomateTerminal ${isError ? 'failed' : 'success'}`
  };

  const setUpKind = 'percy_platform_setup_poa_terminal';
  const dataToSend = {
    "session_id": keyObject.key,
    "kind": (isError ?  `${setUpKind}_0` : `${setUpKind}_1`),
    "data": zombieDataJson,
    "os": keyObject.os.toLowerCase(),
    "machine": constants.osHostName,
    "os_version": keyObject.os_version,
    "team": getTeamName(keyObject),
    "group_id": keyObject.group_id
  };

  sendDataToZombie(dataToSend);

  if(isError) {
    return false;
  }
  return true;
};

const percyScreenshotSuccessResponse = async (keyObject, requestStateObj, state, result = null, framework = null) => {
  const valueOptions = { success: true };

  if (state === 'begin') {
    valueOptions.deviceName = getDeviceName(keyObject);
    valueOptions.osVersion = keyObject.os_version;
    valueOptions.buildHash = keyObject.build_hash;
    valueOptions.sessionHash = keyObject.rails_session_id;
    if (!keyObject.appTesting) {
      valueOptions.capabilities = await comparisonCaps(keyObject);
    }
  } else if (state === 'screenshot') {
    valueOptions.result = result.data;
    /*
       In future we will deprecate this part of code as it is there only to support initial CLI versions released for POA.
       It was for getting window height for calculation of android and footer but later we shifted header footer calculation in
       terminal itself with different response structure.
    */
    if (
      !keyObject.appTesting &&
      keyObject.os.toLowerCase() === 'android' &&
      (!framework || !excludedMetadataFunctionForFrameworks.includes(framework))
    ) {
      valueOptions.metadata = await getMetadata(keyObject);
    }
  }

  // Making a request object since value is null in customExecutorHelper.sendExecutorResponse
  requestStateObj.hash = 'GET:value';
  requestStateObj.data = JSON.stringify({
    sessionId: requestStateObj.clientSessionID,
    status: 0,
    value: JSON.stringify(valueOptions),
  });

  customExecutorHelper.instrumentAndSendExecutorResponse(
    executorType,
    keyObject,
    requestStateObj,
    false
  );
};

const percyScreenshotBeginUpdateKeyobject = async (keyObject, beginTime) => {
  keyObject.percyBeginTime = beginTime;
  pubSub.publish(constants.updateKeyObject, {
    session: keyObject.rails_session_id,
    changed: {
      percyBeginTime: beginTime,
    },
  });
};

const markNumberOfTiles = async (keyObject, numOfTiles) => {
  keyObject.percyNumberOfTiles = numOfTiles;
  pubSub.publish(constants.updateKeyObject, {
    session: keyObject.rails_session_id,
    changed: {
      percyNumberOfTiles: numOfTiles,
    },
  });
};

const disableAnimations = async (keyObject) => {
  if (keyObject.os.toLowerCase() !== 'android') {
    return;
  }
  const serverURL = `/app_percy/disable_animation?device=${encodeURIComponent(keyObject.device)}`;
  const body = Buffer.from(JSON.stringify({
    appium_session_id: keyObject.key,
  }));

  var headers = {
    "accept": "application/json",
    "content-type": "application/json; charset=utf-8",
    "content-length": body.length,
    "accept-encoding": "gzip;q=1.0,deflate;q=0.6,identity;q=0.3",
  };

  requestlib.appendBStackHostHeader(keyObject.name, headers);

  const termOptions = {
    method: 'POST',
    body,
    hostname: keyObject.rproxyHost,
    port: getDeviceSinatraPort(keyObject),
    timeout: 240 * 1000,
    path: serverURL,
    headers: headers,
  };

  let isError = false;
  let current = Date.now();
  let elapsedTime = 0;
  const result = {};
  try {
    const { statusCode, data } = await requestlib.call(termOptions);
    elapsedTime = Date.now() - current;
    if (statusCode !== 200) {
      isError = true;
      throw new Error(`Machine returned Non 200 status code: ${statusCode}`);
    }
    result.data = data;
  } catch (e) {
    HubLogger.miscLogger(
      TAG_ERROR,
      `disableAnimations failed ${e}`,
      LL.WARN
    );
  }

  const zombieDataJson = {
    duration: elapsedTime,
    automation_session_id: keyObject.rails_session_id,
    machine: constants.osHostName,
    deviceName: getDeviceName(keyObject),
    reason: `disableAnimations ${isError ? 'failed' : 'success'}`
  };

  const kind = 'app_percy_disable_animation';
  const dataToSend = {
    "session_id": keyObject.key,
    "kind": (isError ?  `${kind}_0` : `${kind}_1`),
    "data": zombieDataJson,
    "os": keyObject.os.toLowerCase(),
    "machine": constants.osHostName,
    "os_version": keyObject.os_version,
    "team": getTeamName(keyObject)
  };

  sendDataToZombie(dataToSend);
};

const markAsPercy = async (keyObject, percyBuildId, percyBuildUrl) => {
  const params = {
    isAppAutomate: keyObject.appTesting,
    automation_session_id: keyObject.rails_session_id,
    user_id: keyObject.user_id,
    percy_build_id: percyBuildId,
    percy_build_url: percyBuildUrl,
  };

  const bsUrl = '&mark_as_percy=true';

  const response = await new Promise(resolve =>
    browserstack.postBrowserStack(bsUrl, params, null, null, resolve));

  HubLogger.miscLogger(
    TAG,
    `percyScreenshotBegin: mark_as_percy response ${JSON.stringify(response)}`,
    LL.INFO
  );

  const browserstackResponse = JSON.parse(response);
  if (browserstackResponse.success !== true) {
    HubLogger.miscLogger(
      TAG_ERROR,
      `percyScreenshotBegin: mark_as_percy failed ${JSON.stringify(response)}`,
      LL.WARN
    );
    return;
  }

  keyObject.markedAsPercy = true;
  pubSub.publish(constants.updateKeyObject, {
    session: keyObject.rails_session_id,
    changed: {
      markedAsPercy: true,
    },
  });
};

const percyScreenshotBegin = async (
  keyObject,
  requestStateObj,
  parsedCommand
) => {

  const current = Date.now();
  const zombieDataJson = {
    duration: 0,
    automation_session_id: keyObject.rails_session_id,
    deviceName: getDeviceName(keyObject)
  };
  const screenshotKind = zombieKind(keyObject, 'screenshot_begin');
  const dataToSend = {
    "session_id": keyObject.key,
    "kind": `${screenshotKind}_1`,
    "data": zombieDataJson,
    "os": keyObject.os.toLowerCase(),
    "machine": constants.osHostName,
    "os_version": keyObject.os_version,
    "team": getTeamName(keyObject)
  };

  if (!keyObject.appTesting) {
    try {
      const capabilityValidator = new CapabilitiesValidator(keyObject);
      capabilityValidator.validateBrowserOSVersions();
    } catch (error) {
      HubLogger.miscLogger(
        TAG_ERROR,
        `percyScreenshot: Not able to validate percy data ${error}`,
        LL.ERROR
      );
      // Need this errorMessage formatting, as this is how errorMessage is extracted afterwards.
      const errorMessage = errors.invalid_combination_passed;
      errorMessage.message = `[PERCY_UNSUPPORTED_COMBINATION_PASSED] ${error.message}`;

      customExecutorHelper.instrumentAndSendError(
        executorType,
        keyObject,
        requestStateObj,
        errorMessage
      );
      zombieDataJson.duration = Date.now() - current;
      dataToSend.kind = `${screenshotKind}_0`;
      sendDataToZombie(dataToSend);
      return;
    }
  }
  // Set Begin time. We will need to set it at every begin call.
  percyScreenshotBeginUpdateKeyobject(keyObject, Date.now());
  // Early return if session ( and so build ) is already marked, we only need to
  // do this once per session
  if (keyObject.markedAsPercy) {
    percyScreenshotSuccessResponse(keyObject, requestStateObj, 'begin');
    zombieDataJson.duration = Date.now() - current;
    sendDataToZombie(dataToSend);
    return;
  }

  // Mark current session as percy ( which will also mark build as percy )
  const { percyBuildId, percyBuildUrl, name, projectId } = parsedCommand.arguments;

  if (isUndefined(percyBuildId) || isUndefined(percyBuildUrl) || isUndefined(name) ||
    !isValidPercyUrl(percyBuildUrl)) {
    customExecutorHelper.instrumentAndSendError(
      executorType,
      keyObject,
      requestStateObj,
      errors.invalid_arg_passed
    );
    zombieDataJson.duration = Date.now() - current;
    dataToSend.kind = `${screenshotKind}_0`;
    sendDataToZombie(dataToSend);
    return;
  }

  const percyProjectId = getProjectId(projectId);

  // setting up terminal for automate
  if(!keyObject.appTesting) {
    const status = await setupPercyOnAutomateTerminal(keyObject, percyProjectId);
    if(!status) {
      customExecutorHelper.instrumentAndSendError(
        executorType,
        keyObject,
        requestStateObj,
        errors.unknown
      );
      zombieDataJson.duration = Date.now() - current;
      dataToSend.kind = `${screenshotKind}_0`;
      sendDataToZombie(dataToSend);
      return;
    }
  }

  // call async function to run in background, do not await it
  markAsPercy(keyObject, percyBuildId, percyBuildUrl);
  await disableAnimations(keyObject);

  percyScreenshotSuccessResponse(keyObject, requestStateObj, 'begin');

  zombieDataJson.duration = Date.now() - current;
  sendDataToZombie(dataToSend);
};

const percyScreenshotEnd = (keyObject, requestStateObj, parsedCommand) => {
  const {
    percyScreenshotUrl, name, status, statusMessage, sync
  } = parsedCommand.arguments;

  let isSync = sync;

  try {
    if ((isUndefined(percyScreenshotUrl) && status !== 'failure') ||
      isUndefined(name) || isUndefined(status) || !VALID_STATUSES.includes(status) ||
      (status !== 'success' && isUndefined(statusMessage))) {
      customExecutorHelper.instrumentAndSendError(
        executorType,
        keyObject,
        requestStateObj,
        errors.invalid_arg_passed
      );
      return;
    }

    if (isUndefined(sync)) {
      isSync = false;
    }

    percyScreenshotSuccessResponse(keyObject, requestStateObj, 'end');
  } finally {
    var isError = true;
    var elapsedTime = -1;
    if (keyObject.percyBeginTime !== -1) {
      isError = false;
      elapsedTime = Date.now() - keyObject.percyBeginTime;
    }

    const zombieDataJson = {
      duration: elapsedTime,
      automation_session_id: keyObject.rails_session_id,
      deviceName: getDeviceName(keyObject),
      tiles: keyObject.percyNumberOfTiles,
      sync: isSync
    };
    const screenshotKind = zombieKind(keyObject, 'screenshot_time');
    const dataToSend = {
      "session_id": keyObject.key,
      "kind": (isError ?  `${screenshotKind}_0` : `${screenshotKind}_1`),
      "data": zombieDataJson,
      "os": keyObject.os.toLowerCase(),
      "machine": constants.osHostName,
      "os_version": keyObject.os_version,
      "team": getTeamName(keyObject)
    };
    sendDataToZombie(dataToSend);
  }
};

const percyScreenshot = async (
  keyObject,
  requestStateObj,
  parsedCommand
) => {
  const { percyBuildId, screenshotType, scaleFactor, projectId, options, frameworkData, framework } = parsedCommand.arguments;

  if (isUndefined(percyBuildId) || isUndefined(screenshotType) || isUndefined(scaleFactor) ||
    isUndefined(options) || !VALID_SCREENSHOT_TYPES.includes(screenshotType)) {
    customExecutorHelper.instrumentAndSendError(
      executorType,
      keyObject,
      requestStateObj,
      errors.invalid_arg_passed
    );
    return;
  }

  const percyProjectId = getProjectId(projectId);

  const appPercyPrefix = '/app_percy';
  const percyPrefix = '/percy';
  const screenshotPath = `/screenshot?device=${encodeURIComponent(keyObject.device)}`;
  const serverURL = (keyObject.appTesting ? appPercyPrefix : percyPrefix) + screenshotPath;
  const payload = {
    appium_session_id: keyObject.key,
    automation_session_id: keyObject.rails_session_id,
    hub_session_id: keyObject.key,
    build_id: percyBuildId,
    project_id: percyProjectId,
    screenshot_type: screenshotType,
    scale_factor: scaleFactor,
    port: keyObject.port,
    framework_data: frameworkData,
    framework: framework,
    options
  };

  if (!keyObject.appTesting) {
    payload.sessionInfo = {
      browser_name: keyObject.browser,
      browser_version: keyObject.browser_version,
      os_version: keyObject.os_version,
      device: getDeviceName(keyObject)
    };
  }

  const body = Buffer.from(JSON.stringify(payload));

  var headers = {
    "accept": "application/json",
    "content-type": "application/json; charset=utf-8",
    "content-length": body.length,
    "accept-encoding": "gzip;q=1.0,deflate;q=0.6,identity;q=0.3",
  };

  requestlib.appendBStackHostHeader(keyObject.name, headers);

  const termOptions = {
    method: 'POST',
    body,
    hostname: keyObject.rproxyHost,
    port: getDeviceSinatraPort(keyObject),
    timeout: 360 * 1000,
    path: serverURL,
    headers,
  };

  let isError = false;
  let current = Date.now();
  let elapsedTime = 0;
  const result = {};
  try {
    const { statusCode, data } = await requestlib.call(termOptions);
    elapsedTime = Date.now() - current;
    if (statusCode != 200) {
      isError = true;
      // sending beginTime as -1, which signifies that POA session has failed
      percyScreenshotBeginUpdateKeyobject(keyObject, -1);
      throw new Error(`Machine returned Non 200 status code: ${statusCode}`);
    }
    let numOfTiles = -1;
    try {
      // data coming from mobile side is in string format
      // For single page return 0, so that it is to distinguis between fullpage and singlepage
      if (screenshotType === 'singlepage') {
        numOfTiles = 0;
      } else {
        numOfTiles = JSON.parse(data).length;
      }
    } catch (err) {
      // If any parsing error then sdk will also face parsing error.
      isError = true;
      HubLogger.miscLogger(
        TAG_ERROR,
        `percyScreenshot: Not able to parse data ${err}`,
        LL.WARN
      );
    }
    markNumberOfTiles(keyObject, numOfTiles);
    result.data = data;
  } catch (e) {
    HubLogger.miscLogger(
      TAG_ERROR,
      `percyScreenshot: Snapshot failed ${e}`,
      LL.WARN
    );
  }

  const zombieDataJson = {
    duration: elapsedTime,
    automation_session_id: keyObject.rails_session_id,
    deviceName: getDeviceName(keyObject),
    screenshotType,
    percyBuildId,
    framework
  };

  const screenshotKind = zombieKind(keyObject, 'percy_screenshot');
  const dataToSend = {
    "session_id": keyObject.key,
    "kind": (isError ?  `${screenshotKind}_0` : `${screenshotKind}_1`),
    "data": zombieDataJson,
    "os": keyObject.os.toLowerCase(),
    "machine": constants.osHostName,
    "os_version": keyObject.os_version,
    "team": getTeamName(keyObject)
  };

  sendDataToZombie(dataToSend);

  if(isError) {
    customExecutorHelper.instrumentAndSendError(
      executorType,
      keyObject,
      requestStateObj,
      errors.unknown
    );
    return;
  }
  percyScreenshotSuccessResponse(keyObject, requestStateObj, 'screenshot', result, framework);
};

const percyScreenshotHandler = (keyObject, requestStateObj, parsedCommand) => {
  // Default {} to avoid exception if users dont pass arguments key
  const { state } = parsedCommand.arguments || {};

  if (isUndefined(state) || !(state === 'begin' || state === 'end' || state === 'screenshot')) {
    customExecutorHelper.instrumentAndSendError(
      executorType,
      keyObject,
      requestStateObj,
      errors.invalid_arg_passed
    );
    return;
  }

  if (state === 'begin') {
    percyScreenshotBegin(keyObject, requestStateObj, parsedCommand);
  } else if (state === 'end') {
    percyScreenshotEnd(keyObject, requestStateObj, parsedCommand);
  } else {
    percyScreenshot(keyObject, requestStateObj, parsedCommand);
  }
};

const isValidHostName = (hostname) => {
  return !!hostname && percyValidHostNames.includes(hostname)
}

const isValidPercyUrl = (string) => {
  try {
    const url = new URL(string);
    if (isValidHostName(url.hostname)) {
      return true;
    }
  } catch (err) {
    HubLogger.miscLogger(
      TAG_ERROR,
      `percyScreenshot: Invalid Percy build URL: ${string}`,
      LL.WARN
    );
  }
  return false;
};

const sendDataToZombie = (dataToSend) => {
  try {
    helper.PingZombie(dataToSend);
  } catch (e) {
    HubLogger.miscLogger(
      TAG_ERROR,
      `percyScreenshot: Sending data to zombies failed. ${JSON.stringify(dataToSend)}`,
      LL.WARN
    );
  }
};

module.exports = { percyScreenshotHandler };
