'use strict';

const HubLogger = require('../../../log');
const constants = require('../../../constants');
const requestlib = require('../../../lib/request');
const customExecutorHelper = require('../customExecutorHelper');
const { isUndefined } = require('../../../typeSanity');
const helper = require('../../../helper');
const querystring = require('querystring');

const LL = constants.LOG_LEVEL;
const errors = constants.APP_ALLY_CUSTOM_EXECUTOR_ERRORS;
const executorType = 'app_ally_custom_executor';

const TAG = 'APPALLY-CUSTOM-EXECUTOR';
const TAG_ERROR = 'APPALLY-CUSTOM-EXECUTOR-ERROR';

const appAllyScreenshotHandler = async (keyObject, requestStateObj, parsedCommand) => {
  const { thTestRunUuid, thBuildUuid, thJwtToken, method, authHeader, scanTimestamp } = parsedCommand.arguments;
  if (isUndefined(thBuildUuid) || isUndefined(thJwtToken) || isUndefined(authHeader) || isUndefined(scanTimestamp)) {
    customExecutorHelper.instrumentAndSendError(
      executorType,
      keyObject,
      requestStateObj,
      errors.invalid_arg_passed
    );
    return;
  }

  if (!keyObject.appTesting) {
    customExecutorHelper.instrumentAndSendError(
      executorType,
      keyObject,
      requestStateObj,
      errors.invalid_combination_passed
    );
    return;
  }

  appAllyScan(keyObject, requestStateObj, parsedCommand);
};

const appAllyScan = async (keyObject, requestStateObj, parsedCommand) => {
  const { thTestRunUuid, thBuildUuid, thJwtToken, method, authHeader, scanTimestamp } = parsedCommand.arguments;
  
  const queryArgs = {
    test_uuid: thTestRunUuid,
    build_uuid: thBuildUuid,
    th_auth_token: thJwtToken,
    command: method,
    auth_token: authHeader,
    async_mode: true,
    s3_access_key: constants.APP_ALLY_AWS_KEYS,
    s3_secret_key: constants.APP_ALLY_AWS_SECRET,
    s3_bucket: constants.APP_ALLY_AWS_BUCKET,
    region: constants.APP_ALLY_AWS_REGION,
    scan_timestamp: scanTimestamp,
    device: keyObject.device,
    session_id: keyObject.rails_session_id
  }
  const endpointForTerminal = `/accessibility_info?${querystring.encode(queryArgs)}`;
  const headers = {
    accept: 'application/json'
  };
  const options = {
    method: 'GET',
    hostname: keyObject.rproxyHost,
    port: helper.getPortForOS(keyObject),
    path: endpointForTerminal,
    headers: requestlib.appendBStackHostHeader(keyObject.name, headers),
    timeout: constants.JS_EXECUTOR_TIMEOUT
  };

  HubLogger.newCGLogger(TAG, `Making appAllyScan request: ${options}`, LL.DEBUG, keyObject.rails_session_id);

  let result = {};
  try {
    const { statusCode, data } = await requestlib.call(options);
    if (statusCode !== 200) {
      throw new Error(`Terminal returned Non 200 status code: ${statusCode} and data ${data}`);
    }
    result = data;
  } catch (e) {
    HubLogger.newCGLogger(TAG_ERROR, `Error in appAllyScan: ${e}`, LL.ERROR, keyObject.rails_session_id);
    result = {
      "success": false,
      "errors": [
        {
          key: 'appAllyScanError',
          message: e.message
        }
      ]
    }
  }
  requestStateObj.hash = 'GET:value';
  requestStateObj.data = JSON.stringify({
    sessionId: requestStateObj.clientSessionID,
    status: 0,
    value: JSON.stringify(result),
  });
  customExecutorHelper.instrumentAndSendExecutorResponse(
    executorType,
    keyObject,
    requestStateObj,
    false
  );
  return;
}

module.exports = { appAllyScreenshotHandler };
