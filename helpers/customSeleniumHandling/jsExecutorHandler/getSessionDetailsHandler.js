'use strict';

const RestAPIHandler = require("../restAPIHandler");
const HubLogger = require("../../../log");
const constants = require("../../../constants");
const customExecutorHelper = require("../customExecutorHelper");
const LL = constants.LOG_LEVEL;

const runGetSessionDetailsHandler = ( keyObject, requestStateObj, parsedCommand ) => {
  runGetSessionDetailsExecutor(keyObject, requestStateObj, parsedCommand);
};

async function runGetSessionDetailsExecutor(keyObject, requestStateObj, parsedCommand) {
  const apiHandler = new RestAPIHandler(keyObject.rails_session_id, keyObject.appTesting);
  const executorType = parsedCommand.action;
  const body = JSON.stringify(parsedCommand.arguments);
  const headers = RestAPIHandler.DEFAULT_HEADERS;
  headers.Authorization = constants.AUTH_PREFIX + Buffer.from(`${keyObject.user}:${keyObject.accesskey}`).toString('base64');
  try {
    const response = await apiHandler.makeRequest(constants.GET, `/${apiHandler.product}/sessions/${apiHandler.sessionId}.json`, body, headers);
    const { data, statusCode } = response;

    HubLogger.miscLogger(apiHandler.tag, `runGetSessionDetailsExecutor for ${keyObject.rails_session_id}`, LL.DEBUG, keyObject.debugSession);

    const seleniumStatus = statusCode === 200 ? constants.SELENIUM_SUCCESS_CODE : constants.SELENIUM_ERROR_CODE;

    // Making a request object since value is null in customExecutorHelper.sendExecutorResponse
    requestStateObj.hash = 'GET:value';
    requestStateObj.data = JSON.stringify({
      sessionId: requestStateObj.clientSessionID,
      status: seleniumStatus,
      value: JSON.stringify(JSON.parse(data).automation_session),
    });
    customExecutorHelper.instrumentAndSendExecutorResponse(executorType, keyObject, requestStateObj, false);
  } catch (e) {
    HubLogger.exceptionLogger(
      `${apiHandler.tag} runGetSessionDetailsExecutor for ${apiHandler.product} in session ${keyObject.rails_session_id}`,
      e.toString()
    );
    customExecutorHelper.instrumentAndSendError(executorType, keyObject, requestStateObj, constants.JSE_SESSION_STATUS.generic_error);
  }
}
module.exports = { runGetSessionDetailsHandler };
