const fs = require('fs');
const path = require('path');
const HubLogger = require('../../../log');
const constants = require('../../../constants');
const semver = require('semver');
const { getDeviceName } = require('../../../helper');

class CapabilitiesValidator {
  constructor(keyObject) {
    this.keyObject = keyObject;
  }

  TAG = 'PERCY SCREENSHOT 1';
  TAG_ERROR = 'PERCY SCREENSHOT 0';
  LL = constants.LOG_LEVEL;
  ERROR_LOG_LEVEL = 0
  WARN_LOG_LEVEL = 1

  log(message, errorLevel = this.ERROR_LOG_LEVEL) {
    let tag = this.TAG_ERROR
    let level = this.LL.ERROR
    if (errorLevel === this.WARN_LOG_LEVEL) {
      level = this.LL.WARN
    }
    HubLogger.miscLogger(
      tag,
      message,
      level
    );
  }

  existsInArray(array, value) {
    if (!array || array.length === 0) return false;
    return array.some((element) => {
      return element === value;
    });
  }

  getKeyIfPresent(key, obj = {}) {
    for (const k in obj) if (k.toLowerCase() === key.toLowerCase()) return obj[k];
    return false;
  }

  validateBrowserOSVersions() {
    const excludeBrowserData = JSON.parse(
      fs.readFileSync(
        path.join(constants.ROOT_PATH, 'config/percy_excluded_browsers.json')
      )
    );

    let supportedSeleniumVersion = excludeBrowserData.selenium_versions.min_version
    let supportedAppiumVersion = excludeBrowserData.appium_versions.min_version
    
    
    if (this.keyObject.selenium_version && !semver.satisfies(this.keyObject.selenium_version, `>=${supportedSeleniumVersion}`)) {
      this.log(`Selenium Version: ${this.keyObject.selenium_version} used is less than supported`);
      throw new Error(`Selenium Version: ${this.keyObject.selenium_version} used is less than supported. Refer: https://www.browserstack.com/docs/percy/integrate/functional-and-visual#prerequisite`);
    }

    if (this.keyObject.appium_version && !semver.satisfies(this.keyObject.appium_version, `>=${supportedAppiumVersion}`)) {
      this.log(`Appium Version: ${this.keyObject.appium_version} used is less than supported`);
      throw new Error(`Appium Version: ${this.keyObject.appium_version} used is less than supported`);
    }

    if(!this.keyObject.platformDetails){
      this.log('Platform Details are not provided')
      throw new Error('Platform Details are not provided')
    }

    let { os, os_version, browser, browser_version } = this.keyObject;
    let platformName = this.keyObject.platformDetails.platformName
    this.log(`${os}:${os_version}:${browser}:${browser_version}`);
    if (!os || !os_version || !browser || !browser_version) {
      this.log('OS/Browser Combination is not supported in Percy');
      throw new Error('OS/Browser Combination is not supported in Percy');
    }

    let deviceName = getDeviceName(this.keyObject);
    const isMobile = ['ios', 'android'].includes(os.toLowerCase());
    if (isMobile && !deviceName) {
      this.log('Device capabilities are incorrect');
      throw new Error('Device capabilities are incorrect');
    }

    const excludedOSData = this.getKeyIfPresent(platformName, excludeBrowserData.os)
    if (excludedOSData) {
      if (excludedOSData.os_versions && this.existsInArray(excludedOSData.os_versions, os_version)) {
        this.log(`OS version: ${os_version} is not supported in Percy`);
        throw new Error(`OS version: ${os_version} is not supported in Percy`);
      }

      if (excludedOSData.device_names && this.existsInArray(excludedOSData.device_names, deviceName)) {
        this.log(`${deviceName} is not supported in Percy`);
        throw new Error(`${deviceName} is not supported in Percy`);
      }

      if (excludedOSData.browsers) {
        const browserData = this.getKeyIfPresent(browser, excludedOSData.browsers)
        if (!browserData) return;

        if (browserData.min_version === 'all') {
          this.log(`Browser: ${browser} is not supported in Percy`);
          throw new Error(`Browser: ${browser} is not supported in Percy`);
        } else if (parseInt(browser_version, 10) < parseInt(browserData.min_version, 10)) {
          this.log(`Browser: ${browser} ${browser_version} is not supported in Percy on ${os}.`);
          throw new Error(`Browser: ${browser} ${browser_version} is not supported in Percy on ${os}. Refer: https://www.browserstack.com/docs/percy/integrate/functional-and-visual#limitations`);
        }
      }
    }
  }
}

module.exports = CapabilitiesValidator;
