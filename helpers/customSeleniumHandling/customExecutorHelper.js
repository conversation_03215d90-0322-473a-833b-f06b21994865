'use strict';

const bridge = require('../../bridge');
const helper = require('../../helper');
const constants = require('../../constants');
const requestlib = require('../../lib/request');
const HubLogger = require('../../log');

const LL = constants.LOG_LEVEL;
const setInstrumentationData = require('./instrumentExecutor').setInstrumentationData;

const sendError = (keyObject, requestStateObj, errorReason, instrumentOnEDS = true) => {
  const errorMessage = `Error executing browserstack_executor command. ${errorReason}`;
  const edsKind = (keyObject.appTesting ? constants.EDS_KIND_APP_AUTOMATION_SESSION_STATS :
    constants.EDS_KIND_AUTOMATION_SESSION_STATS);
  requestStateObj.hash = 'POST:value';

  let value = errorMessage;

  if (helper.isW3C(keyObject)) {
    requestStateObj.output = { headers: constants.CHUNKED_HEADER, statusCode: 500 };
    value = { error: errorMessage, message: errorMessage };
  }

  requestStateObj.data = JSON.stringify({
    sessionId: requestStateObj.clientSessionID,
    status: constants.SELENIUM_ERROR_CODE,
    value,
  });

  /* `instrumentOnEDS` is a temporary flag added and will be removed once we move completely to
   `instrumentAndSendError` for all custom executors. */
  if (instrumentOnEDS) {
    helper.sendToEDS({
      sessionid: requestStateObj.clientSessionID,
      feature_usage: { custom_executor: { message: errorReason, success: false } },
      kind: `${edsKind}`,
    });
  }
  bridge.sendResponse(keyObject, requestStateObj);
};

const instrumentAndSendError = (executorType, keyObject, requestStateObj, error) => {
  setInstrumentationData(keyObject, executorType, 'error', error.code);
  const errorMessage = error.message.replace(constants.JSE_EXECUTERTYPE_REGEX, executorType);
  sendError(keyObject, requestStateObj, errorMessage, false);
};

const sendExecutorResponse = (requestStateObj, keyObject, defaultFormat = true, hash = 'POST:keys', output = null) => {
  if (defaultFormat) {
    requestStateObj.hash = hash;
    requestStateObj.data = JSON.stringify({
      sessionId: requestStateObj.clientSessionID,
      status: 0,
      value: output,
    });
  }
  bridge.sendResponse(keyObject, requestStateObj);
};

const instrumentAndSendExecutorResponse = (executorType, keyObject, requestStateObj, generateDefaultResponseHashAndData = true, hash = 'POST:keys', output = null) => {
  setInstrumentationData(keyObject, executorType, 'success');
  sendExecutorResponse(requestStateObj, keyObject, generateDefaultResponseHashAndData, hash, output);
};

const instrumentExecutorStatusAndSendResponse = (executorStatus, errorString, executorType, keyObject, requestStateObj, generateDefaultResponseHashAndData = true, hash = 'POST:keys', output = null) => {
  switch (executorStatus) {
    case 'success':
      setInstrumentationData(keyObject, executorType, 'success');
      break;
    case 'error':
      setInstrumentationData(keyObject, executorType, 'error', errorString);
      break;
    default:
      break;
  }
  sendExecutorResponse(requestStateObj, keyObject, generateDefaultResponseHashAndData, hash, output);
};

const sendRequestToPlatform = async (executorType, serverURL, requestStateObj, keyObject, errorMessage, customTimeout, customOptions, responseHandler, callback) => {
  const customHeader = customOptions && customOptions.headers ? customOptions.headers : {};
  if (customOptions) delete customOptions.headers;
  const termOptions = {
    hostname: requestStateObj.rproxyHost,
    port: 45671,
    path: serverURL,
    timeout: customTimeout,
    headers: requestlib.appendBStackHostHeader(requestStateObj.hostname, customHeader),
  };

  if (customOptions) Object.assign(termOptions, customOptions);

  HubLogger.miscLogger('CUSTOM BROWSERSTACK EXECUTOR', `Sending request ${serverURL} to platform session: ${keyObject.rails_session_id} device : ${keyObject.device}`, LL.DEBUG, keyObject.debugSession);

  try {
    const response = await requestlib.call(termOptions);
    HubLogger.miscLogger('CUSTOM BROWSERSTACK EXECUTOR', `Response from platform is ${JSON.stringify(response)}`, LL.DEBUG, keyObject.debugSession);

    if (responseHandler) {
      responseHandler(response);
    } else if (response.statusCode === 200) {
      instrumentAndSendExecutorResponse(executorType, keyObject, requestStateObj);
      return;
    } else {
      HubLogger.tempExceptionLogger(`Non 200 response from platform for session: ${keyObject.rails_session_id}`);
      instrumentAndSendError(executorType, keyObject, requestStateObj, errorMessage);
      return;
    }
  } catch (err) {
    HubLogger.tempExceptionLogger('Error in sendRequestToPlatform from customExecutorHelper.js', err);
    instrumentAndSendError(executorType, keyObject, requestStateObj, errorMessage);
  } finally {
    if (callback) callback();
  }
};

const checkFileUploadExecutor = (keyObject) => {
  if (!/win/.test(keyObject.os) || ['winxp', 'win7'].includes(keyObject.os)) {
    return constants.JSE_FILE_EXECUTORS_GENERIC.supports_only_windows;
  } else if (!/edge|chrome/.test(keyObject.browser.toLowerCase())) {
    return constants.JSE_FILE_EXECUTORS_GENERIC.supports_only_chromium;
  }
  return undefined;
};

module.exports = {
  sendError,
  sendExecutorResponse,
  sendRequestToPlatform,
  instrumentAndSendError,
  instrumentAndSendExecutorResponse,
  instrumentExecutorStatusAndSendResponse,
  checkFileUploadExecutor,
};
