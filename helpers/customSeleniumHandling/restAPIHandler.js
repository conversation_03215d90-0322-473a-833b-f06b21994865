'use strict';

const requestLib = require('../../lib/request');
const HubLogger = require('../../log');
const constants = require('../../constants');
const customExecutorHelper = require('./customExecutorHelper');
const Qig = require('../qig');

const LL = constants.LOG_LEVEL;

const TAG = 'RESTAPI_EXECUTOR';
class RestAPIHandler {
  /**
   * Constructor
   * @param {string} host
   * @param {number} port
   * @param {string} sessionId
   */
  constructor(sessionId, isAppTesting) {
    this.host = (isAppTesting ? constants.APP_AUTOMATE_BSTACK_API_HOST : constants.BSTACK_API_HOST);
    this.product = (isAppTesting ? 'app-automate' : 'automate');
    this.edsKind = (isAppTesting ? constants.EDS_KIND_APP_AUTOMATION_SESSION_STATS :
      constants.EDS_KIND_AUTOMATION_SESSION_STATS);
    this.port = constants.BSTACK_API_PORT;
    this.retries = constants.BSTACK_API_RETRIES;
    this.sessionId = sessionId;
    this.tag = TAG;
  }

  static get DEFAULT_HEADERS() {
    return {
      accept: 'application/json',
      'content-type': 'application/json;',
      'user-agent': 'hub.browserstack.com',
    };
  }

  /**
   * Method to be used by custom executors for rest API calls.
   * @param {string} method http verb for request
   * @param {string} path end-point for request
   * @param {string} body body to be sent in request
   * @param {Object} headers for request
   */
  async makeRequest(method, path, body, headers) {
    const options = {
      method,
      path,
      body,
      headers,
      hostname: this.host,
      port: this.port,
      timeout: constants.BS_TIMEOUT,
      scheme: constants.BS_SCHEME,
      delay: 10000,
    };
    return requestLib.call(options, this.retries);
  }

  /**
   * Method to update session name or status (passed/failed) using custom executor
   * @param {Object} keyObject contains vital information like accesskey and username
   * @param {Object} requestStateObj
   * @param {Object} parsedCommand JSON received from custom executor request as arguments
   */
  async runUpdateSessionStateExecutor(keyObject, requestStateObj, parsedCommand) {
    const executorType = parsedCommand.action;
    const body = JSON.stringify(parsedCommand.arguments);
    const headers = RestAPIHandler.DEFAULT_HEADERS;
    headers.Authorization = constants.AUTH_PREFIX + Buffer.from(`${keyObject.user}:${keyObject.accesskey}`).toString('base64');
    try {
      const response = await this.makeRequest(constants.PUT, `/${this.product}/sessions/${this.sessionId}.json`, body, headers);
      const { data, statusCode } = response;
      if (
        statusCode === 200 &&
        parsedCommand.arguments &&
        parsedCommand.arguments.status
      ) {
        Qig.markSessionStatusExecutor(keyObject);
      }
      HubLogger.miscLogger(TAG, `runUpdateSessionStateExecutor for ${keyObject.rails_session_id}`, LL.DEBUG, keyObject.debugSession);

      const seleniumStatus = statusCode === 200 ? constants.SELENIUM_SUCCESS_CODE : constants.SELENIUM_ERROR_CODE;

      // Making a request object since value is null in customExecutorHelper.sendExecutorResponse
      requestStateObj.hash = 'POST:value';
      requestStateObj.data = JSON.stringify({
        sessionId: requestStateObj.clientSessionID,
        status: seleniumStatus,
        value: JSON.stringify(JSON.parse(data)),
      });
      customExecutorHelper.instrumentAndSendExecutorResponse(executorType, keyObject, requestStateObj, false);
    } catch (e) {
      HubLogger.exceptionLogger(`${TAG} runUpdateSessionStateExecutor for ${this.product} in session ${keyObject.rails_session_id}`, e.toString());
      customExecutorHelper.instrumentAndSendError(executorType, keyObject, requestStateObj, constants.JSE_SESSION_STATUS.generic_error);
    }
  }
}

module.exports = RestAPIHandler;
