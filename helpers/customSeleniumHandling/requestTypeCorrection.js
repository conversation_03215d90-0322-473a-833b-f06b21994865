'use strict';

const {
  genSendValueURL,
  genFindElementURL,
  genAssertMatchURL,
  genOpenURL,
  genClickURL,
  genPressURL,
  genGetCookies,
  genTouch,
  genSetGeolocation,
  genMouseMove,
  genTakeScreenshot,
  genSetFile,
  genNewContext,
  genFocusElement,
  genEvaluateExpression,
  genMouseReleasedURL,
  genMousePressedURL,
  genMouseMovedURL
} = require('./urlModifier');
const {
  TYPE_TEXT,
  FIND_ELEMENT,
  ASSERT_ELEMENT,
  NAVIGATE_TO,
  KEY_PRESS,
  CLICK,
  SET_GEOLOCATION,
  CAPTURE_SCREENSHOT,
  GET_COOKIES,
  TOUCH,
  MOVE_MOUSE,
  SET_FILE,
  NEW_CONTEXT,
  FOCUS,
  EVALUATE_EXPRESSION,
  MOUSE_MOVED,
  MOUSE_PRESSED,
  MOUSE_RELEASED
} = require('./frameworkConstants');

const getCorrespondingCommandAndURL = (commandType, url) => {
  switch (commandType) {
    case MOUSE_RELEASED:
      return {
        url: genMouseReleasedURL(url),
        command: 'POST:released'
      };
    case MOUSE_PRESSED:
      return {
        url: genMousePressedURL(url),
        command: 'POST:pressed'
      };
    case MOUSE_MOVED:
      return {
        url: genMouseMovedURL(url),
        command: 'POST:moved'
      };
    case EVALUATE_EXPRESSION:
      return {
        url: genEvaluateExpression(url),
        command: 'POST:expression'
      };
    case FOCUS:
      return {
        url: genFocusElement(url),
        command: 'GET:focus'
      };
    case TYPE_TEXT:
      return {
        url: genSendValueURL(url),
        command: 'POST:value',
      };
    case FIND_ELEMENT:
      return {
        url: genFindElementURL(url),
        command: 'POST:element',
      };
    case ASSERT_ELEMENT:
      return {
        url: genAssertMatchURL(url),
        command: 'POST:assertion',
      };
    case NAVIGATE_TO:
      return {
        url: genOpenURL(url),
        command: 'POST:url',
      };
    case KEY_PRESS:
      return {
        url: genPressURL(url),
        command: 'POST:press',
      };
    case NEW_CONTEXT:
      return {
        url: genNewContext(url),
        command: 'POST:newcontext'
      };
    case CLICK:
      return {
        url: genClickURL(url),
        command: 'POST:click',
      };
    case GET_COOKIES:
      return {
        url: genGetCookies(url),
        command: 'GET:cookie',
      };
    case TOUCH:
      return {
        url: genTouch(url),
        command: 'POST:touch',
      };
    case MOVE_MOUSE:
      return {
        url: genMouseMove(url),
        command: 'POST:mouse',
      };
    case SET_FILE:
      return {
        url: genSetFile(url),
        command: 'POST:file'
      };
    case SET_GEOLOCATION:
      return {
        url: genSetGeolocation(url),
        command: 'POST:geolocation',
      };
    case CAPTURE_SCREENSHOT:
      return {
        url: genTakeScreenshot(url),
        command: 'GET:screenshot',
      };
    default:
      return {
        url,
        command: 'POST:execute',
      };
  }
};

const URL_KEYS = ['originalUrl', 'url'];
const getURLKey = requestStateObject =>
  URL_KEYS.filter(key => key in requestStateObject).shift();

const requestTypeCorrection = (requestStateObj, commandType) => {
  const urlKey = getURLKey(requestStateObj);
  const {
    command: correspondingCommand,
    url: modifiedURL,
  } = getCorrespondingCommandAndURL(commandType, requestStateObj[urlKey]);
  requestStateObj[urlKey] = modifiedURL;
  requestStateObj.hash = correspondingCommand;
};

module.exports = requestTypeCorrection;
