'use strict';

const constants = require('../constants');
const helper = require('../helper');

const checkNetworkCommands = (keyObject) => {
  const lastOpenedUrl = helper.getSanitizedStringForZombie(keyObject.lastOpenedUrl);
  return constants.NETWORK_STAGING_URL_REGEX.some(regex => regex.test(lastOpenedUrl)) ? constants.SO_TIMEOUT_BUCKET.stagingWebsite : '';
};

// eslint-disable-next-line no-nested-ternary
const checkBasicAuthCommand = keyObject => (constants.BASIC_AUTH_URL_REGEX.some(regex => regex.test(keyObject.lastOpenedUrl)) ? keyObject.browser && keyObject.browser.toLowerCase() === 'safari' ? constants.SO_TIMEOUT_BUCKET.basicAuthSafari : constants.SO_TIMEOUT_BUCKET.basicAuth : '');

/*
 * Array of last commands checks -
 * name - general description
 * hashIn - list to check for lash command hash
 * setValue - set bucket directly to if exists, in case if we can set directly
 * callFunction - call to check if falls in bucket, only in case if setValue doesn't exists
 */
const COMMAND_MAPPINGS = [
  {
    name: 'basicAuth',
    hashIn: constants.BASIC_AUTH_COMMANDS,
    callFunction: checkBasicAuthCommand,
  },
  {
    name: 'deleteWindow',
    hashIn: constants.DELETE_WINDOW_COMMANDS,
    setValue: constants.SO_TIMEOUT_BUCKET.deleteWindow,
  },
  {
    name: 'nonNetworkCommand',
    hashIn: constants.PURE_NON_NETWORK_COMMANDS,
    setValue: constants.SO_TIMEOUT_BUCKET.nonNetworkCommand,
  },
  {
    name: 'stagingWebsite',
    hashIn: constants.PURE_NETWORK_COMMANDS,
    callFunction: checkNetworkCommands,
  },
];

/*
 * This helper is to bucket so_timeout sessions based on certain categories
 * (user staging website, non-network command etc).
 * Useful for a preliminary diagnosis only when none of the existing instrumentation
 * i.e. active_window_bucket or secondary_diagnostic_reason is populated
*/
const getSoTimeoutBucket = (sessionKeyObj) => {
  const buckets = [];
  const hash = sessionKeyObj.lastRequestDetails.split('::')[0];
  COMMAND_MAPPINGS.forEach((command) => {
    if (command.hashIn.includes(hash)) {
      const bucket = command.setValue ?
        command.setValue :
        command.callFunction.call(null, sessionKeyObj);
      if (bucket.trim()) buckets.push(bucket);
    }
  });
  return buckets;
};

module.exports = {
  getSoTimeoutBucket,
};
