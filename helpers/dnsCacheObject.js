'use strict';

const next = require('asap');
const constants = require('../constants');
const hoothoot = require('hoothoot')(constants.metrics_region);
const typeSanity = require('../typeSanity');

let hoothootStats = { cacheSize: 0 };

/* WIP: Move hoothoot functions inside a class
        and export it so that the methods can be
        individually tested.
*/
const createStats = (methodName) => {
  hoothootStats[methodName] = {};
  // per function error and error type counter
  hoothootStats[methodName].errors = {};
  // computed property hit + softMiss
  hoothootStats[methodName].total = 0;
  // when we find a key which is not expired
  hoothootStats[methodName].hit = 0;
  // when we find a key which is expired
  hoothootStats[methodName].softHit = 0;
  // when we don't find a key in the cache object
  hoothootStats[methodName].softMiss = 0;
  // when we make an actual dns function call
  hoothootStats[methodName].hardMiss = 0;
  // when the actual dns function call fails but we use expired cache instead
  hoothootStats[methodName].expiredHit = 0;
  // computed property using the hardMiss and time
  hoothootStats[methodName].avgTime = 0;
  // computed property using hit and total
  hoothootStats[methodName].hitRatioPercentage = 0;
  // when dns timesout
  hoothootStats[methodName].dnsTimedout = 0;
};

const createStatsIfNotPresent = (func) => {
  if (func && !hoothootStats[func]) {
    createStats(func);
  }
};

const setCurrentCacheSizeStats = (size) => {
  hoothootStats.cacheSize = size;
};

const CacheObject = function fn(configuration) {
  const conf = configuration || {};
  conf.ttl = parseInt(conf.ttl, 10) || 30; // 0 is not permissible
  conf.cachesize = parseInt(conf.cachesize, 10) || 1000; // 0 is not permissible

  this.ttl = conf.ttl * 1000;
  this.max = conf.cachesize;

  this.count = 0;
  this.data = {};

  this.set = function z(key, value, callback) {
    const self = this;
    next(() => {
      if (self.data[key]) {
        if (self.data[key].newer) {
          if (self.data[key].older) {
            self.data[key].newer.older = self.data[key].older;
            self.data[key].older.newer = self.data[key].newer;
          } else {
            self.tail = self.data[key].newer;
            delete self.tail.older;
          }

          self.data[key].older = self.head;
          self.head.newer = self.data[key];
          delete self.data[key].newer;
          self.head = self.data[key];
        }

        self.head.val = value;
        self.head.hit = 0;
        self.head.softHit = 0;
        self.head.ts = Date.now();
      } else {
        // key doesnt not exist
        self.data[key] = {
          key,
          val: value,
          hit: 0,
          softHit: 0,
          ts: Date.now(),
        };

        if (!self.head) {
          // cache is empty
          self.head = self.data[key];
          self.tail = self.data[key];
        } else {
          // insert the new entry to the front
          self.head.newer = self.data[key];
          self.data[key].older = self.head;
          self.head = self.data[key];
        }

        if (self.count >= self.max) {
          // remove the tail
          const temp = self.tail;
          self.tail = self.tail.newer;
          delete self.tail.next;
          delete self.data[temp.key];
        } else {
          self.count += 1;
          setCurrentCacheSizeStats(self.count);
        }
      }
      /* jshint -W030 */
      return callback && callback(null, value);
    });
  };

  this.get = function z(key, callback) {
    const self = this;
    let caller = '';
    if (typeSanity.isString(key)) {
      caller = key.split('_')[0];
    }
    if (!callback) {
      throw new Error('cache.get callback is required.');
    }

    createStatsIfNotPresent(caller);

    next(() => {
      // TODO use a global cache stats objects to prevent unnecessary calling from get func
      setCurrentCacheSizeStats(self.count);
      if (!self.data[key]) {
        // miss++
        if (caller) {
          hoothootStats[caller].softMiss += 1;
        }
        return callback(null, undefined);
      }

      let isExpired = false;
      if ((conf.ttl !== 0 && (Date.now() - self.data[key].ts) >= self.ttl) || self.data[key].isExpired) {
        isExpired = true;
        self.data[key].softHit += 1;
      } else {
        self.data[key].hit += 1;
      }

      const valueObj = {
        value: self.data[key].val,
        isExpired,
      };

      if (!isExpired && caller) {
        hoothootStats[caller].hit += 1;
      } else if (caller) {
        // ttl case
        hoothootStats[caller].softHit += 1;
      }
      return callback(null, valueObj);
    });
  };

  this.forceExpire = function z(key) {
    const self = this;
    self.data[key].isExpired = true;
  };
};

const pushStatsToHoothoot = () => {
  // emit
  Object.keys(hoothootStats).forEach((funcName) => {
    if (funcName !== 'cacheSize') {
      // eslint-disable-next-line max-len
      hoothootStats[funcName].total = hoothootStats[funcName].hit + hoothootStats[funcName].softMiss;
      hoothootStats[funcName].hitRatioPercentage
        = ((hoothootStats[funcName].hit / hoothootStats[funcName].total) * 100) || 0;
      Object.keys(hoothootStats[funcName]).forEach((nestedKey) => {
        if (typeof hoothootStats[funcName][nestedKey] === 'object') {
          // we push the errors here
          Object.keys(hoothootStats[funcName][nestedKey]).forEach((error) => {
            hoothoot.emit(
              constants.dnsCaching.hoothootKey,
              hoothootStats[funcName][nestedKey][error],
              { genre: [funcName, nestedKey, error].join('__') }
            );
          });
        } else {
          hoothoot.emit(constants.dnsCaching.hoothootKey, hoothootStats[funcName][nestedKey], { genre: [funcName, nestedKey].join('__') });
        }
      });
    }
  });
  hoothoot.emit(constants.dnsCaching.hoothootKey, hoothootStats.cacheSize, { genre: 'cacheObject__size' });
};

const setErrorStatsForAMethod = (func, err) => {
  const errorName = `${err.code}_${err.syscall}_${err.hostname}`;
  createStatsIfNotPresent(func);
  const firstTime = hoothootStats[func].errors[errorName] === undefined;
  if (firstTime) {
    hoothootStats[func].errors[errorName] = 0;
  }
  hoothootStats[func].errors[errorName] += 1;
};


const postDNSmethodCall = (startTime, func) => {
  createStatsIfNotPresent(func);

  const time = (new Date()).getTime() - startTime;
  hoothootStats[func].hardMiss += 1;
  hoothootStats[func].avgTime =
    ((hoothootStats[func].avgTime * (hoothootStats[func].hardMiss - 1)) + time)
    / (hoothootStats[func].hardMiss);
};

const setIsDnsTimedoutForAMethod = (func, val = 1) => {
  createStatsIfNotPresent(func);
  hoothootStats[func].dnsTimedout += val;
};

const setIsExpiredHitStatsForAMethod = (func) => {
  createStatsIfNotPresent(func);
  hoothootStats[func].expiredHit += 1;
};

const resetStats = () => {
  // TODO consider using a different object for cache to prevent incorrect reporting on hoothoot
  hoothootStats = { cacheSize: 0 };
};

setInterval(() => {
  pushStatsToHoothoot();
  resetStats();
}, 60 * 1000);

module.exports = {
  CacheObject,
  postDNSmethodCall,
  setIsExpiredHitStatsForAMethod,
  setErrorStatsForAMethod,
  setIsDnsTimedoutForAMethod,
};
