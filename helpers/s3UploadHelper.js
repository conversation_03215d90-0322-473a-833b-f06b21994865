'use strict';

const Promise = require('bluebird');
const { timeout_registry: timeoutRegistry, LOG_LEVEL: LL, globalS3ClientRegistry } = require('../constants');
const AWS = require('aws-sdk');
const constants = require('../constants');
const HubLogger = require('../log');

function getS3Client(sessionKeyObj) {
  const { s3key, s3secret, s3bucket } = sessionKeyObj;

  if (globalS3ClientRegistry[s3bucket] && globalS3ClientRegistry[s3bucket][s3key] && globalS3ClientRegistry[s3bucket][s3key][s3secret]) {
    return globalS3ClientRegistry[s3bucket][s3key][s3secret];
  }

  globalS3ClientRegistry[s3bucket] = {};
  globalS3ClientRegistry[s3bucket][s3key] = {};
  globalS3ClientRegistry[s3bucket][s3key][s3secret] = new AWS.S3({
    sslEnabled: false,
    apiVersion: '2006-03-01',
    accessKeyId: s3key,
    secretAccessKey: s3secret,
    maxRetries: 2,
    params: {
      Bucket: s3bucket,
    },
  });
  return globalS3ClientRegistry[s3bucket][s3key][s3secret];
}

function ********************(data, keyObject, fileCounter, framework, attempt, dataType = 'string') {
  let jsonData = {};
  if (dataType === 'object') {
    jsonData = data;
  } else {
    try {
      jsonData = JSON.parse(data);
    } catch (e) {
      HubLogger.seleniumStats('json-data-error', keyObject, e.toString(), 'Node data did not result in a valid json:\n' + data, keyObject.key, 'nodeError');
      return data;
    }
  }
  let ndata = undefined;
  let screenshot = true;
  const timeoutvalue = constants.timeout_registry[keyObject.rails_session_id];

  if (!timeoutvalue) {
    return dataType === 'object' ? jsonData : JSON.stringify(jsonData);
  }

  if (jsonData['value']) {
    let checkBuffer = '';
    if (jsonData['status'] == 0 || (!jsonData.hasOwnProperty('status'))) {
      checkBuffer = jsonData['value'];
    } else if (jsonData['value']['screen']) {
      checkBuffer = jsonData['value']['screen'];
      screenshot = false;
    }
    try {
      ndata = Buffer.from(checkBuffer, 'base64');
    } catch (e) {
      HubLogger.exceptionLogger('Cannot decode base64 screenshot response', keyObject.key, '********************');
      ndata = undefined;
    }
  }
  if (ndata) {
    HubLogger.miscLogger('PostScreenshot', `group_id: ${keyObject.group_id}, user_id: ${keyObject.user_id}, session_id: ${keyObject.rails_session_id}, Length of data to upload: ${ndata.length}`, LL.DEBUG, keyObject.debugSession);
    const filename = keyObject.rails_session_id + (screenshot ? `/screenshot-${framework}-` : '/screenshot-exception-') + fileCounter + '.png';
    const s3UploadParams = {
      Body: ndata,
      ACL: 'public-read',
      ContentType: 'image/png',
      Key: filename,
    };

    const s3logurl = `https://${keyObject.appTesting ? `app-automate` : `automate`}.browserstack.com/s3-debug/${keyObject.s3bucket}/${filename}`;

    const opts = { queueSize: 20, partSize: 1024 * 1024 * 10 };
    let managedUpload = getS3Client(keyObject).upload(s3UploadParams, opts);

    managedUpload.send((err, data) => {
      if (err) {
        HubLogger.exceptionLogger(`Error uploading screenshot to S3: ${err}`, keyObject.key, '********************');
        HubLogger.seleniumStats('hub-snapshot-failed', keyObject, '[Response Error] Error uploading selenium screenshot', err, '', '********************-Response');
      } else {
        HubLogger.miscLogger('S3-Uploads', keyObject.key + ' : File upload to s3: ' + (keyObject.s3bucket + filename), LL.DEBUG, keyObject.debugSession);
      }
    });

    if (screenshot)
      jsonData['value'] = s3logurl;
    else {
      jsonData['value']['screen'] = s3logurl;
    }
  }
  return dataType === 'object' ? jsonData : JSON.stringify(jsonData);
}

exports.******************** = ********************;
