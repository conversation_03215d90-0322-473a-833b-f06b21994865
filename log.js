/* eslint-disable strict */

const browserstack = require('./browserstack');
const constants = require('./constants');
const ha = require('./ha');
const helper = require('./helper');
const myLock = require('./semaphore');
const fs = require('fs');
const pubSub = require('./pubSub');
const requestlib = require('./lib/request');
const hoothoot = require('hoothoot')(constants.metrics_region);
const hoothoot_use = require('hoothoot')('use');
const hoothoot_user = require('hoothoot')(constants.metrics_region, { uniqueUserEvent: true });
const customLogger = require('./logger').customLogger;
const util = require('util');
const maskSensitiveInformation = require('./helpers/maskSensitiveInformation');
const soTimeoutBucketer = require('./helpers/soTimeoutBucketUtil')
const objectTransform = require('./helpers/objectOperations');
const LL = constants.LOG_LEVEL;
const REVERSE_LL_MAP = new Map(Object.entries(LL).map(([k, v]) => [v, k]));
const kafkaConfig = constants.kafkaConfig;
const instrumentation = require('./helpers/instrumentation');
const { isNotUndefined, isTrueString } = require('./typeSanity');
const { Events } = require('browserstack-dwh');
const errorMessageConstants = require('./errorMessages');
const browserstackErrorUtil = require('./helpers/browserstackErrorBucketUtil');
const { uploadLogPartToKafka } = require('./lib/kafka/kafkaProducer');
const env = process.argv[2];
const isProductionEnv = env === 'Production';

exports.hoothoot = hoothoot;
exports.hoothoot_user = hoothoot_user;
exports.hoothoot_use = hoothoot_use;

const logger = function(category, headers, data, host, method, url, level = LL.DEBUG, error, stackTrace, forceDebugMode=false, uuid=undefined, custom=undefined) {
  if(constants.disableLogs)
    return;
  if (level <= constants.hub_log_level || forceDebugMode) {
    let metaData = isProductionEnv ? {
      process: constants.hubName + process.pid,
      log: {
        kind: category
      },
      headers: JSON.stringify(headers).replace('\n', ' '),
    } : {
      time: new Date().toISOString(),
      process: constants.hubName + process.pid,
      category,
      host,
      method,
      url,
      headers: JSON.stringify(headers).replace('\n', ' '),
    };
    if (helper.isExceptionLog(category)) {
      metaData.error = (error ? error.toString() : '');
      metaData.stackTrace = (stackTrace ? stackTrace.toString() : '');
    }
    logLevel = REVERSE_LL_MAP.get(level);
    if (uuid && metaData.log){
      metaData.log.uuid = uuid
    }
    if (custom && metaData.log){
      metaData.log.custom = custom
    }
    customLogger.log(logLevel,
      data,
      metaData
    );
   }
}

const redactString = (data, start, end, redactWith) => {
  var redactWith = redactWith || '[REDACTED]';
  var startIndex = data.indexOf(start);
  var nextParam = -1;
  if (startIndex > -1) {
    startIndex += start.length;
    nextParam = data.indexOf(end, startIndex);
    data = data.substring(0, startIndex) + redactWith + data.substring(nextParam);
  }
  return data;
}
exports.redactString = redactString;

const redactPlainJSONCerts = (data) => {
  const JsonKeysToRedact = [
    'browserstack.percy.gcs_certs'
  ];
  JsonKeysToRedact.forEach((key) => {
    data = redactString(data, key + '":', '},', '"[REDACTED]"');
  });
  return data;
};
exports.redactPlainJSONCerts = redactPlainJSONCerts;

logKeyObject = function (keyObject) {
  return redactHashForAWSKeys(JSON.stringify(keyObject));
};
exports.logKeyObject = logKeyObject;

redactHashForKeys = function(data) {
  data = redactHashForAWSKeys(data);
  data = redactPlainJSONCerts(data);
  return redactHashForAppStoreLoginKeys(data);
};
exports.redactHashForKeys = redactHashForKeys;

redactHash = function (data, keysToRedact) {
  keysToRedact.forEach((key) => {
    data = redactString(data, key + '":', ',', '"[REDACTED]"');
  });
  return data;
};

redactHashForAppStoreLoginKeys = function (data) {
  var keysToRedact = [
    'browserstack.appStoreConfiguration.username',
    'browserstack.appStoreConfiguration.password'
    ];
  return redactHash(data, keysToRedact);
};
exports.redactHashForAppStoreLoginKeys = redactHashForAppStoreLoginKeys;

redactHashForAWSKeys = function (data) {
  var keysToRedact = constants.instrumentationRedactionKeys;
  return redactHash(data, keysToRedact);
};
exports.redactHashForAWSKeys = redactHashForAWSKeys;

filterKeys = function(data) {
  data = filterAWSKeys(data);
  return filterAppStoreLoginKeys(data);
}
exports.filterKeys = filterKeys;

filterAppStoreLoginKeys = function(data) {
  data = redactString(data, "app_store_username=", "&");
  return redactString(data, "app_store_password=", "&");
}

filterAWSKeys = function(data) {
  // Filters the AWS keys and secret for the video
  data = redactString(data, "edsKey=", "&");
  data = redactString(data, "stats_aws_key=", "&");
  data = redactString(data, "stats_aws_secret=", "&");
  data = redactString(data, "video_aws_keys=", "&");
  data = redactString(data, "logs_aws_keys=", "&");
  data = redactString(data, "logs_aws_secret", "&");
  return redactString(data, "video_aws_secret=", "&");
}
exports.filterAWSKeys = filterAWSKeys;

filterDetails = function(data) {
  data = filterAWSDetails(data);
  return filterAppStoreLoginDetails(data);
}
exports.filterDetails = filterDetails;

filterAppStoreLoginDetails = function(data) {
  data = filterAppStoreLoginKeys(data)
  data = redactString(data, "browserstack.appStoreConfiguration.username\":", ",\"")
  return redactString(data, "browserstack.appStoreConfiguration.password\":", ",\"")
}

filterAWSDetails = function(data) {
  // Filters user uploaded App s3 URLs + AWS Keys
  data = filterAWSKeys(data)
  data = redactString(data, "app\":", ",\"")
  data = redactString(data, "appURL\":", ",\"")
  return redactString(data, "s3_app_url=", "&");
}
exports.filterAWSDetails = filterAWSDetails;

const requestLogger = (hostname, request, data, url, response, date, level) => {
  if(level != LL.TRACE)
    level = response? LL.RESPONSE : LL.REQUEST
  if(data && data.length > 2000){
    data = '{"response-length": '+data.length+'}'
  }
  logger(response? "RESPONSE" : "REQUEST",
    util.inspect(request.headers),
    data || '~',
    hostname,
    request.method,
    url,
    level);
}
exports.requestLogger = requestLogger;

// https://github.com/browserstack/infra-config/blob/master/provision/roles/chitragupta_setup/chitragupta_format.md#server
const newCGLogger = (cg_kind, cg_dynamic_data, level, cg_uuid, hub_forceDebugMode=false, cg_f0_custom_float=undefined, cg_f1_custom_keyword=undefined) => {
  const custom = {};
  if (cg_f0_custom_float) {
    custom.f0 = cg_f0_custom_float; // searchable float
  }
  if (cg_f1_custom_keyword) {
    custom.f1 = cg_f1_custom_keyword; // searchable keyword # LIMIT - 40
  }
  logger(cg_kind, {}, cg_dynamic_data,
    '', '', '', level, undefined, undefined, hub_forceDebugMode, cg_uuid, (cg_f0_custom_float || cg_f1_custom_keyword) ? custom : undefined
  );
}
exports.newCGLogger = newCGLogger;

const jarRequestLogger = (hostname, request, data, url, response, date, level) => {
  if(level != LL.TRACE)
    level = response? LL.RESPONSE : LL.REQUEST
  if(data && data.length > 2000){
    data = '{"response-length": '+data.length+'}'
  }
  logger(response? "JAR_RESPONSE" : "JAR_REQUEST",
    util.inspect(request.headers),
    data || '~',
    hostname,
    request.method,
    url,
    level);
}
exports.jarRequestLogger = jarRequestLogger;

const miscLogger = (category, data, level, debugMode=false) => {
  logger(category, {}, data,
    '~', '~', '~', level, undefined, undefined, debugMode
    );
}
exports.miscLogger = miscLogger;

//temporary logger to migrate
const tempExceptionLogger = (data, error = '~', stackTrace = '~', hostname = '~', url = '~') => {
  logger('EXCEPTION', {}, data, hostname, '~', url, LL.ERROR, error, stackTrace);
}
exports.tempExceptionLogger = tempExceptionLogger;

const exceptionLogger = (data, hostname, url, error, stackTrace, identifier = undefined) => {
  logger(`EXCEPTION${identifier}`, {}, data, hostname || '~', "~", url || '~', LL.ERROR, error || '~', stackTrace || '~');
}
exports.exceptionLogger = exceptionLogger;

exports.bsLogger = function(hostname, url, headers, data, date, identifier, level) {
  level = data? LL.RESPONSE : LL.REQUEST
  logger(
    data? `BSTACK_RESPONSE_${identifier}` : `BSTACK_REQUEST_${identifier}`,
    headers,
    data? JSON.stringify(data) : "~",
    hostname,
    "POST",
    url,
    level);
}

exports.bsErrorHandler =  function(request, response, e, errorMessage, requestData, username, options) {
  if(options && options.isStartRequest) {
    helper.addToConsoleTimes(request, `post-browserstack-end`);
  }
  options = options || {};
  var hostname = constants.BS_ENDPOINT;
  exceptionLogger(hostname, "Data: " + requestData + " : Error : " + (e ? e.message : errorMessage));
  var emailData = (e ? e.message : errorMessage) + "\n" + requestData + "\n\n";
  if (e && !constants.disableLogs) { console.trace(e); emailData = emailData + "\n\n" + e.stack;}
  seleniumStats("hub_to_bs", {"region" : constants.sub_region, "name" : hostname }, (e ? e.message : errorMessage), emailData, "", "bsError", {isAppAutomate: options.isAppAutomate, isDetox: options.isDetox});
  const isAppAutomate = options.isAppAutomate || false;
  helper.pushToCLS('hub_to_bs_failure', {
    region: constants.region,
    req_data: requestData,
    error_message: errorMessage
  }, isAppAutomate);
  if (isNotUndefined(request)) {
    helper.sendToEDS({
      kind: options.isAppAutomate ? Events.APP_AUTOMATE_ERROR_DATA : Events.AUTOMATE_ERROR_DATA,
      error_code_str: "hub_to_bs_multiple_attempts_failed",
      request_id: request.id,
      request_received_at: request.requestReceivedAt,
      error_message: e ? errorMessageConstants.RAILS_UNREACHABLE_DURING_START : errorMessage
    });
  }
  hoothoot.emit('hub', 1, { type: 'failures', type_instance: 'hubToBS' });
  if(username) {
    var hoothootPlatform = options.isAppAutomate ? 'all' : 'desktop';
    hoothoot_user.uniqueUserEvent(username, (options.isAppAutomate ? 'app-automate' : 'automate'), 'hub_to_bs', hoothootPlatform, helper.getHubCanaryTag());
    if (!options.isAppAutomate) hoothoot_use.emit('automate_errors_data', 1, { event_type: 'hub_to_bs', platform: hoothootPlatform, product: 'automate', user: username, hub_region: constants.region});
  }
  if(response) {
    var data = JSON.stringify({value: {message: (e ? errorMessageConstants.RAILS_UNREACHABLE_DURING_START: errorMessage)}, sessionId: "", "status": 13});
    instrumentationStats('Rails Down', {user: username}, '', data);
    helper.respondWithError(request, response, data);
  }
};


exports.sendSessionLog = function(keyObject, type, time_ts, logdata, cb){
  exports.sessionLog(keyObject, type, helper.s3LogFormat(type, time_ts, logdata), cb);
}

/*
This function is the entry point for all log data is that is to be uploaded. e.g. to s3, to dashboard.
It does so by passing logs to a middleware service.
*/
var sessionLog = function(keyObject, type, logdata, cb) {
  // This just sends stop request to Kafka to indicate that the session is
  // finished and console log can be uploaded successfully.
  const isStartOrStopRequest = ['REQUEST_START', 'STOP_SESSION'].includes(type);

  // Handling sending start and stop for console logs here
  if (isStartOrStopRequest) {
    uploadLogPartToKafka(keyObject, type, '', kafkaConfig.performance_logs_topic);
    if(!keyObject.appTesting) {
      uploadLogPartToKafka(keyObject, type, '', kafkaConfig.console_logs_topic);
    }
  }
  /*. Do not upload any logs if :
      1. A Stop log is already uploaded.
      2. If Stop has been triggered and the type is not in STOP or TIMED_OUT_REQUEST
  */
  const deletedAndInvalidMessage = (keyObject.pendingDelete && (type != "STOP_SESSION" && type != "TIMED_OUT_REQUEST"));
  if(keyObject && (keyObject.stopLogUploaded || deletedAndInvalidMessage)) {
    if(cb) cb();
    return;
  }
  if(type == "TIMED_OUT_REQUEST") {
    type = "REQUEST";
  }
  var sessionId = keyObject.rails_session_id;

  // Publish messages to dashboard
  helper.redisClient.get(["session_logs_opened_" + keyObject.rails_session_id], function(error, reply){
    if(error){
      miscLogger('REDIS-LOG', 'ERROR getting logs from session_logs_opened_', LL.ERROR);
    }
    if(reply && parseInt(reply) > 0) {
      var logdata_ws = logdata;
      if(logdata.length > 25*1024) {
        const dashboardPubSubStartTime = new Date();
        var logdata_parts = logdata.split("\r\n");
        for (var i = logdata_parts.length - 1; i >= 0; i--) {
          if(logdata_parts[i].length > 25*1024)
            logdata_parts[i] = logdata_parts[i].substring(0, logdata_parts[i].indexOf('{'));
        };
        logdata_ws = logdata_parts.join("\r\n");
        miscLogger("REDIS-LOG", `Sent log event to dashboard, sessionId: ${sessionId} in time: ${(new Date()) - dashboardPubSubStartTime}`, LL.INFO);
      }
      helper.redisClient.publish("session_logs_events_" + keyObject.rails_session_id, sessionId + logdata_ws + "\r\n");
    }
  });

  // Handling raw logs here
  if (keyObject && keyObject.extendedSessionDuration) {
    uploadLogPartToKafka(keyObject, type, `${logdata}\r\n`, kafkaConfig.raw_extended_duration_logs_topic);
    if (constants.SEND_RAW_LOGS_BOTH) {
      uploadLogPartToKafka(keyObject, type, `${logdata}\r\n`, kafkaConfig.raw_logs_topic);
    }
  } else {
    uploadLogPartToKafka(keyObject, type, `${logdata}\r\n`, kafkaConfig.raw_logs_topic);
  }
  if (type === 'STOP_SESSION') {
    keyObject.stopLogUploaded = true;
  }
  if (cb) {
    cb();
  }
}

exports.sessionLog = sessionLog;

// Have migrated uploadLogPartToKafka to lib/kafka/uploadToKafka.js, keeping this to keep imports same.
exports.uploadLogPartToKafka = uploadLogPartToKafka;

const instrumentationStats = (keyIdentifier, keyObject, subKeyIdentifier = '', issueInfo = '') => {
  keyObject = keyObject || {};
  let dataToLog = {
    groupId: keyObject.group_id,
    userId: keyObject.user_id,
    username: keyObject.user,
    issueInfo,
    os: keyObject.os,
    osVersion: keyObject.os_version,
    deviceName: keyObject.deviceName,
    browser: keyObject.browser,
    browserVersion: keyObject.browser_version,
    timestamp: keyObject.timestamp
  }

  dataToLog = objectTransform.validateValuesOfObject(dataToLog, 'NOT AVAILABLE');
  miscLogger(`Instrumentation : ${keyIdentifier} : ${subKeyIdentifier}`, JSON.stringify(dataToLog), LL.INFO)
}
exports.instrumentationStats = instrumentationStats;

function seleniumStats(kind, keyObject, error, data, url, category, options){
  var clipped_data = ( data ? data.toString().substring(0, 1024) : "" );

  if(error == "start-error-firecmd" || error == "firecmd-error-safari-plugin") {
    clipped_data = keyObject.firecmd_error_message ? keyObject.firecmd_error_message.toString().substring(0, 1024) : clipped_data
  }

  var clipped_error = ( error ? error.toString().substring(0, 1024) : "" );
  options = options || {};

  if(keyObject && keyObject.isWebDriverIOSession && kind == "selenium-node-failure" && clipped_error.match(/^connection failed/i)) {
    kind = kind + "-wdio";
  }
  if(keyObject && keyObject.hasBasicAuthPopup && kind == "selenium-node-failure") {
    kind = kind + "-popup";
  }
  // Ignoring start-error for this user, as he is passing wrong chromeOptions
  // "chromeOptions":{"mobileEmulation":{"deviceName":"iPhone 6"}
  // ^^ (Should be 'Apple iPhone 6' instead of 'iPhone 6')
  if(keyObject && keyObject.user_id == "830926" && data == "start-error" && keyObject.browser == "chrome") {
    return;
  }

  if(keyObject && keyObject.isWebDriverIOSession && kind == "selenium-node-failure") {
    kind = kind + "-wdio";
  }
  if(data && data == "start-error-badprofile") {
    exports.miscLogger("Zombie", "Skipping platform-stats entry for badprofile start-error", LL.INFO);
    return;
  }

  let econnrefusedRegex = /original error: error: connect econnrefused/;
    if(data && data.toString().toLowerCase().match(econnrefusedRegex)) {
      kind = "selenium-node-failure-econnrefused";
    }

  // append "app-" suffix to zombie kind for app-automate alerts
  if ((keyObject && keyObject.appTesting) || (options && options.isAppAutomate)) {
    if (isTrueString(keyObject.isDetox) || isTrueString(options.isDetox)) {
      kind = "detox-" + kind;
    } else {
      kind = "app-" + kind;
    }

    const ADB_ISSUE_REGEX = /Stderr: 'error: device '.*' not found'|error: device offline|adb: error: listener '.*' not found/;
    if (isNotUndefined(data) && ADB_ISSUE_REGEX.test(data.toString())) {
      kind = 'app-selenium-node-failure-appium-non200';
    }

    var appCrashRegex = /test with bundle id .* is not running, possibly crashed|Cannot start the .* application|Failed to launch .* application/;
    if(data && data.toString().match(appCrashRegex)) {
      kind = "app-selenium-node-failure-user-app-crash";
    }

    if(data && constants.customerIssuesRegex.some((errorLine) => data.toString().toLowerCase().match(errorLine))) {
      kind = "app-selenium-node-failure-customer-issue";
    }

    var customerIssuesIOSRegex = /could not navigate to webview; there are none/;
    if(data && keyObject.os && ['ios', 'tvos', 'mac'].includes(keyObject.os.toLowerCase()) && (data.toString().toLowerCase().match(customerIssuesIOSRegex) || (helper.getOsVersion(keyObject.os_version) >= 14.5 && browserstackErrorUtil.detectMissingAppIdKey(data)))) {
      kind = "app-selenium-node-failure-customer-issue";
    }

    if(kind === "app-selenium-node-failure-user-app-crash" || kind === "app-selenium-node-failure-customer-issue" || (keyObject.firecmd_error_message && keyObject.firecmd_error_message.toLowerCase().includes("google"))) {
      helper.PingZombie({
        "sessionid": keyObject.sessionId || keyObject.rails_session_id,
        "kind": "app_automation_session_stats",
        "secondary_diagnostic_reason": "start-error-user",
        "secondary_diagnostic_reasons": "start-error-user",
      });
    } else {
      // browserstack issues, flag used to push uptime metric.
      keyObject.browserstackIssue = true;
    }
  }

  var browser = ((keyObject && keyObject.appTesting) || (options && options.isAppAutomate)) ? 'app' : keyObject.browser;
  var opts = browserstack._getLoggingOpts(keyObject.bsCaps, keyObject.browserstackParams);
  var zombieData = {
    "category": category || "selenium-stats",
    "kind": kind,
    "data": clipped_data || keyObject.firecmd_error_message || "",
    "url": url,
    "user_id": keyObject.user_id,
    "error": clipped_error,
    "timestamp": (Math.round(new Date().getTime() /1000).toString()),
    "machine": keyObject.name,
    "terminal_type": keyObject.terminal_type,
    "os": keyObject.os,
    "os_version": keyObject.os_version,
    "browser": browser,
    "browser_version": keyObject.browser_version,
    "session_id": keyObject.sessionId || keyObject.rails_session_id,
    "device": keyObject.device,
    "region": constants.hubName+"--"+ keyObject.terminalSubRegion,
    "user_browser": keyObject.terminalRegion // This field is null for this alert in pager.
  };

  var reattempt = typeof(arguments[1]) == 'undefined' ? true : false
  try {
    helper.PingZombie(zombieData);
  }
  catch(e) {
    exceptionLogger("Zombie-Socket-Exception", "posting data to zombie. Data: " + zombieDataString + " Error: " + e.stack.toString() + " reattempt = " + reattempt);
    var zombieData = {"category": "zombieError", "kind": "sendto-zombie-failed",
      "timestamp": (Math.round(new Date().getTime() /1000).toString()),
      "error": e.stack.toString(),
      "data": zombieDataString
    };
    date = new Date();
    dateString = date.getFullYear().toString() + (date.getMonth()+1).toString() + date.getDate().toString();
    fs.appendFileSync('zombie-' + dateString, JSON.stringify(zombieData) + "\n");
  }
}

const extractAttributesWhenBSF = (options) => {
  let keyObject = {
    video: (options.browserstackParams['browserstack.video'] || false).toString() === 'true',
    video_aws_keys: options.browserstackParams['browserstack.video.aws.key'],
    video_aws_secret: options.browserstackParams['browserstack.video.aws.secret'],
    video_aws_bucket: options.browserstackParams['browserstack.video.aws.s3bucket'],
    video_aws_region: options.browserstackParams['browserstack.video.aws.region'],
    video_aws_storage_class: options.browserstackParams['browserstack.video.aws.storageclass'],
    video_file: options.browserstackParams['browserstack.video.filename'] || 'video',
    logs_new_bucketing: isTrueString(options.bsCaps["new_bucketing"]),
    logs_aws_keys: options.browserstackParams['browserstack.logs.aws.key'],
    logs_aws_secret: options.browserstackParams['browserstack.logs.aws.secret'],
    logs_aws_bucket: options.browserstackParams['browserstack.logs.aws.s3bucket'],
    logs_aws_region: options.browserstackParams['browserstack.logs.aws.region'],
    logs_aws_storage_class: options.browserstackParams['browserstack.logs.aws.storageclass'],
    selenium_version: options['selenium_version'] || options.bsCaps['selenium_version'] || options.browserstackParams['browserstack.selenium.jar.version'] || options.browserstackParams['browserstack.selenium_version'],
    captureCrash: true,
    tunnel: (options.browserstackParams['browserstack.tunnel'] || false).toString() === 'true',
    os: options.bsCaps['orig_os'],
    user: options.post_params['u'],
    sessionId: options.sessionId || options.rails_session_id,
    rails_session_id: options.sessionId || options.rails_session_id,
    webDriverLogs: (options.browserstackParams['browserstack.seleniumLogs'] || false).toString() === 'true',
    name: options.host_name,
    user_id: options.user_id,
    rproxyHost: options.rproxyHost
  }
  return keyObject;
}

exports.seleniumStats = seleniumStats;

function terminalRequest(url, options, callback, attempt, noAbort){
  var get_data = url,
    port_no = options.os.indexOf("win") > -1? "4567" : "45671",
    attempt = attempt || 0;

  options.sessionId = options.rails_session_id;
  if (url !== "") {
    get_data += "&terminal_ip="+options.name;
    miscLogger("terminalRequest", options.sessionId + " Starting terminalRequest: for " + filterAWSKeys(get_data), LL.INFO);

    var tr_url = "http://" + options.name + ":" + port_no + get_data;
    var callbackDone = false;
    const isAppAutomate = options.appTesting || false;
    helper.pushToCLS('terminal_request', {
      user_id: options.user,
      session_id: options.sessionId,
      terminal: options.name,
      get_url: filterAWSKeys(tr_url)
    }, isAppAutomate);
    requestlib.call({
      hostname: options.rproxyHost,
      port: port_no,
      path: get_data,
      timeout: options.os.indexOf("win") > -1? 60000 : 10000,
      headers: requestlib.appendBStackHostHeader(options.name),
    })
    .then((res) => {
      var data = res.data;
      helper.pushToCLS('terminal_response', {
        user_id: options.user,
        session_id: options.sessionId,
        terminal: options.name,
        response: data
      }, isAppAutomate);
      try{
        var parsed_data = JSON.parse(data);
        if(typeof parsed_data.error === 'undefined'){
          miscLogger("terminalRequest", options.sessionId + " Response code while terminalRequest: " + res.statusCode + " for " + filterAWSKeys(get_data) + " " + data, LL.WARN);
          if(!callbackDone){
            callbackDone = true;
            callback();
          }
          if(parseFloat(parsed_data["file_size"]) > 0 && parseFloat(parsed_data["file_size"]) < 1 && parseFloat(parsed_data["time"]) > 10){
            exports.seleniumStats("video-stats-large-upload-time", options, "None - Large Upload Time", parsed_data["time"], parsed_data["file_size"]);
          }
          else if(parseFloat(parsed_data["file_size"]) > 1 && parseFloat(parsed_data["time"])/parseFloat(parsed_data["file_size"]) > 10){
            exports.seleniumStats("video-stats-very-large-upload-time", options, "None - Very Large Upload Time", parsed_data["time"], parsed_data["file_size"]);
          }
          else if(parseFloat(parsed_data["file_size"]) > 1 && parseFloat(parsed_data["time"])/parseFloat(parsed_data["file_size"]) > 5){
            exports.seleniumStats("video-stats-large-upload-time", options, "None - Large Upload Time", parsed_data["time"], parsed_data["file_size"]);
          }
          if(parsed_data["active_window"]){
            var lowerCaseActiveWindow = parsed_data["active_window"].toLowerCase();
            var bucket = '';
            var bucket_hash = {
              'facebook': 'facebook',
              'looks like something went wrong': 'something_wrong',
              'command line server for the ie driver': 'command_line_server',
              'not responding': 'not_responding',
              'data:, - google chrome': 'chrome_data_page',
              'chromedriver': 'chrome_driver',
              'choose file to upload': 'file_upload',
              'firefox stopped working': 'firefox_stopped',
              'ie stopped working': 'ie_stopped',
              'site cant be reached': 'site_unreachable',
              'site can\'t be reached': 'site_unreachable',
              'not available': 'site_unreachable',
              'save as': 'save_dialog',
              'alert': 'alert'
            }
            Object.keys(bucket_hash).forEach(function(element) {
              if(lowerCaseActiveWindow.indexOf(element) > -1) {
                bucket = bucket_hash[element];
              };
            });
          }
        } else {
          exceptionLogger("terminalRequest", options.sessionId + " Error in response for terminalRequest. Exception: " + data, options.name, get_data)
          if(!callbackDone){
            callbackDone = true;
            callback();
          }
          exports.seleniumStats("video-stats-exception", options, parsed_data.error, JSON.stringify(parsed_data), "Error uploading video");
        }
      } catch(err) {
        exceptionLogger("terminalRequest", options.sessionId + " Error while parsing and processing response in terminalRequest. Exception: " + err.toString(), options.name, get_data)
        if(!callbackDone){
          callbackDone = true;
          callback();
        }
        exports.seleniumStats("video-stats-exception", options, err.toString(), data, "Error in parsing response object");
      }
    })
    .catch((e) => {
      if(!callbackDone){
        callbackDone = true;
        callback();
      }
      if(e){
        exceptionLogger("terminalRequest Error", e.type + " while getURL " + e.toString());
        exports.seleniumStats("video-stats-exception", options, e.toString(), e.type, "Timedout Error");
      }
    });
  } else {
    callback();
  }
}

exports.terminalRequest = terminalRequest;

var stopVideoRecording = function(keyObject, callback, source, active_window, additionalParams) {
  additionalParams = additionalParams || {};
  // in case of 'start-error', the keyObject is actually 'options' object. Therefore, the extraction required.
  keyObject = source === "start-error" ? extractAttributesWhenBSF(keyObject) : keyObject;
  if (keyObject) {
    helper.pushToCLS('stop_video', {
      user_id: keyObject.user,
      session_id: keyObject.rails_session_id,
      terminal: keyObject.hostname
    }, keyObject.appTesting);
    constants.stop_sessions_registry[keyObject.os] = constants.stop_sessions_registry[keyObject.os] || {};
    constants.stop_sessions_registry[keyObject.os][keyObject.rails_session_id] = 1;
  }
  if(keyObject) {
    var videoRecParams = {
      video: keyObject.video,
      video_aws_keys: keyObject.video_aws_keys,
      video_aws_secret: keyObject.video_aws_secret,
      video_aws_bucket: keyObject.video_aws_bucket,
      video_aws_region: keyObject.video_aws_region,
      video_aws_storage_class: keyObject.video_aws_storage_class,
      logs_new_bucketing: keyObject.logs_new_bucketing,
      logs_aws_keys: keyObject.logs_aws_keys,
      logs_aws_secret: keyObject.logs_aws_secret,
      logs_aws_bucket: keyObject.logs_aws_bucket,
      logs_aws_region: keyObject.logs_aws_region,
      logs_aws_storage_class: keyObject.logs_aws_storage_class,
      video_file: keyObject.video_file,
      upload_fg: false,
      logHost: constants.zombie_server,
      edsHost: constants.eds_server,
      edsPort: constants.eds_port,
      edsKey: constants.eds_key,
      selenium_version : keyObject.selenium_version,
      session_id: keyObject.sessionId || keyObject.rails_session_id,
      uploadWebDriverLogs : keyObject.webDriverLogs && constants.uploadWebDriverLogs,
      webdriver_logs_key : "selenium-logs.txt", // Decide the name here based on product requirements.
      modifyLogsForAi: keyObject.aiEnabledSessions || 'false',
    }
    if (active_window) {
      videoRecParams['active_window'] = true;
    }
    if (keyObject.tunnel) {
      videoRecParams['local'] = true;
    }
    if (keyObject.captureCrash) {
      videoRecParams['capture_crash'] = true
    }
    if(additionalParams.userRequestStartTimeDiff) {
      videoRecParams['userRequestStartTimeDiff'] = additionalParams.userRequestStartTimeDiff;
    }
    if (keyObject.isPlaywright) {
      videoRecParams['genre'] = 'playwright';
    }

    terminalRequest("/stop_video_rec?" + requestlib.getEncodedURLParams(videoRecParams), keyObject, callback, 0, true);
  } else {
    callback();
  }
}

exports.stopVideoRecording = stopVideoRecording;

// Called in case of start-errors, start-error-firecmd and SO-timeouts
exports.nodeErrorHandler = function(request, response, e, hostname, clientSessionID, errorMessage, errorScreenshot, originalDate, opts, keyObject, user, callback) {
  const isAppAutomate = ((keyObject && keyObject.appTesting) || (opts && opts.appTesting));
  if (keyObject) {
    helper.pushToCLS('node_error', {
      user_id: keyObject.user,
      terminal: hostname,
      session_id: keyObject.sessionId
    },isAppAutomate);
  }
  var exception = (e && e.message ? e.message : errorMessage);
  var device = opts.device;
  requestLogger(hostname, request, undefined, request.url, true, originalDate, LL.WARN);
  exceptionLogger(clientSessionID + " : " + exception, hostname, request.url);
  opts = opts || {};

  if(errorMessage == "start-error" || errorMessage == "start-error-firecmd" || errorMessage == "start-error-firecmd-user" || errorMessage == "start-error-emulator" || errorMessage == "start-error-badprofile" || errorMessage == 'start-error-firecmd-browsermob' || errorMessage == 'firecmd-error-safari-plugin' || errorMessage == "incorrect-google-creds" || errorMessage == "captcha-on-login" || constants.googleLoginUserErrors.includes(errorMessage)) {
    var emailData = hostname + " : " + clientSessionID + " : " + exception;
    var temp = hostname.split(":");
    var host = temp[0];
    var bsurl = "&release=true&ip=" + host;
    const isMobile = (opts && opts.device) || (keyObject && keyObject.device);

    if(device)
      bsurl += "&device=" + encodeURIComponent(device);
    if(clientSessionID)
      bsurl += "&k=" + clientSessionID;
    var release_post_params = {
      r: "start-error",
      s: exception,
    }

    if (errorMessage == "incorrect-google-creds" || errorMessage == "captcha-on-login") {
      release_post_params.app_store_username = opts["app_store_username"];
      release_post_params.app_store_password = opts["app_store_password"];
      release_post_params.platFormError = errorMessage;
    }

    opts["errorMessage"] = errorMessage;

    if (keyObject && keyObject.appTesting) {
      bsurl += "&app=true";
      release_post_params.isAppAutomate = true;
    }
    opts["user_id"] = (keyObject)? keyObject.user_id : -1;
    opts["terminal_type"] = (keyObject)? keyObject.terminal_type : "no-idea";
    opts["name"] = host;
    opts["sessionId"] = clientSessionID ? clientSessionID : keyObject.rails_session_id;

    // Start errors on user defined ie drivers will give a secondary message
    var handlerCallback = function () {
      const isAppAutomateSession = ((keyObject && keyObject.appTesting) || (opts && opts.appTesting));
      var zombie_kind = isAppAutomateSession ? "app_automation_session_stats" : "automation_session_stats";
      if(opts['browser'] && opts['browser'].toLowerCase() == "ie" && typeof(opts['ieDriver']) !== 'undefined') {
        release_post_params.s =  'start_error: user-defined-ie-driver'
      } else if(errorMessage == "start-error") {
        if(opts['browser'] && opts['browser'].match(/chrome|firefox/i)){
          helper.PingZombie({
           "sessionid": clientSessionID,
           "bs_bucket": "customer",
           "kind": zombie_kind
          });
        } else {
          helper.PingZombie({
           "sessionid": clientSessionID,
           "kind": zombie_kind,
           "secondary_diagnostic_reason": "start-error",
           "secondary_diagnostic_reasons": "start-error",
          });
          instrumentation.pushProductStabilityReason(clientSessionID, "start-error-non-200-appium", isAppAutomateSession);
        }
      }
      if(errorMessage.match(/start-error-firecmd|start-error-firecmd-browsermob|start-error-badprofile|start-error-firecmd-user/)) {
        const reason = opts["error_from"] || errorMessage;

        helper.PingZombie({
         "sessionid": clientSessionID,
         "kind": zombie_kind,
         "secondary_diagnostic_reason": reason,
         "secondary_diagnostic_reasons": reason,
        });

        if (errorMessage == "start-error-firecmd") {
          instrumentation.pushProductStabilityReason(clientSessionID, "start-error-firecmd", isAppAutomateSession);
        }
      }

      let scopifiedKeyObject = keyObject;

      browserstack.postBrowserStack(bsurl, release_post_params, undefined, undefined, function () {
        var data = "";
        var secondaryMessage = "";
        var errorMessage = "";

        errorMessage = helper.getFireCMDErrorMessage(opts, scopifiedKeyObject);

        try {
          if (opts["errorMessage"] == "incorrect-google-creds") {
            errorMessage = "Could not login to google account";
            secondaryMessage = "Incorrect username/password";
          } else if (opts["errorMessage"] == "captcha-on-login") {
            errorMessage = "Could not login to google account";
            secondaryMessage = "Captcha encountered while google login. Please enable installation of less secure apps or use a different sandbox account."
          } else {
            var errorJson = JSON.parse(opts.errorStack)["value"];
            secondaryMessage = errorJson["localizedMessage"] || errorJson["message"] || ""
            secondaryMessage = secondaryMessage.replace(/\/usr\/local\/.browserstack\//g, "");
          }

          data = JSON.stringify({value: {message: errorMessage + "\n Reason: \n" + secondaryMessage}, sessionId: "", "status": 13});
        } catch(e){
          //exceptionLogger("JSON Parse Error while getting stack trace: " + e.stack.toString());
          data = JSON.stringify({value: {message: errorMessage}, sessionId: "", "status": 13});
        }
        if(keyObject){ pubSub.publish(constants.sessionTimeout, keyObject.sessionId); }
        //response.writeHead("500", {'content-type': 'application/json; charset=utf-8', 'accept': 'application/json', 'content-length': data.length}, opts.indexCounter);
        if (!((keyObject || {}).appTesting)) {
          instrumentationStats('Start Error', keyObject, '', data);
        }
        helper.respondWithError(request, response, data);
        var keyObject = constants.global_registry[clientSessionID];
        if(keyObject) {
          // not calling with params apart from keyObject because no delete/timeout publish in flow
          helper.sessionRemovedFromRegionHook(keyObject);
        }
        if(callback) callback();
      });

      var dataToBeSent = errorMessage;

      if(e && e.message) {
        dataToBeSent = e.message;
      } else if (opts && opts.appTesting) {
        var eJson = null;
        try {
          eJson = JSON.parse(e);
        } catch(e) {}

        if(eJson && eJson.value && eJson.value.message) {
          dataToBeSent = eJson.value.message;
        }
      }

      const isDetoxSession = isAppAutomateSession && isTrueString(keyObject.isDetox);
      let pagerKind = "selenium-node-failure";
      if (dataToBeSent == "firecmd-error-safari-plugin") {
        pagerKind = "safari-plugin-node-failure-selenium";
      }
      seleniumStats(pagerKind, opts, exception, dataToBeSent, "start-error", "nodeError", {isAppAutomate: opts.appTesting, isDetox: isDetoxSession});

      const username = user ? user : keyObject.post_params ? keyObject.post_params['u'] : constants.HOOTHOOT_DEFAULT_USER;
      const hoothootPlatform = isAppAutomateSession ? 'all' : isMobile ? 'mobile' : 'desktop';
      const startErrorType = errorMessage.match(/start-error-firecmd/) ? 'start_error_firecmd' : ( errorMessage.match(/firecmd-error-safari-plugin/) ? 'safari_plugin_firecmd_error' : 'browser_startup_failure' );

      if (isAppAutomateSession) {
        if (opts.browserstackIssue) {
          hoothoot_user.uniqueUserEvent(username, 'app-automate', startErrorType, hoothootPlatform, keyObject.hoothootCanaryTags);
        }
      } else {
        hoothoot_user.uniqueUserEvent(username, 'automate', startErrorType, hoothootPlatform, keyObject.hoothootCanaryTags);
        keyObject.name = hostname;
        const extraParams = helper.automateErrorDataParams(keyObject);
        hoothoot_use.emit('automate_errors_data', 1, { event_type: startErrorType, platform: hoothootPlatform, product: 'automate', ...extraParams}, 15);
      }
    };

    //calling stopVideoRecording only for non mobile sessions as mobile does not implement /stop_video_rec
    if(errorMessage == "start-error" && !isMobile){
      return stopVideoRecording(keyObject, handlerCallback, "start-error");
    } else {
      return handlerCallback();
    }
  }
  // Start Errors and Firecmd Errors are handled above
  // Below code is only triggered in case of SO Timeouts

  var keyObject = constants.global_registry[clientSessionID];
  var timeoutObject = constants.timeout_registry[clientSessionID];
  if(typeof timeoutObject !== 'undefined') {
    clearTimeout(timeoutObject.timeoutID);
  }

  if(!keyObject) {
    ha.getData(clientSessionID, function(error, result) {
      if(error || !result){
        miscLogger("REDIS-NO-SESSION", "Session not present in Redis. May have already been stopped: " + clientSessionID, LL.WARN);
        var data = JSON.stringify({value: {message: (e ? "Session terminated" : errorMessage)}, sessionId: clientSessionID, "status": 13});
        instrumentationStats('Session Terminated', {}, 'Not Found in Redis after BSF', data)
        helper.respondWithError(request, response, data);
        return;
      }
      keyObject = result;
      constants.global_registry[clientSessionID] = keyObject;
      if (keyObject) {
        handleError(request, response, e, hostname, clientSessionID, errorMessage, errorScreenshot, originalDate, keyObject, emailData, opts, keyObject.appTesting && ((Object.values(constants.MID_SESSION_BROWSERSTACK_ERROR_BUCKET)).indexOf(errorMessage) > -1));
      }
    });
  } else {
    handleError(request, response, e, hostname, clientSessionID, errorMessage, errorScreenshot, originalDate, keyObject, emailData, opts, keyObject.appTesting && ((Object.values(constants.MID_SESSION_BROWSERSTACK_ERROR_BUCKET)).indexOf(errorMessage) > -1));
  }
}

// Only called in case of SO Timeouts and in case of mid session browserstack errors like device off adb, econnrefused etc
function handleError(request, response, e, hostname, clientSessionID, errorMessage, errorScreenshot, originalDate, keyObject, emailData, opts, isGenericBstackErrorSession = false) {
  myLock.ttlSemaphore("session_stop_semaphore", keyObject.rails_session_id).then(() => {
    miscLogger("handleError", "session_stop_semaphore acquired => " + keyObject.rails_session_id, LL.INFO);
    opts = opts || {};
    const isMobile = (opts && opts.device) || (keyObject && keyObject.device);
    const isAppAutomateSession = ((keyObject && keyObject.appTesting) || (opts && opts.appTesting));
    const isDetoxSession = isAppAutomateSession && isTrueString(keyObject.isDetox);
    var exception = ((e && !isGenericBstackErrorSession)? e.message : errorMessage);
    var emailData = hostname + " : " + clientSessionID + " : " + exception;
    if(keyObject) keyObject.stop_init_time = new Date();

    opts["user_id"] = keyObject ? keyObject.user_id : -1;
    opts["terminal_type"] = keyObject ? keyObject.terminal_type : "no-idea";
    opts["name"] = hostname;
    opts["sessionId"] = clientSessionID ? clientSessionID : keyObject.rails_session_id;

    seleniumStats((isGenericBstackErrorSession ? errorMessage : "selenium-node-failure"), opts, (e ? e.message : errorMessage), emailData, clientSessionID, (isGenericBstackErrorSession ? "mid-session" : "nodeError"), {isAppAutomate: keyObject.appTesting, isDetox: isDetoxSession}); // So timeout selenium-node-failure zombie alerting

    clientSessionID = clientSessionID ? clientSessionID : keyObject.rails_session_id;

    if(!keyObject.pendingDelete && constants.global_registry[clientSessionID]) {
      constants.global_registry[clientSessionID].pendingDelete  = true;
      keyObject.captureCrash = true;

      pubSub.publish(constants.updateKeyObject, {
        session: clientSessionID,
        changed: {
          pendingDelete: true,
          captureCrash: true
        }
      });
      let { releaseUrl, releasePostParams } = helper.buildRailsReleaseUrlParams(keyObject, constants.global_registry[clientSessionID], exception);
      browserstack.postBrowserStack(releaseUrl, releasePostParams, undefined, undefined, function(bsdata) {
        var json_response = JSON.parse(bsdata);
        if(json_response.noErrorScreenshot == "true") {
          var screenshot = undefined;
        } else {
          var screenshot = errorScreenshot;
        }
        let genericErrorKey = "SO_TIMEOUT";
        let genericErrorMessage = null;
        if(isGenericBstackErrorSession) {
          genericErrorKey = constants.MID_SESSION_BROWSERSTACK_ERROR_RAWLOGS_MESSAGE[errorMessage];
          genericErrorMessage = e.message;
        }
        genericErrorKey = (isAppAutomateSession && errorMessageConstants.APP_AUTOMATE_ERROR_MESSAGES[genericErrorKey] !== undefined) ? errorMessageConstants.APP_AUTOMATE_ERROR_MESSAGES[genericErrorKey] : genericErrorKey;

        miscLogger(genericErrorKey, "Encountered " + errorMessage + " for " + clientSessionID, LL.INFO);
        if(keyObject && keyObject.user) {
          var hoothootPlatform = keyObject.appTesting ? 'all' : isMobile ? 'mobile' : 'desktop';
          hoothoot_user.uniqueUserEvent(keyObject.user, (keyObject.appTesting ? 'app-automate' : 'automate'), (isGenericBstackErrorSession ? errorMessage : 'so_timeout'), hoothootPlatform, keyObject.hoothootCanaryTags);
          const extraParams = helper.automateErrorDataParams(keyObject);
          if (!keyObject.appTesting) hoothoot_use.emit('automate_errors_data', 1, { event_type: 'so_timeout', platform: hoothootPlatform, product: 'automate', ...extraParams}, 15);
        }

        maskSensitiveInformation(request, keyObject);
        helper.convertUnicodeToASCIIForSendKeys(request);

        const logErrReqData = `[${request.log_date}] ${request.method} ${request.url.replace("/wd/hub", "")} ${request.log_data}`;
        exports.sessionLog(keyObject, "TIMED_OUT_REQUEST", helper.s3LogFormat("REQUEST", helper.getDate(), logErrReqData));
        exports.addStopToRawLogs(keyObject, clientSessionID, genericErrorKey, 1, true, screenshot, genericErrorMessage);

        // defining the response to be sent
        let messageToBeSentInResponse = (e ? "Unable to communicate to node" : errorMessage);
        if(isGenericBstackErrorSession)
          messageToBeSentInResponse = (e ? e.message.toString() : "Unable to communicate to node");
        const data = JSON.stringify({value: {message: messageToBeSentInResponse}, sessionId: "", "status": constants.SELENIUM_ERROR_CODE});
        if(keyObject) {
          /**
           * assigning the last response here itself since we send the last request and last response
           * data via `pingDataToStats` which is called before ending the response.
           */
          keyObject.lastResponseStatus = `200::${constants.SELENIUM_ERROR_CODE}`;
          helper.pingDataToStats(keyObject);
          helper.checkNonZeroStatusErrors(keyObject);
          helper.checkUdpKeystoSend(keyObject);
          // not calling with params apart from keyObject because no delete/timeout publish in flow
          helper.sessionRemovedFromRegionHook(keyObject);
        }

        // This is back-up delete if the user had made any other request and the data was re-populated before delete
        ha.deleteData(clientSessionID, function(error, result) {
          if(constants.timeout_registry[clientSessionID]) {
            clearTimeout(constants.timeout_registry[clientSessionID].timeoutID);
          }
          delete constants.global_registry[clientSessionID];
          delete constants.timeout_registry[clientSessionID];
          delete constants.sessions_registry[clientSessionID];
          if(constants.stop_sessions_registry[opts['os']])
            delete constants.stop_sessions_registry[opts['os']][clientSessionID];
        });

        if (!((keyObject || {}).appTesting)) {
          instrumentationStats('SO Timeout', keyObject, '', data)
        }
        helper.respondWithError(request, response, data);
      });
    }
    var sessionKeyObj = constants.global_registry[clientSessionID];
    if (sessionKeyObj) {
      var zombie_kind = sessionKeyObj.appTesting ? "app_automation_session_stats" : "automation_session_stats"
      let zombieData = {
        "sessionid": clientSessionID,
        "bs_latency_mean": (sessionKeyObj.hubTime || 0) / 1000.0,
        'inside_bs_network_time': sessionKeyObj.appTesting ? null : (sessionKeyObj.insideHubTime || 0),
        "number_of_requests_nginx":  (sessionKeyObj.request_count || 0),
        "customer_session_duration": ((sessionKeyObj.hubTime || 0) - (sessionKeyObj.insideHubTime || 0)) / 1000.0,
        "last_opened_url": sessionKeyObj.georestricted_region ? null : helper.getSanitizedStringForZombie(sessionKeyObj.lastOpenedUrl),
        "first_opened_url": sessionKeyObj.georestricted_region ? null : helper.getSanitizedStringForZombie(sessionKeyObj.firstOpenedUrl),
        "kind": zombie_kind,
      }

      if (sessionKeyObj.appTesting) {
        const product = { performance: {} };
        product.performance.total_inside_bs_time = (sessionKeyObj.insideHubTime || 0) / 1000.0;
        zombieData["product"] = product;
      } else {
        const soTimeoutBucket = soTimeoutBucketer.getSoTimeoutBucket(sessionKeyObj);
        if (exception === 'Hub Timeout' && soTimeoutBucket.length > 0){
          zombieData["secondary_diagnostic_reasons"] = soTimeoutBucket;
        }
      }
      helper.PingZombie(zombieData)
      if(!isGenericBstackErrorSession) {
        var bs_bucket = null;
        if(sessionKeyObj.lastRequest == "POST:move_to") {
          bs_bucket = "customer";
        } else if(sessionKeyObj.lastRequest == "POST:size" || (sessionKeyObj.lastRequest == "POST:async_script" && sessionKeyObj.browser.match(/firefox/i))){
          bs_bucket = "browser";
        } else if(request && request.loadingHash && !sessionKeyObj.tunnel){
          bs_bucket = "page load";
        }
        if(bs_bucket){
          helper.PingZombie({
            "sessionid": clientSessionID,
            "bs_bucket": bs_bucket,
            "kind": zombie_kind
          });
        }
      }
    }

    ha.deleteData(clientSessionID, function(error, result) {
      if(constants.timeout_registry[clientSessionID]) {
        clearTimeout(constants.timeout_registry[clientSessionID].timeoutID);
      }
      pubSub.publish(constants.sessionTimeout, clientSessionID);
      delete constants.global_registry[clientSessionID];
      delete constants.timeout_registry[clientSessionID];
      delete constants.sessions_registry[clientSessionID];
      if(constants.stop_sessions_registry[opts['os']])
        delete constants.stop_sessions_registry[opts['os']][clientSessionID];
    });
  }).catch(() => {
    let genericErrorKey = "SO_TIMEOUT";
    if(isGenericBstackErrorSession)
      genericErrorKey = errorMessage.toUpperCase();
    miscLogger(genericErrorKey + " Race Condition", "IGNORING " + genericErrorKey + " - Encountered " + genericErrorKey + " after session limit reached for " + clientSessionID, LL.WARN);
    return helper.endConflictResponse(response);
  });
};

var addStopToRawLogs = function(keyObject, key, reason, status, delay, errorScreenshot, errorStack) {
  errorStack = errorStack || "{}";
  errorStack = errorStack.replace(/\/usr\/local\/.browserstack\//g, "");
  var logdata = "{\"status\":" + (status ? status : 0) + ",\"sessionId\":\"" + key + "\",\"value\":{\"message\":\""+ reason + "\"" + (errorScreenshot? ",\"screen\": \"" + errorScreenshot + "\"" : "") +"}," + "\"errorStack\":" + errorStack + "}\r\n";

  if (constants.global_registry[keyObject.rails_session_id]) {
    constants.global_registry[keyObject.rails_session_id].lastRawLog = reason;
  }

  if(delay) {
    setTimeout(function() {
      exports.sendSessionLog(keyObject, "STOP_SESSION", helper.getDate(), logdata);
    }, 10000);
  } else {
    exports.sendSessionLog(keyObject, "STOP_SESSION", helper.getDate(), logdata);
  }
}

var fetchConsoleLogs = function(keyObject, callback) {
  if (keyObject && keyObject.consoleLogsEnabled) {
    helper.retrieveUploadConsoleLogs(keyObject, () => {
      exports.miscLogger('fetchConsoleLogs', `console logs fetched for session ${keyObject.rails_session_id}`, LL.INFO);
    });
  }
  callback && callback();
}

// Sends command to chrome-har-capturer-server running on the
// mobile host to write the HAR file.
// Upload is done inside cleanup.
// AUTOMATE CHROME ONLY
// Other products use mitmproxy for networkLogs.
var generateNetworkLogs = function(keyObject, callback) {
  if(!(keyObject && keyObject.networkLogs && keyObject.os === 'android') || keyObject.appTesting) {
    callback && callback();
    return;
  }

  const tryStopCapture = (port) => {
    return requestlib.call({
      method: "GET",
      hostname: keyObject.rproxyHost,
      port: port,
      path: util.format('/stop_capture_v2?device=%s&sessionid=%s', keyObject.device, keyObject.rails_session_id),
      headers: requestlib.appendBStackHostHeader(keyObject.name),
    });
  };

  // Try new port first, then fallback to old port if it fails
  tryStopCapture(constants.chrome_har_capturer_server_port_v2)
    .then((res) => {
      miscLogger(
        "STOP_HAR_RECORD_ANDROID",
        "Stopped successfully for session: " + keyObject.rails_session_id + "responseStatusCode: " + ( res.statusCode || "undefined" ) + " data: " + ( res.data || "undefined" ),
        LL.INFO
      );
      callback && callback();
    })
    .catch((e) => {
      miscLogger("STOP_HAR_RECORD_ANDROID", "HAR stop failed on new port for session ID: " + keyObject.rails_session_id + "error: " + e.toString() + ". Trying old port...", LL.INFO);
      
      // Try old port as fallback
      tryStopCapture(constants.chrome_har_capturer_server_port_v1)
        .then((res) => {
          miscLogger(
            "STOP_HAR_RECORD_ANDROID",
            "Stopped successfully on old port for session: " + keyObject.rails_session_id + "responseStatusCode: " + ( res.statusCode || "undefined" ) + " data: " + ( res.data || "undefined" ),
            LL.INFO
          );
          callback && callback();
        })
        .catch((e) => {
          miscLogger("STOP_HAR_RECORD_ANDROID", "HAR stop failed on both ports for session ID: " + keyObject.rails_session_id + "error: " + e.toString(), LL.INFO);
          callback && callback();
        });
    });
}

// Fetches Console Logs and Network Logs wherever applicable.
var fetchAdditionalLogs = function (keyObject, callback) {
  fetchConsoleLogs(keyObject, function() {
      generateNetworkLogs(keyObject, callback);
  });
}

exports.fetchAdditionalLogs = fetchAdditionalLogs;
exports.addStopToRawLogs = addStopToRawLogs;
exports.helper = helper;
exports.constants = constants;
exports.handleError = handleError;
