module.exports = {
  'extends': 'airbnb',
  'parserOptions': {
    'sourceType': 'script',
    'ecmaVersion': 2018
  },
  'rules': {
    // needed as node >= 5 treats all modules as strict mode default
    'strict': [
      'error',
      'global'
    ],
    // Trailing commas in function declarations and function calls
    // are valid syntax since ECMAScript 2017
    'comma-dangle': [ 'error', {
      'arrays': 'always-multiline',
      'objects': 'always-multiline',
      'imports': 'always-multiline',
      'exports': 'always-multiline',
      'functions': 'ignore'
    }],
    'camelcase': 1,
    'no-param-reassign': 0,
    'max-len': 1,
    'import/order': 0,
    'operator-linebreak': 0,
    'implicit-arrow-linebreak': 0,
    'no-underscore-dangle': 0,
    'no-unused-expressions': 0,
    'no-unused-vars': 1,
    // Node 4.5 does not support the new syntax
    'prefer-destructuring': [ 'error', {
      'object': false,
      'array': false
    }],
  },
  'env': {
    'node': true,
    'mocha': true
  }
};
