var constants = require('./constants'),
    hub = require('./hub'),
    HubLogger = require('./log'),
    LL = constants.LOG_LEVEL,
    helper = require('./helper');

const w3CHelper = require('./helpers/w3CHelper');

var invalidJSON = function(request, response, data, e) {
  hub.handleRequest401(request, response, 'Invalid JSON', 'Caps JSON Invalid');
  var str = "JSON parse data: " + data + ", error: " + e.toString();
  HubLogger.exceptionLogger(str, hub.getClientAddress(request), request.url);
  HubLogger.seleniumStats("json-data-error", {"name" : hub.getClientAddress(request)}, e.toString(), "User data did not result in a valid json:\n" + data, request.url, "userError");
};

var checkDesiredCaps = function(request, response, caps, data) {
  try {
    const { desiredCapabilities, capabilities } = caps;
    if(!helper.isHash(desiredCapabilities) && !helper.isHash(capabilities)) {
      response.writeHead(422, {'content-type': 'application/json; charset=utf-8'});
      let dataToSend = JSON.stringify({value: {message: "Neither Capabilities nor Desired Capabilities was defined"}, sessionId: "", "status": 13});
      helper.respondWithError(request, response, dataToSend);
      HubLogger.instrumentationStats('Invalid Capabilities', {}, '', dataToSend);
      HubLogger.seleniumStats("desired-caps-error", {"name" : hub.getClientAddress(request)}, "", "User data did not result in a valid desired caps:\n" + data, request.url, "userError");
      return false;
    } else if (!helper.isHash(desiredCapabilities)) {
      const { firstMatch: [firstMatchEntry = {}], alwaysMatch } = w3CHelper.getFirstMatchAndAlwaysMatch(caps);
      caps.desiredCapabilities = { ...firstMatchEntry, ...alwaysMatch };
    }
  } catch (error) { 
    HubLogger.exceptionLogger(`Exception in checkDesiredCaps ${error.toString()}\nCapabilities: ${JSON.stringify(caps)}`);
    return false;
  }
  return true;
};

const checkBasicAuth = (request, response, basic_auth, caps) => {
  const authNotAvailable = basic_auth[0] == null || basic_auth[1] == null || basic_auth[0] == "" || basic_auth[1] == "";
  if (authNotAvailable) {
    try {
      hub.handleRequest401(request, response, 'Authorization Required', 'Auth Data Not Available', basic_auth[0]);
    } catch (e) {
      hub.handleRequest401(request, response, 'Authorization Required', 'Auth Data Not Available Retry', basic_auth[0]);
    } finally {
      helper.SanitizeCaps(caps);
      var cap_data = JSON.stringify(caps) || '';
      HubLogger.miscLogger("invalid-auth-error desired caps", cap_data, LL.DEBUG);
    }
  }
  return Boolean(!authNotAvailable);
};

const getBrowserstackSpecificCapability = (caps, ossCapName, w3cCapName) => {
  // only considering the first entry of firstMatch
  const { firstMatch: [firstMatch = {}], alwaysMatch } = w3CHelper.getFirstMatchAndAlwaysMatch(caps);

  const firstMatchEntry = helper.nestedKeyValue(firstMatch, ['bstack:options', w3cCapName]);
  const alwaysMatchEntry = helper.nestedKeyValue(alwaysMatch, ['bstack:options', w3cCapName]);
  const desiredCapsOptionsEntry = helper.nestedKeyValue(caps, ['desiredCapabilities', 'bstack:options', w3cCapName]);
  const desiredCapsEntry = helper.nestedKeyValue(caps, ['desiredCapabilities', ossCapName]);

  return alwaysMatchEntry || firstMatchEntry || desiredCapsOptionsEntry || desiredCapsEntry;
};

const deleteBrowserstackSpecificCapability = (caps, ossCapName, w3cCapName) => {
  // only considering the first entry of firstMatch
  const { firstMatch: [firstMatch = {}], alwaysMatch } = w3CHelper.getFirstMatchAndAlwaysMatch(caps);
  const firstMatchOptions = firstMatch['bstack:options'] || {};
  delete firstMatchOptions[w3cCapName];

  const alwaysMatchOptions = alwaysMatch['bstack:options'] || {};
  delete alwaysMatchOptions[w3cCapName];

  const desiredOptionsCaps = helper.nestedKeyValue(caps, ['desiredCapabilities', 'bstack:options']) || {};
  delete desiredOptionsCaps[w3cCapName];

  const rawOptionsCaps = helper.nestedKeyValue(caps, ['capabilities', 'bstack:options']) || {};
  delete rawOptionsCaps[w3cCapName];

  const w3COptionsCaps = helper.nestedKeyValue(caps, ['bstack:options']) || {};
  delete w3COptionsCaps[w3cCapName];

  const desiredCaps = caps.desiredCapabilities || {};
  delete desiredCaps[ossCapName];

  return caps;
};

const addW3CFlagIfSpecifiedBrowserstackOptions = (caps, app_testing) => {
  // only considering the first entry of firstMatch
  const { firstMatch: [firstMatch = {}], alwaysMatch } = w3CHelper.getFirstMatchAndAlwaysMatch(caps);
  // Prioritizing alwaysMatch over firstMatch's first entry
  const browserstackOptions = alwaysMatch['bstack:options'] || firstMatch['bstack:options'];
  if (!app_testing && helper.isHash(browserstackOptions)) {
    browserstackOptions['useW3C'] = 'true';
  }
  return caps;
};

module.exports = {
  invalidJSON,
  checkDesiredCaps,
  checkBasicAuth,
  getBrowserstackSpecificCapability,
  deleteBrowserstackSpecificCapability,
  addW3CFlagIfSpecifiedBrowserstackOptions
};
