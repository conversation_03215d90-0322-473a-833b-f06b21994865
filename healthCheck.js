const constants = require('./constants');
const redisClient = require('./redisUtils').redisClient;
const HubLogger = require('./log');
const kafkaProducer = require('./lib/kafka/kafkaProducer');

exports.checkHubsHealth = async function(callback){
  const redisConnectionResponse = {
    "componentType": "datastore"
  };
  try { // Redis check
    await redisClient.ping();
    redisConnectionResponse.status = "pass";
  } catch (error) {
    redisConnectionResponse.status = "fail";
  } finally {
    redisConnectionResponse.time = Date.now();
  }

  const terminalSuccessRateResponse = {
    "componentType": "terminal-service",
    "observedUnit": "percent",
    "time": Date.now(),
    "observedValue": exports.termialSessionList.getTerminalSuccessRate()
  };
  const value = terminalSuccessRateResponse.observedValue;
  if( value > 5 ){
    terminalSuccessRateResponse.stauts = "pass";
  } else {
    terminalSuccessRateResponse.status = "fail";
    terminalSuccessRateResponse.output = "Hub did not connected to terminals for a long time";
  }

  const kafkaConnectionResponse = {
    "componentType": "logs-uploader",
    "notes": []
  };

  const finalResponse = {
    status: "pass",
    "description": "health of Selenium-hub",
    "notes": [],
    "checks": {
      "redis-connection": redisConnectionResponse,
      "terminal-success-rate": terminalSuccessRateResponse,
      "kafka-connection": kafkaConnectionResponse
    }
  };

  payloads = [
      { topic: constants.kafkaConfig.instrumentation_logs_topic, messages: JSON.stringify({value: 'This message is for checking kafka connection' })}
  ];

  kafkaProducer.kafkaHealthCheckProducer.send( payloads , (err, data) => {
    if( err ){
      HubLogger.miscLogger("kafkaHealthCheck", "Kafka connection failed", constants.LOG_LEVEL.WARN);
      kafkaConnectionResponse.status = "fail";
    }else{
      kafkaConnectionResponse.status = "pass";
    }
    sendFinalResponse(finalResponse, callback);
  });
};

function sendFinalResponse(finalResponse, callback){
  let criticalComponentResponseStatus  = [ finalResponse.checks['redis-connection'].status, finalResponse.checks['terminal-success-rate'].status ];

  if(criticalComponentResponseStatus.includes("fail")){
    finalResponse["status"] = "fail";
  }
  callback(finalResponse);
}
exports.sendFinalResponse = sendFinalResponse
class TerminalSession{
  constructor(sessionStatus, sessionId){
    this.status = sessionStatus;
    this.sessionId = sessionId;
    this.next = null;
  }
}

class TerminalSessionList{
  constructor(maxSize = 10){
    this.maxSize = maxSize;
    this.head = null;
    this.tail = null;
    this.size = 0;
    this.result = 0;
  }

  addSession(sessionStatus, sessionId){
    if(sessionStatus == null) return;
    let session = new TerminalSession(sessionStatus, sessionId);
    this.result = this.result + ((sessionStatus == true) ? 1 : 0);
    if(this.head == null){
      this.head = session;
      this.tail = session;
    }else{
      this.tail.next = session;
      this.tail = session;
    }
    if(this.size + 1 > this.maxSize){
      const next = this.head.next;
      this.result = this.result + ((this.head.status == true) ? -1 : 0);
      delete this.head;
      this.head = next;
    }else{
      this.size++;
    }
  }

  getTerminalSuccessRate(){
    if(this.size == 0)
      return 100;
    return (this.result / this.size) * 100;
  }
}

exports.termialSessionList = new TerminalSessionList(constants.terminal_session_count);

