{"name": "ws-reconnect-proxy", "version": "1.0.0", "description": "Proxy Server that is between a ws server and a ws client. In case of either server / client disconnects graceful or otherwise - initiates/ supports reconnection .", "main": "index.js", "scripts": {"start": "NODE_ENV=dev node cluster.js", "start:prod": "NODE_ENV=prod LOG_PATH='./logs/proxy.log' node cluster.js | ./node_modules/.bin/rotate-logs", "test": "NODE_ENV=test ./node_modules/.bin/nyc ./node_modules/.bin/mocha 'test/**/*.js' --exit", "format": "./node_modules/.bin/prettier --write .", "eslint": "./node_modules/.bin/eslint *.js", "eslint:test": "./node_modules/.bin/eslint test/**/*.js", "eslint:fix": "./node_modules/.bin/eslint *.js --fix", "test:eslint:fix": "./node_modules/.bin/eslint test/*.js --fix"}, "repository": {"type": "git", "url": "git+https://github.com/browserstack/ws-reconnect-proxy.git"}, "author": "", "license": "ISC", "bugs": {"url": "https://github.com/browserstack/ws-reconnect-proxy/issues"}, "homepage": "https://github.com/browserstack/ws-reconnect-proxy#readme", "dependencies": {"@vrbo/pino-rotating-file": "^3.0.0", "pino": "^6.11.3", "pino-pretty": "^5.1.2", "uuid": "^8.3.2", "ws": "^7.5.10", "aws-sdk": "2.1054.0", "browserstack-dwh": "git+ssh://**************/browserstack/dwh_node.git#51d116bc7049fa691d30dc69b16cfc5a47e77a79", "hoothoot": "git+ssh://**************/browserstack/node-hoothoot.git#fff9d9e51aaa9c8bb490d1198c5c92a2b4c28aea"}, "devDependencies": {"chai": "^4.3.4", "eslint": "^7.32.0", "mocha": "^9.0.2", "nyc": "^15.1.0", "prettier": "^2.3.2", "sinon": "^11.1.2"}}