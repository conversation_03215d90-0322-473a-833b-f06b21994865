const helper = require('./helper');
const logger = require('./loggerFactory');
const { getMetrics } = require('./metrics');

class Instrumentation {
  constructor() {
    this.pushMetrics = this.pushMetrics.bind(this);
  }

  pingZombie(dataJsonObject) {
    helper.sendToPager(dataJsonObject);
    logger.info(`[PAGE] Sent data to pager: ${JSON.stringify(dataJsonObject)}`);
  }

  /**
   * Logs the metrics information.
   * Here you can configure endpoint to push metrics
   */
  pushMetrics() {
    const metric = getMetrics();
    helper.pushStatsToHootHoot('cdp_proxy_stats', metric);
    logger.info(`[METRICS] ${JSON.stringify(metric)}`);
  }
}

module.exports = Instrumentation;
