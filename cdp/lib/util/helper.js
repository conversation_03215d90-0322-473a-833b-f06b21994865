const { Events, Client: EdsClient, DWH } = require('browserstack-dwh');
const logger = require('./loggerFactory');
const { config } = require('../config/constants');
const hoothoot = require('hoothoot')(config.rootConfig.metrics_region);
const http = require('http');
const https = require('https');

exports.sendToPager = (dataJsonObject) => {
  const environment = process.env.NODE_ENV === 'prod' ? 'production' : 'staging';
  try {
    const payloadLength = DWH.send('udp', 'pager', dataJsonObject, environment);
    logger.info(`[PAGER] Payload length:${payloadLength}`);
  } catch (err) {
    logger.info(`[PAGER] Exception ${err.toString()} while sending data: ${JSON.stringify(dataJsonObject)}`);
  }
}

exports.sendToEDS = (data) => {
  const dataToSend = {};
  data.hashed_id = data.sessionid || data.hashed_id || data.session_id;

  /* eslint-disable-next-line default-case */
  switch (data.kind) {
    case 'automation_session_stats':
      dataToSend.event_type = Events.AUTOMATE_TEST_SESSIONS;
      delete data.kind;
      delete data.sessionid;
      break;
    case Events.AUTOMATE_SESSION_TIME_COMPONENTS:
      dataToSend.event_type = Events.AUTOMATE_SESSION_TIME_COMPONENTS;
      delete data.kind;
      break;
    case Events.AUTOMATE_TEST_SESSIONS:
      dataToSend.event_type = Events.AUTOMATE_TEST_SESSIONS;
      delete data.kind;
      delete data.sessionid;
      break;
    case Events.AUTOMATE_ERROR_DATA:
      dataToSend.event_type = Events.AUTOMATE_ERROR_DATA;
      delete data.kind;
      break;
    case Events.AUTOMATE_PERFORMANCE_DATA:
      dataToSend.event_type = Events.AUTOMATE_PERFORMANCE_DATA;
      delete data.kind;
      delete data.sessionid;
      break;
  }

  dataToSend.data = data;
  if (dataToSend.event_type) {
    try {
      logger.debug(`Sending data to EDS ${JSON.stringify(dataToSend)}`);
      dwhClient
        .send(dataToSend, (err) => {
          if (err) {
            logger.error(
              `Exception ${err.toString()} while sending to EDS, data: ${dataToSend}`
            );
          }
        })
        .catch((err) => {
          logger.error(
            `Promise Exception ${err.toString()} while sending to EDS, data: ${dataToSend}`
          );
        });
    } catch (err) {
      logger.info(
        `Exception ${err.toString()} while sending to EDS, data: ${dataToSend}`
      );
    }
  }
};

exports.pushStatsToHootHoot = (hoothootKey, metrics, uniqueId) => {
  Object.keys(metrics).forEach(function (key) {
    if (typeof metrics[key] == 'object') {
      Object.keys(metrics[key]).forEach(function (nestedKey) {
        hoothoot.emit(hoothootKey, metrics[key][nestedKey], {
          genre: key + '_' + nestedKey,
          uniqueId: uniqueId,
        });
      });
    } else {
      hoothoot.emit(hoothootKey, metrics[key], {
        genre: key,
        uniqueId: uniqueId,
      });
    }
  });
};

exports.request = (options) => {
  return new Promise((resolve, reject) => {
    options.scheme = options.scheme || 'http';
    const nodeRequest =
      options.scheme === 'http' ? http.request : https.request;

    const req = nodeRequest(options, (res) => {
      const data = [];
      res.on('data', (chunk) => {
        data.push(chunk);
      });
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          data: Buffer.concat(data).toString(),
          buffer: Buffer.concat(data),
        });
      });
      res.on('error', (err) => {
        err.type = 'ResponseError';
        reject(err);
      });
    });
    req.on('error', (err) => {
      err.type = 'RequestError';
      reject(err);
    });
    if (typeof options.body !== 'undefined') {
      req.write(options.body);
    }
    req.end();
  });
};
