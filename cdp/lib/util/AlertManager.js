const helper = require('./helper');
const querystring = require('querystring');
const logger = require('./loggerFactory');
const { config } = require('../config/constants');

class AlertManager {
  constructor() {
    this.sendAlert = this.sendAlert.bind(this);
  }

  async sendAlert(title, subject, message) {
    const alertBodyToSend = querystring.stringify({
      people: config.rootConfig.alertReceivers,
      subject: subject,
      message: message,
      mobile: false,
      title,
    });
    const alertOptions = {
      method: 'POST',
      path: config.rootConfig.alertPath,
      hostname: config.rootConfig.alertHost,
      port: config.rootConfig.alertPort,
      body: alertBodyToSend,
      scheme: 'http',
      headers: {
        'content-type': 'application/x-www-form-urlencoded; charset=utf-8',
        accept: 'application/json',
      },
    };

    try {
      const { statusCode } = await helper.request(alertOptions);
      logger.info(
        `Sent alert to ${config.rootConfig.alertReceivers} with subject: ${subject} status: ${statusCode}`
      );
    } catch (alertError) {
      logger.error(
        `Failed to send alert to ${config.rootConfig.alertReceivers} with subject: ${subject}. Error: ${alertError}`
      );
    }
  }
}

module.exports = AlertManager;
