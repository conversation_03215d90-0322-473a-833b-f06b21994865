'use strict';

const { Router } = require('tiny-request-router');
const { SessionsController } = require('../controllers/sessions/SessionsController');

const router = new Router();

// This should only have routes mapped to a controller function.

/**
 * TODO: Add all the hub routes here
 * Current Hub Nginx Whitelisted URLs:
 * /check - code not on hub, directly returned from hubnginx
 * /health_check
 * /health_check_v2
 * /next_hubs - code not on hub, directly returned from hubnginx
 * /wd/hub
 * /wd/hub/session
 * /playwright
 * /puppeteer
 * /session
 * /session_timedout
 * /status
 * /stop_ui
 * /stop_limit
 * /stop_limit_aa_freemium
 * /stop_smd
 * /post_bs_check_url_data
 * /callback_done
 */

const sessionsController = new SessionsController();

router.post('/stop_ui', sessionsController.stopByBs.bind(sessionsController));
router.post('/stop_limit', sessionsController.stopByBs.bind(sessionsController));
router.post('/stop_smd', sessionsController.stopByBs.bind(sessionsController));
router.post('/stop_limit_aa_freemium', sessionsController.stopByBs.bind(sessionsController));
router.all('/session/get_session_data(.*)', sessionsController.getSessionData.bind(sessionsController));
router.all('/session_timedout(.*)', sessionsController.sessionTimedout.bind(sessionsController));

module.exports = { router };
