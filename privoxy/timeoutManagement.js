'use strict';

const redisClient = require('../redisUtils').redisClient;
const HubLogger = require('../log');
const {
  PRIVOXY_KEEP_ALIVE_TIMEOUTS,
  PRIVOXY_USER_KEEP_ALIVE_TIMEOUTS,
  LOG_LEVEL: LL,
} = require('../constants');

/**
 * Fetches the privoxy Keep-Alive timeout value from
 * redis, specific to group and user.
 *
 * @params groupId{int}
 * @params userId{int}
 *
 * @returns {int} timeout value specific to group or user if successfull,
 * else returns undefined.
 */
const userPrivoxyKeepAliveTimeout = async (groupId, userId) => {
  try {
    const result = parseInt(
      (await redisClient.hget(PRIVOXY_KEEP_ALIVE_TIMEOUTS, groupId)) ||
        (await redisClient.hget(PRIVOXY_USER_KEEP_ALIVE_TIMEOUTS, userId)),
      10
    );
    if (!Number.isNaN(result)) {
      return result;
    }
  } catch (err) {
    HubLogger.miscLogger(
      'fireCommands',
      `Error while fetching privoxy keep alive timeout from redis: ${err}`,
      LL.INFO
    );
  }
  return null;
};

module.exports = {
  userPrivoxyKeepAliveTimeout,
};
