'use strict';

const { isNotUndefined } = require('../typeSanity');
const { userPrivoxyKeepAliveTimeout } = require('./timeoutManagement');

/**
 * Configures the request parameters in firecommand for
 * the privoxy keepAlive timeout.
 *
 * @params reqOptions{Hash} - Request body for firecommand on terminal
 * @params userInfo{Hash({userId, groupId})} - Meta information of user.
 *
 * @returns {undefined}
 */
const configurePrivoxyTimeout = async (reqOptions, userInfo) => {
  const { userId, groupId } = userInfo;
  const result = await userPrivoxyKeepAliveTimeout(groupId, userId);
  if (isNotUndefined(result)) {
    reqOptions.privoxyKeepAliveTimeout = result;
  }
};

module.exports = {
  configurePrivoxyTimeout,
};
