'use-strict';

/*
This file contains the regex list to whitelist adb commands coming to custom executor.

The regexes here differ from platform in the sense that they have caret instead of \A and dollar instead of \Z
Since in ruby \A and \Z are needed for matching whole text whereas ^ and $ match individual lines
But here ^ and $ match against whole text and \A \Z match against A and Z literals

(/^[a-z]+(\.\w+)+\s*$/).test("com.rand.package")
true

(/^[a-z]+(\.\w+)+\s*$/).test("com.rand.package\nadbshell")
false

(/\A[a-z]+(\.\w+)+\s*\Z/).test("com.rand.package")
false

(/\A[a-z]+(\.\w+)+\s*\Z/).test("Acom.rand.packageab Z")
true
*/

exports.ADB_COMMAND_EXECUTOR_VALIDATIONS = new Map([
  [
    "dumpsys",
    {
      command: {
        gfxinfo: {
          regex: /^[a-z]+(\.\w+)+\s{0,5}(framestats|reset)?\s{0,5}$/,
          maxWords: 4,
        },
        input: {
          regex: /^\s{0,5}$/,
          maxWords: 2,
        },
        package: {
          regex: /^[a-z]+(\.\w+)+\s{0,5}$/,
          maxWords: 3,
        },
        "telephony.registry": {
          regex: /^\s{0,5}$/,
          maxWords: 2,
        },
        bluetooth_manager: {
          regex: /^\s{0,5}$/,
          maxWords: 2,
        },
        activity: {
          regex: /^\s{0,5}-p\s{1,5}[a-z]+(\.\w+)+(\s{1,5}activities(\s{1,5}\|\s{0,5}grep\s{0,5}-E\s{0,5}"mResumedActivity")?|\s{1,5}services|\s{1,5}providers|\s{1,5}recents|\s{1,5}broadcasts|\s{1,5}intents|\s{1,5}permissions|\s{1,5}processes)?\s{0,5}$/,
          maxWords: 9,
        },
        netstats: {
          regex: /^\s{0,5}(detail)?\s{0,5}$/,
          maxWords: 3,
        },
        connectivity: {
          regex: /^\s{0,5}$/,
          maxWords: 2,
        },
        netpolicy: {
          regex: /^\s{0,5}$/,
          maxWords: 2,
        },
        network_management: {
          regex: /^\s{0,5}$/,
          maxWords: 2,
        },
        batterystats: {
          regex: /^\s{0,5}(--charged\s{0,5}[a-z]+(\.\w+)+|options|--checkin)\s{0,5}$/,
          maxWords: 4,
        },
        procstats: {
          regex: /^\s{0,5}(--hours\s{0,5}[0-9]+)\s{0,5}$/,
          maxWords: 4,
        },
        meminfo: {
          regex: /^\s{0,5}([a-z]+(\.\w+)+|[0-9]+)\s{0,5}(-d)?\s{0,5}?$/,
          maxWords: 4,
        },
        cpuinfo: {
          regex: /^\s{0,5}$/,
          maxWords: 2,
        },
        display: {
          regex: /^\s{0,5}$/,
          maxWords: 2,
        },
        power: {
          regex: /^\s{0,5}$/,
          maxWords: 2,
        },
        alarm: {
          regex: /^\s{0,5}$/,
          maxWords: 2,
        },
        location: {
          regex: /^\s{0,5}$/,
          maxWords: 2,
        },
        window: {
          regex: /^\s{0,5}(windows\s{0,5}\|\s{0,5}grep\s{0,5}-E\s{0,5}"(mCurrentFocus|mFocusedApp)(\|)?(mCurrentFocus|mFocusedApp)?"|displays)\s{0,5}$/,
          maxWords: 8,
        },
      },
    },
  ],
  [
    "getprop",
    {
      command: {
        "ro.cdma.home.operator.alpha": {
          regex: /^\s{0,5}$/,
          maxWords: 2,
        },
        "ro.system.build.version.sdk": {
          regex: /^\s{0,5}$/,
          maxWords: 2,
        },
        "gsm.sim.operator.alpha": {
          regex: /^\s{0,5}$/,
          maxWords: 2,
        },
        "ro.build.version.release": {
          regex: /^\s{0,5}$/,
          maxWords: 2,
        },
        "ro.boot.wifimacaddr": {
          regex: /^\s{0,5}$/,
          maxWords: 2,
        },
        "ro.product.manufacturer": {
          regex: /^\s{0,5}$/,
          maxWords: 2,
        },
        "ro.vendor.product.model": {
          regex: /^\s{0,5}$/,
          maxWords: 2,
        },
        "ro.board.platform": {
          regex: /^\s{0,5}$/,
          maxWords: 2,
        },
        "ro.oem_unlock_supported": {
          regex: /^\s{0,5}$/,
          maxWords: 2,
        },
        "ro.bootimage.build.fingerprint": {
          regex: /^\s{0,5}$/,
          maxWords: 2,
        },
        "-T": {
          regex: /^\s{0,5}$/,
          maxWords: 2,
        },
        "ro.build.version.oneui": {
          regex: /^\s{0,5}$/,
          maxWords: 2,
        },
      },
    },
  ],
  [
    "wm",
    {
      command: {
        "fixed-to-user-rotation": {
          regex: /^\s{0,5}(enabled|disabled)$/,
          maxWords: 3,
        },
        "set-fix-to-user-rotation": {
          regex: /^\s{0,5}(enabled|disabled)$/,
          maxWords: 3,
        },
      },
    },
  ],
]);
