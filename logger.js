'use strict';

const winston = require('winston');
const constants = require('./constants');
require('winston-daily-rotate-file');

const LL = constants.LOG_LEVEL;
const env = process.argv[2];
const isProductionEnv = env === 'Production';
const { Chitragupta } = require('chitragupta');

const logsFormatter = (options) => {
  const metaData = Object.keys(options.meta).map(value => options.meta[value]).join(' ');
  return `${metaData} ${options.level.toUpperCase()} ${options.message}`;
};

const customLogger = new (winston.Logger)({
  levels: LL,
  level: 'DEBUG',
  transports: isProductionEnv ? [
    new (winston.transports.DailyRotateFile)({
      filename: `${constants.hubLogsDir}/selenium-node-%DATE%.log.norotate`,
      datePattern: 'YYYY-MM-DD-HH',
      maxFiles: '14d',
      formatter: Chitragupta.jsonLogFormatter,
    }),
  ] : [
    new (winston.transports.Console)({
      stringify: JSON.stringify,
      formatter: logsFormatter,
      // formatter: Chitragupta.jsonLogFormatter,
    }),
  ],
});

if (constants.isProductionEnv) {
  customLogger.rewriters.push((level, msg, meta) => {
    meta.meta = constants.log_meta;
    // Following file path can be used to fetch data from Chitragupta
    meta.meta.file = `${constants.hubLogsDir}/selenium-node.log.norotate`;
    return meta;
  });
}

const basicMeta = {
  process: constants.hubName + process.pid,
  time: new Date().toISOString(),
};

const logAllowed = level => level <= constants.hub_log_level;

const basicLogFn = {
  debug: {
    fn: customLogger.DEBUG,
    level: LL.DEBUG,
  },
  info: {
    fn: customLogger.INFO,
    level: LL.INFO,
  },
  error: {
    fn: customLogger.ERROR,
    level: LL.ERROR,
  },
  warn: {
    fn: customLogger.WARN,
    level: LL.WARN,
  },
};

const basicLogger = {};

Object.entries(basicLogFn).forEach(([methodName, { fn, level }]) => {
  basicLogger[methodName] = (message, metaOptions = {}) => {
    if (!logAllowed(level)) {
      return;
    }
    fn(message, { ...basicMeta, ...metaOptions });
  };
});

module.exports = {
  customLogger,
  basicLogger
};
