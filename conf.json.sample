{"nodeTimeout": 60000, "startNodeTimeout": 60000, "snapshotJarTimeout": 40000, "idleTimeout": 90000, "bsTimeout": 30000, "bstack_api_host": "apidev.bsstag.com", "app_automate_bstack_api_host": "apidev.bsstag.com", "bstack_api_port": 443, "bs_host": "local.bsstag.com", "bs_port": 80, "bs_app_host": "local.bsstag.com", "bs_app_port": 80, "bs_scheme": "http", "hub_host": "localhost", "hub_port": 8080, "log_post_interval": 2000, "env_name": "local", "hubUrlSubRegionMapping": {"localhost": ["us-east-1a", "us-east-1b", "us-east-1c", "us-east-1d", "us-east-2a", "us-east-3b", "us-west-1c", "us-west-1e", "us-west-1c", "us-west-1d", "us-west-2a", "us-west-3a", "eu-west-1a", "eu-west-1b", "eu-west-1c", "eu-west-1d", "eu-central-1a", "ap-south-1a", "ap-south-1c", "ap-southeast-2a", "ap-southeast-2b", "ap-southeast-2c"]}, "cdpUrlSubRegionMapping": {"localhost": ["us-east-1a", "us-east-1b", "us-east-1c", "us-east-1d", "us-east-2a", "us-east-3b", "us-west-1c", "us-west-1e", "us-west-1c", "us-west-1d", "us-west-2a", "us-west-3a", "eu-west-1a", "eu-west-1b", "eu-west-1c", "eu-west-1d", "eu-central-1a", "ap-south-1a", "ap-south-1c", "ap-southeast-2a", "ap-southeast-2b", "ap-southeast-2c"]}, "logs_aws_key": "********************", "logs_aws_secret": "nyj2z7Un8biUmTThj/OGNGmczM/c5ioO9dirUv3L", "logs_put_aws_key": "logs_put_aws_key", "logs_put_aws_secret": "logs_put_aws_secret", "debugscreenshot_aws_key": "********************", "percy_screenshot_uploader_cert": "eyJuYW1lIjoicGVyY3lDZXJ0cyJ9", "debugscreenshot_aws_secret": "nyj2z7Un8biUmTThj/OGNGmczM/c5ioO9dirUv3L", "debugscreenshot_put_aws_key": "debugscreenshot_put_aws_key", "debugscreenshot_put_aws_secret": "debugscreenshot_put_aws_secret", "logs_aws_bucket": "bs-stag-selenium-logs-use", "app_ally_aws_key": "app_ally_aws_key", "app_ally_aws_secret": "app_ally_aws_secret", "app_ally_aws_bucket": "app_ally_aws_bucket", "app_ally_aws_region": "app_ally_aws_region", "blocked_ips": [], "blocked_ips_regex": "", "mail_to": [], "ha_server": "127.0.0.1", "ha_port": "11211", "redis_server": "localhost", "redis_port": "6379", "logkey": "heya", "railstoken": "selautomate", "hub_name": "hub1", "not_allowed_requests": ["POST:maximize"], "browserstack.region": "us-east-1", "browserstack.sub_region": "us-east-1a", "non_pipe_urls": ["POST:value"], "functional_testing_domain": "hub-ft.browserstack.com", "other_hub_servers": [], "zombie_server": "zombiestaging.browserstack.com", "zombie_port": 8000, "eds_server": "edsstaging.bsstag.com", "eds_port": 8553, "eds_key": "abcdef", "multi_redis_cluster_setup": false, "screenshot_count_log_file": ".", "safari_driver_port": 8884, "new_bucketing": true, "eliminate_rproxy_private_hub_sub_regions": false, "secondary_states": {"SUCCESS": "success", "1": "tunnel-required", "2": "tunnel-required", "3": "tunnel-required", "4": "tunnel-required", "401": {"Basic realm=\"Application\"": "basic-auth", "NTLM": "ntlm-auth"}}, "relaxed_http_timeout": 45000, "relaxed_start_timeout": 30000, "ie_compatibility_modes": [11001, 11000, 10001, 10000, 9999, 9000, 8888, 8000, 7000], "hub_status_data": {"status": 0, "sessionId": null, "value": {"build": {"version": "2.45.0", "revision": "5017cb8", "time": "2015-02-26 23:59:50"}}, "state": "success"}, "terminal_cmd_exec_max_time": 25000, "page_load_timeout": 120000, "queue_request_delay": 30000, "hard_retry_request_delay": 30000, "nta_retry_default_delay": 50000, "queue_max_wait": 900000, "hub_log_level": 4, "exceptionAlertReceivers": "dev<PERSON><PERSON>,group2,group3", "kafkaConfig": {"consumer": {"__COMMENT__": "CONFIG REQUIRED BY CONSUMERS(UPLOADERS) TO IDENTIFY WHICH PARTITIONS TO READ FROM. EACH REGION CAN HAVE MULTIPLE CONSUMERS", "autoCommit": true, "autoCommitIntervalMs": 5000, "raw_logs": {"us-east-1": {"__COMMENT__": "THE IDS HERE SIGNIFY THE CONSUMER IDS. IN CASE WE HAVE TO ADD ANOTHER CONSUMER, WE SIMPLY ADD ANOTHER KEY '2' AND RE<PERSON><PERSON>NCE THE PARTITIONS", "1": {"partitions": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19]}, "2": {"partitions": [20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39]}}, "us-west-1c": {"1": {"partitions": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19]}, "2": {"partitions": [20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39]}}, "eu-west-1": {"1": {"partitions": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19]}, "2": {"partitions": [20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39]}}, "ap-south-1": {"1": {"partitions": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19]}, "2": {"partitions": [20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39]}}, "ap-southeast-2": {"1": {"partitions": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19]}, "2": {"partitions": [20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39]}}}, "raw_extended_duration_logs": {"us-east-1": {"__COMMENT__": "THE IDS HERE SIGNIFY THE CONSUMER IDS. IN CASE WE HAVE TO ADD ANOTHER CONSUMER, WE SIMPLY ADD ANOTHER KEY '2' AND RE<PERSON><PERSON>NCE THE PARTITIONS", "1": {"partitions": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19]}, "2": {"partitions": [20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39]}}, "us-west-1c": {"1": {"partitions": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19]}, "2": {"partitions": [20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39]}}, "eu-west-1": {"1": {"partitions": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19]}, "2": {"partitions": [20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39]}}, "ap-south-1": {"1": {"partitions": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19]}, "2": {"partitions": [20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39]}}, "ap-southeast-2": {"1": {"partitions": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19]}, "2": {"partitions": [20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39]}}}, "instrumentation_logs": {"us-east-1": {"__COMMENT__": "THE IDS HERE SIGNIFY THE CONSUMER IDS. IN CASE WE HAVE TO ADD ANOTHER CONSUMER, WE SIMPLY ADD ANOTHER KEY '2' AND RE<PERSON><PERSON>NCE THE PARTITIONS", "1": {"partitions": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19]}, "2": {"partitions": [20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39]}}, "us-west-1c": {"1": {"partitions": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19]}, "2": {"partitions": [20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39]}}, "eu-west-1": {"1": {"partitions": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19]}, "2": {"partitions": [20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39]}}, "ap-south-1": {"1": {"partitions": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19]}, "2": {"partitions": [20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39]}}, "ap-southeast-2": {"1": {"partitions": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19]}, "2": {"partitions": [20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39]}}}, "console_logs": {"us-east-1": {"__COMMENT__": "THE IDS HERE SIGNIFY THE CONSUMER IDS. IN CASE WE HAVE TO ADD ANOTHER CONSUMER, WE SIMPLY ADD ANOTHER KEY '2' AND RE<PERSON><PERSON>NCE THE PARTITIONS", "1": {"partitions": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39]}}, "us-west-1c": {"1": {"partitions": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39]}}, "eu-west-1": {"1": {"partitions": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39]}}, "ap-south-1": {"1": {"partitions": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39]}}, "ap-southeast-2": {"1": {"partitions": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39]}}}, "fetchMaxBytes": 1048576, "fetchMaxWaitMs": 1000}, "producer": {"__COMMENT__": "CONFIGS REQUIRED BY PRODUCERS(HUB) TO IDENTIFY WHICH PARTITIONS TO READ FROM. EACH REGION CAN HAVE MULTIPLE PRODUCERS", "regions": ["us-east-1", "us-west-1", "eu-west-1", "ap-south-1", "ap-southeast-2"], "eu-west-1": {"brokers": ["localhost:9092"], "endPartition": 39, "startPartition": 0, "endPartitionExtended": 9, "startPartitionExtended": 0}, "us-east-1": {"brokers": ["localhost:9092"], "endPartition": 39, "startPartition": 0, "endPartitionExtended": 9, "startPartitionExtended": 0}, "us-west-1": {"brokers": ["localhost:9092"], "endPartition": 39, "startPartition": 0, "endPartitionExtended": 9, "startPartitionExtended": 0}, "ap-south-1": {"brokers": ["localhost:9092"], "endPartition": 39, "startPartition": 0, "endPartitionExtended": 9, "startPartitionExtended": 0}, "ap-southeast-2": {"brokers": ["localhost:9092"], "endPartition": 39, "startPartition": 0, "endPartitionExtended": 9, "startPartitionExtended": 0}, "RAW_LOGS_SERVICE": "both", "RAW_EXTENDED_DURATION_LOGS_SERVICE": "both", "CONSOLE_LOGS_SERVICE": "both"}}, "tcg_service": {"scheme": "https", "tcg_regions": {"us-east-1": {"endpoint": "tcg.bsstag.com", "elb": "tcg.bsstag.com", "s3_upload_endpoint": "browserstack-testcasegeneration-stag-euc1.s3.eu-central-1.amazonaws.com"}, "us-west-2": {"endpoint": "tcg-usw.bsstag.com", "elb": "tcg.bsstag.com", "s3_upload_endpoint": "browserstack-testcasegeneration-stag-euc1.s3.eu-central-1.amazonaws.com"}}, "username": "root", "password": "password"}}