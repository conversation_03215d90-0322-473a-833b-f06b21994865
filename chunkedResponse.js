var HubLogger = require("./log"),
  constants = HubLogger.constants,
  LL = constants.LOG_LEVEL;

var chunkedResponse = function(response, noBlankPolling = false) {
  if (response.isChunked)
    return response;

  var maxTimes = constants.MAX_SPACES_TIMES + 5;
  var header = constants.CHUNKED_HEADER;
  var headerWritten = false;
  var spaceTask;

  if (!noBlankPolling) {
    spaceTask = setInterval(function() {
      if (maxTimes < 0) {
        HubLogger.miscLogger("chunkedResponse:SpaceTask:", "Chunked spaces limit reached. Closing response.", LL.INFO);
        response.end();
        clearInterval(spaceTask);
        return;
      }
      if(!headerWritten){
        headerWritten = true;
        response.writeHead(200, header);
      }
      response.write("                          ");
      maxTimes--;
    }, constants.QUEUE_REQUEST_DELAY);
  }

  response.on('close', function() {
    connOpen = false;
  });

  var first = true,
      connOpen = true;

  var writeHead = function(status, headers) {
    if(!headerWritten){
      headerWritten = true;
      response.writeHead(status || 200, headers ? headers: header);
    } else {
      // console.log("Dummy writeHead: You shouldn't use this.");
      // console.log(status, JSON.stringify(headers));
    }
  };

  var write = function(data) {
    if (first) {
      clearInterval(spaceTask);
      first = false;
    }
    if(!headerWritten){
      headerWritten = true;
      response.writeHead(200, header);
    }
    response.write(data);
  };

  var end = function(data) {
    if (first) {
      clearInterval(spaceTask);
      first = false;
    }
    if(!headerWritten){
      headerWritten = true;
      response.writeHead(200, header);
    }
    response.end(data);
  };

  var on = function(event, handler) {
    response.on(event, handler);
  };

  var clientOnline = function() {
    return connOpen;
  };

  return {
    writeHead: writeHead,
    write: write,
    end: end,
    on: on,
    clientOnline: clientOnline,
    isChunked: true
  };
};

module.exports = chunkedResponse;
