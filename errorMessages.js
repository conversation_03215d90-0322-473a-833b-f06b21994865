exports.APP_AUTOMATE_CONTEXT_START_ERROR = " If your app uses webview, set the webview debugging flag to true when creating the webview object: WebView.setWebContentsDebuggingEnabled(true); -OR- set the automationName capability to 'Appium'";
exports.APP_AUTOMATE_MISSING_APP_ID_KEY_ERROR = " [BROWSERSTACK_WARNING] This issue could be happening due to the app using deprecated UIWebview and the open Appium issue in which connection to the app's UIWebview crashes the app on iOS 14.5 & above devices. Find more details here https://github.com/appium/appium/issues/15325. To fix this issue, we strongly recommend migrating your app from UIWebview(deprecated) to WKWebview. More details here https://developer.apple.com/news/?id=12232019b. Alternatively, for the time being, you may remove the <current_device_name> BrowserStack device from your test scripts. If you have any concerns, please reach out to support - https://www.browserstack.com/support/app-automate";
exports.APP_AUTOMATE_PM_CLEAR = " Error: shell pm clear timed out. To resolve the issue, set the 'noReset' capability to true in your test.";
exports.APP_AUTOMATE_ORIENTATION_FAILED_ERROR = " By default, we set the device orientation to portrait. Please use 'deviceOrientation' capability in your script to set the intended orientation to portrait or landscape.";
exports.COMMON_PUSH_FILE_ERROR = 'To use Appium\'s push_file command on BrowserStack devices, specify a valid path to copy data to. The following paths are valid: "/sdcard/Download", "/sdcard/Pictures"';
exports.ANDROID_11_BELOW_PUSH_FILE_ERROR = ', "/sdcard/Android/data/<your_app_package>/".';
exports.APP_AUTOMATE_PUSH_FILE_LINK = ' For more details, please refer to our documentation: https://www.browserstack.com/docs/app-automate/appium/advanced-features/test-with-sample-data.';

exports.RESPONSE_CLASS = 'org.openqa.selenium.remote.Response';
exports.SERVER_SIDE_ERROR_MESSAGE = 'An unknown server-side error occurred while processing the command.';
exports.SCRIPT_TIMEOUT_ERROR_MESSAGE = 'A script did not complete before its timeout expired.';
exports.ELEMENT_NOT_FOUND_ERROR_MESSAGE = 'An element could not be located on the page using the given search parameters.';
exports.PLAYWRIGHT_IOS_UNSUPPORTED_COMMAND_ERROR = (command) =>
  `Error: Unsupported Playwright command on iOS: "${command}".\nThis command is not yet available on iOS; we're working to expand supported commands. For assistance, please contact support: https://www.browserstack.com/contact`;

exports.RAILS_UNREACHABLE_DURING_START = "Not able to reach Browserstack.com! Please try again!";
exports.APP_AUTOMATE_ERROR_MESSAGES = {
    "SO_TIMEOUT": "[BROWSERSTACK_SO_TIMEOUT] Session errored because an Appium command failed to respond and timed out",
    "BROWSERSTACK_DEVICE_UNREACHABLE": "[BROWSERSTACK_DEVICE_UNREACHABLE] Session errored due to the device being unresponsive",
    "TIMEOUT": "[BROWSERSTACK_IDLE_TIMEOUT] Session timed out due to inactivity for configured time interval (default: 90 secs)",
    "SESSION_LIMIT_REACHED": "[BROWSERSTACK_SESSION_LIMIT_REACHED] Session timed out due to maximum time limit reached"
};
exports.APP_AUTOMATE_AUTOMATION_ERROR_MESSAGES = {
    "flutter": ' Error occured while starting the session. To verify if all the pre-requisites for running Appium Flutter Driver tests have been met, refer to - https://www.browserstack.com/docs/app-automate/appium/test-hybrid-apps/test-flutter-apps',
    "espresso": ' Error occured while starting the session. To verify if all the pre-requisites for running Appium Espresso Driver tests have been met, refer to - https://www.browserstack.com/docs/app-automate/appium/espresso-driver'
};
