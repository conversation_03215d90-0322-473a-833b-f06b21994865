const { <PERSON><PERSON><PERSON>ogHandler } = require('./controllers/seleniumCommand/handlers/DeviceLogHandler');
const sinon = require('sinon');

// Mock dependencies
const mockBridge = { sendResponse: sinon.stub() };
const mockRequestlib = { 
  call: sinon.stub(),
  appendBStackHostHeader: sinon.stub().returns({ Host: 'test' })
};
const mockHubLogger = { 
  miscLogger: sinon.stub(),
  exceptionLogger: sinon.stub()
};
const mockConstants = { 
  global_registry: {},
  CHUNKED_HEADER: { 'Content-Type': 'application/json' }
};
const mockHa = { setData: sinon.stub() };
const mockPubSub = { publish: sinon.stub() };

// Replace require calls
require.cache[require.resolve('./bridge')] = { exports: mockBridge };
require.cache[require.resolve('./lib/request')] = { exports: mockRequestlib };
require.cache[require.resolve('./log')] = { exports: mockHubLogger };
require.cache[require.resolve('./constants')] = { exports: mockConstants };
require.cache[require.resolve('./ha')] = { exports: mockHa };
require.cache[require.resolve('./pubSub')] = { exports: mockPubSub };

async function testInvalidJsonResponse() {
  console.log('Testing invalid JSON response handling...');
  
  const mockSessionKeyObj = {
    rails_session_id: 'test-session-123',
    device: 'test-device',
    deviceLogs: 'true',
    idle_timeout: 30,
    rproxyHost: 'test-host',
    name: 'test-terminal',
    debugSession: false,
  };

  const mockRequestStateObj = {
    data: null,
    output: null,
  };

  // Mock platform response with invalid JSON
  const platformResponse = {
    statusCode: 200,
    data: 'invalid json response',
  };

  mockRequestlib.call.resolves(platformResponse);
  
  const handler = new DeviceLogHandler(mockSessionKeyObj, {}, {});
  const logData = JSON.stringify({ type: 'logcat' });
  
  let sendResponseCallCount = 0;
  mockBridge.sendResponse.callsFake(() => {
    sendResponseCallCount++;
    console.log(`sendResponse called ${sendResponseCallCount} time(s)`);
  });

  try {
    await handler.processCommand(mockRequestStateObj, logData);
    
    console.log(`Final sendResponse call count: ${sendResponseCallCount}`);
    console.log('Test completed successfully!');
    
    if (sendResponseCallCount === 1) {
      console.log('✅ PASS: sendResponse called exactly once');
    } else {
      console.log('❌ FAIL: sendResponse called', sendResponseCallCount, 'times, expected 1');
    }
    
  } catch (error) {
    console.error('Test failed with error:', error);
  }
}

testInvalidJsonResponse();
