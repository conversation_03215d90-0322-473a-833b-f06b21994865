Description: 

JIRA Story: \<*link to JIRA Story*\>

#### Related PRs
Link to all the related PRs railsApp, terminalDeploy, deploy, etc.

#### Author/Reviewer primary checklist

*Following items are to be responded by the author and reviewed by the reviewer:*

- [ ] Changes are made irrespective of the environment (staging/production)
	- If not, highlight staging/production-specific change in Jira / PR description
- [ ] Change requires configuration deploy?
	- If yes, link production + staging configuration PRs in Jira / PR description
- [ ] Change is backward compatible. Ongoing sessions will not break during/after deploy.
- [ ] Involves multiple component deploy
	- If yes, mention deploy sequence of components in Jira / PR description
	- If yes, change is cross-component backward compatible (master <> PR permutations across components)
- [ ] Preserves HA of components involved in the change
- [ ] Preserves hub region toggle functionality
- [ ] Requires set/unset redis key before/after deploy?
	- If yes, add relevant snippet in Jira / PR description.
- [ ] No sensitive information is exposed (Redact keys from logs and UI, if any)
- [ ] Corresponding coverage added for changes made

Revert steps

- [ ] Tick for plain PR revert
- [ ] Tick for specific steps and include in Jira / PR description.
- [ ] Tick if the change is not revertable (approval of TL / EM required).

---

As per the author/reviewer’s judgement:

- Appropriate instrumentation (EDS / Hoothoot / Zombie)  and relevant logging is present

---

#### To Run Hub Coverage unit tests:
comment 'RUN_UNIT_TESTS' in the pr.
Job Link - https://minion.browserstack.com/view/Coverage/job/Coverage/job/SeleniumHubCoverage/
Status will be updated based on tests passing and code coverage check with latest master branch.

#### TODOs
- [ ] By placing an `x` in the preceding checkbox, you confirm that you have run the tests.
