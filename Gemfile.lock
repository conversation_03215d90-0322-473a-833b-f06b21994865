GEM
  remote: https://rubygems.org/
  specs:
    addressable (2.8.1)
      public_suffix (>= 2.0.2, < 6.0)
    capistrano (3.4.1)
      i18n
      rake (>= 10.0.0)
      sshkit (~> 1.3)
    concurrent-ruby (1.1.10)
    faraday (2.7.1)
      faraday-net_http (>= 2.0, < 3.1)
      ruby2_keywords (>= 0.0.4)
    faraday-net_http (3.0.2)
    gitlab (4.19.0)
      httparty (~> 0.20)
      terminal-table (>= 1.5.1)
    httparty (0.20.0)
      mime-types (~> 3.0)
      multi_xml (>= 0.5.2)
    i18n (1.12.0)
      concurrent-ruby (~> 1.0)
    mime-types (3.4.1)
      mime-types-data (~> 3.2015)
    mime-types-data (3.2022.0105)
    multi_xml (0.6.0)
    net-scp (4.0.0)
      net-ssh (>= 2.6.5, < 8.0.0)
    net-ssh (7.0.1)
    octokit (4.25.1)
      faraday (>= 1, < 3)
      sawyer (~> 0.9)
    pronto (0.11.0)
      gitlab (~> 4.4, >= 4.4.0)
      httparty (>= 0.13.7)
      octokit (~> 4.7, >= 4.7.0)
      rainbow (>= 2.2, < 4.0)
      rexml (~> 3.2)
      rugged (>= 0.23.0, < 1.1.0)
      thor (>= 0.20.3, < 2.0)
    pronto-eslint_npm (0.11.0)
      pronto (~> 0.11.0)
    public_suffix (5.0.0)
    rainbow (3.1.1)
    rake (13.0.6)
    rexml (3.2.5)
    ruby2_keywords (0.0.5)
    rugged (1.0.1)
    sawyer (0.9.2)
      addressable (>= 2.3.5)
      faraday (>= 0.17.3, < 3)
    sshkit (1.21.3)
      net-scp (>= 1.1.2)
      net-ssh (>= 2.8.0)
    terminal-table (3.0.2)
      unicode-display_width (>= 1.1.1, < 3)
    thor (1.2.1)
    unicode-display_width (2.3.0)

PLATFORMS
  ruby

DEPENDENCIES
  capistrano (~> 3.4.0)
  pronto (~> 0.11.0)
  pronto-eslint_npm

BUNDLED WITH
   1.16.2
