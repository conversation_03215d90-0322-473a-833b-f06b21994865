'use strict';

const constants = require('../../constants');
const HubLogger = require('../../log');

const async = require('async');
const kafka = require('kafka-node');
const stringHash = require('string-hash');

const Producer = kafka.Producer;
const Consumer = kafka.Consumer;
const LL = constants.LOG_LEVEL;
const kafkaConfig = constants.kafkaConfig;

/* Generic kafka client code that will be used by both producers and consumers */

const getKafkaClient = (kafkaBrokers) => {
  HubLogger.miscLogger('KafkaClient', `Begin initialization of kafkaClient ${kafkaBrokers}`, LL.INFO);
  const client = new kafka.KafkaClient({
    kafkaHost: kafkaBrokers,
    requestTimeout: constants.KafkaRequestTimeout,
    idleConnection: 60 * 60 * 1000,
  });
  return client;
};


/* ******************    PRODUCER RELATED CODE ******************* */

/*  Simply returns an instance of producer.
    Responsibility of the caller to over-ride the producer.on methods */
const initProducers = (region) => {
  const opts = { requireAcks: 0 };
  const kafkaBrokers = constants.kafkaConfig.producer[region].brokers.join();
  const client = getKafkaClient(kafkaBrokers);
  const producer = new Producer(client, opts);
  producer.lastMetaDataRefreshed = new Date();
  producer.region = region;
  HubLogger.miscLogger('KafkaProducers', `Begin init of Producer in the region: ${region}`, LL.INFO);

  producer.on('error', (err) => {
    HubLogger.exceptionLogger(`[KafkaError] Producer Exception. Last metadata refreshed ${new Date() - producer.lastMetaDataRefreshed}ms ago`, undefined, undefined, err);

    if ((new Date() - producer.lastMetaDataRefreshed) > constants.kafkaMetaDataRefreshInterval) {
      producer.lastMetaDataRefreshed = new Date();
      client.refreshBrokerMetadata((data) => {
        HubLogger.miscLogger('KafkaProducers', `Tried refreshing brokers : ${data}`, LL.DEBUG);
      });
    }
  });

  producer.on('ready', () => {
    HubLogger.miscLogger('KafkaProducers', `Producer for region ${region} is ready to send messages`, LL.INFO);
  });

  HubLogger.miscLogger('KafkaProducers', 'Initiating producer connection', LL.INFO);

  setInterval(() => {
    const brokers = client.getBrokers();
    const numberofConnectedBrokers = Object.keys(brokers).length;
    const numberofBrokersNeeded = constants.kafkaConfig.producer[region].brokers.length;
    if (numberofConnectedBrokers < numberofBrokersNeeded) {
      HubLogger.miscLogger('KafkaProducers', `[Broker Check] ${producer.region} ConnectedBrokers: ${numberofConnectedBrokers}. Refreshing broker metadata`, LL.DEBUG);
      producer.lastMetaDataRefreshed = new Date();
      client.refreshBrokerMetadata((data) => {
        HubLogger.miscLogger('KafkaProducers', `[Broker Check] Tried refreshing brokers : ${data}`, LL.DEBUG);
      });
    }
  }, constants.kafkaConnectedBrokersCheckInterval);
  return producer;
};

// We added this method for having additional retries for important
// messages ie. STOP. These messages we cannot afford to drop.
const shouldRetryKafkaMessage = (msg, attempt) => {
  const type = JSON.parse(msg[0].messages).type;
  const isStopRequest = kafkaConfig.LOG_DATA_MESSAGE_TYPE.STOP === type;
  return (isStopRequest && attempt < constants.NUMBER_OF_RETRIES);
};

const refreshMetadata = producer =>
  new Promise((resolve) => {
    producer.lastMetaDataRefreshed = new Date();
    producer.client.refreshBrokerMetadata((result) => {
      HubLogger.miscLogger('KafkaProducers', `Refreshed brokers ${result}`, LL.DEBUG);
      resolve();
    });
  });


/*  Low level function for sending of payload */
const send = (producer, payload, callback, attempt = 0) => {
  producer.send(payload, async (err, data) => {
    if (err) {
      const timeSinceLastRefreshed = new Date() - producer.lastMetaDataRefreshed;
      HubLogger.exceptionLogger(`[KafkaError] Producer Exception. Last metadata refreshed ${new Date() - producer.lastMetaDataRefreshed}ms ago`, undefined, undefined, err);
      if ((timeSinceLastRefreshed > constants.kafkaMetaDataRefreshInterval && attempt < 1)) {
        await refreshMetadata(producer);
        send(producer, payload, callback, attempt + 1);
      } else if (shouldRetryKafkaMessage(payload, attempt)) {
        // Additional retries will be handled here for START/STOP request only
        setTimeout(async () => {
          await refreshMetadata(producer);
          send(producer, payload, callback, attempt + 1);
        }, constants.EXPONENTIAL_BACKOFF_DELAY_TIME);
      } else {
        // All retries failed
        callback(err, data);
      }
    } else {
      // Producer was able to send the message
      callback(err, data);
    }
  });
};

/* Send message to a particular partition.
   Partition to be is decided by the partitioner method */
const sendMessage = (producer, topic, messageKey, message, partitioner, callback) => {
  const partition = partitioner(messageKey, topic);
  const payload = [{ topic, messages: message, partition }];
  const cb = (err) => {
    const metaData = {
      brokers: producer.client.initialHosts.map(broker => broker.host),
      topic,
      sessionId: messageKey.rails_session_id,
      partition,
      message,
    };
    callback(err, metaData);
  };
  send(producer, payload, cb);
};

/* Send multiple messages to the same partiion.
    The partition for message is decided by the partitioner method.
    Same as sendMessage but no need to call partitioner for each chunk.
    Slightly optimized because it collates data at application layer & sends */
const sendMessages = (producer, topic, messageKey, messages, partitioner, callback) => {
  const partition = partitioner(messageKey, topic);
  const payloads = messages.map(msg => ({ topic, messages: msg, partition }));
  async.eachSeries(payloads, (payload, cb) => {
    send(producer, [payload], cb);
  }, (err) => {
    const metaData = {
      brokers: producer.client.initialHosts.map(broker => broker.host),
      topic,
      sessionId: messageKey.rails_session_id,
      partition,
      messages_count: messages.length,
    };
    callback(err, metaData);
  });
};

/* Partitioner method that returns an integer partition based on region & keyid
*/
const getRegionBasedPartition = (msgKey, topic) => {
  const messageKey = msgKey;
  if (!typeof (messageKey.writePartition) === undefined) {
    return messageKey.writePartition;
  }
  const key = messageKey.rails_session_id;
  const region = messageKey.originRegion;
  const startPartition = topic.includes('extended_duration') ? kafkaConfig.producer[region].startPartitionExtended : kafkaConfig.producer[region].startPartition;
  const endPartition = topic.includes('extended_duration') ? kafkaConfig.producer[region].endPartitionExtended : kafkaConfig.producer[region].endPartition;
  const range = (endPartition - startPartition) + 1;
  const writePartition = startPartition + (stringHash(key) % range);
  messageKey.writePartition = writePartition;
  const logMessage = `Got write partition as ${writePartition} for ${key}`;
  HubLogger.miscLogger('KafkaProducers', logMessage, LL.DEBUG);
  return writePartition;
};

/* ******************   CONSUMER RELATED CODE ********************** */

/*  Returns an instance of consumer that reads from topic-partition pairs.
    Responsibility of caller to set the consumer.on [message, error] methods */
const initConsumer = (topicPartitionPairs, overrideOpts) => {
  HubLogger.miscLogger('KafkaConsumer', `Begin initialization of Consumer ${JSON.stringify(topicPartitionPairs)} ${JSON.stringify(overrideOpts)}`, LL.INFO);
  const options = Object.assign({
    autoCommitIntervalMs: kafkaConfig.consumer.autoCommitIntervalMs,
    autoCommit: true,
    fetchMaxWaitMs: kafkaConfig.consumer.fetchMaxWaitMs,
    fetchMaxBytes: kafkaConfig.consumer.fetchMaxBytes,
  }, overrideOpts);
  const region = constants.region;
  const kafkaBrokers = constants.kafkaConfig.producer[region].brokers.join();
  const client = getKafkaClient(kafkaBrokers);
  const consumer = new Consumer(client, topicPartitionPairs, options);
  consumer.lastMetaDataRefreshed = new Date();

  consumer.on('error', (err) => {
    HubLogger.exceptionLogger(`[KafkaError] Consumer Error. Last metadata refreshed ${new Date() - consumer.lastMetaDataRefreshed}ms ago`, undefined, undefined, err);
    if ((new Date() - consumer.lastMetaDataRefreshed) > constants.kafkaMetaDataRefreshInterval) {
      consumer.lastMetaDataRefreshed = new Date();
      client.refreshBrokerMetadata((data) => {
        HubLogger.miscLogger('KafkaConsumer', `Tried refreshing brokers : ${data}`, LL.DEBUG);
      });
    }
  });

  HubLogger.miscLogger('KafkaConsumer', `Begin initialization of Consumer ${JSON.stringify(topicPartitionPairs)} ${JSON.stringify(overrideOpts)}`, LL.INFO);
  return consumer;
};

const initKafkaOffset = () => {
  HubLogger.miscLogger('KafkaOffset', 'Creating Offset using existing kafka client', LL.DEBUG);
  const region = constants.region;
  const kafkaBrokers = constants.kafkaConfig.producer[region].brokers.join();
  const client = getKafkaClient(kafkaBrokers);
  const kafkaOffset = new kafka.Offset(client);
  return kafkaOffset;
};

/* ***************      Exports   *********************************** */
/*  List all exported functions */
exports.getRegionBasedPartition = getRegionBasedPartition;
exports.initProducers = initProducers;
exports.initConsumer = initConsumer;
exports.initKafkaOffset = initKafkaOffset;
exports.sendMessage = sendMessage;
exports.sendMessages = sendMessages;
