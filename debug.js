'use strict';

const net = require('net');

const HubLogger = require('./log');
const constants = require('./constants');

const LL = constants.LOG_LEVEL;

const isPortOpen = (host, port, callback) => {
  const genericCallback = (netClient, genre, result, errMessage = '') => {
    const message = `${genre} checking port ${port} on host ${host} ${errMessage}`;
    HubLogger.miscLogger('Port Check', message, LL.DEBUG);
    netClient.destroy();
    callback(result);
  };
  const client = net.connect({ port, host }, () => genericCallback(client, 'Success', true));
  client.setTimeout(3000);
  client.on('error', err => genericCallback(client, 'Error', false, ` Error : ${err.message}`));
  client.on('timeout', () => genericCallback(client, 'Timeout', false));
  return client;
};

exports.isPortOpen = isPortOpen;
