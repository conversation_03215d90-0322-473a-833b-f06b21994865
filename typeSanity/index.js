'use strict';

/* private */
const edgeChromiumMinVersion = 79;
const edgeChromiumEdgeOptionsMinVersion = 80;
const extractIntVersion = (version) => {
  version = parseInt(version.toString().split(' ')[0].trim(), 10);
  if (Number.isNaN(version)) {
    version = 0;
  }
  return version;
};

/* public */
const isUndefined = val => (val === undefined || val === null || val === '');

const isNotUndefined = val => !isUndefined(val);

const isString = val => (isNotUndefined(val) && typeof val === 'string');

const isTrueString = val => (isNotUndefined(val) && val.toString().toLowerCase() === 'true');

const isFalseString = val => (isNotUndefined(val) && val.toString().toLowerCase() === 'false');

const isFirefox = val => (isString(val) && (/firefox/i).test(val.toLowerCase()));

const isChrome = val => (isString(val) && (/chrome/i).test(val.toLowerCase()));

const isSafari = val => (isString(val) && (/safari/i).test(val.toLowerCase()));

const isEdge = val => (isString(val) && (/edge/i).test(val.toLowerCase()));

const isEdgeChromium = (browser, version, edgeOptions = false) => {
  const versionToCheck = edgeOptions ? edgeChromiumEdgeOptionsMinVersion : edgeChromiumMinVersion;
  return isEdge(browser) && extractIntVersion(version) >= versionToCheck;
};

const isEdgeButNonChromiumBased = (browser, version) => isEdge(browser) && extractIntVersion(version) < edgeChromiumMinVersion;

const isHash = val => (isNotUndefined(val) && typeof val === 'object' && !Array.isArray(val));

const isMac = val => (isString(val) && (/mac/i).test(val.toLowerCase()));

const isAndroid = val => (isString(val) && (/android/i).test(val.toLowerCase()));

const isWindows = val => (isString(val) && ((/windows/i).test(val.toLowerCase())));

const isFunction = val => (isNotUndefined(val) && typeof val === 'function');

const isEmptyJson = obj => (obj && Object.keys(obj).length === 0 && Object.getPrototypeOf(obj) === Object.prototype);

const getType = (object) => {
  let type = 'unknown';
  if (Object.prototype.toString.call(object) === '[object Object]') {
    type = 'object';
  } else if (typeof object === 'string' || object instanceof String) {
    type = 'string';
  } else if (Array.isArray(object)) {
    type = 'array';
  }
  return type;
};

module.exports = {
  isUndefined,
  isTrueString,
  isString,
  isNotUndefined,
  isFirefox,
  isChrome,
  isSafari,
  isEdge,
  isEdgeChromium,
  isEdgeButNonChromiumBased,
  isHash,
  isMac,
  isAndroid,
  isWindows,
  isFalseString,
  extractIntVersion,
  isFunction,
  getType,
  isEmptyJson,
};
