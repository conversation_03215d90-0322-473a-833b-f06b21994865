var constants = require('./constants'),
    LL = constants.LOG_LEVEL,
    HubLogger = require('./log'),
    helper = require('./helper');

// LEGEND:
// u = utilized, l = max queue limit for user

const incrQueueLua = `
local u = tonumber(redis.call("GET", KEYS[1]));
local up = tonumber(redis.call("GET", KEYS[3]));
local lp = tonumber(redis.call("GET", KEYS[4]));
if(u) then
    local l = tonumber(redis.call("GET", KEYS[2]));
    if(u < l and u > 0) then
        redis.call("incr", KEYS[1]);
        return { u, l };
    else
        return { u, l, up, lp };
    end;
else
    return {"No such key"};
end
`;

const decrQueueLua = `
local u = tonumber(redis.call("GET", KEYS[1]));
local l = tonumber(redis.call("GET", KEYS[2]));
if(u and l and u > 0 ) then
    return { redis.call("DECR", KEYS[1]), l }
else
    return {"No such key"}
end
`;

const queueFullLua = `
local u = tonumber(redis.call("GET", KEYS[1]));
local l = tonumber(redis.call("GET", KEYS[2]));
local up = tonumber(redis.call("GET", KEYS[3]));
local lp = tonumber(redis.call("GET", KEYS[4]));
if(u and l and up and lp) then
    if( (u >= l) and (up >= lp) ) then
        return { u, l };
    else
        return false;
    end;
else
    return {"No such key"};
end
`;

// Different Queues for Automate and AppAutomate.
function get_queue_prefix(isAppAutomate = false, isFunctionalTesting = false) {
  let key = isFunctionalTesting ? constants.sessionFTPrefix + constants.sessionQueuePrefix : constants.sessionQueuePrefix;
  return key + (isAppAutomate ? "app_automate_": "");
}

function get_parallel_prefix(isAppAutomate = false, isFunctionalTesting = false) {
  let key = isFunctionalTesting ? constants.sessionFTPrefix + constants.sessionParallelPrefix : constants.sessionParallelPrefix;
  return key + (isAppAutomate ? "app_automate_": "");
}

function parallelUtilizedKey(user, isAppAutomate = false, isFunctionalTesting = false) {
  return get_parallel_prefix(isAppAutomate, isFunctionalTesting) + 'utilized_' + user;
}

function parallelLimitKey(user, isAppAutomate = false, isFunctionalTesting = false) {
  return get_parallel_prefix(isAppAutomate, isFunctionalTesting) + 'limit_' + user;
}

function utilizedKey(user, isAppAutomate = false, isFunctionalTesting = false) {
    return get_queue_prefix(isAppAutomate, isFunctionalTesting) + 'utilized_' + user;
}

function limitKey(user, isAppAutomate = false, isFunctionalTesting = false) {
    return get_queue_prefix(isAppAutomate, isFunctionalTesting) + 'limit_' + user;
}

exports.incrQueue = function(user, isAppAutomate, requestCb, queueFullCb, isFunctionalTesting = false) {
    helper.redisClient.eval([incrQueueLua, 4, utilizedKey(user, isAppAutomate, isFunctionalTesting), limitKey(user, isAppAutomate, isFunctionalTesting), parallelUtilizedKey(user, isAppAutomate, isFunctionalTesting), parallelLimitKey(user, isAppAutomate, isFunctionalTesting)], function(err, res) {
        if(res && res.length > 0 && res[0] === "No such key") requestCb();
        else {
            if(res) {
              // All 4 keys would be returned if queue is full, dont hit rails if parallel are exhausted as well 
                if(+res[0] >= +res[1] && +res[2] >= +res[3]) {
                    HubLogger.miscLogger("User Queue", "Throttled for user " + user + " to " + res[1] , LL.DEBUG);
                    queueFullCb();
                } else {
                  // Queue is full but parallel has space, hit rails
                    if(+res[0] < +res[1] )
                        HubLogger.miscLogger("User Queue", "Incremented queue count for user " + user + " to " + (+res[0] + 1), LL.DEBUG);
                    requestCb();
                }
            } else requestCb();
        }
    });
};

exports.decrQueue = function(user, isAppAutomate = false, isFunctionalTesting = false) {
    helper.redisClient.eval([decrQueueLua, 2, utilizedKey(user, isAppAutomate, isFunctionalTesting), limitKey(user, isAppAutomate, isFunctionalTesting)], function(err, res) {
        if(res && ( res.length > 0 && res[0] !== "No such key" )) {
            HubLogger.miscLogger("User Queue", "Decrementing user queue for " + user + " - Current: " + res[0] + ":" + res[1], LL.DEBUG);
        }
    });
};

exports.isQueueFull = function(user, isAppAutomate, queueFullCb, requestCb, isFunctionalTesting = false) {
    helper.redisClient.eval([queueFullLua, 4, utilizedKey(user, isAppAutomate, isFunctionalTesting), limitKey(user, isAppAutomate, isFunctionalTesting), parallelUtilizedKey(user, isAppAutomate, isFunctionalTesting), parallelLimitKey(user, isAppAutomate, isFunctionalTesting)], function(err, res) {
        if(res && res.length > 0 && res[0] === "No such key") {
          requestCb();
        } else {
            if(res) {
                HubLogger.miscLogger("User Queue",  "Utilized >= limit for " + user + " - Current: " + res[0] + ":" + res[1], LL.DEBUG);
                queueFullCb();
            } else requestCb();
        }
    });
};

exports.updateQueue = function(user, queueUtilized, queueLimit, parallelUtilized, parallelLimit, isAppAutomate = false, isFunctionalTesting = false) {
    if(user && queueLimit) {
        HubLogger.miscLogger("User Queue", 'Updating user queue for ' + user + ' with ' + queueUtilized + ":" + queueLimit + " isAppAutomate: " + isAppAutomate, LL.DEBUG);
        helper.redisClient.setex([utilizedKey(user, isAppAutomate, isFunctionalTesting), constants.sessionQueueResetTime, queueUtilized], function() {});
        helper.redisClient.setex([limitKey(user, isAppAutomate, isFunctionalTesting), constants.sessionQueueResetTime, queueLimit], function() {});
        helper.redisClient.setex([parallelUtilizedKey(user, isAppAutomate, isFunctionalTesting), constants.sessionQueueResetTime, parallelUtilized], function() {});
        helper.redisClient.setex([parallelLimitKey(user, isAppAutomate, isFunctionalTesting), constants.sessionQueueResetTime, parallelLimit], function() {});
    }
};

exports.getQueueProduct = (postParams) => {
    if (postParams.isAppAutomate)
        return 'app_automate::';
    if (postParams.product_package === constants.FUNCTIONAL_TESTING_PACKAGE_NAME)
        return 'functional_testing::';
    return '';
};
