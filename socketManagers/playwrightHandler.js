'use strict';

const { inspect } = require('util');
const SocketMessageHandler = require('./baseSocketMessageHandler');
const {
  isUndefined,
  isString,
  isNotUndefined,
  isHash,
  isTrueString
} = require('../typeSanity');
const { allocateTerminal, markIdle } = require('./terminalAllocation');
const {
  kReqData,
  kSessionDebug,
  kSendReconnectInfo,
  kDesktopDataReceived,
  kPlaywrightType,
  kStartTimer,
  kConsoleLog,
  GRACEFULE_CLOSE_CODES,
  ABRUPT_SOCKET_MESSAGES,
  CLIENT_SIDE_ABRUPT_CLOSE,
  SERVER_SIDE_ABRUPT_CLOSE,
  RECONNECT_PREFIX,
  SOCKET_CLOSE_MESSAGE,
  ERROR_MESSAGE,
  BROWSER_GUID_FORMAT,
  PLAYWRIGHT
} = require('../config/socketConstants');
const { compare } = require('compare-versions');
const { deviceDescriptors } = require('../config/deviceDescriptors');
const HA = require('../ha');
const {
  global_registry: globalRegistry,
  kafkaConfig
} = require('../constants');
const { takeScreenshotAndUpload, getDate, nestedKeyValue, PingZombie, uploadCDPConsoleLog, canUploadConsoleLog, isLocalhostDomainOrIP } = require('../helper');
const {
  checkBstackExecutorString,
  checkandExecuteIfBstackExecutor: executeBStackExecutor
} = require('../lib/customSeleniumHandler/jsExecutor');
const logger = require('../logger').basicLogger;
const PlaywrightRawLog = require('./playwrightRawLog');
const HubLogger = require('../log');
const { checkValidPlaywrightCommand, triggerLighthouse, processCdpExecutorResponse } = require('../helpers/lighthouse/helper');

const CONSOLE_MESSAGE = 'ConsoleMessage';
const CREATE = '__create__';
const CONSOLE_METHOD = 'console';
const TAG = 'PlaywrightHandler';
const UNSUPPORTED_CONTEXT_OPTIONS = ['recordVideo', 'recordHar', '_traceDir'];
const FUNCTION_LIST = new Map([
  ['(utilityScript, ...args) => utilityScript.evaluate(...args)', 'Page.evaluate']
]);
const PERCY_PLATFORM_TEAM = 'percy-platform';
const { pushFeatureUsage } = require('../helpers/instrumentation');


class PlaywrightHandler extends SocketMessageHandler {
  constructor() {
    super();

    this.rawLogState = new Map();
    this.keyLogEvents = new Map();
  }

  enableMethodMaps() {
    this.methodMapping.set('close', this.closeHandler.bind(this));
    this.methodMapping.set('evaluateExpression', this.jsExpressionHandler.bind(this));
    this.methodMapping.set('newContext', this.newContextHandler.bind(this));
    this.methodMapping.set('launchBrowser', this.launchBrowser.bind(this));
    this.methodMapping.set('Runtime.callFunctionOn', this.executeJavascriptFunction.bind(this));
  }

  assignLogBuilder() {
    this.rawLogBuilder = new PlaywrightRawLog();
  }

  debugScreenshotMethodMaps() {
    this.debugScreenshotMethodMapping.add('click');
    this.debugScreenshotMethodMapping.add('goto');
  }

  async messageHandler(ws, message, reqMetaData) {
    try {
      await this.initializeReconnect(ws, message);
      if (message.substring(0, 10) === RECONNECT_PREFIX) {
        return;
      }
      const data = JSON.parse(message);
      const { method } = data;
      const { id: sessionId } = ws;
      await this.waitForAllocation(sessionId);
      const railsSessionId = this.railsSessionMapping.get(ws.id);
      logger.info(`Processing: ws_id - ${sessionId}, method - ${method}, data_length - ${message ? message.length : 0}, railsSessionId - ${railsSessionId}`, { TAG, log: { uuid: railsSessionId, kind: 'MESSAGE_HANDLER_PLAYWRIGHT' } });
      try {
        const keyObject = this.getRegistryObject(ws.id);
        const lighHouseEnabled = keyObject.lighthouseAutomate && keyObject.lighthouseAutomate.report_limit;

        // percy playwright instrumentation for `expectScreenshot` method
        if (method === 'expectScreenshot'){
          try{
            const kind = 'percy_playwright_toHaveScreenshot_event';
            let dataToSend = {
              "session_id": railsSessionId,
              "client_ip": ws.clientIp,
              "kind": kind,
              "capabilities": {
                "framework": "playwright"
              },
              "team": PERCY_PLATFORM_TEAM,
              "category": "playwright-visual-comparisons-stats",
              "user_id": keyObject.user_id,
              "machine": keyObject.name,
              "terminal_type": keyObject.terminal_type,
              "os": keyObject.os,
              "os_version": keyObject.os_version,
              "browser": keyObject.browser,
              "browser_version": keyObject.browser_version,
              "device": keyObject.device,
              "region": keyObject.terminalRegion
            }

            PingZombie(dataToSend);
          } catch (error) {
            logger.error(`Error sending instrumentation for ${method} to zombie: ${JSON.stringify(error)}`, { TAG });
          }
        }

        if (keyObject.translateLocalhostUrl && checkValidPlaywrightCommand(data)) {
          const urlObj = new URL(data.params.url);
          if (isLocalhostDomainOrIP(urlObj.hostname)) {
            data.params.url = urlObj.href.replace(urlObj.hostname, 'bs-local.com');
          }
        }

        if (lighHouseEnabled && checkValidPlaywrightCommand(data)) {
          const url = data.params.url;
          logger.info(`lighthouse-${PLAYWRIGHT}-trigger-start, session: ${keyObject.rails_session_id} url: ${url}`, { TAG });
          await triggerLighthouse(url, keyObject, PLAYWRIGHT);
          logger.info(`Validated, trigger lighthouse on terminal: ${JSON.stringify(keyObject.lighthouseAutomate)}`, { TAG });
        }
      } catch (error) {
        // TODO: Log error to pager/feature usage
        logger.error(`Error reaching to terminal: ${JSON.stringify(error)}`, { TAG });
      }
      await this.processData(sessionId, method, ws, data, reqMetaData);
    } catch (err) {
      logger.error(`Error in the message handler: ${inspect(err, false, 1)}`, {
        TAG,
      });
      ws.close(1001, isNotUndefined(err) ? err.toString().substring(0, 122) : ERROR_MESSAGE);
    }
  }

  async executeJavascriptFunction(ws, data, { reqMetaData }) {
    const { id, sessionId, params: { arguments: executeArgs, functionDeclaration = '' } } = data;
    const modMethod = PlaywrightHandler.identifyJSMethod(functionDeclaration);
    let executorString = undefined;
    if (!isUndefined(executeArgs) && Array.isArray(executeArgs)) {
      /* Getting the last element from the executeArgs as executorString. Length of executeArgs may change based on playwright client versions */
      executorString = executeArgs[executeArgs.length - 1];
    }
    const isBStackExecutor = isHash(executorString) &&
      isString(executorString.value) &&
      checkBstackExecutorString(executorString.value);

    if (isBStackExecutor) {
      this.logBSExecutor(ws, executorString.value, id, data);
      const executeResponse = await new Promise(async (resolve) => {
        const requestStateObj = {
          req_data: JSON.stringify({
            script: executorString.value
          }),
          onResolve: resolve
        };

        let registry = this.getRegistryObject(ws.id);
        if(isUndefined(registry)){
          await HA.getData(ws.id, (err, registryData) => {
            if (err) {
              logger.error(`SessionId ${ws.id} state not found`, {
                TAG,
              });
            } else {
              globalRegistry[ws.id] = registryData;
              registry = registryData;
            }
          });
        }
        executeBStackExecutor(registry, requestStateObj);
      });
      this.sendExecuteSyncResponseOverCdp(executeResponse, ws, id, sessionId);
    }
    else {
      this.sendToRequestLog(ws, modMethod, {
        id,
        sessionId,
        method: modMethod,
        params: {
          value: functionDeclaration,
          args: executeArgs || []
        },
      }, ws.id);
      this.sendToTerminal(data, ws.id, reqMetaData);
    }
  }

  static identifyJSMethod(methodStr, defaultMethod = 'Runtime.callFunctionOn') {
    return FUNCTION_LIST.get(methodStr) || defaultMethod;
  }


  async jsExpressionHandler(ws, data, { reqMetaData }) {
    const { id, params: { arg: { value: { s: evalString } } } } = data;
    if (isString(evalString) && checkBstackExecutorString(evalString)) {
      this.logBSExecutor(ws, evalString, id, data);
      const executeResponse = await new Promise(async (resolve) => {
        const requestStateObj = {
          req_data: JSON.stringify({
            script: evalString
          }),
          onResolve: resolve
        };

        let registry = this.getRegistryObject(ws.id);
        if(isUndefined(registry)){
          await HA.getData(ws.id, (err, registryData) => {
            if (err) {
              logger.error(`SessionId ${ws.id} state not found`, {
                TAG,
              });
            } else {
              globalRegistry[ws.id] = registryData;
              registry = registryData;
            }
          });
        }
        executeBStackExecutor(registry, requestStateObj);
      });
      this.sendExecuteSyncResponse(executeResponse, ws, id);
    } else {
      this.sendToTerminal(data, ws.id, reqMetaData);
    }
  }

  logBSExecutor(ws, evalString, id, data) {
    const logData = {
      value: {
        s: {
          script: evalString,
          args: []
        }
      }
    };
    super.sendToRequestLog(ws, data.method, {
      params: logData,
      id
    }, ws.id);
  }

  ignoreHTTPSErrorsHandler(ws, data) {
    const browser = nestedKeyValue(ws[kReqData],['data','browser']);
    const networkLogs = nestedKeyValue(ws[kReqData],['data', 'browserstack.networkLogs']);
    if ( browser === "playwright-firefox" && String(networkLogs).toLowerCase() === "true") {
      data.params = data.params || {};
      data.params.ignoreHTTPSErrors = true;
    }
  }

  async newContextHandler(ws, data, { reqMetaData }) {
    this.ignoreHTTPSErrorsHandler(ws, data);
    const keyObject = this.getRegistryObject(ws.id);
    this.addRequestLog(keyObject, 'newContext', data, ws.id);
    const clonedJSON = JSON.parse(JSON.stringify(data));
    UNSUPPORTED_CONTEXT_OPTIONS.forEach(key => delete clonedJSON.params[key]);
    this.sendToTerminal(clonedJSON, ws.id, reqMetaData);
  }

  sendExecuteSyncResponse(response = '{}', ws, id, sessionId) {
    const startTime = process.hrtime.bigint();
    const jsonData = JSON.parse(response);
    // Creating this response because playwright has a serialization rule
    // on the client side which sets a key based on the type of value. So
    // setting `s` here because the corresponding value is of string type.
    this.desktopSocketManager.emit(kDesktopDataReceived, ws.id, JSON.stringify({
      id,
      performance: processCdpExecutorResponse(jsonData),
      result: {
        value: {
          s: isString(jsonData.value) ? jsonData.value : JSON.stringify(jsonData.value)
        }
      },
      sessionId,
      method: 'evaluateExpression',
    }),
    { startTime });
  }

  sendExecuteSyncResponseOverCdp(executeResponse = '{}', ws, id, sessionId) {
    const startTime = process.hrtime.bigint();
    const jsonData = JSON.parse(executeResponse);
    this.desktopSocketManager.emit(kDesktopDataReceived, ws.id, JSON.stringify({
      id,
      result: {
        result: {
          type: 'string',
          value: isString(jsonData.value) ? jsonData.value : JSON.stringify(jsonData.value)
        }
      },
      sessionId,
      method: 'evaluateExpression',
    }),
    { startTime });
  }

  closeHandler(ws, data, { reqMetaData = {} } = {}) {
    const keyObject = this.getRegistryObject(ws.id);
    this.addRequestLog(keyObject, 'close', data, ws.id, {
      stop: true,
    });
    const { guid } = data;
    if (isNotUndefined(guid)) {
      const isSessionComplete = BROWSER_GUID_FORMAT[PLAYWRIGHT].reduce(
        (prevValue, closeString) => prevValue + guid.startsWith(closeString),
        false
      );
      if (isSessionComplete) this.addToCompletedSessions(ws.id);
    }
    this.sendToTerminal(data, ws.id, reqMetaData);
    if(ws.isPlaywright){
      let playwrightVersion = keyObject['playwrightVersion']
      let clientPlaywrightVersion = (nestedKeyValue(ws[kReqData], ['data', 'client.playwrightVersion']) || '').toString().trim();
      // Sending explicit close for client.playwrightVersion less than or equals 1.11.1 & browserstack.playwrightVersion running is greater 1.12
      if (clientPlaywrightVersion && compare(clientPlaywrightVersion, '1.12.0', '<') && compare(playwrightVersion, '1.12.0', '>=')){
        ws.close(1001, SOCKET_CLOSE_MESSAGE);
      }
    }
  }

  async launchBrowser(ws, data, { reqMetaData }) {
    const sessionId = ws.id;
    const playwrightData = JSON.parse(JSON.stringify(data));
    if (data && playwrightData) {
      if (!data.params)
        data.params = {}
      if (!playwrightData.params)
        playwrightData.params = {}

      const object = this.getRegistryObject(sessionId);
      if (data.id) {
        this.sendToRequestLog(ws, 'launchBrowser', data, sessionId);
      }
      if (object && object.name && object.port) {
        playwrightData.params.proxy = {server: `http://${object.name}:${this.getPrivoxyPort(object.port)}`};
        let userArgs = [];
        if (playwrightData.params.args) {
          userArgs = playwrightData.params.args;
        }
        playwrightData.params.args = [...userArgs, '--ignore-certificate-errors-spki-list=MxznlzUUPowhwBqZ8rf1Njb3gB6Ov5d/tiv27ZzlhcE='];
      }
    }
    this.sendToTerminal(playwrightData, sessionId, reqMetaData);
  }

  getPrivoxyPort(port) {
    if (!port || isNaN(parseInt(port)) || parseInt(port) < 32000)
      return ""
    return `2${parseInt(port) - 32000}`
  }

  waitForAllocation(socketId) {
    if (this.terminalAllocationState.has(socketId)) {
      return this.terminalAllocationState.get(socketId);
    }
    return Promise.resolve();
  }

  addAiEnabledSession(socketId) {
    const railsSessionId = this.railsSessionMapping.get(socketId);
    this.socketAiHandler.addAiEnabledSession(socketId, railsSessionId);
  }

  async preProcessAllocation(socket) {
    const { id: socketId } = socket;
    try {
      this.startAllocation(socketId, socket);
      await this.waitForAllocation(socketId);
      this.addAiEnabledSession(socketId);
    } catch (err) {
      logger.error('Error in preprocess allocation for the playwright session', { TAG });
      logger.error(inspect(err, false, 1), { TAG });
      socket.close(1001, isNotUndefined(err) ? err.toString().substring(0, 122) : ERROR_MESSAGE); // truncating err msg to avoid bytes range error
    }
  }

  /**
    * Initiates the terminal allocation process for the session and creates
    * a promise so that the messages coming to the server can be holded off
    * till the promise is resolved or rejected.
    *
    * @param {String} socketId - Unique socket identifier assigned on connection.
    * @param {WebSocket} ws - WebSocket instance connected to server.
    * @throws {Error} - Unable to get websocket url when it is not received from terminal.
    * @throws {Error} - Unable to start browser because of some unforseen error.
    * @throws {Error} - Error from upstream hub process in allocation of terminal.
    * @return {void} Sets the allocation promise in the terminal state.
    */
  startAllocation(socketId, ws) {
    this.terminalAllocationState.set(socketId, new Promise((resolve, reject) => {
      // Since we need to proxy the commonds from upstream websocket server
      // as an when it is connected. This is required because it setup some context
      // objects like selector and browser before intialising the remote browser
      // object.
      this.sessionState.set(socketId, ws);
      allocateTerminal({
        ...ws[kReqData],
        type: kPlaywrightType,
        authReqs: this.authRequests,
        currWS: socketId
      })
        .then(async (resData) => {
          const {
            status,
            sessionId: railsSessionId,
            value: { wsURL, message, wsHostname }
          } = resData;
          this.constructor.addConnectionData(ws, wsURL);
          logger.info(`Received websocket url for ${railsSessionId} : ${wsURL} and ${socketId}`, {
            TAG,
          });
          // Set the railsSessionMapping before hand because the
          // websocket url might be undefined and we need to clean the terminal
          // when the client closes the connection and for that we
          // need the mapping to be present.
          if (isNotUndefined(railsSessionId)) {
            this.railsSessionMapping.set(socketId, railsSessionId);
            let dataToSend = {
              sessionid: railsSessionId,
              client_ip: ws.clientIp,
              product_package: 'cbt',
              kind: 'automation_session_stats',
              capabilities: {
                framework: "playwright"
              }
            }
            PingZombie(dataToSend);
            const keyObject = globalRegistry[railsSessionId];
            if (isUndefined(keyObject)) {
              await HA.getData(railsSessionId, (err, data) => {
                if (err) {
                  logger.error(`SessionId ${railsSessionId} state not found`, {
                    TAG,
                  });
                } else {
                  globalRegistry[railsSessionId] = data;
                }
              });
            }
          }

          if (status === 0 && isNotUndefined(wsURL)) {
            return this.connectToDesktopSocket(socketId, wsURL, {
              hostname: wsHostname,
              reconnectId: railsSessionId,
              isPlaywright: true
            });
          }

          if (isUndefined(wsURL)) {
            if (isNotUndefined(message)) {
              throw new Error(message);
            }
            throw new Error('Unable to get the websocket connection');
          }

          if (status === 13 && isNotUndefined(message)) {
            throw new Error(message);
          }

          throw new Error('Unable to start browser');
        })
        .then(() =>
          new Promise((kObjectResolve, kObjectReject) => {
            const railsSessionId = this.railsSessionMapping.get(socketId);
            const keyObject = globalRegistry[railsSessionId];
            if (isUndefined(keyObject)) {
              HA.getData(railsSessionId, (err, data) => {
                if (err) {
                  logger.error(`SessionId ${railsSessionId} state not found`, {
                    TAG,
                  });
                  kObjectReject(new Error('sessionId state not found'));
                } else {
                  globalRegistry[railsSessionId] = data;
                  kObjectResolve(data);
                }
              });
            } else {
              kObjectResolve(keyObject);
            }
          }))
        .then(() => {
          this.emit(kStartTimer, ws);
          this.emit(kSendReconnectInfo, ws, this.provideReconnectInfo(ws));
        })
        .then(resolve)
        .catch(reject);
    }));
  }

  static uploadConsoleLog(keyObject, data, bsConsoleLogLevel, playwrightVersion) {
    if (compare(playwrightVersion, '1.39.0', '>=')) {
      const {
        method: eventMethod,
        params: {
          type: messageType,
          text: eventText
        } = {}
      } = data;
      if (isNotUndefined(eventText) &&
        eventMethod === CONSOLE_METHOD &&
        canUploadConsoleLog(bsConsoleLogLevel, messageType)) {
        HubLogger.uploadLogPartToKafka(keyObject, null, `${String(messageType).toUpperCase()}: ${eventText}\r\n`, kafkaConfig.console_logs_topic);
      }
    } else {
      const {
        method: eventMethod,
        params: {
          type: messageType,
          initializer: {
            type: eventType,
            text: eventText
          } = {}
        } = {}
      } = data;
      if (isNotUndefined(eventText) &&
        eventMethod === CREATE &&
        messageType === CONSOLE_MESSAGE &&
        canUploadConsoleLog(bsConsoleLogLevel, eventType)) {
        HubLogger.uploadLogPartToKafka(keyObject, null, `${String(eventType).toUpperCase()}: ${eventText}\r\n`, kafkaConfig.console_logs_topic);
      }
    }
    uploadCDPConsoleLog(keyObject, data, bsConsoleLogLevel);
  }

  writeResponse(ws, data, opts = {}) {
    const start = process.hrtime.bigint();
    if (ws[kSessionDebug] && data.id && this.railsSessionMapping.has(ws.id)) {
      opts.debug = {
        screenshotCounter: data.id,
      };
      takeScreenshotAndUpload(
        {
          ...this.getRegistryObject(ws.id),
          debug_screenshot_counter: data.id,
        },
        data.id
      );
    }
    const keyObject = this.getRegistryObject(ws.id);
    const clientPlaywrightVersion = (nestedKeyValue(ws[kReqData], ['data', 'client.playwrightVersion']) || '').toString().trim();
    if (isNotUndefined(clientPlaywrightVersion)) {
      let playwrightVersion = keyObject['playwrightVersion'];
      if (isNotUndefined(data) && ws[kConsoleLog] !== -1) {
        PlaywrightHandler.uploadConsoleLog(keyObject, data, ws[kConsoleLog], playwrightVersion);
      }
      PlaywrightHandler.modifyResponseToClient(data, clientPlaywrightVersion, playwrightVersion, {
        railsSessionId: this.railsSessionMapping.get(ws.id),
        platformDetails: keyObject.platformDetails,
        browserName: keyObject.browser
      });
      if (compare(clientPlaywrightVersion, '1.11.0', '>=') && compare(playwrightVersion, '1.11.0', '<') && nestedKeyValue(data,["params","type"]) === 'Playwright') {
        let chromiumBrowserTypeResponse = {
          guid: "",
          method: "__create__",
          params: {
            type: "BrowserType",
            initializer: {
              name: "chromium",
            },
            guid: data.params.initializer.chromium.guid,
          },
        };
        let firefoxBrowserTypeResponse = {
          guid: "",
          method: "__create__",
          params: {
            type: "BrowserType",
            initializer: {
              name: "firefox",
            },
            guid: data.params.initializer.firefox.guid,
          },
        };
        let webkitBrowserTypeResponse = {
          guid: "",
          method: "__create__",
          params: {
            type: "BrowserType",
            initializer: {
              name: "webkit",
            },
            guid: data.params.initializer.webkit.guid,
          },
        };
        let androidResponse = {
          guid: "",
          method: "__create__",
          params: {
            type: "Android",
            initializer: {},
            guid: data.params.initializer.android.guid,
          },
        };
        let electronResponse = {
          guid: "",
          method: "__create__",
          params: {
            type: "Electron",
            initializer: {},
            guid: data.params.initializer.electron.guid,
          },
        };
        super.writeResponse(ws,chromiumBrowserTypeResponse,opts);
        super.writeResponse(ws,firefoxBrowserTypeResponse,opts);
        super.writeResponse(ws,webkitBrowserTypeResponse,opts);
        super.writeResponse(ws,androidResponse,opts);
        super.writeResponse(ws,electronResponse,opts);
      }
      if (compare(clientPlaywrightVersion, '1.18.0', '<') && compare(playwrightVersion, '1.18.0', '>=') && nestedKeyValue(data,['params','type']) === 'LocalUtils'){
          return;
      }
      if (compare(clientPlaywrightVersion, '1.39.0', '>=') && compare(playwrightVersion, '1.38.1', '<=') && nestedKeyValue(data,['params','type']) === 'LocalUtils'){
        // dummy response for LocalUtils, since validation is strict from playwright 1.39.0
        // data.params.initializer will always be present for `LocalUtils`, so check is not needed.
        data.params.initializer.deviceDescriptors = deviceDescriptors;
      }

      if(clientPlaywrightVersion == '1.24.2' && compare(playwrightVersion, '1.25.1', '>=') && data.method == '__adopt__') {
        return;
      }

      if(compare(clientPlaywrightVersion, '1.19.0', '>=') && compare(playwrightVersion, '1.19.0', '<')){
        if(compare(playwrightVersion, '1.15.0', '>=') && nestedKeyValue(data, ['params', 'type']) === 'APIRequestContext'){
          let tracingObject = {
            guid: data.guid,
            method: "__create__",
            params: {
              type: "Tracing",
              initializer: {},
              guid: 'Tracing@fa1f6532f5bb25b919709c9c7339509e',
            },
          };
          super.writeResponse(ws, tracingObject, opts);
        } else if(compare(playwrightVersion, '1.15.0', '<') && nestedKeyValue(data, ['params', 'type']) === 'BrowserContext'){
          let APIRequestContextObject = {
            guid: data.guid,
            method: "__create__",
            params: {
              type: "APIRequestContext",
              initializer: { tracing: 'Tracing@fa1f6532f5bb25b919709c9c7339509e'},
              guid: data.params.initializer.APIRequestContext,
            },
          };
          let tracingObject = {
            guid: data.guid,
            method: "__create__",
            params: {
              type: "Tracing",
              initializer: {},
              guid: 'Tracing@fa1f6532f5bb25b919709c9c7339509e',
            },
          };
          super.writeResponse(ws, tracingObject, opts);
          super.writeResponse(ws, APIRequestContextObject, opts);
        }
        if (nestedKeyValue(data, ['params', 'type']) === 'BrowserContext'){
          ws[kReqData].data.browserContextGuid = nestedKeyValue(data, ["params", "guid"]);
        }
        if(["Artifact", "Stream"].includes(nestedKeyValue(data,['params','type'])) && nestedKeyValue(data,['guid']) === ws[kReqData].data.browserContextGuid){
          data.guid = 'Tracing@fa1f6532f5bb25b919709c9c7339509e';
        }
      } else if (compare(clientPlaywrightVersion, '1.19.0', '<') && compare(playwrightVersion, '1.19.0', '>=')){
        if (nestedKeyValue(data, ['params', 'type']) === 'BrowserContext'){
          ws[kReqData].data.browserContextGuid = nestedKeyValue(data, ["params", "guid"]);
        }
        if(nestedKeyValue(data,['params','type']) === 'Tracing'){
          if(ws[kReqData].data){
            ws[kReqData].data.tracingGuid = nestedKeyValue(data,['params','guid']);
          }
          return;
        } else if(nestedKeyValue(data,['guid']) && nestedKeyValue(data,['guid']) === ws[kReqData].data.tracingGuid){
          data.guid = ws[kReqData].data.browserContextGuid;
        }
      }

      if(compare(clientPlaywrightVersion, '1.34.0', '>=') && compare(playwrightVersion, '1.34.0', '<')) {
        if(data.method === '__create__' && data.params.type === 'ConsoleMessage') {
          return;
        }
        if (data.method === 'console') {
          return;
        }
      }
    }

    super.writeResponse(ws, data, opts);
  }

  async clientCloseHandler(ws, { code } = {}) {
    this.closePendingAuthRequest(ws.id);
    if (ABRUPT_SOCKET_MESSAGES.has(code)) {
      logger.info(`Received error code for websocket ${code}`, { TAG });
    }

    if (this.railsSessionMapping.has(ws.id)) {
      // Look for abrupt websocket close or gracefull close
      // and sessions which are not present in completed mapping.
      // It might be the case that websocket url is not generated
      // and we need to clean the terminal after that.
      const railsSessionId = this.railsSessionMapping.get(ws.id);
      const keyObject = this.getRegistryObject(ws.id);

      if (
        !this.completedSessions.has(ws.id) &&
        !GRACEFULE_CLOSE_CODES.has(code)
      ) {
        const abruptCloseType = (keyObject && isTrueString(keyObject.serverAbruptClose)) ? SERVER_SIDE_ABRUPT_CLOSE : CLIENT_SIDE_ABRUPT_CLOSE;
        pushFeatureUsage(
          railsSessionId,
          { ws: { abruptClose: { type: abruptCloseType} } },
          () => {}
        );
        try {
          await markIdle(railsSessionId, ABRUPT_SOCKET_MESSAGES.get(code), isNotUndefined(keyObject) ? keyObject.errorMessage : undefined);
        } catch (err) {
          logger.error('Unable to mark session as idle ', {
            TAG,
          });
        }
      } else if (this.completedSessions.has(ws.id) || GRACEFULE_CLOSE_CODES.has(code)) {
        if (isNotUndefined(keyObject) && (isNotUndefined(keyObject.C2PprocessingTime) || isNotUndefined(keyObject.P2CprocessingTime))) {
          keyObject.C2PprocessingTime = Number(keyObject.C2PprocessingTime) / 1000000;
          keyObject.P2CprocessingTime = Number(keyObject.P2CprocessingTime) / 1000000;
          const featureUsageData = {
            ws: {
              C2PprocessingTime: keyObject.C2PprocessingTime,
              P2CprocessingTime: keyObject.P2CprocessingTime,
              nginxToHubTime: ws.nginxToHubTime,
              userToNginx: ws.userToNginx,
              C2HrequestCount: ws.C2HrequestCount,
            },
            pwPerformanceData: keyObject.pwPerformanceData || {},
            aiHealingMetrics: {
              totalHealingDuration: keyObject?.playwrightAiMetrics?.cumulativeHealingTime || 0
            }
          };

          pushFeatureUsage(
            railsSessionId,
            featureUsageData,
            () => {}
          );
        }

        // If the session sent `close` packet and we marked the session in closed
        // sessions then use the graceful flow.
        this.closeTerminalSocket(ws);
        await this.startTerminalCleanup(ws.id);
      }
    }

    super.clientCloseHandler(ws);
  }

  clearState(sessionId) {
    const railsSessionId = this.railsSessionMapping.get(sessionId);
    this.rawLogState.delete(railsSessionId);
    this.keyLogEvents.delete(railsSessionId);
    super.clearState(sessionId);
  }

  /**
   * Add the requestLog objects to raw logs state. This method
   * sets the request entry in the state so that if the corresponding
   * response object is received then we can push both of them into the
   * log file, due to lack of REQUEST_RESPONSE type present because of
   * communication over websocket and async nature.
   *
   * @param {String} sessionId - Rails session id for session
   * @param {String} method - CDP method that need to be executed
   * @param {Object} data - Payload sent by the client for the method
   * @return {void} In case if the payload contains a single key, which is some information
   * that can be present into the payload, insert that packet into
   * the log state for mapping it with the response received latter. This is done
   * to avoid empty hashes getting dumped into the logs since it doesn't makes
   * a lot of sense to present them to user.
   */
  addEntryToRawLog(sessionId, method, data) {
    const { id } = data;
    if (isHash(data) && !data.params) return;

    if (!this.rawLogState.has(sessionId)) {
      this.rawLogState.set(sessionId, {});
    }

    this.rawLogState.get(sessionId)[id] = {
      method,
      request: { ...data, currTime: getDate() },
    };
  }

  addRequestLog(_, method, data, sessionId, options = {}) {
    const railsSessionId = this.railsSessionMapping.get(sessionId);
    if (options.stop !== true) {
      this.addEntryToRawLog(railsSessionId, method, data);
    }
  }

  addNormalResponse(responseData, keyObject, railsSessionId, options = {}) {
    if (options.stop === true) {
      return;
    }

    if (this.keyLogEvents.has(railsSessionId)) {
      return;
    }

    const { id } = responseData;
    if (id && this.rawLogState.has(railsSessionId)) {
      const stateObj = this.rawLogState.get(railsSessionId)[id];
      if (stateObj) {
        if (super.shouldIgnoreMethod('playwright', stateObj.method)) {
          return;
        }
        if (options.debug) {
          stateObj.debug = options.debug;
        }
        stateObj.response = responseData;
        this.sendToKafka(
          keyObject,
          stateObj,
          railsSessionId,
          responseData,
          options
        );
        delete this.rawLogState.get(railsSessionId)[id];
      }
    }
  }

  sendToKafka(keyObject, stateObj, railsSessionId, options) {
    super.addRequestLog(keyObject, stateObj.method, stateObj.request, railsSessionId);
    if (stateObj.debug && this.debugScreenshotMethodMapping.has(stateObj.request.method)) {
      takeScreenshotAndUpload({
        ...keyObject,
      }, stateObj.debug.screenshotCounter);
      super.addDebugLog(keyObject, stateObj.debug, railsSessionId);
    }
    if (this.isScreenshotMethod(stateObj.request.method)) {
      stateObj.response.result.value = stateObj.response.result.binary;
      super.takeScreenshotHandler(stateObj.response, keyObject, 'playwright');
      super.addNormalResponse(
        stateObj.response,
        keyObject,
        railsSessionId,
        { ...options, method: `resp-${stateObj.request.method}` }
      );
      if (stateObj && stateObj.response && stateObj.response.result && stateObj.response.result.value) {
        // https://browserstack.atlassian.net/browse/AFD-3062?focusedCommentId=737266
        // playwright python has an assertion that only allows response dictionary of length 1 only.
        delete stateObj.response.result.value;
      }
    } else {
      super.addNormalResponse(stateObj.response, keyObject, railsSessionId, options);
    }
  }

  isScreenshotMethod(method) {
    return method === 'screenshot';
  }

  // Acceptable values for type key was changed in playwright version 1.11.0
  // This method changes response format based on client version
  static modifyResponseToClient(data, clientPlaywrightVersion, playwrightVersion = '', extraData = null) {
    if (!clientPlaywrightVersion) { return; }
    if (
      compare(clientPlaywrightVersion, '1.15.0', '>=') &&
      compare(playwrightVersion, '1.15.0', '<') &&
      nestedKeyValue(data, ["params", "type"]) === "BrowserContext"
    ) {
      data.params.initializer.fetchRequest = {};
    }
    if (
      compare(clientPlaywrightVersion, '1.11.0', '>=')
      && nestedKeyValue(data, ['params', 'type']) === 'RemoteBrowser') {
      data.params.type = 'Playwright';
      const tempInit = data.params.initializer;
      data.params.initializer = {};
      data.params.initializer.chromium = { guid: 'browser-type@a5c8dd0b1901cd7604c0551b9ff6fe33' };
      data.params.initializer.firefox = { guid: 'browser-type@ae3428ca220b67c9b89de143146721e5' };
      data.params.initializer.webkit = { guid: 'browser-type@5bcf1fa3fb0f6614411e6d503399f2d2' };
      data.params.initializer.android = { guid: 'android@7181a9f6c0d3a11250759d42aca331ac' };
      data.params.initializer.electron = { guid: 'electron@cf9f62a95df8dc11a07b14bb33354bb8' };
      data.params.initializer.deviceDescriptors = deviceDescriptors;
      data.params.initializer.selectors = tempInit.selectors;
      data.params.initializer.preLaunchedBrowser = tempInit.browser;
      data.params.guid = 'Playwright';
    } else if (
      compare(clientPlaywrightVersion, '1.11.0', '<')
      && nestedKeyValue(data, ['params', 'type']) === 'Playwright') {
      // Changed the logic on client bindings to wait for message with type Playwright
      // instead of RemoteBrowser which lead to breaking change in the protocol.
      // Ref: https://github.com/microsoft/playwright/commit/fff1f3d4
      // Other options even if passed do not cause harm since thwy won't be evaluated
      // by the client binding logic.
      data.params.type = 'RemoteBrowser';
      data.params.guid = 'remoteBrowser';
      data.params.initializer.browser = data.params.initializer.preLaunchedBrowser;
    }
    // modifying the response back to the client for FetchRequest & BrowserContext as the client binding & server contracts have changed.
    if (compare(clientPlaywrightVersion, '1.17.0', '>=') && compare(playwrightVersion, '1.17.0', '<')) {
      if (data.method === '__create__') {
        if (nestedKeyValue(data, ['params', 'type']) === 'FetchRequest') {
          data.params.type = 'APIRequestContext';
        } else if (nestedKeyValue(data, ['params', 'type']) === 'BrowserContext') {
          data.params.initializer.APIRequestContext = data.params.initializer.fetchRequest;
        }
      }
    } else if (compare(clientPlaywrightVersion, '1.17.0', '<') && compare(playwrightVersion, '1.17.0', '>=')){
      if (data.method === '__create__') {
        if (nestedKeyValue(data, ['params', 'type']) === 'APIRequestContext') {
          if(compare(clientPlaywrightVersion, '1.15.0', '<')){
            data.params.type = 'BrowserContext'
          } else{
            data.params.type = 'FetchRequest'
          }
        } else if (nestedKeyValue(data, ['params', 'type']) === 'BrowserContext' && compare(playwrightVersion, '1.25.1', '<')) {
          data.params.initializer.fetchRequest = data.params.initializer.APIRequestContext;
        }
      }
    }
    if(compare(clientPlaywrightVersion, '1.18.0', '>=') && compare(playwrightVersion, '1.18.0', '<')){
      if(nestedKeyValue(data, ['params', 'type']) === 'Playwright'){
        data.params.initializer.utils = { guid: 'localUtils@663adb1c9eec8f924011e9745c3ecb5d' };
      }
    } else if (compare(clientPlaywrightVersion, '1.18.0', '<') && compare(playwrightVersion, '1.18.0', '>=')){
      if(nestedKeyValue(data, ['params', 'type']) === 'Playwright'){
        delete data.params.initializer.utils;
      }
    }
    if(compare(clientPlaywrightVersion, '1.19.0', '>=') && compare(playwrightVersion, '1.19.0', '<')){
      if(nestedKeyValue(data, ['params', 'type']) === 'APIRequestContext' || nestedKeyValue(data, ['params', 'type']) === 'BrowserContext'){
        data.params.initializer.tracing = { guid: 'Tracing@fa1f6532f5bb25b919709c9c7339509e'};
      }
    }
    if(compare(clientPlaywrightVersion, '1.25.1', '<') && compare(playwrightVersion, '1.25.1', '>=')) {
      if(isNotUndefined(data.params)) {
        if(data.params.type == 'FetchRequest') {
          data.params.initializer = {};
          data.params.guid = "fetchRequest@4676bbaaf8e2eca0f8d658b1a6105e68";
        } else if(data.params.type == 'BrowserContext') {
          data.params.initializer.fetchRequest = { guid: "fetchRequest@4676bbaaf8e2eca0f8d658b1a6105e68" };
          data.params.initializer.APIRequestContext = { guid: "fetchRequest@4676bbaaf8e2eca0f8d658b1a6105e68" };
        }
        if(isNotUndefined(data.method)) {
          if(data.method == '__adopt__') {
            if(isNotUndefined(data.params.guid) && data.params.guid.includes('request-context')) {
              data.params.guid = "fetchRequest@4676bbaaf8e2eca0f8d658b1a6105e68";
            }
            if(isNotUndefined(data.guid) && data.guid.includes('request-context')) {
              data.guid = "fetchRequest@4676bbaaf8e2eca0f8d658b1a6105e68";
            }
          } else if(data.method == '__create__') {
            if(isNotUndefined(data.params.guid) && data.params.guid.includes('request-context')) {
              data.params.guid = "fetchRequest@4676bbaaf8e2eca0f8d658b1a6105e68";
            }
          }
        }
        if(data.params.type == 'APIRequestContext') {
          data.params.guid = "fetchRequest@4676bbaaf8e2eca0f8d658b1a6105e68";
        }
      }
    }

    const connectMethodCheck = isNotUndefined(extraData) && data.method === '__create__' && (nestedKeyValue(data, ['params', 'type']) === 'Browser' || nestedKeyValue(data, ['params', 'type']) === 'AndroidDevice');
    const connectOverCDPMethodCheck = isNotUndefined(extraData) && nestedKeyValue(data, ['result', 'userAgent']) !== undefined;
    if (connectMethodCheck || connectOverCDPMethodCheck) {
      const bStackParamObj = {
        sessionId: extraData.railsSessionId,
        platformDetails: extraData.platformDetails,
        browserName: extraData.browserName
      };
      if (nestedKeyValue(data, ['params'])) {
        data.params.bsParams = bStackParamObj;
      } else {
        data.bStackParams = bStackParamObj;
      }
    }
  }
}

module.exports = PlaywrightHandler;
