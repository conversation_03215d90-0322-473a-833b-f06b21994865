'use strict';

const {
  kR<PERSON>eivedLockAck,
  PROXY_RESTART,
  STOP_SOCKET_DELAY,
  SERVICE_RESTART
} = require('../config/socketConstants');
const { basicLogger: logger } = require('../logger');

class StopSocket {
  addSocket(socket) {
    socket.send(PROXY_RESTART);
    this.addTimer(socket);
  }

  addTimer(socket) {
    socket.on(kReceivedLockAck, () => {
      // Though this can be improved by using an interval and
      // checking for the update time on socket and if it is not updated in
      // while then close the socket.
      setTimeout(() => {
        logger.info(`Closing socket with id: ${socket.id}`);
        socket.close(1001, SERVICE_RESTART);
      }, STOP_SOCKET_DELAY);
    });
  }
}

const stopSocket = new StopSocket();

module.exports = stopSocket;
