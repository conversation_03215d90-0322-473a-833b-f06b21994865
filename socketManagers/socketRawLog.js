'use strict';

const { GENERIC } = require('../helpers/customSeleniumHandling/frameworkConstants');
const requestTypeCorrection = require('../helpers/customSeleniumHandling/requestTypeCorrection');
const {
  requestString, responseString, debugScreenshotString, stopString,
} = require('./logSerializer/serializer');
const { isString, isFunction } = require('../typeSanity');

class SocketRawLog {
  constructor() {
    this.methodLogMap = new Map();
    this.methodPayloadMap = new Map();
    this.generateMethodLogMapping();
    this.generatePayloadMapping();
  }

  // eslint-disable-next-line class-methods-use-this
  generateMethodLogMapping() {
    throw new Error('Need to be implemented by the sub-classes');
  }

  /**
    * This method is used for setting up the mapping of payload
    * structure sent with the type. By default we will be sending
    * the `text` key with the payload but different urls need
    * different logic for setting the data in the raw log, majorly
    * they differ in the structure of data.
    *
   */
  // eslint-disable-next-line class-methods-use-this
  generatePayloadMapping() {
    throw new Error('Need to be implemented by the sub-classes');
  }

  fetchPayloadHandler(method) {
    return this.methodPayloadMap.has(method) ?
      this.methodPayloadMap.get(method) :
      SocketRawLog.genericPayloadHandler;
  }

  /**
    * Generic payload handler function, when the method is not founded in
    * the Map (methodPayloadMap) we use the default strategy to set the data
    * as value of key `text` similar to execute_script syntax in selenium.
    */
  static genericPayloadHandler(data) {
    return { text: data };
  }

  fetchLogType(method) {
    if (this.methodLogMap.has(method)) {
      return this.methodLogMap.get(method);
    }

    return GENERIC;
  }

  generateAnnotateArgs(sessionId, method) {
    return {
      requestStateObj: {
        url: `/session/${sessionId}/execute`,
      },
      commandType: this.fetchLogType(method),
    };
  }

  static isRequest(type) {
    return isString(type) && type.toLowerCase() === 'request';
  }

  static isResponse(type) {
    return isString(type) && type.toLowerCase() === 'response';
  }

  static isStop(type) {
    return isString(type) && type.toLowerCase() === 'stop';
  }

  static isDebug(type) {
    return isString(type) && type.toLowerCase() === 'debug';
  }

  static getSerializer(type) {
    if (SocketRawLog.isRequest(type)) {
      return requestString;
    }

    if (SocketRawLog.isResponse(type)) {
      return responseString;
    }

    if (SocketRawLog.isStop(type)) {
      return stopString;
    }

    if (SocketRawLog.isDebug(type)) {
      return debugScreenshotString;
    }

    return null;
  }

  updateLogEntry(type, method, { sessionId, data }) {
    const { requestStateObj, commandType } = this.generateAnnotateArgs(sessionId, method);
    requestTypeCorrection(requestStateObj, commandType);
    const [requestMethod] = requestStateObj.hash.split(':');
    const stringFn = SocketRawLog.getSerializer(type);
    const logData = this.fetchPayloadHandler(method)(data);
    if (isFunction(stringFn)) {
      return stringFn(logData, {
        method: requestMethod,
        url: requestStateObj.url,
      });
    }

    return `${type} ${method} ${JSON.stringify(data)}`;
  }
}

module.exports = SocketRawLog;
