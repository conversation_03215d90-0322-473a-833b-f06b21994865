'use strict';

const { PingZ<PERSON>ie } = require('../helper');
const { appendBStackHostHeader } = require('../lib/request');
const WebSocket = require('ws');
const EventEmitter = require('events');
const StopSocket = require('./stopSocket');
const { compare } = require('compare-versions');
const {
  kDesktopDataReceived,
  kDesktopCloseReceived,
  DESKTOP_HANDSHAKE_TIMEOUT,
  kSendToDesktop,
  kReqData,
  REMOTE_DEBUGGER_PORT,
  WS_STATS,
  WS_DESKTOP_CLOSE_TAG,
  WS_DESKTOP_MESSAGE_TAG,
  WS_DESKTOP_OPEN_TAG,
  WS_DESKTOP_ERROR_TAG
} = require('../config/socketConstants');
const logger = require('../logger').basicLogger;
const { incrementHootHootStat, pushFeatureUsage } = require('../helpers/instrumentation');
const { region, osHostName } = require('../constants');
const { global_registry: globalRegistry, global_ws_registry } = require('../constants');
const helper = require('../helper');

const TAG = 'DesktopSocketManager';

class DesktopSocketManager extends EventEmitter {
  constructor() {
    super();
    this.socketsStates = new Map();
    this.on(kSendToDesktop, this.sendDataToDesktop.bind(this));
  }

  addNewSocket(sessionId, wsURL, {
    hostname, reconnect = false, reconnectId, isSelenium = false, isPlaywright = false
  } = {}) {
    return new Promise((resolve) => {
      wsURL = helper.eliminateRproxyForWebsocketFrameworks(wsURL, hostname, this.getGlobalRegistryObject(reconnectId));
      logger.info(`Attempting to connection desktop WS ${sessionId} ${wsURL} reconnect = ${reconnect}`, { TAG });
      const desktopSocket = new WebSocket(wsURL, {
        handshakeTimeout: DESKTOP_HANDSHAKE_TIMEOUT,
        headers: appendBStackHostHeader(hostname, {
          Host: `localhost:${REMOTE_DEBUGGER_PORT}`,
          'Cdp-host': `localhost:${REMOTE_DEBUGGER_PORT}`,
          ...reconnect && { 'x-reconnect-id': reconnectId },
          ...reconnectId && { 'x-connection-id': reconnectId }
        })
      });
      desktopSocket.isSelenium = isSelenium;
      desktopSocket.isPlaywright = isPlaywright;
      desktopSocket.on('open', () => {
        logger.info(`Received open event from desktop ${sessionId} ${reconnectId}`, { TAG });
        incrementHootHootStat(WS_STATS, WS_DESKTOP_OPEN_TAG);
        this.socketsStates.set(sessionId, desktopSocket);
        const keyObject = this.getGlobalRegistryObject(reconnectId);
        const ws = this.getGlobalWsObject(sessionId);
        if (ws && ws.isPlaywright) {
          try{
            const serverPlaywrightVersion = keyObject.playwrightVersion;
            const clientPlaywrightVersion = (helper.nestedKeyValue(ws[kReqData], ['data', 'client.playwrightVersion']) || '').toString().trim();
            if (clientPlaywrightVersion && compare(clientPlaywrightVersion, '1.15.0', '<') && compare(serverPlaywrightVersion, '1.15.0', '>=')) {
              setTimeout(() => {
                const initializeData = {
                  id: 1,
                  guid: '',
                  method: 'initialize',
                  params: { sdkLanguage: 'javascript' },
                  metadata: { stack: [], apiName: '' }
                };
                this.emit(kSendToDesktop, sessionId, initializeData);
              }, 1000);
            }
          } catch (err){
            logger.info(`desktopSocket Exception: ${sessionId} ${reconnectId} ${err}`, { TAG });
          }
        }
        resolve();
      });
      this.startListeners(sessionId, desktopSocket);
    });
  }

  startListeners(sessionId, desktopSocket) {
    desktopSocket.on('message', (data) => {
      const startTime = process.hrtime.bigint();
      logger.debug(`Received message event from desktop ${sessionId} ${data.toString()}`, { TAG });
      incrementHootHootStat(WS_STATS, WS_DESKTOP_MESSAGE_TAG);
      if (data === 'PROXY_LOCKED') {
        logger.info(`Proxy locked for desktop ${sessionId}`, { TAG });
        desktopSocket.emit('receivedLocked');
        return;
      }
      this.emit(kDesktopDataReceived, sessionId, data, { startTime });
    });

    desktopSocket.on('error', (event) => {
      logger.error(`Received error event ${sessionId} ${event}`, { TAG });
      incrementHootHootStat(WS_STATS, WS_DESKTOP_ERROR_TAG);
      if (desktopSocket.isSelenium || desktopSocket.isPlaywright) {
        pushFeatureUsage(sessionId, { ws: { errorServer: { event, timestamp: Date.now() } } }, () => {});
        PingZombie({
          timestamp: Date.now(),
          kind: 'hub-ws-error',
          data: `Received error event ${sessionId} ${event}`,
          region,
          machine: osHostName,
          session_id: sessionId
        });
      }
    });

    desktopSocket.on('close', (code, reason) => {
      logger.info(`Received close event from desktop ${sessionId} code = ${code} reason = ${reason.toString()}`, { TAG });
      incrementHootHootStat(WS_STATS, `${WS_DESKTOP_CLOSE_TAG}_${code}`);
      if (desktopSocket.isSelenium || desktopSocket.isPlaywright) {
        pushFeatureUsage(sessionId, { ws: { closeServer: { code, reason, timestamp: Date.now() } } }, () => {});
      }
      if (this.socketsStates.has(sessionId)) {
        this.socketsStates.delete(sessionId);
      }
      this.emit(kDesktopCloseReceived, sessionId, code, reason);
    });
  }

  sendDataToDesktop(sessionId, data, callback = () => {}) {
    if (this.socketsStates.has(sessionId)) {
      this.socketsStates.get(sessionId).send(JSON.stringify(data));
      callback();
    }
  }

  terminateSocket(sessionId, restart = false) {
    if (this.socketsStates.has(sessionId)) {
      const desktopSocket = this.socketsStates.get(sessionId);
      if (!restart) {
        desktopSocket.terminate();
        this.socketsStates.delete(sessionId);
      } else {
        StopSocket.addSocket(desktopSocket);
      }
    }
  }

  getGlobalRegistryObject(id) {
    return globalRegistry[id];
  }

  getGlobalWsObject(id) {
    return global_ws_registry[id];
  }
}

module.exports = DesktopSocketManager;
