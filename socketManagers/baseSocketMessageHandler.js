'use strict';

const { inspect } = require('util');
const { isValidStatusCode } = require('ws/lib/validation');

const DesktopSocketManager = require('./desktopSocket');
const EventEmitter = require('events');
const { isNotUndefined, isHash, isUndefined, isTrueString } = require('../typeSanity');
const HA = require('../ha');
const { stopTerminal } = require('./terminalAllocation');
const { compare } = require('compare-versions');
const {
  kStartTimer,
  kCloseRemote,
  RECONNECT_PREFIX,
  kDesktopDataReceived,
  kDesktopCloseReceived,
  kReconnect,
  ABRUPT_SOCKET_MESSAGES,
  kReqURL,
  kSendToDesktop,
  kReqData,
  PLAYWRIGHT
} = require('../config/socketConstants');
const { replaceSessionId } = require('./responseModifiers');
const { sessionLog } = require('../log');
const { global_registry: globalRegistry, PW_FIND_ELEMENT_ALLOWED_METHODS} = require('../constants');
const logger = require('../logger').basicLogger;
const s3UploadHelper = require('../helpers/s3UploadHelper');
const helper = require('../helper');
const { pushFeatureUsage } = require('../helpers/instrumentation');
const { redact } = require("../helpers/redactor/cdpRedactor");
const { getPerformanceLogData } = require('../helpers/lighthouse/helper');
const SocketAiHandler = require('../helpers/aiHelper/playwrightAiHelper');
const errorMessageConstants = require('../errorMessages');

const TAG = 'SocketMessageHandler';

class SocketMessageHandler extends EventEmitter {
  constructor() {
    super();
    // Map containing the client socket with the sessionId
    this.sessionState = new Map();
    this.methodMapping = new Map();
    this.debugScreenshotMethodMapping = new Set();

    this.reconnectConfigured = new Map();

    // Mapping of current socket with the railsSessionId
    this.railsSessionMapping = new Map();
    this.railsBrowserMapping = new Map();
    this.socketAiHandler = new SocketAiHandler();

    // Map containing the client socket id with the
    // outgoing request for authentication and authorization to
    // loopback process (because of constraint in porting the single
    // socket to IncomingMessage and OutgoingMessage object)
    this.authRequests = new Map();

    // Set for the completed sessions to not trigger timeout flow
    this.completedSessions = new Set();
    // Map containing the promise before which proxy to the remote desktop
    // should not happen, this to break the write on client socket as first
    // terminal needs to get allocated for execution.
    this.terminalAllocationState = new Map();

    // Map containing active api request
    // this is used to hold all incoming message from server till api call finishes
    // Detox uses this to maintain order of api call response and detox server messages
    this.activeApiRequests = new Map();

    this.findElementRequests = new Map();

    this.enableMethodMaps();
    this.debugScreenshotMethodMaps();

    this.rawLogBuilder = null;
    this.assignLogBuilder();

    this.desktopSocketManager = new DesktopSocketManager();
    this.desktopSocketManager.on(kDesktopDataReceived, this.proxyResponseToClient.bind(this));
    this.desktopSocketManager.on(kDesktopCloseReceived, this.proxyCloseToClient.bind(this));

    this.IGNORED_METHODS = {
      puppeteer: [
        'Target.setAutoAttach', 'Page.setLifecycleEventsEnabled',
        'Page.addScriptToEvaluateOnNewDocument', 'Page.createIsolatedWorld',
        'Emulation.setDeviceMetricsOverride', 'Emulation.setTouchEmulationEnabled',
        'DOM.getContentQuads', 'Target.activateTarget', 'Runtime.releaseObject'
      ],
      playwright: ['read']
    };
    this.on(kCloseRemote, this.closeRemoteConnection.bind(this));
    this.setErrorMessage = this.setErrorMessage.bind(this);
  }

  closeRemoteConnection(socketId) {
    this.desktopSocketManager.terminateSocket(socketId, true);
  }

  // eslint-disable-next-line class-methods-use-this
  assignLogBuilder() {
    throw new Error('Need to be implemented by the sub-classes');
  }

  // eslint-disable-next-line class-methods-use-this
  enableMethodMaps() {
    throw new Error('Need to be implemented by the sub-classes');
  }

  // eslint-disable-next-line class-methods-use-this
  debugScreenshotMethodMaps() {
    throw new Error('Need to be implemented by the sub-classes');
  }

  proxyResponseToClient(sessionId, data, respMetaData = {}) {
    if (this.sessionState.has(sessionId)) {
      const clientSocket = this.sessionState.get(sessionId);
      logger.info(`Received message from desktop for railsSessionId: ${sessionId}, ws_id: ${clientSocket.id}, data-length: ${data ? data.length : 0}`, {
        TAG,
        log: {
          kind: 'DESKTOP_RESPONSE',
          uuid: sessionId
        }
      });
      try {
        const jsonData = JSON.parse(data);
        if (clientSocket.isPlaywright) {

          if (this.findElementRequests.has(sessionId) && this.findElementRequests.get(sessionId).has(jsonData.id)) {
            if (this.socketAiHandler.isPlaywrightRequestSuccess(jsonData)) {
              this.incrementPwPerformanceData(clientSocket.id, 'find_element_success');
            } else {
              this.incrementPwPerformanceData(clientSocket.id, 'find_element_failure');
            }
          }

          if (this.socketAiHandler.isHealingResponse(sessionId, jsonData)) {
            // processHealingResponse returns false if the response is a healing response i.e. if we do not want to proxy to client
            if (!this.socketAiHandler.processHealingResponse(sessionId, jsonData, this)) {
              return;
            }
          }
          const keyObject = this.getRegistryObject(clientSocket.id);
          const playwrightVersion = keyObject.playwrightVersion;
          const clientPlaywrightVersion = (helper.nestedKeyValue(clientSocket[kReqData], ['data', 'client.playwrightVersion']) || '').toString().trim();
          this.setErrorMessage(keyObject, jsonData);
          this.instrumentNotImplementedCommands(sessionId, jsonData);
          if (clientPlaywrightVersion && compare(clientPlaywrightVersion, '1.15.0', '<') && compare(playwrightVersion, '1.15.0', '>=')) {
            if (jsonData.method === '__create__' && helper.nestedKeyValue(jsonData, ['params', 'type']) === 'Response') {
              if (isUndefined(jsonData.params.initializer.requestHeaders)) {
                jsonData.params.initializer.requestHeaders = [];
              }
              Object.assign(jsonData.params.initializer.requestHeaders, jsonData.params.initializer.headers);
            }
            if ((jsonData.method === '__create__' && jsonData.params.type === 'FetchRequest') ||
                  (helper.nestedKeyValue(jsonData, ['result', 'playwright', 'guid']) === 'Playwright')) {
              return;
            }
          } else if (clientPlaywrightVersion && compare(clientPlaywrightVersion, '1.47.0', '>=')) {
            if (jsonData.method === '__create__' && helper.nestedKeyValue(jsonData, ['params', 'type']) === 'Playwright') {
              if (clientPlaywrightVersion && compare(clientPlaywrightVersion, '1.48.0', '>=') && compare(playwrightVersion, '1.48.0', '<')) {
                const chromiumGuid = jsonData.params.initializer.chromium.guid;
                const firefoxGuid = jsonData.params.initializer.firefox.guid;
                jsonData.params.initializer.bidiChromium = { guid: chromiumGuid };
                jsonData.params.initializer.bidiFirefox = { guid: firefoxGuid };
              } else if (compare(playwrightVersion, '1.47.0', '<') || compare(clientPlaywrightVersion, '1.48.0', '<')) {
                const chromiumGuid = jsonData.params.initializer.chromium.guid;
                jsonData.params.initializer.bidi = { guid: chromiumGuid };
              }
            }
          }
        }
        this.writeResponse(clientSocket, jsonData, { respMetaData });
      } catch (err) {
        logger.error(`Error in parsing the json response from desktop ${inspect(err, false, 1)}`, {
          TAG,
        });
        if (clientSocket.isSelenium) {
          pushFeatureUsage(sessionId, { ws: { exception: { timestamp: Date.now(), message: err.toString(), stacktrace: err.stack } } }, () => {});
        }
      }
    }
  }

  setErrorMessage(keyObject, data) {
    if(isNotUndefined(data) && isNotUndefined(data.error) && isNotUndefined(data.error.error) && isNotUndefined(data.error.error.message)) {
      if(isNotUndefined(data.error.error['BROWSERSTACK_METHOD_NOT_IMPLEMENTED'])){
        const errorBody = data.error.error;
        const updatedMessage = errorMessageConstants.PLAYWRIGHT_IOS_UNSUPPORTED_COMMAND_ERROR(errorBody['BROWSERSTACK_METHOD_NOT_IMPLEMENTED']);
        errorBody.message = updatedMessage;
        data.log = [updatedMessage];
      }
      keyObject.errorMessage = JSON.stringify({
        message: data.error.error.message.toString().substring(0, 1000),
        name: data.error.error.name
      });
    }
  }

  instrumentNotImplementedCommands(sessionId, data) {
    if (isNotUndefined(data) && isNotUndefined(data.error) && isNotUndefined(data.error.error) && isNotUndefined(data.error.error['BROWSERSTACK_METHOD_NOT_IMPLEMENTED'])) {
      const railsSessionId = this.railsSessionMapping.get(sessionId);
      logger.info(`Received message from terminal for railsSessionId: ${railsSessionId} and sessionId: ${sessionId}`, {
        TAG,
        log: {
          kind: 'BROWSERSTACK_METHOD_NOT_IMPLEMENTED',
          uuid: railsSessionId
        }
      });
      pushFeatureUsage(railsSessionId, { commands: { notImplemented: { timestamp: Date.now(), message: data.error.error['BROWSERSTACK_METHOD_NOT_IMPLEMENTED'] } } }, () => {});
    }
  }

  isFindElementRequest(request) {
    const method = request && request.method;
    const selector = (request && request.params && request.params.selector) || '';
    const isValidSelector = typeof selector === 'string' && !selector.includes('>>');
    const isFindElementRequest = PW_FIND_ELEMENT_ALLOWED_METHODS.includes(method) && isValidSelector;
    logger.debug(`Method: ${method} and isFindElementRequest: ${isFindElementRequest}`, { TAG });
    return isFindElementRequest;
  }

  incrementPwPerformanceData(sessionId, key) {
    const keyObject = this.getRegistryObject(sessionId);
    if (isUndefined(keyObject.pwPerformanceData)) {
      keyObject.pwPerformanceData = {
        total_request: 0,
        total_find_element_request: 0,
        find_element_success: 0,
        find_element_failure: 0,
      }
    }
    
    keyObject.pwPerformanceData[key] += 1;
  }

  proxyCloseToClient(sessionId, code, reason) {
    if (this.sessionState.has(sessionId)) {
      logger.debug(`Received close from desktop for sessionId: ${sessionId}`, {
        TAG,
      });
      const clientSocket = this.sessionState.get(sessionId);
      const keyObject = this.getRegistryObject(clientSocket.id);
      if (keyObject && isUndefined(keyObject.serverAbruptClose) && ABRUPT_SOCKET_MESSAGES.has(code)) {
        keyObject.serverAbruptClose = true;
      }
      try {
        if (isValidStatusCode(code)) {
          // Checks if a status code is allowed in a close frame.
          // .close throws exception if invalid status code is passed
          // https://github.com/websockets/ws/blob/5991c3548404e441129a16887e6a15250722a960/lib/validation.js#L33
          this.closeClientConnection(clientSocket, reason, code);
        } else {
          clientSocket.terminate();
        }
      } catch (err) {
        logger.error(`Error in closing socket ${inspect(err, false, 1)}`, {
          TAG,
        });
        if (clientSocket.isSelenium) {
          pushFeatureUsage(sessionId, { ws: { exception: { timestamp: Date.now(), message: err.toString(), stacktrace: err.stack } } }, () => {});
        }
        clientSocket.terminate();
      }
    }
  }

  closePendingAuthRequest(socketId) {
    if (this.authRequests.has(socketId)) {
      this.authRequests.get(socketId).abort();
    }
  }

  clearState(sessionId) {
    logger.info(`Clearing state for sessionId: ${sessionId}`, {
      TAG,
    });
    this.terminalAllocationState.delete(sessionId);
    this.sessionState.delete(sessionId);
    this.railsSessionMapping.delete(sessionId);
    this.railsBrowserMapping.delete(sessionId);
    this.completedSessions.delete(sessionId);
    this.authRequests.delete(sessionId);
    this.activeApiRequests.delete(sessionId);
    this.findElementRequests.delete(sessionId);
    this.socketAiHandler.clearSessionState(sessionId);
  }

  provideReconnectInfo(socket) {
    const { id } = socket;
    if (this.railsSessionMapping.has(id)) {
      const railsSessionId = this.railsSessionMapping.get(id);
      const browserSessionId = this.railsBrowserMapping.get(id);
      const url = socket[kReqURL] || '';
      return {
        railsSessionId,
        browserSessionId,
        remoteConnection: socket.remoteConnection,
        reqURL: url
      };
    }
    return {};
  }

  static addConnectionData(socket, url) {
    socket.remoteConnection = url;
  }

  // eslint-disable-next-line class-methods-use-this
  preProcessAllocation() {}

  methodHandler(method) {
    if (this.methodMapping.has(method)) {
      return this.methodMapping.get(method);
    }
    return this.genericHandler.bind(this);
  }

  async connectToDesktopSocket(sessionId, wsURL, opts = {}) {
    await this.desktopSocketManager.addNewSocket(sessionId, wsURL, opts);
  }

  getRegistryObject(id) {
    return globalRegistry[this.railsSessionMapping.get(id)];
  }

  genericHandler(ws, data, { method, reqMetaData } = {}) {
    const sessionId = ws.id;
    if (this.sessionState.has(sessionId)) {
      if (data.id) {
        this.sendToRequestLog(ws, method, data, sessionId);
      }
      const clientSocket = this.sessionState.get(sessionId);
      if (clientSocket?.isPlaywright) {
        this.incrementPwPerformanceData(sessionId, 'total_request');
      }
      try {
        const modifiedRequest = this.socketAiHandler.waitForRequest(sessionId, data); // Wrap the request in case AI is enabled
        if (this.isFindElementRequest(data)) {
          this.incrementPwPerformanceData(sessionId, 'total_find_element_request');
          // Store the Request ID for later reference
          if (!this.findElementRequests.has(sessionId)) {
            this.findElementRequests.set(sessionId, new Set());
          }
          this.findElementRequests.get(sessionId).add(modifiedRequest.id);
        }
        this.sendToTerminal(modifiedRequest, sessionId, reqMetaData);
      } catch (err) {
        logger.error(`Error in baseSocketMessageHandler sendToTerminal: ${err.toString()}`, {
          TAG,
        });
      }
    }
  }

  sendToRequestLog(ws, method, data, sessionId) {
    const keyObject = this.getRegistryObject(ws.id);
    this.addRequestLog(keyObject, method, data, sessionId);
  }

  addRequestLog(keyObject, method, data, sessionId) {
    try {
      const framework = keyObject.isPlaywright ? PLAYWRIGHT : null;
      const maskCommands = keyObject.maskCommands;
      data = redact(data, maskCommands, framework);
    } catch (err) {
      logger.error(`Error in masking data ${err.toString()}`, {
        TAG,
      });
    }
    sessionLog(keyObject, 'REQUEST', this.rawLogBuilder.updateLogEntry('REQUEST', method, {
      data,
      sessionId,
    }));
  }

  sendToTerminal(data, sessionId, reqMetaData) {
    let callback = () => {};
    logger.debug(`sendToTerminal is called: ${sessionId}`, {
      TAG,
    });
    const [responseData] = replaceSessionId(
      data,
      this.railsBrowserMapping.get(sessionId),
      this.railsSessionMapping.get(sessionId)
    );
    const ws = this.sessionState.get(sessionId);
    if (ws.isPlaywright) {
      const keyObject = this.getRegistryObject(ws.id);

      // TODO: Remove this later as this can cause memory issues
      callback = () => {
        if(isNotUndefined(reqMetaData) && isNotUndefined(reqMetaData.startTime) && reqMetaData.startTime !== 0) {
          keyObject.C2PprocessingTime = (keyObject.C2PprocessingTime || 0) + Number(process.hrtime.bigint() - reqMetaData.startTime);
        }
      };
      const playwrightVersion = keyObject.playwrightVersion;
      const clientPlaywrightVersion = (helper.nestedKeyValue(ws[kReqData], ['data', 'client.playwrightVersion']) || '').toString().trim();
      // Adding explicit sdkLanguage for client.playwrightVersion greater than or equals 1.15 & browserstack.playwrightVersion running is less than 1.15
      if (clientPlaywrightVersion && compare(clientPlaywrightVersion, '1.15.0', '>=') && compare(playwrightVersion, '1.15.0', '<')) {
        if (data.method === 'initialize') {
          ws[kReqData].data.sdkLanguage = helper.nestedKeyValue(data, ['params', 'sdkLanguage']);
          // Delaying the emit of response as playwright expects some create events from desktop before the final response.
          setTimeout(() => {
            const response = { id: 1, result: { playwright: { guid: 'Playwright' } } };
            this.desktopSocketManager.emit(kDesktopDataReceived, sessionId, JSON.stringify(response));
          }, 1500);
          return;
        }
        Object.assign(responseData.params || {}, { sdkLanguage: ws[kReqData].data.sdkLanguage });
      } else if (clientPlaywrightVersion && compare(clientPlaywrightVersion, '1.15.0', '<') && compare(playwrightVersion, '1.15.0', '>=')) {
        if (isUndefined(responseData.metadata)) {
          responseData.metadata = {};
        }
        Object.assign(responseData.metadata, { stack: [], apiName: '' });
      }
      if(clientPlaywrightVersion && compare(clientPlaywrightVersion, '1.16.0', '>=') && compare(playwrightVersion, '1.16.0', '<')){
        if (data.method === 'waitForTimeout'){
          // Mocking the response of frame.waitForTimeout for higher client.playwrightVersion https://github.com/microsoft/playwright/blob/e31b96cc267f724eb3322308ba5a96f4af83d34f/packages/playwright-core/src/server/frames.ts#L1157
          setTimeout(() => {
            const response = { id: data.id };
            this.desktopSocketManager.emit(kDesktopDataReceived, sessionId, JSON.stringify(response));
          }, helper.nestedKeyValue(data, ['params', 'timeout']) || 0);
          return;
        }
      }
      // Tracing support for playwright version mismatches
      if(helper.nestedKeyValue(responseData, ['method']) === 'tracingStartChunk'){
        if(clientPlaywrightVersion && compare(clientPlaywrightVersion, '1.15.0', '>=') && compare(clientPlaywrightVersion, '1.18.0', '<') && compare(playwrightVersion, '1.15.0', '>=')){
          if(compare(playwrightVersion, '1.17.0', '>=')){
            responseData.params = {};
          } else if(compare(playwrightVersion, '1.15.0', '>=')){
            responseData.params = undefined;
          }
        } else if(clientPlaywrightVersion && compare(clientPlaywrightVersion, '1.15.0', '>=')
                  && compare(playwrightVersion, '1.14.0', '>=') && compare(playwrightVersion, '1.15.0', '<')){
          const response = { id: responseData.id }
          this.desktopSocketManager.emit(kDesktopDataReceived, sessionId, JSON.stringify(response));
          return;
        }
      } else if(helper.nestedKeyValue(responseData, ['method']) === 'tracingStopChunk'){
        if(clientPlaywrightVersion && compare(clientPlaywrightVersion, '1.15.0', '>=')){
          if(compare(playwrightVersion, '1.15.0', '>=')){
            responseData.params = Object.assign({ mode: 'compressTrace', save: true, skipCompress: false }, responseData.params);
          } else if(compare(playwrightVersion, '1.15.0', '<')){
            responseData.method = 'tracingExport';
            responseData.params = undefined;
          }
        }
      }
      if(clientPlaywrightVersion && compare(clientPlaywrightVersion, '1.19.0', '<') && compare(playwrightVersion, '1.19.0', '>=')){
        if(["tracingStart", "tracingStartChunk", "tracingStopChunk", "tracingStop", "tracingExport"].includes(helper.nestedKeyValue(responseData,['method']))){
          responseData.guid = ws[kReqData].data.tracingGuid;
        } else if (["tracingStart", "tracingStartChunk", "tracingStopChunk", "tracingStop", "tracingExport"].includes(helper.nestedKeyValue(responseData,['params','message','method']))){
          responseData.params.message.guid = ws[kReqData].data.tracingGuid;
        }
      } else if(clientPlaywrightVersion && compare(clientPlaywrightVersion, '1.19.0', '>=') && compare(playwrightVersion, '1.19.0', '<')){
        if(["tracingStart", "tracingStartChunk", "tracingStopChunk", "tracingStop", "tracingExport"].includes(helper.nestedKeyValue(responseData,['method']))){
          responseData.guid = ws[kReqData].data.browserContextGuid;
        } else if (["tracingStart", "tracingStartChunk", "tracingStopChunk", "tracingStop", "tracingExport"].includes(helper.nestedKeyValue(responseData,['params','message','method']))){
          responseData.params.message.guid = ws[kReqData].data.browserContextGuid;
        }
      }

      if(clientPlaywrightVersion && compare(clientPlaywrightVersion, '1.25.1', '<') && compare(playwrightVersion, '1.25.1', '>=')) {
        if (isNotUndefined(responseData.metadata) && isNotUndefined(responseData.method)) {
          responseData.metadata.internal = responseData.method == 'initialize' ? true : false ;
        }
      }

      if (clientPlaywrightVersion && compare(clientPlaywrightVersion, '1.28.1', '>=') && compare(playwrightVersion, '1.28.1', '<')) {
        if (helper.nestedKeyValue(responseData, ['method']) === 'textContent') {
          if (isNotUndefined(responseData.params.selector)) {
            responseData.params.selector = responseData.params.selector.replace(/internal:testid/, 'internal:attr');
          }
        }
      }

      if (keyObject && keyObject.os && keyObject.os.toLowerCase() === 'ios' && keyObject.deviceName && clientPlaywrightVersion && compare(clientPlaywrightVersion, '1.40.0', '>=')) {
        const isApiNameBrowserContextClose =
          helper.nestedKeyValue(responseData, ['metadata', 'apiName']) === 'browserContext.close';

        const isGuidBrowserContextClose =
          helper.nestedKeyValue(responseData, ['guid'])?.toLowerCase().includes('browser-context') &&
          helper.nestedKeyValue(responseData, ['method']) === 'close';

        const hasReasonParam = isNotUndefined(helper.nestedKeyValue(responseData, ['params', 'reason']));

        if ((isApiNameBrowserContextClose || isGuidBrowserContextClose) && hasReasonParam) {
          delete responseData.params.reason;
        }
      }
    }
    this.desktopSocketManager.emit(kSendToDesktop, sessionId, responseData, callback);
  }

  /**
    * Initialize the reconnect promise to fetch the keyObject from redis
    * and connect to the terminal socket for the same.
    *
    * @param {WebSocket} ws - Client websocket connected to Hub.
    * @param {String} message - String message which is stringified JSON
    * containing the reconnect information.
    * @return {Promise<void>} Void Psuedo promise to wait for the resolution so that
    * no new message can be transfered to desktop socket manager.
    */
  initializeReconnect(ws, message) {
    if (ws[kReconnect]) {
      if (!this.reconnectConfigured.has(ws.id)) {
        this.sessionState.set(ws.id, ws);
        const reconnectData = JSON.parse(message.substring(RECONNECT_PREFIX.length));
        const { railsSessionId } = reconnectData;
        logger.info(`Received ${railsSessionId} for reconnect ${ws.id}`, { TAG });
        this.reconnectConfigured.set(ws.id, this.fillReconnectData(ws, railsSessionId, reconnectData));
      }
      return this.reconnectConfigured.get(ws.id);
    }
    return () => {};
  }

  /**
    * Creates a promise to fill the local registry with the keyObject associated with
    * the sessionId. It fetches the data from HA and inflates it in memory.
    * After fetching the data creates a remote connection to desktop and relay
    * the messages from client.
    *
    * @param {WebSocket} socket - Client WebSocket connection to Hub
    * @param {String} sessionId - Rails session ID
    * @param {Object} [reconnectData] - JSON object containing the reconnect info.
    * @return {Promise} Promise to evaluate the fetching of data from redis and
    * creating the connection. In any step if it is not able to commit successfully
    * then rejection occurs.
    */
  fillReconnectData(socket, sessionId, reconnectData = {}) {
    const { id: socketId } = socket;
    return new Promise((res, rej) => {
      HA.getData(sessionId, (err, data) => {
        if (err || data === null) {
          logger.error(`Unable to get data for ${sessionId} ${socketId}`, { TAG });
          rej(err);
        } else {
          globalRegistry[sessionId] = data;
          logger.info(`Received keyobject for ${sessionId} for ${socketId}`, { TAG });
          const { name: hostname, rproxyHost } = data;
          const { remoteConnection } = reconnectData;
          const validConnection = isNotUndefined(remoteConnection) &&
            isNotUndefined(hostname) &&
            isNotUndefined(rproxyHost);
          logger.info(`Valid connection info: ${validConnection} ${hostname} ${rproxyHost} ${remoteConnection}`);
          this.railsSessionMapping.set(socketId, sessionId);
          this.railsBrowserMapping.set(socketId, data.browserSessionId);
          this.constructor.addConnectionData(socket, remoteConnection);
          if (validConnection) {
            this.connectToDesktopSocket(socketId, remoteConnection, {
              hostname,
              reconnect: true,
              reconnectId: sessionId,
              isSelenium: socket.isSelenium
            }).then(() => {
              logger.info(`Connected to remote connection ${socketId}`, { TAG });
              this.emit(kStartTimer, socket);
              res();
            }).catch((remoteErr) => {
              logger.error(`Unable to connect to remote connection ${socketId}`, { TAG });
              logger.error(remoteErr, { TAG });
              if (socket.isSelenium) {
                pushFeatureUsage(sessionId, { ws: { exception: { timestamp: Date.now(), message: remoteErr.toString(), stacktrace: remoteErr.stack } } }, () => {});
              }
              rej(remoteErr);
            });
          } else {
            rej(new Error('Not a valid connection info'));
          }
        }
      });
    });
  }

  async messageHandler(ws, message, reqMetaData) {
    const data = JSON.parse(message);
    const { method } = data;
    const { id: sessionId } = ws;
    await this.processData(sessionId, method, ws, data, reqMetaData);
  }

  async processData(sessionId, method, ws, data, reqMetaData = {}) {
    logger.debug(`Processing message for client for sessionId: ${sessionId}`, {
      TAG,
    });
    const handlerFn = this.methodHandler(method);
    handlerFn(ws, data, { method, reqMetaData });
  }

  clientCloseHandler(ws) {
    const { id: sessionId } = ws;
    this.clearState(sessionId);
    this.desktopSocketManager.terminateSocket(sessionId);
  }

  closeClientConnection(ws, message, code) {
    ws.close(code, message);
  };

  writeResponse(ws, data, options = {}) {
    const railsSessionId = this.railsSessionMapping.get(ws.id);
    const [responseData, browserSessionId] = replaceSessionId(
      data,
      railsSessionId,
      this.railsBrowserMapping.get(ws.id)
    );

    if (browserSessionId && !this.railsBrowserMapping.has(ws.id)) {
      this.railsBrowserMapping.set(ws.id, browserSessionId);
      this.updateTime(ws);
    }

    const keyObject = this.getRegistryObject(ws.id);
    this.updateResponseLog(responseData, options, keyObject, railsSessionId);
    ws.send(JSON.stringify(responseData));

    if(isNotUndefined(options.respMetaData) && isNotUndefined(options.respMetaData.startTime) && options.respMetaData.startTime !== 0) {
      keyObject.P2CprocessingTime = (keyObject.P2CprocessingTime || 0) + Number(process.hrtime.bigint() - options.respMetaData.startTime);
    }
  }

  updateResponseLog(responseData, options, keyObject, railsSessionId) {
    this.addNormalResponse(responseData, keyObject, railsSessionId, options);
  }

  addDebugLog(keyObject, data, railsSessionId) {
    sessionLog(keyObject, 'DEBUG', this.rawLogBuilder.updateLogEntry('DEBUG', null, {
      data: {
        keyObject,
        ...data,
      },
      sessionId: railsSessionId,
    }));
  }

  generateAiHealingLog(keyObject) {
    try {
      if (isNotUndefined(keyObject.healedSelector)) {
        const logType = (isTrueString(keyObject.healedSelector?.selfHeal)) ? 'SELFHEAL' : 'SOFTHEAL';
        const using = keyObject.healedSelector?.using;
        const value = keyObject.healedSelector?.value;
        delete keyObject.healedSelector;
        return helper.s3LogFormat(logType, helper.getDate(), JSON.stringify({
          status: true,
          data: {
            using: using, // Move these values to constants in the soft heal PR
            value: value
          }
        })) + '\r\n';
      }
    } catch (error) {
      logger.error(`Error generating AI healing log for ${keyObject?.rails_session_id || 'unknown'} : ${inspect(error, false, 1)}`, {
        TAG,
      });
    }
    return '';
  }

  generatePerformanceLog(responseData, keyObject) {
    try {
      if(isNotUndefined(responseData.performance) && isTrueString(responseData.performance.enabled)) {
        return helper.s3LogFormat('PERFORMANCE', helper.getDate(), getPerformanceLogData(keyObject)) + '\r\n'
      }
    } catch (error) {
      logger.error(`Error generating performance log for ${keyObject?.rails_session_id || 'unknown'} : ${inspect(error, false, 1)}`, {
        TAG,
      });
    }
    return '';
  }

  addNormalResponse(responseData, keyObject, railsSessionId, options = {}) {
    if (responseData.id && options.stop !== true) {
      // Masking responseData only for playwright response as part of APS-12159
      let logData = (keyObject && keyObject.isPlaywright && keyObject.maskCommands) ? JSON.parse(JSON.stringify(responseData)) : responseData;
      try {
        if(keyObject.isPlaywright){
          const maskCommands = keyObject.maskCommands;
          logData = redact(logData, maskCommands, PLAYWRIGHT);
        }
      } catch (err) {
        logger.error(`Error in masking response data ${err.toString()}`, {
          TAG,
        });
      }
      const aiHealingLogLine = this.generateAiHealingLog(keyObject);
      const performanceLogLine = this.generatePerformanceLog(responseData, keyObject);
      const responseLogLine = this.rawLogBuilder.updateLogEntry('RESPONSE', options.method, {
        data: logData,
        sessionId: railsSessionId,
      });
      sessionLog(keyObject, 'RESPONSE', aiHealingLogLine + performanceLogLine + responseLogLine);
    }
  }

  closeTerminalSocket(ws) {
    this.desktopSocketManager.terminateSocket(ws.id);
  }

  addToCompletedSessions(sessionId) {
    this.completedSessions.add(sessionId);
  }

  /**
    * Pushes updated time to redis for keyObject update. Since in websocket
    * session we do not update the keyObject that often. It leads to problem
    * with the rake that is getting status from Hub HA_* keyObject spance and
    * touching the updated_at column for the session in database.
    *
    * Automation Session `updated_at` column is being used the SMD rake for marking
    * the session as done and in those cases the logs won't get uploaded. So to
    * mark the session alive we constantly update the keyObject and also in that
    * the `lastRequestTime` key since this key is only used by the rake for updation.
    *
    * @param {WebSocket} ws - Client Websocket object which has the unique identifier
    * @return {void} Publishes the update event for keyObject update in redis.
    */
  updateTime(ws) {
    const { id: socketId } = ws;
    if (this.railsSessionMapping.has(socketId)) {
      const railsSessionId = this.railsSessionMapping.get(socketId);
      const keyObject = globalRegistry[railsSessionId];
      if (isHash(keyObject)) {
        keyObject.lastRequestTime = Date.now();
        keyObject.browserSessionId = this.railsBrowserMapping.get(socketId);
        logger.info(`Updating keyObject for ${railsSessionId}`, { TAG });
        HA.setData(railsSessionId, keyObject, (err) => {
          if (isNotUndefined(err)) {
            logger.error(`Received error while updating time of keyObject for ${railsSessionId} : ${inspect(err, false, 1)}`, {
              TAG,
            });
          }
        });
      }
    }
  }

  async startTerminalCleanup(sessionId) {
    try {
      const resp = await stopTerminal(this.railsSessionMapping.get(sessionId));
      return resp;
    } catch (err) {
      return null;
    }
  }

  // eslint-disable-next-line class-methods-use-this
  takeScreenshotHandler(response, keyObject, framework) {
    // modify the object as needed for uploadScreenshotToS3

    const resp = s3UploadHelper.uploadScreenshotToS3(
      JSON.stringify(response.result),
      keyObject,
      helper.randomID(5),
      framework
    );
    try {
      response.result = JSON.parse(resp);
    } catch (e) {
      logger.error(`json-data-error from uploadScreenshotToS3 ${e.toString()}`, {
        TAG,
      });
    }
  }

  shouldIgnoreMethod(framework, method) {
    return this.IGNORED_METHODS[framework].includes(method);
  }
}

module.exports = SocketMessageHandler;
