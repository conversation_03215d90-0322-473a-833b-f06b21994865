'use strict';

const PuppeteerRawLog = require('./puppeteerRawLog');
const {
  NAVIGATE_TO,
  CLICK,
  CAPTURE_SCREENSHOT,
  SET_FILE,
  FIND_ELEMENT,
  NEW_CONTEXT
} = require('../helpers/customSeleniumHandling/frameworkConstants');
const {
  isHash,
} = require('../typeSanity');

class PlaywrightRawLog extends PuppeteerRawLog {
  generateMethodLogMapping() {
    this.methodLogMap.set('goto', NAVIGATE_TO);
    this.methodLogMap.set('newContext', NEW_CONTEXT);
    this.methodLogMap.set('screenshot', CAPTURE_SCREENSHOT);
    this.methodLogMap.set('click', CLICK);
    this.methodLogMap.set('setInputFiles', SET_FILE);
    this.methodLogMap.set('querySelector', FIND_ELEMENT);
  }

  generatePayloadMapping() {
    this.methodPayloadMap.set('goto', PlaywrightRawLog.pageOpenPayload);
    this.methodPayloadMap.set('newContext', PlaywrightRawLog.newContextCreated);
    this.methodPayloadMap.set('evaluate', PlaywrightRawLog.executeScript);
    this.methodPayloadMap.set('click', PlaywrightRawLog.executeClick);
    this.methodPayloadMap.set('tap', PlaywrightRawLog.executeTap);
    this.methodPayloadMap.set('setInputFiles', PlaywrightRawLog.setInputFiles);
    this.methodPayloadMap.set('querySelector', PlaywrightRawLog.findElementSelector);
    this.methodPayloadMap.set('resp-screenshot', PlaywrightRawLog.takeScreenshot);
    this.methodPayloadMap.set('evaluateExpression', PlaywrightRawLog.evaluateExpression);
  }

  static evaluateExpression(data) {
    return isHash(data) && isHash(data.value) ? data.value.s : data;
  }

  static takeScreenshot(data) {
    return { value: data.value };
  }

  static pageOpenPayload({ url = '', waitUntil }) {
    return {
      url,
      ...waitUntil && { waitUntil }
    };
  }

  static newContextCreated(contextOptions) {
    return contextOptions;
  }

  static executeScript({ execute = '' }) {
    return { execute };
  }

  static executeClick({ selector = '' }) {
    return { selector };
  }

  static findElementSelector({ selector = '' }) {
    return { selector };
  }

  static executeTap({ selector = '', position = { x: 0, y: 0 } }) {
    return {
      selector,
      position
    };
  }

  static setInputFiles({ files = [], selector = '' }) {
    return {
      name: files.map(({ name }) => name).join(','),
      selector
    };
  }
}

module.exports = PlaywrightRawLog;
