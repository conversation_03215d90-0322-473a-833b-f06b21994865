'use strict';

const { inspect } = require('util');
const { call: request } = require('../lib/request');
const querystring = require('querystring');
const { SERVER_PORT, sessionTimeout: kSessionTimeout, PUPPETEER_CONSTANTS } = require('../constants');
const { kPlaywrightType, kDetoxType, REQUEST_TIMEOUT } = require('../config/socketConstants');
const pubSub = require('../pubSub');
const logger = require('../logger').basicLogger;
const BrowserContext = require('./browserContext');
const { isNotUndefined } = require('../typeSanity');

const TAG = 'TerminalAllocation';

const allocateTerminal = async ({
  data, type, authReqs, currWS
}) => {
  // Currently assuming if there is no type assume it is a CDP
  // session and if explicitly passed as playwright then update the
  // data accordingly.
  if (type === kPlaywrightType) {
    data.isPlaywright = true;
  } else if (type === kDetoxType) {
    data.isDetox = true;
  } else {
    data.isPuppeteer = true;
    const browserName = ((data.browser || data.browserName || '').toLowerCase()).trim();
    if (['chrome', 'firefox', 'edge'].includes(browserName)) {
      const browserContext = new BrowserContext(browserName, PUPPETEER_CONSTANTS.PUPPETEER);
      data = browserContext.getBrowserArgs(data);
    }
  }

  const sessionCaps = {
    desiredCapabilities: data,
  };

  const headers = {
    'content-type': 'application/json; charset=utf-8',
    accept: 'application/json',
    'content-length': Buffer.byteLength(JSON.stringify(sessionCaps), 'utf-8'),
    Authorization: `Basic ${Buffer.from(`${data['browserstack.user']}:${data['browserstack.key']}`).toString('base64')}`,
  };

  const requestOptions = {
    method: 'POST',
    path: '/wd/hub/session',
    headers,
    timeout: REQUEST_TIMEOUT,
    port: SERVER_PORT,
    scheme: 'http',
    hostname: 'localhost',
    body: JSON.stringify(sessionCaps),
    ...authReqs && { authReqs },
    ...currWS && { currWS }
  };

  let responseData = {};
  try {
    const resp = await request(requestOptions);
    responseData = JSON.parse(resp.data);
    if (resp.statusCode === 200) {
      return responseData;
    }
  } catch (err) {
    logger.error(`Error while allocating a terminal: ${err}`, {
      TAG,
    });
    throw new Error(PUPPETEER_CONSTANTS.ERROR_MESSAGE.TERMINAL_ALLOCATION_FAILURE);
  }

  throw new Error(responseData.value && responseData.value.message ? responseData.value.message : PUPPETEER_CONSTANTS.ERROR_MESSAGE.TERMINAL_ALLOCATION_FAILURE);
};

const stopTerminal = async (railsSessionId) => {
  try {
    const headers = {
      'content-type': 'application/json; charset=utf-8',
      accept: 'application/json',
    };

    const reqOptions = {
      method: 'DELETE',
      path: `/wd/hub/session/${railsSessionId}`,
      headers,
      timeout: REQUEST_TIMEOUT,
      port: SERVER_PORT,
      scheme: 'http',
      hostname: 'localhost',
    };

    const resp = await request(reqOptions);
    if (resp.statusCode === 200) {
      return JSON.parse(resp.data);
    }
    throw new Error(resp);
  } catch (err) {
    logger.error(`Error while stopping a terminal: ${inspect(err, false, 1)}`, {
      TAG,
    });
    throw new Error('Unable to stop the terminal');
  }
};

const markIdle = async (railsSessionId, message, errorMessage = undefined) => {
  const headers = {
    'content-type': 'application/json; charset=utf-8',
    accept: 'application/json',
  };

  const queryArgs = {
    sessionId: railsSessionId,
    ...message && { message }
  };

  if(isNotUndefined(errorMessage)) {
    queryArgs["errorMessage"] = errorMessage;
  }

  const reqOptions = {
    method: 'GET',
    path: `/session_timedout?${querystring.encode(queryArgs)}`,
    headers,
    timeout: REQUEST_TIMEOUT,
    port: SERVER_PORT,
    scheme: 'http',
    hostname: 'localhost',
  };

  try {
    const resp = await request(reqOptions);
    if (resp.statusCode === 200) {
      pubSub.publish(kSessionTimeout, railsSessionId);
      return resp.data;
    }
    throw new Error(resp.data);
  } catch (err) {
    throw err;
  }
};

const getSessionStatus = async (railsSessionId) => {
  const headers = {
    'content-type': 'application/json; charset=utf-8',
    accept: 'application/json',
  };

  const reqOptions = {
    method: 'GET',
    path: `/wd/hub/session/${railsSessionId}/url`,
    headers,
    timeout: REQUEST_TIMEOUT,
    port: SERVER_PORT,
    scheme: 'http',
    hostname: 'localhost',
  };

  try {
    const resp = await request(reqOptions);
    if (resp.statusCode === 200) {
      return;
    }
  } catch (err) {
    logger.info(`Error in getSessionStatus while getting URL: ${err}`, {
      TAG,
    });
  }
};

module.exports = {
  allocateTerminal,
  stopTerminal,
  markIdle,
  getSessionStatus,
};
