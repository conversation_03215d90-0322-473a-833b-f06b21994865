'use strict';

const { inspect } = require('util');
const SocketMessageHandler = require('./baseSocketMessageHandler');
const {
  isNotUndefined,
  isUndefined,
  isHash
} = require('../typeSanity');
const {
  kSendToDesktop,
  kSendReconnectInfo,
  RECONNECT_PREFIX,
  SOCKET_IDLE_MESSAGE,
  ERROR_MESSAGE
} = require('../config/socketConstants');
const HA = require('../ha');
const async = require('async');
const requestlib = require('../lib/request');
const { global_registry: globalRegistry, osHostName, other_hub_servers } = require('../constants');
const helper = require('../helper');
const pubSub = require('../pubSub');
const constants = require('../constants');
const logger = require('../logger').basicLogger;
const SeleniumRawLog = require('./seleniumRawLog');
const { pushFeatureUsage } = require('../helpers/instrumentation');
const hub = require('../hub');
const { refreshAndAddKeyObjectToThisHub } = require('../services/session/sessionManagerHelper');

const TAG = 'SeleniumHandler';

class SeleniumHandler extends SocketMessageHandler {
  constructor() {
    super();
    this.rawLogState = new Map();
  }

  // eslint-disable-next-line class-methods-use-this
  enableMethodMaps() {}

  // eslint-disable-next-line class-methods-use-this
  debugScreenshotMethodMaps() {}

  assignLogBuilder() {
    this.rawLogBuilder = new SeleniumRawLog();
  }

  async messageHandler(ws, message) {
    try {
      await this.initializeReconnect(ws, message);
      if (message.substring(0, 10) === RECONNECT_PREFIX) {
        return;
      }
      this.updateTime(ws, message);
      const data = JSON.parse(message);
      const { method } = data;
      const { id: sessionId } = ws;
      await this.waitForAllocation(sessionId);
      const railsSessionId = this.railsSessionMapping.get(ws.id);
      logger.info(`Processing: ws_id - ${sessionId}, method - ${method}, data_length - ${message ? message.length : 0}, railsSessionId - ${railsSessionId}`, { TAG, log: { uuid: railsSessionId, kind: 'MESSAGE_HANDLER_SELENIUM' } });
      await this.processData(sessionId, method, ws, data);
    } catch (err) {
      logger.error(`Error in the message handler: ${inspect(err, false, 1)}`, {
        TAG,
      });
      pushFeatureUsage(ws.id, { ws: { exception: { timestamp: Date.now(), message: isNotUndefined(err) && err.toString(), stacktrace: isNotUndefined(err) && err.stack } } }, () => {});
      helper.PingZombie({
        timestamp: Date.now(),
        kind: 'hub-ws-error',
        data: `Error in the message handler: ${isNotUndefined(err) ? inspect(err, false, 1) : ERROR_MESSAGE}`,
        region: constants.region,
        machine: constants.osHostName,
        session_id: ws.id
      });
      ws.close(1001, isNotUndefined(err) ? err.toString().substring(0, 122) : ERROR_MESSAGE);
    }
  }

  waitForAllocation(socketId) {
    if (this.terminalAllocationState.has(socketId)) {
      return this.terminalAllocationState.get(socketId);
    }
    return Promise.resolve();
  }

  async preProcessAllocation(socket) {
    const { id: socketId } = socket;
    try {
      socket.sessionId = socketId;
      await this.setUpstream(socket);
      this.startAllocation(socketId, socket);
      this.updateTime(socket);
    } catch (err) {
      logger.error(`Error in preprocess allocation for the selenium session ${inspect(err, false, 1)}`, { TAG });
      pushFeatureUsage(socketId, { ws: { exception: { timestamp: Date.now(), message: err.toString(), stacktrace: err.stack } } }, () => {});
      helper.PingZombie({
        timestamp: Date.now(),
        kind: 'hub-ws-error',
        data: `Error in preprocess allocation for the selenium session ${inspect(err, false, 1)}`,
        region: constants.region,
        machine: constants.osHostName,
        session_id: socketId
      });
      socket.close(1001, err.toString().substring(0, 122)); // truncating err msg to avoid bytes range error
    }
  }

  connectToDesktopSocket(sessionId, wsURL, opts = {}) {
    return this.desktopSocketManager.addNewSocket(sessionId, wsURL, opts);
  }

  /**
  * Initiates the terminal proxy connection process for the session and creates
  * a promise so that the messages coming to the server can be holded off
  * till the promise is resolved or rejected.
  *
  * @param {String} socketId - Unique socket identifier assigned on connection.
  * @param {WebSocket} ws - WebSocket instance connected to server.
  * @throws {Error} - Unable to get websocket url when it is not received from terminal.
  * @throws {Error} - Unable to start browser because of some unforseen error.
  * @throws {Error} - Error from upstream hub process in allocation of terminal.
  * @return {void} Sets the allocation promise in the terminal state.
  */
  startAllocation(socketId, ws) {
    this.terminalAllocationState.set(socketId, new Promise((resolve, reject) => {
      this.sessionState.set(socketId, ws);
      this.railsSessionMapping.set(socketId, socketId);
      this.constructor.addConnectionData(ws, ws.upstream);
      this.connectToDesktopSocket(socketId, ws.upstream, {
        hostname: ws.hostname,
        reconnectId: socketId,
        isSelenium: true
      }).then(() => {
        this.emit(kSendReconnectInfo, ws, this.provideReconnectInfo(ws));
        resolve();
      }).catch((err) => {
        logger.error(`Could not connect to desktop ${ws.id} ${err.toString()} ${err.stack}`, { TAG });
        pushFeatureUsage(socketId, { ws: { exception: { timestamp: Date.now(), message: err.toString(), stacktrace: err.stack } } }, () => {});
        helper.PingZombie({
          timestamp: Date.now(),
          kind: 'hub-ws-error',
          data: `Could not connect to desktop ${ws.id} ${err.toString()} ${err.stack}`,
          region: constants.region,
          machine: constants.osHostName,
          session_id: socketId
        });
        reject(err);
      });
    }));
  }

  checkIfSessionOnOtherHubs(sessionId, cb) {
    let hubIndex = 0;

    function makeRequestToNextHub() {
      if (hubIndex >= other_hub_servers.length) {
        return cb(new Error('sessionId state not found'));
      }
      const hub_addr = other_hub_servers[hubIndex];
      requestlib.call({
        hostname: hub_addr,
        path: `/session/get_session_data?sessionId=${sessionId}&cdp=true`,
        timeout: 20000,
        retries: 2
      }).then((res) => {
        try {
          const data = JSON.parse(res.data);
          if (data.found) {
            refreshAndAddKeyObjectToThisHub(sessionId, hub_addr, data);
            cb(null, data);
            return;
          } else {
            makeRequestToNextHub();
          }
        } catch (error) {
          makeRequestToNextHub();
        }
      }).catch((err) => {
        makeRequestToNextHub();
      });
      hubIndex++;
    }

    makeRequestToNextHub();
  }

  // eslint-disable-next-line class-methods-use-this
  setUpstream(ws) {
    return new Promise((resolve, reject) => {
      const setUpstreamFromKeyObject = (keyObject) => {
        if (isNotUndefined(keyObject.seBidiUpstream)) {
          ws.upstream = keyObject.seBidiUpstream;
        } else if (isNotUndefined(keyObject.seCdpUpstream)) {
          ws.upstream = keyObject.seCdpUpstream;
        } else {
          logger.info(`[SETUPSTREAM] upStream url not present`);
          reject(`[SETUPSTREAM] upStream url not present`);
        }
        logger.info(`ws.upstream: ${ws.upstream}`);
        ws.hostname = keyObject.name;
        resolve();
      };
      if (isUndefined(globalRegistry[ws.id])) {
        HA.getData(ws.id, (err, data) => {
          if (err || data === null) {
            this.checkIfSessionOnOtherHubs(ws.id, (err, data) => {
              if (err) {
                reject(err);
              }
              if (data) {
                setUpstreamFromKeyObject(globalRegistry[ws.id]);
              }
            });
          } else {
            logger.info(`keyObject found in Redis for ${ws.id}`, { TAG });
            globalRegistry[ws.id] = data;
            setUpstreamFromKeyObject(data);
          }
        });
      } else {
        setUpstreamFromKeyObject(globalRegistry[ws.id]);
      }
    });
  }

  writeResponse(ws, data, options = {}) {
    const railsSessionId = this.railsSessionMapping.get(ws.id);
    const responseData = data;
    const browserSessionId = helper.nestedKeyValue(data, ['result', 'sessionId']);

    if (browserSessionId && !this.railsBrowserMapping.has(ws.id)) {
      this.railsBrowserMapping.set(ws.id, browserSessionId);
    }

    const keyObject = this.getRegistryObject(ws.id);
    this.updateResponseLog(responseData, options, keyObject, railsSessionId);
    ws.send(JSON.stringify(responseData));
  }

  async sendToTerminal(data, sessionId) {
    this.desktopSocketManager.emit(kSendToDesktop, sessionId, data);
  }

  /**
  * Pushes updated time to redis for keyObject update. Since in websocket
  * session we do not update the keyObject that often. It leads to problem
  * with the rake that is getting status from Hub HA_* keyObject spance and
  * touching the updated_at column for the session in database.
  *
  * Automation Session `updated_at` column is being used the SMD rake for marking
  * the session as done and in those cases the logs won't get uploaded. So to
  * mark the session alive we constantly update the keyObject and also in that
  * the `lastRequestTime` key since this key is only used by the rake for updation.
  *
  * Timeout Manager is reset
  * @param {WebSocket} ws - Client Websocket object which has the unique identifier
  * @return {void} Publishes the update event for keyObject update in redis.
  */
  // eslint-disable-next-line class-methods-use-this
  updateTime(ws, message) {
    const { id: socketId } = ws;
    const railsSessionId = socketId;
    const keyObject = globalRegistry[railsSessionId];
    const updateTimeCB = (data) => {
      data.lastRequestTime = Date.now();
      data.browserSessionId = this.railsBrowserMapping.get(socketId);
      const changed = { lastRequestTime: data.lastRequestTime };
      if (message) {
        data.socketMessages = isNotUndefined(data.socketMessages) ? data.socketMessages + 1 : 1;
        changed.socketMessages = data.socketMessages;
      }
      pubSub.publish(constants.updateKeyObject, {
        session: railsSessionId,
        changed
      });
      logger.info(`Updating keyObject for ${railsSessionId}`, { TAG });
      HA.setData(railsSessionId, data, (err) => {
        if (isNotUndefined(err)) {
          logger.error(`Received error while updating time of keyObject for ${railsSessionId} : ${inspect(err, false, 1)}`, {
            TAG,
          });
        }
      });
      helper.timeoutManagerUpdateTimeout(railsSessionId, data);
    };
    if (isHash(keyObject)) {
      updateTimeCB(keyObject);
    } else {
      HA.getData(railsSessionId, (err, data) => {
        if (err || data === null) {
          logger.info(`keyObject not found for ${railsSessionId}`, { TAG });
          ws.close(1001, SOCKET_IDLE_MESSAGE);
        } else {
          logger.info(`keyObject found in Redis for ${railsSessionId}`, { TAG });
          globalRegistry[railsSessionId] = data;
          updateTimeCB(data);
        }
      });
    }
  }

  clearState(sessionId) {
    const railsSessionId = this.railsSessionMapping.get(sessionId);
    this.rawLogState.delete(railsSessionId);
    super.clearState(sessionId);
  }
}

module.exports = SeleniumHandler;
