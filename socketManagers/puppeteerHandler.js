'use strict';

const { inspect } = require('util');
const SocketMessageHandler = require('./baseSocketMessageHandler');
const {
  isUndefined,
  isString,
  isNotUndefined,
  isHash
} = require('../typeSanity');
const { allocateTerminal, markIdle, getSessionStatus } = require('./terminalAllocation');
const {
  kReqData,
  kReconnect,
  kSendReconnectInfo,
  kSessionDebug,
  RECONNECT_PREFIX,
  kDesktopDataReceived,
  kStartTimer,
  kConsoleLog,
  kPingTime,
  ABRUPT_SOCKET_MESSAGES,
  GRACEFULE_CLOSE_CODES,
  ERROR_MESSAGE,
  PUPPETEER
} = require('../config/socketConstants');
const HA = require('../ha');
const {
  global_registry: globalRegistry
} = require('../constants');
const helper = require('../helper');
const logger = require('../logger').basicLogger;
const {
  checkBstackExecutorString,
  checkandExecuteIfBstackExecutor: executeBStackExecutor
} = require('../lib/customSeleniumHandler/jsExecutor');
const PuppeteerRawLog = require('./puppeteerRawLog');
const S3UploadHelper = require('../helpers/s3UploadHelper');
const HubLogger = require('../log');
const { checkValidPuppeteerCommand, triggerLighthouse, processCdpExecutorResponse } = require('../helpers/lighthouse/helper');

const SESSION_STATUS_PING = 10 * 60 * 1000;

const TAG = 'PuppeteerHandler';

const FUNCTION_LIST = new Map([
  ['(element, selector) => element.querySelector(selector)', 'Page.customQuerySelector'],
  ['(element) => element.focus()', 'Page.customFocus'],
]);

class PuppeteerHandler extends SocketMessageHandler {
  constructor() {
    super();

    this.rawLogState = new Map();
    this.keyLogEvents = new Map();
  }

  enableMethodMaps() {
    this.methodMapping.set('Browser.close', this.closeBrowser.bind(this));
    this.methodMapping.set('Input.dispatchKeyEvent', this.inputKeyHandler.bind(this));
    this.methodMapping.set('Runtime.callFunctionOn', this.executeJavascriptFunction.bind(this));
  }

  debugScreenshotMethodMaps() {
    this.debugScreenshotMethodMapping.add('Page.navigate');
    this.debugScreenshotMethodMapping.add('Browser.close');
    this.debugScreenshotMethodMapping.add('Page.handleJavaScriptDialog');
    this.debugScreenshotMethodMapping.add('Page.click');
  }

  assignLogBuilder() {
    this.rawLogBuilder = new PuppeteerRawLog();
  }

  static identifyJSMethod(methodStr, defaultMethod = 'Runtime.callFunctionOn') {
    return FUNCTION_LIST.get(methodStr) || defaultMethod;
  }

  logBSExecutor(ws, evalString, id, data) {
    const logData = {
      sessionId: data.sessionId,
      params: {
        result: {
          value: {
            script: evalString,
            args: []
          }
        }
      },
      id,
      method: 'evaluateExpression'
    };
    this.sendToRequestLog(ws, logData.method, logData, ws.id);
  }

  async executeJavascriptFunction(ws, data) {
    const { id, sessionId, params: { arguments: executeArgs, functionDeclaration = '' } } = data;
    const strippedFunction = functionDeclaration.replace(/\/\/# sourceURL=__puppeteer_evaluation_script__(.*)/, '').trim();
    const modMethod = PuppeteerHandler.identifyJSMethod(strippedFunction);
    const executorString = isUndefined(executeArgs) ? undefined : executeArgs[0];
    const isBStackExecutor = isHash(executorString) &&
      isString(executorString.value) &&
      checkBstackExecutorString(executorString.value);

    if (id) {
      if (isBStackExecutor) {
        this.logBSExecutor(ws, executorString.value, id, data);
      }
      else {
        this.sendToRequestLog(ws, modMethod, {
          id,
          sessionId,
          method: modMethod,
          params: {
            value: strippedFunction,
            args: executeArgs || []
          },
        }, ws.id);
      }
    }
    if (isBStackExecutor) {
      const executeResponse = await new Promise(async (resolve) => {
        const requestStateObj = {
          req_data: JSON.stringify({
            script: executorString.value
          }),
          onResolve: resolve
        };

        let registry = this.getRegistryObject(ws.id);
        if(isUndefined(registry)){
          await HA.getData(ws.id, (err, registryData) => {
            if (err) {
              logger.error(`SessionId ${ws.id} state not found`, {
                TAG,
              });
            } else {
              globalRegistry[ws.id] = registryData;
              registry = registryData;
            }
          });
        }
        executeBStackExecutor(registry, requestStateObj);
      });
      this.sendExecuteSyncResponse(executeResponse, ws, id, sessionId);
    } else {
      this.sendToTerminal(data, ws.id);
    }
  }

  sendExecuteSyncResponse(executeResponse = '{}', ws, id, sessionId) {
    const jsonData = JSON.parse(executeResponse);
    this.desktopSocketManager.emit(kDesktopDataReceived, ws.id, JSON.stringify({
      id,
      performance: processCdpExecutorResponse(jsonData),
      result: {
        result: {
          type: 'string',
          value: isString(jsonData.value) ? jsonData.value : JSON.stringify(jsonData.value)
        }
      },
      sessionId,
      method: 'evaluateExpression',
    }));
  }

  inputKeyHandler(ws, data) {
    const { sessionId: railsSessionId, params: { type = '', key = '' } } = data;
    if (type.toLowerCase() === 'keydown') {
      if (this.keyLogEvents.has(railsSessionId)) {
        const presentValue = this.keyLogEvents.get(railsSessionId);
        this.keyLogEvents.set(railsSessionId, presentValue + key);
      } else {
        this.keyLogEvents.set(railsSessionId, key);
      }
    }
    this.sendToTerminal(data, ws.id);
  }

  async messageHandler(ws, message) {
    let messageId;
    try {
      await this.initializeReconnect(ws, message);
      if (message.substring(0, 10) === RECONNECT_PREFIX) {
        logger.info(`Received reconnection message ${ws.id}`, { TAG });
        return;
      }
      const data = JSON.parse(message);
      const { method, id: dataId, sessionId: puppeteerSessionId } = data;
      const { id: sessionId } = ws;
      const railsSessionId = this.railsSessionMapping.get(ws.id);
      logger.info(`Processing: ws_id - ${sessionId}, method - ${method}, data_length - ${message ? message.length : 0}, railsSessionId - ${railsSessionId}`, { TAG, log: { uuid: railsSessionId, kind: 'MESSAGE_HANDLER_PUPPETEER' } });
      if (isNotUndefined(dataId)) {
        messageId = dataId;
      }

      if (!this.terminalAllocationState.has(sessionId) && !ws[kReconnect]) {
        // Perform the authentication and authorization and resolve the promise
        this.setAllocationPromise(sessionId, ws);
      } else if (ws[kReconnect]) {
        // Since we already have the reconnectInfo and connected to it, we do
        // not require the allocation again.
        this.terminalAllocationState.set(sessionId, Promise.resolve());
      }
      await this.terminalAllocationState.get(sessionId);
      this.updateWSId(ws, puppeteerSessionId);

      try {
        const keyObject = this.getRegistryObject(ws.id);
        const lighHouseEnabled = keyObject.lighthouseAutomate && keyObject.lighthouseAutomate.report_limit;
        // TODO: Check for lighthouse capability
        if (lighHouseEnabled && checkValidPuppeteerCommand(data)) {
          const url = data.params.url;
          logger.info(`lighthouse-${PUPPETEER}-trigger-start, session: ${keyObject.rails_session_id} url: ${url}`, { TAG });
          await triggerLighthouse(url, keyObject, PUPPETEER);
          logger.info(`Validated, trigger lighthouse on terminal: ${JSON.stringify(keyObject.lighthouseAutomate)}`, { TAG });
        }
      } catch (error) {
        // TODO: Log error to pager/feature usage
        logger.error(`Error reaching to terminal: ${JSON.stringify(error)}`, { TAG });
      }
      await this.processData(sessionId, method, ws, data);
    } catch (err) {
      logger.error(`Error in the message handler: ${inspect(err, false, 1)}`, {
        TAG,
      });
      // Puppeteer client binding looks for the error key in the payload be it if there is
      // a sessionId or not. It looks for the message and data key inside the error
      // key. We are just setting the error message in response to the current message so
      // that client can know what happens in the backend and why the session is
      // unable to start.
      //
      // There need to be a valid id for writing the respons because the cleint side code
      // checks for the callback registered with the `id` and rejects and resolves based
      // on the payload message received.
      if (isNotUndefined(messageId)) {
        PuppeteerHandler.writeErrorMessage(ws, err, {
          id: messageId,
        });
      }
      ws.close(1001, isNotUndefined(err) ? err.toString().substring(0, 122) : ERROR_MESSAGE);
    }
  }

  /**
  * Writes error message on the socket so that client can close the socket and
  * also can happen when the session is unable to start session due to internal or extenal
  * reasons.
  *
  * @static
  * @param {WebSocket} ws - Client Socket
  * @param {Error} error - Error object generated in backend while handling message.
  * @param {Object} [options] - Extra Arguments like id and method that needs to be sent
  * in the payload.
  * @return {void}
  */
  static writeErrorMessage(ws, error, options = {}) {
    ws.send(JSON.stringify({
      ...options,
      error: {
        message: error.toString(),
      },
    }));
  }

  setAllocationPromise(sessionId, ws) {
    this.terminalAllocationState.set(sessionId, new Promise((resolve, reject) => {
      allocateTerminal({
        ...ws[kReqData],
        authReqs: this.authRequests,
        currWS: sessionId
      })
        .then((resData) => {
          const {
            status,
            sessionId: railsSessionId,
            value: { wsURL, message, wsHostname },
          } = resData;
          if (status === 0 && isNotUndefined(wsURL)) {
            this.constructor.addConnectionData(ws, wsURL);
            logger.info(`Received websocket url for ${railsSessionId} : ${wsURL} and ${sessionId}`, {
              TAG,
            });
            this.railsSessionMapping.set(sessionId, railsSessionId);
            let dataToSend = {
              sessionid: railsSessionId,
              client_ip: ws.clientIp,
              product_package: 'cbt',
              kind: "cdp_session_stats"
            }
            helper.PingZombie(dataToSend);
            return this.connectToDesktopSocket(sessionId, wsURL, {
              hostname: wsHostname,
              reconnectId: railsSessionId
            });
          }

          if (isUndefined(wsURL)) {
            logger.error(`Websocket url is undefined for sessionId: ${railsSessionId}`, {
              TAG,
            });
          }

          if (status === 13 && isNotUndefined(message)) {
            throw new Error(message);
          }

          throw new Error('Unable to start browser');
        })
        .then(() => new Promise((kObjectResolve, kObjectReject) => {
          const railsSessionId = this.railsSessionMapping.get(sessionId);
          const keyObject = globalRegistry[railsSessionId];
          if (isUndefined(keyObject)) {
            HA.getData(railsSessionId, (err, data) => {
              if (err) {
                kObjectReject(new Error('sessionId state not found'));
              } else {
                globalRegistry[railsSessionId] = data;
                kObjectResolve(data);
              }
            });
          } else {
            kObjectResolve(keyObject);
          }
        }))
        .then(() => {
          this.sessionState.set(sessionId, ws);
          this.emit(kStartTimer, ws);
          this.emit(kSendReconnectInfo, ws, this.provideReconnectInfo(ws));
        })
        .then(resolve)
        .catch(reject);
    }));
  }

  updateWSId(ws, sessionId) {
    if (ws.id !== sessionId && this.sessionState.has(sessionId)) {
      ws.id = sessionId;
    }
  }

  static uploadConsoleLog(keyObject, data, bsConsoleLogLevel) {
    helper.uploadCDPConsoleLog(keyObject, data, bsConsoleLogLevel);
  }

  writeResponse(ws, data, opts = {}) {
    if (ws[kSessionDebug] && data.id && this.railsSessionMapping.has(ws.id)) {
      opts.debug = {
        screenshotCounter: data.id,
      };
    }

    if (isNotUndefined(data) && ws[kConsoleLog] !== -1) {
      const keyObject = this.getRegistryObject(ws.id);
      PuppeteerHandler.uploadConsoleLog(keyObject, data, ws[kConsoleLog]);
    }

    if ((new Date() - ws[kPingTime]) >= SESSION_STATUS_PING) {
      ws[kPingTime] = new Date();
      const railsSessionId = this.railsSessionMapping.get(ws.id);
      getSessionStatus(railsSessionId);
    }

    super.writeResponse(ws, data, opts);
  }

  async closeBrowser(ws, data) {
    const keyObject = this.getRegistryObject(ws.id);
    this.addRequestLog(keyObject, 'Browser.close', data, ws.id, {
      stop: true,
    });
    // Eager close of puppetter as socket of client gets closed
    // at this response.
    const resp = {
      id: data.id,
      method: data.method,
      result: {},
    };
    this.addToCompletedSessions(ws.id);
    this.writeResponse(ws, resp, { stop: true });
    this.closeTerminalSocket(ws);
    this.startTerminalCleanup(ws.id);
  }

  async clientCloseHandler(ws, { code } = {}) {
    this.closePendingAuthRequest(ws.id);
    if (!this.completedSessions.has(ws.id)
      && this.railsSessionMapping.has(ws.id)
      && !GRACEFULE_CLOSE_CODES.has(code)) {
      const railsSessionId = this.railsSessionMapping.get(ws.id);
      logger.info(`Initiating idle timeout for ${railsSessionId} for ${ws.id}`, { TAG });
      try {
        await markIdle(railsSessionId, ABRUPT_SOCKET_MESSAGES.get(code));
      } catch (err) {
        logger.error('Unable to mark session as idle ', {
          TAG,
        });
      }
    } else if (!this.completedSessions.has(ws.id) && GRACEFULE_CLOSE_CODES.has(code)) {
      // If the session sent `close` packet and we marked the session in closed
      // sessions then use the graceful flow.
      this.closeTerminalSocket(ws);
      await this.startTerminalCleanup(ws.id);
    }
    logger.info(`Socket close data ${ws.id}: ${this.completedSessions.has(ws.id)} and ${this.railsSessionMapping.has(ws.id)}`, {
      TAG,
    });
    super.clientCloseHandler(ws);
  }

  clearState(sessionId) {
    const railsSessionId = this.railsSessionMapping.get(sessionId);
    this.rawLogState.delete(railsSessionId);
    this.keyLogEvents.delete(railsSessionId);
    super.clearState(sessionId);
  }

  /**
  * Add the requestLog objects to raw logs state. This method
  * sets the request entry in the state so that if the corresponding
  * response object is received then we can push both of them into the
  * log file, due to lack of REQUEST_RESPONSE type present because of
  * communication over websocket and async nature.
  *
  * @param {String} sessionId - Rails session id for session
  * @param {String} method - CDP method that need to be executed
  * @param {Object} data - Payload sent by the client for the method
  * @return {void} In case if the payload contains a single key, which is some information
  * that can be present into the payload, insert that packet into
  * the log state for mapping it with the response received latter. This is done
  * to avoid empty hashes getting dumped into the logs since it doesn't makes
  * a lot of sense to present them to user.
  */
  addEntryToRawLog(sessionId, method, data) {
    const { id } = data;
    const invalidParams = isHash(data) && (!isHash(data.params) || (isHash(data.params) &&
      Object.keys(data.params).length === 0));
    if (invalidParams) return;

    if (!this.rawLogState.has(sessionId)) {
      this.rawLogState.set(sessionId, {});
    }

    this.rawLogState.get(sessionId)[id] = {
      method,
      request: { ...data, currTime: helper.getDate() },
    };
  }

  addRequestLog(keyObject, method, data, sessionId, options = {}) {
    const { id, sessionId: dataSessionId } = data;
    const railsSessionId = this.railsSessionMapping.get(sessionId);

    if (this.keyLogEvents.has(railsSessionId)) {
      const currText = this.keyLogEvents.get(railsSessionId);
      this.addEntryToRawLog(railsSessionId, 'Input.dispatchKeyEvent', {
        id: id - 1,
        params: {
          value: currText,
        },
      });
      const stateObj = this.rawLogState.get(railsSessionId)[id - 1];
      stateObj.response = {
        id: id - 1,
        results: {},
      };
      this.sendToKafka(keyObject, stateObj, railsSessionId, {});
      this.keyLogEvents.delete(railsSessionId);
    }

    if (options.stop !== true && dataSessionId) {
      this.addEntryToRawLog(railsSessionId, method, data);
    }
  }

  static isdispatchMouseEvent(method) {
    return method === 'Input.dispatchMouseEvent';
  }

  // handling the different mouse event types seperately so that the
  // events are easy to understand on dashboard and prettifying becomes easy
  static modifyMouseMethod(data, method) {
    const {
      params: {
        type: methodType
      } = {}
    } = data;
    if (isNotUndefined(methodType)) {
      method = `Input.${methodType}Event`;
    }
    return method;
  }

  addNormalResponse(responseData, keyObject, railsSessionId, options = {}) {
    if (options.stop === true) {
      return;
    }

    if (this.keyLogEvents.has(railsSessionId)) {
      return;
    }

    const { id } = responseData;
    if (id && this.rawLogState.has(railsSessionId)) {
      const stateObj = this.rawLogState.get(railsSessionId)[id];
      if (stateObj) {
        if (options.debug) {
          stateObj.debug = options.debug;
        }
        stateObj.response = responseData;

        if (super.shouldIgnoreMethod('puppeteer', stateObj.method)) {
          return;
        }

        if (PuppeteerHandler.isdispatchMouseEvent(stateObj.method)) {
          stateObj.method = PuppeteerHandler.modifyMouseMethod(stateObj.request, stateObj.method);
        }

        this.sendToKafka(keyObject, stateObj, railsSessionId, responseData, options);
        delete this.rawLogState.get(railsSessionId)[id];
      }
    }
  }

  sendToKafka(keyObject, stateObj, railsSessionId, options) {
    super.addRequestLog(keyObject, stateObj.method, stateObj.request, railsSessionId);
    if (stateObj.debug && this.debugScreenshotMethodMapping.has(stateObj.request.method)) {
      helper.takeScreenshotAndUpload({
        ...keyObject,
      }, stateObj.debug.screenshotCounter);
      super.addDebugLog(keyObject, stateObj.debug, railsSessionId);
    }
    if (this.isScreenshotMethod(stateObj.request.method)) {
      stateObj.response.result.value = stateObj.response.result.data;
      super.takeScreenshotHandler(stateObj.response, keyObject, 'puppeteer');
      super.addNormalResponse(
        stateObj.response,
        keyObject,
        railsSessionId,
        { ...options, method: `resp-${stateObj.request.method}` }
      );
    } else {
      super.addNormalResponse(stateObj.response, keyObject, railsSessionId, options);
    }
  }

  // eslint-disable-next-line class-methods-use-this
  isScreenshotMethod(method) {
    return method === 'Page.captureScreenshot';
  }
}

module.exports = PuppeteerHandler;
