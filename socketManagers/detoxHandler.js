'use strict';

const { inspect } = require('util');
const SocketMessageHandler = require('./baseSocketMessageHandler');
const sendRequestToPlatform = require('../helpers/customSeleniumHandling/customExecutorHelper').sendRequestToPlatform;
const { replaceSessionId } = require('./responseModifiers');
const {
  isUndefined,
  isNotUndefined,
} = require('../typeSanity');
const { allocateTerminal, markIdle } = require('./terminalAllocation');
const { PingZombie } = require('../helper');
const {
  kReqData,
  kSendReconnectInfo,
  SERVER_ERROR_CLOSE_CODE,
  SOCKET_IDLE_MESSAGE,
  kDetoxType,
  DETOX,
  kStartTimer,
  GRACEFULE_CLOSE_CODES,
  RECONNECT_PREFIX,
  ABRUPT_SOCKET_MESSAGES,
  ERROR_MESSAGE
} = require('../config/socketConstants');
const HA = require('../ha');
const {
  global_registry: globalRegistry,
} = require('../constants');
const logger = require('../logger').basicLogger;

const TAG = 'DetoxHandler';

class DetoxHandler extends SocketMessageHandler {
  constructor() {
    super();
  }

  /**
    * Creates a map of detox messages and its custom message handler.
    *
    * @param {String} type - `type` property of detox message.
    * @param {method} method binding - custom message handler method.
    */
  enableMethodMaps() {
    this.methodMapping.set('CloudPlatform', this.cloudPlatformTypeHandler.bind(this));
  }

  assignLogBuilder() {}

  debugScreenshotMethodMaps() {}

  waitForAllocation(socketId) {
    if (this.terminalAllocationState.has(socketId)) {
      return this.terminalAllocationState.get(socketId);
    }
    return Promise.resolve();
  }

  async preProcessAllocation(socket) {
    const { id: socketId } = socket;
    try {
      this.startAllocation(socketId, socket);
      await this.waitForAllocation(socketId);
    } catch (err) {
      logger.error('Error in preprocess allocation for the detox session', { TAG });
      logger.error(inspect(err, false, 1), { TAG });
      this.closeClientConnection(socket, isNotUndefined(err) ? err : ERROR_MESSAGE, SERVER_ERROR_CLOSE_CODE)
    }
  }

  /**
    * Initiates the terminal allocation process for the session and creates
    * a promise so that the messages coming to the server can be holded off
    * till the promise is resolved or rejected.
    *
    * @param {String} socketId - Unique socket identifier assigned on connection.
    * @param {WebSocket} ws - WebSocket instance connected to server.
    * @throws {Error} - Unable to get websocket url when it is not received from terminal.
    * @throws {Error} - Unable to start browser because of some unforseen error.
    * @throws {Error} - Error from upstream hub process in allocation of terminal.
    * @return {void} Sets the allocation promise in the terminal state.
    */
  startAllocation(socketId, ws) {
    this.terminalAllocationState.set(socketId, new Promise((resolve, reject) => {
      this.sessionState.set(socketId, ws);
      allocateTerminal({
        ...ws[kReqData],
        type: kDetoxType,
        authReqs: this.authRequests,
        currWS: socketId
      }).then(async (resData) => {
          const {
            status,
            sessionId: railsSessionId,
            value: { wsURL, message, wsHostname }
          } = resData;
          this.constructor.addConnectionData(ws, wsURL);
          logger.info(`Received websocket url for ${railsSessionId} : ${wsURL} and ${socketId}`, {
            TAG,
          });
          // Set the railsSessionMapping before hand because the
          // websocket url might be undefined and we need to clean the terminal
          // when the client closes the connection and for that we
          // need the mapping to be present.
          if (isNotUndefined(railsSessionId)) {
            this.railsSessionMapping.set(socketId, railsSessionId);
            // TODO: PingZombie with seed data like: sessionId, framework, clientIp. Refer Playwrighthandler
            const keyObject = globalRegistry[railsSessionId];
            if (isUndefined(keyObject)) {
              await HA.getData(railsSessionId, (err, data) => {
                if (err) {
                  logger.error(`SessionId ${railsSessionId} state not found`, {
                    TAG,
                  });
                } else {
                  globalRegistry[railsSessionId] = data;
                }
              });
            }
          }

          if (status === 0 && isNotUndefined(wsURL)) {
            return this.connectToDesktopSocket(socketId, wsURL, {
              hostname: wsHostname,
              reconnectId: railsSessionId
            });
          }

          if (isUndefined(wsURL)) {
            if (isNotUndefined(message)) {
              throw new Error(message);
            }
            throw new Error('Unable to get the websocket connection');
          }

          if (status === 13 && isNotUndefined(message)) {
            throw new Error(message);
          }

          throw new Error('Unable to start detox server');
        })
        .then(() =>
          new Promise((kObjectResolve, kObjectReject) => {
            const railsSessionId = this.railsSessionMapping.get(socketId);
            const keyObject = globalRegistry[railsSessionId];
            if (isUndefined(keyObject)) {
              HA.getData(railsSessionId, (err, data) => {
                if (err) {
                  logger.error(`SessionId ${railsSessionId} state not found`, {
                    TAG,
                  });
                  kObjectReject(new Error('sessionId state not found'));
                } else {
                  globalRegistry[railsSessionId] = data;
                  kObjectResolve(data);
                }
              });
            } else {
              kObjectResolve(keyObject);
            }
          }))
        .then(() => {
          this.emit(kStartTimer, ws);
          this.emit(kSendReconnectInfo, ws, this.provideReconnectInfo(ws));
        })
        .then(() => {
          resolve()
        })
        .catch(reject);
    }));
  }

  async clientCloseHandler(ws, { code } = {}) {
    this.closePendingAuthRequest(ws.id);
    if (ABRUPT_SOCKET_MESSAGES.has(code)) {
      logger.info(`Received error code for websocket ${code}`, { TAG });
    }

    if (this.railsSessionMapping.has(ws.id)) {
      // Look for abrupt websocket close or gracefull close.
      // It might be the case that websocket url is not generated
      // and we need to clean the terminal after that.
      if (GRACEFULE_CLOSE_CODES.has(code)) {
        this.closeTerminalSocket(ws);
        await this.startTerminalCleanup(ws.id);
      } else {
        const railsSessionId = this.railsSessionMapping.get(ws.id);
        try {
          await markIdle(railsSessionId, ABRUPT_SOCKET_MESSAGES.get(code));
        } catch (err) {
          logger.error('Unable to mark session as idle', {
            TAG,
          });
        }
      }
    }

    super.clientCloseHandler(ws);
  }

  closeClientConnection(ws, message = ERROR_MESSAGE, code = 1001) {
    let responseErrorMessage = ERROR_MESSAGE; // message consists error trace which should not be sent to client.

    if(code === SERVER_ERROR_CLOSE_CODE) responseErrorMessage = message.message; // error code 1011 is to identify that its a session attempt error
    if(message === SOCKET_IDLE_MESSAGE) responseErrorMessage = SOCKET_IDLE_MESSAGE;

    // send error message to client before closing connection
    try {
      ws.send(JSON.stringify({type: 'serverError', response: {message: responseErrorMessage}}));
    } catch (e) {
      logger.error('Unable to send error message to client', {
        TAG,
      });
    }

    super.closeClientConnection(ws, message.toString().substring(0, 122), code);
  };

  async proxyResponseToClient(sessionId, data) {
    /**
     * Holding incoming messages from the Detox server while waiting for unfinished api call.
     */
    await this.waitForPendingApiRequests(sessionId);
    const railsSessionId = this.railsSessionMapping.get(sessionId);
    data = this.injectCloudSessionId(JSON.parse(data), railsSessionId);
    super.proxyResponseToClient(sessionId, JSON.stringify(data));
  }

  injectCloudSessionId(data, railsSessionId) {
    data.cloudSessionId = railsSessionId;
    return data;
  }

  // If sessionId is present in the message, this should be updated with railsSessionId.
  // During appLaunch AUT and DetoxAndroidClient creates their session using railsSessionId and that should match the tester sessionId.
  // For Logging purposes, cloudSessionId will be added to the incoming messages.
  modifyDetoxMessage(data, railsSessionId, ws) {
    try{
      [data] = replaceSessionId(data, railsSessionId);
      data = this.injectCloudSessionId(data, railsSessionId);
      return data;
    } catch (err) {
      logger.error(`Error in the updating detox sessionId for data: ${data}, error: ${err}`, {
        TAG,
      });
      // Failure in updating Detox sessionId for login message will lead to improper connection establishment.
      if(data.type === "login") {
        this.closeClientConnection(ws, isNotUndefined(err) ? err.toString() : ERROR_MESSAGE);
      }
    }
  }

  async messageHandler(ws, message) {
    try {
      await this.initializeReconnect(ws, message);
      if (message.substring(0, 10) === RECONNECT_PREFIX) {
        return;
      }

      let data = JSON.parse(message);
      const { type } = data;
      const { id: sessionId } = ws;
      await this.waitForAllocation(sessionId);

      const railsSessionId = this.railsSessionMapping.get(sessionId);
      data = this.modifyDetoxMessage(data, railsSessionId, ws);

      logger.info(`Processing: ws_id - ${sessionId}, type - ${type}, data_length - ${message ? message.length : 0}, railsSessionId - ${railsSessionId}`, { TAG, log: { uuid: railsSessionId, kind: 'MESSAGE_HANDLER_DETOX' } });
      await this.processData(sessionId, type, ws, data);
    } catch (err) {
      logger.error(`Error in the message handler: ${inspect(err, false, 1)}`, {
        TAG,
      });
      this.closeClientConnection(ws, isNotUndefined(err) ? err : ERROR_MESSAGE)
    }
  }

  generateRequestUrl(method, keyObject, args, railsSessionId) {
    switch(method) {
      case 'launchApp':
        return `/launch_detox_instrumentation?device=${encodeURIComponent(keyObject.device)}&session_id=${encodeURIComponent(railsSessionId)}&args=${encodeURIComponent(JSON.stringify(args))}`;
      case 'terminateApp':
        return `/launch_detox_instrumentation?device=${encodeURIComponent(keyObject.device)}&session_id=${encodeURIComponent(railsSessionId)}&args=${encodeURIComponent(JSON.stringify(args))}&terminate_app=${encodeURIComponent('true')}`;
      default:
        throw new Error(`Method ${method} is not handled.`);
    }
  }

  async waitForPendingApiRequests(socketId) {
    if (this.activeApiRequests.has(socketId)) {
      return this.activeApiRequests.get(socketId);
    }
  }

  async cloudPlatformTypeHandler(ws, data) {
    await this.waitForPendingApiRequests(ws.id);

    this.activeApiRequests.set(ws.id, new Promise((resolve) => {
      const { messageId, params: { method, args }, type } = data;
      const railsSessionId = this.railsSessionMapping.get(ws.id);
      const keyObject = globalRegistry[railsSessionId];
      const internalError = {code: 'internal_error', message: `some error occured in executing method ${method}`}
      let requestUrl = '';

      // Callback of errored platform request failure.
      // Detox tester uses type `error` to terminate the test script.
      const handleRequestFailure = () => {
        logger.error(`Failure in platform request: ${keyObject.rproxyHost} for type: cloudPlatform and method: ${method}`, {
          TAG
        });
        this.closeClientConnection(ws);
        resolve();
      };

      // Callback of success platform request.
      const platformResponseHandler = (response) => {
        let responseType = 'error';
        try {
          if(response.statusCode === 200) {
            response = JSON.parse(response.data);
            responseType = type;
          }
        } catch (err) {
          logger.error(`Error in parsing response from platform ${response}, error: ${err}`, { TAG });
          handleRequestFailure();
          return;
        }

        this.sendCustomResponseToClient(response, ws, method, responseType, messageId, railsSessionId);
        return resolve();
      };

      try {
        requestUrl = this.generateRequestUrl(method, keyObject, args, railsSessionId);
      } catch (err) {
        logger.error(`Unsupported cloudPlatform method: ${method} error: ${err}`, { TAG });
        // For unhandled methods, send the error message and continue test execution.
        this.sendCustomResponseToClient({ status: false, message: `method ${method} not supported`}, ws, method, type, messageId, railsSessionId);
        return resolve();
      }

      const requestStateObj = {
        rproxyHost: keyObject.rproxyHost,
        hostname: keyObject.name,
        onResolve: handleRequestFailure
      };

      // GET request to platform
      sendRequestToPlatform(undefined, requestUrl, requestStateObj, keyObject, internalError, 90000, undefined, platformResponseHandler);
    }))
  };

  sendCustomResponseToClient(response, ws, method, type, messageId, railsSessionId) {
    this.writeResponse(ws, {
      type,
      response: {
        method,
        success: response.success || false,
        message: !response.success ? response.message || '' : ''
      },
      messageId,
      cloudSessionId: railsSessionId
    });
  }
}

module.exports = DetoxHandler;
