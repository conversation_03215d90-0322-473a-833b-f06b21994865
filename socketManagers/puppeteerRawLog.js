'use strict';

const SocketRawLog = require('./socketRawLog');
const {
  NAVIGATE_TO,
  KEY_PRESS,
  TOUCH,
  SET_GEOLOCATION,
  MOVE_MOUSE,
  CAPTURE_SCREENSHOT,
  GET_COOKIES,
  FIND_ELEMENT,
  FOCUS,
  EVALUATE_EXPRESSION,
  MOUSE_MOVED,
  MOUSE_PRESSED,
  MOUSE_RELEASED,
  RUNTIME_FUNCTION
} = require('../helpers/customSeleniumHandling/frameworkConstants');
const requestTypeCorrection = require('../helpers/customSeleniumHandling/requestTypeCorrection');
const { isFunction, isNotUndefined, isHash, isTrueString } = require('../typeSanity');
class PuppeteerRawLog extends SocketRawLog {
  generateMethodLogMapping() {
    this.methodLogMap.set('Page.navigate', NAVIGATE_TO);
    this.methodLogMap.set('Page.captureScreenshot', CAPTURE_SCREENSHOT);
    this.methodLogMap.set('Input.dispatchKeyEvent', KEY_PRESS);
    this.methodLogMap.set('Input.dispatchMouseEvent', MOVE_MOUSE);
    this.methodLogMap.set('Input.dispatchTouchEvent', TOUCH);
    this.methodLogMap.set('Input.insertText', KEY_PRESS);
    this.methodLogMap.set('Emulation.setGeolocationOverride', SET_GEOLOCATION);
    this.methodLogMap.set('Network.getCookies', GET_COOKIES);
    this.methodLogMap.set('Page.customQuerySelector', FIND_ELEMENT);
    this.methodLogMap.set('Page.customFocus', FOCUS);
    this.methodLogMap.set('Runtime.evaluate', EVALUATE_EXPRESSION);
    this.methodLogMap.set('Input.mouseMovedEvent', MOUSE_MOVED);
    this.methodLogMap.set('Input.mousePressedEvent', MOUSE_PRESSED);
    this.methodLogMap.set('Input.mouseReleasedEvent', MOUSE_RELEASED);
    this.methodLogMap.set('Runtime.callFunctionOn', RUNTIME_FUNCTION)
  }

  // eslint-disable-next-line class-methods-use-this
  generatePayloadMapping() {
    this.methodPayloadMap.set('resp-Page.captureScreenshot', PuppeteerRawLog.takeScreenshot);
    this.methodPayloadMap.set('Page.navigate', PuppeteerRawLog.navigate);
    this.methodPayloadMap.set('Input.dispatchMouseEvent', PuppeteerRawLog.dispatchMouseEvent);
    this.methodPayloadMap.set('Input.dispatchTouchEvent', PuppeteerRawLog.dispatchTouchEvent);
    this.methodPayloadMap.set('Input.insertText', PuppeteerRawLog.insertText);
    this.methodPayloadMap.set('Emulation.setGeolocationOverride', PuppeteerRawLog.setGeolocationOverride);
    this.methodPayloadMap.set('Network.getCookies', PuppeteerRawLog.getCookies);
    this.methodPayloadMap.set('Input.dispatchKeyEvent', PuppeteerRawLog.dispatchKeyEvent);
    this.methodPayloadMap.set('Page.customQuerySelector', PuppeteerRawLog.findElement);
    this.methodPayloadMap.set('Page.customFocus', PuppeteerRawLog.focusElement);
    this.methodPayloadMap.set('Runtime.evaluate', PuppeteerRawLog.evaluateExpression);
    this.methodPayloadMap.set('Input.mouseMovedEvent', PuppeteerRawLog.mouseMovedReleasedPressed);
    this.methodPayloadMap.set('Input.mousePressedEvent', PuppeteerRawLog.mouseMovedReleasedPressed);
    this.methodPayloadMap.set('Input.mouseReleasedEvent', PuppeteerRawLog.mouseMovedReleasedPressed);
    this.methodPayloadMap.set('Runtime.callFunctionOn', PuppeteerRawLog.callJSFunction);
    this.methodPayloadMap.set('evaluateExpression', PuppeteerRawLog.evaluateBSExecutorExpression);
  }

  static evaluateBSExecutorExpression(data) {
    return isHash(data) && isHash(data.result) ? data.result.value : data;
  }

  static callJSFunction({ value = '' }) {
    return { value };
  }

  static mouseMovedReleasedPressed({ x = 0, y = 0 }) {
    return { x, y };
  }

  static evaluateExpression( data ) {
    return {
      expression: (data.expression) ? data.expression.replace(
      /\/\/# sourceURL=__puppeteer_evaluation_script__(.*)/,
      '').trim() : ''
    };
  }

  static focusElement({ value = '' }) {
    return { value };
  }

  static findElement(data) {
    return (Array.isArray(data.args) && data.args.length>1)
    ? { element: data.args[1].value || '' }
    : { element: '' };
  }

  static takeScreenshot(data) {
    return { value: data.value };
  }

  static navigate( data ) {
    return { url: data.url || '' };
  }

  static dispatchMouseEvent( data ) {
    return { mouseEvent: data.type || '', x: data.x || 0, y: data.y || 0};
  }

  static dispatchTouchEvent( data ) {
    if (data.type == "touchStart") {
      return { type: data.type || '', touchPoints: data.touchPoints || [ {} ]};
    } else {
      return { type: data.type || '' }
    }
  }

  static insertText( data ) {
    return { text: data.text || '' };
  }

  static setGeolocationOverride( data ) {
    return { latitude: data.latitude || 0, longitude: data.longitude || 0};
  }

  static getCookies( data ) {
    return { urls: data.urls || [] };
  }

  static dispatchKeyEvent({ value = '' }) {
    return { value }
  }

  // eslint-disable-next-line class-methods-use-this
  fetchStrippedData(type, data) {
    let resp = {};
    if (SocketRawLog.isRequest(type)) {
      resp = {
        ...resp,
        ...data.params,
      };
    }

    if (SocketRawLog.isResponse(type)) {
      resp = {
        ...resp,
        ...data.result,
      };
    }

    if (SocketRawLog.isStop(type)) {
      resp = {
        ...resp,
        ...data,
      };
    }

    if (SocketRawLog.isDebug(type)) {
      resp = {
        ...resp,
        ...data,
      };
    }

    return resp;
  }

  updateLogEntry(type, method, { sessionId, data }) {
    const { requestStateObj, commandType } = this.generateAnnotateArgs(sessionId, method);
    requestTypeCorrection(requestStateObj, commandType);
    const [requestMethod] = requestStateObj.hash.split(':');
    const stringFn = SocketRawLog.getSerializer(type);
    let logData = this.fetchPayloadHandler(method)(this.fetchStrippedData(type, data));
    if(isNotUndefined(data.performance) && isTrueString(data.performance.enabled)) {
      const logDataParsed = JSON.parse(logData);
      if(isTrueString(data.performance.truncate)) {
        logDataParsed.report = 'TRUNCATED';
      }
      logData = { value: logDataParsed }; // Since we don't want to send string but rather JSON for frontend to parse
    }
    if (isFunction(stringFn)) {
      return stringFn(logData, {
        method: requestMethod,
        url: requestStateObj.url,
        ...SocketRawLog.isRequest(type) && { currTime: data.currTime },
        ...SocketRawLog.isDebug(type) && data,
      });
    }

    return `${type} ${method} ${JSON.stringify(data)}`;
  }
}

module.exports = PuppeteerRawLog;
