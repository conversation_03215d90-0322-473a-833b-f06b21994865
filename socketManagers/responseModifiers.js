'use strict';

const SESSION_REGEX = /"sessionId":"([a-zA-Z0-9-]+)"/g;

function stripQuotes(sessionId) {
  return sessionId.substring(sessionId.indexOf(':') + 1).replace(/"/g, '');
}

// Replaces the sessionId group with the passed sessionId value.
// The SESSION_REGEX is used for capturing the group, currently it is
// considering the format in which puppeteer has messages over the network and
// should work with all other types as well in future if need to be done by
// changing the method that need to be called in the handler by having a mapping.
const replaceSessionId = (data, to, from) => {
  const stringData = JSON.stringify(data);
  const regexMatch = stringData.match(SESSION_REGEX);
  if (regexMatch) {
    const { 0: messageSessionId } = regexMatch.map(el => stripQuotes(el));
    // Look for if the sessionId present in the message is present in the state
    // already identified because in some cases we do not want to update
    // sessionIds because they are generated from an internal event like iframe
    // or service worker. If the find extracted is of rails or of terminal (browser)
    // then only replace it else it will be assumed as an internal id and we
    // are not replacing it.
    if (from && to) {
      return [
        JSON.parse(stringData.replace(
          `"sessionId":"${from}"`,
          `"sessionId":"${to}"`
        )),
        from
      ];
    }

    // If there is no upstream set yet and we need to check that the current payload
    // does not contain more than one match for sessionIds in the packet.
    if (messageSessionId && !from && regexMatch.length === 1) {
      return [
        JSON.parse(stringData.replace(
          `"sessionId":"${messageSessionId}"`,
          `"sessionId":"${to}"`
        )),
        messageSessionId
      ];
    }
  }
  return [data, null];
};

module.exports = {
  replaceSessionId,
};
