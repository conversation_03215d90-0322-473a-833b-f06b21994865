'use strict';

const {
  isUndefined,
  isNotUndefined,
  isHash,
  isTrueString
} = require('../typeSanity');

const { CONSOLE_LOG_LEVELS, LH_MIN_CHROME, LH_SUPPORTED_BROWSERS, browserVersionStrToFloat } = require('../constants');
const { PLAYWRIGHT_LH_PORT, REMOTE_DEBUGGER_PORT } = require('../config/socketConstants')
const logger = require('../logger').basicLogger;

const TAG = 'SocketManagerValidations';

/**
  * Refreshes the data (JSON Payload) sent from the client with
  * the username and accessKey in the format which is followed in
  * selenium and as a general practice. This will populate the
  * `browserstack.user` and `browserstack.key` in the payload
  * which will be used later for setting the basic auth params in the
  * request for allocation.
  *
  * @param {Object} data - Client Request Payload for session creation.
  * @return {void} Updates the client payload in-place since the objects
  * are passed by refrence in javascript.
 */
const refreshAccessKeys = (data) => {
  const {
    'browserstack.user': username,
    'browserstack.key': password,
    'browserstack.username': proxyUsername,
    'browserstack.accessKey': proxyAccessKey,
    'username': detoxUsername,
    'accessKey': detoxAccessKey
  } = data;

  if (isUndefined(username)) {
    if (isNotUndefined(proxyUsername)) {
      data['browserstack.user'] = proxyUsername;
    } else if(isNotUndefined(detoxUsername)) {
      data['browserstack.user'] = detoxUsername;
    }
  }
  if (isUndefined(password)) {
    if (isNotUndefined(proxyAccessKey)) {
      data['browserstack.key'] = proxyAccessKey;
    } else if(isNotUndefined(detoxAccessKey)) {
      data['browserstack.key'] = detoxAccessKey;
    }
  }
  // These keys were introduced as a mistake.
  // To avoid throwing 401 to people already using it,
  // just copying it to correct caps and removing
  delete data['browserstack.username'];
  delete data['browserstack.accessKey'];
  delete data['username'];
  delete data['accessKey'];
};

/**
  * Determine the playwright client version from the user-agent value in
  * header sent at the time of upgrading the connection.
  *
  * @param {http.IncomingMessage} request - Incoming request for websocket upgrade.
  * @return {String|null} Will return the valid version if it matches the regex for the
  * playwright agent and on the existence of value in header. If it doesn't
  * matches with the conditions then null will be returned.
  */
const determinePlaywrightVersion = (request) => {
  const { headers: { ['user-agent']: userAgent } = {} } = request;
  if (isNotUndefined(userAgent)) {
    // This is the patten used in the client binding for sending the user-agent
    // Currently i am ignoring the alphabets or hypen in the agent like if one is using the
    // head of playwright then the version sent from client will be in format 1.minor.patch-next.
    // So for now only considering the numerical parts since that only will be used in release.
    // Output from this regex will be around 1.minor.patch ignoring the alphabet parts.
    // Ref: https://github.com/microsoft/playwright/blob/1b2f0714f72e3e4ad1e0bd9cf5003f692c55c61e/src/utils/utils.ts#L312-L315
    const matched = userAgent.match(/Playwright\/(?<versionInfo>[0-9.]+)(.*)$/);
    if (matched && matched.groups) {
      return matched.groups.versionInfo;
    }
  }
  return null;
};

/**
  * Modifies the capabilities object sent to it based on the request for
  * upgrade. If the upgrade request is sent from the playwright client binding,
  * then we check for the user-agent sent. If the user-agent is sent in the
  * client binding then upsert the value of `client.playwrightVersion` key.
  *
  * @param {http.IncomingMessage} request - Incoming request for upgrade.
  * @param {Object} caps - Capabilities object sent in url encoded form.
  * @return {None} Updates the value of key in the object itself since cloning will be
  * a heavy operation and we do not need to do for every upgrade request.
  */
const modifyPlaywrightClient = (request, caps) => {
  const playwrightVersion = determinePlaywrightVersion(request);
  if (playwrightVersion) {
    caps['client.playwrightVersion'] = playwrightVersion;
  }
};

const checkCredentials = (data) => {
  refreshAccessKeys(data);

  const {
    'browserstack.user': username,
    'browserstack.key': password,
  } = data;

  if (isUndefined(username) || isUndefined(password)) {
    throw new Error('Access credentials missing');
  }
};

const sessionDebug = options => isHash(options) && isHash(options.data) && isTrueString(options.data['browserstack.debug']);

const getConsoleLogLevel = (options) => {
  if(isHash(options) && isHash(options.data) && isNotUndefined(options.data['browserstack.console'])) {
    return CONSOLE_LOG_LEVELS.get(options.data['browserstack.console']) || CONSOLE_LOG_LEVELS.get('errors');
  } else {
    return CONSOLE_LOG_LEVELS.get('errors');
  }
}

const isCDP = data => isTrueString(data.isPuppeteer) || isTrueString(data.isPlaywright) || isTrueString(data.isDetox);

const updateProxyBypassList = (config, proxyPaths) => {
  let hasProxyBypass = false;
  const proxyBypassList = `--proxy-bypass-list=`;
  config.args = config.args.map(arg => {
      if (arg.startsWith(proxyBypassList)) {
          hasProxyBypass = true;
          if(!arg.endsWith('='))
            return arg.endsWith(';') ? arg + proxyPaths : `${arg};${proxyPaths}`;
          else
            return arg + proxyPaths;
      }
      return arg;
  });

  // If proxy bypass list was not found, add it to the arguments array
  if (!hasProxyBypass) {
      config.args.push(`${proxyBypassList}${proxyPaths}`);
  }
}

const extractPlaywrightConfig = (railsCaps, lCaps) => {
  const config = {};
  if (railsCaps['browserstack.playwrightVersion']) {
    config.playwrightVersion = railsCaps['browserstack.playwrightVersion'];
  }
  config.browser = lCaps.browser || "chromium";
  const browserOptions = lCaps["args"] || [];
  const { chromeOptions } = lCaps;
  if (isNotUndefined(chromeOptions) || isNotUndefined(lCaps["args"])) {
    const args = chromeOptions ? chromeOptions.args : [];
    const browserArgs = [
      ...new Set([...args || [], ...browserOptions]),
    ];
    if (browserArgs.length) {
      // Playwright assumes any arg that doesn't contains `--` is an url
      // and hence not accepted while launching the server. So removing the
      // test-type from the chromeArgs.
      config.args = browserArgs.filter((el) => el.startsWith("--"));
    }
  }
  if (isNotUndefined(lCaps["ignoreDefaultArgs"])) {
    const ignoreDefaultArgs = lCaps["ignoreDefaultArgs"] ? lCaps["ignoreDefaultArgs"] : [];
    const ignoreArgs = [
      ...new Set([...ignoreDefaultArgs || []]),
    ];
    if (ignoreArgs.length) {
      config.ignoreDefaultArgs = ignoreArgs.filter((el) => el.startsWith("--"));
    }
  }
  if (railsCaps['browserstack.playwrightBundledBrowser']) {
    config.useBundledBrowser = true;
  }
  if (config.browser == 'firefox' && isNotUndefined(lCaps['firefox_user_prefs'])) {
    config.firefoxUserPrefs = lCaps['firefox_user_prefs'];
  }
  config.launchPersistentContext = (railsCaps['mediaFiles'] || isTrueString(railsCaps["browserstack.accessibility"])) ? "true" : "false";
  try {
    if (
      LH_SUPPORTED_BROWSERS.includes(config.browser) &&
      browserVersionStrToFloat(lCaps.browser_version) >= LH_MIN_CHROME &&
      (isUndefined(lCaps.real_mobile) || lCaps.real_mobile != true)
    ) {
      const remote_debugging_port = config.launchPersistentContext === "true" ? REMOTE_DEBUGGER_PORT : PLAYWRIGHT_LH_PORT;
      if (config.args) {
        config.args.push(`--remote-debugging-port=${remote_debugging_port}`);
      } else {
        config.args = [`--remote-debugging-port=${remote_debugging_port}`];
      }
      logger.info(`Set RDP: ${remote_debugging_port} for playwright lighthouse`, {
        TAG,
      });
    }
  } catch (error) {
    logger.info(`Error in setting RDP for playwright LH: ${error}`, {
      TAG,
    });
  }

  try {
    
    if(isTrueString(railsCaps["browserstack.accessibility"])) {
      // Add proxy bypass for a11y-engine
      const proxyPaths = `a11y-engine.bsstag.com;a11y-engine-preprod.bsstag.com;a11y-engine.browserstack.com;<-loopback>`;

      if(config.args) {
        updateProxyBypassList(config, proxyPaths);
      } else {
        config.args = [ `--proxy-bypass-list=${proxyPaths}` ];
      }
      logger.info(`[accessibility] Added a11y-engine proxy-bypass: ${JSON.stringify(config)}`, {
        TAG,
      });
    }

  } catch (error) {
    logger.info(`Error in setting a11y-engine proxy-bypass: ${error}`, {
      TAG,
    });
  }
  
  return config;
};

const isReconnectRequest = (headers = {}) => {
  return (headers['x-reconnect'] || '').toString().toLowerCase() === 'true';
};

module.exports = {
  checkCredentials,
  sessionDebug,
  modifyPlaywrightClient,
  isCDP,
  isReconnectRequest,
  extractPlaywrightConfig,
  getConsoleLogLevel,
  updateProxyBypassList
};
