'use strict';

const { URL } = require('url');
const {
  PLAYWRIGHT,
  PUPPETEER,
  SELENIUM,
  DETOX
} = require('../config/socketConstants');
const { isUndefined } = require('../typeSanity');

const decodeBasicAuth = authValue => Buffer.from(authValue.split(' ')[1], 'base64')
  .toString().split(':');

// identify the product from pathname
const isSelenium = pathname => /\/session\/[a-z0-9]+\/se\/cdp/.test(pathname) || /\/session\/[a-z0-9]+\/se\/bidi/.test(pathname);;
const isPlaywright = pathname => (pathname === '/playwright');
const isPuppeteer = pathname => (pathname === '/' || pathname === '/puppeteer');
const isDetox = pathname => (pathname === '/init');

const getProduct = (pathname) => {
  let product;
  if (isSelenium(pathname)) {
    product = SELENIUM;
  } else if (isPlaywright(pathname)) {
    product = PLAYWRIGHT;
  } else if (isPuppeteer(pathname)) {
    product = PUPPETEER;
  } else if (isDetox(pathname)) {
    product = DETOX;
  }

  return product;
};

const getJSONData = (req) => {
  const { url, headers: { authorization, host } } = req;
  // Here due to limitation in the WHATWG URL api it cannot parse the
  // querystring implicitly and due to which we need to create a socket hostname
  // as it cannot use the request host header.
  const parsedURL = new URL(url, `ws://${host}`);
  const { searchParams, pathname } = parsedURL;
  req.product = getProduct(pathname);
  req.isPlaywright = isPlaywright(pathname);
  req.isSelenium = isSelenium(pathname);
  req.isPuppeteer = isPuppeteer(pathname);
  req.isDetox = isDetox(pathname);

  if (isUndefined(req.product)) {
    throw new Error('Does not allow this pathname');
  }
  if (req.isSelenium) {
    return {};
  }
  const stringifiedCaps = searchParams.get('caps');
  if (stringifiedCaps === null) {
    throw new Error('caps key not present in query Args');
  }

  try {
    let jsonParsedData = JSON.parse(stringifiedCaps);
    if (authorization) {
      const [username, password] = decodeBasicAuth(authorization);
      jsonParsedData = {
        ...jsonParsedData,
        'browserstack.user': username,
        'browserstack.key': password,
      };
    }
    return jsonParsedData;
  } catch (err) {
    throw new Error('Invalid json sent in query args');
  }
};

module.exports = {
  getJSONData,
};
