'use strict';

const { s3LogFormat, getDate } = require('../../helper');
const { getCompleteFileName } = require('../../helpers/debugScreenshots');

const requestString = (data, { method, url, currTime = getDate() }) => s3LogFormat(
  'REQUEST',
  currTime,
  `[${currTime}] ${method} ${url} ${JSON.stringify(data)}`
);

const basicLogString = (type, data) => s3LogFormat(type, getDate(), `${JSON.stringify(data)}`);

const responseString = data => basicLogString('RESPONSE', data);

const stopString = data => basicLogString('STOP_SESSION', data);

const debugScreenshotString = (_, { keyObject, screenshotCounter }) => s3LogFormat('DEBUG', getDate(), getCompleteFileName(keyObject, screenshotCounter));

module.exports = {
  requestString,
  responseString,
  stopString,
  debugScreenshotString,
};
