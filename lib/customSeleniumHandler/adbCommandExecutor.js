'use strict';

const constants = require('../../constants');
const adbCommandsWhitelist = require('../../constants/adbCommandWhitelist');
const { isNotUndefined } = require('../../typeSanity');
const instrumentAndSendError = require('../../helpers/customSeleniumHandling/customExecutorHelper').instrumentAndSendError;
const sendRequestToPlatform = require('../../helpers/customSeleniumHandling/customExecutorHelper').sendRequestToPlatform;
const instrumentAndSendExecutorResponse = require('../../helpers/customSeleniumHandling/customExecutorHelper').instrumentAndSendExecutorResponse;
const HubLogger = require('../../log');
const { getInstrumentationData } = require('../../helpers/customSeleniumHandling/instrumentExecutor');
// driver.execute_script("browserstack_executor: {\"action\":\"adbShell\", \"arguments\": {
//     \"command\" : \"dumpsys gfxinfo <package_name>\" }}”)
// validations
// send request to platform
// handle custom response
// instrument and send response

const errors = constants.ADB_COMMAND_EXECUTOR;
const executorType = 'adb_shell_custom_executor';

const handleUserPackageSensitiveCommands = (userTool, userCommand, inputArr, keyObject) => {
  if (userTool === 'dumpsys' && userCommand === 'activity') {
    const regex = new RegExp(`^\\s{0,5}-p\\s{1,5}${keyObject.bundleId}(\\s{1,5}activities(\\s{1,5}\\|\\s{0,5}grep\\s{0,5}-E\\s{0,5}("|')?mResumedActivity("|')?)?|\\s{1,5}services|\\s{1,5}providers|\\s{1,5}recents|\\s{1,5}broadcasts|\\s{1,5}intents|\\s{1,5}permissions|\\s{1,5}processes)?\\s{0,5}$`);
    return regex.test(inputArr.join(' '));
  }
  return true;
};

const checkForDeviceOrientationValidOS = (userTool, userCommand, keyObject) => {
  if (userTool === 'wm' && userCommand === 'set-fix-to-user-rotation') {
    return [10, 11].includes(parseInt(keyObject.platformDetails.platformVersion, 10));
  }
  if (userTool === 'wm' && userCommand === 'fixed-to-user-rotation') {
    return parseInt(keyObject.platformDetails.platformVersion, 10) >= 12;
  }
  return true;
};

const checkForDeviceOrientationValidDevice = (userTool, userCommand, keyObject) => {
  if (userTool === 'wm' && (userCommand === 'set-fix-to-user-rotation' || userCommand === 'fixed-to-user-rotation')) {
    const unsupportedDevices = ['Oppo', 'Realme 8'];

    for (let i = 0; i < unsupportedDevices.length; i++) {
      const deviceName = unsupportedDevices[i];
      if (keyObject.deviceName.startsWith(deviceName)) return false;
    }
  }
  return true;
};

const checkForDeviceOrientationValidPlan = (userTool, userCommand, keyObject) => {
  if (keyObject.appTesting && userTool === 'wm' && ['set-fix-to-user-rotation', 'fixed-to-user-rotation'].includes(userCommand) && !keyObject.accessibleFeaturesList.includes(constants.DF_FEATURE_FENCE_CODES.DEVICE_ORIENTATION_CODE)) {
    return false;
  }
  return true;
};

/* eslint-disable max-lines-per-function */
const validateCommand = (allowedAdbMap, inputArr, keyObject, requestStateObj, parsedCommand) => {
  let errorCommand = errors.blacklisted_command;
  let errorMessage = errorCommand.message.replace('<COMMAND>', `${parsedCommand.arguments.command}`);

  if (isNotUndefined(allowedAdbMap.get(inputArr[0]))) {
    const tool = allowedAdbMap.get(inputArr[0]);
    const toolCommand = tool.command;
    if (isNotUndefined(toolCommand[inputArr[1]])) {
      const regex = toolCommand[inputArr[1]].regex;
      const maxWords = toolCommand[inputArr[1]].maxWords;
      if (inputArr.length <= maxWords) {
        const userTool = inputArr.shift(); // remove tool
        const userCommand = inputArr.shift(); // remove command
        const testString = inputArr.join(' '); // arguments passed for the tool should satisfy regex expression in allowedAdbMap.
        if (regex.test(testString)) {
          const match = handleUserPackageSensitiveCommands(userTool, userCommand, inputArr, keyObject);
          const isForValidDevice = checkForDeviceOrientationValidDevice(userTool, userCommand, keyObject);
          const isForValidOs = checkForDeviceOrientationValidOS(userTool, userCommand, keyObject);
          const isForValidPlan = checkForDeviceOrientationValidPlan(userTool, userCommand, keyObject);
          if (match && isForValidOs && isForValidDevice && isForValidPlan) return true;
          if (!isForValidPlan) {
            errorCommand = errors.device_orientation_not_in_aa_plan_error;
            const deviceOrientationErrorMessage = errorCommand.message.replace('<COMMAND>', `${parsedCommand.arguments.command}`);
            errorMessage = deviceOrientationErrorMessage.replace('<DEVICE_NAME>', `${keyObject.deviceName}`);
          } else if (!isForValidOs) {
            errorCommand = errors.incompatible_os_version;
            const deviceOrientationErrorMessage = errorCommand.message.replace('<COMMAND>', `${parsedCommand.arguments.command}`);
            if (userCommand === 'set-fix-to-user-rotation') errorMessage = deviceOrientationErrorMessage.replace('<ANDROID_VERSIONS>', 'Android version<10 and version>=12');
            else if (userCommand === 'fixed-to-user-rotation') errorMessage = deviceOrientationErrorMessage.replace('<ANDROID_VERSIONS>', 'Android version<12');
          } else if (!isForValidDevice) {
            errorCommand = errors.unsupported_device;
            const deviceOrientationErrorMessage = errorCommand.message.replace('<COMMAND>', `${parsedCommand.arguments.command}`);
            errorMessage = deviceOrientationErrorMessage.replace('<DEVICE_NAME>', `${keyObject.deviceName}`);
          }
        }
      }
    }
  }
  instrumentAndSendError(executorType, keyObject, requestStateObj, { code: errorCommand.code, message: errorMessage });
  return false;
};

const validateFormat = (parsedCommand, keyObject, requestStateObj) => {
  const expectedKeys = ['action', 'arguments'];
  const keys = Object.keys(parsedCommand);
  const args = parsedCommand.arguments;
  // should only have two keys - action and arguments

  const result = keys.length === expectedKeys.length && expectedKeys.every(element => keys.includes(element));
  if (result) {
    if (args.length === 1 || Object.keys(args).includes('command')) {
      if (args.command.length < 200) { return true; }
    }
  }
  instrumentAndSendError(executorType, keyObject, requestStateObj, errors.invalid_format);
  return false;
};

const isRequestValid = (keyObject, parsedCommand, requestStateObj) => {
  // appTesting and android os with redis flag as true
  // should only have two keys - action and arg
  // command should have only one key arguments
  // max string should be satisfied
  // tool should be whitelisted
  // max len for the tool should satisfied
  // toolcommand should be whitelisted
  // toolcommandarg should be whitelisted
  // rohbust against edge cases like '' undefined etc
  const allowedAdbMap = adbCommandsWhitelist.ADB_COMMAND_EXECUTOR_VALIDATIONS;
  if (!keyObject.adbCustomExecutor || !keyObject.appTesting) {
    instrumentAndSendError(executorType, keyObject, requestStateObj, errors.invalid_action_name);
    return false;
  }
  if (keyObject.os !== 'android') {
    instrumentAndSendError(executorType, keyObject, requestStateObj, errors.invalid_os);
    return false;
  }
  if (!validateFormat(parsedCommand, keyObject, requestStateObj)) { return false; }

  const args = parsedCommand.arguments;
  const command = args.command;
  const arr = command.trim().split(/\s+/);

  if (!validateCommand(allowedAdbMap, arr, keyObject, requestStateObj, parsedCommand)) { return false; }
  return true;
};

const checkUserExceededADBcommandsLimit = (keyObject, requestStateObj) => {
  const instruData = getInstrumentationData(keyObject);
  if (instruData && instruData.custom_executor && instruData.custom_executor[executorType]) {
    if (instruData.custom_executor[executorType].count > constants.ADB_COMMANDS_LIMIT_PER_SESSION) {
      HubLogger.miscLogger('User exceeded ADB commands limit');
      instrumentAndSendError(
        executorType,
        keyObject,
        requestStateObj,
        errors.max_adb_command_error,
      );
      return true;
    }
  }
  return false;
};

/* eslint-disable max-lines-per-function */
const executeAdbCommand = (keyObject, requestStateObj, parsedCommand) => {
  const customTimeout = 20 * 1000;
  if (!isRequestValid(keyObject, parsedCommand, requestStateObj)) { return; }
  if (checkUserExceededADBcommandsLimit(keyObject, requestStateObj)) { return; }
  const product = (keyObject && ('appTesting' in keyObject)) ? 'app_automate' : 'automate';
  const serverURL = `/execute_adb_command?device=${encodeURIComponent(keyObject.device)}&session_id=${encodeURIComponent(keyObject.rails_session_id)}&command=${encodeURIComponent(parsedCommand.arguments.command)}&product=${encodeURIComponent(product)}`;
  const responseHandler = (response) => {
    if (response && response.statusCode === 200) {
      try {
        const data = response.data;
        const parsedData = JSON.parse(data);
        const commandOutput = parsedData.output;
        const hash = 'POST:value';
        instrumentAndSendExecutorResponse(executorType, keyObject, requestStateObj, true, hash, commandOutput);
        return;
      } catch (err) {
        HubLogger.miscLogger(`Adb command execution: Error in parsing respone from platform ${err}`);
      }
    } else if (response && response.statusCode === 429) {
      instrumentAndSendError(executorType, keyObject, requestStateObj, errors.parallel_adb_command_error);
      return;
    } else if (response && response.statusCode === 408) {
      instrumentAndSendError(executorType, keyObject, requestStateObj, errors.timedout);
      return;
    } else if (response && response.statusCode === 422) {
      instrumentAndSendError(executorType, keyObject, requestStateObj, errors.blacklisted_command);
      return;
    }
    instrumentAndSendError(executorType, keyObject, requestStateObj, errors.internal_error);
  };
  sendRequestToPlatform(executorType, serverURL, requestStateObj, keyObject, errors.internal_error, customTimeout, undefined, responseHandler);
};

module.exports = {
  executeAdbCommand,
  handleUserPackageSensitiveCommands
};
