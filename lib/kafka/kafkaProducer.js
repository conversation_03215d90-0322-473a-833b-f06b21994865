'use strict';

const HubLogger = require('../../log');
const constants = require('../../constants');
const kafka = require('../../clients/kafka/kafkaClient');
const helper = require('../../helper');
const instrumentation = require('../../helpers/instrumentation');
const { isUndefined } = require('../../typeSanity');
const randomstring = require('randomstring');

const LL = constants.LOG_LEVEL;
const kafkaConfig = constants.kafkaConfig;

exports.kafkaProducers = {};
exports.kafkaWorkerPool = null;

// needed because of cyclic dependencies. log -> kafkaproducer -> log
exports.setKafkaWorkerPool = (pool) => {
  exports.kafkaWorkerPool = pool;
};

exports.initKafkaProducers = function () {
  const kafkaProducers = {};
  const supportedRegions = constants.kafkaConfig.producer.regions;
  supportedRegions.forEach((region) => {
    const producer = kafka.initProducers(region);
    kafkaProducers[region] = producer;
  });
  exports.kafkaProducers = kafkaProducers;
  exports.kafkaHealthCheckProducer = kafka.initProducers(constants.region, 1);
};

const kafkaPayloadLengthErrorInstrumentation = (topic, keyObject, payLoadLength, type) => {
  helper.PingZombie({
    kind: 'kafka-logs-push-size-errors',
    data: `${topic} ${payLoadLength} ${keyObject.rails_session_id} ${type}`,
    region: constants.region,
    machine: constants.osHostName,
  });
  const featureUsage = {};
  featureUsage[constants.logTypeToFeatureUsageKafkaMap[topic]] = {
    messageDropped: true
  };
  instrumentation.pushFeatureUsage(keyObject.rails_session_id, featureUsage, () => {});
};

exports.kafkaPayloadLengthErrorInstrumentation = kafkaPayloadLengthErrorInstrumentation;

const msgSendCb = function (err, data) {
  if (err) {
    const topicFailureKey = `${data.topic}_failures`;
    const dataLength = JSON.stringify(data).length;
    HubLogger.exceptionLogger(`Kafka data ${dataLength} ${JSON.stringify(data)}`, undefined, undefined, err.message, err.stack && err.stack.toString());
    exports.producerInstrumentationData.msgsFailed += 1;
    // Mark on topic level that logs were dropped.
    if (isUndefined(exports.producerInstrumentationData[topicFailureKey])) {
      exports.producerInstrumentationData[topicFailureKey] = 0;
    }
    if (isUndefined(exports.producerInstrumentationData[`${topicFailureKey}_data_length_max`])) {
      exports.producerInstrumentationData[`${topicFailureKey}_data_length_max`] = dataLength;
    }

    exports.producerInstrumentationData[topicFailureKey] += 1;
    if (dataLength > exports.producerInstrumentationData[`${topicFailureKey}_data_length_max`]) {
      exports.producerInstrumentationData[`${topicFailureKey}_data_length_max`] = dataLength;
    }
    // Mark on session level that logs were dropped.
    const featureUsage = {};
    featureUsage[constants.logTypeToFeatureUsageKafkaMap[data.topic]] = {
      messageDropped: true
    };
    instrumentation.pushFeatureUsage(data.sessionId, featureUsage, () => {});
  }
  exports.producerInstrumentationData.msgsSent += 1;
};

/*  Initiates beginning of chunking.
For now just sends start control message */
const chunkStart = function (keyObject, requestId) {
  return {
    type: kafkaConfig.LOG_DATA_MESSAGE_TYPE.CHUNK_START,
    message: `Chunking started for ${requestId}`,
    requestId,
    rails_session_id: keyObject.rails_session_id
  };
};

/*  Initiates ending of chunking.
    For now just sends chunk finished control message to intimate the consumer */
const chunkEnd = function (keyObject, requestId) {
  return {
    type: kafkaConfig.LOG_DATA_MESSAGE_TYPE.CHUNK_END,
    message: `Chunking ended for ${requestId}`,
    requestId,
    rails_session_id: keyObject.rails_session_id
  };
};

const createRequestId = (requestCount) => {
  const date = new Date();
  const uniqueId = (`${1900 + date.getYear()}_${date.getMonth() + 1}_${date.getDate()}_${date.getHours()}_${date.getMinutes()}_${date.getSeconds()}_${date.getMilliseconds()}_${randomstring.generate(2)}`);
  return `${requestCount}_${uniqueId}`;
};

/*  Function that chunks a log message and sends individual chunks to kafka along with control messages
*/
const chunkLog = function (keyObject, logdata, topic) {
  const requestId = createRequestId(keyObject.request_count); // Webdriver.io fallback because req id might not be updated
  HubLogger.miscLogger('KafkaProducers', `${keyObject.rails_session_id} Large log data for req: ${requestId} len : ${logdata.length}`, LL.INFO);

  const numChunks = Math.ceil(logdata.length / kafkaConfig.LOG_CHUNK_SIZE);
  const payloads = new Array(numChunks + 2); // start + chunks + end

  payloads[0] = JSON.stringify(chunkStart(keyObject, requestId));
  // eslint-disable-next-line no-plusplus
  for (let index = 0, sizeDone = 0; index < numChunks; ++index, sizeDone += kafkaConfig.LOG_CHUNK_SIZE) {
    payloads[index + 1] = JSON.stringify({
      requestId,
      message: logdata.substr(sizeDone, kafkaConfig.LOG_CHUNK_SIZE),
      chunk_id: index,
      type: kafkaConfig.LOG_DATA_MESSAGE_TYPE.CHUNKED_MESSAGE,
      rails_session_id: keyObject.rails_session_id
    });
  }
  payloads[numChunks + 1] = JSON.stringify(chunkEnd(keyObject, requestId));

  const region = keyObject.originRegion;
  const kafkaProducer = exports.kafkaProducers[region];
  kafka.sendMessages(kafkaProducer, topic, keyObject, payloads, kafka.getRegionBasedPartition, msgSendCb);
};

/*  Primitive to simply write to kafka.
    Calls producer.sendMsg */
const uploadToKafka = function (keyObject, logdata, topic) {
  logdata.rails_session_id = keyObject.rails_session_id;
  logdata.requestId = createRequestId(keyObject.request_count);

  const region = keyObject.originRegion;
  const kafkaProducer = exports.kafkaProducers[region];
  if (logdata.type === kafkaConfig.LOG_DATA_MESSAGE_TYPE.STOP) {
    logdata.appTesting = keyObject.appTesting;
    logdata.logs_aws_storage_class = keyObject.logs_aws_storage_class;
    logdata.enableHubLogsZipping = keyObject.enableHubLogsZipping && (topic.toString().toLowerCase().includes('raw_logs') || topic.toString().toLowerCase().includes('console_logs') || topic.toString().toLowerCase().includes('raw_extended_duration_logs'));
    logdata.enableNewAWSUserForUpload = keyObject.aws_new_user_enabled;
  }
  const logdatastr = JSON.stringify(logdata);
  const payLoadLength = logdatastr.length;
  if (payLoadLength >= kafkaConfig.LOG_SIZE_LIMIT) {
    HubLogger.miscLogger('KafkaProducers', `uploadToKafka payload length more than kafka size limit. rails_session_id: ${keyObject.rails_session_id}, logdata.length: ${payLoadLength}, dataType: ${typeof (logdata.message)}, topic: ${topic}`, LL.INFO);
    kafkaPayloadLengthErrorInstrumentation(topic, keyObject, payLoadLength, logdata.type);
    return;
  }
  kafka.sendMessage(kafkaProducer, topic, keyObject, logdatastr, kafka.getRegionBasedPartition, msgSendCb);
};

exports.producerInstrumentationDataSeed = { msgsSent: 0, msgsFailed: 0 };
exports.producerInstrumentationData = Object.assign({}, exports.producerInstrumentationDataSeed);

const sendProducerInstrumentationData = () => {
  if (exports.producerInstrumentationData.msgsFailed > 100) {
    helper.PingZombie({
      kind: 'kafka-logs-push-errors',
      data: JSON.stringify(exports.producerInstrumentationData),
      region: constants.region,
      machine: constants.osHostName,
    });
  }
  helper.pushStatsToHootHoot('kafkaSessionLogsData', exports.producerInstrumentationData);
  exports.producerInstrumentationData = Object.assign({}, exports.producerInstrumentationDataSeed);
};
setInterval(sendProducerInstrumentationData, 60 * 1000);

/*  This function receives a message that is to be written to kafka.
    In case of log message is too large, it chunks messages and writes them to kafka */
const uploadLogPartToKafkaV1 = function (keyObject, type, logdata, topic) {
  /*  Kafka is not optimized for large messages.
      Chunk limit to be in sync with consumer and broker configs */
  const isStartStopRequest = ['REQUEST_START', 'STOP_SESSION'].includes(type);
  const logType = isStartStopRequest ? LL.INFO : LL.DEBUG;
  HubLogger.miscLogger('KafkaProducers', `uploadLogPartToKafka rails_session_id: ${keyObject.rails_session_id}, type: ${type}, logdata.length: ${logdata.length} topic: ${topic}`, logType);
  if (isStartStopRequest) {
    const kafkaMessageType = type === 'STOP_SESSION' ? kafkaConfig.LOG_DATA_MESSAGE_TYPE.STOP : kafkaConfig.LOG_DATA_MESSAGE_TYPE.START;
    uploadToKafka(keyObject, { type: kafkaMessageType, message: logdata }, topic);
  } else if (logdata.length > kafkaConfig.LOG_CHUNK_SIZE) { // Typical for response of large page sources
    chunkLog(keyObject, logdata, topic);
  } else {
    uploadToKafka(keyObject, { type: kafkaConfig.LOG_DATA_MESSAGE_TYPE.ATOMIC_MESSAGE, message: logdata }, topic);
  }
};

exports.uploadLogPartToKafkaV1 = uploadLogPartToKafkaV1;

const uploadLogPartToKafkaV2 = (keyObject, type, logdata, topic) => {
  const isStartStopRequest = ['REQUEST_START', 'STOP_SESSION'].includes(type);
  const logType = isStartStopRequest ? LL.INFO : LL.DEBUG;
  HubLogger.miscLogger('KafkaProducers', `uploadLogPartToKafkaV2 rails_session_id: ${keyObject.rails_session_id}, type: ${type}, logdata.length: ${logdata.length} topic: ${topic}`, logType);
  const requiredKeyobject = {
    rails_session_id: keyObject.rails_session_id,
    request_count: keyObject.request_count,
    originRegion: keyObject.originRegion,
    appTesting: keyObject.appTesting,
    logs_aws_storage_class: keyObject.logs_aws_storage_class,
    writePartition: keyObject.writePartition,
    enableHubLogsZipping: keyObject.enableHubLogsZipping,
    enableNewAWSUserForUpload: keyObject.aws_new_user_enabled
  };
  const taskParams = {
    requiredKeyobject,
    type,
    logdata,
    topic
  };
  exports.kafkaWorkerPool.runTask(taskParams, 'kafkaPush', (err) => {
    if (err) {
      HubLogger.exceptionLogger('kafkaPush worker uncaught exception', undefined, undefined, err.message, err.stack && err.stack.toString());
    }
  });
};

exports.uploadLogPartToKafkaV2 = uploadLogPartToKafkaV2;

const uploadLogPartToKafka = (keyObject, type, logdata, topic) => {
  const workerCount = constants.workerConf.kafkaPush.numberWorkers && parseInt(constants.workerConf.kafkaPush.numberWorkers, 10);
  // starts request need to be in sync else might be picked later by worker pool in case of multiple workers
  if (workerCount > 0 && type !== 'REQUEST_START' && constants.kafkaWorkerFlags && ((constants.kafkaWorkerFlags.enabled && constants.kafkaWorkerFlags.enabled.toString().toLowerCase()) === 'true' || ((constants.kafkaWorkerFlags.partiallyEnabled && constants.kafkaWorkerFlags.partiallyEnabled.toString().toLowerCase() === 'true') && ((constants.kafkaWorkerFlags[`grp_${keyObject.group_id}_${topic}`] && constants.kafkaWorkerFlags[`grp_${keyObject.group_id}_${topic}`].toString().toLowerCase() === 'true') || (constants.kafkaWorkerFlags[`user_${keyObject.user_id}_${topic}`] && constants.kafkaWorkerFlags[`user_${keyObject.user_id}_${topic}`].toString().toLowerCase() === 'true') || (constants.kafkaWorkerFlags[`${topic}`] && constants.kafkaWorkerFlags[`${topic}`].toString().toLowerCase() === 'true'))))) {
    exports.uploadLogPartToKafkaV2(keyObject, type, logdata, topic);
  } else {
    uploadLogPartToKafkaV1(keyObject, type, logdata, topic);
  }
};

exports.uploadLogPartToKafka = uploadLogPartToKafka;
