'use strict';

const constants = require('../../constants');

const convertToJavascriptPayload = (script, args) => (args ? {
  script,
  args: [args],
} : {
  script,
  args: [],
});

const mapAllCookieCommand = (request) => {
  const mappedRequest = request;
  const payload = convertToJavascriptPayload(constants.DELETE_ALL_COOKIES_PAYLOAD);
  mappedRequest.method = 'POST';
  mappedRequest.path = mappedRequest.path.replace(/cookie$/, 'execute/sync');
  mappedRequest.body = JSON.stringify(payload);
  mappedRequest.headers['content-length'] = Buffer.byteLength(mappedRequest.body);
  return mappedRequest;
};

const mapNamedCookieCommand = (request) => {
  const mappedRequest = request;
  const cookieName = mappedRequest.path.split('/')[6];
  const payload = convertToJavascriptPayload(constants.DELETE_NAMED_COOKIES_PAYLOAD, cookieName);
  mappedRequest.method = 'POST';
  mappedRequest.path = mappedRequest.path.replace(/cookie(\/[^/]+)?$/, 'execute/sync');
  mappedRequest.body = JSON.stringify(payload);
  mappedRequest.headers['content-length'] = Buffer.byteLength(mappedRequest.body);
  return mappedRequest;
};

const mapClickCommand = (request) => {
  const mappedRequest = request;
  const elementId = mappedRequest.path.split('/')[6];
  const payload = convertToJavascriptPayload(constants.CLICK_SCRIPT_PAYLOAD, {
    ELEMENT: elementId,
    'element-6066-11e4-a52e-4f735466cecf': elementId,
  });
  mappedRequest.method = 'POST';
  mappedRequest.path = mappedRequest.path.replace(/element\/[^/]+\/click$/, 'execute/sync');
  mappedRequest.body = JSON.stringify(payload);
  mappedRequest.headers['content-length'] = Buffer.byteLength(mappedRequest.body);
  return mappedRequest;
};

const mapFindElement = (request) => {
  const mappedRequest = request;
  const jsonBody = JSON.parse(mappedRequest.body);
  switch (jsonBody.using.toLowerCase()) {
    case 'id':
      jsonBody.using = 'css selector';
      jsonBody.value = `[id=${jsonBody.value}]`;
      break;
    case 'name':
      jsonBody.using = 'css selector';
      jsonBody.value = `[name=${jsonBody.value}]`;
      break;
    case 'tag name':
      jsonBody.using = 'css selector';
      break;
    case 'class name':
      jsonBody.using = 'css selector';
      jsonBody.value = `.${jsonBody.value}`;
      break;
    default: break;
  }
  mappedRequest.body = JSON.stringify(jsonBody);
  mappedRequest.headers['content-length'] = Buffer.byteLength(mappedRequest.body);
  return mappedRequest;
};

const MAP_SAFARIDRIVER_COMMANDS = {
  DELETE: [
    [/session\/[^/]+\/cookie$/, mapAllCookieCommand],
    [/session\/[^/]+\/cookie\/([^/]+)?$/, mapNamedCookieCommand],
  ],
  POST: [
    [/session\/[^/]+\/element\/[^/]+\/click$/, mapClickCommand],
    [/session\/[^/]+\/element(s)?$/, mapFindElement],
    [/session\/[^/]+\/element(s)?\/[^/]+\/element(s)?$/, mapFindElement],
  ]
};

const mapSafariDriverRequest = (request) => {
  request.path = (request.path || '').toString();
  const method = (request.method || '').toUpperCase();
  const endpoint = request.path.replace(/\/wd\/hub\//g, '');
  const command = (MAP_SAFARIDRIVER_COMMANDS[method] || []).find(([regex]) => endpoint.match(regex)) || [];
  return (command[1] ? command[1](request) : request);
};

module.exports = {
  mapSafariDriverRequest,
  convertToJavascriptPayload,
  mapAllCookieCommand
};
