/* eslint-disable max-lines-per-function */
/* eslint-disable complexity */

'use strict';

const Promise = require('bluebird');
const http = require('http');
const https = require('https');
const zlib = require('zlib');
const constants = require('../../constants');
const { isUndefined } = require('../../typeSanity');
const logger = require('../../logger').basicLogger;
const { appendBStackHostHeader, getEncodedURLParams } = require('./requestHelper');
const dns = require('dns');
/*

Puma default keep-alive timeout: 20 secs
Rproxy keep-alive timeout: 70 secs
Appium keep-alive timeout: 10 minutes
Sinatra keep-alive timeout: x secs
Snapshot jar/binary keep-alive timeout: 5 secs
Selenium Jar: ?

Choosing lowest - 2secs [to avoid race] for timeout value, will bump this up later [when we bump up puma keep-alive]
timeout: close socket after `timeout` ms of inactivity

*/


const httpKeepAliveAgent = new http.Agent({
  keepAlive: true,
  maxFreeSockets: 5,
  maxTotalSockets: 5000,
  timeout: 60000
});
exports.httpKeepAliveAgent = httpKeepAliveAgent;

const httpKeepAliveAgentPrivate = new http.Agent({
  keepAlive: true,
  maxFreeSockets: 500,
  timeout: 60000
});
exports.httpKeepAliveAgentPrivate = httpKeepAliveAgentPrivate;

const httpKeepAliveAgentMobile = new http.Agent({
  keepAlive: true,
  maxFreeSockets: 5,
  maxTotalSockets: 5000,
  timeout: 15000
});
exports.httpKeepAliveAgentMobile = httpKeepAliveAgentMobile;

const httpKeepAliveAgentPrivateMobile = new http.Agent({
  keepAlive: true,
  maxFreeSockets: 500,
  timeout: 15000
});
exports.httpKeepAliveAgentPrivateMobile = httpKeepAliveAgentPrivateMobile;

const TAG = 'RequestHandler';

const mockResponse = {
  statusCode: 200,
  headers: {
    expires: 'Thu, 01 Jan 1970 00:00:00 GMT',
    'cache-control': 'no-cache, no-cache',
    'content-type': 'application/json;charset=utf-8',
    server: 'Jetty(9.4.z-SNAPSHOT)'
  },
  dnsData: {},
};

const mockPerformanceSessions = (options) => {
  try {
    const sessionPaths = options.path.toString().split('/');
    const endpoit = sessionPaths[sessionPaths.length - 1];
    if (constants.MOCKING_PEFROMANCE_ENDPOINTS[options.method] && constants.MOCKING_PEFROMANCE_ENDPOINTS[options.method][endpoit]) {
      const data = constants.MOCKING_PEFROMANCE_ENDPOINTS[options.method][endpoit];
      return data;
    }
    return false;
  } catch (error) {
    return false;
  }
};
exports.mockPerformanceSessions = mockPerformanceSessions;

const skipHeaderCheck = (hostname, path) => {
  const skipHeaderCheckHostnames = [
    'alert-external.browserstack.com',
    'monitor.browserstack.com',
    'example.com',
    'localhost',
    constants.APP_AUTOMATE_BSTACK_API_HOST,
    constants.BSTACK_API_HOST,
    ...Object.values(constants.TCG_SERVICE.regions).map(region => region.endpoint),
  ];
  const skipHeaderCheckPaths = ['/selenium/authenticate', '/session/get_session_data', '/session_timedout'];
  const skipHostname = (
    hostname && skipHeaderCheckHostnames.some(skipElement => hostname.includes(skipElement))
  );
  const skipPath = (
    path && typeof path === 'string' &&
    skipHeaderCheckPaths.some(skipElement => path.includes(skipElement))
  );
  return (skipHostname || skipPath);
};
exports.skipHeaderCheck = skipHeaderCheck;

function addRequestAbortHook(authReqs, req, currWS) {
  if (isUndefined(authReqs) || isUndefined(req) || isUndefined(currWS)) {
    return;
  }
  authReqs.set(currWS, req);
}

function removeAbortHook(authReqs, currWS) {
  if (isUndefined(authReqs) || isUndefined(currWS)) {
    return;
  }
  authReqs.delete(currWS);
}

function getKeepAliveAgent(hostname, portType = '') {
  if (portType === 'mobile') {
    return /rproxy/.test(hostname) ? httpKeepAliveAgentPrivateMobile : httpKeepAliveAgentMobile;
  } else if (portType === 'desktop') {
    return /rproxy/.test(hostname) ? httpKeepAliveAgentPrivate : httpKeepAliveAgent;
  }
  return null;
}
exports.getKeepAliveAgent = getKeepAliveAgent;

const isMobilePort = (portStr) => {
  const portStr3 = portStr.substr(0, 3);
  const portStr4 = portStr.substr(0, 4);
  return (
    portStr3 === '808' /* iOS Sel : 808X */
    || portStr4 === '3808' /* Android Sel : 3808X */
    || portStr3 === '840' /* iOS webdriver_port : 840X */
    || ['5037', '5038'].includes(portStr) /* Android ADB_PORT, ADB_PROXY_PORT */
    || portStr4 === '1808' /* Android chrome_driver_port : 1808X */
    || (portStr >= '18100' && portStr <= '18319') /* Android chromedriverPorts array : [18100...18319] */
    || portStr4 === '2608' /* Android privoxy_port : 2608X */
  );
};
exports.isMobilePort = isMobilePort;

/**
 * Returns a bluebird promise for http / https request using builtin node module
 * @param {Object} options http / https request options for node module.
 * @param {int} timeout timeout for the http request
 */
const request = (options, timeout, retries) => {
  let timedOut = false;
  const tries = (retries || 0);
  const { authReqs, currWS } = options;
  delete options.authReqs;
  delete options.currWS;
  const startTime = new Date();
  return new Promise((resolve, reject) => {
    const requestHostname = options.hostname || options.host;
    if (!skipHeaderCheck(requestHostname, options.path) &&
      !(options.headers && options.headers[constants.BSTACK_HOST_HEADER])) {
      reject(new Error(constants.BSTACK_HOST_HEADER_MISSING_ERR));
      return;
    }

    if (!skipHeaderCheck(requestHostname, options.path)) {
      delete options.headers.Authorization;
      delete options.headers.authorization;
    }

    options.scheme = options.scheme || 'http';
    const nodeRequest = options.scheme === 'http' ? http.request : https.request;

    if (options.scheme === 'http' && requestHostname !== 'localhost') {
      if (!isMobilePort(options.port ? options.port.toString() : '')) {
        options.agent = options.agent || getKeepAliveAgent(requestHostname, 'desktop');
      } else if (constants.mobileHubTerminalKeepAlive === 1) {
        options.agent = options.agent || getKeepAliveAgent(requestHostname, 'mobile');
      }
    }
    try {
      const beforeJarCall = process.hrtime();
      if (options.mockPerformanceJarEndpoint) {
        const performance = mockPerformanceSessions(options);
        if (performance) {
          mockResponse.data = performance.data;
          mockResponse.buffer = Buffer.from(performance.data);
          mockResponse.headers['content-length'] = performance.data.length.toString();
          setTimeout(() => {
            if (options.recordJarTime) {
              const elapsedTime = process.hrtime(beforeJarCall);
              mockResponse.currJarTime = parseInt((elapsedTime[0] * 1000) + (elapsedTime[1] / 1000000), 10);
            }
            resolve(mockResponse);
          }, 100);
          return;
        }
      }
      const req = nodeRequest(options, (res) => {
        const data = [];
        const zipHeaders = ['deflate', 'gzip', 'x-gzip'];
        if (zipHeaders.indexOf(res.headers['content-encoding']) > -1 ||
          zipHeaders.indexOf(res.headers['Content-Encoding']) > -1) {
          const gzip = zlib.createGunzip();
          res.pipe(gzip);
          res = gzip;
        }
        res.on('data', (chunk) => {
          data.push(chunk);
        });
        res.on('end', () => {
          const elapsedTime = process.hrtime(beforeJarCall);
          removeAbortHook(authReqs, currWS);
          const dataBuffer = Buffer.concat(data);

          let responseData;
          try {
            responseData = dataBuffer.toString();
          } catch (error) {
            const dataToLog = {
              requestURL: `${options.hostname}${options.path}`,
              requestMethod: options.method,
              responseSize: dataBuffer.length,
              errorMessage: error.message,
              errorStackTrace: error.stack.toString(),
            };
            logger.error('nodeRequestException: dataBuffer.toString()', JSON.stringify(dataToLog));
          }

          const respObj = {
            statusCode: res.statusCode,
            headers: res.headers,
            data: responseData,
            buffer: dataBuffer,
            dnsData: req.dnsData,
          };
          if (options.recordJarTime) {
            respObj.currJarTime = parseInt((elapsedTime[0] * 1000) + (elapsedTime[1] / 1000000), 10);
          }
          const isRproxyRequest = /rproxy/.test(requestHostname);
          if (isRproxyRequest && constants.RPROXY_RETRY_RESPONSE_TYPES.includes(res.statusCode) && isUndefined(res.headers['upstream-status-code'])) {
            if (tries > 1) reject(new Error('Rproxy returned non 200'));
            else resolve(respObj);
          }
          resolve(respObj);
        });
        res.on('error', (err) => {
          removeAbortHook(authReqs, req);
          if (timedOut) return;
          err.type = 'ResponseError';
          err.port = options.port;
          reject(err);
        });
      });
      addRequestAbortHook(authReqs, req, currWS);
      req.once('socket', (socket) => {
        /*
          This is needed when dns request was cached.
          DNS lookup won't be performed so needed a way to still track the target address
        */
        /* eslint-disable no-underscore-dangle */
        req.dnsData = {
          err: undefined,
          address: socket.address && socket.address().address,
          host: socket._host,
        };

        /*
          This event is triggered when a dns lookup takes place.
          We need this data to debug dns related issues
        */
        socket.removeAllListeners('lookup');
        socket.on('lookup', (err, address) => {
          req.dnsData = {
            err,
            address,
            host: socket._host,
          };
        });
      });

      req.setTimeout(timeout);
      req.on('timeout', () => {
        const endTime = new Date();
        logger.info(`Request timeout - Options: ${options.path}, Timeout: ${timeout} Time consumed: - ${startTime - endTime}`, {
          TAG,
        });
        timedOut = true;
        req.abort();
      });

      req.on('error', (err) => {
        if (timedOut) {
          const endTime = new Date();
          logger.info(`Request Error: ${err} - ${options.path} Time consumed: - ${startTime - endTime}`, {
            TAG,
          });
        }
        err.dnsData = req.dnsData;
        err.type = timedOut ? 'TimeoutError' : 'RequestError';
        err.port = options.port;
        removeAbortHook(authReqs, currWS);
        reject(err);
      });
      if (typeof options.body !== 'undefined') { req.write(options.body); }
      req.end();
    } catch (error) {
      logger.error('NODE REQUEST ERROR', `ERROR: ${error} OPTIONS: ${options}`);
      if (error.code === 'ERR_UNESCAPED_CHARACTERS') {
        logger.error('TypeError', `url have unescaped characters ERR_UNESCAPED_CHARACTERS], URL: '${options.path}', OPTIONS: ${options}`);
      }
      throw error;
    }
  });
};

/**
 * Returns a bluebird promise for http / https request using builtin node module
 * @param {Object} options http / https request options for node module.
 * @param {int} retries number of retries if request fails (default 1)
 */
const call = (params, retries, rproxyRetries) => {
  const tries = (retries || 0) + 1;
  const rproxytries = (rproxyRetries || constants.RPROXY_RETRIES);
  const timeout = params.timeout || 10000;
  const delay = params.delay || 0;

  return request(params, timeout, rproxytries)
    .catch((err) => {
      if (err.message === 'Rproxy returned non 200' && rproxytries > 1) {
        const deleteDnsKey = `lookup_${params.hostname}_0_${dns.ADDRCONFIG}_false`;
        dns.internalCache.forceExpire(deleteDnsKey);
        if (params.agent.keepAlive) {
          params.agent.keepAlive = false;
          params.agent.options.keepAlive = false;
        }
        return Promise.delay(delay)
          .then(() => call(params, tries, rproxytries - 1));
      }
      if (tries > 1) {
        return Promise.delay(delay)
          .then(() => call(params, tries - 2, rproxytries));
      } throw err;
    });
};
exports.call = call;

/**
 * Returns a bluebird promise for http / https request using builtin node module
 * @param {Object} options http / https request options for node module.
 * @param {int} retries number of retries if request fails (default 1)
 * @param {function(requestTime)} longRequestHook execute a method
 *  with response time as a parameter when this completes.
 */
const callWithHook = (params, retries, longRequestHook) => {
  const noop = () => { };
  longRequestHook = longRequestHook || noop;
  const tries = (retries || 0) + 1;
  const timeout = params.timeout || 10000;
  const delay = params.delay || 0;
  let promiseResolve;
  let promiseReject;

  const requestStartTime = new Date();
  const respondPromise = new Promise((resolve, reject) => {
    promiseResolve = resolve;
    promiseReject = reject;
  });

  request(params, timeout)
    .then(promiseResolve)
    .catch((err) => {
      if (tries > 1) {
        Promise.delay(delay).then(() => {
          callWithHook(params, tries - 2)
            .then(promiseResolve)
            .catch(promiseReject);
        });
      } else {
        promiseReject(err);
      }
    }).finally(() => {
      const requestTime = new Date() - requestStartTime;
      const timeLeftForTimeout = timeout - requestTime;

      if (timeLeftForTimeout <= constants.longRequestTimeOffset) {
        longRequestHook({
          requestTime,
        });
      }
    });
  return respondPromise;
};
exports.callWithHook = callWithHook;

/**
 * Returns a bluebird promise for reading incoming requests
 * @param {object} req request to read
 */
const readRequest = req =>
  new Promise((resolve, reject) => {
    const data = [];
    req.on('data', chunk => data.push(chunk));
    req.on('end', () => resolve(Buffer.concat(data).toString()));
    req.on('error', err => reject(err));
  });

exports.readRequest = readRequest;
exports.getEncodedURLParams = getEncodedURLParams;
exports.appendBStackHostHeader = appendBStackHostHeader;
