'use strict';

const constants = require('../../constants');
const { isHash } = require('../../typeSanity');

const getEncodedURLParams = (paramsObject) => {
  if (!isHash(paramsObject)) {
    return '';
  }
  let returnURLString = '';
  Object.keys(paramsObject).forEach((paramKey) => {
    returnURLString += `&${encodeURIComponent(paramKey)}=${encodeURIComponent(paramsObject[paramKey])}`;
  });

  // remove the first & from the url string
  return returnURLString.slice(1);
};

const appendBStackHostHeader = (rproxyHost, headers = {}) => {
  const bstackHostHeader = constants.BSTACK_HOST_HEADER;
  headers[bstackHostHeader] = rproxyHost;
  headers[constants.BSTACK_ENV_HEADER] = constants.envNameTerminal;
  return headers;
};

module.exports = {
  getEncodedURLParams,
  appendBStackHostHeader
};
