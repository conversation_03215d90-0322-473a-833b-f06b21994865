'use strict';

const requestlib = require('./lib/request');
const { isNotUndefined, isHash } = require('./typeSanity');

class WdaClient {
  constructor(host, port, rproxyHost) {
    this.host = host;
    this.port = port;
    this.rproxyHost = rproxyHost;
  }

  validateSessionId() {
    if (!this.sessionId) {
      throw new Error('Sesssion ID is not set');
    }
  }

  async makeRequest(method, path, body) {
    const options = {
      method,
      path,
      body,
      hostname: this.rproxyHost,
      port: this.port,
      headers: requestlib.appendBStackHostHeader(this.host),
    };
    const { data } = await requestlib.call(options);
    return JSON.parse(data);
  }

  async attach() {
    return this.getAndSetSessionId();
  }

  async getAndSetSessionId() {
    const { sessionId } = await this.makeRequest('GET', '/status');
    if (!sessionId) {
      throw new Error('sessionId not found');
    }
    this.sessionId = sessionId;
    return sessionId;
  }

  async findElement(using, value) {
    this.validateSessionId();
    const payload = JSON.stringify({ using, value });
    const { value: { ELEMENT: elementId } } = await this.makeRequest('POST', `/session/${this.sessionId}/element`, payload);
    if (!elementId) {
      throw new Error('Not Found');
    }
    return elementId;
  }

  async clickElement(elementId) {
    this.validateSessionId();
    const endpoint = `/session/${this.sessionId}/element/${elementId}/click`;
    const response = await this.makeRequest('POST', endpoint, '{}');
    // Till now, a successful response had status 0. But with appium 1.15 and above,
    // there is no status key in the response.
    // But the success state can be verified by checking the response value.
    // In case of any unsuccessful state,
    // response.value has an object stating the reason.
    // Here, an exhaustive check is made for successful states.
    if ((typeof response.status !== 'undefined' && response.status === 0)
        || (typeof response.status === 'undefined' && [null, ''].includes(response.value))) {
      return response;
    }
    throw new Error(`Couldn't click Element : ${elementId}. with response value : ${JSON.stringify(response.value)}, and status : ${response.status}`);
  }

  async acceptAlert() {
    this.validateSessionId();
    const endpoint = `/session/${this.sessionId}/alert/accept`;
    const { value, status } = await this.makeRequest('POST', endpoint, JSON.stringify({ sessionId: this.sessionId }));
    if ((isNotUndefined(status) && status !== 0) || (isHash(value) && isNotUndefined(value.error))) {
      throw new Error('acceptAlert: Error occurred while accepting alert');
    }
    return 'acceptAlert: Successfully accepted alert';
  }
}

module.exports = WdaClient;
