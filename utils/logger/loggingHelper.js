'use strict';

const maskSensitiveInformation = require('../../helpers/maskSensitiveInformation');
const constants = require('../../constants');
const HubLogger = require('../../log');
const helper = require('../../helper');
const redactor = require('../../helpers/redactor');
const { getCompleteFileName } = require('../../helpers/debugScreenshots');
const jsExecutor = require('../../lib/customSeleniumHandler/jsExecutor');
const { getPerformanceLogData } = require('../../helpers/lighthouse/helper');

const LL = constants.LOG_LEVEL;
const LOG_TYPE = {
  PERFORMANCE: 'performance',
  AI_HEAL: 'ai_session',
  AI_SOFT_HEAL: 'ai_soft_heal',
};

const sendCombinedLogs = (request, sessionKeyObj, data, debugScreenshot, featureLog = {}) => {
  if (request.log_data && request.log_data.length > 2 * 1024 * 1024) {
    request.log_data = 'TRUNCATED';
    HubLogger.miscLogger('truncateRequest', `rails_session_id: ${sessionKeyObj.rails_session_id}, for request.method: ${request.method}, request.url: ${request.url} with size: ${request.log_data.length}`, LL.DEBUG, sessionKeyObj.debugSession);
  }

  let responseData = {
    string: data,
  };
  maskSensitiveInformation(request, sessionKeyObj, responseData);
  let maskCommands = sessionKeyObj.maskCommands;
  if (sessionKeyObj.realMobile && sessionKeyObj.os !== 'android') {
    maskCommands = maskCommands.concat(constants.DEFAULT_IOS_MASKABLE_COMMANDS);
  }
  ({ request, response: responseData } = redactor.redact(request, responseData, maskCommands, { isBStackExecutor: jsExecutor.isBStackExecutor(request) }));
  helper.convertUnicodeToASCIIForSendKeys(request);

  // eslint-disable-next-line prefer-template
  const requestLogline = helper.s3LogFormat('REQUEST', request.log_date, '[' + request.log_date + '] ' + request.method + ' ' + request.url.replace('/wd/hub', '') + ' ' + request.log_data) + '\r\n';
  const debugLogLine = debugScreenshot ? helper.s3LogFormat('DEBUG', helper.getDate(), getCompleteFileName(sessionKeyObj, debugScreenshot)) + '\r\n' : '';
  const performanceLogLine = featureLog.logType === LOG_TYPE.PERFORMANCE ? helper.s3LogFormat('PERFORMANCE', helper.getDate(), getPerformanceLogData(sessionKeyObj)) + '\r\n' : '';
  const selfHealLogLine = featureLog.logType === LOG_TYPE.AI_HEAL ? helper.s3LogFormat('SELFHEAL', helper.getDate(), featureLog.data) + '\r\n' : '';
  const softHealLogLine = featureLog.logType === LOG_TYPE.AI_SOFT_HEAL ? helper.s3LogFormat('SOFTHEAL', helper.getDate(), featureLog.data) + '\r\n' : '';
  const responseLogLine = helper.s3LogFormat('RESPONSE', helper.getDate(), responseData.string.toString('utf-8'));
  HubLogger.sessionLog(sessionKeyObj, 'REQUEST_RESPONSE', requestLogline + performanceLogLine + selfHealLogLine + softHealLogLine + debugLogLine + responseLogLine);
};

module.exports = {
  sendCombinedLogs,
  LOG_TYPE,
};
