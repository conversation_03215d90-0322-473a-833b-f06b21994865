'use strict';

const helper = require('../../helper');
const { isUndefined } = require('../../typeSanity');
/* eslint-disable max-len */

const {
  MOVETO_ACTION_PAYLOAD,
  CLICK_ACTION_PAYLOAD,
  BUTTON_DOWN_PAYLOAD,
  BUTTON_UP_PAYLOAD,
  DOUBLE_CLICK_PAYLOAD,
} = require('./actionPayload');
const {
  DISPLAYED_PAYLOAD,
  GET_ATTRIBUTE_PAYLOAD,
  LOCATION_IN_VIEW_PAYLOAD,
  SUBMIT_FORM_PAYLOAD,
} = require('./customPayloads');

const getCurrentWindowHandle = (request) => {
  const mappedRequest = request;
  const isPlural = mappedRequest.path.match(/_handles/);
  const replacement = isPlural ? '_handles' : '_handle';
  mappedRequest.path = mappedRequest.path.replace(replacement, '');
  mappedRequest.path = isPlural ? `${mappedRequest.path}/handles` : mappedRequest.path;
  return mappedRequest;
};

const findElement = (request) => {
  const mappedRequest = request;
  const jsonBody = JSON.parse(mappedRequest.body);
  switch (jsonBody.using.toLowerCase()) {
    case 'id':
      jsonBody.using = 'css selector';
      jsonBody.value = `[id=${jsonBody.value}]`;
      break;
    case 'name':
      jsonBody.using = 'css selector';
      jsonBody.value = `[name=${jsonBody.value}]`;
      break;
    case 'tag name':
      jsonBody.using = 'css selector';
      break;
    case 'class name':
      jsonBody.using = 'css selector';
      jsonBody.value = `.${jsonBody.value}`;
      break;
    default: break;
  }
  mappedRequest.body = JSON.stringify(jsonBody);
  mappedRequest.headers['content-length'] = Buffer.byteLength(mappedRequest.body);
  return mappedRequest;
};

const executeScript = (request) => {
  const mappedRequest = request;
  const isAsync = request.path.match(/execute_async/);
  mappedRequest.path = `${mappedRequest.path.replace(/_async/, '')}/${isAsync ? 'async' : 'sync'}`;
  return mappedRequest;
};

const maximizeWindow = (request) => {
  const mappedRequest = request;
  mappedRequest.path = mappedRequest.path.replace(/\/window\/[^/]+\/maximize/, '/window/maximize');
  mappedRequest.body = '{}';
  mappedRequest.headers['content-length'] = Buffer.byteLength(mappedRequest.body);
  return mappedRequest;
};

const setTimeouts = (request) => {
  const mappedRequest = request;
  const isAsyncScriptTimeout = request.path.match(/async_script$/);
  const isImplicitWaitTimeout = request.path.match(/implicit_wait$/);
  const { type, ms } = JSON.parse(mappedRequest.body);
  if (isAsyncScriptTimeout) {
    mappedRequest.path = request.path.replace(/\/async_script$/, '');
    mappedRequest.body = JSON.stringify({ script: ms });
  } else if (isImplicitWaitTimeout) {
    mappedRequest.path = request.path.replace(/\/implicit_wait$/, '');
    mappedRequest.body = JSON.stringify({ implicit: ms });
  } else {
    const newType = (type || '').trim() === 'page load' ? 'pageLoad' : type;
    mappedRequest.body = JSON.stringify({ [newType]: ms });
  }
  mappedRequest.headers['content-length'] = Buffer.byteLength(mappedRequest.body);
  return mappedRequest;
};

const getOrSetWindowSize = (request) => {
  const mappedRequest = request;
  mappedRequest.path = mappedRequest.path.replace(/\/window\/[^/]+\/(size|position)/, '/window/rect');
  const jsonBody = JSON.parse(mappedRequest.body);
  delete jsonBody.windowHandle;
  mappedRequest.body = JSON.stringify(jsonBody);
  mappedRequest.headers['content-length'] = Buffer.byteLength(mappedRequest.body);
  return mappedRequest;
};

const getActiveElement = (request) => {
  const mappedRequest = request;
  mappedRequest.method = 'GET';
  return mappedRequest;
};

const dismissOrAcceptAlert = (request) => {
  const mappedRequest = request;
  const splittedPath = mappedRequest.path.split('/');
  const alertAction = splittedPath[splittedPath.length - 1].split('_')[0];
  mappedRequest.path = mappedRequest.path.replace(splittedPath[splittedPath.length - 1], `alert/${alertAction}`);
  mappedRequest.body = '{}';
  mappedRequest.headers['content-length'] = Buffer.byteLength(mappedRequest.body);
  return mappedRequest;
};

const getOrSetAlertText = (request) => {
  const mappedRequest = request;
  mappedRequest.path = mappedRequest.path.replace(/alert_text/, 'alert/text');
  return mappedRequest;
};

const setElementValue = (request) => {
  const mappedRequest = request;
  const { value = [] } = JSON.parse(mappedRequest.body);
  const text = value.join('');
  mappedRequest.body = JSON.stringify({ text });
  mappedRequest.headers['content-length'] = Buffer.byteLength(mappedRequest.body);
  return mappedRequest;
};

const getElementLocationOrSize = (request) => {
  const mappedRequest = request;
  mappedRequest.path = mappedRequest.path.replace(/location|location_in_view|size/, 'rect');
  return mappedRequest;
};

const mapActionIfPossible = (request) => {
  const mappedRequest = request;
  const splittedPath = mappedRequest.path.split('/');
  const action = splittedPath[splittedPath.length - 1];
  mappedRequest.path = mappedRequest.path.replace(action, 'actions');
  switch (action) {
    case 'moveto': {
      const jsonBody = JSON.parse(mappedRequest.body);
      const w3cPayload = Object.assign({}, MOVETO_ACTION_PAYLOAD);
      w3cPayload.actions[0].actions[0].x = jsonBody.xoffset || 0;
      w3cPayload.actions[0].actions[0].y = jsonBody.yoffset || 0;
      w3cPayload.actions[0].actions[0].origin['element-6066-11e4-a52e-4f735466cecf'] = jsonBody.element;
      mappedRequest.body = JSON.stringify(w3cPayload);
      break;
    }
    case 'click': {
      mappedRequest.body = JSON.stringify(CLICK_ACTION_PAYLOAD);
      break;
    }
    case 'buttondown': {
      mappedRequest.body = JSON.stringify(BUTTON_DOWN_PAYLOAD);
      break;
    }
    case 'buttonup': {
      mappedRequest.body = JSON.stringify(BUTTON_UP_PAYLOAD);
      break;
    }
    case 'doubleclick': {
      mappedRequest.body = JSON.stringify(DOUBLE_CLICK_PAYLOAD);
      break;
    }
    default:
      break;
  }
  mappedRequest.headers['content-length'] = Buffer.byteLength(mappedRequest.body);
  return mappedRequest;
};

const convertToJavascriptPayload = (script, elementId, extraArgs = []) => ({
  script,
  args: [{
    ELEMENT: elementId,
    'element-6066-11e4-a52e-4f735466cecf': elementId,
  }, ...extraArgs],
});

const checkIfDisplayed = (request) => {
  const mappedRequest = request;
  const elementId = mappedRequest.path.split('/')[6];
  const payload = convertToJavascriptPayload(DISPLAYED_PAYLOAD, elementId);
  mappedRequest.method = 'POST';
  mappedRequest.path = mappedRequest.path.replace(/element\/[^/]+\/displayed/, 'execute/sync');
  mappedRequest.body = JSON.stringify(payload);
  mappedRequest.headers['content-length'] = Buffer.byteLength(mappedRequest.body);
  return mappedRequest;
};

const getAttributeValue = (request) => {
  const mappedRequest = request;
  const elementId = mappedRequest.path.split('/')[6];
  const attributeName = mappedRequest.path.split('/')[8];
  const payload = convertToJavascriptPayload(GET_ATTRIBUTE_PAYLOAD, elementId, [attributeName]);
  mappedRequest.method = 'POST';
  mappedRequest.path = mappedRequest.path.replace(/element\/[^/]+\/attribute\/[^/]+/, 'execute/sync');
  mappedRequest.body = JSON.stringify(payload);
  mappedRequest.headers['content-length'] = Buffer.byteLength(mappedRequest.body);
  return mappedRequest;
};

const getLocationOfElementInView = (request) => {
  const mappedRequest = request;
  const elementId = mappedRequest.path.split('/')[6];
  const payload = convertToJavascriptPayload(LOCATION_IN_VIEW_PAYLOAD, elementId);
  mappedRequest.method = 'POST';
  mappedRequest.path = mappedRequest.path.replace(/element\/[^/]+\/location_in_view/, 'execute/sync');
  mappedRequest.body = JSON.stringify(payload);
  mappedRequest.headers['content-length'] = Buffer.byteLength(mappedRequest.body);
  return mappedRequest;
};

const initRequestBody = (request) => {
  const mappedRequest = request;
  mappedRequest.body = '{}';
  mappedRequest.headers['content-length'] = Buffer.byteLength(mappedRequest.body);
  return mappedRequest;
};

const renamePayloadParams = (request) => {
  const mappedRequest = request;
  const payload = JSON.parse(mappedRequest.body);
  payload.handle = payload.handle || payload.name;
  mappedRequest.body = JSON.stringify(payload);
  mappedRequest.headers['content-length'] = Buffer.byteLength(mappedRequest.body);
  return mappedRequest;
};

const submitForm = (request) => {
  const mappedRequest = request;
  const elementId = mappedRequest.path.split('/')[6];
  const payload = convertToJavascriptPayload(SUBMIT_FORM_PAYLOAD, elementId);
  mappedRequest.path = mappedRequest.path.replace(/element\/[^/]+\/submit/, 'execute/sync');
  mappedRequest.body = JSON.stringify(payload);
  mappedRequest.headers['content-length'] = Buffer.byteLength(mappedRequest.body);
  return mappedRequest;
};

const MAPPABLE_OSS_COMMANDS = {
  GET: [
    [/session\/[^/]+\/element\/[^/]+\/displayed$/, checkIfDisplayed],
    [/session\/[^/]+\/element\/[^/]+\/attribute\/[^/]+$/, getAttributeValue],
    [/session\/[^/]+\/window_handle(s)?$/, getCurrentWindowHandle],
    [/session\/[^/]+\/window\/[^/]+\/(size|position)$/, getOrSetWindowSize],
    [/session\/[^/]+\/alert_text$/, getOrSetAlertText],
    [/session\/[^/]+\/element\/[^/]+\/(location|size)$/, getElementLocationOrSize],
    [/session\/[^/]+\/element\/[^/]+\/location_in_view$/, getLocationOfElementInView],
  ],
  POST: [
    [/session\/[^/]+\/element(s)?$/, findElement],
    [/session\/[^/]+\/element(s)?\/[^/]+\/element(s)?$/, findElement],
    [/session\/[^/]+\/element\/[^/]+\/value$/, setElementValue],
    [/session\/[^/]+\/execute(_async)?$/, executeScript],
    [/session\/[^/]+\/window\/[^/]+\/maximize$/, maximizeWindow],
    [/session\/[^/]+\/timeouts(\/(async_script|implicit_wait))?$/, setTimeouts],
    [/session\/[^/]+\/window\/[^/]+\/(size|position)$/, getOrSetWindowSize],
    [/session\/[^/]+\/element\/active$/, getActiveElement],
    [/session\/[^/]+\/(dismiss|accept)_alert$/, dismissOrAcceptAlert],
    [/session\/[^/]+\/alert_text$/, getOrSetAlertText],
    [/session\/[^/]+\/(moveto|click|buttondown|buttonup|doubleclick)$/, mapActionIfPossible],
    [/session\/[^/]+\/element\/[^/]+\/(click|clear)$/, initRequestBody],
    [/session\/[^/]+\/(forward|back|refresh)$/, initRequestBody],
    [/session\/[^/]+\/frame\/parent$/, initRequestBody],
    [/session\/[^/]+\/window$/, renamePayloadParams],
  ],
};

const mapRequestToW3C = (request) => {
  request.path = (request.path || '').toString();
  const method = (request.method || '').toUpperCase();
  const endpoint = request.path.replace(/\/wd\/hub\//g, '');
  const command = (MAPPABLE_OSS_COMMANDS[method] || []).find(([regex]) => endpoint.match(regex)) || [];
  return (command[1] ? command[1](request) : request);
};

const mapResponseToW3C = (response) => {
  const mappedResponse = response;
  if (mappedResponse && helper.isHash(mappedResponse.value) && (isUndefined(mappedResponse.value.sessionId && mappedResponse.sessionId))) {
    mappedResponse.value.sessionId = mappedResponse.sessionId;
    mappedResponse.value.capabilities = { ...mappedResponse.value };
  }
  return mappedResponse;
};

module.exports = {
  mapRequestToW3C,
  mapResponseToW3C,
  getOrSetWindowSize,
  getLocationOfElementInView,
  mapActionIfPossible,
  submitForm,
  initRequestBody,
};
