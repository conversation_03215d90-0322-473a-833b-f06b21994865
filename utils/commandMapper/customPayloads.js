'use strict';

const fs = require('fs');
const path = require('path');

const isDisplayedSourcePath = path.join(__dirname, 'isDisplayed.js');
const getAttributeSourcePath = path.join(__dirname, 'getAttribute.js');

const isDisplayedSource = fs.readFileSync(isDisplayedSourcePath, 'utf-8');
const getAttributeSource = fs.readFileSync(getAttributeSourcePath, 'utf-8');

const DISPLAYED_PAYLOAD = `return (${isDisplayedSource}).apply(null, arguments);`;
const GET_ATTRIBUTE_PAYLOAD = `return (${getAttributeSource}).apply(null, arguments);`;
const LOCATION_IN_VIEW_PAYLOAD = 'arguments[0].scrollIntoView(true); return arguments[0].getBoundingClientRect()';
const SUBMIT_FORM_PAYLOAD = 'var e=arguments[0].ownerDocument.createEvent("Event");e.initEvent("submit", true, true);if(arguments[0].dispatchEvent(e)) {arguments[0].submit()}';

module.exports = {
  DISPLAYED_PAYLOAD,
  GET_ATTRIBUTE_PAYLOAD,
  LOCATION_IN_VIEW_PAYLOAD,
  SUBMIT_FORM_PAYLOAD,
};
