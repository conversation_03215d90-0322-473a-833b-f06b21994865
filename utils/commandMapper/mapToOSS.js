'use strict';

/* eslint-disable max-len */

const helper = require('../../helper');
const { fullScreen } = require('./mapHybridToOSS');

// W3C Commands which should be mapped for OSS
const MAPPABLE_W3C_COMMANDS = {
  POST: [
    [/session\/[^/]+\/window\/fullscreen$/, fullScreen],
  ],
};

// Looks for commands available in MAPPABLE_W3C_COMMANDS 
// Returns request as it is if no regex matches from the list
// Calls the matching command if found
const mapRequestToOSS = (request) => {
  request.path = (request.path || '').toString();
  const method = (request.method || '').toUpperCase();
  const endpoint = request.path.replace(/\/wd\/hub\//g, '');
  const command = (MAPPABLE_W3C_COMMANDS[method] || []).find(([regex]) => endpoint.match(regex)) || [];
  return (command[1] ? command[1](request) : request);
};

const errorCodeToStatus = Object.freeze({
  'element click intercepted': 9,
  'element not interactable': 12,
  'insecure certificate': 9,
  'invalid argument': 9,
  'invalid cookie domain': 24,
  'invalid element state': 12,
  'invalid selector': 32,
  'invalid session id': 33,
  'javascript error': 17,
  'move target out of bounds': 34,
  'no such alert': 27,
  'no such cookie': 24,
  'no such element': 7,
  'no such frame': 8,
  'no such window': 23,
  'script timeout': 28,
  'session not created': 33,
  'stale element reference': 10,
  timeout: 21,
  'unable to set cookie': 25,
  'unable to capture screen': 9,
  'unexpected alert open': 26,
  'unknown command': 9,
  'unknown error': 13,
  'unknown method': 9,
  'unsupported operation': 9,
});

const mapErrorCodeToStatus = (response) => {
  const mappedResponse = response;
  if (helper.isHash(mappedResponse.value) && typeof (mappedResponse.value.error) === 'string') {
    const errorCode = mappedResponse.value.error;
    const matchedEntry = Object.entries(errorCodeToStatus).find(([sampleErrorCode]) => errorCode.match(sampleErrorCode)) || [];
    mappedResponse.status = matchedEntry[1] || 13;
  } else {
    mappedResponse.status = 0;
  }
  return mappedResponse;
};

const mapResponseToOSS = (response, clientSessionId) => {
  const mappedResponse = response;
  if (helper.isHash(mappedResponse.value)) {
    mappedResponse.sessionId = mappedResponse.value['webdriver.remote.sessionid'] || mappedResponse.value.sessionId;
    if (mappedResponse.value.capabilities) {
      // Start Session response from target(jar)
      mappedResponse.value = mappedResponse.value.capabilities;
    } else if (mappedResponse.value['element-6066-11e4-a52e-4f735466cecf']) {
      // find one element
      mappedResponse.value.ELEMENT = mappedResponse.value['element-6066-11e4-a52e-4f735466cecf'];
      delete mappedResponse.value['element-6066-11e4-a52e-4f735466cecf'];
    }
  } else if (Array.isArray(mappedResponse.value) && (mappedResponse.value[0] || {})['element-6066-11e4-a52e-4f735466cecf']) {
    // find multiple elements
    mappedResponse.value.map((ele) => {
      const element = ele;
      element.ELEMENT = element['element-6066-11e4-a52e-4f735466cecf'];
      return element;
    });
  }
  mappedResponse.sessionId = mappedResponse.sessionId || clientSessionId;
  return mapErrorCodeToStatus(mappedResponse);
};

module.exports = { mapRequestToOSS, mapResponseToOSS };
