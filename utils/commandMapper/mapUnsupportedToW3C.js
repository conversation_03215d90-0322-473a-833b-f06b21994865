'use strict';

const helper = require('../../helper');
const { getOrSetWindowSize, getLocationOfElementInView, mapActionIfPossible, submitForm, initRequestBody } = require('./mapToW3C');

const MAPPABLE_UNHANDLED_COMMANDS = {
  GET: [
    [/session\/[^/]+\/window\/[^/]+\/size$/, getOrSetWindowSize],
    [/session\/[^/]+\/element\/[^/]+\/location_in_view$/, getLocationOfElementInView],
  ],
  POST: [
    [/session\/[^/]+\/(moveto|click|buttondown|buttonup|doubleclick)$/, mapActionIfPossible],
    [/session\/[^/]+\/element\/[^/]+\/submit$/, submitForm],
    [/session\/[^/]+\/frame\/parent$/, initRequestBody],
  ],
};

const mapUnsupportedRequestToW3C = (request) => {
  request.path = (request.path || '').toString();
  const method = (request.method || '').toUpperCase();
  const endpoint = request.path.replace(/\/wd\/hub\//g, '');
  const command = (MAPPABLE_UNHANDLED_COMMANDS[method] || []).find(([regex]) => endpoint.match(regex)) || [];
  return (command[1] ? command[1](request) : request);
};

module.exports = { mapUnsupportedRequestToW3C };
