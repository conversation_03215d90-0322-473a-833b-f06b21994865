'use strict';

const helper = require('../../helper');
const { isUndefined } = require('../../typeSanity');
/* eslint-disable max-len */

// Directs /se/log path to /log
const consoleLogs = (request) => {
  request.path = request.path.replace('/se/log', '/log');
  return request
}

// Directs /fullscreen to /maximize
const fullScreen = (request) => {
  request.path = request.path.replace('/fullscreen', '/maximize');
  return request;
}

// Directs /execute/sync to /execute
const executeScript = (request) => {
  request.path = request.path.replace('/execute/sync', '/execute');
  return request;
}

// Hybrid Commands which should be mapped for OSS
const MAPPABLE_HYBRID_COMMANDS = {
  POST: [
    [/session\/[^/]+\/se\/log$/, consoleLogs],
    [/session\/[^/]+\/window\/fullscreen$/, fullScreen],
    [/session\/[^/]+\/execute\/sync$/, executeScript],
  ],
};

// Looks for commands available in MAPPABLE_HYBRID_COMMANDS 
// Returns request as it is if no regex matches from the list
// Calls the matching command if found
const mapHybridRequestToOSS = (request) => {
  request.path = (request.path || '').toString();
  const method = (request.method || '').toUpperCase();
  const endpoint = request.path.replace(/\/wd\/hub\//g, '');
  const command = (MAPPABLE_HYBRID_COMMANDS[method] || []).find(([regex]) => endpoint.match(regex)) || [];
  return (command[1] ? command[1](request) : request);
};

const mapOSSResponseToHybrid = response => {
  const mappedResponse = response;
  if (mappedResponse) {
    if (helper.isHash(mappedResponse.value)) {
      if (mappedResponse.value['ELEMENT']) {
        // find one element
        mappedResponse.value['element-6066-11e4-a52e-4f735466cecf'] = mappedResponse.value['ELEMENT'];
      }
      if (isUndefined(mappedResponse.value.sessionId && mappedResponse.sessionId) ) {
        mappedResponse.value.sessionId = mappedResponse.sessionId;
        mappedResponse.value.capabilities = { ...mappedResponse.value };
      }
    } else if (Array.isArray(mappedResponse.value) && (mappedResponse.value[0] || {})['ELEMENT']) {
      // find multiple elements
      mappedResponse.value.map(ele => {
        const element = ele;
        element['element-6066-11e4-a52e-4f735466cecf'] = element['ELEMENT'];
        return element;
      });
    }
    if(mappedResponse.state && mappedResponse.state !== 'success') {
      mappedResponse.value = {
        error:
          mappedResponse.value && mappedResponse.value.error
            ? mappedResponse.value.error
            : mappedResponse.state,
        message:
          mappedResponse.value && mappedResponse.value.message
            ? mappedResponse.value.message
            : mappedResponse.state,
        stacktrace: '',
      };
    }
  }
  return mappedResponse;
};

module.exports = { mapHybridRequestToOSS, mapOSSResponseToHybrid, fullScreen };
