'use strict';

/* eslint-disable max-len */

module.exports = {
  MOVETO_ACTION_PAYLOAD: {
    actions: [{
      type: 'pointer',
      parameters: {
        pointerType: 'mouse',
      },
      id: 'mouse',
      actions: [{
        type: 'pointerMove',
        duration: 250,
        x: -270, // Should be filled with element's x offset
        y: -7, // Should be filled with element's y offset
        origin: {
          'element-6066-11e4-a52e-4f735466cecf': '', // Should be filled with element ID
        },
      }],
    }, {
      type: 'key',
      id: 'key',
      actions: [{
        type: 'pause',
        duration: 0,
      }],
    }],
  },
  CLICK_ACTION_PAYLOAD: {
    actions: [{
      type: 'pointer',
      parameters: {
        pointerType: 'mouse',
      },
      id: 'mouse',
      actions: [{
        type: 'pointerDown',
        duration: 0,
        button: 0,
      }, {
        type: 'pointerUp',
        duration: 0,
        button: 0,
      }],
    }, {
      type: 'key',
      id: 'key',
      actions: [{
        type: 'pause',
        duration: 0,
      }, {
        type: 'pause',
        duration: 0,
      }],
    }],
  },
  BUTTON_DOWN_PAYLOAD: {
    actions: [{
      type: 'pointer',
      parameters: {
        pointerType: 'mouse',
      },
      id: 'mouse',
      actions: [{
        type: 'pointerDown',
        duration: 0,
        button: 0,
      }],
    }, {
      type: 'key',
      id: 'key',
      actions: [{
        type: 'pause',
        duration: 0,
      }],
    }],
  },
  BUTTON_UP_PAYLOAD: {
    actions: [{
      type: 'pointer',
      parameters: {
        pointerType: 'mouse',
      },
      id: 'mouse',
      actions: [{
        type: 'pointerUp',
        duration: 0,
        button: 0,
      }],
    }, {
      type: 'key',
      id: 'key',
      actions: [{
        type: 'pause',
        duration: 0,
      }],
    }],
  },
  DOUBLE_CLICK_PAYLOAD: {
    actions: [{
      type: 'pointer',
      parameters: {
        pointerType: 'mouse',
      },
      id: 'mouse',
      actions: [{
        type: 'pointerDown',
        duration: 0,
        button: 0,
      }, {
        type: 'pointerUp',
        duration: 0,
        button: 0,
      }, {
        type: 'pointerDown',
        duration: 0,
        button: 0,
      }, {
        type: 'pointerUp',
        duration: 0,
        button: 0,
      }],
    }, {
      type: 'key',
      id: 'key',
      actions: [{
        type: 'pause',
        duration: 0,
      }, {
        type: 'pause',
        duration: 0,
      }, {
        type: 'pause',
        duration: 0,
      }, {
        type: 'pause',
        duration: 0,
      }],
    }],
  },
};

// Action Payloads
// moveto -> {'actions': [{'type': 'pointer', 'parameters': {'pointerType': 'mouse'}, 'id': 'mouse', 'actions': [{'type': 'pointerMove', 'duration': 250, 'x': xoffset, 'y': yoffset, 'origin': 'pointer'}]}, {'type': 'key', 'id': 'key', 'actions': [{'type': 'pause', 'duration': 0}]}]}
// click -> {'actions': [{'type': 'pointer', 'parameters': {'pointerType': 'mouse'}, 'id': 'mouse', 'actions': [{'type': 'pointerDown', 'duration': 0, 'button': 0}, {'type': 'pointerUp', 'duration': 0, 'button': 0}]}, {'type': 'key', 'id': 'key', 'actions': [{'type': 'pause', 'duration': 0}, {'type': 'pause', 'duration': 0}]}]}
// buttondown -> {'actions': [{'type': 'pointer', 'parameters': {'pointerType': 'mouse'}, 'id': 'mouse', 'actions': [{'type': 'pointerDown', 'duration': 0, 'button': 0}]}, {'type': 'key', 'id': 'key', 'actions': [{'type': 'pause', 'duration': 0}]}]}
// buttonup -> {'actions': [{'type': 'pointer', 'parameters': {'pointerType': 'mouse'}, 'id': 'mouse', 'actions': [{'type': 'pointerUp', 'duration': 0, 'button': 0}]}, {'type': 'key', 'id': 'key', 'actions': [{'type': 'pause', 'duration': 0}]}]}
// doubleclick -> {'actions': [{'type': 'pointer', 'parameters': {'pointerType': 'mouse'}, 'id': 'mouse', 'actions': [{'type': 'pointerDown', 'duration': 0, 'button': 0}, {'type': 'pointerUp', 'duration': 0, 'button': 0}, {'type': 'pointerDown', 'duration': 0, 'button': 0}, {'type': 'pointerUp', 'duration': 0, 'button': 0}]}, {'type': 'key', 'id': 'key', 'actions': [{'type': 'pause', 'duration': 0}, {'type': 'pause', 'duration': 0}, {'type': 'pause', 'duration': 0}, {'type': 'pause', 'duration': 0}]}]}
