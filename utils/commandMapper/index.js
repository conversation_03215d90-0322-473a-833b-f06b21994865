'use strict';

/* eslint-disable max-len, no-bitwise */

const helper = require('../../helper');
const { mapRequestToW3C, mapResponseToW3C } = require('./mapToW3C');
const { mapRequestToOSS, mapResponseToOSS } = require('./mapToOSS');
const { mapHybridRequestToOSS, mapOSSResponseToHybrid } = require('./mapHybridToOSS');
const { mapUnsupportedRequestToW3C } = require('./mapUnsupportedToW3C');

const CLIENT_DIALECTS = ['INVALID', 'W3C-ONLY', 'OSS-ONLY', 'HYBRID'];
const TARGET_DIALECTS = ['W3C', 'OSS'];

const MAPPABLE_DIALECT_COMBINATIONS = {
  W3C: {
    'OSS-ONLY': {
      request: mapRequestToW3C,
      response: mapResponseToOSS,
    },
    'HYBRID': {
      request: mapUnsupportedRequestToW3C,
    },
    'W3C-ONLY': {
      request: mapUnsupportedRequestToW3C,
    },
    'INVALID': {
      request: mapUnsupportedRequestToW3C,
    },
  },
  OSS: {
    'W3C-ONLY': {
      request: mapRequestToOSS,
      response: mapResponseToW3C,
    },
    'HYBRID' : {
      request: mapHybridRequestToOSS,
      response: mapOSSResponseToHybrid,
    }
  },
};

const detectClientDialect = (caps) => {
  const validDesiredCapabilities = +Boolean(helper.isHash(caps.desiredCapabilities));
  const validCapabilities = +Boolean(helper.isHash(caps.capabilities));
  return CLIENT_DIALECTS[(validDesiredCapabilities << 1) | (validCapabilities)];
};

const mapToAppropriateDialect = (clientDialect, targetDialect, options, payloadType, clientSessionId, dataType = 'string') => {
  const isValidClientDialect = CLIENT_DIALECTS.indexOf(clientDialect) > -1;
  const isValidTargetDialect = TARGET_DIALECTS.indexOf(targetDialect) > -1;
  if (isValidClientDialect && isValidTargetDialect) {
    const mapper = (MAPPABLE_DIALECT_COMBINATIONS[targetDialect][clientDialect] || {})[payloadType];
    if (mapper && options) {
      try {
        let dataOptions = options;
        if (['String', 'Buffer'].indexOf(options.constructor.name) > -1) {
          dataOptions = JSON.parse(options);
        }
        // clientSessionId is only utilized in response mapping.
        let result = mapper(dataOptions, clientSessionId);
        if (['String', 'Buffer'].indexOf(options.constructor.name) > -1 && dataType !== 'object') {
          result = JSON.stringify(result);
        }
        if (options.constructor.name === 'Buffer') {
          result = Buffer.from(result);
        }
        return result;
      } catch (err) {
        return options;
      }
    }
  }
  return options;
};

const mapRequestToAppropriateDialect = ({ clientDialect, dialect: targetDialect }, request) => mapToAppropriateDialect(clientDialect, targetDialect, request, 'request');

const mapResponseToAppropriateDialect = ({ clientDialect, dialect: targetDialect, key: clientSessionId }, response, dataType = 'string') => mapToAppropriateDialect(clientDialect, targetDialect, response, 'response', clientSessionId, dataType);

module.exports = {
  detectClientDialect,
  mapRequestToAppropriateDialect,
  mapResponseToAppropriateDialect,
};
