'use strict';

const constants = require('../../constants');
const HubLogger = require('../../log');

const { LOG_LEVEL: LL } = constants;

const countSeleniumRequests = (railsSessionId) => {
  if (railsSessionId) {
    let { seleniumRequestsCount = 0 } = constants.global_registry[railsSessionId];
    seleniumRequestsCount += 1;
    constants.global_registry[railsSessionId].seleniumRequestsCount = seleniumRequestsCount;
  } else {
    HubLogger.miscLogger('countSeleniumRequests', 'Rails Session Id is undefined', LL.DEBUG);
  }
};

const countAppiumCommandUsage = (railsSessionId, command) => {
  if (railsSessionId) {
    const tag = constants.APP_AUTOMATE_APPIUM_COMMANDS_TO_COUNT[command];
    const { appiumCommandUsage = {} } = constants.global_registry[railsSessionId];
    let commandCount = appiumCommandUsage[tag] || 0;
    commandCount += 1;
    appiumCommandUsage[tag] = commandCount;
    constants.global_registry[railsSessionId].appiumCommandUsage = appiumCommandUsage;
  } else {
    HubLogger.miscLogger('countAppiumCommandUse', 'Rails Session Id is undefined', LL.DEBUG);
  }
};

const trackAppiumCommandData = (railsSessionId, command, data) => {
  if (railsSessionId) {
    const { tag, fields } = constants.APP_AUTOMATE_APPIUM_COMMANDS_TO_TRACK_DATA[command];
    let { appiumCommandUsage } = constants.global_registry[railsSessionId];

    if (!appiumCommandUsage) {
      appiumCommandUsage = {};
      appiumCommandUsage[tag] = { count: 0, params: [] };
    }

    if (!appiumCommandUsage[tag]) {
      appiumCommandUsage[tag] = { count: 0, params: [] };
    }

    appiumCommandUsage[tag].count += 1;

    const dataToTrack = fields.reduce((acc, f) => { acc[f] = data[f]; return acc; }, {});

    // Incremment data count if such data already present in appiumCommandUsage
    const isDuplicateData = appiumCommandUsage[tag].params.some((p) => {
      if (fields.every(f => p.data[f] === dataToTrack[f])) {
        p.count += 1;
        return true;
      }
      return false;
    });

    // Otherwise push it into appiumCommandUsage
    if (!isDuplicateData) {
      appiumCommandUsage[tag].params.push({ count: 1, data: dataToTrack });
    }

    constants.global_registry[railsSessionId].appiumCommandUsage = appiumCommandUsage;
  } else {
    HubLogger.miscLogger('trackAppiumCommandData', 'Rails Session Id is undefined', LL.DEBUG);
  }
};

module.exports = {
  countSeleniumRequests,
  countAppiumCommandUsage,
  trackAppiumCommandData,
};
