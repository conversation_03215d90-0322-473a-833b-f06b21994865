'use strict';

const allowedOrigins = ['bsstag.com', 'browserstack.com'];

// Returns true if request is from allowedOrigins
const isReqFromBrowserstack = (requestHeaders) => {
  let parsedHostName;
  // For same origin requests origin attribute is null
  try {
    const origin = requestHeaders && requestHeaders.origin;

    if (!origin) return false;

    const urlObject = new URL(origin);
    parsedHostName = urlObject.hostname;
  } catch (error) {
    // Handle the error gracefully due to an invalid url
    return false;
  }
  return allowedOrigins.some(domain => parsedHostName.includes(domain));
};

module.exports = isReqFromBrowserstack;
