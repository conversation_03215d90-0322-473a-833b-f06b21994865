var fs = require('fs');
var ConfigReader = function(defaultConfigFileLocation, ignoreEmpty=false) {
    this.defaultConfigFileLocation = defaultConfigFileLocation;
    this.config = this.readJSONFile(defaultConfigFileLocation, ignoreEmpty);
};

ConfigReader.prototype = {
    merge: function(modificationsFileLocation){
        this.config = this.mergeConfig(this.config,JSON.parse(fs.readFileSync(modificationsFileLocation, "utf-8")));
        return this;
    },
    getConfig: function(){
        return this.config;
    },
    mergeConfig: function(config, modifications){
      for (var ele in modifications){
        try {
          if (modifications[ele].constructor == Object){
            config[ele] = this.mergeConfig(config[ele], modifications[ele]);
          } else {
            config[ele] = modifications[ele];
          }
        } catch (err){
          config[ele] = modifications[ele];
        }
      }
      return config;
    },
    readJSONFile: function(fileName, ignoreEmpty=false) {
        try {
            return JSON.parse(fs.readFileSync(fileName, "utf-8"));
        } catch(err) {
            if(ignoreEmpty) return {};
            throw(err);
        }
    }
}

exports.ConfigReader = ConfigReader;
