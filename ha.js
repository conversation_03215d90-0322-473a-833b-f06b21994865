'use strict';

const constants = require('./constants');
const logger = require('./logger').customLogger;
const redisClient = require('./redisUtils').redisClient;
const helper = require('./helper');
const { incrementHootHootStat } = require('./helpers/instrumentation');

const TAG = 'HA';

const setData = async (key, data, callback) => {
  if (!key || !data) {
    return;
  }

  data = JSON.stringify(data);

  const isSetDataOperationSuccessful = await _setData(key, data, callback);

  if (!isSetDataOperationSuccessful) {
    return;
  }

  const ifLengthThresholdBreached = key.length > constants.SET_DATA_THRESHOLD.key || data.length > constants.SET_DATA_THRESHOLD.value;
  if (ifLengthThresholdBreached) {
    logger.INFO(`{key: ${key}, value_lenght: ${data.length}}`, { TAG, log: { uuid: key, kind: 'SET_DATA_THRESHOLD_BREACH' } });
    if (key.length > constants.SET_DATA_THRESHOLD.key) {
      incrementHootHootStat('redisCommandExceededCount', `setData_key`);
    }
    if (data.length > constants.SET_DATA_THRESHOLD.value) {
      incrementHootHootStat('redisCommandExceededCount', `setData_value`);
    }
  }
};

const _setData = async (key, data, callback) => {
  let writeRes;
  let errorFaced;
  let elapsedTime = 0, success = false;
  try {
    const jsonStringifiedData = data;
    const isAlreadyStopped = await redisClient.get(`alreadyStopped_${key}`);
    if (!isAlreadyStopped) {
      const current = Date.now();
      writeRes = await redisClient.writeSingletonKeyObject([
        constants.shouldPersistKeyObjectFlagPrefix,
        key,
        jsonStringifiedData,
      ]);
      elapsedTime = Date.now() - current;
      success = true;
    } else {
      logger.INFO(`Not setting redisKey as alreadyStopped. Key: ${key}`);
    }
  } catch (err) {
    errorFaced = err;
    logger.ERROR(`Error setting key: ${key} Error:${err}`);
  } finally {
    if (callback) {
      callback(errorFaced, writeRes);
    }
    helper.instrumentRedisCommand('setData', elapsedTime);
  }
  return success;
};

const getData = async (key, callback) => {
  let data;
  let errFaced;
  let elapsedTime = 0;
  try {
    const current = Date.now();
    data = await redisClient.get(`HA_${key}`);
    elapsedTime = Date.now() - current;
    try {
      data = JSON.parse(data);
    } catch (jsonParsingErr) {
      logger.ERROR(`JSON Error getting key: ${key} Error: ${jsonParsingErr}`);
    }
  } catch (err) {
    errFaced = err;
    logger.ERROR(`Error getting key: ${key} Error: ${err}`);
  } finally {
    if (callback) {
      callback(errFaced, data);
    }
    helper.instrumentRedisCommand('getData', elapsedTime);
  }
};

const deleteData = async (key, callback) => {
  let data;
  let errMsg;
  let elapsedTime = 0;
  try {
    const current = Date.now();
    data = await redisClient.deleteSingletonKeyObject([
      constants.shouldPersistKeyObjectFlagPrefix,
      key,
    ]);
    elapsedTime = Date.now() - current;
  } catch (err) {
    errMsg = err;
    logger.ERROR(`Error deleting key: ${key} Error: ${err}`);
  } finally {
    if (callback) {
      callback(errMsg, data);
    }
    helper.instrumentRedisCommand('deleteData', elapsedTime);
  }
};

const incrementCounter = async (key, callback) => {
  let data;
  let errFaced;
  try {
    const current = Date.now();
    data = await redisClient.incr([key]);
    const elapsedTime = Date.now() - current;
    helper.instrumentRedisCommand('incrementCounter', elapsedTime);
  } catch (err) {
    logger.ERROR(`Error incrementing key: ${key} Error: ${err}`);
    errFaced = err;
  } finally {
    if (callback) {
      callback(errFaced, data);
    }
  }
};

const trackNonZeroStatusError = async (key, hashedId, callback) => {
  let data;
  let errorRes;
  try {
    data = await redisClient.sadd([key, hashedId]);
  } catch (err) {
    errorRes = err;
    logger.ERROR(`Error adding set member key: ${key} member: ${hashedId} Error: ${err}`);
  } finally {
    if (callback) {
      callback(errorRes, data);
    }
  }
};

module.exports = {
  getData,
  setData,
  incrementCounter,
  trackNonZeroStatusError,
  deleteData,
};
