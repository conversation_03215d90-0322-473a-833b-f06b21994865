'use strict';

/* eslint-disable no-console */

const cluster = require('cluster');
const fs = require('fs');
const path = require('path');

const env = process.argv[2];
const basePath = process.env.CODEBASEPATH;
const vm = process.env.VM;
const isTestingEnv = process.env.HUB_ENV === 'testing';
const isProductionEnv = !isTestingEnv && env && env === 'Production';

const numCPUs = (Number.isInteger(process.argv[4]) && process.argv[4]) || 3;
const workerFileName = process.argv[3] || 'hub.js';

const vmPath = vm === 'mac' ? basePath : path.join(__dirname, '/');
const codeDir = isProductionEnv && (vm !== 'k8s' && vm !== 'mac') ? '/home/<USER>/SeleniumHub/current/' : vmPath;
// eslint-disable-next-line import/no-dynamic-require
const startFilePath = path.join(codeDir, 'tmp/restart.txt');
const configFilePath = path.join(codeDir, 'conf.json');
const workerFilePath = path.join(codeDir, workerFileName);
const workerHubRailsPipelineManagerPath = path.join(codeDir, 'railsRequests/workerRailsPipelineManager');
const clusterUtilFilePath = path.join(codeDir, 'clusterUtil.js');

const stopFilePath = path.join(codeDir, 'tmp/stop.txt');
let stopCalled = false;
let aliveWorkerCount = 0;

// eslint-disable-next-line import/no-dynamic-require
const util = require(clusterUtilFilePath);

const oldWorkers = [];
const timeouts = {};

const forceKill = (worker) => {
  if (!worker.isDead()) {
    console.log(`Worker ${worker.process.pid} is ${worker.state}, Killing it`);
    worker.kill('SIGUSR2');
  }
};

const checkIfAlive = (worker, retries) => {
  retries = retries || 0;

  if (!worker.isDead() && oldWorkers.length > 0) {
    if (worker.state !== 'listening' && retries < 10) {
      setTimeout(checkIfAlive, 2 * 1000, worker, retries + 1);
    } else {
      const oldWorker = oldWorkers.shift();
      // Force kill worker after 3 hours
      timeouts[oldWorker.process.pid] = setTimeout(forceKill, 3 * 60 * 60 * 1000, oldWorker);
      oldWorker.send('keep-alive-shutdown');
      oldWorker.disconnect();
    }
  }
};

const reload = () => {
  if (util.isSafeToStartProcess(configFilePath)) {
    Object.keys(cluster.workers).forEach(key => oldWorkers.push(cluster.workers[key]));
    for (let i = 0; i < numCPUs; i += 1) {
      const newWorker = cluster.fork();
      // After 2 seconds check if new workers are still up and running.
      setTimeout(checkIfAlive, 2 * 1000, newWorker);
    }
  } else {
    util.sendAlerts('Hub Deploy failed', `Retaining old workers since newly spawned workers died due to malformed ${configFilePath} at ${new Date()}`);
  }
};

const stop = () => {
  stopCalled = true;
  aliveWorkerCount = Object.keys(cluster.workers).length;
  Object.keys(cluster.workers).forEach(key => cluster.workers[key].disconnect());
};

if (!isTestingEnv) {
  if (cluster.isMaster) {
    reload();

    cluster.on('exit', (worker, code, signal) => {
      console.log(`[${new Date()}] worker ${worker.process.pid} died with code: ${code} signal: ${signal}`);
      if (!worker.exitedAfterDisconnect && signal !== 'SIGUSR2') {
        if (oldWorkers.length === 0) {
          if (util.isSafeToStartProcess(configFilePath)) {
            cluster.fork();
          }
        } else {
          const oldWorker = oldWorkers.shift();
          console.log(`Retaining old worker ${oldWorker.process.pid} since newly spawned worker ${worker.process.pid} died`);
          util.sendAlerts('Hub Deploy failed', `Retaining old worker ${oldWorker.process.pid} since newly spawned worker ${worker.process.pid} died with code: ${code} and signal: ${signal} at ${new Date()}`);
        }
      } else if (worker.exitedAfterDisconnect) {
        aliveWorkerCount -= 1;
        clearTimeout(timeouts[worker.process.pid]);
        delete timeouts[worker.process.pid];
        console.log(`Checking for exit. code = ${code}, stopCalled = ${stopCalled}, aliveWorkerCount = ${aliveWorkerCount}, clusterWorkerCount = ${Object.keys(cluster.workers).length}`);
        if (stopCalled && Object.keys(cluster.workers).length < 1 && aliveWorkerCount < 1) {
          console.log(`Exiting Master Process ${process.pid}`);
          process.exit(0);
        }
        return;
      }

      const onlineWorkers = Object.values(cluster.workers).reduce((c, w) => c + +!w.isDead(), 0);
      if (onlineWorkers < numCPUs) {
        const hubStatus = onlineWorkers ? `only ${Object.keys(cluster.workers).length} worker(s) are online` : 'No worker is online';
        util.sendAlerts('Unexpected Behaviour: Worker died', `Worker ${worker.process.pid} died with code: ${code} and signal: ${signal} at ${new Date()} ${hubStatus}`);
      }
    });

    fs.watchFile(startFilePath, () => {
      if (!isTestingEnv) {
        console.log(`[${new Date()}] RESTARTING WORKERS`);
      }
      reload();
    });

    if (fs.existsSync(stopFilePath)) {
      fs.watchFile(stopFilePath, () => {
        console.log(`[${new Date()}] Stopping worker`);
        stop();
      });
    }
  } else {
    // eslint-disable-next-line global-require, import/no-dynamic-require
    const worker = require(workerFilePath);
    // eslint-disable-next-line global-require, import/no-dynamic-require
    const workerHubRailsPipelineManager = require(workerHubRailsPipelineManagerPath);
    worker.setWorkerId((+cluster.worker.id || 0) % numCPUs);
    cluster.worker.on('disconnect', () => {
      const server = worker.server;
      server.close(() => {
        console.log(`[${new Date()}] Server closed ${process.pid}`);
        workerHubRailsPipelineManager.startPollingAndExit();
      });
    });

    process.on('message', (msg) => {
      if (msg === 'keep-alive-shutdown') {
        worker.setShouldDieKeepAliveHeader();
        worker.closeWorkerPool();
      }
    });
  }
}

module.exports = {
  checkIfAlive,
  oldWorkers,
  timeouts,
  forceKill,
  reload,
  util,
  cluster,
  stop
};
