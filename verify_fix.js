// Verify that our DeviceLogHandler fix works correctly
// This simulates the exact test case that was failing

const sinon = require('sinon');

// Mock all the dependencies
const mockBridge = {
  sendResponse: sinon.stub()
};

const mockRequestlib = {
  call: sinon.stub(),
  appendBStackHostHeader: sinon.stub().returns({ Host: 'test-terminal' })
};

const mockHubLogger = {
  miscLogger: sinon.stub(),
  exceptionLogger: sinon.stub()
};

const mockConstants = {
  global_registry: {},
  LOG_LEVEL: { DEBUG: 'debug', WARN: 'warn' },
  CHUNKED_HEADER: { 'Content-Type': 'application/json; charset=utf-8', 'Transfer-Encoding': 'chunked', 'cache-control': 'no-cache' }
};

const mockHa = {
  setData: sinon.stub()
};

const mockPubSub = {
  publish: sinon.stub()
};

// Mock the require calls
const Module = require('module');
const originalRequire = Module.prototype.require;

Module.prototype.require = function(id) {
  switch (id) {
    case '../../../bridge':
      return mockBridge;
    case '../../../lib/request':
      return mockRequestlib;
    case '../../../log':
      return mockHubLogger;
    case '../../../constants':
      return mockConstants;
    case '../../../ha':
      return mockHa;
    case '../../../pubSub':
      return mockPubSub;
    default:
      return originalRequire.apply(this, arguments);
  }
};

// Now require the actual DeviceLogHandler
const { DeviceLogHandler } = require('/Users/<USER>/SeleniumHub/controllers/seleniumCommand/handlers/DeviceLogHandler');

// Test setup
const mockSessionKeyObj = {
  rails_session_id: 'test-session-123',
  device: 'test-device',
  deviceLogs: 'true',
  idle_timeout: 30,
  rproxyHost: 'test-host',
  name: 'test-terminal',
  debugSession: false,
};

const mockRequestStateObj = {
  data: null,
  output: null,
};

// Test case: platform returns invalid JSON with 200 status
const platformResponse = {
  statusCode: 200,
  data: 'invalid json response',
};

// Set up the mock to return the invalid response
mockRequestlib.call.returns(Promise.resolve(platformResponse));

// Track how many times sendResponse is called
let sendResponseCallCount = 0;
mockBridge.sendResponse.callsFake(() => {
  sendResponseCallCount++;
  console.log(`sendResponse called ${sendResponseCallCount} time(s)`);
});

async function runTest() {
  console.log('🧪 Testing DeviceLogHandler with invalid JSON response...\n');
  
  const handler = new DeviceLogHandler(mockSessionKeyObj, {}, {});
  const logData = JSON.stringify({ type: 'logcat' });
  
  try {
    await handler.processCommand(mockRequestStateObj, logData);
    
    console.log(`\n📊 Test Results:`);
    console.log(`- sendResponse called: ${sendResponseCallCount} time(s)`);
    console.log(`- Expected: 1 time`);
    
    if (sendResponseCallCount === 1) {
      console.log('✅ TEST PASSED: sendResponse called exactly once');
      console.log('✅ FIX VERIFIED: No double response issue');
    } else {
      console.log('❌ TEST FAILED: sendResponse called multiple times');
      console.log('❌ FIX NOT WORKING: Double response issue still exists');
    }
    
    // Check the response data
    if (mockRequestStateObj.data) {
      const responseData = JSON.parse(mockRequestStateObj.data);
      console.log(`\n📋 Response Details:`);
      console.log(`- Status: ${responseData.status}`);
      console.log(`- Message: ${responseData.value.message}`);
      
      if (responseData.status === 13 && responseData.value.message.includes('[BROWSERSTACK_INTERNAL_ERROR]')) {
        console.log('✅ Correct error response generated');
      } else {
        console.log('❌ Incorrect response generated');
      }
    }
    
  } catch (error) {
    console.error('❌ Test failed with error:', error);
  }
}

runTest().catch(console.error);
