const logger = require('../logger').customLogger;
const instrumentation = require('../helpers/instrumentation');

// methodNameToMonitor: [[argIndex, maxLength], [argIndex, maxLength]]
const redisMethodsToMonitor = {
  "setex": [[0, 70], [2, 70]],
  "incr": [[0, 40]],
  "sadd": [[0, 40], [1, 50]],
  "set": [[0, 40], [1, 50]],
  "hincrby": [[0, 30], [1, 20]],
  "getset": [[0, 40], [1, 20]],
  "setnx": [[0, 20], [1, 30]],
  "hset": [[0, 30], [1, 20]],
  "rpush": [[0, 35], [1, 50]],
  "lrem": [[0, 30], [1, 20]],
};

const TAG = 'REDIS_METHOD_PROXY_HANDLER';

class RedisMethodProxyHandler {
  constructor(redisClientInstance, originalIORedisInstance) {
    this.redisClientInstance = redisClientInstance;
    this.originalIORedisInstance = originalIORedisInstance;
  }

  get(_, prop, receiver) {
    const originalProp = Reflect.get(this.originalIORedisInstance, prop, receiver);
    return (...args) => {
      let ret;
      try {
        ret = originalProp.apply(this.originalIORedisInstance, args);
      } catch (error) {
        throw error;
      }
      if (redisMethodsToMonitor[prop]) {
        // Move processing to microtask queue to avoid blocking the event loop

        // Passing stack from here to avoid stack trace from being lost
        const stack = new Error().stack.split("\n")[2];
        this.runPostProcessingAsync(prop, args, stack);
      }
      return ret;
    }
  }

  runPostProcessingAsync(prop, args, stack) {
    Promise.resolve().then(() => {
      try {
        for (const [argIndex, maxLength] of redisMethodsToMonitor[prop]) {
          let arg = Array.isArray(args[0]) ? args[0][argIndex] : args[argIndex];
          arg = typeof arg === 'string' ? arg : args.toString();
          if (arg.length > maxLength) {
            instrumentation.incrementHootHootStat('redisCommandExceededCount', `${prop}_${argIndex}`);
            logger.INFO(`{"fn":"${prop}","len":"${arg.length}","st":"${stack}", "idx": "${argIndex}"}`, { log: { kind: 'REDIS_THRESHOLD_BREACH_WARNING' } });
          }
        }
      } catch (e) {
        logger.ERROR(`{"error":"${e}"}`, { TAG, log: { kind: 'REDIS_THRESHOLD_BREACH_ERROR' } });
      }
    });
  }
}

module.exports = RedisMethodProxyHandler;
