'use strict';

const redis = require('ioredis');
const { inspect } = require('util');
const logger = require('../logger').customLogger;
const fs = require('fs');
const AlertManager = require('../alertManager');
const helper = require('../helper');
const constants = require('../constants');
const RedisMethodProxyHandler = require('./redisWrapper');
const hoothootUse = require('hoothoot')('use');

class RedisClient {
  constructor() {
    this._redisClient = null;
    this._redisClientSecond = null;
    this._redisClientAi = null;
    this.luaScriptsInfo = {
      registerRailsRequest: {
        numberOfKeys: 2,
        scriptPath: 'registerRailsRequest.lua',
      },
      unregisterRailsRequest: {
        numberOfKeys: 1,
        scriptPath: 'unregisterRailsRequest.lua',
      },
      writeSingletonKeyObject: {
        numberOfKeys: 3,
        scriptPath: 'writeSingletonKeyObject.lua',
      },
      deleteSingletonKeyObject: {
        numberOfKeys: 2,
        scriptPath: 'deleteSingletonKeyObject.lua',
      },
    };
    this.alertManager = new AlertManager();
    this.setupClients();
  }

  setupClients() {
    this._redisClient = RedisClient.createClient(constants.redisNewConfig);
    this._redisClientAi = RedisClient.createClient(constants.redisConfigAi);
    if (constants.multiRedisClusterSetup) {
      this._redisClientSecond = RedisClient.createClient(constants.redisConfigSecond);
    }
    this.initializeCustomLuaScripts();
  }

  static createClient(redisConf) {
    const client = redis.createClient(redisConf);
    client.on('error', (e) => {
      logger.ERROR(`Redis Global error: ${inspect(e, { showHidden: false, depth: 1 })} - ${JSON.stringify(redisConf)}`);
      hoothootUse.emit('automate_miscellaneous_errors', 1, {
        event_type: 'redis_connection_error_n', process_id: process.pid, product: 'automate', hub_region: constants.region, machine: constants.osHostName
      });
    });
    client.on('close', () => {
      logger.ERROR(`Redis client connection closed - ${JSON.stringify(redisConf)}`);
      const kind = 'redis-connection-closed';
      hoothootUse.emit('automate_miscellaneous_errors', 1, {
        event_type: 'redis_connection_closed_n', process_id: process.pid, product: 'automate', hub_region: constants.region, machine: constants.osHostName
      });
      helper.PingZombie({
        kind,
        data: `client pid ${process.pid} - ${JSON.stringify(redisConf)}`,
        region: constants.region,
        machine: constants.osHostName,
      });
    });
    return client;
  }

  initializeCustomLuaScripts() {
    Object.keys(this.luaScriptsInfo).forEach((scriptName) => {
      let luaScriptContent = '';
      try {
        const path = constants.ROOT_PATH;
        luaScriptContent = fs.readFileSync(`${path}/script/lua_scripts/${this.luaScriptsInfo[scriptName].scriptPath}`);
      } catch (readScriptError) {
        const subject = `SeleniumHub: Global Exception: initializeLuaScripts: ${readScriptError.toString()}`;
        const message = `GLOBAL EXCEPTION IN HELPER.JS PROCESS: ${inspect(readScriptError, { depth: 1, showHidden: false })}`;
        this.alertManager.sendAlerts(subject, message);
        process.exit(1);
      }

      this._redisClient.defineCommand(scriptName, {
        numberOfKeys: this.luaScriptsInfo[scriptName].numberOfKeys,
        lua: luaScriptContent,
      });
      if (constants.multiRedisClusterSetup) {
        this._redisClientSecond.defineCommand(scriptName, {
          numberOfKeys: this.luaScriptsInfo[scriptName].numberOfKeys,
          lua: luaScriptContent,
        });
      }
    });
  }

  get redisClient() {
    // eslint-disable-next-line no-undef
    return new globalThis.Proxy(this._redisClient, new RedisMethodProxyHandler(this, this._redisClient));
  }

  get redisClientSecond() {
    // Second cluster in case of multi cluster setup, else use the same client
    if (constants.multiRedisClusterSetup) {
      return this._redisClientSecond;
    } else {
      return this._redisClient;
    }
  }

  get redisClientAi() {
    return this._redisClientAi;
  }
}

const redisClient = new RedisClient();

module.exports = redisClient;
