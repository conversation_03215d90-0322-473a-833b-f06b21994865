var urlModule = require('url'),
    constants = require('./constants'),
    LL = constants.LOG_LEVEL,
    requestlib = require('./lib/request'),
    HubLogger = require('./log'),
    helper = require('./helper'),
    pubSub = require('./pubSub'),
    { mapRequestToAppropriateDialect } = require('./utils/commandMapper');

const querystring = require('querystring');

var checkURLStatus = function(data_json, clientSessionID, hostname, tunnel, certs) {
  certs = certs || false;
  tunnel = tunnel || false;
  if(typeof(data_json.url) == "undefined") return;
  var options = {'url': data_json.url, 'tunnel': tunnel},
    userUrl;

  try {
    userUrl = urlModule.parse(options["url"]);
  } catch(e) {
    HubLogger.exceptionLogger("Secondary-State-Error", "Error while parsing URL in checkURLStatus: url : " + options["url"] + " Exception: " + e);
    return;
  }

  var sessionObj = constants.global_registry[clientSessionID];
  var terminal_server_port = ((sessionObj.os || "").match(/mac/) ? 45671 : 4567);
  var hasBasicAuth = userUrl.auth != null,
    urlAuthUser = (hasBasicAuth) ? encodeURIComponent(userUrl.auth.split(':')[0]) : '',
    urlAuthPass = (hasBasicAuth) ? encodeURIComponent(userUrl.auth.split(':').slice(1).join(':')) : '';

  requestlib.call({
    hostname: sessionObj.rproxyHost,
    port: terminal_server_port,
    path: "/check_url_status?key=@ut0m@t3&logging=true&url="+encodeURIComponent(userUrl.href)+(hasBasicAuth ? ("&bauth_username="+urlAuthUser) : "")+(hasBasicAuth ? ("&bauth_password="+urlAuthPass) : "")+"&tunnel="+tunnel.toString()+"&certs="+certs.toString()+"&sessionId="+clientSessionID.toString()+"&hubPort="+constants.SERVER_PORT + "&proxy_type=privoxy",
    headers: requestlib.appendBStackHostHeader(hostname),
  }).then((get_response) => {
    var get_data = get_response.data;
    try {
      var parsed_data = JSON.parse(get_data);
      if(parsed_data.status){
        modifySecondaryStateForTest(parsed_data, clientSessionID, options, sessionObj);
      }else{
        if(clientSessionID && constants.global_registry[clientSessionID]){
          constants.global_registry[clientSessionID]["secondaryStateOptions"] = options;
        }
      }
    }
    catch(e) {
      HubLogger.exceptionLogger("Secondary-State", "SessionId: " + clientSessionID +",Not a JSON response from node: " + get_data + "Error: " + e.toString(), hostname, data_json.url);
    }
  }).catch((e) => {
    switch (e.type) {
      case 'RequestError':
        HubLogger.exceptionLogger("URL-Verification", "Error while receiving response to hostname: " + hostname, "Error message: " + e.toString());
        break;
      case 'ResponseError':
        HubLogger.exceptionLogger("Secondary-State-Error", "Response error while checkURLStatus " + e);
        break;
      case 'TimeoutError':
        HubLogger.exceptionLogger("Secondary-State-Error", "Timeout error while checkURLStatus " + e);
        break;
      default:
        HubLogger.exceptionLogger("Secondary-State-Error", "Default error while checkURLStatus " + e);
        throw e;
    }
  });
};
exports.checkURLStatus = checkURLStatus;

var modifySecondaryStateForTest = function(parsed_json, rails_session_id, options, sessionObj){
  if(!(rails_session_id && parsed_json && constants.global_registry[rails_session_id])){
    return;
  }

  if(!options){
    return;
  }

  var status = helper.getString(parsed_json.status);
  var modifySecondaryStateFlag = false;

  switch(status){
    case "401":
      constants.global_registry[rails_session_id].secondary_state = constants.secondary_states["401"][parsed_json.header['www-authenticate']] || "basic-auth";
      if(options.isBauth){
        if(parsed_json.header['www-authenticate'] !== "NTLM")
          constants.global_registry[rails_session_id].secondary_state += "-failed";
      }else{
        constants.global_registry[rails_session_id].secondary_state += sessionObj.browser && sessionObj.browser.toLowerCase() === "safari" ? "-required-safari" : "-required";
      }
      modifySecondaryStateFlag = true;
      break;
    case "502":
      //constants.global_registry[rails_session_id].secondary_state = "tunnel-down";
      modifySecondaryStateFlag= true;
      break;
    case "503":
      if(constants.global_registry[rails_session_id].tunnel) {
      //  constants.global_registry[rails_session_id].secondary_state = "tunnel-down";
      }
      else {
        constants.global_registry[rails_session_id].secondary_state = "tunnel-required-maybe";
      }
      modifySecondaryStateFlag = true;
      break;
    default:
      var temp = constants.secondary_states[status] || constants.secondary_states.SUCCESS;
      if (!options.tunnel && (!constants.global_registry[rails_session_id].secondary_state || temp != constants.secondary_states.SUCCESS)){
        constants.global_registry[rails_session_id].secondary_state = temp;
        modifySecondaryStateFlag = true;
      }
      if (helper.getString(parsed_json.p45691) == "false") {
        constants.global_registry[rails_session_id].secondary_state = "45691 down ";
        modifySecondaryStateFlag = true;
      }
  }
  if (parsed_json.p45691 == "up" || parsed_json.p45696 == "up") {
    switch(parseInt(status).toString()) {
      case "1":
        constants.global_registry[rails_session_id].secondary_state = "socket-error";
        break;
      case "2":
        constants.global_registry[rails_session_id].secondary_state = "host-unreachable";
        break;
      case "3":
        constants.global_registry[rails_session_id].secondary_state = "connection-refused";
        break;
      case "4":
        constants.global_registry[rails_session_id].secondary_state = "timeout";
        break;
      case "5":
        constants.global_registry[rails_session_id].secondary_state = "invalid-uri";
        break;
      case "6":
        constants.global_registry[rails_session_id].secondary_state = "http-error";
        break;
      case "7":
        constants.global_registry[rails_session_id].secondary_state = "max-redirects";
        break;
      case "8":
        var msg = (parsed_json.message || "").toLowerCase();
        if(msg.match(/ssl_connect/i)){
          constants.global_registry[rails_session_id].secondary_state = "ssl_connect";
        }
        else {
          constants.global_registry[rails_session_id].secondary_state = msg.split(' ').join('-');
        }
        break;
      default:
        if (!modifySecondaryStateFlag) {
          //constants.global_registry[rails_session_id].secondary_state = "unknown-error";
        }
    }
  } else {
    if(constants.global_registry[rails_session_id].tunnel && !modifySecondaryStateFlag)
        constants.global_registry[rails_session_id].secondary_state = "proxy-down-on-terminal";
  }
  pubSub.publish(constants.updateKeyObject, {
    session: rails_session_id,
    changed: {
      secondary_state: constants.global_registry[rails_session_id].secondary_state,
      check_url: constants.global_registry[rails_session_id].secondary_state
    }
  });
  helper.pushToCLS('modifySecondaryState', {
    session_id: rails_session_id,
    secondary_state: constants.global_registry[rails_session_id].secondary_state,
    checkURLMessage: parsed_json
  });
};
exports.modifySecondaryStateForTest = modifySecondaryStateForTest;

var checkActiveElementOnTerminal = function(host_params, success_callback, error_callback){
  HubLogger.miscLogger("checkActiveElementOnTerminal", "sessionId: " + host_params.rails_session_id + " status: in function" + host_params.key, LL.INFO);
  var request_json = {
    "script":"return document.activeElement",
    "args": []
  };
  var options = {
    hostname: host_params.rproxyHost,
    port: host_params.port,
    path: "/wd/hub/session/"+host_params.key+"/execute",
    method: "POST",
    body: JSON.stringify(request_json),
    timeout: constants.NODE_DIED_IN,
    headers: requestlib.appendBStackHostHeader(host_params.name),
    recordJarTime: true
  };
  
  requestlib.call(options).then((res) => {
    helper.addToJarTime(host_params.rails_session_id, res);

    var data = res.data;
    HubLogger.miscLogger("checkActiveElementOnTerminal", "sessionId: " + host_params.rails_session_id + " status: success, data: " + data, LL.INFO);
    if(constants.global_registry[host_params.rails_session_id]){
      constants.global_registry[host_params.rails_session_id].isMetaPageLoadTimeout = true;
    }
    data = JSON.parse(res.data);
    if(data["value"] == null || typeof(data["value"]) == "undefined")
      error_callback(data, res);
    else
      success_callback(data, res);
  }).catch((err) => {
    HubLogger.exceptionLogger("checkActiveElementOnTerminal", "sessionId: " + host_params.rails_session_id + " status: request error" + err);
    error_callback(err);
  });
  return;
};
exports.checkActiveElementOnTerminal = checkActiveElementOnTerminal;

// Refactored to services/selenium/browserActions.js
var waitForPageLoad = function(success_callback, error_callback, host_params, maxWait) {
  maxWait = maxWait || 10000;
  var startTime = (new Date).getTime();
  HubLogger.miscLogger("waitForPageLoad", "sessionId: " + host_params.rails_session_id + " waiting for pageLoad to complete on terminal", LL.INFO);
  if(maxWait < 0) {
    HubLogger.miscLogger("waitForPageLoad", "sessionId: " + host_params.rails_session_id + " timedout", LL.INFO);
    error_callback();
  } else {
    HubLogger.miscLogger("waitForPageLoad", "sessionId: " + host_params.rails_session_id + " checking page load state", LL.INFO);
    checkPageLoadOnTerminal(host_params, success_callback, function() {
      HubLogger.miscLogger("waitForPageLoad", "sessionId: " + host_params.rails_session_id + " Page load is not yet completed. Retrying after " + constants.checkPageLoadWait + " ms", LL.INFO);
      setTimeout(function() {
        waitForPageLoad(success_callback, error_callback, host_params, ( maxWait - ( (new Date).getTime() - startTime ) ));
      }, constants.checkPageLoadWait);
    });
  }
};
exports.waitForPageLoad = waitForPageLoad;

// Refactored to services/selenium/browserActions.js
var checkPageLoadOnTerminal = function(host_params, success_callback, error_callback, checkLoad, checkTitleAndReadyState){
  HubLogger.miscLogger("checkPageLoadOnTerminal", "sessionId: " + host_params.rails_session_id + " status: in function " + host_params.key, LL.DEBUG, host_params.debugSession);
  var scriptToExecute = checkTitleAndReadyState ? 'return [document.readyState, document.title]' : 'return document.readyState';
  HubLogger.miscLogger("checkPageLoadOnTerminal", "sessionId: " + host_params.rails_session_id + " scriptToExecute " + scriptToExecute, LL.DEBUG, host_params.debugSession);
  var request_json = {
    "script":scriptToExecute,
    "args": []
  };
  var body = JSON.stringify(request_json);
  let headers = {
    "accept": "application/json",
    "content-type": "application/json; charset=utf-8",
    "content-length": body.length,
    "accept-encoding": "gzip;q=1.0,deflate;q=0.6,identity;q=0.3",
  };

  headers = helper.updateHeadersForSelenium4Jars(host_params, headers);
  requestlib.appendBStackHostHeader(host_params.name, headers);

  var endpoint = "/execute";

  if(host_params.dialect == "W3C") {
    endpoint = "/execute/sync";
  }

  var options = {
    hostname: host_params.rproxyHost,
    port: host_params.port,
    path: "/wd/hub/session/" + host_params.key + endpoint,
    method: "POST",
    body: body,
    headers: headers,
    timeout: isNaN(parseInt(host_params.autoWait)) ? 10000 : host_params.autoWait,
    recordJarTime: true
  };
  
  requestlib.call(options).then((res) => {
    helper.addToJarTime(host_params.rails_session_id, res);

    if(constants.global_registry[host_params.rails_session_id]){
      constants.global_registry[host_params.rails_session_id].isMetaPageLoadTimeout = true;
    }
    var data = JSON.parse(res.data);
    if (data && data.value && typeof(data.value) != 'string' && Array.isArray(data.value)) {
      var titleData = data.value.pop(1);
      data.value = data.value.join();
      if (checkTitleAndReadyState && exports.isTitleAmongIssue(titleData)) {
        HubLogger.miscLogger("checkPageLoadOnTerminal", "SessionId: " + host_params.rails_session_id + "Got Title " + titleData, LL.INFO);
        host_params.udpKeys.local_5xx = 1;
        pubSub.publish(constants.updateKeyObject, {
          session: host_params.rails_session_id,
          changed: {
            udpKeys: host_params.udpKeys
          }
        });
      }
    }
    HubLogger.miscLogger("checkPageLoadOnTerminal", "sessionId: " + host_params.rails_session_id + " status: success " + JSON.stringify(data["value"] || "").substring(0,20), LL.DEBUG);
    if(data["value"] == "complete" || data["value"] == "interactive")
      success_callback(data, res);
    else {
      HubLogger.miscLogger("checkPageLoadOnTerminal", "sessionId: " + host_params.rails_session_id + " status: error" + JSON.stringify(data["value"] && data["value"]["message"] || ""), LL.INFO);
      if(typeof(checkLoad) != "undefined" && data["value"] != "loading")
        success_callback(data, res);
      else
        error_callback(data, res);
    }
  }).catch((err) => {
    HubLogger.exceptionLogger("checkPageLoadOnTerminal", "sessionId: " + host_params.rails_session_id + " status: request error" + err);
    error_callback(err);
  });
};
exports.checkPageLoadOnTerminal = checkPageLoadOnTerminal;

// Refactored to services/selenium/browserActions.js
exports.isTitleAmongIssue = function(title) {
  if(title && typeof(title) === 'string') {
    if(title.match(/Internal Server Error|502 Proxy Error|Problem loading page/i)) {
      return true;
    }
  }
  return false;
};

var openURLOnTimeout = function(host_params, success_callback, error_callback){
  HubLogger.miscLogger("openURLOnTimeout", "sessionId: " + host_params.rails_session_id + " status: in function" + host_params.key, LL.INFO);
  var request_json = {
    "url":"about:blank"
  };

  var options = {
    hostname: host_params.rproxyHost,
    port: host_params.port,
    path: "/wd/hub/session/"+host_params.key+"/url",
    method: "POST",
    body: JSON.stringify(request_json),
    timeout: constants.BS_TIMEOUT,
    headers: requestlib.appendBStackHostHeader(host_params.name),
  };
  requestlib.call(options).then((res) => {
    HubLogger.miscLogger("openURLOnTimeout", "sessionId: " + host_params.rails_session_id + " status: success", LL.INFO);
    var data = JSON.parse(res.data);
    success_callback(data, res);
  }).catch((err) => {
    HubLogger.exceptionLogger("openURLOnTimeout", "sessionId: " + host_params.rails_session_id + " status: request error" + err);
    error_callback(err);
  });
};
exports.openURLOnTimeout = openURLOnTimeout;

var setPageLoadTimeoutOnTerminal = function(host_params, callback, timeout){
  HubLogger.miscLogger("setPageLoadTimeoutOnTerminal", "sessionId: " + host_params.rails_session_id + " status: in function", LL.DEBUG);
  //Setting default payload as that of W3C
  let request_json = {
    "pageLoad": timeout || constants.PAGE_LOAD_TIMEOUT
  };
  if(host_params.dialect === 'OSS'){
    request_json = {
      "type":"page load",
      "ms": timeout || constants.PAGE_LOAD_TIMEOUT
    };
  }

  let options = {
    hostname: host_params.rproxyHost,
    port: host_params.port,
    path: "/wd/hub/session/"+host_params.key+"/timeouts",
    method: "POST",
    body: JSON.stringify(request_json),
    timeout: constants.BS_TIMEOUT,
    headers: requestlib.appendBStackHostHeader(host_params.name),
    mockPerformanceJarEndpoint: host_params.mockPerformanceJarEndpoint,
  };
  let headers = {};
  requestlib.appendBStackHostHeader(host_params.name, headers);
  headers = helper.updateHeadersForSelenium4Jars(host_params, headers);
  if (Object.keys(headers).length > 0) {
    options.headers = headers;
  }

  const mappedOptions = mapRequestToAppropriateDialect(host_params, options);
  requestlib.call(mappedOptions)
  .then((timeout_response) => {
    HubLogger.miscLogger("setPageLoadTimeoutOnTerminal", "sessionId: " + host_params.rails_session_id + " status: success, data: " + timeout_response.data, LL.DEBUG);
    if(timeout_response && timeout_response.statusCode != 200 || (timeout_response.data && timeout_response.data.value && JSON.stringify(timeout_response.data.value).includes("error"))){
      const kind = 'automate-set-pageload-error';
      const messageData = (timeout_response.data && typeof timeout_response.data == 'string') ? timeout_response.data.substring(0,50) : 'Unable to error data';
      helper.PingZombie({
        kind,
        browser: host_params.browser,
        browser_version: host_params.browser_version,
        os: host_params.os,
        user_id: host_params.user_id,
        data: {selenium_version: host_params.selenium_version, message: messageData},
        error: timeout_response.error,
        region: constants.region,
        machine: constants.osHostName,
        session_id: host_params.rails_session_id
      });
    }
    if(constants.global_registry[host_params.rails_session_id]){
      constants.global_registry[host_params.rails_session_id].isMetaPageLoadTimeout = true;
    }
  })
  .catch((err) => {
    HubLogger.exceptionLogger("setPageLoadTimeoutOnTerminal", "sessionId: " + host_params.rails_session_id + " status: error, error " + err);
  })
  .then(callback);
};
exports.setPageLoadTimeoutOnTerminal = setPageLoadTimeoutOnTerminal;

var getLogsAndSave = function(type, host_params, success_callback, error_callback){
  HubLogger.miscLogger("getLogsAndSave", "sessionId: " + host_params.rails_session_id + " status: in function" + host_params.key, LL.INFO);
  var request_json = {
    "type":type
  };

  var options = {
    hostname: host_params.rproxyHost,
    port: host_params.port,
    path: "/wd/hub/session/"+host_params.key+"/log",
    method: "POST",
    body: JSON.stringify(request_json),
    headers: requestlib.appendBStackHostHeader(host_params.name),
  };
  var log_date = helper.getDate();
  var requestLogline = helper.s3LogFormat("REQUEST", log_date, "[" + log_date + "] POST /session/" + host_params.rails_session_id + "/log {\"type\":\""+type+"\"}") + "\r\n";
  requestlib.call(options).then((res) => {
    HubLogger.miscLogger("getLogsAndSave", "sessionId: " + host_params.rails_session_id + " status: success", LL.INFO);
    var data = JSON.parse(res.data);
    if(data.status == 0){
      var logs = [];
      for(var i in data.value){
        logs.push(type + " " + data.value[i].level + " " + data.value[i].timestamp + " " + data.value[i].message);
      }
      var log_data = {
        "state":"success",
        "sessionId":host_params.rails_session_id,
        "hCode":22561426,
        "value":logs,
        "class":"org.openqa.selenium.remote.Response",
        "status":0
      };
      var responseLogLine = helper.s3LogFormat("RESPONSE", helper.getDate(), JSON.stringify(log_data));
      HubLogger.sessionLog(host_params, 'REQUEST_RESPONSE', requestLogline + responseLogLine, true);
    }
    success_callback(data, res);
  }).catch((err) => {
    HubLogger.exceptionLogger("getLogsAndSave", "sessionId: " + host_params.rails_session_id + " status: request error" + err);
    error_callback(err);
  });
};
exports.getLogsAndSave = getLogsAndSave;

const setNetworkProfile = async (keyObject, caps, browserstackParams, callback) => {
  const params = {
    genre: 'selenium',
    automate_session_id: keyObject.rails_session_id,
    device: caps.udid,
    network_wifi: browserstackParams.network_wifi,
    network_airplane_mode: browserstackParams.network_airplane_mode,
  };

  ['network_bw_dwld', 'network_bw_upld', 'network_latency']
    .filter(f => browserstackParams[f])
    .forEach((f) => { params[f] = parseInt(browserstackParams[f], 10); });

  const query = querystring.stringify(params);
  const options = {
    hostname: keyObject.rproxyHost,
    port: 45671,
    path: `/update_network?${query}`,
    headers: requestlib.appendBStackHostHeader(keyObject.name),
  };

  try {
    await requestlib.call(options);
  } catch (err) {
    HubLogger.miscLogger('setNetworkProfile', `Could not set network profile due to ${err}`, LL.INFO);
  } finally {
    callback();
  }
};
exports.setNetworkProfile = setNetworkProfile;
