'use strict';

/* eslint-disable no-console */

const fs = require('fs');
const AlertManager = require('./alertManager');

const alertManager = new AlertManager();

exports.sendAlerts = alertManager.sendAlerts;

exports.isSafeToStartProcess = (configFilePath) => {
  try {
    const configData = fs.readFileSync(configFilePath);
    JSON.parse(configData);
  } catch (err) {
    console.log(`isSafeToStartProcess failed: Config file: ${configFilePath} is not a valid JSON. Error: ${err}`);
    return false;
  }
  return true;
};
