'use strict';

const Promise = require('bluebird');

const requestlib = require('./lib/request');
const constants = require('./constants');
const helper = require('./helper');
const { customLogger: logger } = require('./logger');

class SeleniumClient {
  // hostname -> Selenium/Appium server's Address.
  // port -> Selenium/Appium server's port.
  constructor({
    name: hostname,
    port,
    key: sessionId,
    selenium_version: seleniumVersion,
    rproxyHost,
    dialect,
  }) {
    this.hostname = hostname;
    this.port = port;
    this.rproxyHost = rproxyHost;
    this.sessionId = sessionId;
    this.seleniumVersion = seleniumVersion;
    this.isW3CDialect = (dialect === 'W3C');
  }

  static get WINDOW_SIZE_SCRIPT() {
    return `
      function getWindowSize() {
        var outerWidth = window.outerWidth || document.documentElement.clientWidth;
        var outerHeight = window.outerHeight || document.documentElement.clientHeight;
        var dpi = window.devicePixelRatio || 1;
        return {
          width: outerWidth * dpi,
          height: outerHeight * dpi
        }
      }
      return getWindowSize();
    `;
  }

  static get DEFAULT_HEADERS() {
    return {
      accept: 'application/json',
      'content-type': 'application/json; charset=utf-8',
      'accept-encoding': 'gzip;q=1.0,deflate;q=0.6,identity;q=0.3',
    };
  }

  validateSessionId() {
    if (!this.sessionId) {
      throw new Error('No session Id');
    }
  }

  async makeRequest(method, path, {
    payload, timeout, parseRespose = true, completeJarResponse = false
  } = {}) {
    const body = payload ? Buffer.from(JSON.stringify(payload)) : payload;
    let headers = SeleniumClient.DEFAULT_HEADERS;
    headers = helper.updateHeadersForSelenium4Jars({ selenium_version: this.seleniumVersion }, headers);
    requestlib.appendBStackHostHeader(this.hostname, headers);
    if (body) {
      headers['content-length'] = body.length;
    }
    const reqHash = {
      method,
      path,
      body,
      timeout,
      hostname: this.rproxyHost,
      port: this.port,
      headers,
    };
    const jarResponse = await requestlib.call(reqHash);
    if (completeJarResponse.toString() === 'true') return jarResponse;
    return parseRespose ? JSON.parse(jarResponse.data) : jarResponse.data;
  }

  async sendKeys(keys) {
    this.validateSessionId();
    let sendKeysResponse;
    try {
      sendKeysResponse = await this.makeRequest('POST', `/wd/hub/session/${this.sessionId}/keys`, { payload: { value: keys } });
    } catch (err) {
      logger.log('ERROR', err, { time: helper.getISOString() });
      throw err;
    }
    return sendKeysResponse;
  }

  async executeScript(scriptWithArgsObject, { returnResp = false, parseRespose = true } = {}) {
    this.validateSessionId();
    try {
      let executesScriptUrl = `/wd/hub/session/${this.sessionId}/execute`;
      if (this.isW3CDialect) {
        executesScriptUrl += '/sync';
      }
      const resp = await this.makeRequest('POST', executesScriptUrl, { payload: scriptWithArgsObject, parseRespose });
      if (returnResp) {
        return resp;
      }
      const successMessage = 'executeScript: Successfully executed script';

      if (this.isW3CDialect) {
        const { value } = resp;
        if (value == null) {
          return successMessage;
        }
      } else {
        const { status } = resp;
        if (status === 0) {
          return successMessage;
        }
      }
      throw new Error('executeScript: Error occurred while executing script');
    } catch (err) {
      logger.log('ERROR', `executeScript: ${err}`, { time: helper.getISOString() });
      throw err;
    }
  }

  async executeScriptAsync(scriptWithArgsObject, { returnResp = false, parseRespose = true } = {}) {
    this.validateSessionId();
    try {
      let executesScriptUrl = `/wd/hub/session/${this.sessionId}/execute_async`;
      if (this.isW3CDialect) {
        executesScriptUrl = `/wd/hub/session/${this.sessionId}/execute/async`;
      }
      const resp = await this.makeRequest('POST', executesScriptUrl, { payload: scriptWithArgsObject, parseRespose });
      if (returnResp) {
        return resp;
      }
      const successMessage = 'executeScript: Successfully executed script';

      if (this.isW3CDialect) {
        const { value } = resp;
        if (value == null) {
          return successMessage;
        }
      } else {
        const { status } = resp;
        if (status === 0) {
          return successMessage;
        }
      }
      throw new Error('executeScript: Error occurred while executing script');
    } catch (err) {
      logger.log('ERROR', `executeScript: ${err}`, { time: helper.getISOString() });
      throw err;
    }
  }

  async getTitle() {
    this.validateSessionId();
    const { value } = await this.makeRequest('GET', `/wd/hub/session/${this.sessionId}/title`) || {};
    if (value) {
      return value;
    }
    throw new Error('Could not get page title');
  }

  async getContexts() {
    this.validateSessionId();
    const contexts = await this.makeRequest('GET', `/wd/hub/session/${this.sessionId}/contexts`) || {};
    if (contexts) {
      return contexts;
    }
    throw new Error('Could not get contexts');
  }

  async setContext(context) {
    this.validateSessionId();
    await this.makeRequest('POST', `/wd/hub/session/${this.sessionId}/context`, { payload: { name: context }, timeout: 100000 });
  }

  async resetApp() {
    this.validateSessionId();
    try {
      const resp = await this.makeRequest('POST', `/wd/hub/session/${this.sessionId}/appium/app/reset`, { timeout: 100000 });
      const successMessage = 'resetApp: Successfully resetApp';

      if (this.isW3CDialect) {
        const { value } = resp;
        if (value == null) {
          return successMessage;
        }
      } else {
        const { status } = resp;
        if (status === 0) {
          return successMessage;
        }
      }
      throw new Error('resetApp: Error occurred while resetApp');
    } catch (err) {
      logger.log('ERROR', `resetApp: sessionId: ${this.sessionId}; error: ${err}`, { time: helper.getISOString() });
      throw err;
    }
  }

  async launchApp() {
    this.validateSessionId();
    try {
      const { status } = await this.makeRequest('POST', `/wd/hub/session/${this.sessionId}/appium/app/launch`, { timeout: 100000 });
      if (status === 0) {
        return 'launchApp: Successfully launchApp';
      }
      throw new Error('launchApp: Error occurred while launchApp');
    } catch (err) {
      logger.log('ERROR', `launchApp: sessionId: ${this.sessionId}; error: ${err}`, { time: helper.getISOString() });
      throw err;
    }
  }

  async handleBiometricPopupAndroid(biometricMatch) {
    this.validateSessionId();

    const errors = constants.BIOMETRIC_CUSTOM_EXECUTOR_ERRORS;
    let previousTryError;
    let resourceId;
    if (biometricMatch === 'pass') {
      resourceId = 'android:id/button1';
    } else if (biometricMatch === 'cancel') {
      resourceId = 'android:id/button3';
    } else {
      resourceId = 'android:id/button2';
    }

    for (let tries = 0; tries <= 5; tries += 1) {
      let elementId = '';

      // This block will try to find the element id of the desired button
      try {
        const elementsPath = `/wd/hub/session/${this.sessionId}/elements`;
        const requestPayload = { using: 'id', value: resourceId };

        /* eslint-disable-next-line no-await-in-loop */
        const { value } = await this.makeRequest('POST', elementsPath, { payload: requestPayload });

        if (value.length === 0) {
          previousTryError = errors.popup_absent;
          /* eslint-disable-next-line no-continue */
          continue;
        }
        const { ELEMENT } = value[0];
        elementId = ELEMENT;
      } catch (err) {
        logger.log('ERROR', `handleBiometricPopupAndroid: find: sessionId: ${this.sessionId}; error: ${err}`, { time: helper.getISOString() });
        previousTryError = errors.popup_absent;
        /* eslint-disable-next-line no-await-in-loop */
        await Promise.delay(500);
        /* eslint-disable-next-line no-continue */
        continue;
      }

      // This block will try to click on the above found button
      try {
        const clickPath = `/wd/hub/session/${this.sessionId}/element/${elementId}/click`;

        /* eslint-disable-next-line no-await-in-loop */
        await this.makeRequest('POST', clickPath, { payload: {} });
      } catch (err) {
        logger.log('ERROR', `handleBiometricPopupAndroid: click: sessionId: ${this.sessionId}; error: ${err}`, { time: helper.getISOString() });
        previousTryError = errors.popup_not_clicked;
        /* eslint-disable-next-line no-await-in-loop */
        await Promise.delay(500);
        /* eslint-disable-next-line no-continue */
        continue;
      }
      previousTryError = null;
      return previousTryError;
    }

    const error = previousTryError || errors.unknown;
    return error;
  }

  /**
   * This checks current webapage with the title passed and returns results as per titleShouldMatch
   * If titleShouldBePresent = true, it keeps on checking till it finds title = titleToCheck
   * If titleShouldBePresent = false, it keeps on checking till title != titleToCheck
   * @param {string} titleToCheck
   * @param {boolean} titleShouldMatch
   */
  async checkInsecureWebsite(titleToCheck, titleShouldMatch = true) {
    this.validateSessionId();
    for (let attempt = 0; attempt < constants.TITLE_CHECK_MAX_ATTEMPTS; attempt += 1) {
      const titleShouldNotMatch = !titleShouldMatch;

      // eslint-disable-next-line no-await-in-loop
      const title = await this.getTitle();
      const titleMatched = title === titleToCheck;

      if ((titleShouldMatch && titleMatched) || (titleShouldNotMatch && !titleMatched)) {
        return;
      } else if ((titleShouldMatch && title !== '') || (titleShouldNotMatch && titleMatched)) {
        // next website loaded
        const errorMessage = titleShouldMatch ? 'Not a self-signed website' : 'Still on a self-signed insecure error page';
        const err = new Error(errorMessage);
        logger.log('ERROR', `checkInsecureWebsite: Title match needed - ${titleShouldMatch} error - ${err}`, { time: helper.getISOString() });
        throw err;
      }
      // eslint-disable-next-line no-await-in-loop
      await Promise.delay(constants.TITLE_CHECK_RETRY_TIMEOUT);
    }
    throw new Error('Max retry limit reached, and website is still not loaded');
  }

  async getWindowSize() {
    this.validateSessionId();
    try {
      return await this.makeRequest('POST', `/wd/hub/session/${this.sessionId}/execute`, { payload: { script: SeleniumClient.WINDOW_SIZE_SCRIPT, args: [] } });
    } catch (err) {
      logger.log('ERROR', `executeScript: ${err}`, { time: helper.getISOString() });
      throw err;
    }
  }

  async getTypes() {
    this.validateSessionId();
    try {
      return await this.makeRequest('GET', `/wd/hub/session/${this.sessionId}/se/log/types`);
    } catch (err) {
      logger.log('ERROR', `getTypes: ${err}`, { time: helper.getISOString() });
      throw err;
    }
  }

  async getLogTypesMobile() {
    this.validateSessionId();
    try {
      return await this.makeRequest('GET', `/wd/hub/session/${this.sessionId}/log/types`);
    } catch (err) {
      logger.log('ERROR', `getTypes: ${err}`, { time: helper.getISOString() });
      throw err;
    }
  }

  async getActiveElement() {
    this.validateSessionId();
    try {
      const res = await this.makeRequest('GET', `/wd/hub/session/${this.sessionId}/element/active`);
      return res.value[constants.ELEMENT_KEY];
    } catch (err) {
      logger.log('ERROR', `getActiveElement: ${err}`, { time: helper.getISOString() });
      throw err;
    }
  }

  async elementValue(elementId, value) {
    this.validateSessionId();
    const body = {
      id: elementId,
      text: value.join(''),
      value: value.join('').split(''),
    };
    try {
      return await this.makeRequest('POST', `/wd/hub/session/${this.sessionId}/element/${elementId}/value`, { payload: body });
    } catch (err) {
      logger.log('ERROR', `elementValue: ${err}`, { time: helper.getISOString() });
      throw err;
    }
  }

  async findElement(using, value, { completeJarResponse = false, parseRespose = false }) {
    this.validateSessionId();
    // Check actual findElement method and complete the request
    const body = {
      using,
      value,
    };
    try {
      return await this.makeRequest('POST', `/wd/hub/session/${this.sessionId}/element`, { payload: body, parseRespose, completeJarResponse });
    } catch (err) {
      logger.log('ERROR', `findElement: ${err}`, { time: helper.getISOString() });
      throw err;
    }
  }

  async findShadowElement(using, value, shadowId, { completeJarResponse = false, parseRespose = false }) {
    this.validateSessionId();
    // Check actual findShadowElement method and complete the request
    const body = {
      using,
      value,
    };
    try {
      return await this.makeRequest('POST', `/wd/hub/session/${this.sessionId}/shadow/${shadowId}/element`, { payload: body, parseRespose, completeJarResponse });
    } catch (err) {
      logger.log('ERROR', `findShadowElement: ${err}`, { time: helper.getISOString() });
      throw err;
    }
  }
}

module.exports = SeleniumClient;
