const { workerExitHardTimeout, workerExitPollInterval, osHostName, LOG_LEVEL } = require("../constants");
const HubLogger = require('../log');

class WorkerHubRailsPipelineManager {
  constructor() {
    this.workerUniqueKey = `${osHostName}-${process.pid}`;
    this.workerHubRailsPipelineCounter = {};
    this.pollingStartTime = null;
  }

  addToWorkerHubRailsPipelineCounter() {
    if (!this.workerHubRailsPipelineCounter[this.workerUniqueKey]) {
      this.workerHubRailsPipelineCounter[this.workerUniqueKey] = 1;
    } else {
      this.workerHubRailsPipelineCounter[this.workerUniqueKey] += 1;
    }
  }

  removeFromWorkerHubRailsPipelineCounter() {
    if (this.workerHubRailsPipelineCounter[this.workerUniqueKey]) {
      this.workerHubRailsPipelineCounter[this.workerUniqueKey] -= 1;
      if (this.workerHubRailsPipelineCounter[this.workerUniqueKey] < 0) {
        this.workerHubRailsPipelineCounter[this.workerUniqueKey] = 0;
      }
    }
  }

  getRequestCounterValue() {
    return this.workerHubRailsPipelineCounter[this.workerUniqueKey] || 0;
  }

  workerExit() {
    HubLogger.miscLogger('startPollingAndExit', `[${new Date()}] Worker stopping ${process.pid}`, LOG_LEVEL.INFO);
    process.exit(0);
  }

  startPollingAndExit() {
    this.pollingStartTime = Date.now();

    const pollFunction = () => {
      const counterValue = this.getRequestCounterValue();
      const currentTime = Date.now();

      if (counterValue === 0) {
        const msg = `${this.workerUniqueKey} Counter is empty. Exiting...`;
        HubLogger.miscLogger('startPollingAndExit', msg, LOG_LEVEL.INFO);
        this.workerExit();
      } else {
        const elapsedTime = currentTime - this.pollingStartTime;

        if (elapsedTime >= workerExitHardTimeout) {
          const msg = `${this.workerUniqueKey} Hard timeout reached. Exiting...`;
          HubLogger.miscLogger('startPollingAndExit', msg, LOG_LEVEL.INFO);
          this.workerExit();
        } else {
          setTimeout(pollFunction, workerExitPollInterval);
        }
      }
    };

    pollFunction();
  }
}

module.exports = new WorkerHubRailsPipelineManager();
