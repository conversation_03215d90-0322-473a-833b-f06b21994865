/*
 *  RailsPipeline
 *  Purpose is that requests to rails can be
 *  auto-queued and auto-resumed in case of downtime
 *
 *  In case of rails Queue-Critical, all requests to rails
 *  are served 503. We can try to not propogate the error to the user
 *  by queuing user's requests
 */

'use strict';

const Promise = require('bluebird');

const RailsPipelineHelper = require('./railsPipelineHelper');
const constants = require('../constants');

class RailsPipeline {
  constructor() {
    // process.nextTick is needed as piplneHlpr constructor defines redisClient = helper.redisClient
    // As hub.js is initialized, helper file is required but, helper.redisClient might be undefined
    // nextTick waits this to be defined before starting to poll the pipeline
    process.nextTick(() => {
      this.pipelineHelper = new RailsPipelineHelper();
      this.pipelineHelper.pollPipeline('automate');
      this.pipelineHelper.pollPipeline('appAutomate');
    });
  }

  // Wrapper to be used for response from rails
  railsResponsePipeline(pipelineOptions) {
    return new Promise((resolve, reject) => {
      const identifier = pipelineOptions.identifier;
      const username = pipelineOptions.username;
      const password = pipelineOptions.password;
      const isStopRequest = pipelineOptions.isStopRequest;
      const isAppAutomateSession = pipelineOptions.isAppAutomateSession;
      // One of response / requestError will be undefined
      const response = pipelineOptions.response;
      const requestError = pipelineOptions.requestError;
      const capabilities = pipelineOptions.capabilities;

      this.pipelineHelper.unregisterRailsRequest(identifier, isAppAutomateSession)
        .then(() => this.pipelineHelper
          .manipulateRailsResponseTokens(identifier, response, requestError, username, isAppAutomateSession))
        .then(() => this.pipelineHelper
          .cacheRailsResponse(isStopRequest || requestError, response, username, password, capabilities, isAppAutomateSession))
        .then(() => (requestError ? reject(requestError) : resolve(response)));
    });
  }

  // Wrapper to be used before making a request to rails
  // Resolves with true as arg if need to drop the request (not forward to rails)
  railsRequestPipeline(pipelineOptions) {
    const identifier = pipelineOptions.identifier;
    const sessionId = pipelineOptions.sessionId;
    const username = pipelineOptions.username;
    const password = pipelineOptions.password;
    const response = pipelineOptions.response;
    const isStopRequest = pipelineOptions.isStopRequest;
    const isStartRequest = pipelineOptions.isStartRequest;
    const isAppAutomateSession = pipelineOptions.isAppAutomateSession;
    const railsPipelineExtraData = pipelineOptions.railsPipelineExtraData;
    const local = pipelineOptions.local;
    const capabilities = pipelineOptions.capabilities;
    const rawCapabilities = pipelineOptions.rawCapabilities;
    const requestReceivedAt = pipelineOptions.requestReceivedAt;

    return new Promise((resolveRequest) => {
      // defender rejects the promise if cached response was returned
      this.pipelineHelper
        .respondToBlockedUser(username, password, local, response, capabilities, rawCapabilities, isAppAutomateSession, requestReceivedAt)
        .then(() => this.pipelineHelper.enqueueRequest(
          username,
          identifier,
          isStartRequest,
          isStopRequest,
          sessionId,
          isAppAutomateSession,
          false,
          railsPipelineExtraData
        )).then(resolveRequest).catch((enqueueError) => {
          this.pipelineHelper.log(sessionId, identifier, `Queuing request as we got exception: ${enqueueError ? enqueueError.message : ''} trace: ${enqueueError ? enqueueError.trace : ''}`);
          if (enqueueError === constants.railsDefenderConfig.bq_error_codes.binary_disconnected) RailsPipelineHelper.sendToEDS(enqueueError, pipelineOptions);
          // Queuing request as we got an exception
          resolveRequest(true);
        });
    });
  }
}

module.exports = new RailsPipeline();
