/*
 *  RailsDefender
 *  Purpose is to restrict the number of requests which we send to Rails
 *  to prevent spam on Rails machines.
 *
 *  Users passing bad-credentials / expired automate plan can
 *  spam Rails because all the requests are forwarded
 *  to Rails to perform validations etc.
 *
 *  Since, it does not make sense to send request to Rails on every
 *  reaquest for the same user, we cache the Rails response
 *  (in case it had errors related to bad-credentials / expired plan) and serve that from Hub.
 */

'use strict';

const redisClient = require('../redisUtils').redisClientSecond;
const constants = require('../constants');
const HubLogger = require('../log');
const helper = require('../helper');
const crypto = require('crypto');
const { Events } = require('browserstack-dwh');

const LL = constants.LOG_LEVEL;

const redisGet = async key => new Promise((res, rej) => {
  redisClient.get(key, (err, result) => {
    if (err) rej(err);
    res(result);
  });
});

const getCacheKey = (prefix, username, password, capabilities) => {
  let pass = '';
  let caps = '';
  // Avoid storing password in plaintext in Redis.
  // Though all of the cache keys set in Redis will have incorrect combinations,
  // but if only the username is incorrect and has some obvious mistake an attacker
  // with access to Redis can guess the correct credentials.
  if (password) {
    pass = crypto.createHash('sha1').update(`SALT${password}PEPPER`).digest('base64');
  }

  if (capabilities) {
    caps = crypto.createHash('sha1').update(`SALT${capabilities}PEPPER`).digest('base64');
  }
  const key = [username, pass, caps].join('_');
  return `${prefix}:${key}`;
};

const addToCache = async (
  blockKeyPrefix,
  username,
  password,
  requestId,
  capabilities,
  jsonData = null,
  generic = false,
  isAppAutomateSession) => {
  const cacheKey = getCacheKey(blockKeyPrefix, username, password, capabilities);

  let keyExpireTime = isAppAutomateSession
    ? constants.railsDefenderConfig.aa_cacheExpiry
    : constants.railsDefenderConfig.cacheExpiry;
  try {
    const expiryTime = await redisGet(blockKeyPrefix);
    if (!Number.isNaN(parseInt(expiryTime, 10))) {
      keyExpireTime = Number(expiryTime);
    }
  } catch (err) {
    HubLogger.miscLogger('RailsDefender', `Unable to fetch expiry time for ${blockKeyPrefix}`, LL.INFO);
  }
  const value = generic ? JSON.stringify(jsonData) : true;
  redisClient.set(cacheKey, value, 'ex', keyExpireTime, () => {
    HubLogger.miscLogger('RailsDefender', `Request Id: ${requestId} Key: ${cacheKey} defending Rails for ${keyExpireTime} seconds for ${blockKeyPrefix}.`, LL.INFO);
  });
};

// Send rails' response from Hub, instead of sending the request to Rails.
const sendRailsResponse = (username, response, blockKeyPrefix, keyIdentifier) => {
  const respData = { value: { message: '' }, sessionId: '', status: 13 };

  switch (blockKeyPrefix) {
    case constants.railsDefenderConfig.cache_keys.bad_auth:
      respData.value.message = constants.railsResponses.bad_auth;
      HubLogger.instrumentationStats(keyIdentifier, { user: username }, 'Set at Hub for Invalid username-password Combination', JSON.stringify(respData));
      response.writeHead(401);
      break;
    case constants.railsDefenderConfig.cache_keys.expired_plan:
      respData.value.message = constants.railsResponses.expired_plan;
      HubLogger.instrumentationStats(keyIdentifier, { user: username }, 'Set at Hub for Request Made after Plan Expired', JSON.stringify(respData));
      break;
    case constants.railsDefenderConfig.cache_keys.expired_testing_time:
      respData.value.message = constants.railsResponses.expired_testing_time;
      HubLogger.instrumentationStats(keyIdentifier, { user: username }, 'Set at Hub for Request Made after Testing Time Expired', JSON.stringify(respData));
      break;
    case constants.railsDefenderConfig.cache_keys.binary_disconnected:
      respData.value.message = constants.railsResponses.binary_disconnected;
      HubLogger.instrumentationStats(keyIdentifier, { user: username }, 'Set at Hub for Request Made after Binary Disconnected', JSON.stringify(respData));
      break;
    default:
      respData.value.message = '';
  }
  HubLogger.miscLogger('RailsDefender', `Username: ${username} served from Hub for: ${respData.value.message}.`, LL.INFO);
  setTimeout(() => response.end(JSON.stringify(respData)), 1500);
};

exports.AddToCacheForBadAuth = async (username, password, requestId, isAppAutomateSession) => {
  await addToCache(
    constants.railsDefenderConfig.cache_keys.bad_auth,
    username, password, requestId, null, null, false, isAppAutomateSession
  );
};

exports.AddToCacheForExpiredPlan = async (username, requestId, isAppAutomateSession) => {
  await addToCache(
    constants.railsDefenderConfig.cache_keys.expired_plan,
    username, null, requestId, null, null, false, isAppAutomateSession
  );
};

exports.AddToCacheForExpiredTestingTime = async (username, requestId, isAppAutomateSession) => {
  await addToCache(
    constants.railsDefenderConfig.cache_keys.expired_testing_time,
    username, null, requestId, null, null, false, isAppAutomateSession
  );
};

exports.AddToCacheForBinaryDisconnected = async (username, requestId, isAppAutomateSession) => {
  await addToCache(
    constants.railsDefenderConfig.cache_keys.binary_disconnected,
    username, null, requestId, null, null, false, isAppAutomateSession
  );
};

exports.AddToCacheForRailsError = async (username, password, requestId, capabilities, jsonData, isAppAutomateSession) => {
  await addToCache(
    constants.railsDefenderConfig.cache_keys.generic,
    username, password, requestId, capabilities, jsonData, true, isAppAutomateSession
  );
};

const respondToBadAuthKeyUser = (username, response) => {
  sendRailsResponse(username, response, constants.railsDefenderConfig.cache_keys.bad_auth, 'Bad Auth');
};

const respondToExpiredPlanUser = (username, response) => {
  sendRailsResponse(username, response, constants.railsDefenderConfig.cache_keys.expired_plan, 'Expired Plan');
};

const respondToExpiredTestingTimeUser = (username, response) => {
  sendRailsResponse(username, response, constants.railsDefenderConfig.cache_keys.expired_testing_time, 'Expired Testing Time');
};

const respondToBinaryDisconnectedUser = (username, response) => {
  sendRailsResponse(username, response, constants.railsDefenderConfig.cache_keys.binary_disconnected, 'Binary Disconnected');
};

const respondToGenericRailsError = async (username, response, cacheData, rawCapabilities, isAppAutomateSession, requestReceivedAt, resolve, reject) => {
  try {
    const jsonResponse = JSON.parse(cacheData);
    jsonResponse.automate_error_data.requestReceivedAt = requestReceivedAt;
    jsonResponse.automate_error_data.error_code = 2; // code is int in bq, adding 2 randomly as rest all are null for automate
    jsonResponse.automate_error_data.raw_capabilities = decodeURIComponent(rawCapabilities);
    let { automate_error_data: automateErrorData } = jsonResponse;

    if (automateErrorData && !automateErrorData.raw_capabilities['browserstack.machine']) {
      automateErrorData = isAppAutomateSession ? { ...automateErrorData, kind: Events.APP_AUTOMATE_ERROR_DATA, framework: 'appium' } : { ...automateErrorData, kind: Events.AUTOMATE_ERROR_DATA };
      helper.sendToEDS(automateErrorData);
    }

    if (jsonResponse.error === constants.railsResponses.bad_auth) {
      response.writeHead(401);
    }

    const data1 = JSON.stringify({ value: { error: jsonResponse.error, message: jsonResponse.error }, sessionId: '', status: 13 });

    HubLogger.miscLogger('RailsDefender', `Username: ${username} served from Hub for: ${jsonResponse.error}.`, LL.INFO);
    setTimeout(() => response.end(data1), 1500);
  } catch (e) {
    HubLogger.tempExceptionLogger(`Error while fetching data from cache - ${cacheData}`, e);
    resolve();
    return;
  }
  reject();
};

// Returns a promise
// If the user has one of the cache keys set, send response from Hub and reject the promise,
// else resolve
// which will proceed with the usual flow of sending the request to Rails
exports.respondToBlockedUser =
  (username, password, local, response, capabilities, rawCapabilities, isAppAutomateSession, requestReceivedAt) => new Promise((resolve, reject) => {
    if (!username) {
      resolve(false);
      return;
    }

    const badAuthKey = getCacheKey(
      constants.railsDefenderConfig.cache_keys.bad_auth,
      username,
      password
    );

    const expiredPlanKey = getCacheKey(
      constants.railsDefenderConfig.cache_keys.expired_plan,
      username
    );

    const expiredTestingTimeKey = getCacheKey(
      constants.railsDefenderConfig.cache_keys.expired_testing_time,
      username
    );
    const binaryDisconnectedKey = getCacheKey(
      constants.railsDefenderConfig.cache_keys.binary_disconnected,
      username
    );
    const genericBlockKey = getCacheKey(
      constants.railsDefenderConfig.cache_keys.generic,
      username,
      password,
      capabilities
    );

    redisClient.pipeline()
      .get(badAuthKey)
      .get(expiredPlanKey)
      .get(expiredTestingTimeKey)
      .get(binaryDisconnectedKey)
      .get(genericBlockKey)
      .exec((_err, results) => {
        let errorCode;
        if (results[0] && results[0][1] === 'true') {
          respondToBadAuthKeyUser(username, response);
        } else if (results[1] && results[1][1] === 'true') {
          respondToExpiredPlanUser(username, response);
        } else if (results[2] && results[2][1] === 'true') {
          respondToExpiredTestingTimeUser(username, response);
        } else if (results[3] && results[3][1] === 'true' && local === true) {
          errorCode = constants.railsDefenderConfig.bq_error_codes.binary_disconnected;
          respondToBinaryDisconnectedUser(username, response);
        } else if (results[4] && results[4][1] !== null) {
          respondToGenericRailsError(username, response, results[4][1], rawCapabilities, isAppAutomateSession, requestReceivedAt, resolve, reject);
          return;
        } else {
          resolve();
          return;
        }

        reject(errorCode);
      });
  });
