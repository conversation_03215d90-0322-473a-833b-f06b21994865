'use strict';

const Promise = require('bluebird');
const helper = require('../helper');
const HubLogger = require('../log');
const constants = require('../constants');
const hoothoot = require('hoothoot')(constants.metrics_region);
const hoothootUse = require('hoothoot')('use');
const hoothootUser = require('hoothoot')(constants.metrics_region, { uniqueUserEvent: true });
const railsDefender = require('./railsDefender');
const AlertManager = require('../alertManager');
const { Events } = require('browserstack-dwh');
const { isNotUndefined } = require('../typeSanity');
const workerHubRailsPipelineManager = require('./workerRailsPipelineManager');

const alertManager = new AlertManager();

const redisTokenHash = {
  automate: constants.railsPipeline.redisAutomateTrackTokenHash,
  appAutomate: constants.railsPipeline.redisAppAutomateTrackTokenHash,
};

const generateQueueCriticalZombieKind = product => `${product}-pipeline-queue-critical`;

const sendDataToZombie = (product, queueLength) => {
  const kind = generateQueueCriticalZombieKind(product);
  helper.PingZombie({
    kind,
    region: constants.region,
    data: `Hub's in memory hub rails pipeline queue at ${queueLength}. Check pipeline in ${constants.region}.`,
    url: process.pid,
    error: 'queue-critical',
    machine: constants.osHostName,
  });
};

const getSeedDataForHootHoot = () => ({
  automate: {
    trackRequestSetSize: 0,
    sumQueued: 0,
    minConcurrent: Number.MAX_SAFE_INTEGER,
    maxConcurrent: Number.MIN_SAFE_INTEGER,
    maxThreshold: 0,
    addToQueue: 0,
  },
  appAutomate: {
    trackRequestSetSize: 0,
    sumQueued: 0,
    minConcurrent: Number.MAX_SAFE_INTEGER,
    maxConcurrent: Number.MIN_SAFE_INTEGER,
    maxThreshold: 0,
    addToQueue: 0,
  },
  automateResponseTokens: {},
  appAutomateResponseTokens: {},
});

const getDowntimeState = (currentRedisValue) => {
  const allStates = constants.monitoring.downtimeStates;
  const [currentState] = Object.entries(allStates).find(([, downtimeValue]) =>
    currentRedisValue === downtimeValue.redisValue) || [];
  return currentState || constants.monitoringDowntimeStates.runningState;
};

const getQueueCountMaxTag = isAppAutomateSession => (isAppAutomateSession ?
  constants.railsPipeline.redisAppAutomateTrackTokenHash.queueCountMaxTag :
  constants.railsPipeline.redisAutomateTrackTokenHash.queueCountMaxTag);

const getTrackRequestTag = isAppAutomateSession => (isAppAutomateSession ?
  constants.railsPipeline.redisAppAutomateTrackTokenHash.queueTrackRequestsSetTag :
  constants.railsPipeline.redisAutomateTrackTokenHash.queueTrackRequestsSetTag);

class RailsPipelineHelper {
  constructor() {
    this.requestQueue = {
      automate: [],
      appAutomate: [],
    };
    this.dataForHootHoot = getSeedDataForHootHoot();
    this.redisClient = helper.redisClientSecond;
    this.railsDefender = railsDefender;
    this.logLevel = constants.LOG_LEVEL;
    this.sendAlerts = alertManager.sendAlerts;
    this.hoothoot = hoothoot;
    this.hoothootUse = hoothootUse;
    this.uniqueUserEvent = hoothootUser.uniqueUserEvent;
    this.sendDataToZombie = sendDataToZombie.bind(this);

    setInterval(this.flushDataToHootHoot.bind(this), 1000);
    setInterval(this.sendAlertOnQueueCritical.bind(this), 2 * 60 * 1000);
  }

  log(sessionId, identifier, message) {
    HubLogger.miscLogger(
      `railsPipelineHelper_${identifier}`,
      `identifier: ${identifier} session ${sessionId} message: ${message}`,
      this.logLevel.INFO
    );
  }

  async checkDowntimeEnabled(tokenHash) {
    const downtimeValue = await this.redisClient.get(tokenHash.downtimeToken);
    const desiredDowntimeState = getDowntimeState(downtimeValue);
    return desiredDowntimeState;
  }

  async checkQueueCriticalAndDowntimeState(product) {
    const downtimeState = await this.checkDowntimeEnabled(redisTokenHash[product]);
    const queueLength = this.requestQueue[product].length;
    const pipelineThreshold = product === 'automate' ? constants.rails_pipeline_queue_length_threshold : constants.rails_pipeline_queue_length_threshold_aa;
    if (queueLength >= pipelineThreshold
      && downtimeState !== constants.monitoringDowntimeStates.forcedDowntimeState) {
      this.sendDataToZombie(product, queueLength);
    }
  }

  async sendAlertOnQueueCritical() {
    await this.checkQueueCriticalAndDowntimeState('automate');
    await this.checkQueueCriticalAndDowntimeState('appAutomate');
  }

  flushDataToHootHoot() {
    ['automate', 'appAutomate'].forEach((product) => {
      if (
        this.dataForHootHoot[product].minConcurrent === Number.MAX_SAFE_INTEGER &&
        this.dataForHootHoot[product].maxConcurrent === Number.MIN_SAFE_INTEGER
      ) {
        delete this.dataForHootHoot[product];
      }
    });
    helper.pushStatsToHootHoot('pipeLineQueue', this.dataForHootHoot);
    this.dataForHootHoot = getSeedDataForHootHoot();
  }

  // Pushes rails response code to redis and adds to hoothoot data
  manipulateRailsResponseTokens(identifier, response, requestError, username, isAppAutomateSession) {
    const redisTrackTokenHash = isAppAutomateSession ?
      constants.railsPipeline.redisAppAutomateTrackTokenHash :
      constants.railsPipeline.redisAutomateTrackTokenHash;

    const hoothootHash = isAppAutomateSession ?
      this.dataForHootHoot.appAutomateResponseTokens :
      this.dataForHootHoot.automateResponseTokens;

    const logTag = isAppAutomateSession ? 'appAutomate' : 'automate';
    if (response) {
      const responseData = helper.getParsedObjectOrEmpty(response.data);
      let redisTag = '';

      if (responseData.reason === 'nta') {
        const isRealMobile = responseData.is_real_mobile;
        const keyName = `nta${responseData.queue ? 'Soft' : 'Hard'}${isRealMobile ? 'Mobile' : 'Desktop'}TokenHash`;
        redisTag = redisTrackTokenHash[keyName];

        helper.getArrayOrEmpty(responseData.terminal_types).forEach((eachTerminalType) => {
          const hoothootTag = `${redisTag}::${eachTerminalType}`.trim();
          hoothootHash[hoothootTag] = (hoothootHash[hoothootTag] || 0) + 1;
          this.redisClient.hincrby(redisTag, eachTerminalType, 1);
        });
      }
    }

    let responseCodeOrError = '';
    if (requestError && requestError.message) {
      responseCodeOrError = `${requestError.message} code: ${requestError.code} type: ${requestError.type}`;
    }
    if (response && response.statusCode !== 'undefined') {
      responseCodeOrError = response.statusCode.toString();
    }
    if (responseCodeOrError !== '') {
      hoothootHash[responseCodeOrError] = (hoothootHash[responseCodeOrError] || 0) + 1;
      const extractDnsDataFrom = response || requestError;
      const dnsData = extractDnsDataFrom && extractDnsDataFrom.dnsData && `${extractDnsDataFrom.dnsData.address} for ${extractDnsDataFrom.dnsData.host}`;
      this.log('', identifier, `[${logTag}] manipulateTokens: Got response code or Error: ${responseCodeOrError} isAppAutomate: ${isAppAutomateSession} dnsData: ${dnsData}`);

      if (!isAppAutomateSession && username && response && response.statusCode && response.statusCode >= 500) {
        const userErrorKey = redisTrackTokenHash.userErrorToken;
        this.redisClient.zincrby(userErrorKey, 1, username);
      }
    }
    const redisKeyTag = (response && response.statusCode && response.statusCode === 200) ?
      redisTrackTokenHash.response200Token :
      redisTrackTokenHash.responseNon200Token;

    this.redisClient.incr(redisKeyTag);
  }

  // Caches rails responses for future requests
  cacheRailsResponse(isStopRequestOrError, response, username, password, capabilities, isAppAutomateSession) {
    return new Promise(async (resolve) => {
      if (isStopRequestOrError) {
        resolve();
        return;
      }

      const responseData = response.data;
      const jsonData = helper.getParsedObjectOrEmpty(responseData);
      const requestId = (helper.nestedKeyValue(jsonData, ['automate_error_data', 'request_id']) || '');

      if (jsonData.error) {
        switch (jsonData.error) {
          case constants.railsResponses.bad_auth:
            await this.railsDefender.AddToCacheForBadAuth(username, password, requestId, isAppAutomateSession);
            break;
          case constants.railsResponses.expired_plan:
            await this.railsDefender.AddToCacheForExpiredPlan(username, requestId, isAppAutomateSession);
            break;
          case constants.railsResponses.expired_testing_time:
            await this.railsDefender.AddToCacheForExpiredTestingTime(username, requestId, isAppAutomateSession);
            break;
          case constants.railsResponses.binary_disconnected:
            await this.railsDefender.AddToCacheForBinaryDisconnected(username, requestId, isAppAutomateSession);
            break;
          default:
            break;
        }
      }

      const errorCodeStrCache = isAppAutomateSession
        ? constants.AA_CACHE_ERRORS_CODE_STR
        : constants.CACHE_ERRORS_CODE_STR;
      const isErrorCodeStrCacheAllowed =
        jsonData.automate_error_data &&
        jsonData.automate_error_data.error_code_str &&
        errorCodeStrCache[jsonData.automate_error_data.error_code_str];

      if (constants.ENABLE_GENERIC_CACHING && isErrorCodeStrCacheAllowed) {
        const data = {
          error: jsonData.error,
          automate_error_data: {
            user_id: jsonData.automate_error_data.user_id,
            error_message: jsonData.automate_error_data.error_message,
            error_code: jsonData.automate_error_data.error_code,
            error_code_str: jsonData.automate_error_data.error_code_str,
            request_id: jsonData.automate_error_data.request_id,
            requestReceivedAt: jsonData.automate_error_data.request_received_at,
            raw_capabilities: {
              'browserstack.machine': jsonData.automate_error_data.raw_capabilities ? jsonData.automate_error_data.raw_capabilities['browserstack.machine'] : null
            }
          }
        };
        await this.railsDefender.AddToCacheForRailsError(username, password, requestId, capabilities, data, isAppAutomateSession);
      }
      resolve();
    });
  }

  // Marks trackRequestId as currently on-going request to rails
  // Returns needtoQueue true / false based on whether we can go ahed with a request to rails
  registerRailsRequest(trackRequestID, isAppAutomateSession, sessionId) {
    const queueCountMaxTag = getQueueCountMaxTag(isAppAutomateSession);
    const trackRequestTag = getTrackRequestTag(isAppAutomateSession);
    const logTag = isAppAutomateSession ? 'appAutomate' : 'automate';
    let elapsedTime = 0;
    const current = new Date();

    return this.redisClient
      .registerRailsRequest([queueCountMaxTag, trackRequestTag, trackRequestID])
      .then(registerResponse => new Promise((resolve) => {
        const needToQueue = registerResponse[0] === 1;
        const maxRequestCount = registerResponse[1];
        const trackRequestSetSize = registerResponse[2];
        elapsedTime = Date.now() - current;
        if (!needToQueue) workerHubRailsPipelineManager.addToWorkerHubRailsPipelineCounter();
        this.log(sessionId, trackRequestID, `[${logTag}] Request: needToQueue: ${needToQueue} maxRequestCount: ${maxRequestCount} trackRequestSetSize: ${trackRequestSetSize}`);
        helper.instrumentRedisCommand('registerRailsRequest', elapsedTime);
        resolve({
          needToQueue, maxRequestCount, trackRequestSetSize,
        });
      }));
  }

  // Un-marks trackRequestId as we got some form of response from rails
  unregisterRailsRequest(trackRequestID, isAppAutomateSession) {
    const trackRequestTag = getTrackRequestTag(isAppAutomateSession);
    const logTag = isAppAutomateSession ? 'appAutomate' : 'automate';
    let elapsedTime = 0;
    const current = new Date();
    const that = this;

    return this.redisClient.unregisterRailsRequest([trackRequestTag, trackRequestID])
      .then(unregisterResponse => new Promise(async (resolve) => {
        const wasRequestTracked = unregisterResponse[0] === 1;
        const trackRequestSetSize = unregisterResponse[1];

        if (wasRequestTracked) workerHubRailsPipelineManager.removeFromWorkerHubRailsPipelineCounter();
        this.log('', trackRequestID, `[${logTag}] Response: wasTracked: ${wasRequestTracked} trackRequestSetSize: ${trackRequestSetSize}`);
        elapsedTime = Date.now() - current;
        helper.instrumentRedisCommand('unregisterRailsRequest', elapsedTime);
        const hoothootHash = isAppAutomateSession ?
          that.dataForHootHoot.appAutomate :
          that.dataForHootHoot.automate;

        hoothootHash.trackRequestSetSize = trackRequestSetSize;
        hoothootHash.minConcurrent = Math.min(
          hoothootHash.minConcurrent,
          trackRequestSetSize
        );
        hoothootHash.maxConcurrent = Math.max(
          hoothootHash.maxConcurrent,
          trackRequestSetSize
        );
        resolve(true);
      }));
  }

  // Pushes requestTrackId to requestQueue and waits for pollPipeline to give go-ahead
  // Returned promise is resolved once the request can go ahead
  enqueueRequest(
    username,
    identifier,
    isStartRequest,
    isStopRequest,
    sessionId,
    isAppAutomateSession,
    highestPriority,
    railsPipelineExtraData
  ) {
    return new Promise((resolveRequest) => {
      const pushData = {
        username,
        identifier,
        isStartRequest,
        isStopRequest,
        sessionId,
        isAppAutomateSession,
        railsPipelineExtraData,
        resolveRequest,
      };
      const requestQueue = isAppAutomateSession ?
        this.requestQueue.appAutomate : this.requestQueue.automate;
      const pendingLength =
        highestPriority ? requestQueue.unshift(pushData) : requestQueue.push(pushData);
      const logTag = isAppAutomateSession ? 'appAutomate' : 'automate';
      const hoothootHash = isAppAutomateSession ?
        this.dataForHootHoot.appAutomate :
        this.dataForHootHoot.automate;
      hoothootHash.sumQueued = pendingLength;
      hoothootHash.addToQueue += 1;
      this.log(sessionId, identifier, `[${logTag}] Enqueue: wasPushedToQueue currentBackLog: ${pendingLength}`);
    });
  }

  respondToBlockedUser(username, password, local, response, capabilities, rawCapabilities, isAppAutomateSession, requestReceivedAt) {
    return this.railsDefender.respondToBlockedUser(username, password, local, response, capabilities, rawCapabilities, isAppAutomateSession, requestReceivedAt);
  }

  static sendToEDS(bqErrorCode, pipelineOptions) {
    helper.sendToEDS({
      kind: Events.AUTOMATE_ERROR_DATA,
      error_code_str: bqErrorCode,
      request_received_at: pipelineOptions.requestReceivedAt,
      request_id: pipelineOptions.requestId,
    });
  }

  pushToHoothoot(isAppAutomateSession, isMobile, username) {
    let hoothootPlatform = null;
    if (isAppAutomateSession) {
      hoothootPlatform = 'all';
    } else {
      hoothootPlatform = isMobile ? 'mobile' : 'desktop';
    }
    this.uniqueUserEvent(username, (isAppAutomateSession ? 'app-automate' : 'automate'), 'current_queue_count', hoothootPlatform);
    if (!isAppAutomateSession) {
      this.hoothootUse.emit('automate_errors_data', 1, {
        event_type: 'current_queue_count',
        platform: hoothootPlatform,
        product: 'automate',
        hub_region: constants.region
      });
    }
  }

  verifyPipelineExtraDataForPushToHootHoot(isStartRequest, railsPipelineExtraData, username, isAppAutomateSession) {
    if (isNotUndefined(railsPipelineExtraData) && isStartRequest === true && !railsPipelineExtraData.isQueued) {
      railsPipelineExtraData.isQueued = true;
      this.pushToHoothoot(isAppAutomateSession, railsPipelineExtraData.isMobile, username);
    }
  }

  // poll pipeLine for any additions
  // Will get the first requestTrackId from requestQueue and try to register a request to rails
  // Please check eventLoop after any changes to the code structure
  pollPipeline(logTag) {
    const requestDetails = this.requestQueue[logTag].shift();
    const that = this;

    // requestDetails can be undefined if the queue itself is empty
    if (requestDetails) {
      const username = requestDetails.username;
      const identifier = requestDetails.identifier;
      const isStartRequest = requestDetails.isStartRequest;
      const isStopRequest = requestDetails.isStopRequest;
      const sessionId = requestDetails.sessionId;
      const isAppAutomateSession = requestDetails.isAppAutomateSession;
      const resolveRequest = requestDetails.resolveRequest;
      const railsPipelineExtraData = requestDetails.railsPipelineExtraData;

      this.registerRailsRequest(identifier, isAppAutomateSession, sessionId)
        .then(async (registerResponseParams) => {
          const shouldQueueRequests = registerResponseParams.needToQueue;
          const maxRequestCount = registerResponseParams.maxRequestCount;
          const trackRequestSetSize = registerResponseParams.trackRequestSetSize;
          const hoothootHash = isAppAutomateSession ?
            that.dataForHootHoot.appAutomate :
            that.dataForHootHoot.automate;

          hoothootHash.maxThreshold = maxRequestCount;
          hoothootHash.trackRequestSetSize = trackRequestSetSize;
          hoothootHash.minConcurrent = Math.min(
            hoothootHash.minConcurrent,
            trackRequestSetSize
          );
          hoothootHash.maxConcurrent = Math.max(
            hoothootHash.maxConcurrent,
            trackRequestSetSize
          );

          if (!shouldQueueRequests) {
            resolveRequest(false);
            that.pollPipeline.call(that, logTag);
            return;
          }

          hoothootHash.sumQueued = this.requestQueue[logTag].length;

          // This will trigger for start request only
          // Don't send data to hoothoot if already sent once
          // This will be used for the request that has been shifted from the request Queue array
          this.verifyPipelineExtraDataForPushToHootHoot(isStartRequest, railsPipelineExtraData, username, isAppAutomateSession);


          // This will check for the requests in the memory queue and will push to hoot hoot in case of forced downtime
          // if the requests are start requests and if request has not been queued
          const downtimeState = await this.checkDowntimeEnabled(redisTokenHash[logTag]);
          if (downtimeState === constants.monitoringDowntimeStates.forcedDowntimeState) {
            this.requestQueue[logTag].forEach((requestData) => {
              this.verifyPipelineExtraDataForPushToHootHoot(requestData.isStartRequest, requestData.railsPipelineExtraData, requestData.username, requestData.isAppAutomateSession);
            });
          }

          // if (isStopRequest) {
          //   queuePendingStop(sessionId, isAppAutomateSession, shouldQueueRequests)
          //     .then(() => { resolveRequest(false); });
          //   pollPipeline();
          //   return;
          // }

          // re-enqueuing with highest priority
          that.enqueueRequest(
            username,
            identifier,
            isStartRequest,
            isStopRequest,
            sessionId,
            isAppAutomateSession,
            true,
            railsPipelineExtraData
          ).then(resolveRequest);
          Promise.delay(constants.railsPipeline.pollQueuedDelay)
            .then(that.pollPipeline.bind(that, logTag));
        });
    } else {
      Promise.delay(constants.railsPipeline.pollEmptyDelay)
        .then(that.pollPipeline.bind(that, logTag));
    }
  }
}

module.exports = RailsPipelineHelper;
