/* eslint-env mocha */

'use strict';

process.env.HUB_ENV = 'testing';

const fs = require('fs');
const path = require('path');
const redis = require('ioredis');
const sinon = require('sinon');

// Not adding this block to before as we never 'restore' the stub
// Restore is not needed as `select` command works on per-connection basis
// Needed to stub as need to switch db for each connection
const origRedisCreateClient = redis.createClient;
redis.createClient = sinon.stub(redis, 'createClient', (config) => {
  const newRedis = origRedisCreateClient(config);
  newRedis.select('3', (err) => {
    if (err) {
      console.log(`Got Error while ${err}`); // eslint-disable-line no-console
      process.exit(1);
    }
    newRedis.flushdb(() => {});
  });
  return newRedis;
});

exports.requireDir = (dir) => {
  // some functional style
  fs.readdirSync(path.join(__dirname, dir))
    .filter(f => !fs.statSync(path.join(__dirname, dir, f)).isDirectory())
    .filter(f => f.endsWith('.js'))
    .forEach((f) => {
      // eslint-disable-next-line global-require, import/no-dynamic-require
      require(path.join(__dirname, dir, f));
    });
};

before(() => {
  sinon.stub(process, 'exit', () => {});
  it('Node.js version should be correct', () => {
    process.version.should.equal(fs.readFileSync(path.join(__dirname, '../.nvmrc')).toString().trim());
  });
});

after(() => {
  process.exit.restore();
});
