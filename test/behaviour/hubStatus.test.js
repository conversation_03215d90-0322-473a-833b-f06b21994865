'use strict';

const helper = require('./helper');

describe('Hub Status', () => {
  describe('GET /wd/hub/status', () => {
    it('it should return success', (done) => {
      helper.request.get('/wd/hub/status')
        .end((err, res) => {
          res.status.should.equal(200);
          res.headers['content-type'].should.match(/json/);
          res.headers['source-from'].should.equal(helper.constants.NGINX_SOURCE_FROM_CODES.HUB);
          res.body.status.should.equal(0);
          res.body.state.should.equal('success');
          done();
        });
    });
  });
});
