var helper = require('./helper'),
  urlModule = require('url');

const testConf = require('../../conf.test.json')

const hubPortForTest = testConf.hub_port.toString(10);

describe('Running Session', function() {
  beforeEach((done) => {
    helper.clearSeamphoreBySessionId('session_stop_semaphore', helper.SESSION_ID).then(() => {
      done();
    })
  });
  afterEach((done) => {
    helper.clearSeamphoreBySessionId('session_stop_semaphore', helper.SESSION_ID).then(() => {
      done();
    });
  });
  it('Hub forwards webdriver requests to terminal', function(done) {
    var capability = helper.getCapabilities({browser: 'Firefox'});
    helper.doSelenium.startSession(capability)
    .then(() => helper.doSelenium.sendCommand({
      method: 'POST',
      uri: '/wd/hub/session/' + helper.SESSION_ID + '/url',
      action: function(uri, body) {
        body = JSON.parse(body);
        body.url.should.equal('http://www.google.com');
        return helper.sessionStartedJSON();
      },
      body: {
        url: 'http://www.google.com'
      }
    }))
    .then(function(res) {
      res.status.should.equal(200);
      res.body.sessionId.should.equal('1a2b3c4d5e6f');
      res.body.value.should.deep.equal({});
      res.body.status.should.equal(0);
      done();
    });
  });
  it('Hub sends /stop request on quit command', function(done) {
    this.timeout(6000);
    var capability = helper.getCapabilities({browser: 'Firefox', 'browserstack.video': false});
    var stopReceived = false;
    var railsScope;
    helper.doSelenium.startSession(capability)
    .then(() => helper.doSelenium.sendCommand({
      method: 'POST',
      uri: '/wd/hub/session/' + helper.SESSION_ID + '/url',
      action: function(uri, body) {
        body = JSON.parse(body);
        body.url.should.equal('http://www.google.com');
        return helper.sessionStartedJSON();
      },
      body: {
        url: 'http://www.google.com'
      }
    }))
    .then(function(res) {
      res.status.should.equal(200);
      res.body.sessionId.should.equal('1a2b3c4d5e6f');
      res.body.value.should.deep.equal({});
      res.body.status.should.equal(0);
    })
    .then(() => {
      railsScope = helper.buildRails({
        query: true,
        persist: true,
        action: function(uri, body) {
          var options = helper.getParamsFromQuery(uri);
          if (options.stop == 'true')
            stopReceived = true;
          return [200, {done: true}];
        }
      });
      return helper.doSelenium.sendCommand({
        method: 'DELETE',
        uri: '/wd/hub/session/' + helper.SESSION_ID,
        action: [200, {}]
      });
    })
    .then(function(res) {
      setTimeout(() => {
        railsScope.isDone().should.be.true;
        stopReceived.should.be.true;
        res.body.status.should.equal(0);
        res.body.state.should.equal('success');
        done();
      }, 20);
    });
  });
  it('Hub sends /stop on quit command if video is on', function(done) {
    this.timeout(6000);
    var capability = helper.getCapabilities({browser: 'Firefox', 'browserstack.video': true});
    var stopReceived = false;
    var railsScope, macTerminalScope;
    helper.doSelenium.startSession(capability)
    .then(() => helper.doSelenium.sendCommand({
      method: 'POST',
      uri: '/wd/hub/session/' + helper.SESSION_ID + '/url',
      action: function(uri, body) {
        body = JSON.parse(body);
        body.url.should.equal('http://www.google.com');
        return helper.sessionStartedJSON();
      },
      body: {
        url: 'http://www.google.com'
      }
    }))
    .then(function(res) {
      res.status.should.equal(200);
      res.body.sessionId.should.equal('1a2b3c4d5e6f');
      res.body.value.should.deep.equal({});
      res.body.status.should.equal(0);
    })
    .then(() => {
      railsScope = helper.buildRails({
        query: true,
        persist: true,
        action: function(uri, body) {
          var options = helper.getParamsFromQuery(uri);
          if (options.stop == 'true')
            stopReceived = true;
          return [200, {done: true}];
        }
      });
      return helper.doSelenium.sendCommand({
        method: 'DELETE',
        uri: '/wd/hub/session/' + helper.SESSION_ID,
        action: [200, {}]
      });
    })
    .then(function(res) {
      setTimeout(() => {
        railsScope.isDone().should.be.true;
        stopReceived.should.be.true;
        res.body.status.should.equal(0);
        res.body.state.should.equal('success');
        done();
      }, 500);
    });
  });

  xit('Hub detects an IDLE_TIMEOUT', function(done) {
    this.timeout(3000);
    var capability = helper.getCapabilities({browser: 'Firefox', 'browserstack.video': false});
    var stopReceived = false;
    var railsScope;
    helper.doSelenium.startSession(capability)
    .then(() => helper.sleep(2000))
    .then(() => helper.doSelenium.sendCommand({
      method: 'POST',
      uri: '/wd/hub/session/' + helper.SESSION_ID + '/url',
      delay: helper.constants.NODE_DIED_IN + 10000,
      action: function(uri, body) {
        body = JSON.parse(body);
        body.url.should.equal('http://www.google.com');
        return helper.sessionStartedJSON();
      },
      body: {
        url: 'http://www.google.com'
      }
    }))
    .then(function(res) {
      res.status.should.equal(404);
      res.body.sessionId.should.equal('');
      res.body.value.message.should.equal('Session not started or terminated');
      res.body.status.should.equal(13);
      done();
    });
  });
  describe('SO_TIMEOUT', function() {
    // test will be fixed as a part of APS-4780
    xit('Hub detects an SO_TIMEOUT', function(done) {
      this.timeout(60000);
      var capability = helper.getCapabilities({browser: 'Firefox', 'browserstack.video': false});
      helper.doSelenium.startSession(capability)
      .then(() => {
        helper.clearSeamphoreBySessionId("session_stop_semaphore", helper.SESSION_ID)
      .then(() => helper.doSelenium.sendCommand({
        method: 'POST',
        uri: '/wd/hub/session/' + helper.SESSION_ID + '/url',
        delay: helper.constants.NODE_DIED_IN + 10000,
        action: function(uri, body) {
          body = JSON.parse(body);
          body.url.should.equal('http://www.google.com');
          return helper.sessionStartedJSON();
        },
        body: {
          url: 'http://www.google.com'
        }
      }))
      .then(function(res) {
        res.status.should.equal(200);
        res.body.sessionId.should.equal('');
        res.body.value.message.should.equal('Unable to communicate to node');
        res.body.status.should.equal(13);
        done();
      });
    });
  });
    // test will be fixed as a part of APS-4780
    xit('Check possible to proceed sent for desktop browser', function(done) {
      var capability = helper.getCapabilities({browser: 'Firefox', 'browserstack.video': false});

      var postExecute = helper.selenium()
      .post('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/execute')
      .query(true)
      .reply(function(uri, body) {
        body = JSON.parse(body);
        body.script.should.equal('return document.readyState');
        body.args.should.deep.equal([]);
        return helper.sessionStartedJSON('complete');
      });
      helper.doSelenium.startSession(capability)
      .then(() => helper.doSelenium.sendCommand({
        method: 'POST',
        uri: '/wd/hub/session/' + helper.SESSION_ID + '/url',
        delay: helper.constants.NODE_DIED_IN + 10000,
        action: function(uri, body) {
          body = JSON.parse(body);
          body.url.should.equal('http://www.google.com');
          return helper.sessionStartedJSON();
        },
        body: {
          url: 'http://www.google.com'
        }
      }))
      .then(function(res) {
        postExecute.isDone().should.be.true;
        res.status.should.equal(200);
        res.body.sessionId.should.equal(helper.SESSION_ID);
        (res.body.value == null).should.be.true;
        res.body.status.should.equal(0);
        done();
      });
    });
    // test will be fixed as a part of APS-4780
    xit('Check possible to proceed sent for real mobile', function(done) {
      var capability = helper.getCapabilities({ device: 'iPhone 6S Plus', 'realMobile': true });

      var postExecute = helper.appiumMini()
      .post('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/execute')
      .query(true)
      .reply(function(uri, body) {
        body = JSON.parse(body);
        body.script.should.equal('return document.readyState');
        body.args.should.deep.equal([]);
        return helper.sessionStartedJSON('complete');
      });
      helper.doSelenium.startSession(capability, helper.appiumMini())
      .then(() => helper.doSelenium.sendCommand({
        method: 'POST',
        uri: '/wd/hub/session/' + helper.SESSION_ID + '/url',
        delay: helper.constants.NODE_DIED_IN + 10000,
        action: function(uri, body) {
          body = JSON.parse(body);
          body.url.should.equal('http://www.google.com');
          return helper.sessionStartedJSON();
        },
        body: {
          url: 'http://www.google.com'
        }
      }))
      .then(function(res) {
        postExecute.isDone().should.be.true;
        res.status.should.equal(200);
        res.body.sessionId.should.equal(helper.SESSION_ID);
        (res.body.value == null).should.be.true;
        res.body.status.should.equal(0);
        done();
      });
    });
  });
  describe('Hub handles checkURLStatus correctly', function() {
    it('checkURLStatus without basic auth', function(done) {
      var capability = helper.getCapabilities(),
        macTerminalScope = helper.buildMac()
        .get('/check_url_status')
        .query(true)
        .reply(function(uri, body) {
          var query = urlModule.parse(uri, true).query;
          query.url.should.equal('http://google.com/');
          (query.bauth_username == null).should.be.true;
          (query.bauth_password == null).should.be.true;

          query.key.should.equal('@ut0m@t3');
          query.logging.should.equal('true');
          query.tunnel.should.equal('false');
          query.certs.should.equal('false');
          query.sessionId.should.equal(helper.SESSION_ID);
          query.hubPort.should.equal(hubPortForTest);
          return helper.sessionStartedJSON();
        });

      helper.doSelenium.startSession(capability)
      .then(() => helper.doSelenium.sendCommand({
        method: 'POST',
        uri: '/wd/hub/session/' + helper.SESSION_ID + '/url',
        action: function(uri, body) {
          body = JSON.parse(body);
          body.url.should.equal('http://google.com');
          return helper.sessionStartedJSON();
        },
        body: {
          url: 'http://google.com'
        }
      }))
      .then(function(res) {
        macTerminalScope.isDone().should.be.true;
        res.status.should.equal(200);
        res.body.sessionId.should.equal('1a2b3c4d5e6f');
        res.body.value.should.deep.equal({});
        res.body.status.should.equal(0);
        done();
      });
    });
    it('checkURLStatus BasicAuth Case 1', function(done) {
      var capability = helper.getCapabilities(),
        macTerminalScope = helper.buildMac()
        .get('/check_url_status')
        .query(true)
        .reply(function(uri, body) {
          var query = urlModule.parse(uri, true).query;
          query.url.should.equal('http://username:<EMAIL>/basic-auth/username/password');
          query.bauth_username.should.equal('username');
          query.bauth_password.should.equal('password');

          query.key.should.equal('@ut0m@t3');
          query.logging.should.equal('true');
          query.tunnel.should.equal('false');
          query.certs.should.equal('false');
          query.sessionId.should.equal(helper.SESSION_ID);
          query.hubPort.should.equal(hubPortForTest);
          return helper.sessionStartedJSON();
        });

      helper.doSelenium.startSession(capability)
      .then(() => helper.doSelenium.sendCommand({
        method: 'POST',
        uri: '/wd/hub/session/' + helper.SESSION_ID + '/url',
        action: function(uri, body) {
          body = JSON.parse(body);
          body.url.should.equal('http://username:<EMAIL>/basic-auth/username/password');
          return helper.sessionStartedJSON();
        },
        body: {
          url: 'http://username:<EMAIL>/basic-auth/username/password'
        }
      }))
      .then(function(res) {
        macTerminalScope.isDone().should.be.true;
        res.status.should.equal(200);
        res.body.sessionId.should.equal('1a2b3c4d5e6f');
        res.body.value.should.deep.equal({});
        res.body.status.should.equal(0);
        done();
      });
    });
    it('checkURLStatus BasicAuth Case 2', function(done) {
      var capability = helper.getCapabilities(),
        macTerminalScope = helper.buildMac()
        .get('/check_url_status')
        .query(true)
        .reply(function(uri, body) {
          var query = urlModule.parse(uri, true).query;
          query.url.should.equal('https://teas:<EMAIL>/authentication/oauth/test');
          query.bauth_username.should.equal('teas');
          query.bauth_password.should.equal('M@lmberg');

          query.key.should.equal('@ut0m@t3');
          query.logging.should.equal('true');
          query.tunnel.should.equal('false');
          query.certs.should.equal('false');
          query.sessionId.should.equal(helper.SESSION_ID);
          query.hubPort.should.equal(hubPortForTest);
          return helper.sessionStartedJSON();
        });

      helper.doSelenium.startSession(capability)
      .then(() => helper.doSelenium.sendCommand({
        method: 'POST',
        uri: '/wd/hub/session/' + helper.SESSION_ID + '/url',
        action: function(uri, body) {
          body = JSON.parse(body);
          body.url.should.equal('https://teas:<EMAIL>/authentication/oauth/test');
          return helper.sessionStartedJSON();
        },
        body: {
          url: 'https://teas:<EMAIL>/authentication/oauth/test'
        }
      }))
      .then(function(res) {
        macTerminalScope.isDone().should.be.true;
        res.status.should.equal(200);
        res.body.sessionId.should.equal('1a2b3c4d5e6f');
        res.body.value.should.deep.equal({});
        res.body.status.should.equal(0);
        done();
      });
    });
    it('checkURLStatus BasicAuth Case 3', function(done) {
      var capability = helper.getCapabilities(),
        macTerminalScope = helper.buildMac()
        .get('/check_url_status')
        .query(true)
        .reply(function(uri, body) {
          var query = urlModule.parse(uri, true).query;
          query.url.should.equal('http://user:<EMAIL>/basic-auth/user/p@ass');
          query.bauth_username.should.equal('user');
          query.bauth_password.should.equal('p@ass@additional@rate');

          query.key.should.equal('@ut0m@t3');
          query.logging.should.equal('true');
          query.tunnel.should.equal('false');
          query.certs.should.equal('false');
          query.sessionId.should.equal(helper.SESSION_ID);
          query.hubPort.should.equal(hubPortForTest);
          return helper.sessionStartedJSON();
        });

      helper.doSelenium.startSession(capability)
      .then(() => helper.doSelenium.sendCommand({
        method: 'POST',
        uri: '/wd/hub/session/' + helper.SESSION_ID + '/url',
        action: function(uri, body) {
          body = JSON.parse(body);
          body.url.should.equal('http://user:p@ass@additional@<EMAIL>/basic-auth/user/p@ass');
          return helper.sessionStartedJSON();
        },
        body: {
          url: 'http://user:p@ass@additional@<EMAIL>/basic-auth/user/p@ass'
        }
      }))
      .then(function(res) {
        macTerminalScope.isDone().should.be.true;
        res.status.should.equal(200);
        res.body.sessionId.should.equal('1a2b3c4d5e6f');
        res.body.value.should.deep.equal({});
        res.body.status.should.equal(0);
        done();
      });
    });
   it('checkURLStatus BasicAuth Case 4', function(done) {
      var capability = helper.getCapabilities(),
        macTerminalScope = helper.buildMac()
        .get('/check_url_status')
        .query(true)
        .reply(function(uri, body) {
          var query = urlModule.parse(uri, true).query;
          query.url.should.equal('http://user:<EMAIL>/basic-auth/user/p@ass');
          query.bauth_username.should.equal('user');
          query.bauth_password.should.equal('p@ass@additional@rate');

          query.key.should.equal('@ut0m@t3');
          query.logging.should.equal('true');
          query.tunnel.should.equal('false');
          query.certs.should.equal('false');
          query.sessionId.should.equal(helper.SESSION_ID);
          query.hubPort.should.equal(hubPortForTest);
          return helper.sessionStartedJSON();
        });

      helper.doSelenium.startSession(capability)
      .then(() => helper.doSelenium.sendCommand({
        method: 'POST',
        uri: '/wd/hub/session/' + helper.SESSION_ID + '/url',
        action: function(uri, body) {
          body = JSON.parse(body);
          body.url.should.equal('http://user:<EMAIL>/basic-auth/user/p@ass');
          return helper.sessionStartedJSON();
        },
        body: {
          url: 'http://user:<EMAIL>/basic-auth/user/p@ass'
        }
      }))
      .then(function(res) {
        macTerminalScope.isDone().should.be.true;
        res.status.should.equal(200);
        res.body.sessionId.should.equal('1a2b3c4d5e6f');
        res.body.value.should.deep.equal({});
        res.body.status.should.equal(0);
        done();
      });
    });
    it('checkURLStatus BasicAuth Case 5', function(done) {
      var capability = helper.getCapabilities({ 'browserstack.local': true }),
        macTerminalScope = helper.buildMac()
        .get('/check_url_status')
        .query(true)
        .reply(function(uri, body) {
          var query = urlModule.parse(uri, true).query;
          query.url.should.equal('************************************/admin');
          query.bauth_username.should.equal('jo hn');
          query.bauth_password.should.equal('do e');

          query.key.should.equal('@ut0m@t3');
          query.logging.should.equal('true');
          query.tunnel.should.equal('true');
          query.certs.should.equal('false');
          query.sessionId.should.equal(helper.SESSION_ID);
          query.hubPort.should.equal(hubPortForTest);
          return helper.sessionStartedJSON();
        });

      helper.doSelenium.startSession(capability)
      .then(() => helper.doSelenium.sendCommand({
        method: 'POST',
        uri: '/wd/hub/session/' + helper.SESSION_ID + '/url',
        action: function(uri, body) {
          body = JSON.parse(body);
          body.url.should.equal("http://jo hn:do e@localhost:4567/admin");
          return helper.sessionStartedJSON();
        },
        body: {
          url: "http://jo hn:do e@localhost:4567/admin"
        }
      }))
      .then(function(res) {
        macTerminalScope.isDone().should.be.true;
        res.status.should.equal(200);
        res.body.sessionId.should.equal('1a2b3c4d5e6f');
        res.body.value.should.deep.equal({});
        res.body.status.should.equal(0);
        done();
      });
    });
    it('checkURLStatus BasicAuth Case 6', function(done) {
      var capability = helper.getCapabilities({ 'browserstack.local': true }),
        macTerminalScope = helper.buildMac()
        .get('/check_url_status')
        .query(true)
        .reply(function(uri, body) {
          var query = urlModule.parse(uri, true).query;
          query.url.should.equal('************************************/admin');
          query.bauth_username.should.equal('jo hn');
          query.bauth_password.should.equal('do e');

          query.key.should.equal('@ut0m@t3');
          query.logging.should.equal('true');
          query.tunnel.should.equal('true');
          query.certs.should.equal('false');
          query.sessionId.should.equal(helper.SESSION_ID);
          query.hubPort.should.equal(hubPortForTest);
          return helper.sessionStartedJSON();
        });

      helper.doSelenium.startSession(capability)
      .then(() => helper.doSelenium.sendCommand({
        method: 'POST',
        uri: '/wd/hub/session/' + helper.SESSION_ID + '/url',
        action: function(uri, body) {
          body = JSON.parse(body);
          body.url.should.equal('************************************/admin');
          return helper.sessionStartedJSON();
        },
        body: {
          url: '************************************/admin'
        }
      }))
      .then(function(res) {
        macTerminalScope.isDone().should.be.true;
        res.status.should.equal(200);
        res.body.sessionId.should.equal('1a2b3c4d5e6f');
        res.body.value.should.deep.equal({});
        res.body.status.should.equal(0);
        done();
      });
    });
    it('checkURLStatus BasicAuth Case 7', function(done) {
      var capability = helper.getCapabilities({ 'browserstack.local': true }),
        macTerminalScope = helper.buildMac()
        .get('/check_url_status')
        .query(true)
        .reply(function(uri, body) {
          var query = urlModule.parse(uri, true).query;
          query.url.should.equal('http://john:doe:additional:colons@localhost:4567/admin');
          query.bauth_username.should.equal('john');
          query.bauth_password.should.equal('doe:additional:colons');

          query.key.should.equal('@ut0m@t3');
          query.logging.should.equal('true');
          query.tunnel.should.equal('true');
          query.certs.should.equal('false');
          query.sessionId.should.equal(helper.SESSION_ID);
          query.hubPort.should.equal(hubPortForTest);
          return helper.sessionStartedJSON();
        });

      helper.doSelenium.startSession(capability)
      .then(() => helper.doSelenium.sendCommand({
        method: 'POST',
        uri: '/wd/hub/session/' + helper.SESSION_ID + '/url',
        action: function(uri, body) {
          body = JSON.parse(body);
          body.url.should.equal('http://john:doe:additional:colons@localhost:4567/admin');
          return helper.sessionStartedJSON();
        },
        body: {
          url: 'http://john:doe:additional:colons@localhost:4567/admin'
        }
      }))
      .then(function(res) {
        macTerminalScope.isDone().should.be.true;
        res.status.should.equal(200);
        res.body.sessionId.should.equal('1a2b3c4d5e6f');
        res.body.value.should.deep.equal({});
        res.body.status.should.equal(0);
        done();
      });
    });
  });
});

describe('Running Session for app automate', function() {
  it('Hub sends /stop request on quit command', function(done) {
    this.timeout(6000);
    // var capability = helper.AppAutomateCapabilities({browser: 'Firefox', 'browserstack.video': false});
    var capability = helper.AppAutomateCapabilities();
    var stopReceived = false;
    var railsScope;
    var snapshotMacTerminalScope = helper.buildMac({
        uri: '/snapshot_hub',
        action: function(uri, body){
          uri.should.have.all.keys('bucket', 'folder', 'file', 'device', 'orientation');
          uri.should.have.property('bucket', helper.constants.APP_AUTOMATE_LOGS_AWS_BUCKET);
          uri.should.have.property('folder', helper.SESSION_ID);
          uri.should.have.property('file', 'screenshot-1');
          uri.should.have.property('device', helper.UDID);
          uri.should.have.property('orientation', 'portrait');
          return [200, { done: true }]
        }
      });
      helper.clearSeamphoreBySessionId('session_stop_semaphore', helper.SESSION_ID).then(() => {
        helper.doSelenium.startSession(capability, undefined, {isAppAutomate: true})
      .then(() => helper.doSelenium.sendCommand({
        method: 'POST',
        uri: '/wd/hub/session/' + helper.SESSION_ID + '/url',
        action: function(uri, body) {
          body.should.be.empty;
          return helper.sessionStartedJSON();
        }
      }))
      .then(function(res) {
        res.status.should.equal(200);
        res.body.sessionId.should.equal('1a2b3c4d5e6f');
        res.body.value.should.deep.equal({});
        res.body.status.should.equal(0);
      })
      .then(() => {
        railsScope = helper.buildRails({
          query: true,
          persist: true,
          action: function(uri, body) {
            var options = helper.getParamsFromQuery(uri);
            if (options.stop == 'true')
              stopReceived = true;
            return [200, {done: true}];
          },
          isAppAutomate: true
        });

        return helper.doSelenium.sendCommand({
          method: 'DELETE',
          uri: '/wd/hub/session/' + helper.SESSION_ID,
          action: [200, {}]
        });
      })
      .then(function(res) {
        setTimeout(() => {
          railsScope.isDone().should.be.true;
          stopReceived.should.be.true;
          res.body.status.should.equal(0);
          res.body.state.should.equal('success');
          done();
        }, 2000);
      });
    });
  });

  it('should return in case of Econnrefused App-automate Session', function(done) {
    this.timeout(6000);
    var capability = helper.AppAutomateCapabilities();
    var terminalServer = helper.selenium(undefined, undefined, helper.APPIUM_PORT)
        .get('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/url')
        .query(true)
        .reply(function(uri, body) {
          return [500, {status: 13, value : {message : "Appium error: An unknown server-side error occurred while processing the command. Original error: Could not proxy. Proxy error: Could not proxy command to remote server. Original error: Error: connect ECONNREFUSED 127.0.0.1:8403"}}]
        });
    helper.doSelenium.startSession(capability, helper.selenium(undefined, undefined, helper.APPIUM_PORT), {isAppAutomate: true})
    .then(() => new Promise (function(resolve, reject){
      helper.request.get('/wd/hub/session/' + helper.SESSION_ID + '/url')
      .end(function(err, res) {
        terminalServer.isDone().should.be.true;
        resolve(res);
      });
    }))
    .then(function(res) {
      terminalServer.isDone().should.be.true;
      res.status.should.equal(500);
      res.body.value.should.deep.equal({ message: 'Appium error: Appium error: An unknown server-side error occurred while processing the command. Original error: Could not proxy. Proxy error: Could not proxy command to remote server. Original error: Error: connect ECONNREFUSED 127.0.0.1:8403' });
      res.body.status.should.equal(13);
      done();
    })
  });
});
