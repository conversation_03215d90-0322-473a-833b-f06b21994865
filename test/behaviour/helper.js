process.env.HUB_ENV = 'testing';

const chai = require('chai').should();
const supertest = require('supertest');
const nock = require('nock');
const constants = require('../../constants.js');
const {
  clearSemaphore, ttlSemaphore, clearSeamphoreBySessionId, checkSemaphore
} = require('../../semaphore');
const hubHelper = require('../../helper.js');
const pubSub = require('../../pubSub.js');
const { REMOTE_DEBUGGER_PORT } = require('../../config/socketConstants');
const { isTrueString } = require('../../typeSanity');

const browserstackParamsSet = new Set(constants.browserstackParams);

exports.clearSemaphore = clearSemaphore;
exports.clearSeamphoreBySessionId = clearSeamphoreBySessionId;
exports.ttlSemaphore = ttlSemaphore;
exports.checkSemaphore = checkSemaphore;
exports.constants = constants;
exports.TERMINAL_IP = '*************';
exports.RPROXY_HOST = 'rproxy-testing.bsstag.com';
exports.SESSION_ID = '1a2b3c4d5e6f';
exports.APPIUM_PORT = 38080;
exports.TERMINAL_SESSION_ID = '1234-abcd-5678-wxyz';
exports.UDID = 'pqrstu';
exports.request = supertest(`http://${constants.SERVER_NAME}:${constants.SERVER_PORT}`);
exports.sleep = function (millis) {
  return new Promise(((resolve, reject) => {
    setTimeout(() => {
      resolve();
    }, millis);
  }));
};

exports.sessionStartedJSON = function (obj) {
  if (typeof obj === 'undefined') { obj = {}; }
  return [200, { sessionId: exports.TERMINAL_SESSION_ID, value: obj, status: 0 }];
};

exports.doSelenium = {
  _seleniumNock: null,
  startSession(capability, seleniumNock, params) {
    params = params || { query: { auth: 'selautomate' } };
    if (capability.desiredCapabilities['browserstack.local']) {
      params.query.local = 'true';
    }
    return new Promise(function (resolve, reject) {
      let railsScope = exports.buildRails(params),
        macTerminalScope = exports.buildMac(),
        winTerminalScope = exports.buildWin();

      if (params.isAppAutomate === true) {
        var regSessionRailsScope = exports.buildRails({
          query: true,
          action(uri, body) {
            uri.should.include('auth=selautomate');
            uri.should.include(`ci=${exports.TERMINAL_IP}`);
            uri.should.include('reg_session=true');
            uri.should.include(`selenium_key=${exports.TERMINAL_SESSION_ID}`);
            uri.should.include('start_time_ts=');
            return [200, {
              ip: exports.TERMINAL_IP,
              desiredCapabilities: {},
              browserstack_params: {},
              automation_session_id: exports.SESSION_ID,
              collection_number: 1
            }];
          },
          isAppAutomate: true
        });

        this._seleniumNock = seleniumNock || exports.selenium(undefined, undefined, exports.APPIUM_PORT);
      } else {
        this._seleniumNock = seleniumNock || exports.selenium();
      }


      let seleniumScope;
      if (params.skipSeleniumNock) {
        seleniumScope = this._seleniumNock;
      } else {
        seleniumScope = this._seleniumNock
          .post('/wd/hub/session')
          .query(true)
          .reply((uri, body) => exports.sessionStartedJSON(JSON.parse(body).desiredCapabilities));
      }

      exports.request.post('/wd/hub/session')
        .auth('test', 'test')
        .send(capability)
        .then((res) => {
          if (params.query) {
            railsScope.isDone().should.be.true;
          }
          // assert(macTerminalScope.isDone());
          if (!params.skipSeleniumNock) {
            seleniumScope.isDone().should.be.true;
          }
          res.status.should.equal(200);
          if ('deviceName' in res.body.value || 'device' in res.body.value) { res.body.value.should.have.any.keys('browserName', 'deviceName', 'platformName', 'platformVersion'); } else { res.body.value.should.have.any.keys('browser', 'browser_version', 'os', 'os_version'); }

          res.body.sessionId.should.equal(exports.SESSION_ID);
          res.body.status.should.equal(0);

          if (params.isAppAutomate === true) {
            setTimeout(() => {
              regSessionRailsScope.isDone().should.be.true;
              resolve(res);
            }, 20);
          } else {
            resolve(res);
          }
        })
        .catch((err) => {
          reject(err);
        });
    });
  },
  stopSession(params) {
    // startSession and stopSession does not verify if terminal server got request
    return new Promise(((resolve, reject) => {
      let railsStopScope;
      if (!(params && params.mockRails === false)) {
        if (params && params.isAppAutomate === true) { railsStopScope = exports.buildRailsStop({ query: true, isAppAutomate: true }); } else { railsStopScope = exports.buildRailsStop(); }
      }
      if (params && params.isAppAutomate === true) {
        seleniumScope = exports.selenium(undefined, undefined, exports.APPIUM_PORT)
          .delete(`/wd/hub/session/${exports.TERMINAL_SESSION_ID}`)
          .query(true)
          .reply((uri, body) => [200, { sessionId: exports.TERMINAL_SESSION_ID, value: {}, status: 0 }]);
      } else {
        seleniumScope = exports.selenium()
          .delete(`/wd/hub/session/${exports.TERMINAL_SESSION_ID}`)
          .query(true)
          .reply((uri, body) => [200, { sessionId: exports.TERMINAL_SESSION_ID, value: {}, status: 0 }]);
      }

      if (!(params && params.mockTerminals === false)) {
        let macTerminalScope = exports.buildMac({
            uri: '/stop_video_rec'
          }),
          winTerminalScope = exports.buildWin({
            uri: '/stop_video_rec'
          });
      }

      exports.request.delete(`/wd/hub/session/${exports.SESSION_ID}`)
        .auth('test', 'test')
        .then((res) => {
          setTimeout(() => {
            if (!(params && params.mockRails === false)) {
              railsStopScope.isDone().should.be.true;
            }
            if (params && params.isAppAutomate === true) {
              seleniumScope.isDone().should.be.true;
            }
            res.status.should.equal(200);
            res.body.state.should.equal('success');
            res.body.status.should.equal(0);
            resolve(res);
          }, 500);
        }).catch((err) => {
          reject(err);
        });
    }));
  },
  sendCommand(params) {
    return new Promise(function (resolve, reject) {
      let scope = this._seleniumNock;
      if (params && params.persist === true) { scope = scope.persist(); }
      scope = scope[params.method.toLowerCase()](params.uri.replace(exports.SESSION_ID, exports.TERMINAL_SESSION_ID))
        .query(true);
      if (params && params.delay) { scope = scope.delayConnection(params.delay); }
      if (params && params.delayHeader) { scope = scope.delay(params.delayHeader); }
      if (params && params.error) { scope = scope.replyWithError(params.error); } else { scope = scope.reply(params.action); }

      exports.request[params.method.toLowerCase()](params.uri)
        .send(params.body)
        .then((res) => {
        // do not check if scope is fulfilled on driver.quit
        // The hub sometimes doesn't forward the driver.quit request
        // TODO: This can be streamlined in future only for those cases
          if (params.method.toUpperCase() !== 'DELETE' && exports.SESSION_ID in constants.global_registry && params.noAssert != true) {
            scope.isDone().should.be.true;
          }
          resolve(res);
        }).catch((err) => {
          reject(err);
        });
    });
  }
};

exports.getParamsFromQuery = function (query) {
  let options = query.substring(query.indexOf('?') + 1);
  if (options[0] == '&') { options = options.substring(1); }
  const tokens = options.split('&');
  const hash = {};
  for (let i = 0; i < tokens.length; i++) {
    const keyval = tokens[i].split('=');
    hash[keyval[0]] = unescape(keyval[1]);
  }
  return hash;
};

const buildWDAServer = function (port) {
  return function (params) {
    let macScope = nock(`http://${exports.RPROXY_HOST}:${port}`);
    if (macScope = macScope.get('/status')) {
      macScope = macScope.reply(200, { done: true, sessionId: '1234-xyz' });
    }

    if (params && params.accept_alert) {
      macScope = macScope.post('/session/1234-xyz/alert/accept', /\w+/).reply((uri, body) => ({ status: 0 }));
    }
    return macScope;
  };
};
exports.buildWDAServer = buildWDAServer;

const buildTerminal = function (port) {
  return function (params) {
    let macScope = nock(`http://${exports.RPROXY_HOST}:${port}`);

    if (params && params.persist === true) { macScope = macScope.persist(); }

    if (params && params.uri) {
      if (params.isPuppeteer) {
        macScope = macScope.post(params.uri);
      } else {
        macScope = macScope.get(params.uri);
      }
    } else {
      if (params && params.isPuppeteer) {
        macScope = macScope.post('/selenium_command');
      } else {
        macScope = macScope.get('/selenium_command');
      }
    }

    if (params && params.query) { macScope = macScope.query(params.query); } else { macScope = macScope.query(true); }

    if (params && params.delay) { macScope = macScope.delayConnection(params.delay); }

    if (params && params.delayBody) { macScope = macScope.delay(params.delayBody); }

    if (params && params.error === true) { macScope = macScope.replyWithError('I am a mac terminal error'); } else if (params && params.action) {
      if (Array.isArray(params.action)) { macScope = macScope.reply(params.action[0], params.action[1]); } else {
        macScope = macScope.reply((uri, body) => {
          const options = exports.getParamsFromQuery(uri);
          if (params && params.isPuppeteer && options && options[uri] === 'undefined') return params.action(body);
          return params.action(options);
        });
      }
    } else { macScope = macScope.reply(200, { done: true }); }

    return macScope;
  };
};



const buildTerminalRproxy = function (port) {
  return function (params) {
    let macScope = nock(`http://*************:${port}`);

    if (params && params.persist === true) { macScope = macScope.persist(); }

    if (params && params.uri) {
      if (params.isPuppeteer) {
        macScope = macScope.post(params.uri);
      } else {
        macScope = macScope.get(params.uri);
      }
    } else {
      if (params && params.isPuppeteer) {
        macScope = macScope.post('/selenium_command');
      } else {
        macScope = macScope.get('/selenium_command');
      }
    }

    if (params && params.query) { macScope = macScope.query(params.query); } else { macScope = macScope.query(true); }

    if (params && params.delay) { macScope = macScope.delayConnection(params.delay); }

    if (params && params.delayBody) { macScope = macScope.delay(params.delayBody); }

    if (params && params.error === true) { macScope = macScope.replyWithError('I am a mac terminal error'); } else if (params && params.action) {
      if (Array.isArray(params.action)) { macScope = macScope.reply(params.action[0], params.action[1]); } else {
        macScope = macScope.reply((uri, body) => {
          const options = exports.getParamsFromQuery(uri);
          if (params && params.isPuppeteer && options && options[uri] === 'undefined') return params.action(body);
          return params.action(options);
        });
      }
    } else { macScope = macScope.reply(200, { done: true }); }

    return macScope;
  };
};

exports.getCapabilities = function (userCaps) {
  const caps = {
    desiredCapabilities: {
      browser: 'Chrome',
      os: 'OS X',
      os_version: 'Yosemite',
      browser_version: 45,
      'browserstack.video': true
    }
  };
  if (userCaps && typeof userCaps === 'object') {
  for (const key in userCaps) { caps.desiredCapabilities[key] = userCaps[key]; }
    if (caps.desiredCapabilities.device) {
      switch (caps.desiredCapabilities.device) {
        case 'Motorola Moto X 2nd Gen':
          caps.desiredCapabilities.os = 'android';
          caps.desiredCapabilities.os_version = '5.0';
          caps.desiredCapabilities.browser = 'android';
          delete caps.desiredCapabilities.browser_version;
          break;
      }
    }
  }
  return caps;
};

exports.AppAutomateCapabilities = function (userCaps, del_caps) {
  const caps = {
    desiredCapabilities: {
      app: 'bs://be66ef65c26c2490ad2884f0bd6df4fba203d552',
      build: 'junit-browserstack',
      realMobile: 'true',
      name: 'single_appium_test',
      platformName: 'Android',
      device: 'Google Pixel'
    }
  };
  if (userCaps && typeof userCaps === 'object') {
    for (const key in userCaps) {
      caps.desiredCapabilities[key] = userCaps[key];
    }
  }
  if (del_caps && typeof del_caps === 'object') {
    for (const index in del_caps) {
      delete caps.desiredCapabilities[del_caps[index]];
    }
  }
  return caps;
};


exports.buildRailsStop = function (params) {
  let host = constants.BS_ENDPOINT;
  let port = constants.BS_ENDPOINT_PORT;

  if (params && params.isAppAutomate === true) {
    host = constants.BS_APP_ENDPOINT;
    port = constants.BS_APP_ENDPOINT_PORT;
  }

  let railsStopScope = nock(`http://${host}:${port}`);
  const stopQueryObject = (params && params.query) ? params.query : {
    auth: 'selautomate',
    stop: true,
    r: 'CLIENT_STOPPED_SESSION',
    k: exports.SESSION_ID,
    i: exports.TERMINAL_IP,
    local_key: exports.TERMINAL_SESSION_ID
  };
  if (params && params.persist === true) {
    railsStopScope = railsStopScope.persist();
  }
  railsStopScope = railsStopScope.post('/selenium/authenticate').query(stopQueryObject);
  if (params && params.reply) {
    railsStopScope = railsStopScope.reply(params.reply);
  } else {
    railsStopScope = railsStopScope.reply((uri, body) => 'true');
  }
  return railsStopScope;
};

exports.addBrowserstackParamsAndDeleteFromDesiredCapabilities = function (browserstackParams, desiredCapabilities) {
  const keys = Object.keys(desiredCapabilities);

  //adding this manually as this was directly added through ocm in rails without addition in browserstackParams.json
  browserstackParamsSet.add('browserstack.timezone_mdm');

  keys.forEach((key) => {
    if (browserstackParamsSet.has(key)) {
      browserstackParams[key] = desiredCapabilities[key];
      delete desiredCapabilities[key];
    }
  });
};

exports.buildRails = function (params) {
  var host = constants.BS_ENDPOINT;
  var port = constants.BS_ENDPOINT_PORT;
  var group_id;

  if(params && params.group_id) {
    group_id = params.group_id;
  }

  if (params && params.isAppAutomate) {
    var host = constants.BS_APP_ENDPOINT;
    var port = constants.BS_APP_ENDPOINT_PORT;
  }
  let railsScope = nock(`http://${host}:${port}`);

  if (params && params.persist === true) { railsScope = railsScope.persist(); }

  railsScope = railsScope.post('/selenium/authenticate');

  if (params && params.query) { railsScope = railsScope.query(params.query); } else { railsScope = railsScope.query({ auth: 'selautomate' }); }

  if (params && params.delay) { railsScope = railsScope.delayConnection(params.delay); }

  if (params && params.error === true) { railsScope = railsScope.replyWithError('I am a rails error'); } else if (params && params.action) {
    if (Array.isArray(params.action)) { railsScope = railsScope.reply(params.action[0], params.action[1]); } else { railsScope = railsScope.reply(params.action); }
  } else {
    railsScope = railsScope.reply((uri, body) => {
      if (params && params.extraValidation) { params.extraValidation(uri, body); }

      const desiredCapabilities = JSON.parse(unescape(JSON.parse(body).desiredCapabilities));
      const browserstackParams = {};
      if(desiredCapabilities['browserstack.browserstackSDK']) {
        browserstackParams['browserstack.browserstackSDK'] = desiredCapabilities['browserstack.browserstackSDK'];
        browserstackParams["browserstack.terminal_sub_region"] = "us-east-1a";
      } else {
        browserstackParams["browserstack.terminal_sub_region"] = "us-east-3b";
      }

      exports.addBrowserstackParamsAndDeleteFromDesiredCapabilities(browserstackParams, desiredCapabilities);

      if (desiredCapabilities.os === 'OS X') {
        desiredCapabilities.platform = 'MAC';
        switch (desiredCapabilities.os_version) {
          case 'Yosemite':
            desiredCapabilities.orig_os = 'macyos';
            break;
          case 'El Capitan':
            desiredCapabilities.orig_os = 'macelc';
            break;
          case 'Mavericks':
            desiredCapabilities.orig_os = 'macmav';
            break;
          case 'Snow Leopard':
            desiredCapabilities.orig_os = 'macsl';
            break;
          case 'Sierra':
            desiredCapabilities.orig_os = 'macsie';
            break;
          case 'High Sierra':
            desiredCapabilities.orig_os = 'machs';
            break;
          case 'Mojave':
            desiredCapabilities.orig_os = 'macmo';
            break;
          case 'Catalina':
            desiredCapabilities.orig_os = 'maccat';
            break;
          case 'Big Sur':
            desiredCapabilities.orig_os = 'macbsr';
            break;
          case 'Monterey':
            desiredCapabilities.orig_os = 'macmty';
            break;
          case 'Ventura':
            desiredCapabilities.orig_os = 'macven';
            break;
          case 'Sonoma':
            desiredCapabilities.orig_os = 'macson';
            break;
          case 'Sequoia':
            desiredCapabilities.orig_os = 'macsqa';
            break;
        }
      } else if (desiredCapabilities.os === 'Windows') {
        desiredCapabilities.platform = 'WINDOWS';
        switch (desiredCapabilities.os_version) {
          case '11':
            desiredCapabilities.orig_os = 'win11';
            break;
          case '10':
            desiredCapabilities.orig_os = 'win10';
            break;
          case '8.1':
            desiredCapabilities.orig_os = 'win8.1';
            break;
          case '7':
            desiredCapabilities.orig_os = 'win7';
            break;
          case 'XP':
            desiredCapabilities.orig_os = 'winxp';
            break;
        }
      }
      if ('device' in desiredCapabilities) {
        switch (desiredCapabilities.device) {
          case 'iPhone 6 Plus':
            desiredCapabilities.mobile = {};
            desiredCapabilities.mobile.browser = 'mobile';
            desiredCapabilities.mobile.version = 'iPhone 6 Plus-8.3';
            desiredCapabilities.browser = desiredCapabilities.browser || 'iPhone';
            desiredCapabilities.platform = 'MAC';
            break;
          case 'iPhone 6S Plus':
            desiredCapabilities.mobile = {};
            desiredCapabilities.mobile.browser = 'mobile';
            desiredCapabilities.mobile.version = 'iPhone 6S Plus-9.0';
            desiredCapabilities.browser = desiredCapabilities.browser || 'iPhone';
            desiredCapabilities.platform = 'MAC';
            desiredCapabilities.orig_os = 'ios';
            if (browserstackParams.realMobile && browserstackParams.realMobile.toString() == 'true') {
              browserstackParams.appium_port = '1234';
              desiredCapabilities.udid = 'abcdxyz';
            }
            break;
          case 'iPhone 7':
            desiredCapabilities.mobile = {};
            desiredCapabilities.mobile.browser = 'mobile';
            desiredCapabilities.mobile.version = 'iPhone 7-10.0';
            desiredCapabilities.browser = desiredCapabilities.browser || 'iPhone';
            desiredCapabilities.platform = 'MAC';
            desiredCapabilities.orig_os = 'ios';
            if (browserstackParams.realMobile && browserstackParams.realMobile.toString() == 'true') {
              browserstackParams.appium_port = '1234';
              desiredCapabilities.udid = 'abcdxyz';
            }
            break;
          case 'iPhone 8':
            desiredCapabilities.mobile = {};
            desiredCapabilities.mobile.browser = 'mobile';
            desiredCapabilities.mobile.version = 'iPhone 8-11.0';
            desiredCapabilities.browser = desiredCapabilities.browser || 'iPhone';
            desiredCapabilities.platform = 'MAC';
            desiredCapabilities.orig_os = 'ios';
            if (browserstackParams.realMobile && browserstackParams.realMobile.toString() == 'true') {
              browserstackParams.appium_port = '1234';
              desiredCapabilities.wda_port = '1233';
              desiredCapabilities.udid = 'abcdxyz';
            }
            break;
          case 'iPad Mini 3':
            desiredCapabilities.mobile = {};
            desiredCapabilities.mobile.browser = 'mobile';
            desiredCapabilities.mobile.version = 'iPad Mini 3-8.1';
            desiredCapabilities.browser = desiredCapabilities.browser || 'iPad';
            desiredCapabilities.platform = 'MAC';
            if (browserstackParams.realMobile && browserstackParams.realMobile.toString() == 'true') {
              browserstackParams.appium_port = '1234';
              desiredCapabilities.udid = 'abcdxyz';
              desiredCapabilities.orig_os = 'ios';
            }
            break;
          case 'Google Nexus 5':
            desiredCapabilities.mobile = {};
            desiredCapabilities.mobile.browser = 'mobile';
            desiredCapabilities.mobile.version = 'Google Nexus 5-4.4';
            desiredCapabilities.deviceName = 'Google Nexus 5-4.4';
            desiredCapabilities.browser = desiredCapabilities.browser || 'android';
            desiredCapabilities.platform = 'ANDROID';
            if (browserstackParams.realMobile && browserstackParams.realMobile.toString() == 'true') {
              browserstackParams.appium_port = '5678';
              desiredCapabilities.udid = 'pqrstu';
              desiredCapabilities.orig_os = 'android';
            }
            break;
          case 'Google Nexus 6':
            desiredCapabilities.mobile = {};
            desiredCapabilities.mobile.browser = 'mobile';
            desiredCapabilities.mobile.version = 'Google Nexus 6-5.0';
            desiredCapabilities.deviceName = 'Google Nexus 6-5.0';
            desiredCapabilities.browser = desiredCapabilities.browser || 'android';
            desiredCapabilities.platform = 'ANDROID';
            if (browserstackParams.realMobile && browserstackParams.realMobile.toString() == 'true') {
              browserstackParams.appium_port = '5678';
              desiredCapabilities.udid = 'pqrstu';
              desiredCapabilities.orig_os = 'android';
            }
            break;
          case 'Samsung Galaxy S4':
            desiredCapabilities.mobile = {};
            desiredCapabilities.mobile.browser = 'mobile';
            desiredCapabilities.mobile.version = 'Samsung Galaxy S4-4.3';
            desiredCapabilities.deviceName = 'Samsung Galaxy S4-4.3';
            desiredCapabilities.browser = desiredCapabilities.browser || 'android';
            desiredCapabilities.platform = 'ANDROID';
            if (browserstackParams.realMobile && browserstackParams.realMobile.toString() == 'true') {
              browserstackParams.appium_port = '5678';
              desiredCapabilities.udid = 'pqrstu';
              desiredCapabilities.orig_os = 'android';
            }
            break;
          case 'Samsung Galaxy Tab 4':
            desiredCapabilities.mobile = {};
            desiredCapabilities.mobile.browser = 'mobile';
            desiredCapabilities.mobile.version = 'Samsung Galaxy Tab 4-4.4';
            desiredCapabilities.deviceName = 'Samsung Galaxy Tab 4-4.4';
            desiredCapabilities.browser = desiredCapabilities.browser || 'android';
            desiredCapabilities.platform = 'ANDROID';
            desiredCapabilities.orig_os = 'android';
            if (browserstackParams.realMobile && browserstackParams.realMobile.toString() == 'true') {
              browserstackParams.appium_port = '5678';
              desiredCapabilities.udid = 'pqrstu';
            }
            break;
          case 'Samsung Galaxy S5 Mini':
            desiredCapabilities.mobile = {};
            desiredCapabilities.mobile.browser = 'mobile';
            desiredCapabilities.mobile.version = 'Samsung Galaxy S5 Mini-4.4-720x1280';
            desiredCapabilities.deviceName = 'Samsung Galaxy S5 Mini-4.4';
            desiredCapabilities.browser = desiredCapabilities.browser || 'android';
            desiredCapabilities.platform = 'ANDROID';
            break;
          case 'Motorola Moto X 2nd Gen':
            desiredCapabilities.mobile = {};
            desiredCapabilities.mobile.browser = 'mobile';
            desiredCapabilities.mobile.version = 'Motorola Moto X 2nd Gen-5.0';
            desiredCapabilities.deviceName = 'Motorola Moto X 2nd Gen-5.0';
            desiredCapabilities.browser = desiredCapabilities.browser || 'android';
            desiredCapabilities.platform = 'ANDROID';
            if (browserstackParams.realMobile && browserstackParams.realMobile.toString() == 'true') {
              browserstackParams.appium_port = '5678';
              desiredCapabilities.udid = 'pqrstu';
              desiredCapabilities.orig_os = 'android';
            }
            break;
          case 'Google Pixel':
            desiredCapabilities.mobile = {};
            desiredCapabilities.mobile.browser = 'mobile';
            desiredCapabilities.mobile.version = 'Google Pixel-7.1';
            desiredCapabilities.deviceName = 'Google Pixel-7.1';
            desiredCapabilities.browser = desiredCapabilities.browser || 'android';
            desiredCapabilities.platform = 'ANDROID';
            desiredCapabilities.orig_os = 'android';
            if (browserstackParams.realMobile && browserstackParams.realMobile.toString() == 'true') {
              browserstackParams.appium_port = '5678';
              desiredCapabilities.udid = 'pqrstu';
            }
            break;
          case 'Google Pixel 5':
            desiredCapabilities.mobile = {}
            desiredCapabilities.mobile.browser = 'mobile';
            desiredCapabilities.mobile.version = 'Google Pixel 5-11.0';
            desiredCapabilities.deviceName = 'Google Pixel 5-11.0';
            desiredCapabilities.browser = desiredCapabilities.browser || 'android';
            desiredCapabilities.platform = 'ANDROID';
            desiredCapabilities.orig_os = "android";
            if (browserstackParams.realMobile && browserstackParams.realMobile.toString() == "true") {
              browserstackParams.appium_port = "5678";
              desiredCapabilities.udid = "pqrstu";
            }
            break;
          case 'Apple TV 4k':
              desiredCapabilities.mobile = {};
              desiredCapabilities.mobile.browser = 'appletv';
              desiredCapabilities.mobile.version = 'Apple TV 4k-15.6';
              desiredCapabilities.browser = desiredCapabilities.browser || 'appletv';
              desiredCapabilities.platform = 'MAC';
              desiredCapabilities.orig_os = 'tvos';
              if (browserstackParams.realMobile && browserstackParams.realMobile.toString() == 'true') {
                browserstackParams.appium_port = '1234';
                desiredCapabilities.udid = 'abcdxyz';
              }
              break;
        }
      }
      if (desiredCapabilities.app != undefined) {
        browserstackParams.appium_port = exports.APPIUM_PORT;
        browserstackParams['browserstack.video'] = 'false';
      }

      if (params && params.isPlaywrightAndroid) {
        desiredCapabilities.platform = 'android';
        desiredCapabilities.browser = 'chrome_android'
      }
      browserstackParams['browserstack.aws.save'] = 'bs-stag-selenium-logs-use/1a2b3c4d5e6f';
      desiredCapabilities.browserName = desiredCapabilities.browser.toLowerCase();
      desiredCapabilities.version = parseInt(desiredCapabilities.browser_version).toFixed(1).toString();
      if (!desiredCapabilities.deviceName && !browserstackParams['browserstack.chrome.driver'] && desiredCapabilities.browser.toLowerCase() === 'chrome') {
        if (desiredCapabilities.version === '42.0') {
          browserstackParams['browserstack.chrome.driver'] = '2.16';
        } else if (desiredCapabilities.version === '49.0') {
          browserstackParams['browserstack.chrome.driver'] = '2.22';
        } else if (desiredCapabilities.version === '60.0') {
          browserstackParams['browserstack.chrome.driver'] = '2.31';
        } else {
          browserstackParams['browserstack.chrome.driver'] = '2.20';
        }
      }
      if (!desiredCapabilities.deviceName && !browserstackParams['browserstack.edge.driver'] && desiredCapabilities.browser.toLowerCase() === 'edge' && parseInt(desiredCapabilities.browser_version.toString().split(' ')[0].trim(), 10) >= 79) {
        browserstackParams['browserstack.edge.driver'] = '79.0.309.18';
      }
      browserstackParams['browserstack.tunnel'] = browserstackParams['browserstack.local'] || browserstackParams['browserstack.tunnel'] || false;
      browserstackParams['browserstack.video'] = browserstackParams['browserstack.video'].toString();
      browserstackParams['browserstack.video.aws.key'] = 'awskey';
      browserstackParams['browserstack.video.aws.secret'] = 'awssecret';
      browserstackParams['browserstack.video.aws.s3bucket'] = 'videobucket';
      browserstackParams['browserstack.video.aws.region'] = 'videoregion';
      browserstackParams['browserstack.aws.key'] = 'awskey';
      browserstackParams['browserstack.aws.secret'] = 'awssecret';
      if (desiredCapabilities.selenium_version !== null) {
        browserstackParams['browserstack.selenium.jar.version'] = browserstackParams['browserstack.selenium_version'];
      }

      if (params && isTrueString(params.interactiveSession)) {
        browserstackParams['browserstack.peerServerUrl'] = 'https://preprod-chrome-repeater.bsstag.com:8082';
        browserstackParams['browserstack.useReplayKitForInteraction'] = true;
        browserstackParams['browserstack.useRtcApp'] = true;
        browserstackParams['browserstack.useRtcAppAudio'] = false;
        browserstackParams['browserstack.iceServers'] = [
          { url: 'turn:turn-live-004-ec2-use1d-prod.browserstack.com:443', credential: 'key2', username: 'username2' },
          { url: 'turn:turn-live-004-ec2-use1d-prod.browserstack.com:443?transport=tcp', credential: 'key2', username: 'username2' },
          { url: 'turn:turn-live-003-ec2-use1b-prod.browserstack.com:443', credential: 'key2', username: 'username2' },
          { url: 'turn:turn-live-003-ec2-use1b-prod.browserstack.com:443?transport=tcp', credential: 'key2', username: 'username2' },
          { url: 'stun:turn-live-003-ec2-use1b-prod.browserstack.com:443' },
          { url: 'stun:turn-live-003-ec2-use1b-prod.browserstack.com:443?transport=tcp' }
        ];
        browserstackParams['browserstack.webrtcSessionId'] = exports.SESSION_ID;
        browserstackParams['browserstack.webrtcStreaming'] = true;
        browserstackParams['browserstack.useInteractiveSessionRefreshFlow'] = true;
        browserstackParams['browserstack.video_v2_enabled'] = true;
      }

      if(desiredCapabilities.browser && desiredCapabilities.browser == "chromium"){
        desiredCapabilities.browser = "chromium_iphone"
        desiredCapabilities.browserName = "chromium_iphone"
      }

      if (params && params.extraAccess) {
        browserstackParams['extraAccess'] = params.extraAccess;
      }
      if (params && params.skip_platform_enterprise_flow) {
        browserstackParams['skip_platform_enterprise_flow'] = params.skip_platform_enterprise_flow;
      }
      if (desiredCapabilities.app == '') {
        return [200, { error: '[app] capability must be a valid BrowserStack App URL.' }];
      }
      if (desiredCapabilities.app && !browserstackParams.realMobile) {
        return [200, { error: 'Could not find device: Google Pixel' }];
      }

      return [200, {
        ip: exports.TERMINAL_IP,
        rproxy_host: exports.RPROXY_HOST,
        desiredCapabilities,
        browserstack_params: browserstackParams,
        automation_session_id: exports.SESSION_ID,
        collection_number: 1,
        group_id
      }];
    });
  }
  return railsScope;
};

exports.selenium = (os, seleniumVersion, forcePort) => {
  if (forcePort != null) {
    return nock(`http://${exports.RPROXY_HOST}:${forcePort}`);
  }
  if (seleniumVersion == null) {
    return nock(`http://${exports.RPROXY_HOST}:${constants.default_seleniumjar_port}`);
  }
  return nock(`http://${exports.RPROXY_HOST}:5555`);
};

exports.chromeDebugger = (portNumber = REMOTE_DEBUGGER_PORT) => nock(`http://${exports.RPROXY_HOST}:${portNumber}`);

exports.updateUploaderKey = function () {
  redisClientBuffer = exports.hubHelper.redisClient;
  redisClientBuffer.sadd(constants.APP_AUTOMATE_CURRENT_SESSIONS, exports.SESSION_ID);
  pubSub.publish(constants.updateKeyObject, {
    session: exports.SESSION_ID,
    changed: {
      uploaderKey: constants.APP_AUTOMATE_S3_UPLOADER_QUEUE
    }
  });
};

exports.nonAppium = () => nock(`http://${exports.RPROXY_HOST}:45693`);
exports.appiumMini = () => nock(`http://${exports.RPROXY_HOST}:1234`);
exports.appiumMiniRproxy = () => nock(`http://*************:1234`);
exports.appiumZotac = () => nock(`http://${exports.RPROXY_HOST}:5678`);
exports.buildMac = buildTerminal(45671);
exports.buildWin = buildTerminal(4567);
exports.hubHelper = hubHelper;
exports.buildMacRproxy = buildTerminalRproxy(45671);

const isZeroOrNull = arg => arg === null || typeof arg === 'undefined' || arg.toString() === '0';

const verifyPipelineCleared = () => new Promise((resolve, reject) => {
  hubHelper.redisClient.multi()
    .scard(constants.railsPipeline.redisAutomateTrackTokenHash.queueTrackRequestsSetTag)
    .scard(constants.railsPipeline.redisAppAutomateTrackTokenHash.queueTrackRequestsSetTag)
    .exec()
    .then((res) => {
      if (isZeroOrNull(res[0][1]) && isZeroOrNull(res[1][1]) && isZeroOrNull(res[2][1])) {
        resolve();
      } else {
        reject(`RailsPipeline not Cleared: Got response ${res}`);
      }
    });
});

before((done) => {
  hubHelper.removeFromGlobalRegistry({ rails_session_id: '1a2b3c4d5e6f' });
  hubHelper.resetPipelineQueue().then(done).catch(done);
});

afterEach((done) => {
  hubHelper.removeFromGlobalRegistry({ rails_session_id: '1a2b3c4d5e6f' });
  nock.cleanAll();
  done();
});

after((done) => {
  process.env.HUB_ENV = 'development';
  hubHelper.resetPipelineQueue().then(done).catch(done);
});
