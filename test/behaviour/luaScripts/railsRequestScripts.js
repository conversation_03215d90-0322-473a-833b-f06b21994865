'use strict';

const hubHelper = require('../../../helper');

const redis = hubHelper.redisClient;

const verifyPresentInSet =
  (member, set) => new Promise((resolve) => {
    redis.sismember([set, member])
      .then((isMember) => {
        isMember.should.equal(1);
        resolve();
      });
  });

describe('unregisterRailsRequest script', () => {
  const maxRailsRequestTag = 'test:maxRailsRequest';
  const maxRailsRequestAppAutomateTag = 'test:maxRailsRequestAppAutomate';
  const trackRequestsSetTag = 'test:requestTrackSet';
  const trackRequestsAppAutomateSetTag = 'test:requestTrackAppAutomateSet';
  const trackRequstID = 'test:requestID';

  beforeEach((done) => {
    redis.set([maxRailsRequestTag, 5])
      .then(() => { redis.set(maxRailsRequestAppAutomateTag, 5); })
      .then(() => { redis.sadd(trackRequestsSetTag, trackRequstID); })
      .then(() => { redis.sadd(trackRequestsAppAutomateSetTag, trackRequstID); })
      .then(() => { done(); });
  });

  afterEach((done) => {
    redis.flushall().then(() => {
      done();
    });
  });

  it('removes requestId from the automate set on unregister', (done) => {
    redis
      .unregisterRailsRequest([trackRequestsSetTag, trackRequstID])
      .then((res) => {
        res[0].should.equal(1); // signifies that requestId was present in set
        res[1].should.equal(0); // number of requests in system after unregister
        redis
          .sismember([trackRequestsSetTag, trackRequstID])
          .then((isMember) => {
            isMember.toString().should.equal('0');
            done();
          });
      });
  });

  it('removes requestId from the app automate set on unregister', (done) => {
    redis
      .unregisterRailsRequest([trackRequestsAppAutomateSetTag, trackRequstID])
      .then((res) => {
        res[0].should.equal(1); // signifies that requestId was present in set
        res[1].should.equal(0); // number of requests in system after unregister
        redis
          .sismember([trackRequestsAppAutomateSetTag, trackRequstID])
          .then((isMember) => {
            isMember.toString().should.equal('0');
            done();
          });
      });
  });
});

describe('registerRailsRequest script', () => {
  const maxRailsRequestTag = 'test:maxRailsRequest';
  const maxRailsRequestAppAutomateTag = 'test:maxRailsRequestAppAutomate';
  const trackRequestsSetTag = 'test:requestTrackSet';
  const trackRequestsAppAutomateSetTag = 'test:requestAppAutomateTrackSet';
  const trackRequstID = idNumber => (`test:requestID${idNumber}`);

  beforeEach((done) => {
    redis.set([maxRailsRequestTag, 5])
      .then(() => { redis.set(maxRailsRequestAppAutomateTag, 5); })
      .then(() => { done(); });
  });

  afterEach((done) => {
    redis.flushall().then(() => {
      done();
    });
  });

  it('returns needToQueue true in case of reaching threshold for automate', (done) => {
    const shouldNotQueuePromise = idNumber => new Promise((resolve) => {
      redis
        .registerRailsRequest([
          maxRailsRequestTag, trackRequestsSetTag, trackRequstID(idNumber),
        ]).then((res) => {
          res[0].should.equal(0);
          resolve();
        });
    });

    Promise.all([
      shouldNotQueuePromise(1), shouldNotQueuePromise(2), shouldNotQueuePromise(3),
      shouldNotQueuePromise(4), shouldNotQueuePromise(5),
    ]).then(() => {
      redis
        .registerRailsRequest([
          maxRailsRequestTag, trackRequestsSetTag, trackRequstID,
        ]).then((res) => {
          res[0].should.equal(1);
          hubHelper.resetPipelineQueue().then(done);
        });
    });
  });

  it('returns needToQueue true in case of reaching threshold for app automate', (done) => {
    // this.timeout(60000);
    const shouldNotQueuePromise = idNumber => new Promise((resolve) => {
      redis
        .registerRailsRequest([
          maxRailsRequestAppAutomateTag, trackRequestsAppAutomateSetTag, trackRequstID(idNumber),
        ]).then((res) => {
          res[0].should.equal(0);
          resolve();
        });
    });

    Promise.all([
      shouldNotQueuePromise(1), shouldNotQueuePromise(2), shouldNotQueuePromise(3),
      shouldNotQueuePromise(4), shouldNotQueuePromise(5),
    ]).then(() => {
      redis
        .registerRailsRequest([
          maxRailsRequestAppAutomateTag, trackRequestsAppAutomateSetTag, trackRequstID,
        ]).then((res) => {
          res[0].should.equal(1);
          hubHelper.resetPipelineQueue().then(done);
        });
    });
  });

  it('assumes 100 as max limit in case the key is unset', (done) => {
    redis
      .registerRailsRequest(['randomUnsetKey', trackRequestsSetTag, trackRequstID])
      .then((res) => {
        res[1].should.equal(100);
        done();
      });
  });

  it('replies with 3 numbers', (done) => {
    redis
      .registerRailsRequest([
        maxRailsRequestTag, trackRequestsSetTag, trackRequstID,
      ]).then((res) => {
        res.length.should.equal(3);
        done();
      });
  });

  it('increments track set size in redis as well as in response for automate', (done) => {
    redis
      .registerRailsRequest([
        maxRailsRequestTag, trackRequestsSetTag, trackRequstID,
      ]).then((res) => {
        res[2].should.equal(1);
        redis.scard(trackRequestsSetTag)
          .then((result) => {
            result.should.equal(1);
            done();
          });
      });
  });

  it('increments track set size in redis as well as in response for app automate', (done) => {
    redis
      .registerRailsRequest([
        maxRailsRequestTag, trackRequestsAppAutomateSetTag, trackRequstID,
      ]).then((res) => {
        res[2].should.equal(1);
        redis.scard(trackRequestsAppAutomateSetTag)
          .then((result) => {
            result.should.equal(1);
            done();
          });
      });
  });

  it('should add requestId in set for automate', (done) => {
    redis
      .registerRailsRequest([
        maxRailsRequestTag, trackRequestsSetTag, trackRequstID,
      ]).then(() => {
        verifyPresentInSet(trackRequstID, trackRequestsSetTag)
          .then(done);
      });
  });

  it('should add requestId in set for app automate', (done) => {
    redis
      .registerRailsRequest([
        maxRailsRequestTag, trackRequestsAppAutomateSetTag, trackRequstID,
      ]).then(() => {
        verifyPresentInSet(trackRequstID, trackRequestsAppAutomateSetTag)
          .then(done);
      });
  });

  it('should add requestId in set even if requestId is already present for automate', (done) => {
    redis.sadd([trackRequestsSetTag, trackRequstID])
      .then(() => {
        redis
          .registerRailsRequest([
            maxRailsRequestTag, trackRequestsSetTag, trackRequstID,
          ]).then(() => {
            verifyPresentInSet(trackRequstID, trackRequestsSetTag)
              .then(done);
          });
      });
  });

  it('should add requestId in set even if requestId is already present for app automate', (done) => {
    redis.sadd([trackRequestsAppAutomateSetTag, trackRequstID])
      .then(() => {
        redis
          .registerRailsRequest([
            maxRailsRequestTag, trackRequestsAppAutomateSetTag, trackRequstID,
          ]).then(() => {
            verifyPresentInSet(trackRequstID, trackRequestsAppAutomateSetTag)
              .then(done);
          });
      });
  });
});
