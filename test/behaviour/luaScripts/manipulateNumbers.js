'use strict';

const fs = require('fs');
const path = require('path');
// eslint-disable-next-line no-unused-vars
const chai = require('chai').should(); // provides .should method

const hubHelper = require('../../../helper');
const hubConstants = require('../../../constants');

const redis = hubHelper.redisClient;

const initializeLuaScript = (callback) => {
  const luaScriptsInfo = hubConstants.manipulateNumberScripts;

  Object.keys(luaScriptsInfo).forEach((scriptName) => {
    let luaScriptContent = '';
    luaScriptContent = fs.readFileSync(`${path.join(__dirname, '/../../../')}/script/lua_scripts/${luaScriptsInfo[scriptName].scriptPath}`);

    redis.defineCommand(scriptName, {
      numberOfKeys: luaScriptsInfo[scriptName].numberOfKeys,
      lua: luaScriptContent,
    });
    callback();
  });
};

describe('manipulateNumbers script', () => {
  before((done) => {
    initializeLuaScript(done);
  });

  const maxRequestsToken = 'test:maxRequests';

  describe('multiply action', () => {
    it('should multiply the number by the factor properly', (done) => {
      redis.pipeline()
        .set(maxRequestsToken, 128)
        .manipulateNumber(maxRequestsToken, 'multiply', 2, 2, 256)
        .get(maxRequestsToken)
        .exec()
        .then((res) => {
          res[1][1][1].should.eql(128);
          res[1][1][2].should.eql(256);
          res[2][1].should.eql('256');
          done();
        })
        .catch((err) => {
          done(err);
        });
    });

    it('should set the value to the max threshold on multiply', (done) => {
      redis.pipeline()
        .set(maxRequestsToken, 128)
        .manipulateNumber(maxRequestsToken, 'multiply', 2, 2, 130)
        .get(maxRequestsToken)
        .exec()
        .then((res) => {
          res[1][1][1].should.eql(128);
          res[1][1][2].should.eql(130);
          res[2][1].should.eql('130');
          done();
        })
        .catch((err) => {
          done(err);
        });
    });

    it('should set the value to minimum if undefined', (done) => {
      redis.pipeline()
        .del(maxRequestsToken)
        .manipulateNumber(maxRequestsToken, 'multiply', 123, 2, 130)
        .get(maxRequestsToken)
        .exec()
        .then((res) => {
          res[1][1][1].should.eql(0);
          res[1][1][2].should.eql(2);
          res[2][1].should.eql('2');
          done();
        })
        .catch((err) => {
          done(err);
        });
    });

    it('should give ceil for the value', (done) => {
      redis.pipeline()
        .set(maxRequestsToken, 2)
        .manipulateNumber(maxRequestsToken, 'multiply', 1.1, 2, 130)
        .get(maxRequestsToken)
        .exec()
        .then((res) => {
          res[1][1][1].should.eql(2);
          res[1][1][2].should.eql(3);
          res[2][1].should.eql('3');
          done();
        })
        .catch((err) => {
          done(err);
        });
    });
  });

  describe('divide action', () => {
    it('should divide the number by the factor properly', (done) => {
      redis.pipeline()
        .set(maxRequestsToken, 128)
        .manipulateNumber(maxRequestsToken, 'divide', 2, 2, 256)
        .get(maxRequestsToken)
        .exec()
        .then((res) => {
          res[1][1][1].should.eql(128);
          res[1][1][2].should.eql(64);
          res[2][1].should.eql('64');
          done();
        })
        .catch((err) => {
          done(err);
        });
    });

    it('should set the value to the min threshold on divide', (done) => {
      redis.pipeline()
        .set(maxRequestsToken, 128)
        .manipulateNumber(maxRequestsToken, 'divide', 2, 120, 256)
        .get(maxRequestsToken)
        .exec()
        .then((res) => {
          res[1][1][1].should.eql(128);
          res[1][1][2].should.eql(120);
          res[2][1].should.eql('120');
          done();
        })
        .catch((err) => {
          done(err);
        });
    });

    it('should set the value to min if undefined', (done) => {
      redis.pipeline()
        .del(maxRequestsToken)
        .manipulateNumber(maxRequestsToken, 'divide', 123, 2, 256)
        .get(maxRequestsToken)
        .exec()
        .then((res) => {
          res[1][1][1].should.eql(0);
          res[1][1][2].should.eql(2);
          res[2][1].should.eql('2');
          done();
        })
        .catch((err) => {
          done(err);
        });
    });

    it('should give ceil value on divide', (done) => {
      redis.pipeline()
        .set(maxRequestsToken, 131)
        .manipulateNumber(maxRequestsToken, 'divide', 2, 2, 256)
        .get(maxRequestsToken)
        .exec()
        .then((res) => {
          res[1][1][1].should.eql(131);
          res[1][1][2].should.eql(66);
          res[2][1].should.eql('66');
          done();
        })
        .catch((err) => {
          done(err);
        });
    });
  });
});
