const helper = require('./helper');
const HubLogger = require('../../log');
const requestlib = require('../../lib/request');
const helperFn = require('../../helper');
const sinon = require('sinon');
const assert = require('chai').assert;
const expect = require('chai').expect;
const { REMOTE_DEBUGGER_PORT } = require('../../config/socketConstants');

describe('Fire Commands', function() {
  describe('Behaviour', function() {
    it('sends fireCommand to terminal on success response from rails', function(done) {
      var requestBody = helper.getCapabilities(),
          railsScope = helper.buildRails(),
          macTerminalScope = helper.buildMac({
            action: function(options) {
              options.chromeDriverVersion.should.equal('2.20');
              options.terminal_ip.should.equal(helper.TERMINAL_IP);
              return [200, { done: true }];
            }
          });

      var seleniumScope = helper.selenium()
      .post('/wd/hub/session')
      .query(true)
      .reply(function(uri, body) {
        return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
      });

      helper.request.post('/wd/hub/session')
      .auth('test', 'test')
      .send(requestBody)
      .end(function(err, res) {
        railsScope.isDone().should.be.true;
        macTerminalScope.isDone().should.be.true;
        seleniumScope.isDone().should.be.true;
        res.status.should.equal(200);
        res.body.value.should.have.any.keys('browser', 'browser_version', 'os', 'os_version');
        res.body.sessionId.should.equal(helper.SESSION_ID);
        res.body.status.should.equal(0);
        done();
      });
    });

    it('Add prerun in fire_cmd if prerunFile cap is present', function(done) {
      var requestBody = helper.getCapabilities(),
          railsScope = helper.buildRails(),
          macTerminalScope = helper.buildMac({
            action: function(options) {
              options.chromeDriverVersion.should.equal('2.20');
              options.terminal_ip.should.equal(helper.TERMINAL_IP);
              return [200, { done: true }];
            }
          });
      requestBody.desiredCapabilities['prerunFile'] = "test";
      var seleniumScope = helper.selenium()
      .post('/wd/hub/session')
      .query(true)
      .reply(function(uri, body) {
        return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
      });
      const encodedURLFn = sinon.spy(requestlib, 'getEncodedURLParams');
      helper.request.post('/wd/hub/session')
      .auth('test', 'test')
      .send(requestBody)
      .end(function(err, res) {
        railsScope.isDone().should.be.true;
        macTerminalScope.isDone().should.be.true;
        seleniumScope.isDone().should.be.true;
        res.status.should.equal(200);
        res.body.value.should.have.any.keys('browser', 'browser_version', 'os', 'os_version');
        expect(encodedURLFn.calledOnce);
        const fireCommandArguments = encodedURLFn.getCall(0).args[0];
        res.body.sessionId.should.equal(helper.SESSION_ID);
        assert.ok(fireCommandArguments.prerun !== undefined);
        res.body.status.should.equal(0);
        encodedURLFn.restore();
        done();
      });
    });

    it('Add geo_comply_app in fire_cmd if geoComplyApp cap is present', function(done) {
      var requestBody = helper.getCapabilities(),
          railsScope = helper.buildRails(),
          macTerminalScope = helper.buildMac({
            action: function(options) {
              options.chromeDriverVersion.should.equal('2.20');
              options.terminal_ip.should.equal(helper.TERMINAL_IP);
              return [200, { done: true }];
            }
          });
      requestBody.desiredCapabilities['browserstack.geoComplyApp'] = true;
      var seleniumScope = helper.selenium()
      .post('/wd/hub/session')
      .query(true)
      .reply(function(uri, body) {
        return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
      });
      const encodedURLFn = sinon.spy(requestlib, 'getEncodedURLParams');
      helper.request.post('/wd/hub/session')
      .auth('test', 'test')
      .send(requestBody)
      .end(function(err, res) {
        railsScope.isDone().should.be.true;
        macTerminalScope.isDone().should.be.true;
        seleniumScope.isDone().should.be.true;
        res.status.should.equal(200);
        res.body.value.should.have.any.keys('browser', 'browser_version', 'os', 'os_version');
        expect(encodedURLFn.calledOnce);
        const fireCommandArguments = encodedURLFn.getCall(0).args[0];
        res.body.sessionId.should.equal(helper.SESSION_ID);
        assert.ok(fireCommandArguments.geo_comply_app !== undefined);
        res.body.status.should.equal(0);
        encodedURLFn.restore();
        done();
      });
    });

    it('Add safari_plugin in fire_cmd if safariPluginFiles cap is present', function(done) {
      var requestBody = helper.getCapabilities(),
          railsScope = helper.buildRails(),
          macTerminalScope = helper.buildMac({
            action: function(options) {
              options.chromeDriverVersion.should.equal('2.20');
              options.terminal_ip.should.equal(helper.TERMINAL_IP);
              return [200, { done: true }];
            }
          });
      requestBody.desiredCapabilities['safariPluginFiles'] = "test";
      var seleniumScope = helper.selenium()
      .post('/wd/hub/session')
      .query(true)
      .reply(function(uri, body) {
        return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
      });
      const encodedURLFn = sinon.spy(requestlib, 'getEncodedURLParams');
      helper.request.post('/wd/hub/session')
      .auth('test', 'test')
      .send(requestBody)
      .end(function(err, res) {
        railsScope.isDone().should.be.true;
        macTerminalScope.isDone().should.be.true;
        seleniumScope.isDone().should.be.true;
        res.status.should.equal(200);
        res.body.value.should.have.any.keys('browser', 'browser_version', 'os', 'os_version');
        expect(encodedURLFn.calledOnce);
        const fireCommandArguments = encodedURLFn.getCall(0).args[0];
        res.body.sessionId.should.equal(helper.SESSION_ID);
        assert.ok(fireCommandArguments.safari_plugin !== undefined);
        res.body.status.should.equal(0);
        encodedURLFn.restore();
        done();
      });
    });

    it('Add browser_profiling in fire_cmd if browserProfiling cap is present', function(done) {
      var requestBody = helper.getCapabilities(),
          railsScope = helper.buildRails(),
          macTerminalScope = helper.buildMac({
            action: function(options) {
              options.chromeDriverVersion.should.equal('2.20');
              options.terminal_ip.should.equal(helper.TERMINAL_IP);
              return [200, { done: true }];
            }
          });
      requestBody.desiredCapabilities['browserstack.browserProfiling'] = true;
      var seleniumScope = helper.selenium()
      .post('/wd/hub/session')
      .query(true)
      .reply(function(uri, body) {
        return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
      });
      const encodedURLFn = sinon.spy(requestlib, 'getEncodedURLParams');
      helper.request.post('/wd/hub/session')
      .auth('test', 'test')
      .send(requestBody)
      .end(function(err, res) {
        railsScope.isDone().should.be.true;
        macTerminalScope.isDone().should.be.true;
        seleniumScope.isDone().should.be.true;
        res.status.should.equal(200);
        res.body.value.should.have.any.keys('browser', 'browser_version', 'os', 'os_version');
        expect(encodedURLFn.calledOnce);
        const fireCommandArguments = encodedURLFn.getCall(0).args[0];
        res.body.sessionId.should.equal(helper.SESSION_ID);
        assert.ok(fireCommandArguments.browser_profiling !== undefined);
        assert.ok(fireCommandArguments.browser_profiling_freq !== undefined);
        res.body.status.should.equal(0);
        encodedURLFn.restore();
        done();
      });
    });

    describe('detox start flow', () => {
      it('should return dummySeleniumResponse when isDetox is true', (done) => {
        const requestBody = helper.AppAutomateCapabilities({"app": "bs://testapp", "browserstack.detoxAndroidClient": ["bs://detoxAndroidClient"], "isDetox" : "true"});
        const railsScope = helper.buildRails();
        const appAutomateRailsScope = helper.buildRails({isAppAutomate: true});
        const macTerminalScope = helper.buildMac({
          action: function(options) {
            return [200, { done: true, detox_server_url: 'ws://121.0.01:1234' }];
          }
        });

        const seleniumScope = helper.selenium(undefined, undefined, helper.APPIUM_PORT)
          .post('/wd/hub/session')
          .query(true)
          .reply(function(uri, body) {
            return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
          });

        helper.request.post('/wd/hub/session')
          .auth('test', 'test')
          .send(requestBody)
          .end(function(_, res) {
            railsScope.isDone().should.equal(false);
            appAutomateRailsScope.isDone().should.equal(true);
            macTerminalScope.isDone().should.be.true;
            seleniumScope.isDone().should.equal(false);
            res.status.should.equal(200);
            res.body.value.should.have.any.keys('wsURL', 'wsHostname');
            assert.equal(res.body.value.wsURL, "ws://121.0.01:1234");
            res.body.sessionId.should.equal(helper.SESSION_ID);
            res.body.status.should.equal(0);
            done();
          });
      });

      it('should not return dummySeleniumResponse when isDetox is not set', (done) => {
        const requestBody = helper.AppAutomateCapabilities({"app": "bs://testapp", "browserstack.detoxAndroidClient": ["bs://detoxAndroidClient"]});
        const railsScope = helper.buildRails();
        const appAutomateRailsScope = helper.buildRails({isAppAutomate: true});
        const macTerminalScope = helper.buildMac({
          action: function(options) {
            return [200, { done: true, detox_server_url: 'ws://121.0.01:1234' }];
          }
        });

        const seleniumScope = helper.selenium(undefined, undefined, helper.APPIUM_PORT)
          .post('/wd/hub/session')
          .query(true)
          .reply(function(uri, body) {
            return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
          });

        helper.request.post('/wd/hub/session')
          .auth('test', 'test')
          .send(requestBody)
          .end(function(_, res) {
            railsScope.isDone().should.equal(false);
            appAutomateRailsScope.isDone().should.equal(true);
            macTerminalScope.isDone().should.be.true;
            seleniumScope.isDone().should.equal(true);
            res.status.should.equal(200);
            res.body.sessionId.should.equal(helper.SESSION_ID);
            res.body.status.should.equal(0);
            done();
          });
      });

      it('should not return wsurl in dummySeleniumResponse when detoxURL is not sent in firecmd', (done) => {
          const requestBody = helper.AppAutomateCapabilities({"app": "bs://testapp", "browserstack.detoxAndroidClient": ["bs://detoxAndroidClient"], "isDetox" : "true"});
          const railsScope = helper.buildRails();
          const appAutomateRailsScope = helper.buildRails({isAppAutomate: true});
          const macTerminalScope = helper.buildMac({
            action: function(options) {
              return [200, { done: true }];
            }
          });

          const seleniumScope = helper.selenium(undefined, undefined, helper.APPIUM_PORT)
            .post('/wd/hub/session')
            .query(true)
            .reply(function(uri, body) {
              return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
            });

          helper.request.post('/wd/hub/session')
            .auth('test', 'test')
            .send(requestBody)
            .end(function(_, res) {
              railsScope.isDone().should.equal(false);
              appAutomateRailsScope.isDone().should.equal(true);
              macTerminalScope.isDone().should.be.true;
              seleniumScope.isDone().should.equal(false);
              assert.deepEqual(res.body.value, {});
              res.status.should.equal(200);
              res.body.sessionId.should.equal(helper.SESSION_ID);
              res.body.status.should.equal(0);
              done();
            });
      });
    });

    describe('puppeteer start flow', () => {
      beforeEach(() => {
        sinon.spy(HubLogger, 'nodeErrorHandler');
        sinon.stub(HubLogger, 'addStopToRawLogs', () => {});
      });

      afterEach(() => {
        HubLogger.nodeErrorHandler.restore();
        HubLogger.addStopToRawLogs.restore();
      });

      it('should fetch the debugger port url from chrome', (done) => {
        const requestBody = helper.getCapabilities({
          isPuppeteer: true,
          chromeOptions: {
            args: [`--remote-debugging-port=${REMOTE_DEBUGGER_PORT}`],
          }
        });
        const railsScope = helper.buildRails();
        const macTerminalScope = helper.buildMac({
          isPuppeteer: true,
          action: function(options) {
            options = JSON.parse(options);
            assert(options.chromeDriverVersion, '2.20');
            assert(options.terminal_ip, helper.TERMINAL_IP);
            return [200, { done: true }];
          }
        });

        const seleniumScope = helper.selenium()
          .post('/wd/hub/session')
          .query(true)
          .reply(function(_, body) {
            return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
          });

        const remoteDebuggerScope = helper.chromeDebugger()
          .get('/json/version')
          .query(true)
          .reply(() => {
            return [200, { webSocketDebuggerUrl: 'ws://localhost:9222' }];
          });

        helper.request.post('/wd/hub/session')
          .auth('test', 'test')
          .send(requestBody)
          .end(function(_, res) {
            railsScope.isDone().should.be.true;
            macTerminalScope.isDone().should.be.true;
            seleniumScope.isDone().should.be.true;
            remoteDebuggerScope.isDone().should.be.true;
            res.status.should.equal(200);
            res.body.value.should.have.any.keys('browser', 'browser_version', 'os', 'os_version');
            res.body.value.should.have.any.keys('wsURL', 'wsHostname');
            res.body.sessionId.should.equal(helper.SESSION_ID);
            res.body.status.should.equal(0);
            done();
          });
      });

      it('should form the websocket url from rproxy host', (done) => {
        const requestBody = helper.getCapabilities({
          isPuppeteer: true,
          chromeOptions: {
            args: [`--remote-debugging-port=${REMOTE_DEBUGGER_PORT}`],
          }
        });
        const railsScope = helper.buildRails();
        const macTerminalScope = helper.buildMac({
          isPuppeteer: true,
          action: function(options) {
            options = JSON.parse(options);
            assert(options.chromeDriverVersion, '2.20');
            assert(options.terminal_ip, helper.TERMINAL_IP);
            return [200, { done: true }];
          }
        });

        const seleniumScope = helper.selenium()
          .post('/wd/hub/session')
          .query(true)
          .reply(function(_, body) {
            return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
          });

        const remoteDebuggerScope = helper.chromeDebugger()
          .get('/json/version')
          .query(true)
          .reply(() => {
            return [200, { webSocketDebuggerUrl: 'ws://rproxy-euw.bsstag.com/devtools/browsers/random-id' }];
          });

        helper.request.post('/wd/hub/session')
          .auth('test', 'test')
          .send(requestBody)
          .end(function(_, res) {
            railsScope.isDone().should.be.true;
            macTerminalScope.isDone().should.be.true;
            seleniumScope.isDone().should.be.true;
            remoteDebuggerScope.isDone().should.be.true;
            res.status.should.equal(200);
            res.body.value.should.have.any.keys('browser', 'browser_version', 'os', 'os_version');
            res.body.value.should.have.any.keys('wsURL', 'wsHostname');
            res.body.sessionId.should.equal(helper.SESSION_ID);
            res.body.status.should.equal(0);
            done();
          });
      });

      it('should trigger the start error flow in puppeteer when unable to fetch data from terminal', (done) => {
        const requestBody = helper.getCapabilities({
          isPuppeteer: true,
          chromeOptions: {
            args: [`--remote-debugging-port=${REMOTE_DEBUGGER_PORT}`],
          }
        });
        const railsScope = helper.buildRails();
        const macTerminalScope = helper.buildMac({
          isPuppeteer: true,
          action: function(options) {
            options = JSON.parse(options);
            assert(options.chromeDriverVersion, '2.20');
            assert(options.terminal_ip, helper.TERMINAL_IP);
            return [200, { done: true }];
          }
        });

        const seleniumScope = helper.selenium()
          .post('/wd/hub/session')
          .query(true)
          .reply(function(_, body) {
            return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
          });

        const remoteDebuggerScope = helper.chromeDebugger()
          .get('/json/version')
          .query(true)
          .reply(() => {
            return [404, "No such url found"];
          });

        helper.request.post('/wd/hub/session')
          .auth('test', 'test')
          .send(requestBody)
          .end(function(_, res) {
            railsScope.isDone().should.be.true;
            macTerminalScope.isDone().should.be.true;
            seleniumScope.isDone().should.be.true;
            remoteDebuggerScope.isDone().should.be.true;
            res.status.should.equal(200);
            expect(HubLogger.nodeErrorHandler.calledOnce).to.be.true;
            expect(res.body.value.message).to.be.eql('Could not start Browser / Emulator');
            res.body.status.should.equal(13);
            done();
          });
      });
    });

    describe('interactive session mobile', () => {
      const RTC_KEYS = [ 'peer_server_url', 'webrtc_session_id', 'ice_servers'];

      it('If railsApp sent the params then only initialize in firecmd', (done) => {
        const requestBody = helper.getCapabilities({
          browser: 'Safari',
          device: 'iPad Mini 3',
          realMobile: true,
          acceptSslCerts: true
        });
        const railsScope = helper.buildRails({ interactiveSession: true });
        const macTerminalScope = helper.buildMac({
          action: function(options) {
            try {
              expect(options).to.include.all.keys(...RTC_KEYS);
            } catch (err) {
              done(err);
            }
            return [200, { done: true }];
          }
        });

        const appiumScope = helper.appiumMini()
          .post('/wd/hub/session')
          .query(true)
          .reply(function(_, body) {
            return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
          });

        helper.request.post('/wd/hub/session')
          .auth('test', 'test')
          .send(requestBody)
          .end(function(_, res) {
            railsScope.isDone().should.be.true;
            macTerminalScope.isDone().should.be.true;
            appiumScope.isDone().should.be.true;
            res.status.should.equal(200);
            res.body.status.should.equal(0);
            done();
          });
      });

      it('Do not proxy if rails did not allow interactive session', (done) => {
        const requestBody = helper.getCapabilities({
          browser: 'Safari',
          device: 'iPad Mini 3',
          realMobile: true,
          acceptSslCerts: true
        });
        const railsScope = helper.buildRails();
        const macTerminalScope = helper.buildMac({
          action: function(options) {
            try {
              expect(options).to.not.include.all.keys(...RTC_KEYS);
            } catch (err) {
              done(err);
            }
            return [200, { done: true }];
          }
        });

        const appiumScope = helper.appiumMini()
          .post('/wd/hub/session')
          .query(true)
          .reply(function(_, body) {
            return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
          });

        helper.request.post('/wd/hub/session')
          .auth('test', 'test')
          .send(requestBody)
          .end(function(_, res) {
            railsScope.isDone().should.be.true;
            macTerminalScope.isDone().should.be.true;
            appiumScope.isDone().should.be.true;
            res.status.should.equal(200);
            res.body.status.should.equal(0);
            done();
          });
      });
    });

    it('should add acceptSslCert for playwright android', function(done) {
      var requestBody = helper.getCapabilities({
        isPlaywright: true,
        device: 'Google Pixel 5',
        'browserstack.acceptInsecureCerts': 'true',
        realMobile: true,
      });

      var railsScope = helper.buildRails({isPlaywrightAndroid: true});
          macTerminalScope = helper.buildMac({
            action: function(options) {
              return [200, { done: true, playwright_url: 'ws://localhost:9222', platform: 'android' }];
            }
          });

      var seleniumScope = helper.selenium()
        .post('/wd/hub/session')
        .query(true)
        .reply(function(uri, body) {
          return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
        });

      const encodedURLFn = sinon.spy(requestlib, 'getEncodedURLParams');

      helper.request.post('/wd/hub/session')
        .auth('test', 'test')
        .send(requestBody)
        .end(function(err, res) {
          railsScope.isDone().should.be.true;
          macTerminalScope.isDone().should.be.true;
          seleniumScope.isDone().should.be.false;
          res.status.should.equal(200);
          res.body.value.should.have.all.keys('wsURL', 'wsHostname');
          res.body.sessionId.should.equal(helper.SESSION_ID);
          res.body.status.should.equal(0);

          expect(encodedURLFn.calledOnce);
          const fireCommandArguments = encodedURLFn.getCall(0).args[0];
          assert(fireCommandArguments.acceptInsecureCerts, true);
          encodedURLFn.restore();

          done();
        });
    });

    it('should add networkLogs for playwright android', function(done) {
      var requestBody = helper.getCapabilities({
        isPlaywright: true,
        device: 'Google Pixel 5',
        'browserstack.networkLogs': 'true',
        realMobile: true,
      });

      var railsScope = helper.buildRails({isPlaywrightAndroid: true});
          macTerminalScope = helper.buildMac({
            action: function(options) {
              return [200, { done: true, playwright_url: 'ws://localhost:9222', platform: 'android' }];
            }
          });

      var seleniumScope = helper.selenium()
        .post('/wd/hub/session')
        .query(true)
        .reply(function(uri, body) {
          return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
        });

      const encodedURLFn = sinon.spy(requestlib, 'getEncodedURLParams');

      helper.request.post('/wd/hub/session')
        .auth('test', 'test')
        .send(requestBody)
        .end(function(err, res) {
          railsScope.isDone().should.be.true;
          macTerminalScope.isDone().should.be.true;
          seleniumScope.isDone().should.be.false;
          res.status.should.equal(200);
          res.body.value.should.have.all.keys('wsURL', 'wsHostname');
          res.body.sessionId.should.equal(helper.SESSION_ID);
          res.body.status.should.equal(0);

          expect(encodedURLFn.calledOnce);
          const fireCommandArguments = encodedURLFn.getCall(0).args[0];
          assert(fireCommandArguments.networkLogs, true);
          encodedURLFn.restore();

          done();
        });
    });

    it('sends fireCommand to terminal on success response from rails even after retry', function(done) {
      var requestBody = helper.getCapabilities(),
          railsScope1 = helper.buildRails({error: true}),
          railsScope2 = helper.buildRails(),
          macTerminalScope = helper.buildMac({
            action: function(options) {
              options.chromeDriverVersion.should.equal('2.20');
              options.terminal_ip.should.equal(helper.TERMINAL_IP);
              return [200, { done: true }];
            }
          });

      var seleniumScope = helper.selenium()
      .post('/wd/hub/session')
      .query(true)
      .reply(function(uri, body) {
        return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
      });

      helper.request.post('/wd/hub/session')
      .auth('test', 'test')
      .send(requestBody)
      .end(function(err, res) {
        railsScope1.isDone().should.be.true;
        railsScope2.isDone().should.be.true;
        macTerminalScope.isDone().should.be.true;
        seleniumScope.isDone().should.be.true;
        res.status.should.equal(200);
        res.body.value.should.have.any.keys('browser', 'browser_version', 'os', 'os_version');
        res.body.sessionId.should.equal(helper.SESSION_ID);
        res.body.status.should.equal(0);
        done();
      });
    });

    it('gets port in response from firecommand', function(done) {
      var requestBody = helper.getCapabilities(),
        usePort = 6557,
        macTerminalScope = helper.buildMac({
          action: [200, {done:true, port:usePort}],
        }),
      seleniumNock = helper.selenium(undefined, undefined, usePort);
      var capability = helper.getCapabilities({
          browserName: 'chrome',
          browser_version: '66',
          os: 'OS X',
          os_version: 'Sierra'
        });
      helper.doSelenium.startSession(capability, seleniumNock)
        .then(function(res) {
          res.status.should.equal(200);
          res.body.value.should.have.any.keys('browser', 'browser_version', 'os', 'os_version');
          res.body.sessionId.should.equal(helper.SESSION_ID);
          res.body.status.should.equal(0);
          done();
        });
    });

    it('loads accessibility extension and opens devtools when accessibilityOptions capabilities are flattened', function(done) {
      const accessibilityExtensionPath = "/Users/<USER>/Downloads/accessibility-toolkit/1.0.0.0-preprod";
      var macTerminalScope = helper.buildMac({
        action: [200, {done: true, accessibilityExtensionPath: accessibilityExtensionPath}],
      });
      seleniumNock = helper.selenium();
      var capability = helper.getCapabilities({
          browserName: 'chrome',
          browser_version: '114',
          os: 'OS X',
          os_version: 'Big Sur',
        'browserstack.accessibilityOptions.authToken': "eyJhbGciOiJIUzI1NiJ9.eyJ1c2VyX2lkIjo1MzAsInRlc3RfcnVuX2lkIjoxOTEyLCJhMTF5X2NvcmVfY29uZmlnIjp7ImRpc2FibGVfYTExeV9lbmdpbmVfY29uZmlnIjpmYWxzZSwiZGlzYWJsZV9hMTF5X2VuZ2luZV9leHBlcmltZW50YWxfcnVsZXMiOmZhbHNlLCJkb21Gb3JnZSI6dHJ1ZSwiZXhjbHVkZV9ydWxlcyI6W10sImFkdmFuY2VkIjp0cnVlfSwic2Nhbm5lcl92ZXJzaW9uIjoiMS4wLjAuMC1wcmVwcm9kIiwiYWR2YW5jZWRfc2Nhbl90aW1lb3V0IjoxNSwicmN0IjoyNSwiYXV0b21hdGlvbiI6dHJ1ZSwiZXhwIjoxNzI4MjA2NTMyfQ.o0-uCCWMiig4n9L-TA06vFaqJjS3Hlpim-zGfPc-f5Q"
        });
      helper.doSelenium.startSession(capability, seleniumNock).then(function(res) {
        const args = JSON.parse(res.text)['value']['chromeOptions']['args'];
        expect(args).to.include(`--load-extension=${accessibilityExtensionPath}`);
        expect(args).to.include(`--auto-open-devtools-for-tabs`);
        const devtoolsPrefs = JSON.parse(res.text)['value']['chromeOptions']['prefs']['devtools']['preferences'];
        expect(devtoolsPrefs['currentDockState']).to.equal("\"undocked\"");
        expect(devtoolsPrefs['last-dock-state']).to.equal("\"undocked\"");
        done();
      });
    });

    it('loads accessibility extension when accessibilityOptions capability is Hash and does not open devtools if domForge is false', function (done) {
      const accessibilityExtensionPath = "/Users/<USER>/Downloads/accessibility-toolkit/1.0.0.0-preprod";
      var macTerminalScope = helper.buildMac({
        action: [200, { done: true, accessibilityExtensionPath: accessibilityExtensionPath }],
      });
      seleniumNock = helper.selenium();
      var capability = helper.getCapabilities({
        browserName: 'chrome',
        browser_version: '114',
        os: 'OS X',
        os_version: 'Big Sur',
        'browserstack.accessibilityOptions': {"authToken": "eyJhbGciOiJIUzI1NiJ9.eyJ1c2VyX2lkIjo0LCJ0ZXN0X3J1bl9pZCI6MTQ2OSwiYTExeV9jb3JlX2NvbmZpZyI6eyJkaXNhYmxlX2ExMXlfZW5naW5lX2NvbmZpZyI6ZmFsc2UsImRpc2FibGVfYTExeV9lbmdpbmVfZXhwZXJpbWVudGFsX3J1bGVzIjpmYWxzZSwiZG9tRm9yZ2UiOmZhbHNlLCJleGNsdWRlX3J1bGVzIjpbXSwiYWR2YW5jZWQiOnRydWV9LCJzY2FubmVyX3ZlcnNpb24iOiIxLjAuMC4wLXFhIiwiYWR2YW5jZWRfc2Nhbl90aW1lb3V0IjoxNSwicmN0IjoyNSwiYXV0b21hdGlvbiI6dHJ1ZSwiZXhwIjoxNzI4MTMyMDc1fQ.hH0zFuoBSCm8DDo0fMBxQt4xKaJzc3WddPvsjkaSsZ4"}
      });
      helper.doSelenium.startSession(capability, seleniumNock).then(function (res) {
        const args = JSON.parse(res.text)['value']['chromeOptions']['args'];
        expect(args).to.include(`--load-extension=${accessibilityExtensionPath}`);
        expect(args).to.not.include(`--auto-open-devtools-for-tabs`);
        const devtoolsPrefs = JSON.parse(res.text)['value']['chromeOptions']['prefs']['devtools']['preferences'];
        expect(devtoolsPrefs['currentDockState']).to.not.equal("\"undocked\"");
        expect(devtoolsPrefs['last-dock-state']).to.not.equal("\"undocked\"");
        done();
      });
    });

    it('loads accessibility extension and does not open devtools if domForge not passed', function(done) {
      const accessibilityExtensionPath = "/Users/<USER>/Downloads/accessibility-toolkit/0.4.11.0";
      var macTerminalScope = helper.buildMac({
        action: [200, {done: true, accessibilityExtensionPath: accessibilityExtensionPath}],
      });
      seleniumNock = helper.selenium();
      var capability = helper.getCapabilities({
          browserName: 'chrome',
          browser_version: '114',
          os: 'OS X',
          os_version: 'Big Sur',
        'browserstack.accessibilityOptions.authToken': "eyJhbGciOiJIUzI1NiJ9.eyJ1c2VyX2lkIjo3NzA3NjcyLCJ0ZXN0X3J1bl9pZCI6ODQ1MjEsImF1dG9tYXRpb24iOnRydWUsImExMXlfY29yZV9jb25maWciOnsiZGlzYWJsZV9hMTF5X2VuZ2luZV9jb25maWciOmZhbHNlLCJkaXNhYmxlX2ExMXlfZW5naW5lX2V4cGVyaW1lbnRhbF9ydWxlcyI6ZmFsc2UsImV4Y2x1ZGVfcnVsZXMiOltdLCJhZHZhbmNlZCI6dHJ1ZX0sImFkdmFuY2VkX3NjYW5fdGltZW91dCI6MTUsInNjYW5uZXJfdmVyc2lvbiI6IjAuNC4xMS4wIiwiZXhwIjoxNzI4MzAwMzk4fQ.Ndlq49wclrPDCJ7auodH41ggofm8T8By0OoJ4K0_fN4"
        });
      helper.doSelenium.startSession(capability, seleniumNock).then(function(res) {
        const args = JSON.parse(res.text)['value']['chromeOptions']['args'];
        expect(args).to.include(`--load-extension=${accessibilityExtensionPath}`);
        expect(args).to.not.include(`--auto-open-devtools-for-tabs`);
        const devtoolsPrefs = JSON.parse(res.text)['value']['chromeOptions']['prefs']['devtools']['preferences'];
        expect(devtoolsPrefs['currentDockState']).to.not.equal("\"undocked\"");
        expect(devtoolsPrefs['last-dock-state']).to.not.equal("\"undocked\"");
        done();
      });
    });


    it('loads accessibility extension and does not open devtools if JWT token could not be decoded', function(done) {
      const accessibilityExtensionPath = "/Users/<USER>/Downloads/accessibility-toolkit/0.4.11.0";
      var macTerminalScope = helper.buildMac({
        action: [200, {done: true, accessibilityExtensionPath: accessibilityExtensionPath}],
      });
      seleniumNock = helper.selenium();
      var capability = helper.getCapabilities({
          browserName: 'chrome',
          browser_version: '114',
          os: 'OS X',
          os_version: 'Big Sur',
        'browserstack.accessibilityOptions.authToken': "invalidTokenFormat"
        });
      helper.doSelenium.startSession(capability, seleniumNock).then(function(res) {
        const args = JSON.parse(res.text)['value']['chromeOptions']['args'];
        expect(args).to.include(`--load-extension=${accessibilityExtensionPath}`);
        expect(args).to.not.include(`--auto-open-devtools-for-tabs`);
        const devtoolsPrefs = JSON.parse(res.text)['value']['chromeOptions']['prefs']['devtools']['preferences'];
        expect(devtoolsPrefs['currentDockState']).to.not.equal("\"undocked\"");
        expect(devtoolsPrefs['last-dock-state']).to.not.equal("\"undocked\"");
        done();
      });
    });

    it('gets adb proxy port in response from firecommand', function(done) {
      var requestBody = helper.getCapabilities(),
        usePort = 6557,
        macTerminalScope = helper.buildMac({
          action: [200, {done:true, port:usePort, adb_proxy_port: 5038}],
        }),
      seleniumNock = helper.appiumMini();
      var capability = helper.getCapabilities({
        browser: 'chrome',
        device: 'iPad Mini 3',
        realMobile: true
      });
      helper.doSelenium.startSession(capability, seleniumNock)
        .then(function(res) {
          assert.equal(res.body.value.chromedriverArgs.length, 1);
          assert.equal(res.body.value.chromedriverArgs.includes('--adb-port=5038'), true);
          res.status.should.equal(200);
          res.body.sessionId.should.equal(helper.SESSION_ID);
          res.body.status.should.equal(0);
          done();
        });
    });

    it('gets proxy_port in response from firecommand', function(done) {
      var requestBody = helper.getCapabilities(),
        usePort = 6557,
        macTerminalScope = helper.buildMac({
          action: [200, {done:true, port:usePort, proxy_port: 1234}],
        }),
      seleniumNock = helper.appiumMini();
      var capability = helper.getCapabilities({
        browser: 'chrome',
        device: 'iPad Mini 3',
        realMobile: true
      });
      helper.doSelenium.startSession(capability, seleniumNock)
        .then(function(res) {
          res.status.should.equal(200);
          res.body.sessionId.should.equal(helper.SESSION_ID);
          res.body.status.should.equal(0);
          done();
        });
    });

    it('dont use adb proxy when firecmd response doesnt have it', function(done) {
      var requestBody = helper.getCapabilities(),
        usePort = 6557,
        macTerminalScope = helper.buildMac({
          action: [200, {done:true, port:usePort}],
        }),
      seleniumNock = helper.appiumMini();
      var capability = helper.getCapabilities({
        browser: 'chrome',
        device: 'iPad Mini 3',
        realMobile: true
      });
      helper.doSelenium.startSession(capability, seleniumNock)
        .then(function(res) {
          assert.equal(res.body.value.chromedriverArgs, null);
          res.status.should.equal(200);
          res.body.sessionId.should.equal(helper.SESSION_ID);
          res.body.status.should.equal(0);
          done();
        });
    });

    it('adb proxy port should append in existing chromedriverargs', function(done) {
      var requestBody = helper.getCapabilities(),
        usePort = 6557,
        macTerminalScope = helper.buildMac({
          action: [200, {done:true, port:usePort, adb_proxy_port: 5038}],
        }),
      seleniumNock = helper.appiumMini();
      var capability = helper.getCapabilities({
        browser: 'chrome',
        device: 'iPad Mini 3',
        realMobile: true,
        chromedriverArgs: ['abcd']
      });
      helper.doSelenium.startSession(capability, seleniumNock)
        .then(function(res) {
          assert.equal(res.body.value.chromedriverArgs.length, 2);
          assert.equal(res.body.value.chromedriverArgs.includes('--adb-port=5038'), true);
          res.status.should.equal(200);
          res.body.sessionId.should.equal(helper.SESSION_ID);
          res.body.status.should.equal(0);
          done();
        });
    });

    it('adb proxy port should not duplicate in chromedriverargs', function(done) {
      var requestBody = helper.getCapabilities(),
        usePort = 6557,
        macTerminalScope = helper.buildMac({
          action: [200, {done:true, port:usePort, adb_proxy_port: 5038}],
        }),
      seleniumNock = helper.appiumMini();
      var capability = helper.getCapabilities({
        browser: 'chrome',
        device: 'iPad Mini 3',
        realMobile: true,
        chromedriverArgs: ['--adb-port=5038']
      });
      helper.doSelenium.startSession(capability, seleniumNock)
        .then(function(res) {
          assert.equal(res.body.value.chromedriverArgs.length, 1);
          assert.equal(res.body.value.chromedriverArgs.includes('--adb-port=5038'), true);
          res.status.should.equal(200);
          res.body.sessionId.should.equal(helper.SESSION_ID);
          res.body.status.should.equal(0);
          done();
        });
    });

    it('sends a retry to rails if fireCommand has an error', function(done) {
      var requestBody = helper.getCapabilities(),
          railsScope1 = helper.buildRails(),
          macTerminalScope = helper.buildMac({error: true});

      var railsScope2 = helper.buildRails({action: [500, 'Boom! An error.'], query: {auth: 'selautomate', hrelease: true}});

      helper.request.post('/wd/hub/session')
      .auth('test', 'test')
      .send(requestBody)
      .end(function(err, res) {
        railsScope1.isDone().should.be.true;
        macTerminalScope.isDone().should.be.true;
        railsScope2.isDone().should.be.true;
        macTerminalScope.isDone().should.be.true;
        res.status.should.equal(200);
        res.body.value.message.should.equal('No response from BrowserStack!!');
        res.body.sessionId.should.equal('');
        res.body.status.should.equal(13);
        done();
      });
    });

    it('sends a retry to rails (which can do a retry) if fireCommand has an error', function(done) {
      var requestBody = helper.getCapabilities(),
          railsScope1 = helper.buildRails(),
          macTerminalScope = helper.buildMac({error: true}),
          railsScope2 = helper.buildRails({action: [202, 'Boom! An error.'], query: {auth: 'selautomate', hrelease: true}}),
          railsScope3 = helper.buildRails({action: [202, 'Boom! An error.'], query: {auth: 'selautomate', hrelease: true}});

      helper.request.post('/wd/hub/session')
      .auth('test', 'test')
      .send(requestBody)
      .end(function(err, res) {
        railsScope1.isDone().should.be.true;
        macTerminalScope.isDone().should.be.true;
        railsScope2.isDone().should.be.true;
        railsScope3.isDone().should.be.true;
        macTerminalScope.isDone().should.be.true;
        res.status.should.equal(200);
        res.body.value.message.should.equal('Not able to reach Browserstack.com! Please try again!');
        res.body.sessionId.should.equal('');
        res.body.status.should.equal(13);
        done();
      });
    });

    // test will be fixed as a part of APS-4780
    xit('sends a retry to rails if fireCommand has a timeout', function(done) {
      var requestBody = helper.getCapabilities(),
          railsScope1 = helper.buildRails(),
          macTerminalScope = helper.buildMac({delay: 120000}),
          railsScope2 = helper.buildRails({action: [500, 'Boom! An error.'], query: {auth: 'selautomate', hrelease: true}});

      helper.request.post('/wd/hub/session')
      .auth('test', 'test')
      .send(requestBody)
      .end(function(err, res) {
        railsScope1.isDone().should.be.true;
        macTerminalScope.isDone().should.be.true;
        railsScope2.isDone().should.be.true;
        macTerminalScope.isDone().should.be.true;
        res.status.should.equal(200);
        res.body.value.message.should.equal('No response from BrowserStack!!');
        res.body.sessionId.should.equal('');
        res.body.status.should.equal(13);
        done();
      });
    });

    it('error in fireCommand sends hardRelease request to rails and retries', function(done) {
      this.timeout(5000);
      var requestBody = helper.getCapabilities(),
          railsScope1 = helper.buildRails(),
          macTerminalScope = helper.buildMac({error: true}),
          railsScope2 = helper.buildRails({
            extraValidation: function(uri, body) {
              body = JSON.parse(body);
              body.hardRelease.should.equal(helper.TERMINAL_IP);
            },
            query: {auth: 'selautomate', hrelease: true}
          });

      helper.request.post('/wd/hub/session')
      .auth('test', 'test')
      .send(requestBody)
      .end(function(err, res) {
        railsScope1.isDone().should.be.true;
        macTerminalScope.isDone().should.be.true;
        railsScope2.isDone().should.be.true;
        res.status.should.equal(200);
        res.body.value.message.should.equal('Could not start Browser / Emulator');
        res.body.sessionId.should.equal('');
        res.body.status.should.equal(13);
        done();
      });
    });

    xit('machine obtained after hardRelease will have a same machine retry');
  });

  describe('Fire Commands for useDirectChromeDriver', function() {
    it('Uses 9091 for Chrome 42 on Windows', function(done) {
      var capability = helper.getCapabilities({
          browserName: 'chrome',
          browser_version: '42',
          os: 'Windows',
          os_version: '10',
          'browserstack.useChromeDriver': true
        }),
        seleniumNock = helper.selenium(undefined, undefined, 9091);

      helper.doSelenium.startSession(capability, seleniumNock)
        .then(function(res) {
          res.status.should.equal(200);
          res.body.value.should.have.any.keys('browser', 'browser_version', 'os', 'os_version');
          res.body.sessionId.should.equal(helper.SESSION_ID);
          res.body.status.should.equal(0);
          seleniumNock.isDone().should.be.true;
          done();
        });
    });
    it('Uses 9092 for latest chrome on Windows', function(done) {
      var capability = helper.getCapabilities({
          browserName: 'chrome',
          browser_version: '60.0',
          os: 'Windows',
          os_version: '10'
        }),
        seleniumNock = helper.selenium(undefined, undefined, 9092);

      helper.doSelenium.startSession(capability, seleniumNock)
        .then(function(res) {
          res.status.should.equal(200);
          res.body.value.should.have.any.keys('browser', 'browser_version', 'os', 'os_version');
          res.body.sessionId.should.equal(helper.SESSION_ID);
          res.body.status.should.equal(0);
          seleniumNock.isDone().should.be.true;
          done();
        });
    });
    it('Uses selenium jar for non latest chrome on Windows', function(done) {
      var capability = helper.getCapabilities({
          browserName: 'chrome',
          browser_version: '58.0',
          os: 'Windows',
          os_version: '10'
        }),
        seleniumNock = helper.selenium(undefined, undefined, 6556);

      helper.doSelenium.startSession(capability, seleniumNock)
        .then(function(res) {
          res.status.should.equal(200);
          res.body.value.should.have.any.keys('browser', 'browser_version', 'os', 'os_version');
          res.body.sessionId.should.equal(helper.SESSION_ID);
          res.body.status.should.equal(0);
          seleniumNock.isDone().should.be.true;
          done();
        });
    });
    it('Uses selenium jar for Chrome 42 on Mac', function(done) {
      var capability = helper.getCapabilities({
          browserName: 'chrome',
          browser_version: '42',
          os: 'OS X',
          os_version: 'Sierra'
        }),
        seleniumNock = helper.selenium(undefined, undefined, 6556);

      helper.doSelenium.startSession(capability, seleniumNock)
        .then(function(res) {
          res.status.should.equal(200);
          res.body.value.should.have.any.keys('browser', 'browser_version', 'os', 'os_version');
          res.body.sessionId.should.equal(helper.SESSION_ID);
          res.body.status.should.equal(0);
          seleniumNock.isDone().should.be.true;
          done();
        });
    });
    it('Uses 9091 for Chrome 42 on Windows without capability', function(done) {
      var capability = helper.getCapabilities({
          browserName: 'chrome',
          browser_version: '42',
          os: 'Windows',
          os_version: '10',
        }),
        seleniumNock = helper.selenium(undefined, undefined, 9091);

      helper.doSelenium.startSession(capability, seleniumNock)
        .then(function(res) {
          res.status.should.equal(200);
          res.body.value.should.have.any.keys('browser', 'browser_version', 'os', 'os_version');
          res.body.sessionId.should.equal(helper.SESSION_ID);
          res.body.status.should.equal(0);
          seleniumNock.isDone().should.be.true;
          done();
        });
    });
  });

  describe('Fire Commands for marionette', function() {
    it('Params for Mac', function(done) {
      var capability = helper.getCapabilities( { browser: 'firefox', browser_version: 45, marionette: true } ),
        macTerminalScope = helper.buildMac({
          action: function(options) {
            options.marionette.should.equal('true');
            options.marionette_firefox_version.should.equal('45.0');
            options.terminal_ip.should.equal(helper.TERMINAL_IP);
            return [200, { done: true }];
          }
        });

        helper.doSelenium.startSession(capability)
        .then(function(res) {
          res.status.should.equal(200);
          res.body.value.should.have.any.keys('browser', 'browser_version', 'os', 'os_version');
          res.body.sessionId.should.equal(helper.SESSION_ID);
          res.body.status.should.equal(0);
          done();
        });
    });
    it('Params for Mac', function(done) {
      var capability = helper.getCapabilities( { browser: 'firefox', browser_version: 45, marionette: false } ),
        macTerminalScope = helper.buildMac({
          action: function(options) {
            options.should.not.include.keys('marionette');
            options.should.not.include.keys('marionette_firefox_version');
            options.terminal_ip.should.equal(helper.TERMINAL_IP);
            return [200, { done: true }];
          }
        });

        helper.doSelenium.startSession(capability)
        .then(function(res) {
          res.status.should.equal(200);
          res.body.value.should.have.any.keys('browser', 'browser_version', 'os', 'os_version');
          res.body.sessionId.should.equal(helper.SESSION_ID);
          res.body.status.should.equal(0);
          done();
        });
    });
    it('Params for Windows', function(done) {
      var capability = helper.getCapabilities( { os: 'Windows', os_version: '10', browser: 'firefox', browser_version: 45, marionette: true } ),
        winTerminalScope = helper.buildWin({
          action: function(options) {
            options.marionetteBrowserVersion.should.equal('c:\\Program Files\\firefox 45.0');
            options.terminal_ip.should.equal(helper.TERMINAL_IP);
            return [200, { done: true }];
          }
        });

        helper.doSelenium.startSession(capability)
        .then(function(res) {
          res.status.should.equal(200);
          res.body.value.should.have.any.keys('browser', 'browser_version', 'os', 'os_version');
          res.body.sessionId.should.equal(helper.SESSION_ID);
          res.body.status.should.equal(0);
          done();
        });
    });
    it('Params for Windows', function(done) {
      var capability = helper.getCapabilities( { os: 'Windows', os_version: '10', browser: 'firefox', browser_version: 45, marionette: false } ),
        winTerminalScope = helper.buildWin({
          action: function(options) {
            options.should.not.include.keys('marionetteBrowserVersion');
            options.terminal_ip.should.equal(helper.TERMINAL_IP);
            return [200, { done: true }];
          }
        });

        helper.doSelenium.startSession(capability)
        .then(function(res) {
          res.status.should.equal(200);
          res.body.value.should.have.any.keys('browser', 'browser_version', 'os', 'os_version');
          res.body.sessionId.should.equal(helper.SESSION_ID);
          res.body.status.should.equal(0);
          done();
        });
    });
  });

  describe('Fire commands for Edge chromium', function() {
    it('Shouldnt send chromeDriverVersion for Edge Chromium', function(done) {
      var capability = helper.getCapabilities( { os: 'Windows', os_version: '10', browser: 'Edge', browser_version: '79.0 beta', 'browserstack.chrome.driver': '80.0.3987.16' } ),
        winTerminalScope = helper.buildWin({
          action: function(options) {
            options.should.not.include.keys('browserstack.chrome.driver');
            options.terminal_ip.should.equal(helper.TERMINAL_IP);
            return [200, { done: true }];
          }
        });

        helper.doSelenium.startSession(capability)
        .then(function(res) {
          res.status.should.equal(200);
          res.body.value.should.have.any.keys('browser', 'browser_version', 'os', 'os_version');
          res.body.sessionId.should.equal(helper.SESSION_ID);
          res.body.status.should.equal(0);
          done();
        });
    });
  });

  describe('GET command string should match conditions', function() {
    it('Resolution only if it is not 1920x1080', function(done) {
      var requestBody = helper.getCapabilities({resolution: '1600x1200'}),
          railsScope = helper.buildRails(),
          macTerminalScope = helper.buildMac({
            action: function(options) {
              options.resw.should.equal('1600');
              options.resh.should.equal('1200');
              options.colorbit.should.equal('32');
              return [200, { done: true }];
            }
          });

      var seleniumScope = helper.selenium()
      .post('/wd/hub/session')
      .query(true)
      .reply(function(uri, body) {
        return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
      });

      helper.request.post('/wd/hub/session')
      .auth('test', 'test')
      .send(requestBody)
      .end(function(err, res) {
        railsScope.isDone().should.be.true;
        macTerminalScope.isDone().should.be.true;
        seleniumScope.isDone().should.be.true;
        res.status.should.equal(200);
        res.body.value.should.have.any.keys('browser', 'browser_version', 'os', 'os_version');
        res.body.sessionId.should.equal(helper.SESSION_ID);
        res.body.status.should.equal(0);
        done();
      });
    });
    it('Should process dedicated browserstack params', function(done) {
      var requestBody = helper.getCapabilities(),
          railsScope = helper.buildRails(
            {
              extraAccess: 'sms',
              skip_platform_enterprise_flow: true
            }
          ),
          macTerminalScope = helper.buildMac({
            action: function(options) {
              options.extraAccess.should.equal('sms');
              options.skip_platform_enterprise_flow.should.equal('true');
              return [200, { done: true }];
            }
          });

      var seleniumScope = helper.selenium()
      .post('/wd/hub/session')
      .query(true)
      .reply(function(uri, body) {
        return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
      });

      helper.request.post('/wd/hub/session')
      .auth('test', 'test')
      .send(requestBody)
      .end(function(err, res) {
        railsScope.isDone().should.be.true;
        macTerminalScope.isDone().should.be.true;
        seleniumScope.isDone().should.be.true;
        res.status.should.equal(200);
        res.body.value.should.have.any.keys('browser', 'browser_version', 'os', 'os_version');
        res.body.sessionId.should.equal(helper.SESSION_ID);
        res.body.status.should.equal(0);
        done();
      });
    });
    it('Resolution not set if it is 1920x1080', function(done) {
      var requestBody = helper.getCapabilities({resolution: '1920x1080'}),
          railsScope = helper.buildRails(),
          macTerminalScope = helper.buildMac({
            action: function(options) {
              options.should.not.have.keys('resh', 'resw', 'colorbit');
              return [200, { done: true }];
            }
          });

      var seleniumScope = helper.selenium()
      .post('/wd/hub/session')
      .query(true)
      .reply(function(uri, body) {
        return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
      });

      helper.request.post('/wd/hub/session')
      .auth('test', 'test')
      .send(requestBody)
      .end(function(err, res) {
        railsScope.isDone().should.be.true;
        macTerminalScope.isDone().should.be.true;
        seleniumScope.isDone().should.be.true;
        res.status.should.equal(200);
        res.body.value.should.have.any.keys('browser', 'browser_version', 'os', 'os_version');
        res.body.sessionId.should.equal(helper.SESSION_ID);
        res.body.status.should.equal(0);
        done();
      });
    });
    xit('Windows 10 resolution should be 1920x1080', function(done) {
      var requestBody = helper.getCapabilities({os: 'Windows', os_version: '10'}),
          railsScope = helper.buildRails();
          winTerminalScope = helper.buildWin({
            action: function(options) {
              options.resw.should.equal('1920');
              options.resh.should.equal('1080');
              options.colorbit.should.equal('32');
              return [200, { done: true }];
            }
          });

      var seleniumScope = helper.selenium()
      .post('/wd/hub/session')
      .query(true)
      .reply(function(uri, body) {
        return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
      });

      helper.request.post('/wd/hub/session')
      .auth('test', 'test')
      .send(requestBody)
      .end(function(err, res) {
        railsScope.isDone().should.be.true;
        winTerminalScope.isDone().should.be.true;
        seleniumScope.isDone().should.be.true;
        res.status.should.equal(200);
        res.body.value.should.have.any.keys('browser', 'browser_version', 'os', 'os_version');
        res.body.sessionId.should.equal(helper.SESSION_ID);
        res.body.status.should.equal(0);
        done();
      });
    });
    it('does not send the video fireCommand on browserstack.video false', function(done) {
      var requestBody = helper.getCapabilities(),
          railsScope = helper.buildRails(),
          macTerminalScope = helper.buildMac({
            action: function(options) {
              options.should.not.have.keys('video');
              return [200, { done: true }];
            }
          });
      requestBody['desiredCapabilities']['browserstack.video'] = false;

      var seleniumScope = helper.selenium()
        .post('/wd/hub/session')
        .query(true)
        .reply(function(uri, body) {
          return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
        });

      helper.request.post('/wd/hub/session')
        .auth('test', 'test')
        .send(requestBody)
        .end(function(err, res) {
          railsScope.isDone().should.be.true;
          macTerminalScope.isDone().should.be.true;
          seleniumScope.isDone().should.be.true;
          res.status.should.equal(200);
          res.body.value.should.have.any.keys('browser', 'browser_version', 'os', 'os_version');
          res.body.sessionId.should.equal(helper.SESSION_ID);
          res.body.status.should.equal(0);
          done();
        });
    });
    it('Video', function(done) {
      var requestBody = helper.getCapabilities(),
          railsScope = helper.buildRails(),
          macTerminalScope = helper.buildMac({
          action: function(options) {
            options.video.should.equal('true');
            options.video_session_id.should.equal(helper.SESSION_ID);
            options.video_aws_keys.should.equal('awskey');
            options.video_aws_secret.should.equal('awssecret');
            options.video_aws_bucket.should.equal('videobucket');
            options.video_aws_region.should.equal('videoregion');
            options.video_file.should.equal('video');
            return [200, { done: true }];
          }
        });

      var seleniumScope = helper.selenium()
      .post('/wd/hub/session')
      .query(true)
      .reply(function(uri, body) {
        return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
      });

      helper.request.post('/wd/hub/session')
      .auth('test', 'test')
      .send(requestBody)
      .end(function(err, res) {
        railsScope.isDone().should.be.true;
        macTerminalScope.isDone().should.be.true;
        seleniumScope.isDone().should.be.true;
        res.status.should.equal(200);
        res.body.value.should.have.any.keys('browser', 'browser_version', 'os', 'os_version');
        res.body.sessionId.should.equal(helper.SESSION_ID);
        res.body.status.should.equal(0);
        done();
      });
    });
    it('bfCache', function(done) {
      var requestBody = helper.getCapabilities({
            'browser': 'IE',
            'os': 'Windows',
            'os_version': '7',
            'browser_version': 9,
            'browserstack.bfcache': '0'
          }),
          railsScope = helper.buildRails(),
          winTerminalScope = helper.buildWin({
            action: function(options) {
              options.bfcache.should.equal('0');
              return [200, { done: true }];
            }
          });

      var seleniumScope = helper.selenium()
      .post('/wd/hub/session')
      .query(true)
      .reply(function(uri, body) {
        return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
      });

      helper.request.post('/wd/hub/session')
      .auth('test', 'test')
      .send(requestBody)
      .end(function(err, res) {
        railsScope.isDone().should.be.true;
        winTerminalScope.isDone().should.be.true;
        seleniumScope.isDone().should.be.true;
        res.status.should.equal(200);
        res.body.value.should.have.any.keys('browser', 'browser_version', 'os', 'os_version');
        res.body.sessionId.should.equal(helper.SESSION_ID);
        res.body.status.should.equal(0);
        done();
      });
    });
    it('unlock keychain and jar version for safari', function(done) {
      var os = 'macyos', seleniumVersion = '2.52.0';
      var requestBody = helper.getCapabilities({browser: 'Safari', 'browser_version': 8}),
          railsScope = helper.buildRails(),
          macTerminalScope = helper.buildMac({
            action: function(options) {
              options.unlockkeychain.should.equal('1');
              options.seleniumVersion.should.equal('2.52.0');
              return [200, { done: true }];
            }
          });

      var seleniumScope = helper.selenium(os, seleniumVersion)
      .post('/wd/hub/session')
      .query(true)
      .reply(function(uri, body) {
        return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
      });

      helper.request.post('/wd/hub/session')
      .auth('test', 'test')
      .send(requestBody)
      .end(function(err, res) {
        railsScope.isDone().should.be.true;
        macTerminalScope.isDone().should.be.true;
        seleniumScope.isDone().should.be.true;
        res.status.should.equal(200);
        res.body.value.should.have.any.keys('browser', 'browser_version', 'os', 'os_version');
        res.body.sessionId.should.equal(helper.SESSION_ID);
        res.body.status.should.equal(0);
        done();
      });
    });
    it('timezone', function(done) {
      var os = 'macyos', seleniumVersion = '2.52.0';
      var requestBody = helper.getCapabilities({
            browser: 'Safari',
            browser_version: 8,
            'browserstack.timezone': 'Kolkata'
          }),
          railsScope = helper.buildRails(),
          macTerminalScope = helper.buildMac({
            action: function(options) {
              options.timezone.should.equal('Asia/Kolkata');
              return [200, { done: true }];
            }
          });

      var seleniumScope = helper.selenium(os, seleniumVersion)
      .post('/wd/hub/session')
      .query(true)
      .reply(function(uri, body) {
        return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
      });

      helper.request.post('/wd/hub/session')
      .auth('test', 'test')
      .send(requestBody)
      .end(function(err, res) {
        railsScope.isDone().should.be.true;
        macTerminalScope.isDone().should.be.true;
        seleniumScope.isDone().should.be.true;
        res.status.should.equal(200);
        res.body.value.should.have.any.keys('browser', 'browser_version', 'os', 'os_version');
        res.body.sessionId.should.equal(helper.SESSION_ID);
        res.body.status.should.equal(0);
        done();
      });
    });

    it('timezone for appium should be as it is as received for non group 2', function(done) {
      var requestBody = helper.getCapabilities({
            os: 'ios',
            device: 'iPad Mini 3',
            realMobile: true,
            'browserstack.timezone': 'random'
          }),
          railsScope = helper.buildRails(),
          macTerminalScope = helper.buildMac({
            action: function(options) {
              options.timezone.should.equal('random');
              return [200, { done: true }];
            }
          });

      var seleniumScope = helper.appiumMini()
      .post('/wd/hub/session')
      .query(true)
      .reply(function(uri, body) {
        return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
      });

      helper.request.post('/wd/hub/session')
      .auth('test', 'test')
      .send(requestBody)
      .end(function(err, res) {
        railsScope.isDone().should.be.true;
        macTerminalScope.isDone().should.be.true;
        seleniumScope.isDone().should.be.true;
        res.status.should.equal(200);
        res.body.value.should.have.any.keys('browser', 'browser_version', 'os', 'os_version');
        res.body.sessionId.should.equal(helper.SESSION_ID);
        res.body.status.should.equal(0);
        done();
      });
    });

    it('timezone for appium should be as it is as received for non groudasdp 2', function(done) {
      var requestBody = helper.getCapabilities({
            os: 'ios',
            device: 'iPad Mini 3',
            realMobile: true,
            'browserstack.timezone': 'random',
            'browserstack.timezone_mdm': 'random'
          }),
          railsScope = helper.buildRails(),
          macTerminalScope = helper.buildMac({
            action: function (options) {
              options.timezone.should.equal('random');
              options.timezone_mdm.should.equal('random');
              return [200, { done: true }];
            }
          });

      var seleniumScope = helper.appiumMini()
      .post('/wd/hub/session')
      .query(true)
      .reply(function(uri, body) {
        return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
      });

      helper.request.post('/wd/hub/session')
      .auth('test', 'test')
      .send(requestBody)
      .end(function(err, res) {
        railsScope.isDone().should.be.true;
        macTerminalScope.isDone().should.be.true;
        seleniumScope.isDone().should.be.true;
        res.status.should.equal(200);
        res.body.value.should.have.any.keys('browser', 'browser_version', 'os', 'os_version');
        res.body.sessionId.should.equal(helper.SESSION_ID);
        res.body.status.should.equal(0);
        done();
      });
    });

    it('timezone for appium should be as it is as received for group 2', function(done) {
      var requestBody = helper.getCapabilities({
            os: 'ios',
            device: 'iPad Mini 3',
            realMobile: true,
            'browserstack.timezone': 'random',
          }),
          railsScope = helper.buildRails({group_id: 2}),
          macTerminalScope = helper.buildMac({
            action: function(options) {
              options.timezone.should.equal('random');
              return [200, { done: true }];
            }
          });

      var seleniumScope = helper.appiumMini()
      .post('/wd/hub/session')
      .query(true)
      .reply(function(uri, body) {
        return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
      });

      helper.request.post('/wd/hub/session')
      .auth('test', 'test')
      .send(requestBody)
      .end(function(err, res) {
        railsScope.isDone().should.be.true;
        macTerminalScope.isDone().should.be.true;
        seleniumScope.isDone().should.be.true;
        res.status.should.equal(200);
        res.body.value.should.have.any.keys('browser', 'browser_version', 'os', 'os_version');
        res.body.sessionId.should.equal(helper.SESSION_ID);
        res.body.status.should.equal(0);
        done();
      });
    });

    it('safari enable popups, allowAllCookiesa and disableAutoOpenSafeDownloads', function(done) {
      var os = 'macyos', seleniumVersion = '2.52.0';
      var requestBody = helper.getCapabilities({
            browser: 'Safari',
            browser_version: 8,
            'browserstack.safari.enablePopups': true,
            'browserstack.safari.allowAllCookies': true,
            'browserstack.safari.disableAutoOpenSafeDownloads': true,
          }),
          railsScope = helper.buildRails(),
          macTerminalScope = helper.buildMac({
            action: function(options) {
              options.enablePopupsSafari.should.equal('true');
              options.allowAllCookies.should.equal('true');
              options.disableAutoOpenSafeDownloads.should.equal('true');
              return [200, { done: true }];
            }
          });

      var seleniumScope = helper.selenium(os, seleniumVersion)
      .post('/wd/hub/session')
      .query(true)
      .reply(function(uri, body) {
        return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
      });

      helper.request.post('/wd/hub/session')
      .auth('test', 'test')
      .send(requestBody)
      .end(function(err, res) {
        railsScope.isDone().should.be.true;
        macTerminalScope.isDone().should.be.true;
        seleniumScope.isDone().should.be.true;
        res.status.should.equal(200);
        res.body.value.should.have.any.keys('browser', 'browser_version', 'os', 'os_version');
        res.body.sessionId.should.equal(helper.SESSION_ID);
        res.body.status.should.equal(0);
        done();
      });
    });

    it('safari cross-site tracking', function(done) {
      var os = 'macyos', seleniumVersion = '2.52.0';
      var requestBody = helper.getCapabilities({
            browser: 'Safari',
            browser_version: 8,
            'browserstack.preventCrossSiteTracking': false
          }),
          railsScope = helper.buildRails(),
          macTerminalScope = helper.buildMac({
            action: function(options) {
              options.preventCrossSiteTracking.should.equal('true');
              return [200, { done: true }];
            }
          });

      var seleniumScope = helper.selenium(os, seleniumVersion)
      .post('/wd/hub/session')
      .query(true)
      .reply(function(uri, body) {
        return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
      });

      helper.request.post('/wd/hub/session')
      .auth('test', 'test')
      .send(requestBody)
      .end(function(err, res) {
        railsScope.isDone().should.be.true;
        macTerminalScope.isDone().should.be.true;
        seleniumScope.isDone().should.be.true;
        res.status.should.equal(200);
        res.body.value.should.have.any.keys('browser', 'browser_version', 'os', 'os_version');
        res.body.sessionId.should.equal(helper.SESSION_ID);
        res.body.status.should.equal(0);
        done();
      });
    });

    it('enable self signed certificates for safari', function(done) {
      var os = 'macyos', seleniumVersion = '2.52.0';
      var requestBody = helper.getCapabilities({
            browser: 'Safari',
            browser_version: 8,
            acceptSslCerts: true
          }),
          railsScope = helper.buildRails(),
          macTerminalScope = helper.buildMac({
            action: function(options) {
              options.enableCertSafari.should.equal('true');
              return [200, { done: true }];
            }
          });

      var seleniumScope = helper.selenium(os, seleniumVersion)
      .post('/wd/hub/session')
      .query(true)
      .reply(function(uri, body) {
        return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
      });

      helper.request.post('/wd/hub/session')
      .auth('test', 'test')
      .send(requestBody)
      .end(function(err, res) {
        railsScope.isDone().should.be.true;
        macTerminalScope.isDone().should.be.true;
        seleniumScope.isDone().should.be.true;
        res.status.should.equal(200);
        res.body.value.should.have.any.keys('browser', 'browser_version', 'os', 'os_version');
        res.body.sessionId.should.equal(helper.SESSION_ID);
        res.body.status.should.equal(0);
        done();
      });
    });
    it("disable self signed certificates in firecmd for safari 11", function(done) {
      var os = "machs", seleniumVersion = "3.4.0";
      var requestBody = helper.getCapabilities({
          browser: "Safari",
          browser_version: 11,
          os_version: "High Sierra",
          acceptSslCerts: true
        }),
        railsScope = helper.buildRails(),
        macTerminalScope = helper.buildMac({
          action: function(options) {
            options.should.not.have.any.keys("enableCertSafari");
            return [200, { done: true }];
          }
        });

      var seleniumScope = helper.selenium(os, seleniumVersion)
        .post("/wd/hub/session")
        .query(true)
        .reply(function(uri, body) {
          return helper.sessionStartedJSON(JSON.parse(body)["desiredCapabilities"]);
        });

      helper.request.post("/wd/hub/session")
        .auth("test", "test")
        .send(requestBody)
        .end(function(err, res) {
          railsScope.isDone().should.be.true;
          macTerminalScope.isDone().should.be.true;
          seleniumScope.isDone().should.be.true;
          res.status.should.equal(200);
          res.body.value.should.not.have.any.keys("enableCertSafari");
          res.body.sessionId.should.equal(helper.SESSION_ID);
          res.body.status.should.equal(0);
          done();
        });
    });
    it('enable self signed certificates for real mobile on iPad Mini 3', function(done) {
      var requestBody = helper.getCapabilities({
            browser: 'Safari',
            device: 'iPad Mini 3',
            realMobile: true,
            acceptSslCerts: true
          }),
          railsScope = helper.buildRails(),
          macTerminalScope = helper.buildMac({
            action: function(options) {
              options.autoAcceptAlerts.should.equal('true');
              return [200, { done: true }];
            }
          });

      var seleniumScope = helper.appiumMini()
      .post('/wd/hub/session')
      .query(true)
      .reply(function(uri, body) {
        return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
      });

      helper.request.post('/wd/hub/session')
      .auth('test', 'test')
      .send(requestBody)
      .end(function(err, res) {
        railsScope.isDone().should.be.true;
        macTerminalScope.isDone().should.be.true;
        seleniumScope.isDone().should.be.true;
        res.status.should.equal(200);
        res.body.value.should.have.any.keys('browser', 'browser_version', 'os', 'os_version');
        res.body.sessionId.should.equal(helper.SESSION_ID);
        res.body.status.should.equal(0);
        done();
      });
    });
    it('session id should be sent in params for real mobile', function(done) {
      var requestBody = helper.getCapabilities({
            browser: 'Safari',
            device: 'iPhone 6S Plus',
            realMobile: true
          }),
          railsScope = helper.buildRails(),
          macTerminalScope = helper.buildMac({
            action: function(options) {
              options.automate_session_id.should.equal('1a2b3c4d5e6f');
              return [200, { done: true }];
            }
          });

      var seleniumScope = helper.appiumMini()
      .post('/wd/hub/session')
      .query(true)
      .reply(function(uri, body) {
        return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
      });

      helper.request.post('/wd/hub/session')
      .auth('test', 'test')
      .send(requestBody)
      .end(function(err, res) {
        railsScope.isDone().should.be.true;
        macTerminalScope.isDone().should.be.true;
        seleniumScope.isDone().should.be.true;
        res.status.should.equal(200);
        res.body.value.should.have.any.keys('browser', 'browser_version', 'os', 'os_version');
        res.body.sessionId.should.equal(helper.SESSION_ID);
        res.body.status.should.equal(0);
        done();
      });
    });
    it('autoAcceptAlerts should be default for iOS real mobile', function(done) {
      var requestBody = helper.getCapabilities({
            browser: 'Safari',
            device: 'iPhone 6S Plus',
            realMobile: true
          }),
          railsScope = helper.buildRails(),
          macTerminalScope = helper.buildMac({
            action: function(options) {
              options.autoAcceptAlerts.should.equal('true');
              return [200, { done: true }];
            }
          });

      var seleniumScope = helper.appiumMini()
      .post('/wd/hub/session')
      .query(true)
      .reply(function(uri, body) {
        return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
      });

      helper.request.post('/wd/hub/session')
      .auth('test', 'test')
      .send(requestBody)
      .end(function(err, res) {
        railsScope.isDone().should.be.true;
        macTerminalScope.isDone().should.be.true;
        seleniumScope.isDone().should.be.true;
        res.status.should.equal(200);
        res.body.value.should.have.any.keys('browser', 'browser_version', 'os', 'os_version');
        res.body.sessionId.should.equal(helper.SESSION_ID);
        res.body.status.should.equal(0);
        done();
      });
    });

    it('automate chromium iOS real mobile', function(done) {
      var requestBody = helper.getCapabilities({
            browser: 'chromium',
            device: 'iPhone 6S Plus',
            realMobile: true
          }),
          railsScope = helper.buildRails(),
          macTerminalScope = helper.buildMac({
            action: function(options) {
              options.autoAcceptAlerts.should.equal('true');
              return [200, { done: true, chromium_bundle_id: "chromium_bundle_id" }];
            }
          });

      var seleniumScope = helper.appiumMini()
      .post('/wd/hub/session')
      .query(true)
      .reply(function(uri, body) {
        return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
      });

      var seleniumScope2 = helper.appiumMini()
      .get('/wd/hub/session/1234-abcd-5678-wxyz/contexts')
      .query(true)
      .reply(function(uri, body) {
        return [200, { done: true, value: ['NATIVE_APP', 'WEBVIEW_1234-abcd-5678-wxyz'] }]
      });

      var seleniumScope3 = helper.appiumMini()
      .post('/wd/hub/session/1234-abcd-5678-wxyz/context', { name: 'WEBVIEW_1234-abcd-5678-wxyz' })
      .reply(200, { done: true });

      helper.request.post('/wd/hub/session')
      .auth('test', 'test')
      .send(requestBody)
      .end(function(err, res) {
        railsScope.isDone().should.be.true;
        macTerminalScope.isDone().should.be.true;
        seleniumScope.isDone().should.be.true;
        seleniumScope2.isDone().should.be.true;
        seleniumScope3.isDone().should.be.true;
        res.status.should.equal(200);
        res.body.value.should.have.any.keys('browser', 'browser_version', 'os', 'os_version');
        res.body.sessionId.should.equal(helper.SESSION_ID);
        res.body.status.should.equal(0);
        done();
      });
    }).timeout(6000);

    it('automate chromium iOS real mobile - W3C', function(done) {
      var requestBody = helper.getCapabilities({
        browser: 'chromium',
        device: 'iPhone 6S Plus',
        realMobile: true
      });
      requestBody["desiredCapabilities"]["browserstack.use_w3c"] = true;
      requestBody["capabilities"] = { "firstMatch": [{
        browser: 'chromium',
        device: 'iPhone 6S Plus',
        realMobile: true,
      }]};

      railsScope = helper.buildRails(),
      macTerminalScope = helper.buildMac({
        action: function(options) {
          options.autoAcceptAlerts.should.equal('true');
          return [200, { done: true, chromium_bundle_id: "chromium_bundle_id" }];
        }
      });

      var seleniumScope = helper.appiumMini()
      .post('/wd/hub/session')
      .query(true)
      .reply(function(uri, body) {
        return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
      });

      var seleniumScope2 = helper.appiumMini()
      .get('/wd/hub/session/1234-abcd-5678-wxyz/contexts')
      .query(true)
      .reply(function(uri, body) {
        return [200, { done: true, value: ['NATIVE_APP', 'WEBVIEW_1234-abcd-5678-wxyz'] }]
      });

      var seleniumScope3 = helper.appiumMini()
      .post('/wd/hub/session/1234-abcd-5678-wxyz/context', { name: 'WEBVIEW_1234-abcd-5678-wxyz' })
      .reply(200, { done: true });

      helper.request.post('/wd/hub/session')
      .auth('test', 'test')
      .send(requestBody)
      .end(function(err, res) {
        railsScope.isDone().should.be.true;
        macTerminalScope.isDone().should.be.true;
        seleniumScope.isDone().should.be.true;
        seleniumScope2.isDone().should.be.true;
        seleniumScope3.isDone().should.be.true;
        res.status.should.equal(200);
        res.body.sessionId.should.equal(helper.SESSION_ID);
        res.body.status.should.equal(0);
        done();
      });
    }).timeout(6000);

    it('accept SSL certs for iOS real mobile', function(done) {
      var requestBody = helper.getCapabilities({
            browser: 'Safari',
            device: 'iPhone 6S Plus',
            realMobile: true,
            acceptSslCerts: true,
          }),
          railsScope = helper.buildRails(),
          macTerminalScope = helper.buildMac({
            action: function(options) {
              options.autoAcceptAlerts.should.equal('true');
              return [200, { done: true }];
            }
          });

      var seleniumScope = helper.appiumMini()
      .post('/wd/hub/session')
      .query(true)
      .reply(function(uri, body) {
        return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
      });

      helper.request.post('/wd/hub/session')
      .auth('test', 'test')
      .send(requestBody)
      .end(function(err, res) {
        railsScope.isDone().should.be.true;
        macTerminalScope.isDone().should.be.true;
        seleniumScope.isDone().should.be.true;
        res.status.should.equal(200);
        res.body.value.should.have.any.keys('browser', 'browser_version', 'os', 'os_version');
        res.body.sessionId.should.equal(helper.SESSION_ID);
        res.body.status.should.equal(0);
        done();
      });
    });
    it('should send autoAccept as false if accept SSL certs is false for iOS real mobile', function(done) {
      var requestBody = helper.getCapabilities({
            browser: 'Safari',
            device: 'iPhone 6S Plus',
            realMobile: true,
            acceptSslCerts: false,
          }),
          railsScope = helper.buildRails(),
          macTerminalScope = helper.buildMac({
            action: function(options) {
              options.autoAcceptAlerts.should.equal('false');
              return [200, { done: true }];
            }
          });

      var seleniumScope = helper.appiumMini()
      .post('/wd/hub/session')
      .query(true)
      .reply(function(uri, body) {
        return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
      });

      helper.request.post('/wd/hub/session')
      .auth('test', 'test')
      .send(requestBody)
      .end(function(err, res) {
        railsScope.isDone().should.be.true;
        macTerminalScope.isDone().should.be.true;
        seleniumScope.isDone().should.be.true;
        res.status.should.equal(200);
        res.body.value.should.have.any.keys('browser', 'browser_version', 'os', 'os_version');
        res.body.sessionId.should.equal(helper.SESSION_ID);
        res.body.status.should.equal(0);
        done();
      });
    });
    it('autoAcceptAlerts should be false if the capbility is false for iOS real mobile', function(done) {
      var requestBody = helper.getCapabilities({
            browser: 'Safari',
            device: 'iPhone 6S Plus',
            realMobile: true,
            autoAcceptAlerts: false
          }),
          railsScope = helper.buildRails(),
          macTerminalScope = helper.buildMac({
            action: function(options) {
              options.autoAcceptAlerts.should.equal('false');
              return [200, { done: true }];
            }
          });

      var seleniumScope = helper.appiumMini()
      .post('/wd/hub/session')
      .query(true)
      .reply(function(uri, body) {
        return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
      });

      helper.request.post('/wd/hub/session')
      .auth('test', 'test')
      .send(requestBody)
      .end(function(err, res) {
        railsScope.isDone().should.be.true;
        macTerminalScope.isDone().should.be.true;
        seleniumScope.isDone().should.be.true;
        res.status.should.equal(200);
        res.body.value.should.have.any.keys('browser', 'browser_version', 'os', 'os_version');
        res.body.sessionId.should.equal(helper.SESSION_ID);
        res.body.status.should.equal(0);
        done();
      });
    });
    it('autoDismissAlerts for iOS real mobile', function(done) {
      var requestBody = helper.getCapabilities({
            browser: 'Safari',
            device: 'iPhone 6S Plus',
            realMobile: true,
            autoDismissAlerts: true
          }),
          railsScope = helper.buildRails(),
          macTerminalScope = helper.buildMac({
            action: function(options) {
              options.autoDismissAlerts.should.equal('true');
              options.autoAcceptAlerts.should.equal('false');
              return [200, { done: true }];
            }
          });

      var seleniumScope = helper.appiumMini()
      .post('/wd/hub/session')
      .query(true)
      .reply(function(uri, body) {
        return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
      });

      helper.request.post('/wd/hub/session')
      .auth('test', 'test')
      .send(requestBody)
      .end(function(err, res) {
        railsScope.isDone().should.be.true;
        macTerminalScope.isDone().should.be.true;
        seleniumScope.isDone().should.be.true;
        res.status.should.equal(200);
        res.body.value.should.have.any.keys('browser', 'browser_version', 'os', 'os_version');
        res.body.sessionId.should.equal(helper.SESSION_ID);
        res.body.status.should.equal(0);
        done();
      });
    });
    it('IE max connections', function(done) {
      var requestBody = helper.getCapabilities({
            browser: 'IE',
            os: 'Windows',
            os_version: '7',
            browser_version: 9
          }),
          railsScope = helper.buildRails(),
          winTerminalScope = helper.buildWin({
            action: function(options) {
              options.maxConnection.should.equal('10');
              return [200, { done: true }];
            }
          });

      var seleniumScope = helper.selenium()
      .post('/wd/hub/session')
      .query(true)
      .reply(function(uri, body) {
        return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
      });

      helper.request.post('/wd/hub/session')
      .auth('test', 'test')
      .send(requestBody)
      .end(function(err, res) {
        railsScope.isDone().should.be.true;
        winTerminalScope.isDone().should.be.true;
        seleniumScope.isDone().should.be.true;
        res.status.should.equal(200);
        res.body.value.should.have.any.keys('browser', 'browser_version', 'os', 'os_version');
        res.body.sessionId.should.equal(helper.SESSION_ID);
        res.body.status.should.equal(0);
        done();
      });
    });
    it('accept SSL Certs for IE', function(done) {
      var requestBody = helper.getCapabilities({
            browser: 'IE',
            os: 'Windows',
            os_version: '7',
            browser_version: 9,
            acceptSslCerts: true
          }),
          railsScope = helper.buildRails(),
          winTerminalScope = helper.buildWin({
            action: function(options) {
              options.acceptSslCertsIE.should.equal('true');
              return [200, { done: true }];
            }
          });

      var seleniumScope = helper.selenium()
      .post('/wd/hub/session')
      .query(true)
      .reply(function(uri, body) {
        return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
      });

      helper.request.post('/wd/hub/session')
      .auth('test', 'test')
      .send(requestBody)
      .end(function(err, res) {
        railsScope.isDone().should.be.true;
        winTerminalScope.isDone().should.be.true;
        seleniumScope.isDone().should.be.true;
        res.status.should.equal(200);
        res.body.value.should.have.any.keys('browser', 'browser_version', 'os', 'os_version');
        res.body.sessionId.should.equal(helper.SESSION_ID);
        res.body.status.should.equal(0);
        done();
      });
    });
    it('disable Flash for IE', function(done) {
      var requestBody = helper.getCapabilities({
            browser: 'IE',
            os: 'Windows',
            os_version: '7',
            browser_version: 9,
            'browserstack.ie.noFlash': true
          }),
          railsScope = helper.buildRails(),
          winTerminalScope = helper.buildWin({
            action: function(options) {
              options.disableFlashIE.should.equal('true');
              return [200, { done: true }];
            }
          });

      var seleniumScope = helper.selenium()
      .post('/wd/hub/session')
      .query(true)
      .reply(function(uri, body) {
        return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
      });

      helper.request.post('/wd/hub/session')
      .auth('test', 'test')
      .send(requestBody)
      .end(function(err, res) {
        railsScope.isDone().should.be.true;
        winTerminalScope.isDone().should.be.true;
        seleniumScope.isDone().should.be.true;
        res.status.should.equal(200);
        res.body.value.should.have.any.keys('browser', 'browser_version', 'os', 'os_version');
        res.body.sessionId.should.equal(helper.SESSION_ID);
        res.body.status.should.equal(0);
        done();
      });
    });
    it('runs with seleniumBidi as true', function(done) {
      var os = 'macbsr', seleniumVersion = '4.20.0';
      var requestBody = helper.getCapabilities({
        'browserstack.selenium_version': seleniumVersion,
        browser: 'chrome',
        browser_version: 124,
        'browserstack.seleniumBidi': 'true'
      }),
          railsScope = helper.buildRails(),
          macTerminalScope = helper.buildMac({
            options: function(options) {
              options.seleniumVersion.should.equal(seleniumVersion);
              return [200, { done: true }];
            }
          });

      var seleniumScope = helper.selenium(os, seleniumVersion)
      .post('/wd/hub/session')
      .query(true)
      .reply(function(uri, body) {
        return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
      });

      helper.request.post('/wd/hub/session')
      .auth('test', 'test')
      .send(requestBody)
      .end(function(err, res) {
        railsScope.isDone().should.be.true;
        macTerminalScope.isDone().should.be.true;
        seleniumScope.isDone().should.be.true;
        res.status.should.equal(200);
        res.body.value.should.have.any.keys('browser', 'browser_version', 'os', 'os_version');
        res.body.sessionId.should.equal(helper.SESSION_ID);
        res.body.status.should.equal(0);
        done();
      });
    });
    it('removes seleniumBidi capability if unsupported seleniumVersion', function(done) {
      var os = 'macbsr', seleniumVersion = '4.19.0';
      var requestBody = helper.getCapabilities({
        'browserstack.selenium_version': seleniumVersion,
        browser: 'chrome',
        browser_version: 124,
        'browserstack.seleniumBidi': 'true'
      }),
          railsScope = helper.buildRails(),
          macTerminalScope = helper.buildMac({
            options: function(options) {
              options.seleniumVersion.should.equal(seleniumVersion);
              return [200, { done: true }];
            }
          });

      var seleniumScope = helper.selenium(os, seleniumVersion)
      .post('/wd/hub/session')
      .query(true)
      .reply(function(uri, body) {
        return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
      });

      helper.request.post('/wd/hub/session')
      .auth('test', 'test')
      .send(requestBody)
      .end(function(err, res) {
        railsScope.isDone().should.be.true;
        macTerminalScope.isDone().should.be.true;
        seleniumScope.isDone().should.be.true;
        res.status.should.equal(200);
        res.body.value.should.have.any.keys('browser', 'browser_version', 'os', 'os_version');
        res.body.sessionId.should.equal(helper.SESSION_ID);
        res.body.status.should.equal(0);
        done();
      });
    });
    it('runs with seleniumCdp as true', function(done) {
      var os = 'macbsr', seleniumVersion = '4.20.0';
      var requestBody = helper.getCapabilities({
        'browserstack.selenium_version': seleniumVersion,
        browser: 'chrome',
        'browserstack.seleniumCdp': 'true'
      }),
          railsScope = helper.buildRails(),
          macTerminalScope = helper.buildMac({
            options: function(options) {
              options.seleniumVersion.should.equal(seleniumVersion);
              return [200, { done: true }];
            }
          });

      var seleniumScope = helper.selenium(os, seleniumVersion)
      .post('/wd/hub/session')
      .query(true)
      .reply(function(uri, body) {
        return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
      });

      helper.request.post('/wd/hub/session')
      .auth('test', 'test')
      .send(requestBody)
      .end(function(err, res) {
        railsScope.isDone().should.be.true;
        macTerminalScope.isDone().should.be.true;
        seleniumScope.isDone().should.be.true;
        res.status.should.equal(200);
        res.body.value.should.have.any.keys('browser', 'browser_version', 'os', 'os_version');
        res.body.sessionId.should.equal(helper.SESSION_ID);
        res.body.status.should.equal(0);
        done();
      });
    });
    it('removes seleniumCdp capability if unsupported seleniumVersion', function(done) {
      var os = 'macbsr', seleniumVersion = '3.12.2';
      var requestBody = helper.getCapabilities({
        'browserstack.selenium_version': seleniumVersion,
        browser: 'chrome',
        'browserstack.seleniumCdp': 'true'
      }),
          railsScope = helper.buildRails(),
          macTerminalScope = helper.buildMac({
            options: function(options) {
              options.seleniumVersion.should.equal(seleniumVersion);
              return [200, { done: true }];
            }
          });

      var seleniumScope = helper.selenium(os, seleniumVersion)
      .post('/wd/hub/session')
      .query(true)
      .reply(function(uri, body) {
        return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
      });

      helper.request.post('/wd/hub/session')
      .auth('test', 'test')
      .send(requestBody)
      .end(function(err, res) {
        railsScope.isDone().should.be.true;
        macTerminalScope.isDone().should.be.true;
        seleniumScope.isDone().should.be.true;
        res.status.should.equal(200);
        res.body.value.should.have.any.keys('browser', 'browser_version', 'os', 'os_version');
        res.body.sessionId.should.equal(helper.SESSION_ID);
        res.body.status.should.equal(0);
        done();
      });
    });
    it('Selenium JAR version', function(done) {
      var os = 'macyos', seleniumVersion = '2.48.2';
      var requestBody = helper.getCapabilities({'browserstack.selenium_version': seleniumVersion}),
          railsScope = helper.buildRails(),
          macTerminalScope = helper.buildMac({
            options: function(options) {
              options.seleniumVersion.should.equal(seleniumVersion);
              return [200, { done: true }];
            }
          });

      var seleniumScope = helper.selenium(os, seleniumVersion)
      .post('/wd/hub/session')
      .query(true)
      .reply(function(uri, body) {
        return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
      });

      helper.request.post('/wd/hub/session')
      .auth('test', 'test')
      .send(requestBody)
      .end(function(err, res) {
        railsScope.isDone().should.be.true;
        macTerminalScope.isDone().should.be.true;
        seleniumScope.isDone().should.be.true;
        res.status.should.equal(200);
        res.body.value.should.have.any.keys('browser', 'browser_version', 'os', 'os_version');
        res.body.sessionId.should.equal(helper.SESSION_ID);
        res.body.status.should.equal(0);
        done();
      });
    });
    it('IE Driver version', function(done) {
      var requestBody = helper.getCapabilities({
            browser: 'IE',
            os: 'Windows',
            os_version: '7',
            browser_version: 9,
            'browserstack.ie.driver': '2.40'
          }),
          railsScope = helper.buildRails(),
          winTerminalScope = helper.buildWin({
            action: function(options) {
              options.ieDriver.should.equal('2.40');
              return [200, { done: true }];
            }
          });

      var seleniumScope = helper.selenium()
      .post('/wd/hub/session')
      .query(true)
      .reply(function(uri, body) {
        return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
      });

      helper.request.post('/wd/hub/session')
      .auth('test', 'test')
      .send(requestBody)
      .end(function(err, res) {
        railsScope.isDone().should.be.true;
        winTerminalScope.isDone().should.be.true;
        seleniumScope.isDone().should.be.true;
        res.status.should.equal(200);
        res.body.value.should.have.any.keys('browser', 'browser_version', 'os', 'os_version');
        res.body.sessionId.should.equal(helper.SESSION_ID);
        res.body.status.should.equal(0);
        done();
      });
    });
    it('Default IE Driver version for IE > 9 is 2.46', function(done) {
      var requestBody = helper.getCapabilities({
            browser: 'IE',
            os: 'Windows',
            os_version: '7',
            browser_version: 10
          }),
          railsScope = helper.buildRails(),
          winTerminalScope = helper.buildWin({
            action: function(options) {
              options.ieDriver.should.equal('2.46');
              return [200, { done: true }];
            }
          });

      var seleniumScope = helper.selenium()
      .post('/wd/hub/session')
      .query(true)
      .reply(function(uri, body) {
        return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
      });

      helper.request.post('/wd/hub/session')
      .auth('test', 'test')
      .send(requestBody)
      .end(function(err, res) {
        railsScope.isDone().should.be.true;
        winTerminalScope.isDone().should.be.true;
        seleniumScope.isDone().should.be.true;
        res.status.should.equal(200);
        res.body.value.should.have.any.keys('browser', 'browser_version', 'os', 'os_version');
        res.body.sessionId.should.equal(helper.SESSION_ID);
        res.body.status.should.equal(0);
        done();
      });
    });
    it('enable popups for IE', function(done) {
      var requestBody = helper.getCapabilities({
            browser: 'IE',
            os: 'Windows',
            os_version: '7',
            browser_version: 9,
            'browserstack.ie.enablePopups': true
          }),
          railsScope = helper.buildRails(),
          winTerminalScope = helper.buildWin({
            action: function(options) {
              options.enablePopupsIE.should.equal('true');
              return [200, { done: true }];
            }
          });

      var seleniumScope = helper.selenium()
      .post('/wd/hub/session')
      .query(true)
      .reply(function(uri, body) {
        return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
      });

      helper.request.post('/wd/hub/session')
      .auth('test', 'test')
      .send(requestBody)
      .end(function(err, res) {
        railsScope.isDone().should.be.true;
        winTerminalScope.isDone().should.be.true;
        seleniumScope.isDone().should.be.true;
        res.status.should.equal(200);
        res.body.value.should.have.any.keys('browser', 'browser_version', 'os', 'os_version');
        res.body.sessionId.should.equal(helper.SESSION_ID);
        res.body.status.should.equal(0);
        done();
      });
    });
    it('enable popups and sidebar for edge', function(done) {
      var requestBody = helper.getCapabilities({
            browser: 'edge',
            os: 'Windows',
            os_version: '10',
            browser_version: 85,
            'browserstack.edge.enablePopups': true,
            'browserstack.edge.enableSidebar': true,
            'browserstack.edge.enablePasswordManager' : true,
            'browserstack.enableFlash' : true,
          }),
          railsScope = helper.buildRails(),
          winTerminalScope = helper.buildWin({
            action: function(options) {
              options.enablePopupsEdge.should.equal('true');
              options.enableSidebarEdge.should.equal('true');
              options.enablePasswordManager.should.equal('true');
              options.enableFlashEdge.should.equal('true');
              return [200, { done: true }];
            }
          });

      var seleniumScope = helper.selenium()
      .post('/wd/hub/session')
      .query(true)
      .reply(function(uri, body) {
        return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
      });

      helper.request.post('/wd/hub/session')
      .auth('test', 'test')
      .send(requestBody)
      .end(function(err, res) {
        railsScope.isDone().should.be.true;
        winTerminalScope.isDone().should.be.true;
        seleniumScope.isDone().should.be.true;
        res.status.should.equal(200);
        res.body.value.should.have.any.keys('browser', 'browser_version', 'os', 'os_version');
        res.body.sessionId.should.equal(helper.SESSION_ID);
        res.body.status.should.equal(0);
        done();
      });
    });
    it('IE compatibility mode', function(done) {
      var requestBody = helper.getCapabilities({
            browser: 'IE',
            os: 'Windows',
            os_version: '7',
            browser_version: 11,
            'browserstack.ie.compatibility': 7000
          }),
          railsScope = helper.buildRails(),
          winTerminalScope = helper.buildWin({
            action: function(options) {
              options.ie_compatibility.should.equal('7000');
              return [200, { done: true }];
            }
          });

      var seleniumScope = helper.selenium()
      .post('/wd/hub/session')
      .query(true)
      .reply(function(uri, body) {
        return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
      });

      helper.request.post('/wd/hub/session')
      .auth('test', 'test')
      .send(requestBody)
      .end(function(err, res) {
        railsScope.isDone().should.be.true;
        winTerminalScope.isDone().should.be.true;
        seleniumScope.isDone().should.be.true;
        res.status.should.equal(200);
        res.body.value.should.have.any.keys('browser', 'browser_version', 'os', 'os_version');
        res.body.sessionId.should.equal(helper.SESSION_ID);
        res.body.status.should.equal(0);
        done();
      });
    });
    it('Windows high contrast mode', function(done) {
      var requestBody = helper.getCapabilities({
            browser: 'IE',
            os: 'Windows',
            os_version: '7',
            browser_version: 11,
            "browserstack.high_contrast": true
          }),
          railsScope = helper.buildRails(),
          winTerminalScope = helper.buildWin({
            action: function(options) {
              options.high_contrast.should.equal('true');
              return [200, { done: true }];
            }
          });

      var seleniumScope = helper.selenium()
      .post('/wd/hub/session')
      .query(true)
      .reply(function(uri, body) {
        return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
      });

      helper.request.post('/wd/hub/session')
      .auth('test', 'test')
      .send(requestBody)
      .end(function(err, res) {
        railsScope.isDone().should.be.true;
        winTerminalScope.isDone().should.be.true;
        seleniumScope.isDone().should.be.true;
        res.status.should.equal(200);
        res.body.value.should.have.any.keys('browser', 'browser_version', 'os', 'os_version');
        res.body.sessionId.should.equal(helper.SESSION_ID);
        res.body.status.should.equal(0);
        done();
      });
    });
    it('force create process API for IE', function(done) {
      var requestBody = helper.getCapabilities({
            browser: 'IE',
            os: 'Windows',
            os_version: '7',
            browser_version: 9
          }),
          railsScope = helper.buildRails(),
          winTerminalScope = helper.buildWin({
            action: function(options) {
              options.tabProcGrowth.should.equal('0');
              return [200, { done: true }];
            }
          });
      requestBody['desiredCapabilities']['ie.forceCreateProcessApi'] = true;

      var seleniumScope = helper.selenium()
        .post('/wd/hub/session')
        .query(true)
        .reply(function(uri, body) {
          return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
        });

      helper.request.post('/wd/hub/session')
        .auth('test', 'test')
        .send(requestBody)
        .end(function(err, res) {
          railsScope.isDone().should.be.true;
          winTerminalScope.isDone().should.be.true;
          seleniumScope.isDone().should.be.true;
          res.status.should.equal(200);
          res.body.value.should.have.any.keys('browser', 'browser_version', 'os', 'os_version');
          res.body.sessionId.should.equal(helper.SESSION_ID);
          res.body.status.should.equal(0);
          done();
        });
    });
    it('correctly forwards on hosts capability with legacy caps', function(done) {
      var requestBody = helper.getCapabilities(),
          railsScope = helper.buildRails(),
          macTerminalScope = helper.buildMac({
            action: function(options) {
              options.new_hosts.should.equal(hosts);
              return [200, { done: true }];
            }
          }),
          hosts = 'google.com;reddit.com;duckduckgo.com';

      requestBody['desiredCapabilities']['browserstack.hosts'] = hosts;
      var seleniumScope = helper.selenium()
        .post('/wd/hub/session')
        .query(true)
        .reply(function(uri, body) {
          return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
        });

      helper.request.post('/wd/hub/session')
        .auth('test', 'test')
        .send(requestBody)
        .end(function(err, res) {
          railsScope.isDone().should.be.true;
          macTerminalScope.isDone().should.be.true;
          seleniumScope.isDone().should.be.true;
          res.status.should.equal(200);
          res.body.value.should.have.any.keys('browser', 'browser_version', 'os', 'os_version');
          res.body.sessionId.should.equal(helper.SESSION_ID);
          res.body.status.should.equal(0);
          done();
        });
    });
    it('correctly forwards on hosts capability with hybrid caps', function(done) {
      var requestBody = helper.getCapabilities(),
          railsScope = helper.buildRails(),
          macTerminalScope = helper.buildMac({
            action: function(options) {
              options.new_hosts.should.equal(hosts);
              return [200, { done: true }];
            }
          }),
          hosts = 'google.com;reddit.com;duckduckgo.com';

      requestBody['desiredCapabilities']['bstack:options'] = {'hosts': hosts};
      var seleniumScope = helper.selenium()
        .post('/wd/hub/session')
        .query(true)
        .reply(function(uri, body) {
          return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
        });

      helper.request.post('/wd/hub/session')
        .auth('test', 'test')
        .send(requestBody)
        .end(function(err, res) {
          railsScope.isDone().should.be.true;
          macTerminalScope.isDone().should.be.true;
          seleniumScope.isDone().should.be.true;
          res.status.should.equal(200);
          res.body.sessionId.should.equal(helper.SESSION_ID);
          res.body.status.should.equal(0);
          done();
        });
    });
    it('correctly forwards on hosts capability with w3c caps firstMatch', function(done) {
      var requestBody = helper.getCapabilities(),
          railsScope = helper.buildRails(),
          macTerminalScope = helper.buildMac({
            action: function(options) {
              options.new_hosts.should.equal(hosts);
              return [200, { done: true }];
            }
          }),
          hosts = 'google.com;reddit.com;duckduckgo.com';

      requestBody['capabilities'] = {'firstMatch': [{'bstack:options': {'hosts': hosts}}]};

      var seleniumScope = helper.selenium()
        .post('/wd/hub/session')
        .query(true)
        .reply(function(uri, body) {
          return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
        });

      helper.request.post('/wd/hub/session')
        .auth('test', 'test')
        .send(requestBody)
        .end(function(err, res) {
          railsScope.isDone().should.be.true;
          macTerminalScope.isDone().should.be.true;
          seleniumScope.isDone().should.be.true;
          res.status.should.equal(200);
          res.body.sessionId.should.equal(helper.SESSION_ID);
          res.body.status.should.equal(0);
          done();
        });
    });
    it('correctly forwards on hosts capability with w3c caps firstMatch along with seleniumBidi', function(done) {
      var requestBody = helper.getCapabilities(),
          railsScope = helper.buildRails(),
          macTerminalScope = helper.buildMac({
            action: function(options) {
              options.new_hosts.should.equal(hosts);
              return [200, { done: true }];
            }
          }),
          hosts = 'google.com;reddit.com;duckduckgo.com';

      requestBody['capabilities'] = {'firstMatch': [{'bstack:options': {'hosts': hosts, "seleniumBidi": "true", 'seleniumVersion': '4.20.0'}}]};

      var seleniumScope = helper.selenium()
        .post('/wd/hub/session')
        .query(true)
        .reply(function(uri, body) {
          return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
        });

      helper.request.post('/wd/hub/session')
        .auth('test', 'test')
        .send(requestBody)
        .end(function(err, res) {
          railsScope.isDone().should.be.true;
          macTerminalScope.isDone().should.be.true;
          seleniumScope.isDone().should.be.true;
          res.status.should.equal(200);
          res.body.sessionId.should.equal(helper.SESSION_ID);
          res.body.status.should.equal(0);
          done();
        });
    });
    it('correctly forwards on hosts capability with alwaysMatch', function(done) {
      var requestBody = helper.getCapabilities(),
          railsScope = helper.buildRails(),
          macTerminalScope = helper.buildMac({
            action: function(options) {
              options.new_hosts.should.equal(hosts);
              return [200, { done: true }];
            }
          }),
          hosts = 'google.com;reddit.com;duckduckgo.com';

      requestBody['capabilities'] = {'alwaysMatch': {'bstack:options': {'hosts': hosts}}};

      var seleniumScope = helper.selenium()
        .post('/wd/hub/session')
        .query(true)
        .reply(function(uri, body) {
          return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
        });

      helper.request.post('/wd/hub/session')
        .auth('test', 'test')
        .send(requestBody)
        .end(function(err, res) {
          railsScope.isDone().should.be.true;
          macTerminalScope.isDone().should.be.true;
          seleniumScope.isDone().should.be.true;
          res.status.should.equal(200);
          res.body.sessionId.should.equal(helper.SESSION_ID);
          res.body.status.should.equal(0);
          done();
        });
    });
    it('Safari Driver version', function(done) {
      var os = 'macyos', seleniumVersion = '2.52.0';
      var requestBody = helper.getCapabilities({
            browser: 'Safari',
            os: 'OS X',
            os_version: 'Yosemite',
            browser_version: 8,
            'browserstack.safari.driver': '2.45'
          }),
          railsScope = helper.buildRails(),
          macTerminalScope = helper.buildMac({
            action: function(options) {
              options.copySafariDriverVersion.should.equal('2.45');
              options.seleniumVersion.should.equal('2.52.0');
              return [200, { done: true }];
            }
          });

      var seleniumScope = helper.selenium(os, seleniumVersion)
      .post('/wd/hub/session')
      .query(true)
      .reply(function(uri, body) {
        return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
      });

      helper.request.post('/wd/hub/session')
      .auth('test', 'test')
      .send(requestBody)
      .end(function(err, res) {
        railsScope.isDone().should.be.true;
        macTerminalScope.isDone().should.be.true;
        seleniumScope.isDone().should.be.true;
        res.status.should.equal(200);
        res.body.value.should.have.any.keys('browser', 'browser_version', 'os', 'os_version');
        res.body.sessionId.should.equal(helper.SESSION_ID);
        res.body.status.should.equal(0);
        done();
      });
    });
    it('Chrome Driver version', function(done) {
      var requestBody = helper.getCapabilities(),
          railsScope = helper.buildRails(),
          macTerminalScope = helper.buildMac({
            action: function(options) {
              options.chromeDriverVersion.should.equal('2.20');
              return [200, { done: true }];
            }
          });

      var seleniumScope = helper.selenium()
      .post('/wd/hub/session')
      .query(true)
      .reply(function(uri, body) {
        return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
      });

      helper.request.post('/wd/hub/session')
      .auth('test', 'test')
      .send(requestBody)
      .end(function(err, res) {
        railsScope.isDone().should.be.true;
        macTerminalScope.isDone().should.be.true;
        seleniumScope.isDone().should.be.true;
        res.status.should.equal(200);
        res.body.value.should.have.any.keys('browser', 'browser_version', 'os', 'os_version');
        res.body.sessionId.should.equal(helper.SESSION_ID);
        res.body.status.should.equal(0);
        done();
      });
    });
    it('Chrome Driver non default version', function(done) {
      var requestBody = helper.getCapabilities({
            browser: 'Chrome',
            os: 'OS X',
            os_version: 'Yosemite',
            browser_version: 46,
            'browserstack.chrome.driver': '2.20'
          }),
          railsScope = helper.buildRails(),
          macTerminalScope = helper.buildMac({
            action: function(options) {
              options.chromeDriverVersion.should.equal('2.20');
              return [200, { done: true }];
            }
          });

      var seleniumScope = helper.selenium()
      .post('/wd/hub/session')
      .query(true)
      .reply(function(uri, body) {
        return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
      });

      helper.request.post('/wd/hub/session')
      .auth('test', 'test')
      .send(requestBody)
      .end(function(err, res) {
        railsScope.isDone().should.be.true;
        macTerminalScope.isDone().should.be.true;
        seleniumScope.isDone().should.be.true;
        res.status.should.equal(200);
        res.body.value.should.have.any.keys('browser', 'browser_version', 'os', 'os_version');
        res.body.sessionId.should.equal(helper.SESSION_ID);
        res.body.status.should.equal(0);
        done();
      });
    });
    it('Chrome Driver should be 2.22 for Chrome 49 on other Windows', function(done) {
      var capability = helper.getCapabilities( { os: 'Windows', os_version: '7', browser: 'chrome', browser_version: 49 } ),
          winTerminalScope = helper.buildWin({
            action: function(options) {
              options.chromeDriverVersion.should.equal('2.22');
              return [200, { done: true }];
            }
          });

      helper.doSelenium.startSession(capability)
          .then(function(res) {
            res.status.should.equal(200);
            res.body.value.should.have.any.keys('browser', 'browser_version', 'os', 'os_version');
            res.body.sessionId.should.equal(helper.SESSION_ID);
            res.body.status.should.equal(0);
            done();
          });

    });

    it('Chrome Driver should not be sent in fire command for real devices', function(done) {
      var requestBody = helper.getCapabilities({
            browser: 'Chrome',
            device : 'Google Nexus 6',
            realMobile: true }),
          railsScope = helper.buildRails(),
          macTerminalScope = helper.buildMac({
            action: function(options) {
              options.should.not.have.keys('chromeDriverVersion');
              return [200, { done: true }];
            }
          });

      var seleniumScope = helper.appiumZotac()
      .post('/wd/hub/session')
      .query(true)
      .reply(function(uri, body) {
        return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
      });

      helper.request.post('/wd/hub/session')
      .auth('test', 'test')
      .send(requestBody)
      .end(function(err, res) {
        railsScope.isDone().should.be.true;
        macTerminalScope.isDone().should.be.true;
        seleniumScope.isDone().should.be.true;
        res.status.should.equal(200);
        res.body.sessionId.should.equal(helper.SESSION_ID);
        res.body.status.should.equal(0);
        done();
      });
    });
    it('Chrome Driver should not be sent in fire command for emulators', function(done) {
      var os = 'macyos', seleniumVersion = '2.52.0';
      var requestBody = helper.getCapabilities({
            browser: 'Chrome',
            device: 'Google Nexus 6',
            realMobile: false}),
          railsScope = helper.buildRails(),
          macTerminalScope = helper.buildMac({
            action: function(options) {
              options.should.not.have.keys('chromeDriverVersion');
              return [200, { done: true }];
            }
          });
      var seleniumScope = helper.selenium(os, seleniumVersion)
        .post('/wd/hub/session')
        .query(true)
        .reply(function(uri, body) {
          return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
        });

      helper.request.post('/wd/hub/session')
      .auth('test', 'test')
      .send(requestBody)
      .end(function(err, res) {
        railsScope.isDone().should.be.true;
        macTerminalScope.isDone().should.be.true;
        seleniumScope.isDone().should.be.true;
        res.status.should.equal(200);
        res.body.sessionId.should.equal(helper.SESSION_ID);
        res.body.value.browserName.should.equal('chrome');
        res.body.value.platform.should.equal('ANDROID');
        res.body.value.deviceName.should.equal('Google Nexus 6-5.0');
        res.body.status.should.equal(0);
        done();
      });
    });
    it('correctly forwards if Local is true', function(done) {
      var requestBody = helper.getCapabilities(),
          railsScope = helper.buildRails({
            query: {'auth': 'selautomate', 'local': 'true'}
          });
          macTerminalScope = helper.buildMac({
            action: function(options) {
              options.local.should.equal('true');
              return [200, { done: true }];
            }
          });

      requestBody['desiredCapabilities']['browserstack.local'] = true;
      var seleniumScope = helper.selenium()
        .post('/wd/hub/session')
        .query(true)
        .reply(function(uri, body) {
          return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
        });

      helper.request.post('/wd/hub/session')
        .auth('test', 'test')
        .send(requestBody)
        .end(function(err, res) {
          railsScope.isDone().should.be.true;
          macTerminalScope.isDone().should.be.true;
          seleniumScope.isDone().should.be.true;
          res.status.should.equal(200);
          res.body.value.should.have.any.keys('browser', 'browser_version', 'os', 'os_version');
          res.body.sessionId.should.equal(helper.SESSION_ID);
          res.body.status.should.equal(0);
          done();
        });
    });
    it('should correctly forward for appium ios', function(done) {
      var appiumForceSeleniumPort = 5555;
      var requestBody = helper.getCapabilities({device: 'iPhone 6 Plus'});
      delete requestBody.desiredCapabilities["browser"];

      var railsScope = helper.buildRails();
          macTerminalScope = helper.buildMac({
            action: function(options) {
              options.appiumios.should.equal('true');
              options.iosVersion.should.equal('8.3');
              return [200, { done: true }];
            }
          });

      var seleniumScope = helper.selenium(null, null, appiumForceSeleniumPort)
        .post('/wd/hub/session')
        .query(true)
        .reply(function(uri, body) {
          return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
        });

      helper.request.post('/wd/hub/session')
        .auth('test', 'test')
        .send(requestBody)
        .end(function(err, res) {
          railsScope.isDone().should.be.true;
          macTerminalScope.isDone().should.be.true;
          seleniumScope.isDone().should.be.true;
          res.status.should.equal(200);
          res.body.value.should.have.any.keys('browser', 'browser_version', 'os', 'os_version');
          res.body.sessionId.should.equal(helper.SESSION_ID);
          res.body.status.should.equal(0);
          done();
        });
    });
    it('should correctly forward for appium android', function(done) {
      var appiumForceSeleniumPort = 5555;
      var requestBody = helper.getCapabilities({ device: 'Samsung Galaxy S5 Mini' });
      delete requestBody.desiredCapabilities["browser"];

      var railsScope = helper.buildRails();
          macTerminalScope = helper.buildMac({
            action: function(options) {
              options.appiumandroid.should.equal('true');
              options.deviceName.should.equal('Samsung__Galaxy__S5__Mini-4.4-720x1280');
              return [200, { done: true }];
            }
          });

      var seleniumScope = helper.selenium(null, null, appiumForceSeleniumPort)
        .post('/wd/hub/session')
        .query(true)
        .reply(function(uri, body) {
          return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
        });

      helper.request.post('/wd/hub/session')
        .auth('test', 'test')
        .send(requestBody)
        .end(function(err, res) {
          railsScope.isDone().should.be.true;
          macTerminalScope.isDone().should.be.true;
          seleniumScope.isDone().should.be.true;
          res.status.should.equal(200);
          res.body.sessionId.should.equal(helper.SESSION_ID);
          res.body.status.should.equal(0);
          done();
        });
    });
  });

  describe('App automate Tests', function() {
    describe('Generic Tests', function() {
      it('absent app capability', function(done) {
        var requestBody = helper.AppAutomateCapabilities({'browserstack.video': true}, ["app"])
        var railsScope = helper.buildRails()
        var appAutomateRailsScope = helper.buildRails({isAppAutomate: true})
        var macTerminalScope = helper.buildMac({
            action: function(options) {
              return [200, { done: true }];
            }
          });
        seleniumScope = helper.selenium(undefined, undefined, 5678)
            .post('/wd/hub/session')
            .query(true)
            .reply(function(uri, body) {
              return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
            });

        helper.request.post('/wd/hub/session')
          .auth('test', 'test')
          .send(requestBody)
          .end(function(err, res) {
            railsScope.isDone().should.be.true;
            appAutomateRailsScope.isDone().should.be.false;
            macTerminalScope.isDone().should.be.true;
            res.status.should.equal(200);
            seleniumScope.isDone().should.be.true;
            done();
          });
      });

      it('invalid app capability', function(done) {
        var requestBody = helper.AppAutomateCapabilities({"app": ""})
        var railsScope = helper.buildRails()
        var appAutomateRailsScope = helper.buildRails({isAppAutomate: true})

        helper.request.post('/wd/hub/session')
          .auth('test', 'test')
          .send(requestBody)
          .end(function(err, res) {
            railsScope.isDone().should.be.false;
            appAutomateRailsScope.isDone().should.be.true;
            res.status.should.equal(200);
            text = JSON.parse(res.text)
            text.value.message.should.equal('[app] capability must be a valid BrowserStack App URL.')
            done();
          });
      });

      it('does not retry in case of user error', function(done) {
        var requestBody = helper.AppAutomateCapabilities();
        var railsScope = helper.buildRails();
        var appAutomateRailsScope = helper.buildRails({ isAppAutomate: true });
        var appAutomateRailsScope2 = helper.buildRails({ isAppAutomate: true });
        var macTerminalScope = helper.buildMac({
            action: [200, { error: 'some error occured', kind: 'some-user-error', type: constants.FIRECMD_USER_ERROR_STRING }],
        });

        helper.request.post('/wd/hub/session')
          .auth('test', 'test')
          .send(requestBody)
          .end(function(err, res) {
            railsScope.isDone().should.be.false;
            appAutomateRailsScope.isDone().should.be.true;
            macTerminalScope.isDone().should.be.true;
            appAutomateRailsScope2.isDone().should.be.false;
            res.body.status.should.equal(13);
            text = JSON.parse(res.text);
            text.value.message.should.equal('Could not start a session. Something went wrong with app launch. Please try to run the test again.');
            done();
          });
      });

      it('setting realMobile to false', function(done) {
        var requestBody = helper.AppAutomateCapabilities({"realMobile": "false"})
        var railsScope = helper.buildRails()
        var appAutomateRailsScope = helper.buildRails({isAppAutomate: true})

        helper.request.post('/wd/hub/session')
          .auth('test', 'test')
          .send(requestBody)
          .end(function(err, res) {
            railsScope.isDone().should.be.false;
            appAutomateRailsScope.isDone().should.be.true;
            res.status.should.equal(200);
            text = JSON.parse(res.text)
            text.value.message.should.equal('Could not find device: Google Pixel')
            done();
          });
      });

      it('hub switch for rails console', function(done) {
        var requestBody = helper.AppAutomateCapabilities(),
          railsScope = helper.buildRails(),
          appAutomateRailsScope = helper.buildRails({isAppAutomate: true}),
          macTerminalScope = helper.buildMac({
            action: function(options) {
              return [200, { done: true }];
            }
          });
          seleniumScope = helper.selenium(undefined, undefined, helper.APPIUM_PORT)
            .post('/wd/hub/session')
            .query(true)
            .reply(function(uri, body) {
              return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
            });
        helper.request.post('/wd/hub/session')
          .auth('test', 'test')
          .send(requestBody)
          .end(function(err, res) {
            railsScope.isDone().should.be.false;
            macTerminalScope.isDone().should.be.true;
            seleniumScope.isDone().should.be.true;
            appAutomateRailsScope.isDone().should.be.true;
            done();
          });
      });

      it('hub switch for reg_session request', function(done) {
        var requestBody = helper.AppAutomateCapabilities(),
          appAutomateRailsScope = helper.buildRails({isAppAutomate: true}),
          resSessionRails = helper.buildRails({
            query: true,
            action: function(uri, body){
              uri.should.include("auth=selautomate")
              uri.should.include("ci=" + helper.TERMINAL_IP)
              uri.should.include("reg_session=true")
              uri.should.include("selenium_key=" + helper.TERMINAL_SESSION_ID)
              uri.should.include("start_time_ts=")
              return [200, {
                ip: helper.TERMINAL_IP,
                desiredCapabilities: {},
                automation_session_id: helper.SESSION_ID,
                collection_number: 1
              }]
            },
            isAppAutomate: true
          }),
          macTerminalScope = helper.buildMac({
            action: function(options) {
              return [200, { done: true }];
            }
          }),
          seleniumScope = helper.selenium(undefined, undefined, helper.APPIUM_PORT)
          .post('/wd/hub/session')
          .query(true)
          .reply(function(uri, body) {
            return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
          });
        helper.request.post('/wd/hub/session')
          .auth('test', 'test')
          .send(requestBody)
          .end(function(err, res) {
            macTerminalScope.isDone().should.be.true;
            seleniumScope.isDone().should.be.true;
            appAutomateRailsScope.isDone().should.be.true;
            setTimeout(() => {
              resSessionRails.isDone().should.be.true;
              done();
            }, 20);
          });
      });

      it('snapshot_hub request', function(done) {
        var requestBody = helper.AppAutomateCapabilities(),
          appAutomateRailsScope = helper.buildRails({isAppAutomate: true}),
          regSessionRails = helper.buildRails({
            query: true,
            action: function(uri, body){
              return [200, {
                ip: helper.TERMINAL_IP,
                desiredCapabilities: {},
                automation_session_id: helper.SESSION_ID,
                collection_number: 1
              }]
            },
            isAppAutomate: true
          }),
          macTerminalScope = helper.buildMac({
            action: function(options) {
              return [200, { done: true }];
            }
          });
        seleniumScope = helper.selenium(undefined, undefined, helper.APPIUM_PORT)
          .post('/wd/hub/session')
          .query(true)
          .reply(function(uri, body) {
            return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
          }),
          snapshotMacTerminalScope = helper.buildMac({
            uri: '/snapshot_hub',
            action: function(uri, body){
              uri.should.have.all.keys('bucket', 'folder', 'file', 'device', 'orientation','zombie_host','zombie_port', 'key', 'secret', 'useADBforScreenshot');
              uri.should.have.property('bucket', helper.constants.APP_AUTOMATE_LOGS_AWS_BUCKET);
              uri.should.have.property('folder', helper.SESSION_ID);
              uri.should.have.property('file', 'screenshot-1');
              uri.should.have.property('device', helper.UDID);
              uri.should.have.property('orientation', 'portrait');
              uri.should.have.property('key', helper.constants.DEBUGSCREENSHOT_AWS_KEY);
              uri.should.have.property('secret', helper.constants.DEBUGSCREENSHOT_AWS_SECRET);
              uri.should.have.property('useADBforScreenshot', 'true');
              return [200, { done: true }]
            }
          });
        helper.request.post('/wd/hub/session')
          .auth('test', 'test')
          .send(requestBody)
          .end(function(err, res) {
            macTerminalScope.isDone().should.be.true;
            seleniumScope.isDone().should.be.true;
            appAutomateRailsScope.isDone().should.be.true;
            snapshotMacTerminalScope.isDone().should.be.true;
            setTimeout(() => {
              regSessionRails.isDone().should.be.true;
              done();
            }, 20);
          });
      });
    });

    describe('FireCmd Params for any Appium Sessions', function() {
      it('Add networkLogs flag in fire_cmd if cap is set to true', (done) => {
        var requestBody = helper.AppAutomateCapabilities({"app": "bs://testapp", "browserstack.networkLogs": "true"}),
          railsScope = helper.buildRails(),
          appAutomateRailsScope = helper.buildRails({isAppAutomate: true}),
          macTerminalScope = helper.buildMac({
            action: function(options) {
              return [200, { done: true }];
            }
          });

        seleniumScope = helper.selenium(undefined, undefined, helper.APPIUM_PORT)
        .post('/wd/hub/session')
        .query(true)
        .reply(function(uri, body) {
          return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
        });

        const encodedURLFn = sinon.spy(requestlib, 'getEncodedURLParams');

        helper.request.post('/wd/hub/session')
          .auth('test', 'test')
          .send(requestBody)
          .end((err, res) => {

            railsScope.isDone().should.equal(false);
            appAutomateRailsScope.isDone().should.equal(true);
            macTerminalScope.isDone().should.equal(true);
            seleniumScope.isDone().should.equal(true);
            res.status.should.equal(200);

            expect(encodedURLFn.calledOnce);
            const fireCommandArguments = encodedURLFn.getCall(0).args[0];
            assert(fireCommandArguments.networkLogs, true);

            res.body.sessionId.should.equal(helper.SESSION_ID);
            res.body.status.should.equal(0);

            expect(err === null);
            encodedURLFn.restore();
            done();
          });
      });
      it('Should not add networkLogs flag in fire_cmd if cap is not set', (done) => {
        var requestBody = helper.AppAutomateCapabilities({"app": "bs://testapp"}),
          railsScope = helper.buildRails(),
          appAutomateRailsScope = helper.buildRails({isAppAutomate: true}),
          macTerminalScope = helper.buildMac({
            action: function(options) {
              return [200, { done: true }];
            }
          });

        seleniumScope = helper.selenium(undefined, undefined, helper.APPIUM_PORT)
        .post('/wd/hub/session')
        .query(true)
        .reply(function(uri, body) {
          return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
        });

        const encodedURLFn = sinon.spy(requestlib, 'getEncodedURLParams');

        helper.request.post('/wd/hub/session')
          .auth('test', 'test')
          .send(requestBody)
          .end((err, res) => {

            railsScope.isDone().should.equal(false);
            appAutomateRailsScope.isDone().should.equal(true);
            macTerminalScope.isDone().should.equal(true);
            seleniumScope.isDone().should.equal(true);
            res.status.should.equal(200);

            expect(encodedURLFn.calledOnce);
            const fireCommandArguments = encodedURLFn.getCall(0).args[0];
            expect(fireCommandArguments).to.not.have.all.keys('networkLogs');

            res.body.sessionId.should.equal(helper.SESSION_ID);
            res.body.status.should.equal(0);

            expect(err === null);
            encodedURLFn.restore();
            done();
          });
      });
      it('Add acceptInsecureCerts flag in fire_cmd if cap is set to true', (done) => {
        var requestBody = helper.AppAutomateCapabilities({"app": "bs://testapp", "browserstack.acceptInsecureCerts": "true"}),
          railsScope = helper.buildRails(),
          appAutomateRailsScope = helper.buildRails({isAppAutomate: true}),
          macTerminalScope = helper.buildMac({
            action: function(options) {
              return [200, { done: true }];
            }
          });

        seleniumScope = helper.selenium(undefined, undefined, helper.APPIUM_PORT)
        .post('/wd/hub/session')
        .query(true)
        .reply(function(uri, body) {
          return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
        });

        const encodedURLFn = sinon.spy(requestlib, 'getEncodedURLParams');

        helper.request.post('/wd/hub/session')
          .auth('test', 'test')
          .send(requestBody)
          .end((err, res) => {

            railsScope.isDone().should.equal(false);
            appAutomateRailsScope.isDone().should.equal(true);
            macTerminalScope.isDone().should.equal(true);
            seleniumScope.isDone().should.equal(true);
            res.status.should.equal(200);

            expect(encodedURLFn.calledOnce);
            const fireCommandArguments = encodedURLFn.getCall(0).args[0];
            assert(fireCommandArguments.acceptInsecureCerts, true);

            res.body.sessionId.should.equal(helper.SESSION_ID);
            res.body.status.should.equal(0);

            expect(err === null);
            encodedURLFn.restore();
            done();
          });
      });
      it('Should not add acceptInsecureCerts flag in fire_cmd if cap is not set', (done) => {
        var requestBody = helper.AppAutomateCapabilities({"app": "bs://testapp"}),
          railsScope = helper.buildRails(),
          appAutomateRailsScope = helper.buildRails({isAppAutomate: true}),
          macTerminalScope = helper.buildMac({
            action: function(options) {
              return [200, { done: true }];
            }
          });

        seleniumScope = helper.selenium(undefined, undefined, helper.APPIUM_PORT)
        .post('/wd/hub/session')
        .query(true)
        .reply(function(uri, body) {
          return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
        });

        const encodedURLFn = sinon.spy(requestlib, 'getEncodedURLParams');

        helper.request.post('/wd/hub/session')
          .auth('test', 'test')
          .send(requestBody)
          .end((err, res) => {

            railsScope.isDone().should.equal(false);
            appAutomateRailsScope.isDone().should.equal(true);
            macTerminalScope.isDone().should.equal(true);
            seleniumScope.isDone().should.equal(true);
            res.status.should.equal(200);

            expect(encodedURLFn.calledOnce);
            const fireCommandArguments = encodedURLFn.getCall(0).args[0];
            expect(fireCommandArguments).to.not.have.all.keys('acceptInsecureCerts');

            res.body.sessionId.should.equal(helper.SESSION_ID);
            res.body.status.should.equal(0);

            expect(err === null);
            encodedURLFn.restore();
            done();
          });
      });
      it('Add otherApps in fire_cmd if cap is passed', (done) => {
        var requestBody = helper.AppAutomateCapabilities({"app": "bs://testapp", "otherApps": ["bs://otherApp"]}),
          railsScope = helper.buildRails(),
          appAutomateRailsScope = helper.buildRails({isAppAutomate: true}),
          macTerminalScope = helper.buildMac({
            action: function(options) {
              return [200, { done: true }];
            }
          });

        seleniumScope = helper.selenium(undefined, undefined, helper.APPIUM_PORT)
        .post('/wd/hub/session')
        .query(true)
        .reply(function(uri, body) {
          return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
        });

        const encodedURLFn = sinon.spy(requestlib, 'getEncodedURLParams');

        helper.request.post('/wd/hub/session')
          .auth('test', 'test')
          .send(requestBody)
          .end((err, res) => {

            railsScope.isDone().should.equal(false);
            appAutomateRailsScope.isDone().should.equal(true);
            macTerminalScope.isDone().should.equal(true);
            seleniumScope.isDone().should.equal(true);
            res.status.should.equal(200);

            expect(encodedURLFn.calledOnce);
            const fireCommandArguments = encodedURLFn.getCall(0).args[0];

            assert.ok(fireCommandArguments.other_apps !== undefined);
            res.body.sessionId.should.equal(helper.SESSION_ID);
            res.body.status.should.equal(0);

            expect(err === null);
            encodedURLFn.restore();
            done();
          });
      });
      it('Add midSessionInstallApps in fire_cmd if cap is passed', (done) => {
        var requestBody = helper.AppAutomateCapabilities({"app": "bs://testapp", "browserstack.midSessionInstallApps": ["bs://midSessionInstallApps"]}),
          railsScope = helper.buildRails(),
          appAutomateRailsScope = helper.buildRails({isAppAutomate: true}),
          macTerminalScope = helper.buildMac({
            action: function(options) {
              return [200, { done: true }];
            }
          });

        seleniumScope = helper.selenium(undefined, undefined, helper.APPIUM_PORT)
        .post('/wd/hub/session')
        .query(true)
        .reply(function(uri, body) {
          return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
        });

        const encodedURLFn = sinon.spy(requestlib, 'getEncodedURLParams');

        helper.request.post('/wd/hub/session')
          .auth('test', 'test')
          .send(requestBody)
          .end((err, res) => {

            railsScope.isDone().should.equal(false);
            appAutomateRailsScope.isDone().should.equal(true);
            macTerminalScope.isDone().should.equal(true);
            seleniumScope.isDone().should.equal(true);
            res.status.should.equal(200);

            expect(encodedURLFn.calledOnce);
            const fireCommandArguments = encodedURLFn.getCall(0).args[0];

            assert.ok(fireCommandArguments.mid_session_install_apps !== undefined);
            res.body.sessionId.should.equal(helper.SESSION_ID);
            res.body.status.should.equal(0);

            expect(err === null);
            encodedURLFn.restore();
            done();
          });
      });
      it('Add espressoServer in fire_cmd if cap is passed', (done) => {
        var requestBody = helper.AppAutomateCapabilities({"app": "bs://testapp", "browserstack.espressoServer": ["bs://espressoServerApp"]}),
          railsScope = helper.buildRails(),
          appAutomateRailsScope = helper.buildRails({isAppAutomate: true}),
          macTerminalScope = helper.buildMac({
            action: function(options) {
              return [200, { done: true }];
            }
          });

        seleniumScope = helper.selenium(undefined, undefined, helper.APPIUM_PORT)
        .post('/wd/hub/session')
        .query(true)
        .reply(function(uri, body) {
          return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
        });

        const encodedURLFn = sinon.spy(requestlib, 'getEncodedURLParams');

        helper.request.post('/wd/hub/session')
          .auth('test', 'test')
          .send(requestBody)
          .end((err, res) => {

            railsScope.isDone().should.equal(false);
            appAutomateRailsScope.isDone().should.equal(true);
            macTerminalScope.isDone().should.equal(true);
            seleniumScope.isDone().should.equal(true);
            res.status.should.equal(200);

            expect(encodedURLFn.calledOnce);
            const fireCommandArguments = encodedURLFn.getCall(0).args[0];

            assert.ok(fireCommandArguments.espresso_server_app !== undefined);
            res.body.sessionId.should.equal(helper.SESSION_ID);
            res.body.status.should.equal(0);

            expect(err === null);
            encodedURLFn.restore();
            done();
          });
      });

      it('Add detoxAndroidClient in fire_cmd if cap is passed', (done) => {
        var requestBody = helper.AppAutomateCapabilities({"app": "bs://testapp", "appClient": ["bs://detoxAndroidClient"]}),
          railsScope = helper.buildRails(),
          appAutomateRailsScope = helper.buildRails({isAppAutomate: true}),
          macTerminalScope = helper.buildMac({
            action: function(options) {
              return [200, { done: true }];
            }
          });

        seleniumScope = helper.selenium(undefined, undefined, helper.APPIUM_PORT)
        .post('/wd/hub/session')
        .query(true)
        .reply(function(uri, body) {
          return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
        });

        const encodedURLFn = sinon.spy(requestlib, 'getEncodedURLParams');

        helper.request.post('/wd/hub/session')
          .auth('test', 'test')
          .send(requestBody)
          .end((err, res) => {

            railsScope.isDone().should.equal(false);
            appAutomateRailsScope.isDone().should.equal(true);
            macTerminalScope.isDone().should.equal(true);
            seleniumScope.isDone().should.equal(true);
            res.status.should.equal(200);

            expect(encodedURLFn.calledOnce);
            const fireCommandArguments = encodedURLFn.getCall(0).args[0];

            assert.ok(fireCommandArguments.detox_app_client !== undefined);
            res.body.sessionId.should.equal(helper.SESSION_ID);
            res.body.status.should.equal(0);

            expect(err === null);
            encodedURLFn.restore();
            done();
          });
      });

      it('Add does not add detoxAndroidClient in fire_cmd if cap is passed', (done) => {
        var requestBody = helper.AppAutomateCapabilities({"app": "bs://testapp"}),
          railsScope = helper.buildRails(),
          appAutomateRailsScope = helper.buildRails({isAppAutomate: true}),
          macTerminalScope = helper.buildMac({
            action: function(options) {
              return [200, { done: true }];
            }
          });

        seleniumScope = helper.selenium(undefined, undefined, helper.APPIUM_PORT)
        .post('/wd/hub/session')
        .query(true)
        .reply(function(uri, body) {
          return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
        });

        const encodedURLFn = sinon.spy(requestlib, 'getEncodedURLParams');

        helper.request.post('/wd/hub/session')
          .auth('test', 'test')
          .send(requestBody)
          .end((err, res) => {

            railsScope.isDone().should.equal(false);
            appAutomateRailsScope.isDone().should.equal(true);
            macTerminalScope.isDone().should.equal(true);
            seleniumScope.isDone().should.equal(true);
            res.status.should.equal(200);

            expect(encodedURLFn.calledOnce);
            const fireCommandArguments = encodedURLFn.getCall(0).args[0];

            assert.ok(fireCommandArguments.detox_app_client == undefined);
            res.body.sessionId.should.equal(helper.SESSION_ID);
            res.body.status.should.equal(0);

            expect(err === null);
            encodedURLFn.restore();
            done();
          });
      });

      it('Add mediaFiles in fire_cmd if cap is passed', (done) => {
        var requestBody = helper.AppAutomateCapabilities({"app": "bs://testapp", "otherApps": ["bs://otherApp"]}),
          railsScope = helper.buildRails(),
          appAutomateRailsScope = helper.buildRails({isAppAutomate: true}),
          macTerminalScope = helper.buildMac({
            action: function(options) {
              return [200, { done: true }];
            }
          });

        requestBody.desiredCapabilities['mediaFiles'] = ["media://testmedia"];

        seleniumScope = helper.selenium(undefined, undefined, helper.APPIUM_PORT)
        .post('/wd/hub/session')
        .query(true)
        .reply(function(uri, body) {
          return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
        });

        const encodedURLFn = sinon.spy(requestlib, 'getEncodedURLParams');

        helper.request.post('/wd/hub/session')
          .auth('test', 'test')
          .send(requestBody)
          .end((err, res) => {

            railsScope.isDone().should.equal(false);
            appAutomateRailsScope.isDone().should.equal(true);
            macTerminalScope.isDone().should.equal(true);
            seleniumScope.isDone().should.equal(true);
            res.status.should.equal(200);

            expect(encodedURLFn.calledOnce);
            const fireCommandArguments = encodedURLFn.getCall(0).args[0];

            assert.ok(fireCommandArguments.custom_media !== undefined);
            res.body.sessionId.should.equal(helper.SESSION_ID);
            res.body.status.should.equal(0);

            expect(err === null);
            encodedURLFn.restore();
            done();
          });
      });
      it('Add localization params in fire_cmd if locale is passed', (done) => {
        var requestBody = helper.AppAutomateCapabilities({"app": "bs://testapp", "locale": "en_US"}),
          railsScope = helper.buildRails(),
          appAutomateRailsScope = helper.buildRails({isAppAutomate: true}),
          macTerminalScope = helper.buildMac({
            action: function(options) {
              return [200, { done: true }];
            }
          });

        requestBody.desiredCapabilities['localization'] = "en_US";

        seleniumScope = helper.selenium(undefined, undefined, helper.APPIUM_PORT)
        .post('/wd/hub/session')
        .query(true)
        .reply(function(uri, body) {
          return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
        });

        const encodedURLFn = sinon.spy(requestlib, 'getEncodedURLParams');

        helper.request.post('/wd/hub/session')
          .auth('test', 'test')
          .send(requestBody)
          .end((err, res) => {

            railsScope.isDone().should.equal(false);
            appAutomateRailsScope.isDone().should.equal(true);
            macTerminalScope.isDone().should.equal(true);
            seleniumScope.isDone().should.equal(true);
            res.status.should.equal(200);

            expect(encodedURLFn.calledOnce);
            const fireCommandArguments = encodedURLFn.getCall(0).args[0];
            assert(fireCommandArguments.localization, "en_US");

            res.body.sessionId.should.equal(helper.SESSION_ID);
            res.body.status.should.equal(0);

            expect(err === null);
            encodedURLFn.restore();
            done();
          });
      });

      it('Add non replay kit params in fire_cmd', (done) => {
        var requestBody = helper.AppAutomateCapabilities({"app": "bs://testapp", "locale": "en_US"}),
          railsScope = helper.buildRails(),
          appAutomateRailsScope = helper.buildRails({isAppAutomate: true}),
          macTerminalScope = helper.buildMac({
            action: function(options) {
              return [200, { done: true }];
            }
          });

        const non_replay_kit_params = {
          "captureWidth": 500,
          "captureHeight": 500
        };

        requestBody.desiredCapabilities['non_replay_kit_params'] = non_replay_kit_params;

        seleniumScope = helper.selenium(undefined, undefined, helper.APPIUM_PORT)
        .post('/wd/hub/session')
        .query(true)
        .reply(function(uri, body) {
          return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
        });

        const encodedURLFn = sinon.spy(requestlib, 'getEncodedURLParams');

        helper.request.post('/wd/hub/session')
          .auth('test', 'test')
          .send(requestBody)
          .end((err, res) => {

            railsScope.isDone().should.equal(false);
            appAutomateRailsScope.isDone().should.equal(true);
            macTerminalScope.isDone().should.equal(true);
            seleniumScope.isDone().should.equal(true);
            res.status.should.equal(200);

            expect(encodedURLFn.calledOnce);
            const fireCommandArguments = encodedURLFn.getCall(0).args[0];
            assert(fireCommandArguments.non_replay_kit_params, non_replay_kit_params);

            res.body.sessionId.should.equal(helper.SESSION_ID);
            res.body.status.should.equal(0);

            expect(err === null);
            encodedURLFn.restore();
            done();
          });
      });

      it('Adds media_projection_popup_coords in fire_cmd', (done) => {
        var requestBody = helper.AppAutomateCapabilities({ "app": "bs://testapp", "locale": "en_US" }),
          railsScope = helper.buildRails(),
          appAutomateRailsScope = helper.buildRails({ isAppAutomate: true }),
          macTerminalScope = helper.buildMac({
            action: function(options) {
              return [200, { done: true }];
            }
          });
      
        const popupCoords = "849 1582";
      
        requestBody.desiredCapabilities['media_projection_popup_coords'] = popupCoords;
      
        seleniumScope = helper.selenium(undefined, undefined, helper.APPIUM_PORT)
          .post('/wd/hub/session')
          .query(true)
          .reply(function(uri, body) {
            return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
          });
      
        const encodedURLFn = sinon.spy(requestlib, 'getEncodedURLParams');
      
        helper.request.post('/wd/hub/session')
          .auth('test', 'test')
          .send(requestBody)
          .end((err, res) => {
      
            railsScope.isDone().should.equal(false);
            appAutomateRailsScope.isDone().should.equal(true);
            macTerminalScope.isDone().should.equal(true);
            seleniumScope.isDone().should.equal(true);
            res.status.should.equal(200);
      
            expect(encodedURLFn.calledOnce);
            const fireCommandArguments = encodedURLFn.getCall(0).args[0];
            assert.strictEqual(fireCommandArguments.media_projection_popup_coords, popupCoords);
      
            res.body.sessionId.should.equal(helper.SESSION_ID);
            res.body.status.should.equal(0);
      
            expect(err === null);
            encodedURLFn.restore();
            done();
          });
      });

      [
        ["youiEngineAppPort", "18081", "youiEngineAppPort", "18081"],
        ["youiengine_driver_port", "12345", "youiengine_driver_port", "12345"],
      ].forEach(function (param) {
        it(`Add ${param[2] } in fire_cmd if ${param[0]} is passed`, (done) => {
          var requestBody = helper.AppAutomateCapabilities({
              "app": "bs://testapp",
              "locale": "en_US"
            }),
            railsScope = helper.buildRails(),
            appAutomateRailsScope = helper.buildRails({ isAppAutomate: true }),
            macTerminalScope = helper.buildMac({
              action: function (options) {
                return [200, { done: true }];
              }
            });

          requestBody.desiredCapabilities[param[0]] = param[1];

          seleniumScope = helper.selenium(undefined, undefined, helper.APPIUM_PORT)
            .post('/wd/hub/session')
            .query(true)
            .reply(function (uri, body) {
              body = JSON.parse(body).desiredCapabilities;
              return helper.sessionStartedJSON(body);
            });

          const encodedURLFn = sinon.spy(requestlib, 'getEncodedURLParams');

          helper.request.post('/wd/hub/session')
            .auth('test', 'test')
            .send(requestBody)
            .end((err, res) => {

              railsScope.isDone()
                .should
                .equal(false);
              appAutomateRailsScope.isDone()
                .should
                .equal(true);
              macTerminalScope.isDone()
                .should
                .equal(true);
              seleniumScope.isDone()
                .should
                .equal(true);
              res.status.should.equal(200);

              expect(encodedURLFn.calledOnce);
              const fireCommandArguments = encodedURLFn.getCall(0).args[0];
              assert(fireCommandArguments[param[2]], param[3]);

              res.body.sessionId.should.equal(helper.SESSION_ID);
              res.body.status.should.equal(0);

              expect(err === null);
              encodedURLFn.restore();
              done();
            });
        });
      });

      it('Add card_network flag in fire_cmd if cap is present', (done) => {
        var requestBody = helper.AppAutomateCapabilities({ "app": "bs://testapp", "device": "iPhone 6S Plus", "card_network": "visa" }),
          railsScope = helper.buildRails(),
          appAutomateRailsScope = helper.buildRails({isAppAutomate: true}),
          macTerminalScope = helper.buildMac({
            action: function(options) {
              return [200, { done: true }];
            }
          });

        seleniumScope = helper.selenium(undefined, undefined, helper.APPIUM_PORT)
        .post('/wd/hub/session')
        .query(true)
        .reply(function(uri, body) {
          return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
        });

        const encodedURLFn = sinon.spy(requestlib, 'getEncodedURLParams');

        helper.request.post('/wd/hub/session')
          .auth('test', 'test')
          .send(requestBody)
          .end((err, res) => {

            railsScope.isDone().should.equal(false);
            appAutomateRailsScope.isDone().should.equal(true);
            macTerminalScope.isDone().should.equal(true);
            seleniumScope.isDone().should.equal(true);
            res.status.should.equal(200);

            expect(encodedURLFn.calledOnce);
            const fireCommandArguments = encodedURLFn.getCall(0).args[0];
            expect(fireCommandArguments.card_network).to.equal('visa');

            res.body.sessionId.should.equal(helper.SESSION_ID);
            res.body.status.should.equal(0);

            expect(err === null);
            encodedURLFn.restore();
            done();
          });
      })

      it('Add apple_pay_cards flag in fire_cmd if cap is present', (done) => {
        var requestBody = helper.AppAutomateCapabilities({ "app": "bs://testapp", "device": "iPhone 6S Plus", "apple_pay_cards": "visa" }),
          railsScope = helper.buildRails(),
          appAutomateRailsScope = helper.buildRails({isAppAutomate: true}),
          macTerminalScope = helper.buildMac({
            action: function(options) {
              return [200, { done: true }];
            }
          });

        seleniumScope = helper.selenium(undefined, undefined, helper.APPIUM_PORT)
        .post('/wd/hub/session')
        .query(true)
        .reply(function(uri, body) {
          return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
        });

        const encodedURLFn = sinon.spy(requestlib, 'getEncodedURLParams');

        helper.request.post('/wd/hub/session')
          .auth('test', 'test')
          .send(requestBody)
          .end((err, res) => {

            railsScope.isDone().should.equal(false);
            appAutomateRailsScope.isDone().should.equal(true);
            macTerminalScope.isDone().should.equal(true);
            seleniumScope.isDone().should.equal(true);
            res.status.should.equal(200);

            expect(encodedURLFn.calledOnce);
            const fireCommandArguments = encodedURLFn.getCall(0).args[0];
            expect(fireCommandArguments.apple_pay_cards).to.equal('visa');

            res.body.sessionId.should.equal(helper.SESSION_ID);
            res.body.status.should.equal(0);

            expect(err === null);
            encodedURLFn.restore();
            done();
          });
      })

      it('Add keychainBioAuth flag in fire_cmd if cap is set to true and its a session on Apple OS', (done) => {
        var requestBody = helper.AppAutomateCapabilities({ "app": "bs://testapp", "device": "iPhone 6S Plus", "keychainBioAuth": "true" }),
          railsScope = helper.buildRails(),
          appAutomateRailsScope = helper.buildRails({isAppAutomate: true}),
          macTerminalScope = helper.buildMac({
            action: function(options) {
              return [200, { done: true }];
            }
          });

        seleniumScope = helper.selenium(undefined, undefined, helper.APPIUM_PORT)
        .post('/wd/hub/session')
        .query(true)
        .reply(function(uri, body) {
          return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
        });

        const encodedURLFn = sinon.spy(requestlib, 'getEncodedURLParams');

        helper.request.post('/wd/hub/session')
          .auth('test', 'test')
          .send(requestBody)
          .end((err, res) => {

            railsScope.isDone().should.equal(false);
            appAutomateRailsScope.isDone().should.equal(true);
            macTerminalScope.isDone().should.equal(true);
            seleniumScope.isDone().should.equal(true);
            res.status.should.equal(200);

            expect(encodedURLFn.calledOnce);
            const fireCommandArguments = encodedURLFn.getCall(0).args[0];
            expect(fireCommandArguments.keychainBioAuth).to.equal(true);

            res.body.sessionId.should.equal(helper.SESSION_ID);
            res.body.status.should.equal(0);

            expect(err === null);
            encodedURLFn.restore();
            done();
          });
      });

      it('Add biometricUserOptionAccessible flag in fire_cmd if cap is set to true and its a session on Apple OS', (done) => {
        var requestBody = helper.AppAutomateCapabilities({ "app": "bs://testapp", "device": "iPhone 6S Plus", "biometricUserOptionAccessible": "true" }),
          railsScope = helper.buildRails(),
          appAutomateRailsScope = helper.buildRails({isAppAutomate: true}),
          macTerminalScope = helper.buildMac({
            action: function(options) {
              return [200, { done: true }];
            }
          });

        seleniumScope = helper.selenium(undefined, undefined, helper.APPIUM_PORT)
          .post('/wd/hub/session')
          .query(true)
          .reply(function(uri, body) {
            return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
          });

        const encodedURLFn = sinon.spy(requestlib, 'getEncodedURLParams');

        helper.request.post('/wd/hub/session')
          .auth('test', 'test')
          .send(requestBody)
          .end((err, res) => {

            railsScope.isDone().should.equal(false);
            appAutomateRailsScope.isDone().should.equal(true);
            macTerminalScope.isDone().should.equal(true);
            seleniumScope.isDone().should.equal(true);
            res.status.should.equal(200);

            expect(encodedURLFn.calledOnce);
            const fireCommandArguments = encodedURLFn.getCall(0).args[0];
            expect(fireCommandArguments.biometricUserOptionAccessible).to.equal(true);

            res.body.sessionId.should.equal(helper.SESSION_ID);
            res.body.status.should.equal(0);

            expect(err === null);
            encodedURLFn.restore();
            done();
          });
      });

      it('Should not add keychainBioAuth flag in fire_cmd if cap is set to true and its not a session on Apple OS', (done) => {
        var requestBody = helper.AppAutomateCapabilities({ "app": "bs://testapp", "device": "Google Pixel 5", "keychainBioAuth": "true" }),
          railsScope = helper.buildRails(),
          appAutomateRailsScope = helper.buildRails({isAppAutomate: true}),
          macTerminalScope = helper.buildMac({
            action: function(options) {
              return [200, { done: true }];
            }
          });

        seleniumScope = helper.selenium(undefined, undefined, helper.APPIUM_PORT)
        .post('/wd/hub/session')
        .query(true)
        .reply(function(uri, body) {
          return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
        });

        const encodedURLFn = sinon.spy(requestlib, 'getEncodedURLParams');

        helper.request.post('/wd/hub/session')
          .auth('test', 'test')
          .send(requestBody)
          .end((err, res) => {

            railsScope.isDone().should.equal(false);
            appAutomateRailsScope.isDone().should.equal(true);
            macTerminalScope.isDone().should.equal(true);
            seleniumScope.isDone().should.equal(true);
            res.status.should.equal(200);

            expect(encodedURLFn.calledOnce);
            const fireCommandArguments = encodedURLFn.getCall(0).args[0];
            expect(fireCommandArguments.keychainBioAuth || false).to.eq(false);

            res.body.sessionId.should.equal(helper.SESSION_ID);
            res.body.status.should.equal(0);

            expect(err === null);
            encodedURLFn.restore();
            done();
          });
      });

      it('Add chooserIntentSupport flag in fire_cmd if cap is set to true and its a session on Android', (done) => {
        var requestBody = helper.AppAutomateCapabilities({"app": "bs://testapp", "device": "Google Pixel 5", "chooserIntentSupport": "true"}),
          railsScope = helper.buildRails(),
          appAutomateRailsScope = helper.buildRails({isAppAutomate: true}),
          macTerminalScope = helper.buildMac({
            action: function(options) {
              return [200, { done: true }];
            }
          });

        seleniumScope = helper.selenium(undefined, undefined, helper.APPIUM_PORT)
        .post('/wd/hub/session')
        .query(true)
        .reply(function(uri, body) {
          return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
        });

        const encodedURLFn = sinon.spy(requestlib, 'getEncodedURLParams');

        helper.request.post('/wd/hub/session')
          .auth('test', 'test')
          .send(requestBody)
          .end((err, res) => {

            railsScope.isDone().should.equal(false);
            appAutomateRailsScope.isDone().should.equal(true);
            macTerminalScope.isDone().should.equal(true);
            seleniumScope.isDone().should.equal(true);
            res.status.should.equal(200);

            expect(encodedURLFn.calledOnce);
            const fireCommandArguments = encodedURLFn.getCall(0).args[0];
            expect(fireCommandArguments.chooserIntentSupport).to.equal(true);

            res.body.sessionId.should.equal(helper.SESSION_ID);
            res.body.status.should.equal(0);

            expect(err === null);
            encodedURLFn.restore();
            done();
          });
      });

      it('Should not add chooserIntentSupport flag in fire_cmd if cap is set to true and its not a session on Android', (done) => {
        var requestBody = helper.AppAutomateCapabilities({"app": "bs://testapp", "device": "iPhone 6S Plus", "chooserIntentSupport": "true"}),
          railsScope = helper.buildRails(),
          appAutomateRailsScope = helper.buildRails({isAppAutomate: true}),
          macTerminalScope = helper.buildMac({
            action: function(options) {
              return [200, { done: true }];
            }
          });

        seleniumScope = helper.selenium(undefined, undefined, helper.APPIUM_PORT)
        .post('/wd/hub/session')
        .query(true)
        .reply(function(uri, body) {
          return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
        });

        const encodedURLFn = sinon.spy(requestlib, 'getEncodedURLParams');

        helper.request.post('/wd/hub/session')
          .auth('test', 'test')
          .send(requestBody)
          .end((err, res) => {

            railsScope.isDone().should.equal(false);
            appAutomateRailsScope.isDone().should.equal(true);
            macTerminalScope.isDone().should.equal(true);
            seleniumScope.isDone().should.equal(true);
            res.status.should.equal(200);

            expect(encodedURLFn.calledOnce);
            const fireCommandArguments = encodedURLFn.getCall(0).args[0];
            expect(fireCommandArguments.chooserIntentSupport || false).to.eq(false);

            res.body.sessionId.should.equal(helper.SESSION_ID);
            res.body.status.should.equal(0);

            expect(err === null);
            encodedURLFn.restore();
            done();
          });
      });

      it('Add enableBiometric flag in fire_cmd if cap is set to true', (done) => {
        var requestBody = helper.AppAutomateCapabilities({"app": "bs://testapp", "device": "Google Pixel 5", "browserstack.enableBiometric": "true"}),
          railsScope = helper.buildRails(),
          appAutomateRailsScope = helper.buildRails({isAppAutomate: true}),
          macTerminalScope = helper.buildMac({
            action: function(options) {
              return [200, { done: true }];
            }
          });

        seleniumScope = helper.selenium(undefined, undefined, helper.APPIUM_PORT)
        .post('/wd/hub/session')
        .query(true)
        .reply(function(uri, body) {
          return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
        });

        const encodedURLFn = sinon.spy(requestlib, 'getEncodedURLParams');

        helper.request.post('/wd/hub/session')
          .auth('test', 'test')
          .send(requestBody)
          .end((err, res) => {

            railsScope.isDone().should.equal(false);
            appAutomateRailsScope.isDone().should.equal(true);
            macTerminalScope.isDone().should.equal(true);
            seleniumScope.isDone().should.equal(true);
            res.status.should.equal(200);

            expect(encodedURLFn.calledOnce);
            const fireCommandArguments = encodedURLFn.getCall(0).args[0];
            expect(fireCommandArguments.enableBiometric).to.equal("true");

            res.body.sessionId.should.equal(helper.SESSION_ID);
            res.body.status.should.equal(0);

            expect(err === null);
            encodedURLFn.restore();
            done();
          });
      });

      it('Not add enableBiometric flag in fire_cmd if cap is set to false', (done) => {
        var requestBody = helper.AppAutomateCapabilities({"app": "bs://testapp", "device": "Google Pixel 5", "browserstack.enableBiometric": "false"}),
          railsScope = helper.buildRails(),
          appAutomateRailsScope = helper.buildRails({isAppAutomate: true}),
          macTerminalScope = helper.buildMac({
            action: function(options) {
              return [200, { done: true }];
            }
          });

        seleniumScope = helper.selenium(undefined, undefined, helper.APPIUM_PORT)
        .post('/wd/hub/session')
        .query(true)
        .reply(function(uri, body) {
          return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
        });

        const encodedURLFn = sinon.spy(requestlib, 'getEncodedURLParams');

        helper.request.post('/wd/hub/session')
          .auth('test', 'test')
          .send(requestBody)
          .end((err, res) => {

            railsScope.isDone().should.equal(false);
            appAutomateRailsScope.isDone().should.equal(true);
            macTerminalScope.isDone().should.equal(true);
            seleniumScope.isDone().should.equal(true);
            res.status.should.equal(200);

            expect(encodedURLFn.calledOnce);
            const fireCommandArguments = encodedURLFn.getCall(0).args[0];
            expect(fireCommandArguments.enableBiometric || false).to.equal(false);
            res.body.sessionId.should.equal(helper.SESSION_ID);
            res.body.status.should.equal(0);

            expect(err === null);
            encodedURLFn.restore();
            done();
          });
      });

      it('Add enableCameraImageInjection flag in fire_cmd if cap is set to true', (done) => {
        var requestBody = helper.AppAutomateCapabilities({"app": "bs://testapp", "device": "Google Pixel 5", "browserstack.enableCameraImageInjection": "true"}),
          railsScope = helper.buildRails(),
          appAutomateRailsScope = helper.buildRails({isAppAutomate: true}),
          macTerminalScope = helper.buildMac({
            action: function(options) {
              return [200, { done: true }];
            }
          });

        seleniumScope = helper.selenium(undefined, undefined, helper.APPIUM_PORT)
        .post('/wd/hub/session')
        .query(true)
        .reply(function(uri, body) {
          return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
        });

        const encodedURLFn = sinon.spy(requestlib, 'getEncodedURLParams');

        helper.request.post('/wd/hub/session')
          .auth('test', 'test')
          .send(requestBody)
          .end((err, res) => {

            railsScope.isDone().should.equal(false);
            appAutomateRailsScope.isDone().should.equal(true);
            macTerminalScope.isDone().should.equal(true);
            seleniumScope.isDone().should.equal(true);
            res.status.should.equal(200);

            expect(encodedURLFn.calledOnce);
            const fireCommandArguments = encodedURLFn.getCall(0).args[0];
            expect(fireCommandArguments.enableCameraImageInjection).to.equal("true");

            res.body.sessionId.should.equal(helper.SESSION_ID);
            res.body.status.should.equal(0);

            expect(err === null);
            encodedURLFn.restore();
            done();
          });
      });

      it('Not add enableCameraImageInjection flag in fire_cmd if cap is set to false', (done) => {
        var requestBody = helper.AppAutomateCapabilities({"app": "bs://testapp", "device": "Google Pixel 5", "browserstack.enableCameraImageInjection": "false"}),
          railsScope = helper.buildRails(),
          appAutomateRailsScope = helper.buildRails({isAppAutomate: true}),
          macTerminalScope = helper.buildMac({
            action: function(options) {
              return [200, { done: true }];
            }
          });

        seleniumScope = helper.selenium(undefined, undefined, helper.APPIUM_PORT)
        .post('/wd/hub/session')
        .query(true)
        .reply(function(uri, body) {
          return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
        });

        const encodedURLFn = sinon.spy(requestlib, 'getEncodedURLParams');

        helper.request.post('/wd/hub/session')
          .auth('test', 'test')
          .send(requestBody)
          .end((err, res) => {

            railsScope.isDone().should.equal(false);
            appAutomateRailsScope.isDone().should.equal(true);
            macTerminalScope.isDone().should.equal(true);
            seleniumScope.isDone().should.equal(true);
            res.status.should.equal(200);

            expect(encodedURLFn.calledOnce);
            const fireCommandArguments = encodedURLFn.getCall(0).args[0];
            expect(fireCommandArguments.enableCameraImageInjection || false).to.equal(false);

            res.body.sessionId.should.equal(helper.SESSION_ID);
            res.body.status.should.equal(0);

            expect(err === null);
            encodedURLFn.restore();
            done();
          });
      });

      it('Add enableCameraVideoInjection flag in fire_cmd if cap is set to true', (done) => {
        var requestBody = helper.AppAutomateCapabilities({"app": "bs://testapp", "device": "Google Pixel 5", "browserstack.enableCameraVideoInjection": "true"}),
          railsScope = helper.buildRails(),
          appAutomateRailsScope = helper.buildRails({isAppAutomate: true}),
          macTerminalScope = helper.buildMac({
            action: function(options) {
              return [200, { done: true }];
            }
          });

        seleniumScope = helper.selenium(undefined, undefined, helper.APPIUM_PORT)
        .post('/wd/hub/session')
        .query(true)
        .reply(function(uri, body) {
          return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
        });

        const encodedURLFn = sinon.spy(requestlib, 'getEncodedURLParams');

        helper.request.post('/wd/hub/session')
          .auth('test', 'test')
          .send(requestBody)
          .end((err, res) => {

            railsScope.isDone().should.equal(false);
            appAutomateRailsScope.isDone().should.equal(true);
            macTerminalScope.isDone().should.equal(true);
            seleniumScope.isDone().should.equal(true);
            res.status.should.equal(200);

            expect(encodedURLFn.calledOnce);
            const fireCommandArguments = encodedURLFn.getCall(0).args[0];
            expect(fireCommandArguments.enableCameraVideoInjection).to.equal("true");

            res.body.sessionId.should.equal(helper.SESSION_ID);
            res.body.status.should.equal(0);

            expect(err === null);
            encodedURLFn.restore();
            done();
          });
      });

      it('Not add enableCameraVideoInjection flag in fire_cmd if cap is set to false', (done) => {
        var requestBody = helper.AppAutomateCapabilities({"app": "bs://testapp", "device": "Google Pixel 5", "browserstack.enableCameraVideoInjection": "false"}),
          railsScope = helper.buildRails(),
          appAutomateRailsScope = helper.buildRails({isAppAutomate: true}),
          macTerminalScope = helper.buildMac({
            action: function(options) {
              return [200, { done: true }];
            }
          });

        seleniumScope = helper.selenium(undefined, undefined, helper.APPIUM_PORT)
        .post('/wd/hub/session')
        .query(true)
        .reply(function(uri, body) {
          return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
        });

        const encodedURLFn = sinon.spy(requestlib, 'getEncodedURLParams');

        helper.request.post('/wd/hub/session')
          .auth('test', 'test')
          .send(requestBody)
          .end((err, res) => {

            railsScope.isDone().should.equal(false);
            appAutomateRailsScope.isDone().should.equal(true);
            macTerminalScope.isDone().should.equal(true);
            seleniumScope.isDone().should.equal(true);
            res.status.should.equal(200);

            expect(encodedURLFn.calledOnce);
            const fireCommandArguments = encodedURLFn.getCall(0).args[0];
            expect(fireCommandArguments.enableCameraVideoInjection || false).to.equal(false);

            res.body.sessionId.should.equal(helper.SESSION_ID);
            res.body.status.should.equal(0);

            expect(err === null);
            encodedURLFn.restore();
            done();
          });
      });

      it('Set appProfiling flag true in fire_cmd if cap is set to true', (done) => {
        var requestBody = helper.AppAutomateCapabilities({"app": "bs://testapp", "browserstack.appProfiling": "true"}),
          railsScope = helper.buildRails(),
          appAutomateRailsScope = helper.buildRails({isAppAutomate: true}),
          macTerminalScope = helper.buildMac({
            action: function(options) {
              return [200, { done: true }];
            }
          });

        seleniumScope = helper.selenium(undefined, undefined, helper.APPIUM_PORT)
        .post('/wd/hub/session')
        .query(true)
        .reply(function(uri, body) {
          return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
        });

        const encodedURLFn = sinon.spy(requestlib, 'getEncodedURLParams');

        helper.request.post('/wd/hub/session')
          .auth('test', 'test')
          .send(requestBody)
          .end((err, res) => {

            railsScope.isDone().should.equal(false);
            appAutomateRailsScope.isDone().should.equal(true);
            macTerminalScope.isDone().should.equal(true);
            seleniumScope.isDone().should.equal(true);
            res.status.should.equal(200);

            expect(encodedURLFn.calledOnce);
            const fireCommandArguments = encodedURLFn.getCall(0).args[0];
            assert(fireCommandArguments.appProfiling, true);

            res.body.sessionId.should.equal(helper.SESSION_ID);
            res.body.status.should.equal(0);

            expect(err === null);
            encodedURLFn.restore();
            done();
          });
      });

      it('Set fire_cmd method to post if cap is passed', (done) => {
        var requestBody = helper.AppAutomateCapabilities({"app": "bs://testapp", "browserstack.enable_firecmd_post": "true"}),
          railsScope = helper.buildRails(),
          appAutomateRailsScope = helper.buildRails({isAppAutomate: true}),
          macTerminalScope = helper.buildMac({
            post: true,
            action: function(options) {
              return [200, { done: true }];
            }
          });

        seleniumScope = helper.selenium(undefined, undefined, helper.APPIUM_PORT)
        .post('/wd/hub/session')
        .query(true)
        .reply(function(uri, body) {
          return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
        });

        const callFn = sinon.spy(requestlib, 'call');

        helper.request.post('/wd/hub/session')
          .auth('test', 'test')
          .send(requestBody)
          .end((err, res) => {

            railsScope.isDone().should.equal(false);
            appAutomateRailsScope.isDone().should.equal(true);
            macTerminalScope.isDone().should.equal(true);
            seleniumScope.isDone().should.equal(true);
            res.status.should.equal(200);

            expect(callFn.calledOnce);
            const fireCommandArguments = callFn.getCall(0).args[0];
            assert(fireCommandArguments.method, "POST");

            res.body.sessionId.should.equal(helper.SESSION_ID);
            res.body.status.should.equal(0);

            expect(err === null);
            callFn.restore();
            done();
          });
      });

      it('Set appProfiling flag false in fire_cmd if cap is not set', (done) => {
        var requestBody = helper.AppAutomateCapabilities({"app": "bs://testapp"}),
          railsScope = helper.buildRails(),
          appAutomateRailsScope = helper.buildRails({isAppAutomate: true}),
          macTerminalScope = helper.buildMac({
            action: function(options) {
              return [200, { done: true }];
            }
          });

        seleniumScope = helper.selenium(undefined, undefined, helper.APPIUM_PORT)
        .post('/wd/hub/session')
        .query(true)
        .reply(function(uri, body) {
          return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
        });

        const encodedURLFn = sinon.spy(requestlib, 'getEncodedURLParams');

        helper.request.post('/wd/hub/session')
          .auth('test', 'test')
          .send(requestBody)
          .end((err, res) => {

            railsScope.isDone().should.equal(false);
            appAutomateRailsScope.isDone().should.equal(true);
            macTerminalScope.isDone().should.equal(true);
            seleniumScope.isDone().should.equal(true);
            res.status.should.equal(200);

            expect(encodedURLFn.calledOnce);
            const fireCommandArguments = encodedURLFn.getCall(0).args[0];
            expect(fireCommandArguments.appProfiling|| false).to.eq(false);

            res.body.sessionId.should.equal(helper.SESSION_ID);
            res.body.status.should.equal(0);

            expect(err === null);
            encodedURLFn.restore();
            done();
          });
      });

      it('Enable transparent mode if cap is passed', (done) => {
        const requestBody = helper.AppAutomateCapabilities({"app": "bs://testapp", "browserstack.enableTransparentMode": true}),
          railsScope = helper.buildRails(),
          appAutomateRailsScope = helper.buildRails({isAppAutomate: true}),
          macTerminalScope = helper.buildMac({
            action: function(options) {
              return [200, { done: true }];
            }
          });

        seleniumScope = helper.selenium(undefined, undefined, helper.APPIUM_PORT)
          .post('/wd/hub/session')
          .query(true)
          .reply(function(uri, body) {
            return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
          });

        const encodedURLFn = sinon.spy(requestlib, 'getEncodedURLParams');

        helper.request.post('/wd/hub/session')
          .auth('test', 'test')
          .send(requestBody)
          .end((err, res) => {

            railsScope.isDone().should.equal(false);
            appAutomateRailsScope.isDone().should.equal(true);
            macTerminalScope.isDone().should.equal(true);
            seleniumScope.isDone().should.equal(true);
            res.status.should.equal(200);

            expect(encodedURLFn.calledOnce);
            const fireCommandArguments = encodedURLFn.getCall(0).args[0];
            expect(fireCommandArguments.enableTransparentMode).to.eq(true);

            res.body.sessionId.should.equal(helper.SESSION_ID);
            res.body.status.should.equal(0);

            expect(err === null);
            encodedURLFn.restore();
            done();
          });
      });

      it('Don\'t set transparent mode if cap is not passed', (done) => {
        const requestBody = helper.AppAutomateCapabilities({"app": "bs://testapp"}),
          railsScope = helper.buildRails(),
          appAutomateRailsScope = helper.buildRails({isAppAutomate: true}),
          macTerminalScope = helper.buildMac({
            action: function(options) {
              return [200, { done: true }];
            }
          });

        seleniumScope = helper.selenium(undefined, undefined, helper.APPIUM_PORT)
          .post('/wd/hub/session')
          .query(true)
          .reply(function(uri, body) {
            return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
          });

        const encodedURLFn = sinon.spy(requestlib, 'getEncodedURLParams');

        helper.request.post('/wd/hub/session')
          .auth('test', 'test')
          .send(requestBody)
          .end((err, res) => {

            railsScope.isDone().should.equal(false);
            appAutomateRailsScope.isDone().should.equal(true);
            macTerminalScope.isDone().should.equal(true);
            seleniumScope.isDone().should.equal(true);
            res.status.should.equal(200);

            expect(encodedURLFn.calledOnce);
            const fireCommandArguments = encodedURLFn.getCall(0).args[0];
            expect(fireCommandArguments.enableTransparentMode).to.eq(undefined);

            res.body.sessionId.should.equal(helper.SESSION_ID);
            res.body.status.should.equal(0);

            expect(err === null);
            encodedURLFn.restore();
            done();
          });
      });

      it('Enable network logs patch if cap is passed', (done) => {
        const requestBody = helper.AppAutomateCapabilities({"app": "bs://testapp", "browserstack.networkLogsPatch": true}),
          railsScope = helper.buildRails(),
          appAutomateRailsScope = helper.buildRails({isAppAutomate: true}),
          macTerminalScope = helper.buildMac({
            action: function(options) {
              return [200, { done: true }];
            }
          });

        seleniumScope = helper.selenium(undefined, undefined, helper.APPIUM_PORT)
          .post('/wd/hub/session')
          .query(true)
          .reply(function(uri, body) {
            return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
          });

        const encodedURLFn = sinon.spy(requestlib, 'getEncodedURLParams');

        helper.request.post('/wd/hub/session')
          .auth('test', 'test')
          .send(requestBody)
          .end((err, res) => {

            railsScope.isDone().should.equal(false);
            appAutomateRailsScope.isDone().should.equal(true);
            macTerminalScope.isDone().should.equal(true);
            seleniumScope.isDone().should.equal(true);
            res.status.should.equal(200);

            expect(encodedURLFn.calledOnce);
            const fireCommandArguments = encodedURLFn.getCall(0).args[0];
            expect(fireCommandArguments.networkLogsPatch).to.eq(true);

            res.body.sessionId.should.equal(helper.SESSION_ID);
            res.body.status.should.equal(0);

            expect(err === null);
            encodedURLFn.restore();
            done();
          });
      });

      it('Don\'t set networkLogsPatch if cap is not passed', (done) => {
        const requestBody = helper.AppAutomateCapabilities({"app": "bs://testapp"}),
          railsScope = helper.buildRails(),
          appAutomateRailsScope = helper.buildRails({isAppAutomate: true}),
          macTerminalScope = helper.buildMac({
            action: function(options) {
              return [200, { done: true }];
            }
          });

        seleniumScope = helper.selenium(undefined, undefined, helper.APPIUM_PORT)
          .post('/wd/hub/session')
          .query(true)
          .reply(function(uri, body) {
            return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
          });

        const encodedURLFn = sinon.spy(requestlib, 'getEncodedURLParams');

        helper.request.post('/wd/hub/session')
          .auth('test', 'test')
          .send(requestBody)
          .end((err, res) => {

            railsScope.isDone().should.equal(false);
            appAutomateRailsScope.isDone().should.equal(true);
            macTerminalScope.isDone().should.equal(true);
            seleniumScope.isDone().should.equal(true);
            res.status.should.equal(200);

            expect(encodedURLFn.calledOnce);
            const fireCommandArguments = encodedURLFn.getCall(0).args[0];
            expect(fireCommandArguments.networkLogsPatch).to.eq(undefined);

            res.body.sessionId.should.equal(helper.SESSION_ID);
            res.body.status.should.equal(0);

            expect(err === null);
            encodedURLFn.restore();
            done();
          });
      });

    });

    describe('FireCmd not called as parallel tests and queue limit exceed', function () {

      it('should return queue size exceeded error', function (done) {
        const user = 'test';
        const p1 = helper.hubHelper.redisClient.setex([`testQueueapp_automate_utilized_${user}`, constants.sessionQueueResetTime, 6]);
        const p2 = helper.hubHelper.redisClient.setex([`testQueueapp_automate_limit_${user}`, constants.sessionQueueResetTime, 6]);
        const p3 = helper.hubHelper.redisClient.setex([`testParallelapp_automate_utilized_${user}`, constants.sessionQueueResetTime, 10]);
        const p4 = helper.hubHelper.redisClient.setex([`testParallelapp_automate_limit_${user}`, constants.sessionQueueResetTime, 10]);
        Promise.all([p1, p2, p3, p4]).then(() => {
          var requestBody = helper.AppAutomateCapabilities({ "app": "bs://testapp" });
          helper.request.post('/wd/hub/session')
            .auth('test', 'test')
            .send(requestBody)
            .end(function (err, res) {
              res.status.should.equal(200);
              res.body.value.message.should.equal(helper.constants.QUEUE_SIZE_EXCEEDED.errorMessage);
              res.body.sessionId.should.equal('');
              res.body.status.should.equal(13);
              done();
          });
        });
      });
    });

    describe('FireCmd Params for Android Appium Sessions', function() {

    });

    describe('FireCmd Params for iOS Appium Sessions', function() {

    });
  });
});
