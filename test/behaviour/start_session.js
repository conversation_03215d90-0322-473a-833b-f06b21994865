const { assert } = require('chai');
var helper = require('./helper');

describe('Start Session', function() {
  let originalRegion;
  let originalEliminateRproxy;

  beforeEach(function() {
    originalRegion = helper.constants.region;
    originalEliminateRproxy = helper.constants.eliminateRproxyPrivateHubSubRegions;
  });

  afterEach(function() {
    helper.constants.region = originalRegion;
    helper.constants.eliminateRproxyPrivateHubSubRegions = originalEliminateRproxy;
  });

  describe('Proxy Object in start caps', function() {
    var shouldSetProxyObject = [{
        browser: 'Firefox',
        os: 'Windows',
        os_version: '8.1',
        'browserstack.local': true
      }, {
        browser: 'Chrome',
        'browserstack.local': true
      }, {
        browser: 'Chrome',
        os: 'Windows',
        os_version: '8.1',
        'browserstack.local': true
      }, {
        browser: 'Chrome',
        os: 'Windows',
        os_version: '8.1',
        'browserstack.networkLogs': true
      }];
    var shouldNotSetProxyObject = [{
        browser: 'Firefox',
        os: 'Windows',
        os_version: '8.1',
        'browserstack.networkLogs': true
      }, {
        browser: 'ie',
        os: 'Windows',
        os_version: '10',
        'browserstack.local': true
      }];
    shouldSetProxyObject.forEach(function(userCaps, userCapsIndex) {
      it('Checking cases where proxy object should be present. Test: ' + userCapsIndex, function(done) {
        var requestBody = helper.getCapabilities(userCaps);
        var outputCaps;
        var seleniumScope = helper.selenium()
          .post('/wd/hub/session')
          .query(true)
          .reply(function(uri, body) {
            body = JSON.parse(body).desiredCapabilities;
            body.should.have.any.keys('proxy');
            body.proxy.should.have.any.keys('proxyType', 'proxyAutoconfigUrl');
            body.proxy.proxyType.should.equal('PAC');
            ['http://localhost:4567/pacfile', 'http://localhost:45671/pacfile', 'file://c:/Users/<USER>/proxy.pac'].should.contain(body.proxy.proxyAutoconfigUrl)
            return [200, {
              sessionId: '1234-abcd-7890-wxyz',
              status: 0,
              value: body
            }];
          });

        var queryObject = {
          'auth': 'selautomate'
        };
        if(userCaps['browserstack.local']) {
          queryObject.local = true;
        }
        helper.doSelenium.startSession(requestBody, seleniumScope, {
          'query': queryObject,
          'skipSeleniumNock': true
        }).then(function(res) {
          seleniumScope.isDone().should.be.true;
          res.status.should.equal(200);
          res.body.sessionId.should.equal(helper.SESSION_ID);
          res.body.status.should.equal(0);
          done();
        }).catch(function(err) {
          throw err;
        });
      });
    });
    shouldNotSetProxyObject.forEach(function(userCaps, userCapsIndex) {
      it('Checking cases where proxy object should not be present. Test: ' + userCapsIndex, function(done) {
        var requestBody = helper.getCapabilities(userCaps);
        var outputCaps;
        var seleniumScope = helper.selenium()
          .post('/wd/hub/session')
          .query(true)
          .reply(function(uri, body) {
            body = JSON.parse(body).desiredCapabilities;
            (body.proxy == undefined).should.be.true;
            return [200, {
              sessionId: '1234-abcd-7890-wxyz',
              status: 0,
              value: body
            }];
          });

        var queryObject = {
          'auth': 'selautomate'
        };
        if(userCaps['browserstack.local']) {
          queryObject.local = true;
        }
        helper.doSelenium.startSession(requestBody, seleniumScope, {
          'query': queryObject,
          'skipSeleniumNock': true
        }).then(function(res) {
          seleniumScope.isDone().should.be.true;
          res.status.should.equal(200);
          res.body.sessionId.should.equal(helper.SESSION_ID);
          res.body.status.should.equal(0);
          done();
        }).catch(function(err) {
          throw err;
        });
      });
    });
  });
  describe('Behavior', function() {
    it('browserstack.browserstackSDK', function(done) {
      this.timeout(5000);
      const requestBody = {
        desiredCapabilities: {
          browser: 'IPHONE',
          device : "iPhone 8",
          realMobile : true,
          nativeWebTap: true,
          'browserstack.use_w3c': true,
          'browserstack.video': true,
          'browserstack.browserstackSDK' : 'testng-javaagent/2.0.0-SNAPSHOT'
        }
      };
      const railsScope = helper.buildRails();
      const macTerminalScope = helper.buildMac({
        action: function(options) {
          return [200, { done: true }];
        }
      });

      const appiumScope = helper.appiumMini()
      .post('/wd/hub/session')
      .query(true)
      .reply(function(uri, body) {
        return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
      });

      helper.request.post('/wd/hub/session')
      .auth('test', 'test')
      .send(requestBody)
      .then(function(res) {
        railsScope.isDone().should.be.true;
        macTerminalScope.isDone().should.be.true;
        appiumScope.isDone().should.be.true;
        res.status.should.equal(200);
        res.body.value.should.have.any.keys('browser', 'browser_version', 'os', 'os_version');
        res.body.sessionId.should.equal(helper.SESSION_ID);
        res.body.status.should.equal(0);
        done();
      }).catch(function(err) {
        throw err;
      });
    });

    it('rproxy elimination coverage', function(done) {
      this.timeout(10000);
      helper.constants.region = "us-east-3b";
      helper.constants.eliminateRproxyPrivateHubSubRegions = true;
      const requestBody = {
        desiredCapabilities: {
          browser: 'IPHONE',
          device : "iPhone 8",
          realMobile : true,
          nativeWebTap: true,
          'browserstack.use_w3c': true,
          'browserstack.video': true,
        }
      };
      const railsScope = helper.buildRails();
      const macTerminalScope = helper.buildMacRproxy({
        action: function(options) {
          return [200, { done: true }];
        }
      });

      const appiumScope = helper.appiumMiniRproxy()
      .post('/wd/hub/session')
      .query(true)
      .reply(function(uri, body) {
        return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
      });

      helper.request.post('/wd/hub/session')
      .auth('test', 'test')
      .send(requestBody)
      .then(function(res) {
        railsScope.isDone().should.be.true;
        macTerminalScope.isDone().should.be.true;
        appiumScope.isDone().should.be.true;
        res.status.should.equal(200);
        res.body.value.should.have.any.keys('browser', 'browser_version', 'os', 'os_version');
        res.body.sessionId.should.equal(helper.SESSION_ID);
        res.body.status.should.equal(0);
        return Promise.resolve();
      })
      .then(function() {
        helper.request.post('/wd/hub/session/' + helper.SESSION_ID + '/url')
        .send({ url: 'http://www.google.com' })
        .end(function(err, res) {
          done();
        });
      })
      .catch(function(err) {
        throw err;
      });
    });

    it('proxy nativeWebTap in start_session command', function(done) {
      this.timeout(5000);
      const requestBody = {
        desiredCapabilities: {
          browser: 'IPHONE',
          device : "iPhone 8",
          realMobile : true,
          nativeWebTap: true,
          'browserstack.use_w3c': true,
          'browserstack.video': true
        }
      };
      const railsScope = helper.buildRails();
      const macTerminalScope = helper.buildMac({
        action: function(options) {
          return [200, { done: true }];
        }
      });

      const appiumScope = helper.appiumMini()
      .post('/wd/hub/session')
      .query(true)
      .reply(function(uri, body) {
        return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
      });

      helper.request.post('/wd/hub/session')
      .auth('test', 'test')
      .send(requestBody)
      .then(function(res) {
        railsScope.isDone().should.be.true;
        macTerminalScope.isDone().should.be.true;
        appiumScope.isDone().should.be.true;
        res.status.should.equal(200);
        res.body.value.should.have.any.keys('browser', 'browser_version', 'os', 'os_version');
        res.body.sessionId.should.equal(helper.SESSION_ID);
        res.body.status.should.equal(0);
        done();
      }).catch(function(err) {
        throw err;
      });
    });

    it('start session is called if there is no fire commands', function(done) {
      this.timeout(5000);
      var requestBody = helper.getCapabilities({
            browser: 'Firefox',
            browser_version: 41,
            'browserstack.video': false
          }),
          railsScope = helper.buildRails(),
          macTerminalScope = helper.buildMac();

      var seleniumScope = helper.selenium()
      .post('/wd/hub/session')
      .query(true)
      .reply(function(uri, body) {
        return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
      });

      helper.request.post('/wd/hub/session')
      .auth('test', 'test')
      .send(requestBody)
      .then(function(res) {
        railsScope.isDone().should.be.true;
        macTerminalScope.isDone().should.be.false;
        seleniumScope.isDone().should.be.true;
        res.status.should.equal(200);
        res.body.value.should.have.any.keys('browser', 'browser_version', 'os', 'os_version');
        res.body.sessionId.should.equal(helper.SESSION_ID);
        res.body.status.should.equal(0);
        done();
      }).catch(function(err) {
        throw err;
      });
    });
    it('start session sends capabilities and success starts the session', function(done) {
      this.timeout(5000);
      var requestBody = helper.getCapabilities({
            browser: 'Firefox',
            browser_version: 41,
            'browserstack.video': false
          }),
          railsScope = helper.buildRails();

      var outputCaps;
      var seleniumScope = helper.selenium()
      .post('/wd/hub/session')
      .query(true)
      .reply(function(uri, body) {
        body = JSON.parse(body).desiredCapabilities;
        body.browser.should.equal('Firefox');
        body.os.should.equal('OS X');
        body.os_version.should.equal('Yosemite');
        body.browser_version.should.equal(41);
        outputCaps = body;
        return [200, {
          sessionId: '1234-abcd-7890-wxyz',
          status: 0,
          value: { desiredCapabilities: body }
        }];
      });

      helper.request.post('/wd/hub/session')
      .auth('test', 'test')
      .send(requestBody)
      .then(function(res) {
        railsScope.isDone().should.be.true;
        seleniumScope.isDone().should.be.true;
        res.status.should.equal(200);
        res.body.value.desiredCapabilities.should.deep.equal(outputCaps);
        res.body.sessionId.should.equal(helper.SESSION_ID);
        res.body.status.should.equal(0);
        done();
      }).catch(function(err) {
        throw err;
      });
    });
    it('sends a retry to jar if start session returns an error', function(done) {
      this.timeout(5000);
      var requestBody = helper.getCapabilities({
            browser: 'Firefox',
            'browserstack.video': false
          }),
          railsScope = helper.buildRails();

      var count = 0;
      var seleniumScope = helper.selenium()
      .persist()
      .post('/wd/hub/session')
      .query(true)
      .reply(function(uri, body) {
        count++;
        return [200, {
          sessionId: '',
          status: 13,
          value: { message: 'I am error' }
        }];
      });

      helper.request.post('/wd/hub/session')
      .auth('test', 'test')
      .send(requestBody)
      .then(function(res) {
        railsScope.isDone().should.be.true;
        seleniumScope.isDone().should.be.true;
        count.should.equal(2);
        res.status.should.equal(200);
        res.body.status.should.equal(13);
        res.body.sessionId.should.equal('');
        // This is because railsApp is not for subsequent retries
        res.body.value.message.should.equal('Not able to reach Browserstack.com! Please try again!');
        done();
      }).catch(function(err) {
        throw err;
      });
    });
    it('sends a retry to rails if start session has an error', function(done) {
      this.timeout(12000);
      var requestBody = helper.getCapabilities({
            browser: 'Firefox',
            'browserstack.video': false
          }),
          railsScope1 = helper.buildRails();

      var seleniumScope1 = helper.selenium()
      .post('/wd/hub/session')
      .query(true)
      .replyWithError('I am an error');

      var railsScope2 = helper.buildRails({query: {auth: 'selautomate', hrelease: true}});

      helper.request.post('/wd/hub/session')
      .auth('test', 'test')
      .send(requestBody)
      .then(function(res) {
        railsScope1.isDone().should.be.true;
        seleniumScope1.isDone().should.be.true;
        railsScope2.isDone().should.be.true;
        res.status.should.equal(200);
        res.body.status.should.equal(13);
        res.body.sessionId.should.equal('');
        res.body.value.message.should.equal('Could not start Browser / Emulator');
        done();
      }).catch(function(err) {
        throw err;
      });
    });
    it('sends a retry to jar if start session has a timeout', function(done) {
      this.timeout(5000);
      var requestBody = helper.getCapabilities({
            browser: 'Firefox',
            'browserstack.video': false
          }),
          railsScope1 = helper.buildRails();

      var seleniumScope1 = helper.selenium()
      .post('/wd/hub/session')
      .query(true)
      .delay(120000)
      .reply([200, {
        sessionId: '',
        status: 13,
        value: { message: 'I am error' }
      }]);
      var seleniumScope2 = helper.selenium()
      .post('/wd/hub/session')
      .query(true)
      .reply([200, {
        sessionId: '',
        status: 13,
        value: { message: 'I am error' }
      }]);

      helper.request.post('/wd/hub/session')
      .auth('test', 'test')
      .send(requestBody)
      .then(function(res) {
        railsScope1.isDone().should.be.true;
        seleniumScope1.isDone().should.be.true;
        seleniumScope2.isDone().should.be.true;
        res.status.should.equal(200);
        res.body.status.should.equal(13);
        res.body.sessionId.should.equal('');
        // This is because railsApp is not for subsequent retries
        res.body.value.message.should.equal('Not able to reach Browserstack.com! Please try again!');
        done();
      }).catch(function(err) {
        throw err;
      });
    });
    it('sends a request to rails if retry returns error', function(done) {
      this.timeout(5000);
      var requestBody = helper.getCapabilities({
        browser: 'Firefox',
        'browserstack.video': false
      }),
      railsScope1 = helper.buildRails();

      var seleniumScope1 = helper.selenium()
      .post('/wd/hub/session')
      .query(true)
      .reply([200, {
        sessionId: '',
        status: 13,
        value: { message: 'I am error' }
      }]);

      var seleniumScope2 = helper.selenium()
      .post('/wd/hub/session')
      .query(true)
      .reply([200, {
        sessionId: '',
        status: 13,
        value: { message: 'I am error' }
      }]);

      railsScope2 = helper.buildRails({query: {auth: 'selautomate', hrelease: true}});
      helper.request.post('/wd/hub/session')
      .auth('test', 'test')
      .send(requestBody)
      .then(function(res) {
        railsScope1.isDone().should.be.true;
        seleniumScope1.isDone().should.be.true;
        seleniumScope2.isDone().should.be.true;
        railsScope2.isDone().should.be.true;
        res.status.should.equal(200);
        res.body.status.should.equal(13);
        res.body.sessionId.should.equal('');
        // This is because railsApp is not for subsequent retries
        res.body.value.message.should.equal('Not able to reach Browserstack.com! Please try again!');
        done();
      }).catch(function(err) {
        throw err;
      });
    });
    it('sends a request to rails if retry has a timeout', function(done) {
      this.timeout(5000);
      var requestBody = helper.getCapabilities({
        browser: 'Firefox',
        'browserstack.video': false
      }),
      railsScope1 = helper.buildRails();

      var seleniumScope1 = helper.selenium()
      .post('/wd/hub/session')
      .query(true)
      .reply([200, {
        sessionId: '',
        status: 13,
        value: { message: 'I am error' }
      }]);

      var seleniumScope2 = helper.selenium()
      .post('/wd/hub/session')
      .query(true)
      .delay(120000)
      .reply([200, {
        sessionId: '',
        status: 13,
        value: { message: 'I am error' }
      }]);

      railsScope2 = helper.buildRails({query: {auth: 'selautomate', hrelease: true}});
      helper.request.post('/wd/hub/session')
      .auth('test', 'test')
      .send(requestBody)
      .then(function(res) {
        railsScope1.isDone().should.be.true;
        seleniumScope1.isDone().should.be.true;
        seleniumScope2.isDone().should.be.true;
        railsScope2.isDone().should.be.true;
        res.status.should.equal(200);
        res.body.status.should.equal(13);
        res.body.sessionId.should.equal('');
        // This is because railsApp is not for subsequent retries
        res.body.value.message.should.equal('Not able to reach Browserstack.com! Please try again!');
        done();
      }).catch(function(err) {
        throw err;
      });
    });
    it('sends a request to rails if retry has an error', function(done) {
      this.timeout(12000);
      var requestBody = helper.getCapabilities({
        browser: 'Firefox',
        'browserstack.video': false
      }),
      releaseReceived = false;
      railsScope1 = helper.buildRails();

      var seleniumScope1 = helper.selenium()
      .post('/wd/hub/session')
      .query(true)
      .reply(200, {
        sessionId: '',
        status: 13,
        value: { message: 'I am error' }
      });

      var seleniumScope2 = helper.selenium()
      .post('/wd/hub/session')
      .query(true)
      .replyWithError('I am an error');

      var railsScope2 = helper.buildRails({
        query: true,
        persist: true,
        action: function(uri, body) {
          var options = helper.getParamsFromQuery(uri);
          if ('release' in options)
            releaseReceived = true;
          return [200, {done: true}];
        }
      });

      helper.request.post('/wd/hub/session')
      .auth('test', 'test')
      .send(requestBody)
      .then(function(res) {
        helper.sleep(1000).then(() => {
          railsScope1.isDone().should.be.true;
          seleniumScope1.isDone().should.be.true;
          seleniumScope2.isDone().should.be.true;
          railsScope2.isDone().should.be.true;
          releaseReceived.should.be.true;
          res.status.should.equal(200);
          res.body.status.should.equal(13);
          res.body.sessionId.should.equal('');
          res.body.value.message.should.equal('Could not start Browser / Emulator');
          done();
        }).catch(function(err) {
          throw err;
        });
      }).catch(function(err) {
        throw err;
      });
    });
    it('error in start session sends a release request to rails', function(done) {
      this.timeout(12000);
      var requestBody = helper.getCapabilities({
        browser: 'Firefox',
        'browserstack.video': false
      }),
      releaseReceived = false;
      railsScope1 = helper.buildRails();

      var seleniumScope1 = helper.selenium()
      .post('/wd/hub/session')
      .query(true)
      .reply(200, {
        sessionId: '',
        status: 13,
        value: { message: 'I am error' }
      });

      var seleniumScope2 = helper.selenium()
      .post('/wd/hub/session')
      .query(true)
      .replyWithError('I am an error');

      var railsScope2 = helper.buildRails({
        query: true,
        persist: true,
        action: function(uri, body) {
          var options = helper.getParamsFromQuery(uri);
          if ('release' in options)
            releaseReceived = true;
          return [200, {done: true}];
        }
      });

      helper.request.post('/wd/hub/session')
      .auth('test', 'test')
      .send(requestBody)
      .then(function(res) {
        helper.sleep(1000).then(() => {
          railsScope1.isDone().should.be.true;
          seleniumScope1.isDone().should.be.true;
          seleniumScope2.isDone().should.be.true;
          railsScope2.isDone().should.be.true;
          releaseReceived.should.be.true;
          res.status.should.equal(200);
          res.body.status.should.equal(13);
          res.body.sessionId.should.equal('');
          res.body.value.message.should.equal('Could not start Browser / Emulator');
          done();
        }).catch(function(err) {
          throw err;
        });
      }).catch(function(err) {
        throw err;
      });
    });
    it('reg_session is sent after session is started', function(done) {
      this.timeout(5000);
      var requestBody = helper.getCapabilities({
        browser: 'Firefox',
        'browserstack.video': false
      }),
      railsScope1 = helper.buildRails();

      var seleniumScope1 = helper.selenium()
      .post('/wd/hub/session')
      .query(true)
      .reply(function(uri, body) {
        return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
      });

      var railsScope2 = helper.buildRails({
        query: true,
        action: function(uri, body) {
          var options = helper.getParamsFromQuery(uri);
          options.reg_session.should.equal('true');
          return [200, {done: true}];
        }
      });

      helper.request.post('/wd/hub/session')
      .auth('test', 'test')
      .send(requestBody)
      .then(function(res) {
        helper.sleep(1000)
        .then(() => {
          railsScope1.isDone().should.be.true;
          seleniumScope1.isDone().should.be.true;
          railsScope2.isDone().should.be.true;
          res.status.should.equal(200);
          res.body.status.should.equal(0);
          res.body.sessionId.should.equal(helper.SESSION_ID);
          res.body.value.should.have.any.keys('browser', 'browser_version', 'os', 'os_version');
          done();
        }).catch(function(err) {
          throw err;
        });
      }).catch(function(err) {
        throw err;
      });
    });

    it(`Add youiEngineAppPort in start req if youiengine_driver_port is present in bscaps`, (done) => {
      var requestBody = helper.AppAutomateCapabilities({
          "app": "bs://testapp",
          "locale": "en_US"
        }),
        railsScope = helper.buildRails(),
        appAutomateRailsScope = helper.buildRails({ isAppAutomate: true }),
        macTerminalScope = helper.buildMac({
          action: function (options) {
            return [200, { done: true }];
          }
        });

      requestBody.desiredCapabilities["youiengine_driver_port"] = "18201";
      requestBody.desiredCapabilities["automationName"] = "YouIEngine";

      seleniumScope = helper.selenium(undefined, undefined, helper.APPIUM_PORT)
        .post('/wd/hub/session')
        .query(true)
        .reply(function (uri, body) {
          body = JSON.parse(body).desiredCapabilities;
          body.should.have.any.keys('youiEngineAppPort');
          body.youiEngineAppPort.should.equal("18201");
          return helper.sessionStartedJSON(body);
        });

      helper.request.post('/wd/hub/session')
        .auth('test', 'test')
        .send(requestBody)
        .end((err, res) => {

          railsScope.isDone()
            .should
            .equal(false);
          appAutomateRailsScope.isDone()
            .should
            .equal(true);
          macTerminalScope.isDone()
            .should
            .equal(true);
          seleniumScope.isDone()
            .should
            .equal(true);
          res.status.should.equal(200);

          res.body.sessionId.should.equal(helper.SESSION_ID);
          res.body.status.should.equal(0);
          done();
        });
    });

    it('Retry a session for AppAutomate in case of start-error', (done) => {
      var requestBody = helper.AppAutomateCapabilities(),
        appAutomateRailsScope1 = helper.buildRails({ isAppAutomate: true }),
        macTerminalScope = helper.buildMac().persist(),
        seleniumScope1 = helper.selenium(undefined, undefined, helper.APPIUM_PORT)
        .post('/wd/hub/session')
        .query(true)
        .reply(500, {
          sessionId: null,
          status: 13,
          value: { message: 'Unable to launch WebDriverAgent because of xcodebuild failure: xcodebuild failed with code 65' }
        }),
        appAutomateHardReleaseScope = helper.buildRails({
          extraValidation: function(uri, body) {
            body = JSON.parse(body);
            body.hardRelease.should.equal(helper.TERMINAL_IP);
          },
          query: {auth: 'selautomate', hrelease: true},
          isAppAutomate: true
        }),
        seleniumScope2 = helper.selenium(undefined, undefined, helper.APPIUM_PORT)
          .post('/wd/hub/session')
          .query(true)
          .reply(function(uri, body) {
            body = JSON.parse(body).desiredCapabilities;
          return helper.sessionStartedJSON(body);
          });

      helper.request.post('/wd/hub/session')
      .auth('test', 'test')
      .send(requestBody)
      .end(function(err, res) {
        appAutomateRailsScope1.isDone().should.be.true;
        macTerminalScope.isDone().should.be.true;
        seleniumScope1.isDone().should.be.true;
        appAutomateHardReleaseScope.isDone().should.be.true;
        seleniumScope2.isDone().should.be.true;

        res.status.should.equal(200);
        res.body.sessionId.should.equal(helper.SESSION_ID);
        res.body.status.should.equal(0);
        done();
      });
    });

    it('Does not retry session for AppAutomate in case of non-expected scenarios', (done) => {
      var requestBody = helper.AppAutomateCapabilities(),
        appAutomateRailsScope1 = helper.buildRails({ isAppAutomate: true }),
        macTerminalScope = helper.buildMac().persist(),
        seleniumScope1 = helper.selenium(undefined, undefined, helper.APPIUM_PORT)
        .post('/wd/hub/session')
        .query(true)
        .reply(500, {
          sessionId: null,
          status: 13,
          value: { message: 'have a xctestrun file' }
        }),
        appAutomateHardReleaseScope = helper.buildRails({
          extraValidation: function(uri, body) {
            body = JSON.parse(body);
            body.hardRelease.should.equal(helper.TERMINAL_IP);
          },
          query: {auth: 'selautomate', hrelease: true},
          isAppAutomate: true
        }),
        seleniumScope2 = helper.selenium(undefined, undefined, helper.APPIUM_PORT)
          .post('/wd/hub/session')
          .query(true)
          .reply(function(uri, body) {
            body = JSON.parse(body).desiredCapabilities;
          return helper.sessionStartedJSON(body);
          });

      helper.request.post('/wd/hub/session')
      .auth('test', 'test')
      .send(requestBody)
      .end(function(err, res) {
        appAutomateRailsScope1.isDone().should.be.true;
        macTerminalScope.isDone().should.be.true;
        seleniumScope1.isDone().should.be.true;
        appAutomateHardReleaseScope.isDone().should.be.false;
        seleniumScope2.isDone().should.be.false;

        res.status.should.equal(200);
        res.body.sessionId.should.equal('');
        res.body.status.should.equal(13);
        done();
      });
    });

    it('Retry a session exactly once for AppAutomate in case of start-error', (done) => {
      var requestBody = helper.AppAutomateCapabilities(),
        count = 0,
        appAutomateRailsScope1 = helper.buildRails({ isAppAutomate: true }),
        macTerminalScope = helper.buildMac().persist(),
        seleniumScope1 = helper.selenium(undefined, undefined, helper.APPIUM_PORT)
        .persist()
        .post('/wd/hub/session')
        .query(true)
        .reply(function(uri, body) {
          count++;
          return [500, {
            sessionId: null,
            status: 13,
            value: { message: 'Unable to launch WebDriverAgent because of xcodebuild failure: xcodebuild failed with code 65' }
          }]
        }),
        appAutomateHardReleaseScope = helper.buildRails({
          extraValidation: function(uri, body) {
            body = JSON.parse(body);
            body.hardRelease.should.equal(helper.TERMINAL_IP);
          },
          query: {auth: 'selautomate', hrelease: true},
          isAppAutomate: true
        }).persist();

      helper.request.post('/wd/hub/session')
      .auth('test', 'test')
      .send(requestBody)
      .end(function(err, res) {
        appAutomateRailsScope1.isDone().should.be.true;
        macTerminalScope.isDone().should.be.true;
        seleniumScope1.isDone().should.be.true;
        appAutomateHardReleaseScope.isDone().should.be.true;

        count.should.equal(2);
        res.status.should.equal(200);
        res.body.sessionId.should.equal('');
        res.body.status.should.equal(13);
        done();
      });
    });

    context('Edge Chromium Caps', () => {
      const testFunction = (done, browserVersion, seleniumVersion, shouldHaveChromeOptions) => {
        const requestBody = helper.getCapabilities({
          browser: 'Edge',
          browser_version: browserVersion,
          os_version: 'Catalina',
          'browserstack.video': false,
          'browserstack.selenium_version': seleniumVersion,
          chromeOptions: {mockChromeOption: 'mockValue'},
        }),
        railsScope = helper.buildRails();
        macTerminalScope = helper.buildMac();

        const seleniumScope = helper.selenium('OS X', seleniumVersion)
        .post('/wd/hub/session')
        .query(true)
        .reply(function(uri, body) {
          body = JSON.parse(body).desiredCapabilities;
          return [200, {
            sessionId: '1234-abcd-7890-wxyz',
            status: 0,
            value: { desiredCapabilities: body }
          }];
        });

        helper.request.post('/wd/hub/session')
        .auth('test', 'test')
        .send(requestBody)
        .then(function(res) {
          railsScope.isDone().should.be.true;
          seleniumScope.isDone().should.be.true;
          macTerminalScope.isDone().should.be.true;
          if (shouldHaveChromeOptions) {
            assert.isDefined(res.body.value.desiredCapabilities.chromeOptions);
          } else {
            assert.isUndefined(res.body.value.desiredCapabilities.chromeOptions);
            assert.isDefined(res.body.value.desiredCapabilities['ms:edgeOptions']);
          }
          done();
        }).catch(function(err) {
          throw err;
        });
      };
      it('Remove chromeOptions for edge chromium for selenium beta 2', (done) => { testFunction(done, 82, '4.0.0-beta-2', false); });
      it('Remove chromeOptions for edge chromium for selenium beta 3', (done) => { testFunction(done, 82, '4.0.0-beta-3', false); });
      it('Does not remove chromeOptions for edge chromium for selenium 3', (done) => { testFunction(done, 82, '3.14', true); });
      it('Does not remove chromeOptions for edge chromium for Edge version < 80', (done) => { testFunction(done, 79, '4.0.0-beta-2', true); });
    });
  });
});
