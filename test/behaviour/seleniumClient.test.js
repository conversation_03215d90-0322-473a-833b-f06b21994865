'use strict';

const SeleniumClient = require('../../seleniumClient');
const sinon = require('sinon');
const assert = require('assert');

describe('test methods in seleniumClient', () => {
  it('throw err when sessionID not present', (done) => {
    const params = {
      name: 'hostname',
      port: '12345',
      selenium_version: '3.14',
    };
    const client = new SeleniumClient(params);
    try {
      client.validateSessionId();
    } catch (e) {
      done();
    }
  });
  it('dont throw err when sessionID is present', (done) => {
    const params = {
      name: 'hostname',
      port: '12345',
      key: 'randomID',
      selenium_version: '3.14',
    };
    const client = new SeleniumClient(params);
    client.validateSessionId();
    done();
  });
  it('sendKeys() should validate sessionID and then make request', (done) => {
    const params = {
      name: 'hostname',
      port: '12345',
      key: 'randomID',
      selenium_version: '3.14',
    };
    const client = new SeleniumClient(params);
    sinon.stub(client, 'validateSessionId');
    const x = sinon.stub(client, 'makeRequest').returns(Promise.resolve(true));
    client.sendKeys('random key');
    assert(x.calledOnce === true);
    client.validateSessionId.restore();
    client.makeRequest.restore();
    done();
  });
});
