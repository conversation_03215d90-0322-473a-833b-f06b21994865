'use strict';

const assert = require('assert');
const helper = require('./helper');
const origHelper = require('../../helper');
const winston = require('winston');
const sinon = require('sinon');

const TimeoutManager = require('../../apps/timeoutManager/timeoutManager');

describe('Timeout Manager', () => {
  it('create timeout Manager object', (done) => {
    let _ = new TimeoutManager({winston});
    done();
  });

  it('check if message delay gets updated', (done) => {
    const timeoutManager = new TimeoutManager({winston});
    const delay = 10000;
    const message = {
      time: Date.now() - delay,
    };
    const response = timeoutManager.checkDelay(message);
    const receivedDelay = response.split(' ')[1];
    assert(receivedDelay >= delay);
    done();
  });


  it('pushHootHootStats should invoke helper.pushStatsToHootHoot and reset hoothootPerMinKeys', (done) => {
    const timeoutManager = new TimeoutManager({winston});
    sinon.stub(timeoutManager.winston, 'debug');
    sinon.stub(origHelper, 'pushStatsToHootHoot');
    timeoutManager.pushHootHootStats();
    origHelper.pushStatsToHootHoot.called.should.be.true;
    helper.constants.timeoutManager.hoothootPerMinKeys.forEach((hoothootKey) => {
      timeoutManager.trackHootHootStats[hoothootKey].should.equal(0);
    });

    timeoutManager.winston.debug.restore();
    origHelper.pushStatsToHootHoot.restore();
    done();
  });
});
