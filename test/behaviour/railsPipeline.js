'use strict';

const Promise = require('bluebird');

const railsPipeline = require('../../railsRequests/railsPipeline');
const RailsPipelineHelper = require('../../railsRequests/railsPipelineHelper');
const constants = require('../../constants');
const hubHelper = require('../../helper');
const helper = require('./helper');
const assert = require('assert');
const sinon = require('sinon');
const HubLogger = require('../../log');

const railsPipelineHelper = new RailsPipelineHelper();

let pipelineReqMeta = {};
let user = {};
let pushData = {};

const startForcedDowntime = () =>
  hubHelper.redisClient.set(constants.railsPipeline.redisAutomateTrackTokenHash.downtimeToken, 'forcedZero');
const stopDowntime = () => hubHelper.redisClient
  .del(constants.railsPipeline.redisAutomateTrackTokenHash.downtimeToken);
const appAutomateStartForcedDownTime = () =>
  hubHelper.redisClient.set(constants.railsPipeline.redisAppAutomateTrackTokenHash.downtimeToken, 'forcedZero');
const appAutomateStopDowntime = () =>
  hubHelper.redisClient.del(constants.railsPipeline.redisAppAutomateTrackTokenHash.downtimeToken);

describe('Tests helper methods', () => {
  describe('#sendDataToZombie', () => {
    ['automate', 'appAutomate'].forEach((product) => {
      it(`sends the data to zombie for ${product}`, () => {
        const x = sinon.spy(hubHelper, 'PingZombie');
        railsPipelineHelper.sendDataToZombie(product, 30);
        assert(x.calledOnce === true);
        hubHelper.PingZombie.restore();
      });
    });
  });
});

describe('pipeline alerts with and without forced downt time', () => {
  after((done) => {
    hubHelper.redisClient.flushall()
      .then(() => {
        done();
      });
  });

  afterEach((done) => {
    railsPipelineHelper.requestQueue.appAutomate.length = 0;
    railsPipelineHelper.requestQueue.automate.length = 0;
    stopDowntime().then(() => {
      appAutomateStopDowntime().then(() => {
        done();
      });
    });
  });

  it('should not send alerts if queue is not critical', (done) => {
    railsPipelineHelper.requestQueue.appAutomate.length = 2;
    railsPipelineHelper.requestQueue.automate.length = 10;
    const y = sinon.spy(railsPipelineHelper, 'checkQueueCriticalAndDowntimeState');
    const z = sinon.spy(railsPipelineHelper, 'sendDataToZombie');
    railsPipelineHelper.sendAlertOnQueueCritical().then(() => {
      assert(y.calledTwice === true);
      assert(z.notCalled === true);
      assert(y.calledWith('automate') === true);
      assert(y.calledWith('appAutomate') === true);
      railsPipelineHelper.checkQueueCriticalAndDowntimeState.restore();
      railsPipelineHelper.sendDataToZombie.restore();
      railsPipelineHelper.requestQueue.appAutomate.length = 0;
      railsPipelineHelper.requestQueue.automate.length = 0;
      done();
    });
  });

  it('should send alerts if queue is critical for automate,app-automate and down time is not enabled for automate, app-automate', (done) => {
    railsPipelineHelper.requestQueue.appAutomate.length = 60;
    railsPipelineHelper.requestQueue.automate.length = 60;
    const y = sinon.spy(railsPipelineHelper, 'checkQueueCriticalAndDowntimeState');
    const z = sinon.spy(railsPipelineHelper, 'sendDataToZombie');
    railsPipelineHelper.sendAlertOnQueueCritical().then(() => {
      assert(y.calledTwice === true);
      assert(z.calledTwice === true);
      railsPipelineHelper.checkQueueCriticalAndDowntimeState.restore();
      railsPipelineHelper.sendDataToZombie.restore();
      done();
    });
  });
  ['automate', 'appAutomate'].forEach((product) => {
    it(`should send alerts for ${product} if queue is critical for ${product} and down time is enabled for ${product === 'automate' ? 'appAutomate' : 'automate'}`, (done) => {
      railsPipelineHelper.requestQueue[product].length = 60;
      if (product === 'automate') {
        appAutomateStartForcedDownTime();
      } else {
        startForcedDowntime();
      }
      const y = sinon.spy(railsPipelineHelper, 'sendDataToZombie');
      railsPipelineHelper.sendAlertOnQueueCritical().then(() => {
        assert(y.calledOnce === true);
        railsPipelineHelper.sendDataToZombie.restore();
        done();
      });
    });
  });
  it('should not send alerts to both if queue is critical for automate,app-automate and down time is enabled for both', (done) => {
    railsPipelineHelper.requestQueue.appAutomate.length = 60;
    railsPipelineHelper.requestQueue.automate.length = 60;
    startForcedDowntime().then(() => {
      appAutomateStartForcedDownTime().then(() => {
        const z = sinon.spy(railsPipelineHelper, 'sendDataToZombie');
        const y = sinon.spy(railsPipelineHelper, 'checkQueueCriticalAndDowntimeState');
        railsPipelineHelper.sendAlertOnQueueCritical().then(() => {
          assert(y.calledTwice === true);
          assert(z.notCalled === true);
          railsPipelineHelper.checkQueueCriticalAndDowntimeState.restore();
          railsPipelineHelper.sendDataToZombie.restore();
          done();
        });
      });
    });
  });
});

describe('behaviour of rails pipeline helper methods for app automate', () => {
  after((done) => {
    hubHelper.redisClient.flushall()
      .then(() => {
        done();
      });
  });

  beforeEach((done) => {
    user = {
      username: 'user::name',
      password: 'user::pass',
      local: true,
      response: {
        statusCode: 200,
      },
    };

    constants.pushToHootHootRegistry = {};

    const resolveRequest = () => { };

    pushData = {
      username: 'raichu',
      identifier: 'pipe::ident',
      isStartRequest: true,
      isStopRequest: false,
      sessionId: helper.SESSION_ID,
      isAppAutomateSession: true,
      railsPipelineExtraData: {
        isQueued: false,
        isMobile: false,
      },
      resolveRequest,
    };

    pipelineReqMeta = {
      identifier: 'pipe::ident',
      sessionId: helper.SESSION_ID,
      username: 'user::name',
      password: 'user::pass',
      local: true,
      response: {
        statusCode: 200,
      },
      isStartRequest: true,
      isStopRequest: false,
      isAppAutomateSession: true,
      railsPipelineExtraData: {
        isQueued: false,
        isMobile: false,
      },
    };

    hubHelper.redisClient.flushall()
      .then(() => {
        done();
      });
  });

  it('should flush data to hoot hoot', (done) => {
    const x = sinon.stub(hubHelper, 'pushStatsToHootHoot');
    railsPipelineHelper.flushDataToHootHoot();
    assert(x.calledOnce === true);
    hubHelper.pushStatsToHootHoot.restore();
    done();
  });

  it('print the logs', (done) => {
    const x = sinon.spy(HubLogger, 'miscLogger');
    railsPipelineHelper.log('', '', 'Test');
    assert(x.calledOnce === true);
    HubLogger.miscLogger.restore();
    done();
  });

  it('should respond to the blocked user', (done) => {
    const x = sinon.stub(railsPipelineHelper.railsDefender, 'respondToBlockedUser').returns(Promise.resolve());
    railsPipelineHelper.respondToBlockedUser(user.username, user.password, user.local, user.response)
      .then(() => {
        assert(x.calledOnce === true);
        done();
        railsPipelineHelper.railsDefender.respondToBlockedUser.restore();
      });
  });

  it('should register rails requests and add request to the track set for app automate', (done) => {
    pipelineReqMeta.identifier = 'uniqIdentifier::asdkhfj';
    const x = sinon.spy(hubHelper, 'instrumentRedisCommand');

    railsPipelineHelper
      .registerRailsRequest(
        pipelineReqMeta.identifier,
        pipelineReqMeta.isAppAutomateSession, pipelineReqMeta.sessionId
      )
      .then(() => {
        assert(x.called);
        assert(constants.pushToHootHootRegistry.redisCommandCount.registerRailsRequest);
        hubHelper.redisClient
          .sismember(
            constants.railsPipeline.redisAppAutomateTrackTokenHash.queueTrackRequestsSetTag,
            pipelineReqMeta.identifier
          )
          .then((res) => {
            res.should.equal(1);
            hubHelper.instrumentRedisCommand.restore();
            done();
          });
      });
  });

  it('should unregister rails requests and remove from track set for app automate', (done) => {
    const x = sinon.spy(hubHelper, 'instrumentRedisCommand');
    hubHelper.redisClient.multi()
      .sadd(
        constants.railsPipeline.redisAppAutomateTrackTokenHash.queueTrackRequestsSetTag,
        pipelineReqMeta.identifier
      )
      .exec()
      .then(() => {
        railsPipelineHelper
          .unregisterRailsRequest(pipelineReqMeta.identifier, pipelineReqMeta.isAppAutomateSession)
          .then(() => {
            assert(x.called);
            assert(constants.pushToHootHootRegistry.redisCommandCount.unregisterRailsRequest);
            hubHelper.redisClient
              .sismember(
                constants.railsPipeline.redisAppAutomateTrackTokenHash.queueTrackRequestsSetTag,
                pipelineReqMeta.identifier
              ).then((res) => {
                res.should.equal(0);
                hubHelper.instrumentRedisCommand.restore();
                done();
              });
          });
      });
  });

  it('should call poll pipeline for app automate and not call enqueue request', (done) => {
    const responseParams = {
      needToQueue: false,
      maxRequestCount: 256,
      trackRequestSetSize: 10,
    };
    railsPipelineHelper.requestQueue.appAutomate.push(pushData);
    const x = sinon.stub(railsPipelineHelper, 'registerRailsRequest').returns(Promise.resolve(responseParams));
    railsPipelineHelper.pollPipeline('appAutomate');
    assert(x.calledOnce === true);
    railsPipelineHelper.registerRailsRequest.restore();
    railsPipelineHelper.requestQueue.appAutomate.length = 0;
    done();
  });
});

describe('behaviour of rails pipeline helper methods for automate', () => {
  after((done) => {
    hubHelper.redisClient.flushall()
      .then(() => {
        done();
      });
  });

  beforeEach((done) => {
    user = {
      username: 'user::name',
      password: 'user::pass',
      local: true,
      response: {
        statusCode: 200,
      },
    };

    pipelineReqMeta = {
      identifier: 'pipe::ident',
      sessionId: helper.SESSION_ID,
      username: 'user::name',
      password: 'user::pass',
      local: true,
      response: {
        statusCode: 200,
      },
      isStartRequest: true,
      isStopRequest: false,
      isAppAutomateSession: false,
      railsPipelineExtraData: {
        isQueued: false,
        isMobile: false,
      },
    };

    constants.pushToHootHootRegistry = {};

    const resolveRequest = () => { };

    pushData = {
      username: 'random',
      identifier: 'pipe::ident',
      isStartRequest: true,
      isStopRequest: false,
      sessionId: helper.SESSION_ID,
      isAppAutomateSession: true,
      railsPipelineExtraData: {
        isQueued: false,
        isMobile: false,
      },
      resolveRequest,
    };

    hubHelper.redisClient.flushall()
      .then(() => {
        done();
      });
  });

  it('should respond to the blocked user', (done) => {
    const x = sinon.stub(railsPipelineHelper.railsDefender, 'respondToBlockedUser').returns(Promise.resolve());

    railsPipelineHelper.respondToBlockedUser(user.username, user.password, user.local, user.response)
      .then(() => {
        assert(x.calledOnce === true);
        done();
        railsPipelineHelper.railsDefender.respondToBlockedUser.restore();
      });
  });

  it('should register rails requests and add request to the track set for automate', (done) => {
    pipelineReqMeta.identifier = 'uniqIdentifier::asdkhfj';
    const x = sinon.spy(hubHelper, 'instrumentRedisCommand');

    railsPipelineHelper
      .registerRailsRequest(
        pipelineReqMeta.identifier,
        pipelineReqMeta.isAppAutomateSession, pipelineReqMeta.sessionId
      )
      .then(() => {
        assert(x.called);
        assert(constants.pushToHootHootRegistry.redisCommandCount.registerRailsRequest);
        hubHelper.redisClient
          .sismember(
            constants.railsPipeline.redisAutomateTrackTokenHash.queueTrackRequestsSetTag,
            pipelineReqMeta.identifier
          )
          .then((res) => {
            res.should.equal(1);
            hubHelper.instrumentRedisCommand.restore();
            done();
          });
      });
  });

  it('should unregister rails requests and remove from track set for automate', (done) => {
    const x = sinon.spy(hubHelper, 'instrumentRedisCommand');
    hubHelper.redisClient.multi()
      .sadd(
        constants.railsPipeline.redisAutomateTrackTokenHash.queueTrackRequestsSetTag,
        pipelineReqMeta.identifier
      )
      .exec()
      .then(() => {
        railsPipelineHelper
          .unregisterRailsRequest(pipelineReqMeta.identifier, pipelineReqMeta.isAppAutomateSession)
          .then(() => {
            assert(x.called);
            assert(constants.pushToHootHootRegistry.redisCommandCount.unregisterRailsRequest);
            hubHelper.redisClient
              .sismember(
                constants.railsPipeline.redisAutomateTrackTokenHash.queueTrackRequestsSetTag,
                pipelineReqMeta.identifier
              ).then((res) => {
                res.should.equal(0);
                hubHelper.instrumentRedisCommand.restore();
                done();
              });
          });
      });
  });

  it('should call poll pipeline for automate and not call enqueue request', (done) => {
    const responseParams = {
      needToQueue: false,
      maxRequestCount: 256,
      trackRequestSetSize: 10,
    };
    railsPipelineHelper.requestQueue.automate.push(pushData);
    const x = sinon.stub(railsPipelineHelper, 'registerRailsRequest').returns(Promise.resolve(responseParams));
    railsPipelineHelper.pollPipeline('automate');
    assert(x.calledOnce === true);
    railsPipelineHelper.registerRailsRequest.restore();
    railsPipelineHelper.requestQueue.automate.length = 0;
    done();
  });
});

describe('rails pipeline behaviour for app automate', () => {
  after((done) => {
    hubHelper.redisClient.flushall()
      .then(() => {
        done();
      });
  });

  beforeEach((done) => {
    pipelineReqMeta = {
      identifier: 'pipe::ident',
      sessionId: helper.SESSION_ID,
      username: 'user::name',
      password: 'user::pass',
      local: true,
      response: {
        statusCode: 200,
      },
      isStartRequest: true,
      isStopRequest: false,
      isAppAutomateSession: true,
      railsPipelineExtraData: {
        isQueued: false,
        isMobile: false,
      },
    };

    hubHelper.redisClient.flushall()
      .then(() => {
        done();
      });
  });

  it('resolves with shouldDrop = false when requestThreshold to rails is not yet reached for app automate', (done) => {
    railsPipeline.railsRequestPipeline(pipelineReqMeta)
      .then((shouldDrop) => {
        shouldDrop.should.equal(false);
        done();
      });
  });

  it('should increase track set size when threshold is not reached for app automate', (done) => {
    railsPipeline.railsRequestPipeline(pipelineReqMeta)
      .then(() => {
        hubHelper.redisClient.scard(constants.railsPipeline
          .redisAppAutomateTrackTokenHash.queueTrackRequestsSetTag)
          .then((res) => {
            res.should.equal(1);
            done();
          });
      });
  });

  it('should add identifier to redis for app automate', (done) => {
    pipelineReqMeta.identifier = 'uniqIdentifier::asdkhfj';
    railsPipeline.railsRequestPipeline(pipelineReqMeta)
      .then(() => {
        hubHelper.redisClient
          .sismember(
            constants.railsPipeline.redisAppAutomateTrackTokenHash.queueTrackRequestsSetTag,
            pipelineReqMeta.identifier
          )
          .then((res) => {
            res.should.equal(1);
            done();
          });
      });
  });

  it('should never resolve when requestThreshold is reached for app automate', (done) => {
    let promiseResolved = false;

    hubHelper.redisClient.multi()
      .sadd(
        constants.railsPipeline.redisAppAutomateTrackTokenHash.queueTrackRequestsSetTag,
        ['uniqIdentifier1::asdkhfj', 'uniqIdentifier2::asdkhfj', 'uniqIdentifier3::asdkhfj', 'uniqIdentifier4::asdkhfj', 'uniqIdentifier5::asdkhfj']
      )
      .set(constants.railsPipeline.redisAppAutomateTrackTokenHash.queueCountMaxTag, 5)
      .exec()
      .then(() => {
        railsPipeline.railsRequestPipeline(pipelineReqMeta)
          .then(() => {
            promiseResolved = true;
          });
      });

    Promise.delay(500).then(() => {
      promiseResolved.should.equal(false);
      done();
    });
  });

  it('should remove from redis set on response for app automate', (done) => {
    const x = sinon.stub(railsPipeline.pipelineHelper, 'unregisterRailsRequest').returns(Promise.resolve(true));
    const y = sinon.stub(railsPipeline.pipelineHelper, 'manipulateRailsResponseTokens');
    const z = sinon.stub(railsPipeline.pipelineHelper, 'cacheRailsResponse').returns(Promise.resolve());
    hubHelper.redisClient.multi()
      .sadd(
        constants.railsPipeline.redisAppAutomateTrackTokenHash.queueTrackRequestsSetTag,
        pipelineReqMeta.identifier
      )
      .exec()
      .then(() => {
        railsPipeline.railsResponsePipeline(pipelineReqMeta)
          .then(() => {
            assert(x.calledOnce === true);
            assert(y.calledOnce === true);
            assert(z.calledOnce === true);
            railsPipeline.pipelineHelper.unregisterRailsRequest.restore();
            railsPipeline.pipelineHelper.manipulateRailsResponseTokens.restore();
            railsPipeline.pipelineHelper.cacheRailsResponse.restore();
            done();
          });
      });
  });
});

describe('railsPipeline behaviour for automate', () => {
  after((done) => {
    hubHelper.redisClient.flushall()
      .then(() => {
        done();
      });
  });

  beforeEach((done) => {
    pipelineReqMeta = {
      identifier: 'pipe::ident',
      sessionId: helper.SESSION_ID,
      username: 'user::name',
      password: 'user::pass',
      local: true,
      response: {
        statusCode: 200,
      },
      isStartRequest: true,
      isStopRequest: false,
      isAppAutomateSession: false,
      railsPipelineExtraData: {
        isQueued: false,
        isMobile: false,
      },
    };

    hubHelper.redisClient.flushall()
      .then(() => {
        done();
      });
  });

  it('resolves with shouldDrop = false when requestThreshold to rails is not yet reached', (done) => {
    railsPipeline.railsRequestPipeline(pipelineReqMeta)
      .then((shouldDrop) => {
        shouldDrop.should.equal(false);
        done();
      });
  });

  it('should increase track request set size when threshold is not reached', (done) => {
    railsPipeline.railsRequestPipeline(pipelineReqMeta)
      .then(() => {
        hubHelper.redisClient
          .scard(constants.railsPipeline.redisAutomateTrackTokenHash.queueTrackRequestsSetTag)
          .then((res) => {
            res.should.equal(1);
            done();
          });
      });
  });

  it('should add identifier to redis', (done) => {
    pipelineReqMeta.identifier = 'uniqIdentifier::asdkhfj';
    railsPipeline.railsRequestPipeline(pipelineReqMeta)
      .then(() => {
        hubHelper.redisClient
          .sismember(
            constants.railsPipeline.redisAutomateTrackTokenHash.queueTrackRequestsSetTag,
            pipelineReqMeta.identifier
          )
          .then((res) => {
            res.should.equal(1);
            done();
          });
      });
  });

  it('should never resolve when requestThreshold is reached', (done) => {
    let promiseResolved = false;

    hubHelper.redisClient.multi()
      .sadd(
        constants.railsPipeline.redisAutomateTrackTokenHash.queueTrackRequestsSetTag,
        ['uniqIdentifier1::asdkhfj', 'uniqIdentifier2::asdkhfj', 'uniqIdentifier3::asdkhfj', 'uniqIdentifier4::asdkhfj', 'uniqIdentifier5::asdkhfj']
      )
      .set(constants.railsPipeline.redisAutomateTrackTokenHash.queueCountMaxTag, 5)
      .exec()
      .then(() => {
        railsPipeline.railsRequestPipeline(pipelineReqMeta)
          .then(() => {
            promiseResolved = true;
          });
      });

    Promise.delay(500).then(() => {
      promiseResolved.should.equal(false);
      done();
    });
  });

  it('should remove from redis set on response', (done) => {
    const x = sinon.stub(railsPipeline.pipelineHelper, 'unregisterRailsRequest').returns(Promise.resolve(true));
    const y = sinon.stub(railsPipeline.pipelineHelper, 'manipulateRailsResponseTokens');
    const z = sinon.stub(railsPipeline.pipelineHelper, 'cacheRailsResponse').returns(Promise.resolve());
    hubHelper.redisClient.multi()
      .sadd(
        constants.railsPipeline.redisAutomateTrackTokenHash.queueTrackRequestsSetTag,
        pipelineReqMeta.identifier
      )
      .exec()
      .then(() => {
        railsPipeline.railsResponsePipeline(pipelineReqMeta)
          .then(() => {
            assert(x.calledOnce === true);
            assert(y.calledOnce === true);
            assert(z.calledOnce === true);
            railsPipeline.pipelineHelper.unregisterRailsRequest.restore();
            railsPipeline.pipelineHelper.manipulateRailsResponseTokens.restore();
            railsPipeline.pipelineHelper.cacheRailsResponse.restore();
            done();
          });
      });
  });
});

['automate', 'appAutomate'].forEach((product) => {
  describe(`#verifyPipelineExtraDataForPushToHootHoot from railsPipelineHelper for ${product}`, () => {
    const isAppAutomateSession = product === 'appAutomate';

    beforeEach((done) => {
      sinon.spy(railsPipelineHelper, 'pushToHoothoot');
      done();
    });

    afterEach((done) => {
      railsPipelineHelper.pushToHoothoot.restore();
      done();
    });

    it('should not push to hoot if not start request', (done) => {
      railsPipelineHelper.verifyPipelineExtraDataForPushToHootHoot(false, { isQueued: false, isMobile: false }, 'user_test', isAppAutomateSession);
      assert(railsPipelineHelper.pushToHoothoot.called === false);
      done();
    });

    it('should not push to hoot if isQueued true', (done) => {
      railsPipelineHelper.verifyPipelineExtraDataForPushToHootHoot(true, { isQueued: true, isMobile: false }, 'user_test', isAppAutomateSession);
      assert(railsPipelineHelper.pushToHoothoot.called === false);
      done();
    });

    it('should not push to hoot if isQueued true and not start request', (done) => {
      railsPipelineHelper.verifyPipelineExtraDataForPushToHootHoot(false, { isQueued: true, isMobile: false }, 'user_test', isAppAutomateSession);
      assert(railsPipelineHelper.pushToHoothoot.called === false);
      done();
    });

    it('should push to hoot if isQueued false and is start request', (done) => {
      railsPipelineHelper.verifyPipelineExtraDataForPushToHootHoot(true, { isQueued: false, isMobile: false }, 'user_test', isAppAutomateSession);
      assert(railsPipelineHelper.pushToHoothoot.called === true);
      done();
    });
  });
});

['automate', 'appAutomate'].forEach((product) => {
  describe(`common pipeline behavioral tests for ${product}`, () => {
    after((done) => {
      hubHelper.redisClient.flushall()
        .then(() => {
          done();
        });
    });

    beforeEach((done) => {
      user = {
        username: 'user::name',
        password: 'user::pass',
        local: true,
        response: {
          statusCode: 200,
        },
      };
      const isAppAutomateSession = product === 'appAutomate';
      pipelineReqMeta = {
        identifier: 'pipe::ident',
        sessionId: helper.SESSION_ID,
        username: 'user::name',
        password: 'user::pass',
        local: true,
        response: {
          statusCode: 200,
        },
        isStartRequest: true,
        isStopRequest: false,
        isAppAutomateSession,
        railsPipelineExtraData: {
          isQueued: false,
          isMobile: false,
        },
      };

      const resolveRequest = () => { };

      pushData = i => ({
        username: `random_${i}`,
        identifier: `pipe::ident_${i}`,
        isStartRequest: true,
        isStopRequest: false,
        sessionId: helper.SESSION_ID,
        isAppAutomateSession,
        railsPipelineExtraData: {
          isQueued: false,
          isMobile: false,
        },
        resolveRequest,
      });

      hubHelper.redisClient.flushall()
        .then(() => {
          done();
        });
    });

    it(`should call poll pipeline for ${product} and call enqueue request`, (done) => {
      const x = sinon.spy(railsPipelineHelper, 'pushToHoothoot');

      const responseParams = {
        needToQueue: true,
        maxRequestCount: 2,
        trackRequestSetSize: 10,
      };

      const pushDataTmp = pushData(1);

      // pollPipeline should always be tested with a callback method
      // without cb we will be facing race conditions while testing
      const cb = () => {
        assert(x.calledOnce === true);
        assert(pushDataTmp.railsPipelineExtraData.isQueued === true);
        railsPipelineHelper.pushToHoothoot.restore();
        railsPipelineHelper.registerRailsRequest.restore();
        railsPipelineHelper.requestQueue[product].length = 0;
        railsPipelineHelper.enqueueRequest.restore();
        done();
      };

      pushDataTmp.resolveRequest = cb;
      railsPipelineHelper.requestQueue[product].push(pushDataTmp);

      sinon.stub(railsPipelineHelper, 'registerRailsRequest').returns(Promise.resolve(responseParams));
      sinon.stub(railsPipelineHelper, 'enqueueRequest').returns(Promise.resolve(true));

      railsPipelineHelper.pollPipeline(product);
    });

    it(`should call poll pipeline for ${product} and push all the start requests to hoothoot during forced downtime`, (done) => {
      const x = sinon.spy(railsPipelineHelper, 'pushToHoothoot');
      sinon.stub(railsPipelineHelper, 'checkDowntimeEnabled').returns(Promise.resolve(constants.monitoringDowntimeStates.forcedDowntimeState));
      const responseParams = {
        needToQueue: true,
        maxRequestCount: 2,
        trackRequestSetSize: 15,
      };
      const requestQueueArray = [];

      const cb = () => {
        assert(x.callCount === 15);
        requestQueueArray.forEach((request) => {
          assert(request.railsPipelineExtraData.isQueued === true);
        });
        railsPipelineHelper.checkDowntimeEnabled.restore();
        railsPipelineHelper.enqueueRequest.restore();
        railsPipelineHelper.pushToHoothoot.restore();
        railsPipelineHelper.registerRailsRequest.restore();
        railsPipelineHelper.requestQueue[product].length = 0;
        done();
      };

      for (let i = 0; i < 15; i++) {
        requestQueueArray.push(pushData(i));
        if (i === 0) requestQueueArray[i].resolveRequest = cb;
        railsPipelineHelper.requestQueue[product].push(requestQueueArray[i]);
      }

      sinon.stub(railsPipelineHelper, 'registerRailsRequest').returns(Promise.resolve(responseParams));
      sinon.stub(railsPipelineHelper, 'enqueueRequest').returns(Promise.resolve(true));

      railsPipelineHelper.pollPipeline(product);
    });

    it(`poll pipeline for ${product} should not send hoothoot data for reg_session`, (done) => {
      const x = sinon.spy(railsPipelineHelper, 'pushToHoothoot');

      const responseParams = {
        needToQueue: true,
        maxRequestCount: 2,
        trackRequestSetSize: 10,
      };

      const pushDataTmp = pushData(20); // assign a random value here

      // pollPipeline should always be tested with a callback method
      // without cb we will be facing race conditions while testing
      const cb = () => {
        assert(x.calledOnce === false);
        assert(pushDataTmp.railsPipelineExtraData.isQueued === false);
        railsPipelineHelper.pushToHoothoot.restore();
        railsPipelineHelper.registerRailsRequest.restore();
        railsPipelineHelper.requestQueue[product].length = 0;
        railsPipelineHelper.enqueueRequest.restore();
        done();
      };

      pushDataTmp.isStartRequest = false;
      pushDataTmp.isStopRequest = false;
      pushDataTmp.resolveRequest = cb;
      railsPipelineHelper.requestQueue[product].push(pushDataTmp);

      sinon.stub(railsPipelineHelper, 'registerRailsRequest').returns(Promise.resolve(responseParams));
      sinon.stub(railsPipelineHelper, 'enqueueRequest').returns(Promise.resolve(true));

      railsPipelineHelper.pollPipeline(product);
    });

    it(`poll pipeline for ${product} should not send hoothoot data when selauth_request_ever_queued is true`, (done) => {
      const x = sinon.spy(railsPipelineHelper, 'pushToHoothoot');

      const responseParams = {
        needToQueue: true,
        maxRequestCount: 2,
        trackRequestSetSize: 10,
      };

      const pushDataTmp = pushData(30); // assign a random value here

      // pollPipeline should always be tested with a callback method
      // without cb we will be facing race conditions while testing
      const cb = () => {
        assert(x.calledOnce === false);
        assert(pushDataTmp.railsPipelineExtraData.isQueued === true);
        railsPipelineHelper.pushToHoothoot.restore();
        railsPipelineHelper.registerRailsRequest.restore();
        railsPipelineHelper.requestQueue[product].length = 0;
        railsPipelineHelper.enqueueRequest.restore();
        done();
      };
      pushDataTmp.railsPipelineExtraData.isQueued = true;
      pushDataTmp.isStartRequest = true;
      pushDataTmp.isStopRequest = false;
      pushDataTmp.resolveRequest = cb;
      railsPipelineHelper.requestQueue[product].push(pushDataTmp);

      sinon.stub(railsPipelineHelper, 'registerRailsRequest').returns(Promise.resolve(responseParams));
      sinon.stub(railsPipelineHelper, 'enqueueRequest').returns(Promise.resolve(true));

      railsPipelineHelper.pollPipeline(product);
    });
  });
});

['automate:desktop', 'automate:mobile', 'app-automate:all'].forEach((productPlatform) => {
  describe('#pushToHoothoot', () => {
    it(`should call unique user event for ${productPlatform}`, (done) => {
      const x = sinon.spy(railsPipelineHelper, 'uniqueUserEvent');
      const product = productPlatform.split(':')[0];
      const isAppAutomateSession = product === 'app-automate';
      const outputPlatform = productPlatform.split(':')[1];
      const isMobile = outputPlatform === 'mobile';
      const username = 'user';
      railsPipelineHelper.pushToHoothoot(isAppAutomateSession, isMobile, username);
      assert(x.calledWith(username, product, 'current_queue_count', outputPlatform));
      railsPipelineHelper.uniqueUserEvent.restore();
      done();
    });
  });
});

describe('#sendToEDS from railsPipelineHelper', () => {
  it('should call send to eds', (done) => {
    const x = sinon.stub(hubHelper, 'sendToEDS');
    RailsPipelineHelper.sendToEDS('error-code', {
      kind: 'automate_error_data',
      requestReceivedAt: new Date(),
      request_id: 'request-id',
    });
    assert(x.calledOnce === true);
    hubHelper.sendToEDS.restore();
    done();
  });
});
