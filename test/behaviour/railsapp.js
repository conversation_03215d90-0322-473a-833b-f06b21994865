var helper = require('./helper');
const browserstack = require("../../browserstack");

const sinon = require('sinon');

describe('POST BrowserStack', function() {
  describe('Hub behaviour depending on railsApp', function() {
    beforeEach((done) => {
      helper.hubHelper.redisClient.flushall()
        .then(() => {
          done();
        });
    });
    afterEach((done) => {
      helper.hubHelper.redisClient.flushall()
        .then(() => {
          done();
        });
    });
    describe('no retry on response', () => {
      [500, 406].forEach((errorCode) => {
        it(`${errorCode} response from rails causes no retries`, function(done) {
          var requestBody = helper.getCapabilities();
          const browserstackPostBrowserStack = sinon.spy(browserstack, 'postBrowserStack');

          var nthRequest = 0;
          var scope = helper.buildRails({
            persist: true,
            action: function(uri, body) {
              nthRequest++;
              return [errorCode, 'Some random error'];
            }
          });

          helper.request.post('/wd/hub/session')
          .auth('test', 'test')
          .send(requestBody)
          .end(function(err, res) {
            scope.isDone().should.be.true;
            nthRequest.should.equal(1);
            res.status.should.equal(200);
            res.body.value.message.should.equal('No response from BrowserStack!!');
            res.body.sessionId.should.equal('');
            res.body.status.should.equal(13);
            browserstackPostBrowserStack.callCount.should.equal(1)
            browserstackPostBrowserStack.restore();
            done();
          });
        });

        it(`${errorCode} response from rails for stop request should not give error to user`, function(done) {
          this.timeout(6000);
          var capability = helper.getCapabilities({browser: 'Firefox'});
          var stopReceived = false;
          var railsScope;
          helper.doSelenium.startSession(capability)
          .then(() => helper.doSelenium.sendCommand({
            method: 'POST',
            uri: '/wd/hub/session/' + helper.SESSION_ID + '/url',
            action: function(uri, body) {
              body = JSON.parse(body);
              body.url.should.equal('http://www.google.com');
              return helper.sessionStartedJSON();
            },
            body: {
              url: 'http://www.google.com'
            }
          }))
          .then(function(res) {
            res.status.should.equal(200);
            res.body.sessionId.should.equal('1a2b3c4d5e6f');
            res.body.value.should.deep.equal({});
            res.body.status.should.equal(0);
          })
          .then(() => {
            railsScope = helper.buildRails({
              query: true,
              persist: true,
              action: function(uri, body) {
                var options = helper.getParamsFromQuery(uri);
                if (options.stop == 'true')
                  stopReceived = true;
                return [errorCode, {done: true}];
              }
            });
            return helper.doSelenium.sendCommand({
              method: 'DELETE',
              uri: '/wd/hub/session/' + helper.SESSION_ID,
              action: [200, {}]
            });
          })
          .then(function(res) {
            setTimeout(() => {
              railsScope.isDone().should.be.true;
              stopReceived.should.be.true;
              res.body.status.should.equal(0);
              res.body.state.should.equal('success');
              done();
            }, 200);
          });
        });
      });
    });


    it('retry on non 200 response from rails', function(done) {
      var requestBody = helper.getCapabilities();
      const browserstackPostBrowserStack = sinon.spy(browserstack, 'postBrowserStack');

      var nthRequest = 0;
      var scope = helper.buildRails({
        persist: true,
        action: function(uri, body) {
          nthRequest++;
          return [202, 'Some random error'];
        }
      });

      helper.request.post('/wd/hub/session')
      .auth('test', 'test')
      .send(requestBody)
      .end(function(err, res) {
        scope.isDone().should.be.true;
        nthRequest.should.equal(6);
        res.status.should.equal(200);
        res.body.value.message.should.equal('Not able to reach Browserstack.com! Please try again!');
        res.body.sessionId.should.equal('');
        res.body.status.should.equal(13);
        browserstackPostBrowserStack.callCount.should.equal(6)
        browserstackPostBrowserStack.restore();
        done();
      });
    });

    describe('late retries on certain responses', () => {
      [503, 404, 504].forEach((errorCode) => {
        it(`retry on ${errorCode} response from rails`, function(done) {
          var requestBody = helper.getCapabilities();

          const browserstackPostBrowserStack = sinon.spy(browserstack, 'postBrowserStack');

          var nthRequest = 0;
          var scope = helper.buildRails({
            persist: true,
            action: function(uri, body) {
              nthRequest++;
              return [errorCode, 'Some random error'];
            }
          });

          helper.request.post('/wd/hub/session')
          .auth('test', 'test')
          .send(requestBody)
          .end(function(err, res) {
            scope.isDone().should.be.true;
            nthRequest.should.equal(5);
            res.status.should.equal(200);
            res.body.value.message.should.equal('Not able to reach Browserstack.com! Please try again!');
            res.body.sessionId.should.equal('');
            res.body.status.should.equal(13);
            browserstackPostBrowserStack.callCount.should.equal(5)
            browserstackPostBrowserStack.restore();
            done();
          });
        });
      });
    });

      it('session close on error response from rails', function(done) {
        var requestBody = helper.getCapabilities(),
        scope = helper.buildRails({action: [200, {error: 'Session will quit'}]});

        helper.request.post('/wd/hub/session')
      .auth('test', 'test')
      .send(requestBody)
      .end(function(err, res) {
        scope.isDone().should.be.true;
        res.status.should.equal(200);
        res.body.value.message.should.equal('Session will quit');
        res.body.sessionId.should.equal('');
        res.body.status.should.equal(13);
        done();
      });
    });

    it('sessions list without auth gives error', function(done) {
      helper.request.get('/wd/hub/sessions')
      .end(function(err, res) {
        res.status.should.equal(401);
        res.text.should.equal('');
        done();
      });
    });

    it('sessions with wrong auth gives 401', function(done) {
      var requestBody = helper.getCapabilities(),
          scope = helper.buildRails({action: [200, {error: 'Invalid username or password'}]});

      helper.request.post('/wd/hub/session')
      .auth('abcd', 'pqrs')
      .send(requestBody)
      .end(function(err, res) {
        scope.isDone().should.be.true;
        res.status.should.equal(401);
        res.body.value.message.should.equal('Invalid username or password');
        res.body.sessionId.should.equal('');
        res.body.status.should.equal(13);
        done();
      });
    });

    describe("RailsDefender", function() {
      beforeEach(function(done) {
        // Delete bad-auth cache key
        helper.hubHelper.redisClient.del('temp_blocked_user_auth:abcd_vUCIKWryTSmslbP2J07xHJo3kug=_', function(err, res) {
          helper.hubHelper.redisClient.del('temp_blocked_user_expired_plan:abcd__', function(err, res) {
            helper.hubHelper.redisClient.del('temp_blocked_user_expired_testing_time:abcd__', function(err, res) {
              helper.hubHelper.redisClient.del('temp_blocked_user_binary_disconnected:abcd__', function (err, res) {
                helper.hubHelper.redisClient.del('temp_blocked_user_generic:abcd_vUCIKWryTSmslbP2J07xHJo3kug=_ie+84z8TK/njZ8GEzXjz/WHSpko=', function (err, res) {
                  done();
                });
              });
            });
          });
        });
      });

      it('sessions with expired plan should set the RailsDefender cache key for expired_plan', function(done) {
        var requestBody = helper.getCapabilities(),
            scope = helper.buildRails({action: [200, {error: 'Your Automate plan has expired'}]});

        helper.request.post('/wd/hub/session')
        .auth('abcd', 'pqrs')
        .send(requestBody)
        .end(function(err, res) {
          scope.isDone().should.be.true;
          res.status.should.equal(200);
          res.body.value.message.should.equal('Your Automate plan has expired');
          helper.hubHelper.redisClient.get('temp_blocked_user_expired_plan:abcd__', function(err, res) {
            res.should.equal('true');
          });
          res.body.sessionId.should.equal('');
          res.body.status.should.equal(13);
          done();
        });
      });


      it('sessions with selenium error should set the RailsDefender cache key for expired_plan', function(done) {
        const response = {
          error: 'selenium jar version is either invalid, wrongly formatted, or not supported. Please refer to http://bit.ly/automate-capabilities',
          automate_error_data: {
            user_id: 2,
            error_message: 'selenium jar version is either invalid, wrongly formatted, or not supported. Please refer to http://bit.ly/automate-capabilities',
            error_code: null,
            error_code_str: 'invalid_desktop_caps',
            request_id: 'xyz',
            raw_capabilities: {}
          },
        }
        helper.constants.ENABLE_GENERIC_CACHING = true;
        var requestBody = helper.getCapabilities(),
            scope = helper.buildRails({action: [200, response]});
        requestBody.desiredCapabilities['browserstack.selenium_version'] = '10.100.2';

        helper.request.post('/wd/hub/session')
        .auth('abcd', 'pqrs')
        .send(requestBody)
        .end(function(err, res) {
          // console.log("here 1", res.body.value.message, response.error)
          scope.isDone().should.be.true;
          res.status.should.equal(200);
          res.body.value.message.should.equal(response.error);
          helper.hubHelper.redisClient.get('temp_blocked_user_generic:abcd_vUCIKWryTSmslbP2J07xHJo3kug=_ie+84z8TK/njZ8GEzXjz/WHSpko=', function(err, res) {
            // console.log('redis ', res)
            res.should.equal(JSON.stringify(response));
          });
          res.body.sessionId.should.equal('');
          res.body.status.should.equal(13);
          helper.constants.ENABLE_GENERIC_CACHING = false;
          done();
        });
      });


      it('sessions with expired testing time should set the RailsDefender cache key for expired_testing_time', function(done) {
        var requestBody = helper.getCapabilities(),
            scope = helper.buildRails({action: [200, {error: 'Automate testing time expired.'}]});

        helper.request.post('/wd/hub/session')
        .auth('abcd', 'pqrs')
        .send(requestBody)
        .end(function(err, res) {
          scope.isDone().should.be.true;
          res.status.should.equal(200);
          res.body.value.message.should.equal('Automate testing time expired.');
          helper.hubHelper.redisClient.get('temp_blocked_user_expired_testing_time:abcd__', function(err, res) {
            res.should.equal('true');
          });
          res.body.sessionId.should.equal('');
          res.body.status.should.equal(13);
          done();
        });
      });

      it('sessions with wrong auth should set the RailsDefender cache key for bad-auth', function(done) {
        var requestBody = helper.getCapabilities(),
            scope = helper.buildRails({action: [200, {error: 'Invalid username or password'}]});

        helper.request.post('/wd/hub/session')
        .auth('abcd', 'pqrs')
        .send(requestBody)
        .end(function(err, res) {
          scope.isDone().should.be.true;
          res.status.should.equal(401);
          res.body.value.message.should.equal('Invalid username or password');
          helper.hubHelper.redisClient.get('temp_blocked_user_auth:abcd_vUCIKWryTSmslbP2J07xHJo3kug=_', function(err, res) {
            res.should.equal('true');
          });
          res.body.sessionId.should.equal('');
          res.body.status.should.equal(13);
          done();
        });
      });

      it('consecutive request with bad-auth should hit rails only the first time', function(done) {
        var requestBody = helper.getCapabilities(),
            scope = helper.buildRails({action: [200, {error: 'Invalid username or password'}]}).persist();

        // First request with bad auth
        helper.request.post('/wd/hub/session')
        .auth('abcd', 'pqrs')
        .send(requestBody)
        .end(function(err, res) {
          // Should send to Rails.
          scope.isDone().should.be.true;

          //Reset Rails nock.
          scope = helper.buildRails({action: [200, {error: 'Invalid username or password'}]}).persist();

          res.status.should.equal(401);
          res.body.value.message.should.equal('Invalid username or password');
          helper.hubHelper.redisClient.get('temp_blocked_user_auth:abcd_vUCIKWryTSmslbP2J07xHJo3kug=_', function(err, res) {
            res.should.equal('true');
          });
          res.body.sessionId.should.equal('');
          res.body.status.should.equal(13);

          // Second request with bad auth
          const startTime = new Date();
          helper.request.post('/wd/hub/session')
          .auth('abcd', 'pqrs')
          .send(requestBody)
          .end(function(err, res) {
            // Should NOT be sent Rails.
            scope.isDone().should.be.false;

            res.status.should.equal(401);
            res.body.value.message.should.equal('Invalid username or password');
            helper.hubHelper.redisClient.get('temp_blocked_user_auth:abcd_vUCIKWryTSmslbP2J07xHJo3kug=_', function(err, res) {
              res.should.equal('true');
            });

            // Should have a non-negative TTL
            helper.hubHelper.redisClient.ttl('temp_blocked_user_auth:abcd_vUCIKWryTSmslbP2J07xHJo3kug=_', function(err, res) {
              res.should.be.above(1);
            });
            res.body.sessionId.should.equal('');
            res.body.status.should.equal(13);
            // delayed response for second request
            (new Date() - startTime > 1500).should.be.true
            done();
          });
        });
      });

      it('consecutive request with expired plan should hit rails only the first time', function(done) {
        var requestBody = helper.getCapabilities(),
            scope = helper.buildRails({action: [200, {error: 'Your Automate plan has expired'}]});

        // First request to Rails
        helper.request.post('/wd/hub/session')
        .auth('abcd', 'pqrs')
        .send(requestBody)
        .end(function(err, res) {
          // Should be sent to Rails
          scope.isDone().should.be.true;
          // Reset Rails nock.
          scope = helper.buildRails({action: [200, {error: 'Your Automate plan has expired'}]});

          res.status.should.equal(200);
          res.body.value.message.should.equal('Your Automate plan has expired');
          helper.hubHelper.redisClient.get('temp_blocked_user_expired_plan:abcd__', function(err, res) {
            res.should.equal('true');
          });
          res.body.sessionId.should.equal('');
          res.body.status.should.equal(13);

          const startTime = new Date();
          // Second request to Rails
          helper.request.post('/wd/hub/session')
          .auth('abcd', 'pqrs')
          .send(requestBody)
          .end(function(err, res) {
            // Should NOT be sent to Rails
            scope.isDone().should.be.false;
            res.status.should.equal(200);
            res.body.value.message.should.equal('Your Automate plan has expired');
            helper.hubHelper.redisClient.get('temp_blocked_user_expired_plan:abcd__', function(err, res) {
              res.should.equal('true');
            });

            // Should have a non-negative TTL
            helper.hubHelper.redisClient.ttl('temp_blocked_user_expired_plan:abcd__', function(err, res) {
              res.should.be.above(1);
            });

            res.body.sessionId.should.equal('');
            res.body.status.should.equal(13);
            // delayed response for second request
            (new Date() - startTime > 1500).should.be.true
            done();
          });
        });
      });

      it('consecutive request with selenium error should hit rails only the first time', function(done) {
        const response = {
          error: 'selenium jar version is either invalid, wrongly formatted, or not supported. Please refer to http://bit.ly/automate-capabilities',
          automate_error_data: {
            user_id: 2,
            error_message: 'selenium jar version is either invalid, wrongly formatted, or not supported. Please refer to http://bit.ly/automate-capabilities',
            error_code: null,
            error_code_str: 'invalid_desktop_caps',
            request_id: 'xyz',
            raw_capabilities: {}
          },
        }
        helper.constants.ENABLE_GENERIC_CACHING = true;
        var requestBody = helper.getCapabilities(),
            scope = helper.buildRails({action: [200, response]});
        requestBody.desiredCapabilities['browserstack.selenium_version'] = '10.100.2';

        // First request to Rails
        helper.request.post('/wd/hub/session')
        .auth('abcd', 'pqrs')
        .send(requestBody)
        .end(function(err, res) {
          // console.log("res",res)
          // Should be sent to Rails
          scope.isDone().should.be.true;
          // Reset Rails nock.
          scope = helper.buildRails({action: [200, response]});

          res.status.should.equal(200);
          res.body.value.message.should.equal(response.error);
          helper.hubHelper.redisClient.get('temp_blocked_user_generic:abcd_vUCIKWryTSmslbP2J07xHJo3kug=_ie+84z8TK/njZ8GEzXjz/WHSpko=', function(err, res) {
            // console.log(res)
            res.should.equal(JSON.stringify(response));
          });
          res.body.sessionId.should.equal('');
          res.body.status.should.equal(13);

          const startTime = new Date();
          // Second request to Rails
          helper.request.post('/wd/hub/session')
          .auth('abcd', 'pqrs')
          .send(requestBody)
          .end(function(err, res) {
            // Should NOT be sent to Rails
            scope.isDone().should.be.false;
            res.status.should.equal(200);
            res.body.value.message.should.equal(response.error);
            helper.hubHelper.redisClient.get('temp_blocked_user_generic:abcd_vUCIKWryTSmslbP2J07xHJo3kug=_ie+84z8TK/njZ8GEzXjz/WHSpko=', function(err, res) {
              res.should.equal(JSON.stringify(response));
            });

            // Should have a non-negative TTL
            helper.hubHelper.redisClient.ttl('temp_blocked_user_generic:abcd_vUCIKWryTSmslbP2J07xHJo3kug=_ie+84z8TK/njZ8GEzXjz/WHSpko=', function(err, res) {
              res.should.be.above(1);
            });

            res.body.sessionId.should.equal('');
            res.body.status.should.equal(13);
            // delayed response for second request
            (new Date() - startTime > 1500).should.be.true
            helper.constants.ENABLE_GENERIC_CACHING = false;
            done();
          });
        });
      });

      it('consecutive request with expired testing time should hit rails only the first time', function(done) {
        var requestBody = helper.getCapabilities(),
            scope = helper.buildRails({action: [200, {error: 'Automate testing time expired.'}]});

        // First request to Rails
        helper.request.post('/wd/hub/session')
        .auth('abcd', 'pqrs')
        .send(requestBody)
        .end(function(err, res) {
          // Should be sent to Rails
          scope.isDone().should.be.true;
          // Reset Rails nock.
          scope = helper.buildRails({action: [200, {error: 'Automate testing time expired.'}]});

          res.status.should.equal(200);
          res.body.value.message.should.equal('Automate testing time expired.');
          helper.hubHelper.redisClient.get('temp_blocked_user_expired_testing_time:abcd__', function(err, res) {
            res.should.equal('true');
          });
          res.body.sessionId.should.equal('');
          res.body.status.should.equal(13);

          const startTime = new Date();
          // Second request to Rails
          helper.request.post('/wd/hub/session')
          .auth('abcd', 'pqrs')
          .send(requestBody)
          .end(function(err, res) {
            // Should NOT be sent to Rails
            scope.isDone().should.be.false;
            res.status.should.equal(200);
            res.body.value.message.should.equal('Automate testing time expired.');
            helper.hubHelper.redisClient.get('temp_blocked_user_expired_testing_time:abcd__', function(err, res) {
              res.should.equal('true');
            });

            // Should have a non-negative TTL
            helper.hubHelper.redisClient.ttl('temp_blocked_user_expired_testing_time:abcd__', function(err, res) {
              res.should.be.above(1);
            });

            res.body.sessionId.should.equal('');
            res.body.status.should.equal(13);
            // delayed response for second request
            (new Date() - startTime > 1500).should.be.true
            done();
          });
        });
      });

      it('consecutive request with binary disconnected should hit rails only the first time', function (done) {
        var requestBody = helper.getCapabilities(),
          scope = helper.buildRails({ action: [200, { error: helper.constants.railsResponses.binary_disconnected }] });

        // First request to Rails
        helper.request.post('/wd/hub/session')
          .auth('abcd', 'pqrs')
          .send(requestBody)
          .end(function (err, res) {
            // Should be sent to Rails
            scope.isDone().should.be.true;
            // Reset Rails nock.
            scope = helper.buildRails({ action: [200, { error: helper.constants.railsResponses.binary_disconnected }] });

            res.status.should.equal(200);
            res.body.value.message.should.equal(helper.constants.railsResponses.binary_disconnected);
            helper.hubHelper.redisClient.get('temp_blocked_user_binary_disconnected:abcd__', function (err, res) {
              res.should.equal('true');
            });
            res.body.sessionId.should.equal('');
            res.body.status.should.equal(13);

            const startTime = new Date();
            // Second request to Rails
            requestBody.desiredCapabilities['browserstack.local'] = true;
            helper.request.post('/wd/hub/session')
              .auth('abcd', 'pqrs')
              .send(requestBody)
              .end(function (err, res) {
                // Should NOT be sent to Rails
                scope.isDone().should.be.false;
                res.status.should.equal(200);
                res.body.value.message.should.equal(helper.constants.railsResponses.binary_disconnected);
                helper.hubHelper.redisClient.get('temp_blocked_user_binary_disconnected:abcd__', function (err, res) {
                  res.should.equal('true');
                });

                // Should have a non-negative TTL
                helper.hubHelper.redisClient.ttl('temp_blocked_user_binary_disconnected:abcd__', function (err, res) {
                  res.should.be.above(1);
                });

                res.body.sessionId.should.equal('');
                res.body.status.should.equal(13);
                // delayed response for second request
                (new Date() - startTime > 1500).should.be.true
                done();
              });
          });
      });

      it('should not check binary disconnected if local is false on consecutive request', function (done) {
        var requestBody = helper.getCapabilities(),
          scope = helper.buildRails({ action: [200, { error: helper.constants.railsResponses.binary_disconnected }] });

        // First request to Rails
        helper.request.post('/wd/hub/session')
          .auth('abcd', 'pqrs')
          .send(requestBody)
          .end(function (err, res) {
            // Should be sent to Rails
            scope.isDone().should.be.true;
            // Reset Rails nock.
            scope = helper.buildRails({ action: [200, { error: helper.constants.railsResponses.binary_disconnected }] });

            res.status.should.equal(200);
            res.body.value.message.should.equal(helper.constants.railsResponses.binary_disconnected);
            helper.hubHelper.redisClient.get('temp_blocked_user_binary_disconnected:abcd__', function (err, res) {
              res.should.equal('true');
            });
            res.body.sessionId.should.equal('');
            res.body.status.should.equal(13);

            const startTime = new Date();
            // Second request to Rails
            helper.request.post('/wd/hub/session')
              .auth('abcd', 'pqrs')
              .send(requestBody)
              .end(function (err, res) {
                // Should send request to Rails as local is not set to true
                scope.isDone().should.be.true;
                res.status.should.equal(200);

                // Should have a non-negative TTL
                helper.hubHelper.redisClient.ttl('temp_blocked_user_binary_disconnected:abcd__', function (err, res) {
                  res.should.be.above(1);
                });

                res.body.sessionId.should.equal('');
                res.body.status.should.equal(13);
                done();
              });
          });
      });
    });

    it('sessions list', function(done) {
      var nthRequest = 0;
      var scope = helper.buildRails({
        query: {
            auth: 'selautomate',
            get_user_sessions_list: true,
            user_agent: 'node-superagent',
            user_id: 'test',
            password: 'test'
          },
        action: function(uri, body) {
          nthRequest++;
          return [200, {'sessions_list': ['1234', 'abcd']}];
        }
      });
      scope.filteringPath(/user_agent=[^&]*/g, 'user_agent=node-superagent');

      helper.request.get('/wd/hub/sessions')
      .auth('test', 'test')
      .end(function(err, res) {
        scope.isDone().should.be.true;
        nthRequest.should.equal(1);
        res.status.should.equal(200);
        res.body.value[0].should.equal('1234');
        res.body.value[1].should.equal('abcd');
        res.body.status.should.equal(0);
        done();
      });
    });

    // test will be fixed as a part of APS-4780
    xit('retry on timeout', function(done) {
      // TODO: (BUG) After 2nd retry the hub has another timeout before throwing error
      var requestBody = helper.getCapabilities();

      var nthRequest = 0;
      var scope = helper.buildRails({
        persist: true,
        delay: 120000,
        action: function(uri, body) {
          nthRequest++;
          return [200, {session_id: 'lol'}];
        }
      });

      helper.request.post('/wd/hub/session')
      .auth('test', 'test')
      .send(requestBody)
      .end(function(err, res) {
        scope.isDone().should.be.true;
        nthRequest.should.equal(6);
        res.status.should.equal(200);
        res.body.value.message.should.equal('Not able to reach Browserstack.com! Please try again!');
        res.body.sessionId.should.equal('');
        res.body.status.should.equal(13);
        done();
      });
    });

    describe('retries on error', function() {
      setTimeout(() => {
        xit('retry on error', function(done) {
          var requestBody = helper.getCapabilities(),
              scope1 = helper.buildRails({error: true});

          var scope2;

          // TODO: There should be a better way to test this. :(
          setTimeout(function() {
            scope1.isDone().should.be.true;
            scope2 = helper.buildRails({error: true});
            scope2.isDone().should.be.false;
          }, 50);

          helper.request.post('/wd/hub/session')
          .auth('test', 'test')
          .send(requestBody)
          .end(function(err, res) {
            scope2.isDone().should.be.true;
            res.status.should.equal(200);
            res.body.value.message.should.equal('Not able to reach Browserstack.com! Please try again!');
            res.body.sessionId.should.equal('');
            res.body.status.should.equal(13);
            done();
          });
        });
      }, 30);
    });
  });

  describe('Queuing', function() {
    it('retries POST to BrowserStack after a delay in case of queue response', function(done) {
      var requestBody = helper.getCapabilities(),
          scope1 = helper.buildRails({action: [200, {reason: 'nta', queue: true, error: 'NTA', automate_error_data: { user_id: 2, error_code_str: 'random', raw_capabilities: {} }}]});
          macTerminalScope = helper.buildMac({action: [200, { done: true }]});

      var seleniumScope = helper.selenium()
        .post('/wd/hub/session')
        .query(true)
        .reply(function(uri, body) {
          return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
        });

      var scope2;

      // TODO: There should be a better way to test this. :(
      setTimeout(function() {
        scope1.isDone().should.be.true;
        scope2 = helper.buildRails();
        scope2.isDone().should.be.false;
      }, helper.constants.QUEUE_REQUEST_DELAY);

      helper.request.post('/wd/hub/session')
      .auth('test', 'test')
      .send(requestBody)
      .end(function(err, res) {
        scope2.isDone().should.be.true;
        macTerminalScope.isDone().should.be.true;
        seleniumScope.isDone().should.be.true;
        res.status.should.equal(200);
        res.body.sessionId.should.equal(helper.SESSION_ID);
        res.body.status.should.equal(0);
        res.status.should.equal(200);
        done();
      });
    });
    it('does not retry POST to BrowserStack after default number of times', function(done) {
      this.timeout(50000);
      var requestBody = helper.getCapabilities(),
          max_retries = 15,
          nthRequest = 0,
          scope = helper.buildRails({
            persist: true,
            action: function(uri, body) {
              nthRequest++;
              return [200, {reason: 'nta', queue: true, error: 'NTA', automate_error_data: { user_id: 2, error_code_str: 'random', raw_capabilities: {} }}];
            }
          });

      helper.request.post('/wd/hub/session')
      .auth('test', 'test')
      .send(requestBody)
      .end(function(err, res) {
        scope.isDone().should.be.true;
        nthRequest.should.equal(max_retries + 1);
        res.status.should.equal(200);
        res.body.value.message.should.equal('NTA');
        res.body.sessionId.should.equal('');
        res.body.status.should.equal(13);
        done();
      });
    });
    it('does not retry POST to BrowserStack after default number of times for App Automate', function(done) {
      this.timeout(50000);
      var requestBody = helper.AppAutomateCapabilities(),
          max_retries = 15,
          nthRequest = 0,
          scope = helper.buildRails({
            persist: true,
            isAppAutomate: true,
            action: function(uri, body) {
              nthRequest++;
              return [200, {reason: 'nta', queue: true, error: 'NTA', automate_error_data: { user_id: 2, error_code_str: 'random', raw_capabilities: {} }}];
            }
          });

      helper.request.post('/wd/hub/session')
      .auth('test', 'test')
      .send(requestBody)
      .end(function(err, res) {
        scope.isDone().should.be.true;
        nthRequest.should.equal(max_retries + 1);
        res.status.should.equal(200);
        res.body.value.message.should.equal('NTA');
        res.body.sessionId.should.equal('');
        res.body.status.should.equal(13);
        done();
      });
    });
    it('does not retry POST to BrowserStack exceeding the tries in browserstack.queue.retries capability', function(done) {
      var max_retries = 3,
          requestBody = helper.getCapabilities({'browserstack.queue.retries': max_retries}),
          nthRequest = 0,
          scope = helper.buildRails({
            persist: true,
            action: function(uri, body) {
              nthRequest++;
              return [200, {reason: 'nta', queue: true, error: 'NTA', automate_error_data: { user_id: 2, error_code_str: 'random', raw_capabilities: {} }}];
            }
          });

      helper.request.post('/wd/hub/session')
      .auth('test', 'test')
      .send(requestBody)
      .end(function(err, res) {
        scope.isDone().should.be.true;
        nthRequest.should.equal(max_retries + 1);
        res.status.should.equal(200);
        res.body.value.message.should.equal('NTA');
        res.body.sessionId.should.equal('');
        res.body.status.should.equal(13);
        done();
      });
    });

    it('does not retry POST to BrowserStack if queue is false and queue_size_exceeded is returned true for App Automate', function(done) {
      const sendToEDS = sinon.stub(helper.hubHelper,'sendToEDS');
      var requestBody = helper.AppAutomateCapabilities(),
          scope = helper.buildRails({
            persist: true,
            isAppAutomate: true,
            action: function(uri, body) {
              return [200, { reason: 'nta', queue: false, error: 'NTA', queue_size_exceeded: true, automate_error_data: { user_id: 2, error_code_str: 'random', raw_capabilities: {}  } }];
            }
          });

      helper.request.post('/wd/hub/session')
      .auth('test', 'test')
      .send(requestBody)
      .end(function(err, res) {
        res.status.should.equal(200);
        res.body.value.message.should.equal(helper.constants.QUEUE_SIZE_EXCEEDED.errorMessage);
        res.body.sessionId.should.equal('');
        res.body.status.should.equal(13);
        sendToEDS.calledOnce.should.equal(true);
        helper.hubHelper.sendToEDS.restore();
        done();
      });
    });
  });
});
