const assert = require('assert');
const uuid = require('uuid');
const helper = require('./helper');
const sinon = require('sinon');

describe('test semaphore functionality', function () {

  const random_string = uuid();

  afterEach((done) => {
    helper.clearSemaphore(`test_lock_${random_string}`).then(() => {
      done();
    });
  });

  it('tests clear semaphore by session id functionality', (done) => {
    helper.ttlSemaphore(`test_lock_${random_string}`, random_string, 0).then((resp) => {
      helper.clearSeamphoreBySessionId(`test_lock_${random_string}`, random_string).then(() => {
        helper.ttlSemaphore(`test_lock_${random_string}`, random_string, 0).then((response) => {
          done();
        }).catch((error) => { throw error });
      }).catch((error) => { throw error })
    }).catch((error) => { throw error });
  });

  it('tests semaphore functionality', (done) => {
    helper.ttlSemaphore(`test_lock_${random_string}`, random_string, 0).then((resp) => {
      helper.ttlSemaphore(`test_lock_${random_string}`, random_string, 0).then(() => {
        throw new assert.AssertionError({ message: "Lock failed to reject." });
      }).catch((err) => {
        done();
      });
    }).catch((error) => { throw error });
  });

  it('tests if semaphore is cleared after timeout', (done) => {
    helper.ttlSemaphore(`test_lock_${random_string}`, random_string, 0).then((resp) => {
      helper.clearSemaphore(`test_lock_${random_string}`).then(() => {
        helper.ttlSemaphore(`test_lock_${random_string}`, random_string, 0).then((response) => {
          done();
        }).catch((error) => { throw error });
      }).catch((error) => { throw error; });
    }).catch((error) => {throw error});
  });

  it('tests if semaphore is not cleared before timeout', (done) => {
    helper.ttlSemaphore(`test_lock_${random_string}`, random_string, ttl = 1000000).then((resp) => {
      helper.clearSemaphore(`test_lock_${random_string}`).then(() => {
        helper.ttlSemaphore(`test_lock_${random_string}`, random_string, 0).then((response) => {
          throw new assert.AssertionError({ message: "Lock failed to be acquired." });
        }).catch((resp) => {
          done();
        })
      }).catch((error) => { throw error });
    }).catch((error) => { throw error });
  });

  it('tests if semaphore is applied on the key', (done) => {
    helper.ttlSemaphore(`test_lock_${random_string}`, random_string, 1000).then((resp) => {
      helper.checkSemaphore(`test_lock_${random_string}`, random_string).then((response) => {
        const now = Date.now() + 1000000;
        sinon.stub(Date, 'now').returns(now);
        helper.clearSemaphore(`test_lock_${random_string}`).then((response1) => {
          helper.checkSemaphore(`test_lock_${random_string}`, random_string).then(() => {
            Date.now.restore();
            throw new assert.AssertionError({ message: "Lock was acquired." });
          }).catch((resp) => {
            Date.now.restore();
            done();
          })
        }).catch((error) => { throw error });
      }).catch((error) => { throw error });
    }).catch((error) => { throw error });
  });
});
