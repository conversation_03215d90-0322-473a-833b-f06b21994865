'use strict';

const helper = require('./helper');
const sinon = require('sinon');
const helperFn = require('../../helper');
const requestlib = require('../../lib/request');
const assert = require('chai').assert;
const expect = require('chai').expect;

function bootstrapMacSession() {
  const requestBody = helper.getCapabilities();
  const railsScope = helper.buildRails();
  const macTerminalScope = helper.buildMac({
    action(options) {
      options.chromeDriverVersion.should.equal('2.20');
      options.terminal_ip.should.equal(helper.TERMINAL_IP);
      return [200, { done: true }];
    },
  });
  return { requestBody, railsScope, macTerminalScope };
}

describe('Fire Commands', () => {
  it('Add cookie capture flag in fire_cmd', (done) => {
    const { requestBody, railsScope, macTerminalScope } = bootstrapMacSession();

    const seleniumDriver = helper.selenium();

    const seleniumScope = seleniumDriver
      .post('/wd/hub/session')
      .query(true)
      .reply((_, body) => helper.sessionStartedJSON(JSON.parse(body).desiredCapabilities));

    requestBody.desiredCapabilities['browserstack.captureCookies'] = true;

    const encodedURLFn = sinon.spy(requestlib, 'getEncodedURLParams');

    helper.request.post('/wd/hub/session')
      .auth('test', 'test')
      .send(requestBody)
      .end((err, res) => {
        railsScope.isDone().should.equal(true);
        macTerminalScope.isDone().should.equal(true);
        seleniumScope.isDone().should.equal(true);
        res.status.should.equal(200);

        expect(encodedURLFn.calledOnce);
        const fireCommandArguments = encodedURLFn.getCall(0).args[0];
        assert(fireCommandArguments.capture_cookies, true);


        res.body.value.should.have.any.keys('browser', 'browser_version', 'os', 'os_version');
        res.body.sessionId.should.equal(helper.SESSION_ID);
        res.body.status.should.equal(0);

        expect(err === null);
        encodedURLFn.restore();
        done();
      });
  });

  it('Do not add cookie flag in fire_cmd if not present', (done) => {
    const { requestBody, railsScope, macTerminalScope } = bootstrapMacSession();

    const seleniumDriver = helper.selenium();

    const seleniumScope = seleniumDriver
      .post('/wd/hub/session')
      .query(true)
      .reply((_, body) => helper.sessionStartedJSON(JSON.parse(body).desiredCapabilities));

    const encodedURLFn = sinon.spy(requestlib, 'getEncodedURLParams');

    helper.request.post('/wd/hub/session')
      .auth('test', 'test')
      .send(requestBody)
      .end((err, res) => {
        railsScope.isDone().should.equal(true);
        macTerminalScope.isDone().should.equal(true);
        seleniumScope.isDone().should.equal(true);
        res.status.should.equal(200);

        expect(encodedURLFn.calledOnce);
        const fireCommandArguments = encodedURLFn.getCall(0).args[0];
        expect(fireCommandArguments).to.not.have.all.keys('capture_cookies');

        res.body.value.should.have.any.keys('browser', 'browser_version', 'os', 'os_version');
        res.body.sessionId.should.equal(helper.SESSION_ID);
        res.body.status.should.equal(0);

        expect(err === null);

        encodedURLFn.restore();
        done();
      });
  });
});
