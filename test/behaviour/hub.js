var helper = require('./helper');
const ha = require('./../../ha');
const { assert } = require('chai');
const sinon = require('sinon');
const redisClient = require('./../../redisUtils').redisClient;

describe('#get_session_data', () => {
    beforeEach((done) => {
        helper.clearSeamphoreBySessionId("session_stop_semaphore", helper.SESSION_ID).then(() => {
            done();
        });
    });

    it('should not get session data for invalid sessionId format', (done) => {
        const invalidSessionId = 'invalid-session-id';
        helper.request.get(`/session/get_session_data?sessionId=${invalidSessionId}`)
        .end(function(err, res) {
            assert.equal('{"found":false}', JSON.stringify(res.body));
            done();
        });
    });

    it('should not get session data if session has not been started', (done) => {
        helper.request.get(`/session/get_session_data?sessionId=${helper.SESSION_ID}`)
        .end(function(err, res) {
            assert.equal('{"found":false}', JSON.stringify(res.body));
            done();
        });
    });
      

    it('should get session data', (done) => {
        const capability = helper.getCapabilities({browser: 'Firefox', 'browserstack.video': false});
        helper.doSelenium.startSession(capability)
        .then(() => {
        helper.request.get(`/session/get_session_data?sessionId=${helper.SESSION_ID}`)
        .end(function(err, res) {
                assert.equal(res.body.name, helper.TERMINAL_IP);
                assert.equal(res.body.rproxyHost, helper.RPROXY_HOST);
                assert.equal(res.body.automation_session_id, helper.SESSION_ID);
                done();
            });
        });
    });

    it('should get session data if fetch for first cdp req', (done) => {
        const capability = helper.getCapabilities({browser: 'Firefox', 'browserstack.video': false});
        helper.doSelenium.startSession(capability)
        .then(() => {
        helper.request.get(`/session/get_session_data?sessionId=${helper.SESSION_ID}&cdp=true`)
        .end(function(err, res) {
              assert.equal(res.body.name, helper.TERMINAL_IP);
              assert.equal(res.body.rproxyHost, helper.RPROXY_HOST);
              assert.equal(res.body.automation_session_id, helper.SESSION_ID);
              done();
            });
        });
    });

    it('should not get session data', (done) => {
        helper.request.get(`/session/get_session_data?sessionId=${helper.SESSION_ID}`)
        .end(function(err, res) {
            assert.equal('{"found":false}', JSON.stringify(res.body));
            done();
        });
    });

    it('should not get session data', (done) => {
        helper.request.get('/session/get_session_data/')
        .end(function(err, res) {
            assert.equal('{"found":false}', JSON.stringify(res.body));
            done();
        });
    });

    it('should get session data', (done) => {
        const capability = helper.getCapabilities({browser: 'Firefox', 'browserstack.video': false});
        helper.doSelenium.startSession(capability)
        .then(() => {
        delete helper.constants.global_registry[helper.SESSION_ID]
        helper.request.get(`/session/get_session_data?sessionId=${helper.SESSION_ID}`)
        .end(function(err, res) {
                assert.equal('{"found":false}', JSON.stringify(res.body));
                done();
            });
        });

    });

});

describe('#handleRequest', () => {
    it('should call ha.getData if object not found locally', (done) => {
        const capability = helper.getCapabilities({browser: 'Firefox', 'browserstack.video': false});
        helper.doSelenium.startSession(capability).then(() => {
            redisClient.set(`HA_${helper.SESSION_ID}`, JSON.stringify(Object.assign({}, helper.constants.global_registry[helper.SESSION_ID])), () => {
                sinon.spy(ha, 'getData');
                delete helper.constants.global_registry[helper.SESSION_ID];
                helper.request.get(`/session/abc/xyz/${helper.SESSION_ID}/title`).end(function(err, res) {
                    ha.getData.called.should.be.true;
                    ha.getData.restore();
                    done();
                });
            });
        });
    });
    it('should call ha.getData if object not found locally forward if not in redis', (done) => {
        const capability = helper.getCapabilities({browser: 'Firefox', 'browserstack.video': false});
        helper.doSelenium.startSession(capability).then(() => {
            sinon.spy(ha, 'getData');
            delete helper.constants.global_registry[helper.SESSION_ID];
            helper.request.get(`/session/abc/xyz/${helper.SESSION_ID}/title`).end(function(err, res) {
                ha.getData.called.should.be.true;
                ha.getData.restore();
                done();
            });
        });
    });
});

describe('ignore connect requests', () => {
    it('should ignore connect requests', (done) => {
        helper.request.connect('').then(function(a,b) {
        });
        done();
    })
})

// Tests writen for passing headerParams as Object/String

describe('pass headerParams as Object', () => {
    it('should return success', function(done) {
        var capability = helper.getCapabilities({ 'browserstack.headerParams': { "Authorization": "Basic" } });
        helper.doSelenium.startSession(capability)
        .then(function(res) {
          res.status.should.equal(200);
          res.body.sessionId.should.equal(helper.SESSION_ID);
          res.body.status.should.equal(0);
          done();
        });
    });
});

describe('pass headerParams as String', () => {
    it('should return success', function(done) {
        var capability = helper.getCapabilities({ 'browserstack.headerParams': "{\"Authorization\": \"Basic\"}" });
        helper.doSelenium.startSession(capability)
        .then(function(res) {
          res.status.should.equal(200);
          res.body.sessionId.should.equal(helper.SESSION_ID);
          res.body.status.should.equal(0);
          done();
        });
    });
});

describe('pass headerParams as Object in bstack:options', () => {
    it('should return success', function(done) {
        var requestBody = helper.getCapabilities({ 'bstack:options': {
            "headerParams" : { 'Authorization': 'Basic' }
        }});
        requestBody["desiredCapabilities"]["browserstack.use_w3c"] = true;
        requestBody["capabilities"] = {"firstMatch": [{
            "bstack:options": {
                "useW3C": 'true',
                "headerParams" : "{\"Authorization\": \"Basic\"}" 
            }
        }], "alwaysMatch": {
            "bstack:options": {
                "useW3C": 'true',
                "headerParams" : "{\"Authorization\": \"Basic\"}"
            }
        } }
        var scope = helper.buildRails({
            action: function(uri, body) {
              body = JSON.parse(body);
              capabilities = JSON.parse(unescape(body.desiredCapabilities));
              body.u.should.equal('test');
              body.password.should.equal('test');
              capabilities.browser.should.equal('Chrome');
              capabilities.should.have.property("W3C_capabilities");
              capabilities["W3C_capabilities"].should.deep.equal(requestBody["capabilities"]);
              return [200, {'error': 'Invalid username or key'}]
            }
          });
    
          helper.request.post('/wd/hub/session')
          .auth('test', 'test')
          .send(requestBody)
          .end(function(err, res) {
            scope.isDone().should.be.true;
            res.status.should.equal(200);
            res.body.value.message.should.equal('Invalid username or key');
            res.body.sessionId.should.equal('');
            res.body.status.should.equal(13);
            done();
          });
    });
});
  
describe('pass headerParams as String in bstack:options', () => {
    it('should return success', function(done) {
        var requestBody = helper.getCapabilities({ 'bstack:options': {
            "headerParams" : "{\"Authorization\": \"Basic\"}" 
        }});
        requestBody["desiredCapabilities"]["browserstack.use_w3c"] = true;
        requestBody["capabilities"] = {"firstMatch": [{
            "bstack:options": {
                "useW3C": 'true',
                "headerParams" : "{\"Authorization\": \"Basic\"}" 
            }
        }], 
        "alwaysMatch": {
            "bstack:options": {
                "useW3C": 'true',
                "headerParams" : "{\"Authorization\": \"Basic\"}" 
            }
        } }
        var scope = helper.buildRails({
            action: function(uri, body) {
              body = JSON.parse(body);
              capabilities = JSON.parse(unescape(body.desiredCapabilities));
              body.u.should.equal('test');
              body.password.should.equal('test');
              capabilities.browser.should.equal('Chrome');
              capabilities.should.have.property("W3C_capabilities");
              capabilities["W3C_capabilities"].should.deep.equal(requestBody["capabilities"]);
              return [200, {'error': 'Invalid username or key'}]
            }
          });
    
          helper.request.post('/wd/hub/session')
          .auth('test', 'test')
          .send(requestBody)
          .end(function(err, res) {
            scope.isDone().should.be.true;
            res.status.should.equal(200);
            res.body.value.message.should.equal('Invalid username or key');
            res.body.sessionId.should.equal('');
            res.body.status.should.equal(13);
            done();
          });
    });
});

describe('pass headerParams as Object in bstack:options with firstMatch', () => {
    it('should return success', function(done) {
        var requestBody = helper.getCapabilities({ 'bstack:options': {
            "headerParams" : { 'Authorization': 'Basic' }
        }}),
          railsScope = helper.buildRails(),
          macTerminalScope = helper.buildMac({
            action: function(options) {
              options.new_hosts.should.equal(hosts);
              return [200, { done: true }];
            }
          }),
          hosts = 'google.com;reddit.com;duckduckgo.com';

        requestBody["capabilities"] = {"firstMatch": [{
            "bstack:options": {
                "headerParams" : { 'Authorization': 'Basic' },
                'hosts': hosts
            }
        }]};

        var seleniumScope = helper.selenium()
        .post('/wd/hub/session')
        .query(true)
        .reply(function(uri, body) {
          return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
        });
        
        helper.request.post('/wd/hub/session')
        .auth('test', 'test')
        .send(requestBody)
        .end(function(err, res) {
          railsScope.isDone().should.be.true;
          macTerminalScope.isDone().should.be.true;
          seleniumScope.isDone().should.be.true;
          res.status.should.equal(200);
          res.body.sessionId.should.equal(helper.SESSION_ID);
          res.body.status.should.equal(0);
          done();
        });
    });
});

describe('pass headerParams as Object in bstack:options with alwaysMatch', () => {
    it('should return success', function(done) {
        var requestBody = helper.getCapabilities({ 'bstack:options': {
            "headerParams" : { 'Authorization': 'Basic' }
        }}),
          railsScope = helper.buildRails(),
          macTerminalScope = helper.buildMac({
            action: function(options) {
              options.new_hosts.should.equal(hosts);
              return [200, { done: true }];
            }
          }),
          hosts = 'google.com;reddit.com;duckduckgo.com';
        
        requestBody['capabilities'] = {'alwaysMatch': {
            'bstack:options': {
                "headerParams" : { 'Authorization': 'Basic' },
                'hosts': hosts
            }
        }};

        var seleniumScope = helper.selenium()
        .post('/wd/hub/session')
        .query(true)
        .reply(function(uri, body) {
          return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
        });

        helper.request.post('/wd/hub/session')
        .auth('test', 'test')
        .send(requestBody)
        .end(function(err, res) {
          railsScope.isDone().should.be.true;
          macTerminalScope.isDone().should.be.true;
          seleniumScope.isDone().should.be.true;
          res.status.should.equal(200);
          res.body.sessionId.should.equal(helper.SESSION_ID);
          res.body.status.should.equal(0);
          done();
        });
    });
});
