var helper = require('./helper'),
assert = require('assert');

describe('Miscellaneous', function() {
  describe('Post session start', function() {
    xit('page load timeout is set on the browser');
  });

  describe('new_bucketing flag checks', function(){
    it('checks new_bucketing flag set for new session', function(done) {
      var capability = helper.getCapabilities({browser: 'Firefox'});
      helper.doSelenium.startSession(capability)
      .then(() => {
        assert(helper.constants.global_registry[helper.SESSION_ID]['logs_new_bucketing']==helper.constants.newBucketing)
        done();
      });
    });
  });

  describe('Session ID masking', function() {
    xit('Session ID is correctly masked for file upload');
    it('Session ID is not masked for other requests like send_keys', function(done) {
      var capability = helper.getCapabilities({browser: 'Firefox'});
      helper.doSelenium.startSession(capability)
      .then(() => helper.doSelenium.sendCommand({
        method: 'POST',
        uri: '/wd/hub/session/' + helper.SESSION_ID + '/url',
        action: function(uri, body) {
          body = JSON.parse(body);
          body.url.should.equal('http://www.google.com?q=' + helper.SESSION_ID);
          return helper.sessionStartedJSON();
        },
        body: {
          url: 'http://www.google.com?q=' + helper.SESSION_ID
        }
      }))
      .then(function(res) {
        res.status.should.equal(200);
        res.body.sessionId.should.equal('1a2b3c4d5e6f');
        res.body.value.should.deep.equal({});
        res.body.status.should.equal(0);
        done();
      });
    });
  });

  it('Masking /usr/local/.browserstack in error message during start session request', function(done) {
    var requestBody = helper.getCapabilities({
          browser: 'android',
          device : 'Google Nexus 6',
          realMobile: true }),
        railsScope = helper.buildRails(),
        macTerminalScope = helper.buildMac().persist(true);
    var appiumReturnValue = {
      message: "Here is a string that contains /usr/local/.browserstack/appium"
    };
    var railsRetryScope = helper.buildRails({
      query: {
        auth: 'selautomate', hrelease: true, hardReleaseDevice: 'pqrstu'
      }
    });

    var seleniumScope = helper.appiumZotac()
    .persist(true)
    .post('/wd/hub/session')
    .query(true)
    .reply(function(uri, body) {
      return [200, {value: appiumReturnValue, status: 33}];
    });

    helper.request.post('/wd/hub/session')
    .auth('test', 'test')
    .send(requestBody)
    .end(function(err, res) {
      railsScope.isDone().should.be.true;
      railsRetryScope.isDone().should.be.true;
      macTerminalScope.isDone().should.be.true;
      seleniumScope.isDone().should.be.true;
      res.status.should.equal(200);
      res.body.status.should.equal(13);
      res.body.value.message.should.not.contain("/usr/local/.browserstack/");
      res.body.value.message.should.contain("appium");
      done();
    });
  });
  it('Masking /usr/local/.browserstack in error message after sessions has started', function(done) {
    var capability = helper.getCapabilities({device: 'Google Nexus 6', 'realMobile': 'true'}),
        appiumReturnValue = {message: "Here is a string that contains /usr/local/.browserstack/appium"};
    helper.doSelenium.startSession(capability, helper.appiumZotac())
    .then(() => helper.doSelenium.sendCommand({
      method: 'POST',
      uri: '/wd/hub/session/' + helper.SESSION_ID + '/json_end_point',
      action: function(uri, body) {
        return [200, {value: appiumReturnValue, status: 33}];
      },
      body: { params: 'params' }
    }))
    .then(function(res) {
      res.status.should.equal(200);
      res.body.status.should.equal(33);
      res.body.value.message.should.not.contain("/usr/local/.browserstack/");
      res.body.value.message.should.contain("appium");
      done();
    })
  });

  xit('HTTP hack works');
  xit('Buffer byteLength is sent in the header');
  xit('Hub toggling');
  it('Retries getURL in Safari incase of NullPointerException', function(done) {
    var os = 'macyos', seleniumVersion = '2.45.0';
    this.timeout(3000);
    var capability = helper.getCapabilities({browser: 'Safari' , browser_version: '8'});
    var try1 = helper.selenium(os, seleniumVersion)
      .get('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/url')
      .query(true)
      .reply(function(uri, body) {
        return {status: 13, value : {class : "java.lang.NullPointerException"}};
      });

    var try2 = helper.selenium(os, seleniumVersion)
      .get('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/url')
      .query(true)
      .reply(function(uri, body) {
        return helper.sessionStartedJSON({url : 'http://www.google.com'});
      });

    helper.doSelenium.startSession(capability, helper.selenium(os, seleniumVersion))
    .then(() => new Promise(function(resolve, reject) {
        helper.request.get('/wd/hub/session/' + helper.SESSION_ID + '/url')
        .end(function(err, res) {
          try1.isDone().should.be.true;
          try2.isDone().should.be.true;
          resolve(res);
        });
    }))
    .then(function(res) {
      res.status.should.equal(200);
      res.body.status.should.equal(0);
      res.body.value.url.should.equal("http://www.google.com");
      done();
    });
  });

  it('Retries incase of BrowserDiedException', function(done) {
    var os = 'macyos', seleniumVersion = '2.45.0';
    this.timeout(3000);
    var capability = helper.getCapabilities({browser: 'Safari' , browser_version: '8'});
    var try1 = helper.selenium(os, seleniumVersion)
      .get('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/url')
      .query(true)
      .reply(function(uri, body) {
        return {status: 13, value : {message : "Error communicating with the remote browser"}};
      });

    var try2 = helper.selenium(os, seleniumVersion)
      .get('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/url')
      .query(true)
      .reply(function(uri, body) {
        return helper.sessionStartedJSON({url : 'http://www.google.com'});
      });

    helper.doSelenium.startSession(capability, helper.selenium(os, seleniumVersion))
    .then(() => new Promise(function(resolve, reject) {
        helper.request.get('/wd/hub/session/' + helper.SESSION_ID + '/url')
        .end(function(err, res) {
          try1.isDone().should.be.true;
          try2.isDone().should.be.true;
          resolve(res);
        });
    }))
    .then(function(res) {
      res.status.should.equal(200);
      res.body.status.should.equal(0);
      res.body.value.url.should.equal("http://www.google.com");
      done();
    });
  });

  it('Masks result for open url incase of BrowserDiedException', function(done) {
    var os = 'macyos', seleniumVersion = '2.45.0';
    this.timeout(3000);
    var capability = helper.getCapabilities({browser: 'Safari' , browser_version: '8'});
    var try1 = helper.selenium(os, seleniumVersion)
      .post('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/url')
      .query(true)
      .reply(function(uri, body) {
        return {status: 13, value : {message : "Error communicating with the remote browser"}};
      });

    helper.doSelenium.startSession(capability, helper.selenium(os, seleniumVersion))
    .then(() => new Promise(function(resolve, reject) {
        helper.request.post('/wd/hub/session/' + helper.SESSION_ID + '/url')
        .end(function(err, res) {
          try1.isDone().should.be.true;
          resolve(res);
        });
    }))
    .then(function(res) {
      res.status.should.equal(200);
      res.body.status.should.equal(0);
      done();
    });
  });

  xit('Sends w3c caps in case of safari 12', function(done) {
    var os = 'macmo', seleniumVersion = '3.5.2';
    var capability = helper.getCapabilities({ browser: 'Safari' , browser_version: '12' });

    helper.doSelenium.startSession(capability, helper.selenium(os, seleniumVersion), {'extraValidation': (uri, body) => {
      var body = JSON.parse(body);
      body.u.should.eql('test');
      body.password.should.eql('test');
      body.auth.should.eql('selautomate');
      body.start.should.eql('true');
      body.local.should.eql(false);
      body.isAppAutomate.should.eql(false);

      var railsCaps = JSON.parse(decodeURIComponent(body.desiredCapabilities));
      railsCaps.browser.should.eql('Safari');
      railsCaps.os.should.eql('OS X');
      railsCaps.browser_version.should.eql('12');
      railsCaps['browserstack.use_w3c'].should.eql(true);
      railsCaps.W3C_capabilities.alwaysMatch.should.eql({});
      railsCaps.W3C_capabilities.firstMatch[0].browserName.should.eql("safari");
      railsCaps.W3C_capabilities.firstMatch[0].browserVersion.should.eql("12.0");
    }}).then(function(res) {
      res.status.should.equal(200);
      res.body.status.should.equal(0);
      done();
    });
  });

  xit('Sends w3c caps in case of safari mojave', function(done) {
    var os = 'macmo', seleniumVersion = '3.5.2';
    var capability = helper.getCapabilities({ browser: 'Safari' , os_version: 'Mojave' });

    helper.doSelenium.startSession(capability, helper.selenium(os, seleniumVersion), {'extraValidation': (uri, body) => {
      var body = JSON.parse(body);
      body.u.should.eql('test');
      body.password.should.eql('test');
      body.auth.should.eql('selautomate');
      body.start.should.eql('true');
      body.local.should.eql(false);
      body.isAppAutomate.should.eql(false);

      var railsCaps = JSON.parse(decodeURIComponent(body.desiredCapabilities));
      railsCaps.browser.should.eql('Safari');
      railsCaps.os.should.eql('OS X');
      railsCaps['browserstack.use_w3c'].should.eql(true);
      railsCaps.W3C_capabilities.alwaysMatch.should.eql({});
      railsCaps.W3C_capabilities.firstMatch[0].browserName.should.eql("safari");
      railsCaps.W3C_capabilities.firstMatch[0].browserVersion.should.eql("12.0");
    }}).then(function(res) {
      res.status.should.equal(200);
      res.body.status.should.equal(0);
      done();
    });
  });
});
