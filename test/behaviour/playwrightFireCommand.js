'use strict';

const sinon = require('sinon');
const helper = require('./helper');
const HubLogger = require('../../log');
const { REMOTE_DEBUGGER_PORT } = require('../../config/socketConstants');

describe('Playwright start flow', () => {
  let requestBody;
  let railsScope;
  let seleniumScope;

  beforeEach(() => {
    requestBody = helper.getCapabilities({ isPlaywright: true });
    railsScope = helper.buildRails();
    seleniumScope = helper.selenium()
      .post('/wd/hub/session')
      .query(true)
      .reply(function(_, body) {
        return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
      });
    sinon.spy(HubLogger, 'nodeErrorHandler');
    sinon.stub(HubLogger, 'addStopToRawLogs', () => {});
  });

  afterEach(() => {
    HubLogger.nodeErrorHandler.restore();
    HubLogger.addStopToRawLogs.restore();
  });

  it('should fetch the playwright websocket url from terminal', (done) => {
    const macTerminalScope = helper.buildMac({
      action: function(options) {
        options.chromeDriverVersion.should.equal('2.20');
        options.terminal_ip.should.equal(helper.TERMINAL_IP);
        return [200, { done: true, playwright_url: 'ws://localhost:9222' }];
      }
    });

    helper.request.post('/wd/hub/session')
      .auth('test', 'test')
      .send(requestBody)
      .end((_, res) => {
        try {
          railsScope.isDone().should.be.true;
          macTerminalScope.isDone().should.be.true;
          seleniumScope.isDone().should.be.false;
          res.status.should.equal(200);
          res.body.value.should.have.all.keys('wsURL', 'wsHostname');
          res.body.sessionId.should.equal(helper.SESSION_ID);
          res.body.status.should.equal(0);
          done();
        } catch (err) {
          done(err);
        }
      });
  });

  it('Should not crash the response if no playwright_url is found', (done) => {
    const macTerminalScope = helper.buildMac({
      action: function(options) {
        options.chromeDriverVersion.should.equal('2.20');
        options.terminal_ip.should.equal(helper.TERMINAL_IP);
        return [200, { done: true }];
      }
    });

    helper.request.post('/wd/hub/session')
      .auth('test', 'test')
      .send(requestBody)
      .end((_, res) => {
        try {
          railsScope.isDone().should.be.true;
          macTerminalScope.isDone().should.be.true;
          seleniumScope.isDone().should.be.false;
          res.status.should.equal(200);
          res.body.value.should.not.have.all.keys('wsURL', 'wsHostname');
          res.body.sessionId.should.equal(helper.SESSION_ID);
          res.body.status.should.equal(0);
          done();
        } catch (err) {
          done(err);
        }
      });
  });

  it('should return dummySeleniumResponse when isPlaywright is true and playwrightAndroid is true', (done) => {
    const requestBody = helper.getCapabilities({
      isPlaywright: true,
      playwrightAndroid: true
    });

    const macTerminalScope = helper.buildMac({
      action: function(options) {
        options.chromeDriverVersion.should.equal('2.20');
        options.terminal_ip.should.equal(helper.TERMINAL_IP);
        return [200, { done: true, playwright_url: 'ws://localhost:9222' }];
      }
    });

    const seleniumScope = helper.selenium(undefined, undefined, REMOTE_DEBUGGER_PORT)
      .post('/wd/hub/session')
      .query(true)
      .reply(function(uri, body) {
        return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
      });

    helper.request.post('/wd/hub/session')
      .auth('test', 'test')
      .send(requestBody)
      .end((_, res) => {
        try {
          railsScope.isDone().should.be.true;
          macTerminalScope.isDone().should.be.true;
          seleniumScope.isDone().should.be.false;
          res.status.should.equal(200);
          res.body.value.should.have.all.keys('wsURL', 'wsHostname');
          res.body.value.wsURL.should.equal("ws://rproxy-testing.bsstag.com:9022");
          res.body.sessionId.should.equal(helper.SESSION_ID);
          res.body.status.should.equal(0);
          done();
        } catch (err) {
          done(err);
        }
      });
  });
});
