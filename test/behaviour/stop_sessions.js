/* eslint-disable */
var helper = require('./helper');
const { assert } = require('chai');
const sinon = require('sinon');
const util = require('util');
const { StopVideoRecordingService } = require('../../services/terminal/stopSessionInterface/StopVideoRecordingService');
const { consoleLogsQueue } = require('../../constants');

describe('stops session', function(){
  let fetchAdditionalLogsPromisified;
  beforeEach((done) => {
    fetchAdditionalLogsPromisified = sinon.stub(util, 'promisify').returns(() => {});
    helper.hubHelper.redisClient.flushall()
      .then(() => {
        done();
      });
  });

  afterEach(() => {
    fetchAdditionalLogsPromisified.restore();
  });

  it('stops session if App Automate free limit exceeded', () => {
    this.timeout(60000);
    const capability = helper.getCapabilities({
      browser: 'Firefox',
      'browserstack.video': false,
    });
    let stopQueryObject;
    return helper.doSelenium.startSession(capability)
      .then(() => {
        assert.notEqual(Object.keys(helper.constants.global_registry).length, 0);
        stopQueryObject = {
          auth: 'selautomate',
          k: helper.SESSION_ID,
        };
        return helper.clearSeamphoreBySessionId('session_stop_semaphore', helper.SESSION_ID);
      })
      .then(() => helper.request.post('/stop_limit_aa_freemium')
        .send(stopQueryObject))
      .then((res) => {
        assert.equal(Object.keys(helper.constants.global_registry).length, 0);
        assert.equal(res.status, 200);
      })
      .finally(() => {
        // cleaning up
        helper.clearSeamphoreBySessionId('session_stop_semaphore', helper.SESSION_ID);
      });
  });

  it("stops_session calls stopVideoRecording", function(done){
    this.timeout(60000);
    const capability = helper.getCapabilities({browser: 'Firefox', 'browserstack.video': false});
    const stopVideoSpy = sinon.spy(StopVideoRecordingService.prototype, 'handleStop');
    // const fetchAdditionalLogsPromisified = sinon.stub(util, 'promisify').returns(() => {});
    helper.doSelenium.startSession(capability)
    .then(() => {
        assert.notEqual(Object.keys(helper.constants.global_registry).length, 0)
        var stopQueryObject = {
          auth: 'selautomate',
          k: helper.SESSION_ID
        };
        helper.clearSeamphoreBySessionId('session_stop_semaphore', helper.SESSION_ID).then(() => {
          helper.request.post('/stop_limit').send(stopQueryObject).then(function(res) {
            assert.equal(Object.keys(helper.constants.global_registry).length, 0);
            assert.equal(res.status,200);
            assert(stopVideoSpy.calledOnce === true);
            stopVideoSpy.restore();
            // fetchAdditionalLogsPromisified.restore();
            //cleaning up
            helper.clearSeamphoreBySessionId('session_stop_semaphore', helper.SESSION_ID).then(() => {
              done();
            });
        })
      })
    })
  });

  it("stops_session_timedout", async function() {
    this.timeout(60000);
    const capability = helper.getCapabilities({browser: 'Firefox', 'browserstack.video': false});
    await helper.doSelenium.startSession(capability)
    assert.notEqual(Object.keys(helper.constants.global_registry).length, 0);
    const query = `/session_timedout?sessionId=${helper.SESSION_ID}`
    await helper.clearSemaphore('session_stop_semaphore', helper.SESSION_ID)
    helper.request.get(query).end(function(err, res) {
      assert.equal(Object.keys(helper.constants.global_registry).length, 0);
      assert.equal(res.status,200);
      //cleaning up
      helper.clearSemaphore('session_stop_semaphore', helper.SESSION_ID).then(() => {
      });
    });
  });

  it("doesnt session timedout if already stopped", async function() {
    this.timeout(60000);
    const capability = helper.getCapabilities({browser: 'Firefox', 'browserstack.video': false});
    await helper.doSelenium.startSession(capability)
    assert.notEqual(Object.keys(helper.constants.global_registry).length, 0);
    const query = `/session_timedout?sessionId=${helper.SESSION_ID}`
    await helper.clearSemaphore('session_stop_semaphore', helper.SESSION_ID)
    helper.request.get(query).end(function(err, res) {
      assert.equal(Object.keys(helper.constants.global_registry).length, 0);
      assert.equal(res.status,200);
    });
    helper.request.get(query).end(function(err, res) {
      assert.equal(Object.keys(helper.constants.global_registry).length, 0);
      assert.equal(res.status,409);
      //cleaning up
      helper.clearSemaphore('session_stop_semaphore', helper.SESSION_ID).then(() => {
      });
    });
  });

  it('stop_ui for App Automate session doesnt call stopVideoRecording', function (done) {
    this.timeout(60000);
    const capability = helper.AppAutomateCapabilities();
    const stopVideoSpy = sinon.spy(StopVideoRecordingService.prototype, 'handleStop');
    helper.doSelenium.startSession(capability, undefined, {isAppAutomate: true})
      .then(() => {
        // assert.notEqual(Object.keys(helper.constants.global_registry).length, 0);
        const stopQueryObject = {
          auth: 'selautomate',
          k: helper.SESSION_ID,
        };
        helper.clearSeamphoreBySessionId('session_stop_semaphore', helper.SESSION_ID)
          .then(() => {
            helper.request.post('/stop_ui')
              .send(stopQueryObject)
              .then((res) => {
                assert.equal(Object.keys(helper.constants.global_registry).length, 0);
                // assert.equal(res.status, 200);
                // cleaning up
                helper.clearSeamphoreBySessionId('session_stop_semaphore', helper.SESSION_ID)
                  .then(() => {
                    done();
                  });
                assert(stopVideoSpy.notCalled === true);
                stopVideoSpy.restore();
              });
          });
      });
  });


  it('stop_smd for App Automate session doesnt call stopVideoRecording', function (done) {
    this.timeout(60000);
    const capability = helper.AppAutomateCapabilities();
    const stopVideoSpy = sinon.spy(StopVideoRecordingService.prototype, 'handleStop');
    helper.doSelenium.startSession(capability, undefined, {isAppAutomate: true})
      .then(() => {
        // assert.notEqual(Object.keys(helper.constants.global_registry).length, 0);
        const stopQueryObject = {
          auth: 'selautomate',
          k: helper.SESSION_ID,
        };
        helper.clearSeamphoreBySessionId('session_stop_semaphore', helper.SESSION_ID)
          .then(() => {
            helper.request.post('/stop_smd')
              .send(stopQueryObject)
              .then((res) => {
                assert.equal(Object.keys(helper.constants.global_registry).length, 0);
                // assert.equal(res.status, 200);
                // cleaning up
                helper.clearSeamphoreBySessionId('session_stop_semaphore', helper.SESSION_ID)
                  .then(() => {
                    done();
                  });
                assert(stopVideoSpy.notCalled === true);
                stopVideoSpy.restore();
              });
          });
      });
  });

  it("stops session for automate mobile, and doesnt call stopVideoRecording", function(done){
    this.timeout(60000);
    const capability = helper.getCapabilities({
      device: 'Motorola Moto X 2nd Gen', realMobile: true
    });
    const stopVideoSpy = sinon.spy(StopVideoRecordingService.prototype, 'handleStop');
    helper.doSelenium.startSession(capability, helper.appiumZotac())
      .then(() => {
        assert.notEqual(Object.keys(helper.constants.global_registry).length, 0)
        var stopQueryObject = {
          auth: 'selautomate',
          k: helper.SESSION_ID
        };
        helper.clearSeamphoreBySessionId('session_stop_semaphore', helper.SESSION_ID).then(() => {
          helper.request.post('/stop_limit').send(stopQueryObject).then(function(res) {
            assert.equal(Object.keys(helper.constants.global_registry).length, 0);
            assert.equal(res.status,200);
            assert(stopVideoSpy.notCalled === true);
            stopVideoSpy.restore();
            //cleaning up
            helper.clearSeamphoreBySessionId('session_stop_semaphore', helper.SESSION_ID).then(() => {
              done();
            });
          })
        })
      })
  });

});

  //todo - mock request, response for check stopByBS individually

describe('generate stop state from request url', () => {
  it('returns STOP_UI', () => {
      state = helper.hubHelper.getStopStateFromUrl("/stop_ui");
      assert.equal(state, helper.constants.SESSIONS_STOP_STATE.STOP_UI);
  });
  it('returns STOP_LIMIT', () => {
    state = helper.hubHelper.getStopStateFromUrl("/stop_limit");
    assert.equal(state, helper.constants.SESSIONS_STOP_STATE.STOP_LIMIT);
  });
  it('returns STOP_LIMIT_AA_FREEMIUM', () => {
    state = helper.hubHelper.getStopStateFromUrl('/stop_limit_aa_freemium');
    assert.equal(state, helper.constants.SESSIONS_STOP_STATE.STOP_LIMIT_AA_FREEMIUM);
  });
  it('returns STOP_SMD', () => {
    state = helper.hubHelper.getStopStateFromUrl("/stop_smd");
    assert.equal(state, helper.constants.SESSIONS_STOP_STATE.STOP_SMD);
  });
  it('returns null for random string', () => {
    state = helper.hubHelper.getStopStateFromUrl("random_string");
    assert.equal(state, null);
  });
  it('returns null for random url', () => {
    state = helper.hubHelper.getStopStateFromUrl("/wd/hub/session");
    assert.equal(state, null);
  });

})
