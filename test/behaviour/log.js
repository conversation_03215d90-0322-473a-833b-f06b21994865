'use strict';

const sinon = require('sinon');
const HubLogger = require('../../log');
const helper = require('../../helper');
const assert = require('assert');
const constants = require('../../constants');
const LL = constants.LOG_LEVEL;
const Events = require('browserstack-dwh').Events;
const errorMessageConstants = require('../../errorMessages');


describe('HubLogger', () => {

  it('bs logger should run without errors', (done) => {
    constants.disableLogs = false;
    HubLogger.bsLogger('google.com', undefined, {}, undefined, LL.DEBUG);
    constants.disableLogs = true;
    done();
  });

  describe('redactString', () => {
    it('redacts string passed with data', (done) => {
      const redactedString = HubLogger.redactString('Hello', 'He', 'o', '[REDACT]');
      assert(redactedString == 'He[REDACT]o');
      done();
    })
  });

  describe('redactPlainJSONCerts', () => {
    it('redact linear json string passed with data at depth 0 occuring in between', (done) => {
      const redactedString = HubLogger.redactString('"plainJsonCerts":{"name": "a"},', '":', '},', '[REDACTED]');
      assert(redactedString == '"plainJsonCerts":[REDACTED]},');
      done();
    });
  });

  describe('bsErrorHandler', ()=>{
    let request;
    let errorMessage = "random_error_string";
    beforeEach(() => {
      sinon.stub(helper,'sendToEDS');
      sinon.stub(helper,'pushToCLS');
      sinon.stub(HubLogger,'seleniumStats');
      sinon.stub(HubLogger,'exceptionLogger');
    });
    afterEach(() => {
      helper.sendToEDS.restore();
      helper.pushToCLS.restore();
      HubLogger.seleniumStats.restore();
      HubLogger.exceptionLogger.restore();
    });
    it('do not send data to EDS if request object is undefined',()=>{
      request = undefined;
      HubLogger.bsErrorHandler(request, undefined, undefined, errorMessage, undefined, undefined, undefined);
      sinon.assert.notCalled(helper.sendToEDS);
    });

    it('send error data to EDS if request object is not null and exception object is not null',()=>{
      request = {
        id:"random_req_id",
        requestReceivedAt:"request_received_at",
      }
      let expectedEDSHash = {
        error_code_str: "hub_to_bs_multiple_attempts_failed",
        error_message:  errorMessageConstants.RAILS_UNREACHABLE_DURING_START,
        kind: Events.AUTOMATE_ERROR_DATA,
        request_id: request.id,
        request_received_at: request.requestReceivedAt,
      }
      let e="something";
      HubLogger.bsErrorHandler(request, undefined, e, errorMessage, undefined, undefined);
      sinon.assert.calledWithExactly(helper.sendToEDS, expectedEDSHash);
    });
    it('send error data to EDS if request object is not null and exception object is undefined',()=>{
      request = {
        id:"random_req_id",
        requestReceivedAt:"request_received_at",
      }
      let expectedEDSHash = {
        error_code_str: "hub_to_bs_multiple_attempts_failed",
        error_message: errorMessage,
        kind: Events.AUTOMATE_ERROR_DATA,
        request_id: request.id,
        request_received_at: request.requestReceivedAt,
      }
      HubLogger.bsErrorHandler(request, undefined, undefined, errorMessage, undefined, undefined);
      sinon.assert.calledWithExactly(helper.sendToEDS, expectedEDSHash);
    });
    it('send error data to AA EDS if request when AppAutomate Session',()=>{
      request = {
        id:"random_req_id",
        requestReceivedAt:"request_received_at",
      }
      let expectedEDSHash = {
        error_code_str: "hub_to_bs_multiple_attempts_failed",
        error_message: errorMessage,
        kind: Events.APP_AUTOMATE_ERROR_DATA,
        request_id: request.id,
        request_received_at: request.requestReceivedAt,
      }
      HubLogger.bsErrorHandler(request, undefined, undefined, errorMessage, undefined, undefined, { isAppAutomate: "true" });
      sinon.assert.calledWithExactly(helper.sendToEDS, expectedEDSHash);
    });
  });
});
