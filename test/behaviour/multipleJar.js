var helper = require('./helper');

// Commented as this is pending on multiple jar deploy
xdescribe('Test Multiple Jar ports behaviour', function() {
  [{
    os: 'Windows',
    os_version: '10',
    port: 6558,
    selenium_version: '3.1.0',
    mergeCaps: {}
  }, {
    os: 'android',
    os_version: null,
    port: 5555,
    selenium_version: null,
    mergeCaps: {
      device: 'Samsung Galaxy Tab 4',
      realMobile: false,
      emulator: true
    }
  }, {
    os: 'ios',
    os_version: null,
    port: 5555,
    selenium_version: null,
    mergeCaps: {
      device: 'iPhone 6S Plus',
      realMobile: false,
      emulator: true
    }
  }, {
    os: 'OS X',
    os_version: 'Snow Leopard',
    port: 6556,
    selenium_version: null,
    mergeCaps: {}
  }, {
    os: 'OS X',
    os_version: 'Snow Leopard',
    port: 6556,
    selenium_version: '2.45.0',
    mergeCaps: {}
  }, {
    os: 'OS X',
    os_version: 'Snow Leopard',
    port: 6557,
    selenium_version: '2.44.0',
    mergeCaps: {}
  }, {
    os: 'OS X',
    os_version: 'Snow Leopard',
    port: 6558,
    selenium_version: '2.46.0',
    mergeCaps: {}
  }].forEach(function(selVersionPortHash) {
    it('Should use port ' + selVersionPortHash.port + ' if capabilities has ' + selVersionPortHash.selenium_version + '  jar and os ' + selVersionPortHash.os + ' ' + selVersionPortHash.os_version, function(done) {
      var seleniumScope = helper.selenium(null, null, selVersionPortHash.port);
      var baseCaps = {
        os: selVersionPortHash.os,
        os_version: selVersionPortHash.os_version,
        'browserstack.selenium_version': selVersionPortHash.selenium_version
      };
      for (var key in selVersionPortHash.mergeCaps)
        baseCaps[key] = selVersionPortHash.mergeCaps[key];
      var capability = helper.getCapabilities(baseCaps);

      helper.doSelenium.startSession(capability, seleniumScope)
      .then(function(res) {
        res.status.should.equal(200);
        res.body.sessionId.should.equal('1a2b3c4d5e6f');
        res.body.status.should.equal(0);
        done();
      })
    });
  });
});
