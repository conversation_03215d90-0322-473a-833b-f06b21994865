var helper = require('./helper');

describe('New Session', function() {
  describe('POST /wd/hub/session', function() {
    it('returns authorization error if invalid JSON is passed', function(done) {
      helper.request.post('/wd/hub/session')
      .send('abcd')
      .end(function(err, res) {
        res.status.should.equal(401);
        res.text.should.equal('Authorization required');
        done();
      });
    });

    it('returns error when capabilities is not given', function(done) {
      helper.request.post('/wd/hub/session')
      .send({ hello: 'abc' })
      .end(function(err, res) {
        res.status.should.equal(422);
        res.body.value.message.should.equal('Neither Capabilities nor Desired Capabilities was defined');
        res.body.sessionId.should.equal('');
        res.body.status.should.equal(13);
        done();
      });
    });

    it('returns error when no basic auth / capabilities auth is passed', function(done) {
      helper.request.post('/wd/hub/session')
      .send({ desiredCapabilities: { hello: 'abc' } })
      .end(function(err, res) {
        res.status.should.equal(401);
        res.text.should.equal('Authorization required');
        done();
      });
    });

    it('returns error when blocked user detail is passed by basic auth', function(done) {
      helper.request.post('/wd/hub/session')
      .auth('blocked_user', 'hello')
      .send({ desiredCapabilities: { hello: 'abc' } })
      .end(function(err, res) {
        res.status.should.equal(401);
        res.text.should.equal('Authorization required');
        done();
      });
    });

    it('rails errors are given back to the users exactly with basic auth', function(done) {
      var requestBody = helper.getCapabilities();

      var scope = helper.buildRails({
        action: function(uri, body) {
          body = JSON.parse(body);
          capabilities = JSON.parse(unescape(body.desiredCapabilities));
          body.u.should.equal('test');
          body.password.should.equal('test');
          capabilities.browser.should.equal('Chrome');
          return [200, {'error': 'Invalid username or key'}]
        }
      });

      helper.request.post('/wd/hub/session')
      .auth('test', 'test')
      .send(requestBody)
      .end(function(err, res) {
        scope.isDone().should.be.true;
        res.status.should.equal(200);
        res.body.value.message.should.equal('Invalid username or key');
        res.body.sessionId.should.equal('');
        res.body.status.should.equal(13);
        done();
      });
    });

    xit('should pack W3C capabilities inside desiredCapabilities', function(done) {
      var requestBody = helper.getCapabilities();
      requestBody["desiredCapabilities"]["browserstack.use_w3c"] = true;
      requestBody["capabilities"] = {"firstMatch": [{"browserName": "firefox"}], "alwaysMatch": {"browserName": "chrome"}}

      var scope = helper.buildRails({
        action: function(uri, body) {
          body = JSON.parse(body);
          capabilities = JSON.parse(unescape(body.desiredCapabilities));
          body.u.should.equal('test');
          body.password.should.equal('test');
          capabilities.browser.should.equal('Chrome');
          capabilities.should.have.property("W3C_capabilities");
          capabilities["W3C_capabilities"].should.deep.equal(requestBody["capabilities"]);
          return [200, {'error': 'Invalid username or key'}]
        }
      });

      helper.request.post('/wd/hub/session')
      .auth('test', 'test')
      .send(requestBody)
      .end(function(err, res) {
        scope.isDone().should.be.true;
        res.status.should.equal(200);
        res.body.value.message.should.equal('Invalid username or key');
        res.body.sessionId.should.equal('');
        res.body.status.should.equal(13);
        done();
      });
    });

    xit('should remove w3c capabilities if browserstack.use_w3c is not passed', function(done) {
      var requestBody = helper.getCapabilities();
      requestBody["capabilities"] = {"firstMatch": ["asdas"], "alwaysMatch": [{"browserName": "chrome"}]};

      var scope = helper.buildRails({
        action: function(uri, body) {
          body = JSON.parse(body);
          capabilities = JSON.parse(unescape(body.desiredCapabilities));
          body.u.should.equal('test');
          body.password.should.equal('test');
          capabilities.browser.should.equal('Chrome');
          console.log(capabilities["W3C_capabilities"]);
          capabilities.should.not.have.property("W3C_capabilities");
          return [200, {'error': 'Invalid username or key'}]
      }});

      helper.request.post('/wd/hub/session')
      .auth('test', 'test')
      .send(requestBody)
      .end(function(err, res) {
        scope.isDone().should.be.true;
        res.status.should.equal(200);
        res.body.value.message.should.equal('Invalid username or key');
        res.body.sessionId.should.equal('');
        res.body.status.should.equal(13);
        done();
      });
    });

    xit('should remove w3c capabilities if browserstack.use_w3c is false', function(done) {
      var requestBody = helper.getCapabilities();
      requestBody["desiredCapabilities"]["browserstack.use_w3c"] = false;
      requestBody["capabilities"] = {"firstMatch": ["asdas"], "alwaysMatch": [{"browserName": "chrome"}]};

      var scope = helper.buildRails({
        action: function(uri, body) {
          body = JSON.parse(body);
          capabilities = JSON.parse(unescape(body.desiredCapabilities));
          body.u.should.equal('test');
          body.password.should.equal('test');
          capabilities.browser.should.equal('Chrome');
          console.log(capabilities["W3C_capabilities"]);
          capabilities.should.not.have.property("W3C_capabilities");
          return [200, {'error': 'Invalid username or key'}]
      }});

      helper.request.post('/wd/hub/session')
      .auth('test', 'test')
      .send(requestBody)
      .end(function(err, res) {
        scope.isDone().should.be.true;
        res.status.should.equal(200);
        res.body.value.message.should.equal('Invalid username or key');
        res.body.sessionId.should.equal('');
        res.body.status.should.equal(13);
        done();
      });
    });

    it('should initialize empty firstMatch and alwaysMatch if both are invalid', function(done) {
      var requestBody = helper.getCapabilities();
      requestBody["desiredCapabilities"]["browserstack.use_w3c"] = true;
      requestBody["capabilities"] = {"firstMatch": ["asdas"], "alwaysMatch": [{"browserName": "chrome"}]}

      var scope = helper.buildRails({
        action: function(uri, body) {
          body = JSON.parse(body);
          capabilities = JSON.parse(unescape(body.desiredCapabilities));
          body.u.should.equal('test');
          body.password.should.equal('test');
          capabilities.browser.should.equal('Chrome');
          capabilities["W3C_capabilities"].should.deep.equal({"firstMatch": [{}], "alwaysMatch": {}});
          return [200, {'error': 'Invalid username or key'}]
        }
      });

      helper.request.post('/wd/hub/session')
      .auth('test', 'test')
      .send(requestBody)
      .end(function(err, res) {
        scope.isDone().should.be.true;
        res.status.should.equal(200);
        res.body.value.message.should.equal('Invalid username or key');
        res.body.sessionId.should.equal('');
        res.body.status.should.equal(13);
        done();
      });
    });


    it('should initialize empty firstMatch if invalid', function(done) {
      var requestBody = helper.getCapabilities();
      requestBody["desiredCapabilities"]["browserstack.use_w3c"] = true;
      requestBody["capabilities"] = {"firstMatch": "asdasd", "alwaysMatch": {"browserName": "chrome"}}

      var scope = helper.buildRails({
        action: function(uri, body) {
          body = JSON.parse(body);
          capabilities = JSON.parse(unescape(body.desiredCapabilities));
          body.u.should.equal('test');
          body.password.should.equal('test');
          capabilities.browser.should.equal('Chrome');
          capabilities["W3C_capabilities"].should.deep.equal({"firstMatch": [{}], "alwaysMatch": {"browserName": "chrome"}});
          return [200, {'error': 'Invalid username or key'}]
        }
      });

      helper.request.post('/wd/hub/session')
      .auth('test', 'test')
      .send(requestBody)
      .end(function(err, res) {
        scope.isDone().should.be.true;
        res.status.should.equal(200);
        res.body.value.message.should.equal('Invalid username or key');
        res.body.sessionId.should.equal('');
        res.body.status.should.equal(13);
        done();
      });
    });

    it('should delete extensions from goog:chromeOptions before sending to Rails', function(done) {
      var requestBody = helper.getCapabilities();
      requestBody["desiredCapabilities"]["browserstack.use_w3c"] = true;
      requestBody["capabilities"] = { "firstMatch": [{
        "browserName": "chrome",
        "goog:chromeOptions": {
          "args": ["some args", "that", "should not be affected"],
          "extensions" : "insert_large_base64_encoded_file"
        }
      }]};

      var scope = helper.buildRails({
        action: function(uri, body) {
          body = JSON.parse(body);
          capabilities = JSON.parse(unescape(body.desiredCapabilities));
          body.u.should.equal('test');
          body.password.should.equal('test');
          capabilities.browser.should.equal('Chrome');
          capabilities["W3C_capabilities"].should.deep.equal({"firstMatch": [{
              "browserName": "chrome",
              "goog:chromeOptions": {
                "args": ["some args", "that", "should not be affected"],
              }
           }],
          "alwaysMatch":{}
          });
          return [200, {'error': 'Invalid username or key'}]
        }
      });

      helper.request.post('/wd/hub/session')
      .auth('test', 'test')
      .send(requestBody)
      .end(function(err, res) {
        scope.isDone().should.be.true;
        res.status.should.equal(200);
        res.body.value.message.should.equal('Invalid username or key');
        res.body.sessionId.should.equal('');
        res.body.status.should.equal(13);
        done();
      });
    });

    it('should redact profile from moz:firefoxOptions before sending to Rails', function(done) {
      var requestBody = helper.getCapabilities();
      requestBody["desiredCapabilities"]["browserstack.use_w3c"] = true;
      requestBody["capabilities"] = { "firstMatch": [{
        "browserName": "firefox",
        "moz:firefoxOptions": {
          "args": ["some args", "that", "should not be affected"],
          "profile" : "insert_large_base64_encoded_file"
        }
      }]};

      var scope = helper.buildRails({
        action: function(uri, body) {
          body = JSON.parse(body);
          capabilities = JSON.parse(unescape(body.desiredCapabilities));
          body.u.should.equal('test');
          body.password.should.equal('test');
          capabilities.browser.should.equal('Chrome');
          capabilities["W3C_capabilities"].should.deep.equal({"firstMatch": [{
              "browserName": "firefox",
              "moz:firefoxOptions": {
                "args": ["some args", "that", "should not be affected"],
                "profile": "[REDACTED]"
              }
           }],
          "alwaysMatch":{}
          });
          return [200, {'error': 'Invalid username or key'}]
        }
      });

      helper.request.post('/wd/hub/session')
      .auth('test', 'test')
      .send(requestBody)
      .end(function(err, res) {
        scope.isDone().should.be.true;
        res.status.should.equal(200);
        res.body.value.message.should.equal('Invalid username or key');
        res.body.sessionId.should.equal('');
        res.body.status.should.equal(13);
        done();
      });
    });

    it('should redact profile from moz:firefoxOptions in desiredCapabilities before sending to Rails', function(done) {
      var requestBody = helper.getCapabilities();
      requestBody['desiredCapabilities']['browser'] = 'Firefox'
      requestBody['desiredCapabilities']['moz:firefoxOptions'] = {
        'args': ['some args', 'that', 'should not be affected'],
        'profile' : 'insert_large_base64_encoded_file'
      };

      var scope = helper.buildRails({
        action: function(uri, body) {
          body = JSON.parse(body);
          capabilities = JSON.parse(unescape(body.desiredCapabilities));
          body.u.should.equal('test');
          body.password.should.equal('test');
          capabilities.browser.should.equal('Firefox');
          capabilities['moz:firefoxOptions'].should.deep.equal({
            'args': ['some args', 'that', 'should not be affected'],
            "profile": "[REDACTED]"
          });
          return [200, {'error': 'Invalid username or key'}]
        }
      });

      helper.request.post('/wd/hub/session')
      .auth('test', 'test')
      .send(requestBody)
      .end(function(err, res) {
        scope.isDone().should.be.true;
        res.status.should.equal(200);
        res.body.value.message.should.equal('Invalid username or key');
        res.body.sessionId.should.equal('');
        res.body.status.should.equal(13);
        done();
      });
    });

    it('should redact profile from firefoxOptions in desiredCapabilities before sending to Rails', function(done) {
      var requestBody = helper.getCapabilities();
      requestBody['desiredCapabilities']['browser'] = 'Firefox'
      requestBody['desiredCapabilities']['firefoxOptions'] = {
        'args': ['some args', 'that', 'should not be affected'],
        'profile' : 'insert_large_base64_encoded_file'
      };

      var scope = helper.buildRails({
        action: function(uri, body) {
          body = JSON.parse(body);
          capabilities = JSON.parse(unescape(body.desiredCapabilities));
          body.u.should.equal('test');
          body.password.should.equal('test');
          capabilities.browser.should.equal('Firefox');
          capabilities['firefoxOptions'].should.deep.equal({
            'args': ['some args', 'that', 'should not be affected'],
            "profile": "[REDACTED]"
          });
          return [200, {'error': 'Invalid username or key'}]
        }
      });

      helper.request.post('/wd/hub/session')
      .auth('test', 'test')
      .send(requestBody)
      .end(function(err, res) {
        scope.isDone().should.be.true;
        res.status.should.equal(200);
        res.body.value.message.should.equal('Invalid username or key');
        res.body.sessionId.should.equal('');
        res.body.status.should.equal(13);
        done();
      });
    });

    it('rails errors are given back to the users exactly with capabilities auth', function(done) {
      var requestBody = helper.getCapabilities({
        'browserstack.user': 'test',
        'browserstack.key': 'test',
      });

      var scope = helper.buildRails({
        action: function(uri, body) {
          body = JSON.parse(body);
          capabilities = JSON.parse(unescape(body.desiredCapabilities));
          body.u.should.equal('test');
          body.password.should.equal('test');
          capabilities.browser.should.equal('Chrome');
          return [200, {'error': 'Invalid username or key'}]
        }
      });

      helper.request.post('/wd/hub/session')
      .send(requestBody)
      .end(function(err, res) {
        scope.isDone().should.be.true;
        res.status.should.equal(200);
        res.body.value.message.should.equal('Invalid username or key');
        res.body.sessionId.should.equal('');
        res.body.status.should.equal(13);
        done();
      });
    });
  });
});
