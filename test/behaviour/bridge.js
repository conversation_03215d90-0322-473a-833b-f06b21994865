var helper = require('./helper');
const sinon = require('sinon');
const bridge = require('../../bridge');
const { after } = require('mocha');
const { LaunchAppHandler } = require('../../controllers/seleniumCommand/handlers/LaunchAppHandler');

describe('Bridge Client and Node', function() {
  describe('CustomSendKeys Capability for IE on Windows', function() {
    describe('Send text to particular element', function() {
      it('Clicks and sends text for sendKeys', function(done) {
        var performClick = helper.selenium()
          .post('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/element/0/click')
          .query(true)
          .reply(function(uri, body) {
            return helper.sessionStartedJSON('complete');
          });
        var sendKeysIE = helper.buildWin({
          uri: '/sendkeys',
          query: true,
          action: [function(uri, body) {
            uri.should.equal('/sendkeys?sleep=800&text=' + encodeURIComponent('B#-#r#-#o#-#w#-#s#-#e#-#r#-#S#-#t#-#a#-#c#-#k#-#'));
            return 'done';
          }]
        });

        var capability = helper.getCapabilities({
          browser: 'internet explorer',
          'browserstack.customSendKeys': true
        });

        helper.doSelenium.startSession(capability)
        .then(() => helper.doSelenium.sendCommand({
          method: 'POST',
          uri: '/wd/hub/session/' + helper.SESSION_ID + '/element/0/value',
          noAssert: true,
          body: { value: [ 'BrowserStack' ] }
        }))
        .then(function(res) {
          performClick.isDone().should.be.true;
          sendKeysIE.isDone().should.be.true;
          res.status.should.equal(200);
          res.body.sessionId.should.equal('1a2b3c4d5e6f');
          (res.body.value == null).should.be.true;
          res.body.status.should.equal(0);
          done();
        })
      });
      it('Clicks and sends text for sendKeys in case of string element id', function(done) {
        var performClick = helper.selenium()
          .post('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/element/abcd-efgh-ijkl/click')
          .query(true)
          .reply(function(uri, body) {
            return helper.sessionStartedJSON('complete');
          });
        var sendKeysIE = helper.buildWin({
          uri: '/sendkeys',
          query: true,
          action: [function(uri, body) {
            uri.should.equal('/sendkeys?sleep=800&text=' + encodeURIComponent('B#-#r#-#o#-#w#-#s#-#e#-#r#-#S#-#t#-#a#-#c#-#k#-#'));
            return 'done';
          }]
        });

        var capability = helper.getCapabilities({
          browser: 'internet explorer',
          'browserstack.customSendKeys': true
        });

        helper.doSelenium.startSession(capability)
        .then(() => helper.doSelenium.sendCommand({
          method: 'POST',
          uri: '/wd/hub/session/' + helper.SESSION_ID + '/element/abcd-efgh-ijkl/value',
          noAssert: true,
          body: { value: [ 'BrowserStack' ] }
        }))
        .then(function(res) {
          performClick.isDone().should.be.true;
          sendKeysIE.isDone().should.be.true;
          res.status.should.equal(200);
          res.body.sessionId.should.equal('1a2b3c4d5e6f');
          (res.body.value == null).should.be.true;
          res.body.status.should.equal(0);
          done();
        })
      });
      it('SendKeys on IE errors without element id', function(done) {
        var sendKeysIE = helper.buildWin()
          .get('/sendkeys')
          .query(true)
          .reply(function(uri, body) {
            uri.should.equal('/sendkeys?sleep=800&text=' + encodeURIComponent('B#-#r#-#o#-#w#-#s#-#e#-#r#-#S#-#t#-#a#-#c#-#k#-#'));
            return 'done';
          });

        var capability = helper.getCapabilities({
          browser: 'internet explorer',
          'browserstack.customSendKeys': true
        });

        helper.doSelenium.startSession(capability)
        .then(() => helper.doSelenium.sendCommand({
          method: 'POST',
          uri: '/wd/hub/session/' + helper.SESSION_ID + '/element//value',
          noAssert: true,
          body: { value: [ 'BrowserStack' ] }
        }))
        .then(function(res) {
          sendKeysIE.isDone().should.be.false;
          res.status.should.equal(500);
          res.body.value.message.should.equal('An unknown server-side error occurred while processing the command.');
          res.body.status.should.equal(13);
          done();
        })
      });
      it('SendKeys on IE errors when click errors out', function(done) {
        var performClick = helper.selenium()
          .post('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/element/0/click')
          .query(true)
          .replyWithError(JSON.stringify({ sessionId: exports.TERMINAL_SESSION_ID, value: null, status: 13 }));

        var sendKeysIE = helper.buildWin()
          .get('/sendkeys')
          .query(true)
          .reply(function(uri, body) {
            uri.should.equal('/sendkeys?sleep=800&text=' + encodeURIComponent('B#-#r#-#o#-#w#-#s#-#e#-#r#-#S#-#t#-#a#-#c#-#k#-#'));
            return 'done';
          });

        var capability = helper.getCapabilities({
          browser: 'internet explorer',
          'browserstack.customSendKeys': true
        });

        helper.doSelenium.startSession(capability)
        .then(() => helper.doSelenium.sendCommand({
          method: 'POST',
          uri: '/wd/hub/session/' + helper.SESSION_ID + '/element/0/value',
          noAssert: true,
          body: { value: [ 'BrowserStack' ] }
        }))
        .then(function(res) {
          performClick.isDone().should.be.true;
          sendKeysIE.isDone().should.be.false;
          res.status.should.equal(500);
          res.body.value.message.should.equal('An unknown server-side error occurred while processing the command.');
          res.body.status.should.equal(13);
          done();
        })
      });
      it('SendKeys on IE errors when autoit errors out', function(done) {
        var performClick = helper.selenium()
          .post('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/element/0/click')
          .query(true)
          .reply(function(uri, body) {
            return helper.sessionStartedJSON('complete');
          });

        var sendKeysIE = helper.buildWin()
          .get('/sendkeys')
          .query(true)
          .replyWithError('Random Error');

        var capability = helper.getCapabilities({
          browser: 'internet explorer',
          'browserstack.customSendKeys': true
        });

        helper.doSelenium.startSession(capability)
        .then(() => helper.doSelenium.sendCommand({
          method: 'POST',
          uri: '/wd/hub/session/' + helper.SESSION_ID + '/element/0/value',
          noAssert: true,
          body: { value: [ 'BrowserStack' ] }
        }))
        .then(function(res) {
          performClick.isDone().should.be.true;
          sendKeysIE.isDone().should.be.false;
          res.status.should.equal(500);
          res.body.value.message.should.equal('An unknown server-side error occurred while processing the command.');
          res.body.status.should.equal(13);
          done();
        })
      });
    });
    describe('Send text to active element', function() {
      it('Sends text for keys', function(done) {
        var sendKeysIE = helper.buildWin({
          uri: '/sendkeys',
          query: true,
          action: [function(uri, body) {
            uri.should.equal('/sendkeys?sleep=800&text=' + encodeURIComponent('B#-#r#-#o#-#w#-#s#-#e#-#r#-#S#-#t#-#a#-#c#-#k#-#'));
            return 'done';
          }]
        });

        var capability = helper.getCapabilities({
          browser: 'internet explorer',
          'browserstack.customSendKeys': true
        });

        helper.doSelenium.startSession(capability)
        .then(() => helper.doSelenium.sendCommand({
          method: 'POST',
          uri: '/wd/hub/session/' + helper.SESSION_ID + '/keys',
          noAssert: true,
          body: { value: [ 'BrowserStack' ] }
        }))
        .then(function(res) {
          sendKeysIE.isDone().should.be.true;
          res.status.should.equal(200);
          res.body.sessionId.should.equal('1a2b3c4d5e6f');
          (res.body.value == null).should.be.true;
          res.body.status.should.equal(0);
          done();
        })
      });
      it('Keys on IE errors when autoit errors out', function(done) {
        var sendKeysIE = helper.buildWin()
          .get('/sendkeys')
          .query(true)
          .replyWithError('Random Error');

        var capability = helper.getCapabilities({
          browser: 'internet explorer',
          'browserstack.customSendKeys': true
        });

        helper.doSelenium.startSession(capability)
        .then(() => helper.doSelenium.sendCommand({
          method: 'POST',
          uri: '/wd/hub/session/' + helper.SESSION_ID + '/keys',
          noAssert: true,
          body: { value: [ 'BrowserStack' ] }
        }))
        .then(function(res) {
          sendKeysIE.isDone().should.be.false;
          res.status.should.equal(500);
          res.body.value.message.should.equal('An unknown server-side error occurred while processing the command.');
          res.body.status.should.equal(13);
          done();
        })
      });
    });
  });
  xit('Basic Auth popup check');
  describe('Transition check', function() {
    it('Firefox => POST:url -> POST:element', function(done) {
      var capability = helper.getCapabilities({browser: 'Firefox', 'browserstack.video': false});
      helper.doSelenium.startSession(capability)
      .then(() => helper.doSelenium.sendCommand({
        method: 'POST',
        uri: '/wd/hub/session/' + helper.SESSION_ID + '/url',
        action: function(uri, body) {
          body = JSON.parse(body);
          body.url.should.equal('http://www.google.com');
          return helper.sessionStartedJSON();
        },
        body: { url: 'http://www.google.com' }
      }))
      .then(function(res) {
        res.status.should.equal(200);
        res.body.sessionId.should.equal('1a2b3c4d5e6f');
        res.body.value.should.deep.equal({});
        res.body.status.should.equal(0);
      })
      .then(() => {
        var postExecute = helper.selenium()
        .post('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/execute')
        .query(true)
        .reply(function(uri, body) {
          body = JSON.parse(body);
          body.script.should.equal('return document.readyState');
          body.args.should.deep.equal([]);
          return helper.sessionStartedJSON('complete');
        });
        return helper.doSelenium.sendCommand({
          method: 'POST',
          uri: '/wd/hub/session/' + helper.SESSION_ID + '/element',
          action: function(uri, body) {
            postExecute.isDone().should.be.true;
            return helper.sessionStartedJSON({ELEMENT: '0'});
          },
          body: {
            using: "id",
            value: "q"
          }
        });
      })
      .then(function(res) {
        res.status.should.equal(200);
        res.body.sessionId.should.equal('1a2b3c4d5e6f');
        res.body.value.should.deep.equal({ELEMENT: '0'});
        res.body.status.should.equal(0);
        done();
      });
    });
    it('iOS => DELETE:window -> Any command', function(done) {
      var capability = helper.getCapabilities({ device: 'iPhone 6S Plus', 'realMobile': true });
      delete capability.desiredCapabilities['browser'];

      helper.doSelenium.startSession(capability, helper.appiumMini())
      .then(() => helper.doSelenium.sendCommand({
        method: 'DELETE',
        uri: '/wd/hub/session/' + helper.SESSION_ID + '/window',
        action: helper.sessionStartedJSON()
      }))
      .then(function(res) {
        res.status.should.equal(200);
      })
      .then(() => helper.doSelenium.sendCommand({
        method: 'POST',
        uri: '/wd/hub/session/' + helper.SESSION_ID + '/url',
        noAssert: true,
        body: { url: 'http://www.google.com' }
      }))
      .then(function(res) {
        res.status.should.equal(200);
        res.body.sessionId.should.equal(helper.SESSION_ID);
        res.body.state.should.equal('error');
        res.body.value.should.deep.equal({'message':'An unknown server-side error occurred while processing the command.', 'error':'unknown error'});
        res.body.class.should.equal('org.openqa.selenium.remote.Response');
        res.body.status.should.equal(13);
        done();
      });
    });
  });
  it('Active element check', function(done) {
    var capability = helper.getCapabilities({browser: 'Firefox', browser_version: 13});
    var postExecute;
    helper.doSelenium.startSession(capability)
    .then(() => helper.doSelenium.sendCommand({
      method: 'POST',
      uri: '/wd/hub/session/' + helper.SESSION_ID + '/url',
      action: function(uri, body) {
        body = JSON.parse(body);
        body.url.should.equal('http://www.google.com');
        return helper.sessionStartedJSON();
      },
      body: { url: 'http://www.google.com' }
    }))
    .then(function(res) {
      res.status.should.equal(200);
      res.body.sessionId.should.equal('1a2b3c4d5e6f');
      res.body.value.should.deep.equal({});
      res.body.status.should.equal(0);
    })
    .then(() => {
      postExecute = helper.selenium()
      .post('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/execute')
      .query(true)
      .reply(function(uri, body) {
        body = JSON.parse(body);
        body.script.should.equal('return document.activeElement');
        body.args.should.deep.equal([]);
        return helper.sessionStartedJSON('complete');
      });
      return helper.doSelenium.sendCommand({
        method: 'POST',
        uri: '/wd/hub/session/' + helper.SESSION_ID + '/element/active',
        action: helper.sessionStartedJSON({ELEMENT: '0'}),
        noAssert: true
      });
    })
    .then(function(res) {
      postExecute.isDone().should.be.true;
      res.status.should.equal(200);
      res.body.sessionId.should.equal('1a2b3c4d5e6f');
      res.body.value.should.deep.equal('complete');
      res.body.status.should.equal(0);
      done();
    });
  });
  //Moving it to 1 as retry has been removed in this PR - #7877.
  it('Retry on page load timeout firefox', function(done) {
    var capability = helper.getCapabilities({browser: 'Firefox'});
    var count = 0;
    helper.doSelenium.startSession(capability)
    .then(() => helper.doSelenium.sendCommand({
      method: 'POST',
      uri: '/wd/hub/session/' + helper.SESSION_ID + '/url',
      persist: true,
      action: function(uri, body) {
        count++;
        if (count == 1) {
          body = JSON.parse(body)
          body.url.should.equal('http://www.google.com/');
          return [200, {status: 28}];
        }
        else if(count == 2) {
          body = JSON.parse(body)
          body.url.should.equal('about:blank');
          return helper.sessionStartedJSON();
        }
        else {
          body = JSON.parse(body)
          body.url.should.equal('http://www.google.com/');
          return helper.sessionStartedJSON();
        }
      },
      body: {
        url: 'http://www.google.com/'
      }
    }))
    .then(function(res) {
      count.should.equal(1);
      res.status.should.equal(200);
      done();
    });
  });
  //Moving retries to 1 as retry logic has been removed in this PR - #7877.
  describe('Page Load Logic', function() {
    describe('Chrome', function() {
      it('Retry on page load timeout chrome', function(done) {
        var capability = helper.getCapabilities({browser: 'Chrome'});
        var count = 0;
        helper.doSelenium.startSession(capability)
        .then(() => helper.doSelenium.sendCommand({
          method: 'POST',
          uri: '/wd/hub/session/' + helper.SESSION_ID + '/url',
          persist: true,
          action: function(uri, body) {
            count++;
            if (count == 1) {
              body = JSON.parse(body)
              body.url.should.equal('http://www.google.com/');
              return [200, {status: 28}];
            }
            else {
              body = JSON.parse(body)
              body.url.should.equal('http://www.google.com/');
              return helper.sessionStartedJSON();
            }
          },
          body: {
            url: 'http://www.google.com/'
          }
        }))
        .then(function(res) {
          count.should.equal(1);
          res.status.should.equal(200);
          res.body.status.should.equal(28);
          done();
        });
      });
      it('Retry on page load timeout chrome resets pageLoadTimeout ( between retry AND after)', function(done) {
        var capability = helper.getCapabilities({browser: 'Chrome'});
        var count = 0;
        var pageLoadSetCount = 0;
        var setTimeoutTerminal = helper.selenium()
          .persist()
          .post('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/timeouts')
          .query(true)
          .reply(function(uri, body) {
            pageLoadSetCount += 1;
            body = JSON.parse(body);
            body.type.should.equal('page load');
            body.ms.should.equal(200000);
            return helper.sessionStartedJSON('complete');
          });

        helper.doSelenium.startSession(capability)
        .then(() => helper.doSelenium.sendCommand({
          method: 'POST',
          uri: '/wd/hub/session/' + helper.SESSION_ID + '/url',
          persist: true,
          action: function(uri, body) {
            count++;
            if (count == 1) {
              body = JSON.parse(body)
              body.url.should.equal('http://www.google.com/');
              return [200, {status: 28}];
            }
            else {
              body = JSON.parse(body)
              body.url.should.equal('http://www.google.com/');
              return helper.sessionStartedJSON();
            }
          },
          body: {
            url: 'http://www.google.com/'
          }
        }))
        .then(function(res) {
          count.should.equal(1);
          setTimeoutTerminal.isDone().should.be.true;
          pageLoadSetCount.should.equal(2);
          res.status.should.equal(200);
          res.body.status.should.equal(28);
          done();
        });
      });
      it('Returns OK if retry failed with "timeout: cannot determine loading status"', function(done) {
        var capability = helper.getCapabilities({browser: 'Chrome'});
        var count = 0;
        var pageLoadSetCount = 0;
        var setTimeoutTerminal = helper.selenium()
          .persist()
          .post('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/timeouts')
          .query(true)
          .reply(function(uri, body) {
            pageLoadSetCount += 1;
            body = JSON.parse(body);
            body.type.should.equal('page load');
            body.ms.should.equal(200000);
            return helper.sessionStartedJSON('complete');
          });
        var readyStateCheck = helper.selenium()
          .persist()
          .post('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/execute')
          .query(true)
          .reply(function(uri, body) {
            body = JSON.parse(body);
            body.script.should.equal("return document.readyState");
            return helper.sessionStartedJSON('complete');
          });

        helper.doSelenium.startSession(capability)
        .then(() => helper.doSelenium.sendCommand({
          method: 'POST',
          uri: '/wd/hub/session/' + helper.SESSION_ID + '/url',
          persist: true,
          action: function(uri, body) {
            count++;
            if (count == 1) {
              body = JSON.parse(body)
              body.url.should.equal('http://www.google.com/');
              return [200, {status: 28}];
            }
            else {
              body = JSON.parse(body)
              body.url.should.equal('http://www.google.com/');
              return [200, {status: 28, value: 'EXTRAtiMeout: caNnOt deTeRmine loading stAtusTEXT'}];
            }
          },
          body: {
            url: 'http://www.google.com/'
          }
        }))
        .then(function(res) {
          count.should.equal(1);
          setTimeoutTerminal.isDone().should.be.true;
          readyStateCheck.isDone().should.be.false;
          pageLoadSetCount.should.equal(2);
          res.status.should.equal(200);
          res.body.status.should.equal(28);
          done();
        });
      });
      it('Returns OK if retry failed with "timeout: Timed out receiving message from renderer"', function(done) {
        var capability = helper.getCapabilities({browser: 'Chrome'});
        var count = 0;
        var pageLoadSetCount = 0;
        var setTimeoutTerminal = helper.selenium()
          .persist()
          .post('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/timeouts')
          .query(true)
          .reply(function(uri, body) {
            pageLoadSetCount += 1;
            body = JSON.parse(body);
            body.type.should.equal('page load');
            body.ms.should.equal(200000);
            return helper.sessionStartedJSON('complete');
          });
        var readyStateCheck = helper.selenium()
          .persist()
          .post('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/execute')
          .query(true)
          .reply(function(uri, body) {
            body = JSON.parse(body);
            body.script.should.equal("return document.readyState");
            return helper.sessionStartedJSON('complete');
          });

        helper.doSelenium.startSession(capability)
        .then(() => helper.doSelenium.sendCommand({
          method: 'POST',
          uri: '/wd/hub/session/' + helper.SESSION_ID + '/url',
          persist: true,
          action: function(uri, body) {
            count++;
            if (count == 1) {
              body = JSON.parse(body)
              body.url.should.equal('http://www.google.com/');
              return [200, {status: 28}];
            }
            else {
              body = JSON.parse(body)
              body.url.should.equal('http://www.google.com/');
              return [200, {status: 28, value: 'EXTRAtiMEout: TiMed Out reCeiving messaGe from reNderErTEXT'}];
            }
          },
          body: {
            url: 'http://www.google.com/'
          }
        }))
        .then(function(res) {
          count.should.equal(1);
          setTimeoutTerminal.isDone().should.be.true;
          readyStateCheck.isDone().should.be.false;
          pageLoadSetCount.should.equal(2);
          res.status.should.equal(200);
          res.body.status.should.equal(28);
          done();
        });
      });
      it('Should not return OK if retry failed with any other timeout', function(done) {
        var capability = helper.getCapabilities({browser: 'Chrome'});
        var count = 0;
        var pageLoadSetCount = 0;
        var setTimeoutTerminal = helper.selenium()
          .persist()
          .post('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/timeouts')
          .query(true)
          .reply(function(uri, body) {
            pageLoadSetCount += 1;
            body = JSON.parse(body);
            body.type.should.equal('page load');
            body.ms.should.equal(200000);
            return helper.sessionStartedJSON('complete');
          });
        var readyStateCheck = helper.selenium()
          .persist()
          .post('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/execute')
          .query(true)
          .reply(function(uri, body) {
            body = JSON.parse(body);
            body.script.should.equal("return document.readyState");
            return helper.sessionStartedJSON('complete');
          });

        helper.doSelenium.startSession(capability)
        .then(() => helper.doSelenium.sendCommand({
          method: 'POST',
          uri: '/wd/hub/session/' + helper.SESSION_ID + '/url',
          persist: true,
          action: function(uri, body) {
            count++;
            if (count == 1) {
              body = JSON.parse(body)
              body.url.should.equal('http://www.google.com/');
              return [200, {status: 28, value: 'page load timeout'}];
            }
            else {
              body = JSON.parse(body)
              body.url.should.equal('http://www.google.com/');
              return [200, {status: 28, value: 'page load timeout'}];
            }
          },
          body: {
            url: 'http://www.google.com/'
          }
        }))
        .then(function(res) {
          count.should.equal(1);
          setTimeoutTerminal.isDone().should.be.true;
          readyStateCheck.isDone().should.be.false;
          pageLoadSetCount.should.equal(2);
          res.status.should.equal(200);
          res.body.status.should.equal(28);
          res.body.value.should.equal('page load timeout');
          done();
        });
      });
    });
    describe('Firefox', function() {
      it('Retry on page load timeout firefox', function(done) {
        var capability = helper.getCapabilities({browser: 'Firefox'});
        var count = 0;
        helper.doSelenium.startSession(capability)
        .then(() => helper.doSelenium.sendCommand({
          method: 'POST',
          uri: '/wd/hub/session/' + helper.SESSION_ID + '/url',
          persist: true,
          action: function(uri, body) {
            count++;
            if (count == 1) {
              body = JSON.parse(body)
              body.url.should.equal('http://www.google.com/');
              return [200, {status: 28}];
            }
            else {
              body = JSON.parse(body)
              body.url.should.equal('http://www.google.com/');
              return helper.sessionStartedJSON();
            }
          },
          body: {
            url: 'http://www.google.com/'
          }
        }))
        .then(function(res) {
          count.should.equal(1);
          res.status.should.equal(200);
          res.body.status.should.equal(28);
          done();
        });
      });
      it('Retry on page load timeout firefox does not reset pageLoadTimeout ( between retry OR after)', function(done) {
        var capability = helper.getCapabilities({browser: 'Firefox'});
        var count = 0;
        var pageLoadSetCount = 0;
        var setTimeoutTerminal = helper.selenium()
          .persist()
          .post('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/timeouts')
          .query(true)
          .reply(function(uri, body) {
            pageLoadSetCount += 1;
            body = JSON.parse(body);
            body.type.should.equal('page load');
            body.ms.should.equal(200000);
            return helper.sessionStartedJSON('complete');
          });

        helper.doSelenium.startSession(capability)
        .then(() => helper.doSelenium.sendCommand({
          method: 'POST',
          uri: '/wd/hub/session/' + helper.SESSION_ID + '/url',
          persist: true,
          action: function(uri, body) {
            count++;
            if (count == 1) {
              body = JSON.parse(body)
              body.url.should.equal('http://www.google.com/');
              return [200, {status: 28}];
            }
            else {
              body = JSON.parse(body)
              body.url.should.equal('http://www.google.com/');
              return helper.sessionStartedJSON();
            }
          },
          body: {
            url: 'http://www.google.com/'
          }
        }))
        .then(function(res) {
          count.should.equal(1);
          setTimeoutTerminal.isDone().should.be.true;
          // We set page load timeout before /url
          pageLoadSetCount.should.equal(1);
          res.status.should.equal(200);
          res.body.status.should.equal(28);
          done();
        });
      });
      it('Returns OK if retry failed with "timeout: cannot determine loading status" for firefox', function(done) {
        var capability = helper.getCapabilities({browser: 'Firefox'});
        var count = 0;
        var pageLoadSetCount = 0;
        var setTimeoutTerminal = helper.selenium()
          .persist()
          .post('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/timeouts')
          .query(true)
          .reply(function(uri, body) {
            pageLoadSetCount += 1;
            body = JSON.parse(body);
            body.type.should.equal('page load');
            body.ms.should.equal(200000);
            return helper.sessionStartedJSON('complete');
          });
        var readyStateCheck = helper.selenium()
          .persist()
          .post('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/execute')
          .query(true)
          .reply(function(uri, body) {
            body = JSON.parse(body);
            body.script.should.equal("return document.readyState");
            return helper.sessionStartedJSON('complete');
          });

        helper.doSelenium.startSession(capability)
        .then(() => helper.doSelenium.sendCommand({
          method: 'POST',
          uri: '/wd/hub/session/' + helper.SESSION_ID + '/url',
          persist: true,
          action: function(uri, body) {
            count++;
            if (count == 1) {
              body = JSON.parse(body)
              body.url.should.equal('http://www.google.com/');
              return [200, {status: 28, value: 'Page Load Timeout'}];
            }
            else {
              body = JSON.parse(body)
              body.url.should.equal('http://www.google.com/');
              return [200, {status: 28, value: 'EXTRAtiMeout: caNnOt deTeRmine loading stAtusTEXT'}];
            }
          },
          body: {
            url: 'http://www.google.com/'
          }
        }))
        .then(function(res) {
          count.should.equal(1);
          setTimeoutTerminal.isDone().should.be.true;
          readyStateCheck.isDone().should.be.false;
          pageLoadSetCount.should.equal(1);
          res.status.should.equal(200);
          res.body.status.should.equal(28);
          done();
        });
      });
      it('Returns OK if retry failed with "timeout: Timed out receiving message from renderer" for firefox', function(done) {
        var capability = helper.getCapabilities({browser: 'Firefox'});
        var count = 0;
        var pageLoadSetCount = 0;
        var setTimeoutTerminal = helper.selenium()
          .persist()
          .post('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/timeouts')
          .query(true)
          .reply(function(uri, body) {
            pageLoadSetCount += 1;
            body = JSON.parse(body);
            body.type.should.equal('page load');
            body.ms.should.equal(200000);
            return helper.sessionStartedJSON('complete');
          });
        var readyStateCheck = helper.selenium()
          .persist()
          .post('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/execute')
          .query(true)
          .reply(function(uri, body) {
            body = JSON.parse(body);
            body.script.should.equal("return document.readyState");
            return helper.sessionStartedJSON('complete');
          });

        helper.doSelenium.startSession(capability)
        .then(() => helper.doSelenium.sendCommand({
          method: 'POST',
          uri: '/wd/hub/session/' + helper.SESSION_ID + '/url',
          persist: true,
          action: function(uri, body) {
            count++;
            if (count == 1) {
              body = JSON.parse(body)
              body.url.should.equal('http://www.google.com/');
              return [200, {status: 28, value: 'EXTRAtiMEout: TiMed Out reCeiving messaGe from reNderErTEXT'}];
            }
            else {
              body = JSON.parse(body)
              body.url.should.equal('http://www.google.com/');
              return [200, {status: 28, value: 'EXTRAtiMEout: TiMed Out reCeiving messaGe from reNderErTEXT'}];
            }
          },
          body: {
            url: 'http://www.google.com/'
          }
        }))
        .then(function(res) {
          count.should.equal(1);
          setTimeoutTerminal.isDone().should.be.true;
          readyStateCheck.isDone().should.be.false;
          pageLoadSetCount.should.equal(1);
          res.status.should.equal(200);
          res.body.status.should.equal(28);
          done();
        });
      });
      it('Should not return OK if retry failed with any other timeout for firefox', function(done) {
        var capability = helper.getCapabilities({browser: 'Firefox'});
        var count = 0;
        var pageLoadSetCount = 0;
        var setTimeoutTerminal = helper.selenium()
          .persist()
          .post('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/timeouts')
          .query(true)
          .reply(function(uri, body) {
            pageLoadSetCount += 1;
            body = JSON.parse(body);
            body.type.should.equal('page load');
            body.ms.should.equal(200000);
            return helper.sessionStartedJSON('complete');
          });
        var readyStateCheck = helper.selenium()
          .persist()
          .post('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/execute')
          .query(true)
          .reply(function(uri, body) {
            body = JSON.parse(body);
            body.script.should.equal("return document.readyState");
            return helper.sessionStartedJSON('complete');
          });

        helper.doSelenium.startSession(capability)
        .then(() => helper.doSelenium.sendCommand({
          method: 'POST',
          uri: '/wd/hub/session/' + helper.SESSION_ID + '/url',
          persist: true,
          action: function(uri, body) {
            count++;
            if (count == 1) {
              body = JSON.parse(body)
              body.url.should.equal('http://www.google.com/');
              return [200, {status: 28, value: 'page load timeout'}];
            }
            else {
              body = JSON.parse(body)
              body.url.should.equal('http://www.google.com/');
              return [200, {status: 28, value: 'page load timeout'}];
            }
          },
          body: {
            url: 'http://www.google.com/'
          }
        }))
        .then(function(res) {
          count.should.equal(1);
          setTimeoutTerminal.isDone().should.be.true;
          readyStateCheck.isDone().should.be.false;
          pageLoadSetCount.should.equal(1);
          res.status.should.equal(200);
          res.body.status.should.equal(28);
          res.body.value.should.equal('page load timeout');
          done();
        });
      });
    });
  });
  it('Jugaad screenshot for appium android', function(done) {
    var appiumForceSeleniumPort = 5555;

    var capability = helper.getCapabilities({browser: 'android', device: 'Samsung Galaxy S5 Mini', 'browserstack.video': false});
    var screenshot = helper.buildMac()
    .get('/custom_screenshot')
    .query(true)
    .reply(function(uri, body) {
      var options = helper.getParamsFromQuery(uri);
      var keyObject = helper.constants.global_registry[helper.SESSION_ID];
      var deviceConfig = helper.constants.emulatorDeviceSizes[keyObject.deviceName][keyObject.deviceOrientation];
      options.sessionId.should.equal(helper.SESSION_ID);
      options.x.should.equal(deviceConfig.x.toString());
      options.y.should.equal(deviceConfig.y.toString());
      options.width.should.equal(deviceConfig.width.toString());
      options.height.should.equal(deviceConfig.height.toString());
      options.os.should.equal('android');
      // Base 64 encoded string
      return helper.sessionStartedJSON('QSBiYXNlIDY0IGVuY29kZWQgc3RyaW5nLg==');
    });

    var postExecute = helper.selenium(null, null, appiumForceSeleniumPort)
      .post('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/execute')
      .query(true)
      .reply(function(uri, body) {
        body = JSON.parse(body);
        body.script.should.equal('return document.readyState');
        body.args.should.deep.equal([]);
        return helper.sessionStartedJSON('complete');
      });

    helper.doSelenium.startSession(capability, helper.selenium(null, null, appiumForceSeleniumPort))
    .then(() => helper.doSelenium.sendCommand({
      method: 'POST',
      uri: '/wd/hub/session/' + helper.SESSION_ID + '/url',
      action: function(uri, body) {
        body = JSON.parse(body);
        body.url.should.equal('http://www.google.com');
        return helper.sessionStartedJSON();
      },
      body: { url: 'http://www.google.com' }
    }))
    .then(function(res) {
      res.status.should.equal(200);
      res.body.sessionId.should.equal('1a2b3c4d5e6f');
      res.body.value.should.deep.equal({});
      res.body.status.should.equal(0);
    })
    .then(() => helper.doSelenium.sendCommand({
      method: 'GET',
      uri: '/wd/hub/session/' + helper.SESSION_ID + '/screenshot',
      action: function(uri, body) {
        var retval = helper.sessionStartedJSON();
        retval[1].status = 8; // some random number other than 0
        return retval;
      }
    }))
    .then(function(res) {
      postExecute.isDone().should.be.true;
      screenshot.isDone().should.be.true;
      res.status.should.equal(200);
      res.body.sessionId.should.equal('1a2b3c4d5e6f');
      res.body.value.should.deep.equal('QSBiYXNlIDY0IGVuY29kZWQgc3RyaW5nLg==');
      res.body.status.should.equal(0);
      done();
    });
  });
  xit('Jugaad screenshot for appium on iOS 9 Simulator');
  it('Page loading check for script timeout', function(done) {
    var capability = helper.getCapabilities({browser: 'ie'});
    var postExecute = helper.selenium()
                            .post('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/execute')
                            .query(true)
                            .reply(function(uri, body) {
                                    body = JSON.parse(body);
                                    body.script.should.equal('return document.activeElement');
                                    body.args.should.deep.equal([]);
                                    return helper.sessionStartedJSON('complete');
                                 });
    helper.doSelenium.startSession(capability, helper.selenium())
    .then(() => helper.doSelenium.sendCommand({
      method: 'POST',
      uri: '/wd/hub/session/' + helper.SESSION_ID + '/url',
      action: function(uri, body) {
        body = JSON.parse(body);
        body.url.should.equal('http://www.google.com');
        return [200, {sessionId: helper.SESSION_ID, value: {}, status: 28}];
      },
      body: { url: 'http://www.google.com' },
      noAssert: true
    }))
    .then(function(res) {
      postExecute.isDone().should.be.false;
      done();
    });
  });

  it('retry open URL in case of ios simulator and basic auth and acceptssl cert', function(done) {
    var appiumForceSeleniumPort = 5555;
    var capability = helper.getCapabilities({ device: 'iPhone 6 Plus', 'acceptSslCerts': true});

    var try1 = helper.selenium(null, null, appiumForceSeleniumPort)
      .post('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/url')
      .query(true)
      .reply(function(uri, body) {
        body = JSON.parse(body);
        body.url.should.equal('https://a:<EMAIL>');
        return helper.sessionStartedJSON();
      });

    var try2 = helper.selenium(null, null, appiumForceSeleniumPort)
      .post('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/url')
      .query(true)
      .reply(function(uri, body) {
        body = JSON.parse(body);
        body.url.should.equal('https://a:<EMAIL>');
        return helper.sessionStartedJSON();
      });

    helper.doSelenium.startSession(capability, helper.selenium(null, null, appiumForceSeleniumPort))
    .then(() => new Promise(function(resolve, reject) {
        helper.request.post('/wd/hub/session/' + helper.SESSION_ID + '/url')
        .send({ url: 'https://a:<EMAIL>' })
        .end(function(err, res) {
          try1.isDone().should.be.true;
          try2.isDone().should.be.true;
          resolve(res);
        });
      }))
    .then(function(res) {
      res.status.should.equal(200);
      res.body.status.should.equal(0);
      done();
    });
  });

  it('retry open URL in case of real ios with basic auth and acceptssl cert', function(done) {
    var capability = helper.getCapabilities({ device: 'iPhone 6S Plus', 'acceptSslCerts': true, 'realMobile': true});

    var try1 = helper.appiumMini()
      .post('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/url')
      .query(true)
      .reply(function(uri, body) {
        body = JSON.parse(body);
        body.url.should.equal('https://a:<EMAIL>');
        return helper.sessionStartedJSON();
      });

    var try2 = helper.appiumMini()
      .post('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/url')
      .query(true)
      .reply(function(uri, body) {
        body = JSON.parse(body);
        body.url.should.equal('https://a:<EMAIL>');
        return helper.sessionStartedJSON();
      });

    helper.doSelenium.startSession(capability, helper.appiumMini())
    .then(() => new Promise(function(resolve, reject) {
        helper.request.post('/wd/hub/session/' + helper.SESSION_ID + '/url')
        .send({ url: 'https://a:<EMAIL>' })
        .end(function(err, res) {
          try1.isDone().should.be.true;
          try2.isDone().should.be.true;
          resolve(res);
        });
      }))
    .then(function(res) {
      res.status.should.equal(200);
      res.body.status.should.equal(0);
      done();
    });
  });

  describe('AcceptSsl for firefox > 48', function() {
    describe('Mac', function() {
      it('calls endpoint for acceptssl_ff for firefox 48.0', function(done) {
        var capability = helper.getCapabilities({ browser: 'firefox' , browser_version: '48.0', os: 'OS X', os_version: 'El Capitan', acceptSslCerts: true });
        var acceptsslEndpoint = helper.buildMac()
          .get('/acceptssl_ff')
          .query(true)
          .delay(200)
          .reply(function(uri, body) {
            uri.should.equal('/acceptssl_ff');
            return 'random';
          });

        var postUrl = helper.selenium()
          .post('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/execute')
          .query(true)
          .delay(200)
          .reply(function(uri, body) {
            var jsonBody = JSON.parse(body);
            jsonBody.script.should.equal("return document.readyState");
            return [ 200, "{\"value\":\"complete\"}" ];
          });

        var postUrl = helper.selenium()
          .post('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/url')
          .query(true)
          .delayConnection(200)
          .reply(function(uri, body) {
            body = JSON.parse(body);
            body.url.should.equal('https://a:<EMAIL>');
            return [ 500, { sessionId: helper.SESSION_ID, value: {}, status: 0 }];
          });

        helper.doSelenium.startSession(capability, helper.selenium())
        .then(() => new Promise(function(resolve, reject) {
            helper.request.post('/wd/hub/session/' + helper.SESSION_ID + '/url')
            .send({ url: 'https://a:<EMAIL>' })
            .end(function(err, res) {
              postUrl.isDone().should.be.true;
              acceptsslEndpoint.isDone().should.be.true;
              resolve(res);
            });
          }))
        .then(function(res) {
          res.status.should.equal(200);
          res.body.status.should.equal(0);
          done();
        });
      });
    });
    describe('Windows', function() {
      it('calls endpoint for acceptssl_ff for firefox 48.0', function(done) {
        var capability = helper.getCapabilities({ browser: 'firefox' , browser_version: '48.0', os: 'Windows', os_version: '10', acceptSslCerts: true });
        var acceptsslEndpoint = helper.buildWin()
          .get('/acceptssl_ff')
          .query(true)
          .delay(200)
          .reply(function(uri, body) {
            uri.should.equal('/acceptssl_ff?numTabs=2');
            return 'random';
          });

        var postUrl = helper.selenium()
          .post('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/execute')
          .query(true)
          .delay(200)
          .reply(function(uri, body) {
            var jsonBody = JSON.parse(body);
            jsonBody.script.should.equal("return document.readyState");
            return [ 200, "{\"value\":\"complete\"}" ];
          });

        var postUrl = helper.selenium()
          .post('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/url')
          .query(true)
          .delayConnection(200)
          .reply(function(uri, body) {
            body = JSON.parse(body);
            body.url.should.equal('https://a:<EMAIL>');
            return [ 500, { sessionId: helper.SESSION_ID, value: {}, status: 0 }];
          });

        helper.doSelenium.startSession(capability, helper.selenium())
        .then(() => new Promise(function(resolve, reject) {
            helper.request.post('/wd/hub/session/' + helper.SESSION_ID + '/url')
            .send({ url: 'https://a:<EMAIL>' })
            .end(function(err, res) {
              postUrl.isDone().should.be.true;
              acceptsslEndpoint.isDone().should.be.true;
              resolve(res);
            });
          }))
        .then(function(res) {
          res.status.should.equal(200);
          res.body.status.should.equal(0);
          done();
        });
      });
      it('calls endpoint for acceptssl_ff for 50.0 firefox', function(done) {
        var capability = helper.getCapabilities({ browser: 'firefox' , browser_version: '50.0', os: 'Windows', os_version: '10', acceptSslCerts: true });
        var acceptsslEndpoint = helper.buildWin()
          .get('/acceptssl_ff')
          .query(true)
          .delay(200)
          .reply(function(uri, body) {
            uri.should.equal('/acceptssl_ff?numTabs=3');
            return 'random';
          });

        var postUrl = helper.selenium()
          .post('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/execute')
          .query(true)
          .delay(200)
          .reply(function(uri, body) {
            var jsonBody = JSON.parse(body);
            jsonBody.script.should.equal("return document.readyState");
            return [ 200, "{\"value\":\"complete\"}" ];
          });

        var postUrl = helper.selenium()
          .post('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/url')
          .query(true)
          .delayConnection(200)
          .reply(function(uri, body) {
            body = JSON.parse(body);
            body.url.should.equal('https://a:<EMAIL>');
            return [ 500, { sessionId: helper.SESSION_ID, value: {}, status: 0 }];
          });

        helper.doSelenium.startSession(capability, helper.selenium())
        .then(() => new Promise(function(resolve, reject) {
            helper.request.post('/wd/hub/session/' + helper.SESSION_ID + '/url')
            .send({ url: 'https://a:<EMAIL>' })
            .end(function(err, res) {
              postUrl.isDone().should.be.true;
              acceptsslEndpoint.isDone().should.be.true;
              resolve(res);
            });
          }))
        .then(function(res) {
          res.status.should.equal(200);
          res.body.status.should.equal(0);
          done();
        });
      });
    });
  });

  it('disallows open url for file:// scheme', function(done) {
    var capability = helper.getCapabilities();
    var postUrl = helper.selenium()
      .post('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/url')
      .query(true)
      .reply(function(uri, body) {
        return helper.sessionStartedJSON();
      });

    helper.doSelenium.startSession(capability)
      .then(() => new Promise(function(resolve, reject) {
        helper.request.post('/wd/hub/session/' + helper.SESSION_ID + '/url')
          .send({ url: 'file://Users/<USER>/Downloads/sample.txt' })
          .end(function(err, res) {
            postUrl.isDone().should.be.false;
            res.status.should.equal(500);
            res.body.status.should.equal(13);
            res.body.value.message.should.equal('Opening url with scheme \'file://\' is not supported');
            done();
          });
      }));
  });

  describe('Basic Auth on Edge', function() {
    it('calls endpoint to enter auth in case basic auth is present', function(done) {
      var capability = helper.getCapabilities({browser: 'Edge' , browser_version: '13', os: 'Windows', os_version: '10'});
      var authEdge = helper.buildWin()
      .get('/basic_auth_edge')
      .query(true)
      .delay(200)
      .reply(function(uri, body) {
        uri.should.equal('/basic_auth_edge?username=a&password=b&browser_version=13.0');
        return 'random';
      });

      var postUrl = helper.selenium()
        .post('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/url')
        .query(true)
        .delayConnection(200)
        .reply(function(uri, body) {
          body = JSON.parse(body);
          body.url.should.equal('https://xyz.com/');
          return helper.sessionStartedJSON();
        });

      helper.doSelenium.startSession(capability, helper.selenium())
      .then(() => new Promise(function(resolve, reject) {
          helper.request.post('/wd/hub/session/' + helper.SESSION_ID + '/url')
          .send({ url: 'https://a:<EMAIL>' })
          .end(function(err, res) {
            postUrl.isDone().should.be.true;
            authEdge.isDone().should.be.true;
            resolve(res);
          });
        }))
      .then(function(res) {
        res.status.should.equal(200);
        res.body.status.should.equal(0);
        done();
      });
    });
    it('basic auth edge with space in auth', function(done) {
      var capability = helper.getCapabilities({browser: 'Edge' , browser_version: '13', os: 'Windows', os_version: '10'});
      var authEdge = helper.buildWin()
      .get('/basic_auth_edge')
      .query(true)
      .delay(200)
      .reply(function(uri, body) {
        uri.should.equal('/basic_auth_edge?username=ab%20cd&password=wx%20yz&browser_version=13.0');
        return 'random';
      });

      var postUrl = helper.selenium()
        .post('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/url')
        .query(true)
        .delayConnection(200)
        .reply(function(uri, body) {
          body = JSON.parse(body);
          body.url.should.equal('https://xyz.com/');
          return helper.sessionStartedJSON();
        });

      helper.doSelenium.startSession(capability, helper.selenium())
      .then(() => new Promise(function(resolve, reject) {
          helper.request.post('/wd/hub/session/' + helper.SESSION_ID + '/url')
          .send({ url: 'https://ab cd:wx <EMAIL>' })
          .end(function(err, res) {
            postUrl.isDone().should.be.true;
            authEdge.isDone().should.be.true;
            resolve(res);
          });
        }))
      .then(function(res) {
        res.status.should.equal(200);
        res.body.status.should.equal(0);
        done();
      });
    });
    it('does not call endpoint to enter auth in case basic auth is not present', function(done) {
      var capability = helper.getCapabilities({browser: 'Edge' , browser_version: '13', os: 'Windows', os_version: '10'});
      var authEdge = helper.buildWin()
      .get('/basic_auth_edge')
      .query(true)
      .delay(200)
      .reply(function(uri, body) {
        return 'random';
      });

      var postUrl = helper.selenium()
        .post('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/url')
        .query(true)
        .delayConnection(200)
        .reply(function(uri, body) {
          body = JSON.parse(body);
          body.url.should.equal('https://xyz.com');
          return helper.sessionStartedJSON();
        });

      helper.doSelenium.startSession(capability, helper.selenium())
      .then(() => new Promise(function(resolve, reject) {
          helper.request.post('/wd/hub/session/' + helper.SESSION_ID + '/url')
          .send({ url: 'https://xyz.com' })
          .end(function(err, res) {
            postUrl.isDone().should.be.true;
            authEdge.isDone().should.not.be.true;
            resolve(res);
          });
        }))
      .then(function(res) {
        res.status.should.equal(200);
        res.body.status.should.equal(0);
        done();
      });
    });
    it('does call endpoint to enter auth in case basic auth is present and /url returns first', function(done) {
      var capability = helper.getCapabilities({browser: 'Edge' , browser_version: '13', os: 'Windows', os_version: '10'});
      var authEdge = helper.buildWin()
      .get('/basic_auth_edge')
      .query(true)
      .delay(200)
      .reply(function(uri, body) {
        uri.should.equal('/basic_auth_edge?username=a&password=b&browser_version=13.0');
        return 'random';
      });

      var postUrl = helper.selenium()
        .post('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/url')
        .query(true)
        .delayConnection(50)
        .reply(function(uri, body) {
          body = JSON.parse(body);
          body.url.should.equal('https://xyz.com/');
          return helper.sessionStartedJSON();
        });

      helper.doSelenium.startSession(capability, helper.selenium())
      .then(() => new Promise(function(resolve, reject) {
          helper.request.post('/wd/hub/session/' + helper.SESSION_ID + '/url')
          .send({ url: 'https://a:<EMAIL>' })
          .end(function(err, res) {
            postUrl.isDone().should.be.true;
            authEdge.isDone().should.be.true;
            resolve(res);
          });
        }))
      .then(function(res) {
        res.status.should.equal(200);
        res.body.status.should.equal(0);
        done();
      });
    });
    it('does not call endpoint to enter auth in case basic auth is not present and /url returns first', function(done) {
      var capability = helper.getCapabilities({browser: 'Edge' , browser_version: '13', os: 'Windows', os_version: '10'});
      var authEdge = helper.buildWin()
      .get('/basic_auth_edge')
      .query(true)
      .delay(200)
      .reply(function(uri, body) {
        return 'random';
      });

      var postUrl = helper.selenium()
        .post('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/url')
        .query(true)
        .delayConnection(50)
        .reply(function(uri, body) {
          body = JSON.parse(body);
          body.url.should.equal('https://xyz.com');
          return helper.sessionStartedJSON();
        });

      helper.doSelenium.startSession(capability, helper.selenium())
      .then(() => new Promise(function(resolve, reject) {
          helper.request.post('/wd/hub/session/' + helper.SESSION_ID + '/url')
          .send({ url: 'https://xyz.com' })
          .end(function(err, res) {
            postUrl.isDone().should.be.true;
            authEdge.isDone().should.not.be.true;
            resolve(res);
          });
        }))
      .then(function(res) {
        res.status.should.equal(200);
        res.body.status.should.equal(0);
        done();
      });
    });
    it('does not call endpoint to enter auth in case browser is not Edge and basic auth is present', function(done) {
      var capability = helper.getCapabilities({browser: 'IE' , browser_version: '11', os: 'Windows', os_version: '10'});
      var authEdge = helper.buildWin()
      .get('/basic_auth_edge')
      .query(true)
      .delay(200)
      .reply(function(uri, body) {
        return 'random';
      });

      var postUrl = helper.selenium()
        .post('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/url')
        .query(true)
        .delayConnection(200)
        .reply(function(uri, body) {
          body = JSON.parse(body);
          body.url.should.equal('https://a:<EMAIL>');
          return helper.sessionStartedJSON();
        });

      helper.doSelenium.startSession(capability, helper.selenium())
      .then(() => new Promise(function(resolve, reject) {
          helper.request.post('/wd/hub/session/' + helper.SESSION_ID + '/url')
          .send({ url: 'https://a:<EMAIL>' })
          .end(function(err, res) {
            postUrl.isDone().should.be.true;
            authEdge.isDone().should.not.be.true;
            resolve(res);
          });
        }))
      .then(function(res) {
        res.status.should.equal(200);
        res.body.status.should.equal(0);
        done();
      });
    });
    it('does not call endpoint to enter auth in case browser is not Edge and basic auth is not present', function(done) {
      var capability = helper.getCapabilities({browser: 'IE' , browser_version: '11', os: 'Windows', os_version: '10'});
      var authEdge = helper.buildWin()
      .get('/basic_auth_edge')
      .query(true)
      .delay(200)
      .reply(function(uri, body) {
        return 'random';
      });

      var postUrl = helper.selenium()
        .post('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/url')
        .query(true)
        .delayConnection(200)
        .reply(function(uri, body) {
          body = JSON.parse(body);
          body.url.should.equal('https://xyz.com');
          return helper.sessionStartedJSON();
        });

      helper.doSelenium.startSession(capability, helper.selenium())
      .then(() => new Promise(function(resolve, reject) {
          helper.request.post('/wd/hub/session/' + helper.SESSION_ID + '/url')
          .send({ url: 'https://xyz.com' })
          .end(function(err, res) {
            postUrl.isDone().should.be.true;
            authEdge.isDone().should.not.be.true;
            resolve(res);
          });
        }))
      .then(function(res) {
        res.status.should.equal(200);
        res.body.status.should.equal(0);
        done();
      });
    });
  });
  describe('File Upload on IE11W10', function() {
    this.timeout(0);
    var encodedFile = "UEsDBBQAAAAIAHmUGEmhr5SZJwAAACoAAAAVAAAAZmlsZV91cGxvYWRfanNvbi5qc29uq+ZSUFBKy8xJVbJSUCotyMlPTFHSAYmllObmVoIEs4rz85S4arkAUEsBAjQDFAAAAAgAeZQYSaGvlJknAAAAKgAAABUAAAAAAAAAAQAAAKSBAAAAAGZpbGVfdXBsb2FkX2pzb24uanNvblBLBQYAAAAAAQABAEMAAABaAAAAAAA=";
    var fileUploadedLocation = "C:\\Windows\\proxy\\f47b07c88fe2805dde1b8272d88fce329b13af2f\\upload8930572632992517421file\\file_upload_json.json";

    it('calls endpoint to enter file path', function(done) {
      var capability = helper.getCapabilities({browser: 'internet explorer' , browser_version: '11', os: 'Windows', os_version: '10'});
      var terminalFileUpload = helper.selenium()
        .post('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/file')
        .query(true)
        .reply(function(uri, body) {
          var bodyJSON = JSON.parse(body);
          bodyJSON.file.should.equal(encodedFile);
          return {"state":"success","sessionId":helper.TERMINAL_SESSION_ID,"value":fileUploadedLocation,"status":0};
        });
      var terminalSendKeys = helper.selenium()
        .post('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/element/0/value')
        .query(true)
        .delayConnection(70)
        .reply(function(uri, body) {
          var bodyJSON = JSON.parse(body);
          (Array.isArray(bodyJSON.value)).should.be.true;
          bodyJSON.value.length.should.equal(1);
          (bodyJSON.value[0]).should.equal(fileUploadedLocation);
          return {"state":"success","sessionId":helper.TERMINAL_SESSION_ID,"value":null,"status":0};
        });
      var customSendKeys = helper.buildWin()
        .get('/file_upload_ie11')
        .query(true)
        .delay(10)
        .reply(function(uri, body) {
          const fileUploadedLocationSplit = fileUploadedLocation.split("\\");
          const fileName = fileUploadedLocationSplit.pop();
          const asciiSafeFileNamePath = [...fileUploadedLocationSplit, `"${fileName}"`].join("\\");

          uri.should.equal('/file_upload_ie11?file_path=' + encodeURIComponent(asciiSafeFileNamePath));
          return 'done';
        });

      helper.doSelenium.startSession(capability, helper.selenium())
      .then(() => new Promise(function(resolve, reject) {
          helper.request.post('/wd/hub/session/' + helper.SESSION_ID + '/file')
          .send({ "file" : encodedFile })
          .end(function(err, res) {
            res.body.value.should.equal(fileUploadedLocation);
            res.body.status.should.equal(0);
            res.body.sessionId.should.equal(helper.SESSION_ID);
            terminalFileUpload.isDone().should.be.true;
            terminalSendKeys.isDone().should.be.false;
            customSendKeys.isDone().should.be.false;
            resolve(res);
          });
        }))
      .then(() => new Promise(function(resolve, reject) {
          helper.request.post('/wd/hub/session/' + helper.SESSION_ID + '/element/0/value')
          .send({ "value" : [ fileUploadedLocation ]})
          .end(function(err, res) {
            (res.body.value === null).should.be.true;
            res.body.status.should.equal(0);
            res.body.sessionId.should.equal(helper.SESSION_ID);
            terminalSendKeys.isDone().should.be.true;
            customSendKeys.isDone().should.be.true;
            resolve(res);
          });
        }))
      .then(function(res) {
        res.status.should.equal(200);
        res.body.status.should.equal(0);
        done();
      });
    });
    it('does not call endpoint for sendKeys in case os is not Windows 10', function(done) {
      var capability = helper.getCapabilities({browser: 'internet explorer' , browser_version: '11', os: 'Windows', os_version: '8.1'});
      var terminalFileUpload = helper.selenium()
        .post('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/file')
        .query(true)
        .reply(function(uri, body) {
          var bodyJSON = JSON.parse(body);
          bodyJSON.file.should.equal(encodedFile);
          return {"state":"success","sessionId":helper.TERMINAL_SESSION_ID,"value":fileUploadedLocation,"status":0};
        });
      var terminalSendKeys = helper.selenium()
        .post('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/element/0/value')
        .query(true)
        .delayConnection(70)
        .reply(function(uri, body) {
          var bodyJSON = JSON.parse(body);
          (Array.isArray(bodyJSON.value)).should.be.true;
          bodyJSON.value.length.should.equal(1);
          (bodyJSON.value[0]).should.equal(fileUploadedLocation);
          return {"state":"success","sessionId":helper.TERMINAL_SESSION_ID,"value":null,"status":0};
        });
      var customSendKeys = helper.buildWin()
        .get('/file_upload_ie11')
        .query(true)
        .delay(10)
        .reply(function(uri, body) {
          uri.should.equal('/file_upload_ie11?file_path=' + fileUploadedLocation);
          return 'done';
        });

      helper.doSelenium.startSession(capability, helper.selenium())
      .then(() => new Promise(function(resolve, reject) {
          helper.request.post('/wd/hub/session/' + helper.SESSION_ID + '/file')
          .send({ "file" : encodedFile })
          .end(function(err, res) {
            res.body.value.should.equal(fileUploadedLocation);
            res.body.status.should.equal(0);
            res.body.sessionId.should.equal(helper.SESSION_ID);
            terminalFileUpload.isDone().should.be.true;
            terminalSendKeys.isDone().should.be.false;
            customSendKeys.isDone().should.be.false;
            resolve(res);
          });
        }))
      .then(() => new Promise(function(resolve, reject) {
          helper.request.post('/wd/hub/session/' + helper.SESSION_ID + '/element/0/value')
          .send({ "value" : [ fileUploadedLocation ]})
          .end(function(err, res) {
            (res.body.value === null).should.be.true;
            res.body.status.should.equal(0);
            res.body.sessionId.should.equal(helper.SESSION_ID);
            terminalSendKeys.isDone().should.be.true;
            customSendKeys.isDone().should.be.false;
            resolve(res);
          });
        }))
      .then(function(res) {
        res.status.should.equal(200);
        res.body.status.should.equal(0);
        res.headers['source-from'].should.equal(helper.constants.NGINX_SOURCE_FROM_CODES.JAR);
        done();
      });
    });
  });
  it('retry open URL in case of basic auth and invalid cert in Safari', function(done) {
    var os = 'macyos', seleniumVersion = '2.45.0';
    var capability = helper.getCapabilities({browser: 'Safari' , browser_version: '8', 'acceptSslCerts': true});
    var popupCancel = helper.buildMac()
    .get('/safari_popup_cancel')
    .query(true)
    .delayConnection(200)
    .reply(function(uri, body) {
      return 'random';
    });

    var try1 = helper.selenium(os, seleniumVersion)
      .post('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/url')
      .query(true)
      .delayConnection(200)
      .reply(function(uri, body) {
        body = JSON.parse(body);
        body.url.should.equal('https://a:<EMAIL>');
        return helper.sessionStartedJSON();
      });

    var try2 = helper.selenium(os, seleniumVersion)
      .post('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/url')
      .query(true)
      .reply(function(uri, body) {
        body = JSON.parse(body);
        body.url.should.equal('https://a:<EMAIL>');
        return helper.sessionStartedJSON();
      });

    helper.doSelenium.startSession(capability, helper.selenium(os, seleniumVersion))
    .then(() => new Promise(function(resolve, reject) {
        helper.request.post('/wd/hub/session/' + helper.SESSION_ID + '/url')
        .send({ url: 'https://a:<EMAIL>' })
        .end(function(err, res) {
          try1.isDone().should.be.true;
          popupCancel.isDone().should.be.true;
          try2.isDone().should.be.true;
          resolve(res);
        });
      }))
    .then(function(res) {
      res.status.should.equal(200);
      res.body.status.should.equal(0);
      done();
    });
  });

  it('does not retry open URL in case safari popup cleanup is not called for basic auth and invalid cert in Safari', function(done) {
    var os = 'macyos', seleniumVersion = '2.45.0';
    var capability = helper.getCapabilities({browser: 'Safari' , browser_version: '8', 'acceptSslCerts': true});

    var popupCancel = helper.buildMac()
    .get('/safari_popup_cancel')
    .query(true)
    .delayConnection(200)
    .reply(function(uri, body) {
      return 'random';
    });

    var try1 = helper.selenium(os, seleniumVersion)
      .post('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/url')
      .query(true)
      .reply(function(uri, body) {
        body = JSON.parse(body);
        body.url.should.equal('https://a:<EMAIL>');
        return helper.sessionStartedJSON();
      });

    var try2 = helper.selenium(os, seleniumVersion)
      .post('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/url')
      .query(true)
      .reply(function(uri, body) {
        body = JSON.parse(body);
        body.url.should.equal('https://a:<EMAIL>');
        return helper.sessionStartedJSON();
      });

    helper.doSelenium.startSession(capability, helper.selenium(os, seleniumVersion))
    .then(() => new Promise(function(resolve, reject) {
        helper.request.post('/wd/hub/session/' + helper.SESSION_ID + '/url')
        .send({ url: 'https://a:<EMAIL>' })
        .end(function(err, res) {
          try1.isDone().should.be.true;
          popupCancel.isDone().should.be.false;
          try2.isDone().should.be.false;
          resolve(res);
        });
      }))
    .then(function(res) {
      res.status.should.equal(200);
      res.body.status.should.equal(0);
      done();
    });
  });

  it('retry open URL in case safari popup cancel returns after open url returns for basic auth and invalid cert in Safari', function(done) {
    var os = 'macyos', seleniumVersion = '2.45.0';
    var capability = helper.getCapabilities({browser: 'Safari' , browser_version: '8', 'acceptSslCerts': true});

    var popupCancel = helper.buildMac()
    .get('/safari_popup_cancel')
    .query(true)
    .delayConnection(300)
    .reply(function(uri, body) {
      return 'random';
    });

    var try1 = helper.selenium(os, seleniumVersion)
      .post('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/url')
      .query(true)
      .delayConnection(200)
      .reply(function(uri, body) {
        body = JSON.parse(body);
        body.url.should.equal('https://a:<EMAIL>');
        return helper.sessionStartedJSON();
      });

    var try2 = helper.selenium(os, seleniumVersion)
      .post('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/url')
      .query(true)
      .reply(function(uri, body) {
        body = JSON.parse(body);
        body.url.should.equal('https://a:<EMAIL>');
        return helper.sessionStartedJSON();
      });

    helper.doSelenium.startSession(capability, helper.selenium(os, seleniumVersion))
    .then(() => new Promise(function(resolve, reject) {
        helper.request.post('/wd/hub/session/' + helper.SESSION_ID + '/url')
        .send({ url: 'https://a:<EMAIL>' })
        .end(function(err, res) {
          try1.isDone().should.be.true;
          popupCancel.isDone().should.be.true;
          try2.isDone().should.be.true;
          resolve(res);
        });
      }))
    .then(function(res) {
      res.status.should.equal(200);
      res.body.status.should.equal(0);
      done();
    });
  });

  it('does not retry open URL in case safari popup cleanup returns no popup before open url for basic auth and invalid cert in Safari', function(done) {
    var os = 'macyos', seleniumVersion = '2.45.0';
    var capability = helper.getCapabilities({browser: 'Safari' , browser_version: '8', 'acceptSslCerts': true});

    var popupCancel = helper.buildMac()
    .get('/safari_popup_cancel')
    .query(true)
    .delayConnection(200)
    .reply(200, 'no popup');


    var try1 = helper.selenium(os, seleniumVersion)
      .post('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/url')
      .query(true)
      .delayConnection(300)
      .reply(function(uri, body) {
        body = JSON.parse(body);
        body.url.should.equal('https://a:<EMAIL>');
        return helper.sessionStartedJSON();
      });

    var try2 = helper.selenium(os, seleniumVersion)
      .post('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/url')
      .query(true)
      .reply(function(uri, body) {
        body = JSON.parse(body);
        body.url.should.equal('https://a:<EMAIL>');
        return helper.sessionStartedJSON();
      });

    helper.doSelenium.startSession(capability, helper.selenium(os, seleniumVersion))
    .then(() => new Promise(function(resolve, reject) {
        helper.request.post('/wd/hub/session/' + helper.SESSION_ID + '/url')
        .send({ url: 'https://a:<EMAIL>' })
        .end(function(err, res) {
          try1.isDone().should.be.true;
          popupCancel.isDone().should.be.true;
          try2.isDone().should.be.false;
          resolve(res);
        });
      }))
    .then(function(res) {
      res.status.should.equal(200);
      res.body.status.should.equal(0);
      done();
    });
  });

  it('does not retry open URL in case safari popup cleanup returns no popup after open url for basic auth and invalid cert in Safari', function(done) {
    var os = 'macyos', seleniumVersion = '2.45.0';
    var capability = helper.getCapabilities({browser: 'Safari' , browser_version: '8', 'acceptSslCerts': true});

    var popupCancel = helper.buildMac()
    .get('/safari_popup_cancel')
    .query(true)
    .delayConnection(300)
    .reply(200, 'no popup');

    var try1 = helper.selenium(os, seleniumVersion)
      .post('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/url')
      .query(true)
      .delayConnection(200)
      .reply(function(uri, body) {
        body = JSON.parse(body);
        body.url.should.equal('https://a:<EMAIL>');
        return helper.sessionStartedJSON();
      });

    var try2 = helper.selenium(os, seleniumVersion)
      .post('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/url')
      .query(true)
      .reply(function(uri, body) {
        body = JSON.parse(body);
        body.url.should.equal('https://a:<EMAIL>');
        return helper.sessionStartedJSON();
      });

    helper.doSelenium.startSession(capability, helper.selenium(os, seleniumVersion))
    .then(() => new Promise(function(resolve, reject) {
        helper.request.post('/wd/hub/session/' + helper.SESSION_ID + '/url')
        .send({ url: 'https://a:<EMAIL>' })
        .end(function(err, res) {
          try1.isDone().should.be.true;
          popupCancel.isDone().should.be.true;
          try2.isDone().should.be.false;
          resolve(res);
        });
      }))
    .then(function(res) {
      res.status.should.equal(200);
      res.body.status.should.equal(0);
      done();
    });
  });

  it('does not retry open URL in only basic auth in safari', function(done) {
    var os = 'macyos', seleniumVersion = '2.45.0';
    var capability = helper.getCapabilities({browser: 'Safari' , browser_version: '8'});

    var popupCancel = helper.buildMac()
    .get('/safari_popup_cancel')
    .query(true)
    .delayConnection(200)
    .reply(200, 'random');

    var try1 = helper.selenium(os, seleniumVersion)
      .post('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/url')
      .query(true)
      .delayConnection(200)
      .reply(function(uri, body) {
        body = JSON.parse(body);
        body.url.should.equal('http://a:<EMAIL>');
        return helper.sessionStartedJSON();
      });

    var try2 = helper.selenium(os, seleniumVersion)
      .post('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/url')
      .query(true)
      .reply(function(uri, body) {
        body = JSON.parse(body);
        body.url.should.equal('http://a:<EMAIL>');
        return helper.sessionStartedJSON();
      });

    helper.doSelenium.startSession(capability, helper.selenium(os, seleniumVersion))
    .then(() => new Promise(function(resolve, reject) {
        helper.request.post('/wd/hub/session/' + helper.SESSION_ID + '/url')
        .send({ url: 'http://a:<EMAIL>' })
        .end(function(err, res) {
          try1.isDone().should.be.true;
          popupCancel.isDone().should.be.false;
          try2.isDone().should.be.false;
          resolve(res);
        });
      }))
    .then(function(res) {
      res.status.should.equal(200);
      res.body.status.should.equal(0);
      done();
    });
  });

  it('does not retry open URL in only invalid cert in safari', function(done) {
    var os = 'macyos', seleniumVersion = '2.45.0';
    var capability = helper.getCapabilities({browser: 'Safari' , browser_version: '8', 'acceptSslCerts': true});

    var popupCancel = helper.buildMac()
    .get('/safari_popup_cancel')
    .query(true)
    .delayConnection(200)
    .reply(200, 'random');

    var try1 = helper.selenium(os, seleniumVersion)
      .post('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/url')
      .query(true)
      .delayConnection(200)
      .reply(function(uri, body) {
        body = JSON.parse(body);
        body.url.should.equal('https://xyz.com');
        return helper.sessionStartedJSON();
      });

    var try2 = helper.selenium(os, seleniumVersion)
      .post('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/url')
      .query(true)
      .reply(function(uri, body) {
        body = JSON.parse(body);
        body.url.should.equal('https://xyz.com');
        return helper.sessionStartedJSON();
      });

    helper.doSelenium.startSession(capability, helper.selenium(os, seleniumVersion))
    .then(() => new Promise(function(resolve, reject) {
        helper.request.post('/wd/hub/session/' + helper.SESSION_ID + '/url')
        .send({ url: 'https://xyz.com' })
        .end(function(err, res) {
          try1.isDone().should.be.true;
          popupCancel.isDone().should.be.false;
          try2.isDone().should.be.false;
          resolve(res);
        });
      }))
    .then(function(res) {
      res.status.should.equal(200);
      res.body.status.should.equal(0);
      done();
    });
  });

  it('retry same request in case of ECONNRESET', function(done) {
    var capability = helper.getCapabilities({browser: 'Firefox'});
    helper.doSelenium.startSession(capability)
    .then(() => {
      var try1 = helper.selenium()
      .post('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/url')
      .query(true)
      .replyWithError({code: 'ECONNRESET'});

      var try2 = helper.selenium()
      .post('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/url')
      .query(true)
      .reply(function(uri, body) {
        body = JSON.parse(body);
        body.url.should.equal('http://www.google.com');
        return helper.sessionStartedJSON();
      });
      return new Promise(function(resolve, reject) {
        helper.request.post('/wd/hub/session/' + helper.SESSION_ID + '/url')
        .send({ url: 'http://www.google.com' })
        .end(function(err, res) {
          try1.isDone().should.be.true;
          try2.isDone().should.be.true;
          resolve(res);
        });
      });
    })
    .then(function(res) {
      res.status.should.equal(200);
      res.body.status.should.equal(0);
      done();
    });
  });

  it('retry same request in case of ETIMEDOUT', function(done) {
    var capability = helper.getCapabilities({browser: 'Firefox'});
    helper.doSelenium.startSession(capability)
    .then(() => {
      var try1 = helper.selenium()
      .post('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/url')
      .query(true)
      .replyWithError({code: 'ETIMEDOUT'});

      var try2 = helper.selenium()
      .post('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/url')
      .query(true)
      .reply(function(uri, body) {
        body = JSON.parse(body);
        body.url.should.equal('http://www.google.com');
        return helper.sessionStartedJSON();
      });
      return new Promise(function(resolve, reject) {
        helper.request.post('/wd/hub/session/' + helper.SESSION_ID + '/url')
        .send({ url: 'http://www.google.com' })
        .end(function(err, res) {
          try1.isDone().should.be.true;
          try2.isDone().should.be.true;
          resolve(res);
        });
      });
    })
    .then(function(res) {
      res.status.should.equal(200);
      res.body.status.should.equal(0);
      done();
    });
  });
  xit('Open URL failure in local causes check URL status');
  it('file upload not supported for real iOS devices', function(done) {
    var capability = helper.getCapabilities({device: 'iPhone 6S Plus', realMobile: true});
    helper.doSelenium.startSession(capability, helper.appiumMini())
    .then(() => {
      helper.request.post('/wd/hub/session/' + helper.SESSION_ID + '/file')
      .send({file: 'Sample File data'})
      .end(function(err, res) {
        res.body.status.should.equal(13);
        res.body.value.message.should.equal('Appium error: File upload is not supported on real iOS devices');
        done();
      });
    });
  });
  it('file upload for real android devices', function(done) {
    var capability = helper.getCapabilities({device: 'Google Nexus 6', realMobile: true});
    var terminalServer = helper.buildMac()
      .get('/push_file')
      .query(true)
      .reply(function(uri, body) {
        return '/tmp/filename';
      });

    helper.doSelenium.startSession(capability, helper.appiumZotac())
      .then(() => helper.doSelenium.sendCommand({
        method: 'POST',
        uri: '/wd/hub/session/' + helper.SESSION_ID + '/file',
        action: function(uri, body) {
          return helper.sessionStartedJSON('/tmp/anotherFileName');
        },
        body: { file: 'Sample File Content' }
      })).then((res) => {
        terminalServer.isDone().should.be.true;
        res.body.value.should.equal('/tmp/filename');
        res.status.should.equal(200);
        done();
      });
  });
  it('Custom Screenshot for real mobile Samsung Tab4', function(done) {
    var capability = helper.getCapabilities({device: 'Samsung Galaxy Tab 4', realMobile: true});
    var terminalServer = helper.buildMac()
      .get('/custom_screenshot')
      .query(true)
      .reply(function(uri, body) {
        var options = helper.getParamsFromQuery(uri);
        var keyObject = helper.constants.global_registry[helper.SESSION_ID];
        var deviceConfig = helper.constants.realDeviceSizes[keyObject.deviceName][keyObject.deviceOrientation];
        options.device.should.equal('pqrstu');
        options.orientation.should.equal('landscape');
        options.x.should.equal(deviceConfig.x.toString());
        options.y.should.equal(deviceConfig.y.toString());
        options.width.should.equal(deviceConfig.width.toString());
        options.height.should.equal(deviceConfig.height.toString());
        options.os.should.equal('android');
        // Base 64 encoded string
        return helper.sessionStartedJSON('QSBiYXNlIDY0IGVuY29kZWQgc3RyaW5nLg==');
      });

    helper.doSelenium.startSession(capability, helper.appiumZotac())
      .then(() => helper.doSelenium.sendCommand({
        method: 'GET',
        uri: '/wd/hub/session/' + helper.SESSION_ID + '/screenshot',
        action: function(uri, body) {
          return helper.sessionStartedJSON();
        }
      })).then((res) => {
        terminalServer.isDone().should.be.true;
        res.status.should.equal(200);
        res.body.sessionId.should.equal('1a2b3c4d5e6f');
        res.body.value.should.deep.equal('QSBiYXNlIDY0IGVuY29kZWQgc3RyaW5nLg==');
        res.body.status.should.equal(0);
        done();
      });
  });
  it('Custom Screenshot for real iOS devices', function(done) {
    var capability = helper.getCapabilities({device: 'iPhone 6S Plus', realMobile: true});
    var terminalServer = helper.buildMac()
      .get('/custom_screenshot')
      .query(true)
      .reply(function(uri, body) {
        var options = helper.getParamsFromQuery(uri);
        var keyObject = helper.constants.global_registry[helper.SESSION_ID];
        options.sessionId.should.equal('1a2b3c4d5e6f');
        options.device.should.equal('abcdxyz');
        options.orientation.should.equal('portrait');
        // Base 64 encoded string
        return helper.sessionStartedJSON('QSBiYXNlIDY0IGVuY29kZWQgc3RyaW5nLg==');
      });

    helper.doSelenium.startSession(capability, helper.appiumMini())
    .then(() => {
      helper.request.get('/wd/hub/session/' + helper.SESSION_ID + '/screenshot')
      .end(function(err, res) {
        terminalServer.isDone().should.be.true;
        res.status.should.equal(200);
        res.body.sessionId.should.equal('1a2b3c4d5e6f');
        res.body.value.should.deep.equal('QSBiYXNlIDY0IGVuY29kZWQgc3RyaW5nLg==');
        res.body.status.should.equal(0);
        done();
      });
    });
  });
  describe('Custom Screenshot capability for desktop mac browsers', function(done) {
    it('does call endpoint to get custom desktop screenshots for mac chrome with correct capability', function(done) {
      var capability = helper.getCapabilities({ 'browserstack.customScreenshots': true, browser: 'chrome' , browser_version: '45', os: 'OS X', os_version: 'El Capitan' });

      var macTerminalScope = helper.buildMac()
      .get('/desktop_screenshot')
      .query(true)
      .reply(function(uri, body) {
        uri.should.equal('/desktop_screenshot?x=0&y=80&width=0&height=80');
        return JSON.stringify({ sessionId: helper.TERMINAL_SESSION_ID, status: 0, value: 'QSBCYXNlNjQgZW5jb2RlZCBpbWFnZQo=' });
      });

      helper.doSelenium.startSession(capability, helper.selenium())
        .then(() => helper.doSelenium.sendCommand({
          method: 'GET',
          uri: '/wd/hub/session/' + helper.SESSION_ID + '/screenshot',
          action: function(uri, body) {
            return [ 200, { sessionId: helper.TERMINAL_SESSION_ID, value: 'QW4gaW1hZ2UgcmV0cmlldmVkIGZyb20gc2VsZW5pdW0gamFyCg==', status: 0 } ];
          }})
        .then(function(res) {
          macTerminalScope.isDone().should.be.true;
          res.status.should.equal(200);
          res.body.status.should.equal(0);
          res.body.sessionId.should.equal(helper.SESSION_ID);
          res.body.value.should.equal('QSBCYXNlNjQgZW5jb2RlZCBpbWFnZQo=');
          done();
        }));
    });
    it('does call endpoint to get custom desktop screenshots for mac chrome with string capability', function(done) {
      var capability = helper.getCapabilities({ 'browserstack.customScreenshots': 'true', browser: 'chrome' , browser_version: '45', os: 'OS X', os_version: 'El Capitan' });

      var macTerminalScope = helper.buildMac()
      .get('/desktop_screenshot')
      .query(true)
      .reply(function(uri, body) {
        uri.should.equal('/desktop_screenshot?x=0&y=80&width=0&height=80');
        return JSON.stringify({ sessionId: helper.TERMINAL_SESSION_ID, status: 0, value: 'QSBCYXNlNjQgZW5jb2RlZCBpbWFnZQo=' });
      });

      helper.doSelenium.startSession(capability, helper.selenium())
        .then(() => helper.doSelenium.sendCommand({
          method: 'GET',
          uri: '/wd/hub/session/' + helper.SESSION_ID + '/screenshot',
          action: function(uri, body) {
            return [ 200, { sessionId: helper.TERMINAL_SESSION_ID, value: 'QW4gaW1hZ2UgcmV0cmlldmVkIGZyb20gc2VsZW5pdW0gamFyCg==', status: 0 } ];
          }})
        .then(function(res) {
          macTerminalScope.isDone().should.be.true;
          res.status.should.equal(200);
          res.body.status.should.equal(0);
          res.body.sessionId.should.equal(helper.SESSION_ID);
          res.body.value.should.equal('QSBCYXNlNjQgZW5jb2RlZCBpbWFnZQo=');
          done();
        }));
    });
    it('does not call endpoint to get custom desktop screenshots for mac chrome with capability false', function(done) {
      var capability = helper.getCapabilities({ 'browserstack.customScreenshots': false, browser: 'chrome' , browser_version: '45', os: 'OS X', os_version: 'El Capitan' });

      var macTerminalScope = helper.buildMac()
      .get('/desktop_screenshot')
      .query(true)
      .reply(function(uri, body) {
        uri.should.equal('/desktop_screenshot?x=0&y=80&width=0&height=80');
        return JSON.stringify({ sessionId: helper.TERMINAL_SESSION_ID, status: 0, value: 'QSBCYXNlNjQgZW5jb2RlZCBpbWFnZQo=' });
      });

      helper.doSelenium.startSession(capability, helper.selenium())
        .then(() => helper.doSelenium.sendCommand({
          method: 'GET',
          uri: '/wd/hub/session/' + helper.SESSION_ID + '/screenshot',
          action: function(uri, body) {
            return [ 200, { sessionId: helper.TERMINAL_SESSION_ID, value: 'QW4gaW1hZ2UgcmV0cmlldmVkIGZyb20gc2VsZW5pdW0gamFyCg==', status: 0 } ];
          }})
        .then(function(res) {
          macTerminalScope.isDone().should.be.false;
          res.status.should.equal(200);
          res.body.status.should.equal(0);
          res.body.sessionId.should.equal(helper.SESSION_ID);
          res.body.value.should.equal('QW4gaW1hZ2UgcmV0cmlldmVkIGZyb20gc2VsZW5pdW0gamFyCg==');
          done();
        }));
    });
    it('does not call endpoint to get custom desktop screenshots for mac chrome even with correct capability for windows', function(done) {
      var capability = helper.getCapabilities({ 'browserstack.customScreenshots': true, browser: 'chrome' , browser_version: '45', os: 'Windows', os_version: '10' });

      var macTerminalScope = helper.buildMac()
      .get('/desktop_screenshot')
      .query(true)
      .reply(function(uri, body) {
        uri.should.equal('/desktop_screenshot?x=0&y=80&width=0&height=80');
        return JSON.stringify({ sessionId: helper.TERMINAL_SESSION_ID, status: 0, value: 'QSBCYXNlNjQgZW5jb2RlZCBpbWFnZQo=' });
      });

      helper.doSelenium.startSession(capability, helper.selenium())
        .then(() => helper.doSelenium.sendCommand({
          method: 'GET',
          uri: '/wd/hub/session/' + helper.SESSION_ID + '/screenshot',
          action: function(uri, body) {
            return [ 200, { sessionId: helper.TERMINAL_SESSION_ID, value: 'QW4gaW1hZ2UgcmV0cmlldmVkIGZyb20gc2VsZW5pdW0gamFyCg==', status: 0 } ];
          }})
        .then(function(res) {
          macTerminalScope.isDone().should.be.false;
          res.status.should.equal(200);
          res.body.status.should.equal(0);
          res.body.sessionId.should.equal(helper.SESSION_ID);
          res.body.value.should.equal('QW4gaW1hZ2UgcmV0cmlldmVkIGZyb20gc2VsZW5pdW0gamFyCg==');
          done();
        }));
    });
  });
  describe('Set and Get orientation for Real Mobile Android', function() {
    it('Custom Get Orientation for real iOS should work the normal way', function(done) {
      var capability = helper.getCapabilities({ device: 'iPhone 6S Plus', realMobile: true });

      var terminalServer = helper.appiumMini()
        .get('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/orientation')
        .query(true)
        .reply(function(uri, body) {
          return [ 200, { status: 0, sessionId: helper.SESSION_ID, value: "PORTRAIT" } ];
        });

      helper.doSelenium.startSession(capability, helper.appiumMini())
      .then(() => helper.doSelenium.sendCommand({
        method: 'GET',
        uri: '/wd/hub/session/' + helper.SESSION_ID + '/orientation',
        noAssert: true
      }))
      .then(function(res) {
        terminalServer.isDone().should.be.true;
        res.status.should.equal(200);
        res.body.status.should.equal(0);
        res.body.value.should.equal('PORTRAIT');
        done();
      });
    });
    it('Custom Set Orientation for real iOS should not use the endpoint', function(done) {
      var capability = helper.getCapabilities({ device: 'iPhone 6S Plus', realMobile: true });

      var terminalServer = helper.buildMac()
        .post('/orientation')
        .query(true)
        .reply(function(uri, body) {
          uri.should.equal('/orientation');
          body.should.equal('device=abcdxyz&orientation=LANDSCAPE&currentOrientation=PORTRAIT');
          return [ 200, "LANDSCAPE" ];
        });

      helper.doSelenium.startSession(capability, helper.appiumMini())
      .then(() => helper.doSelenium.sendCommand({
        method: 'POST',
        uri: '/wd/hub/session/' + helper.SESSION_ID + '/orientation',
        body: { orientation: 'LANDSCAPE' },
        noAssert: true
      }))
      .then(function(res) {
        terminalServer.isDone().should.be.false;
        res.status.should.equal(200);
        done();
      });
    });
    it('Custom Get Orientation for real Android', function(done) {
      var capability = helper.getCapabilities({ device: 'Motorola Moto X 2nd Gen', realMobile: true });

      var terminalServer = helper.buildMac()
        .get('/orientation')
        .query(true)
        .reply(function(uri, body) {
          uri.should.equal('/orientation?device=pqrstu');
          return [ 200, "portrait" ];
        });

      helper.doSelenium.startSession(capability, helper.appiumZotac())
      helper.doSelenium.startSession(capability, helper.appiumZotac())
      .then(() => helper.doSelenium.sendCommand({
        method: 'GET',
        uri: '/wd/hub/session/' + helper.SESSION_ID + '/orientation',
        noAssert: true
      }))
      .then(function(res) {
        terminalServer.isDone().should.be.true;
        res.status.should.equal(200);
        res.body.status.should.equal(0);
        res.body.value.should.equal('PORTRAIT');
        done();
      });
    });
    it('Custom Set Orientation for real Android', function(done) {
      var capability = helper.getCapabilities({ device: 'Motorola Moto X 2nd Gen', realMobile: true });

      var terminalServer = helper.buildMac()
        .post('/orientation')
        .query(true)
        .reply(function(uri, body) {
          uri.should.equal('/orientation');
          body.should.equal('device=pqrstu&orientation=LANDSCAPE');
          return [ 200, "LANDSCAPE" ];
        });

      helper.doSelenium.startSession(capability, helper.appiumZotac())
      .then(() => helper.doSelenium.sendCommand({
        method: 'POST',
        uri: '/wd/hub/session/' + helper.SESSION_ID + '/orientation',
        body: { orientation: 'LANDSCAPE' },
        noAssert: true
      }))
      .then(function(res) {
        terminalServer.isDone().should.be.true;
        res.status.should.equal(200);
        res.body.status.should.equal(0);
        res.body.value.should.equal('LANDSCAPE');
        done();
      });
    });
    });
  describe("GET and SET orientation for emulators Appium", function() {
    var appiumForceSeleniumPort = 5555;

    it('gets orientation', function(done) {
      var capability = helper.getCapabilities({ device: 'Samsung Galaxy S5 Mini', browser: 'android', 'browserstack.video': false });
      helper.doSelenium.startSession(capability, helper.selenium(null, null, appiumForceSeleniumPort))
          .then(() => {
            helper.request.get('/wd/hub/session/' + helper.SESSION_ID + '/orientation')
                .end(function(err, res) {
                  res.status.should.equal(200);
                  res.body.value.should.equal('portrait');
                  done();
                });
          });
    });

    it('gets with different orientation', function(done) {
      var capability = helper.getCapabilities({ device: 'Samsung Galaxy S5 Mini', browser: 'android', 'browserstack.video': false, 'deviceOrientation': 'landscape' });
      helper.doSelenium.startSession(capability, helper.selenium(null, null, appiumForceSeleniumPort))
          .then(() => {
            helper.request.get('/wd/hub/session/' + helper.SESSION_ID + '/orientation')
                .end(function(err, res) {
                  res.status.should.equal(200);
                  res.body.value.should.equal('landscape');
                  done();
                });
          });
    });

    it('sets orientation', function(done) {
      var capability = helper.getCapabilities({ device: 'Samsung Galaxy S5 Mini', browser: 'android', 'browserstack.video': false });

      var terminalServer = helper.buildMac()
          .get('/switch_orientation')
          .query(true)
          .reply(function(uri, body) {
            uri.should.match(/\/switch_orientation.*landscape/);
            return [200, "LANDSCAPE"]
          });
      helper.doSelenium.startSession(capability, helper.selenium(null, null, appiumForceSeleniumPort))
          .then(() => helper.doSelenium.sendCommand({
            method: 'POST',
            uri: '/wd/hub/session/' + helper.SESSION_ID + '/orientation',
            body: { orientation: 'LANDSCAPE' },
            noAssert: true
            }))
          .then(function(res) {
            terminalServer.isDone().should.be.true;
            res.status.should.equal(200);
            res.body.status.should.equal(0);
            res.body.value.should.equal('LANDSCAPE');
            done();
          });

    });

    it('sets orientation to portrait and responds to GET call', function(done) {
      var capability = helper.getCapabilities({ device: 'Samsung Galaxy S5 Mini', browser: 'android', 'browserstack.video': false });

      var terminalServer = helper.buildMac()
          .get('/switch_orientation')
          .query(true)
          .reply(function(uri, body) {
            uri.should.match(/\/switch_orientation.*portrait/);
            return [200, "PORTRAIT"]
          });
      helper.doSelenium.startSession(capability, helper.selenium(null, null, appiumForceSeleniumPort))
          .then(() => helper.doSelenium.sendCommand({
            method: 'POST',
            uri: '/wd/hub/session/' + helper.SESSION_ID + '/orientation',
            body: { orientation: 'PORTRAIT' },
            noAssert: true
          }))
          .then(function(res) {
            terminalServer.isDone().should.be.true;
            res.status.should.equal(200);
            res.body.status.should.equal(0);
            res.body.value.should.equal('PORTRAIT');
          })
          .then(() => helper.request.get('/wd/hub/session/' + helper.SESSION_ID + '/orientation')
              .then(function(res) {
                res.body.value.toLowerCase().should.equal('portrait');
                done();
              }).catch(function(err) {
                throw err;
              })
          )
    });

    it('sets orientation to landscape and responds to GET call when asked for reverseLandscape', function(done) {
      var capability = helper.getCapabilities({ device: 'Samsung Galaxy S5 Mini', browser: 'android', 'browserstack.video': false });

      var terminalServer = helper.buildMac()
          .get('/switch_orientation')
          .query(true)
          .reply(function(uri, body) {
            uri.should.match(/\/switch_orientation.*landscape/);
            return [200, "LANDSCAPE"]
          });
      helper.doSelenium.startSession(capability, helper.selenium(null, null, appiumForceSeleniumPort))
          .then(() => helper.doSelenium.sendCommand({
            method: 'POST',
            uri: '/wd/hub/session/' + helper.SESSION_ID + '/orientation',
            body: { orientation: 'reverseLandscape' },
            noAssert: true
          }))
          .then(function(res) {
            terminalServer.isDone().should.be.true;
            res.status.should.equal(200);
            res.body.status.should.equal(0);
            res.body.value.should.equal('LANDSCAPE');
          })
          .then(() => helper.request.get('/wd/hub/session/' + helper.SESSION_ID + '/orientation')
              .then(function(res) {
                res.body.value.toLowerCase().should.equal('landscape');
                done();
              }).catch(function(err) {
                throw err;
              })
          )
    });
    it('sets orientation as portrait and responds to GET call when asked for garbled orientation', function(done) {
      var capability = helper.getCapabilities({ device: 'Samsung Galaxy S5 Mini', browser: 'android', 'browserstack.video': false });

      var terminalServer = helper.buildMac()
          .get('/switch_orientation')
          .query(true)
          .reply(function(uri, body) {
            uri.should.match(/\/switch_orientation.*portrait/);
            return [200, "PORTRAIT"]
          });
      helper.doSelenium.startSession(capability, helper.selenium(null, null, appiumForceSeleniumPort))
          .then(() => helper.doSelenium.sendCommand({
            method: 'POST',
            uri: '/wd/hub/session/' + helper.SESSION_ID + '/orientation',
            body: { orientation: 'lolorientation' },
            noAssert: true
          }))
          .then(function(res) {
            terminalServer.isDone().should.be.true;
            res.status.should.equal(200);
            res.body.status.should.equal(0);
            res.body.value.should.equal('PORTRAIT');
          })
          .then(() => helper.request.get('/wd/hub/session/' + helper.SESSION_ID + '/orientation')
              .then(function(res) {
                res.body.value.toLowerCase().should.equal('portrait');
                done();
              }).catch(function(err) {
                throw err;
              })
          )
    });

    it('shouldn\'t call switch_orientation for non appium', function(done) {
      var capability = helper.getCapabilities({ device: 'Samsung Galaxy S4', browser: 'android', 'browserstack.video': false });

      var terminalServer = helper.buildMac()
          .get('/switch_orientation')
          .query(true)
          .reply(function(uri, body) {
            uri.should.match(/\/switch_orientation.*portrait/);
            return [200, "PORTRAIT"]
          });
      helper.doSelenium.startSession(capability, helper.nonAppium())
          .then(() => helper.doSelenium.sendCommand({
            method: 'POST',
            uri: '/wd/hub/session/' + helper.SESSION_ID + '/orientation',
            body: { orientation: 'lolorientation' },
            noAssert: true
          }))
          .then(function(res) {
            terminalServer.isDone().should.be.false;
            res.status.should.equal(200);
            done();
          });
    });
  })
  describe("Execute after /url checks", function() {
    it("Does not runs javascript after open url for acceptSsl on Safari other than 11", function(done) {
      var capability = helper.getCapabilities({ browser: 'safari' , browser_version: '10.0', os: 'OS X', os_version: 'Sierra', acceptSslCerts: true });
      var executeScriptEndpoint = helper.selenium(null, "3.4.0")
        .post('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/execute')
        .query(true)
        .reply(function(uri, body) {
          return 'random';
        });

      var postUrl = helper.selenium(null, "3.4.0")
        .post('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/url')
        .query(true)
        .reply(function(uri, body) {
          body = JSON.parse(body);
          body.url.should.equal('https://xyz.com');
          return [ 200, { sessionId: helper.SESSION_ID, value: {}, status: 0 }];
        });

      helper.doSelenium.startSession(capability, helper.selenium(null, "3.4.0"))
        .then(() => new Promise(function(resolve, reject) {
          helper.request.post('/wd/hub/session/' + helper.SESSION_ID + '/url')
            .send({ url: 'https://xyz.com' })
            .end(function(err, res) {
              postUrl.isDone().should.be.true;
              executeScriptEndpoint.isDone().should.be.false;
              res.status.should.equal(200);
              res.body.status.should.equal(0);
              done();
            });
        }));
    });
    it("Runs javascript after open url for acceptSsl on Safari 11", function(done) {
      var capability = helper.getCapabilities({ browser: 'safari' , browser_version: '11.0', os: 'OS X', os_version: 'High Sierra', acceptSslCerts: true });
      var executeScriptEndpoint = helper.selenium(null, "3.4.0")
        .post('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/execute')
        .query(true)
        .reply(function(uri, body) {
          return 'random';
        });

      var postUrl = helper.selenium(null, "3.4.0")
        .post('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/url')
        .query(true)
        .reply(function(uri, body) {
          body = JSON.parse(body);
          body.url.should.equal('https://xyz.com');
          return [ 200, { sessionId: helper.SESSION_ID, value: {}, status: 0 }];
        });

      helper.doSelenium.startSession(capability, helper.selenium(null, "3.4.0"))
        .then(() => new Promise(function(resolve, reject) {
          helper.request.post('/wd/hub/session/' + helper.SESSION_ID + '/url')
            .send({ url: 'https://xyz.com' })
            .end(function(err, res) {
              postUrl.isDone().should.be.true;
              executeScriptEndpoint.isDone().should.be.true;
              res.status.should.equal(200);
              res.body.status.should.equal(0);
              done();
            });
        }));
    });
  });
  describe('test UdpKeys and diagnostics', function() {
    it('Gets title along with readyState on requests after every open url', function(done) {
      var capability = helper.getCapabilities({ 'browserstack.local': true });

      var openUrl = helper.selenium()
        .post('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/url')
        .query(true)
        .reply(function (uri, body) {
          body = JSON.parse(body);
          body.url.should.equal('https://twitter.com');
          return helper.sessionStartedJSON();
        });
      var getUrl = helper.selenium()
        .get('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/url')
        .reply(function (uri, body) {
          return helper.sessionStartedJSON('https://twitter.com');
        });
      var postExecute = helper.selenium()
        .post('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/execute')
        .query(true)
        .reply(function(uri, body) {
          body = JSON.parse(body);
          body.script.should.equal('return [document.readyState, document.title]');
          body.args.should.deep.equal([]);
          return helper.sessionStartedJSON(['complete', 'Twitter']);
        });

      helper.doSelenium.startSession(capability)
      .then(() => helper.doSelenium.sendCommand({
        method: 'POST',
        uri: '/wd/hub/session/' + helper.SESSION_ID + '/url',
        body: { url: 'https://twitter.com' },
        noAssert: true
      }))
      .then(() => helper.doSelenium.sendCommand({
        method: 'GET',
        uri: '/wd/hub/session/' + helper.SESSION_ID + '/url',
        noAssert: true
      }))
      .then(function(res) {
        openUrl.isDone().should.be.true;
        postExecute.isDone().should.be.true;
        getUrl.isDone().should.be.true;
        res.status.should.equal(200);
        done();
      });
    });

    it('Should not check for title on open url requests', function(done) {
      var capability = helper.getCapabilities({ 'browserstack.local': true });

      var openUrl = helper.selenium()
        .post('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/url')
        .query(true)
        .reply(function (uri, body) {
          body = JSON.parse(body);
          body.url.should.equal('https://twitter.com');
          return helper.sessionStartedJSON();
        });

      var postExecute = helper.selenium()
        .post('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/execute')
        .query(true)
        .reply(function(uri, body) {
          body = JSON.parse(body);
          body.script.should.equal('return [document.readyState, document.title]');
          body.args.should.deep.equal([]);
          return helper.sessionStartedJSON(['complete', 'Twitter']);
        });

      helper.doSelenium.startSession(capability)
      .then(() => helper.doSelenium.sendCommand({
        method: 'POST',
        uri: '/wd/hub/session/' + helper.SESSION_ID + '/url',
        body: { url: 'https://twitter.com' },
        noAssert: true
      }))
      .then(function(res) {
        openUrl.isDone().should.be.true;
        postExecute.isDone().should.be.false;
        res.status.should.equal(200);
        done();
      });
    });

    it('Should not check for title along with readyState on requests after every open url for non-local sessions', function(done) {
      var capability = helper.getCapabilities({ 'browserstack.local': false });

      var openUrl = helper.selenium()
        .post('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/url')
        .query(true)
        .reply(function (uri, body) {
          body = JSON.parse(body);
          body.url.should.equal('https://twitter.com');
          return helper.sessionStartedJSON();
        });
      var getUrl = helper.selenium()
        .get('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/url')
        .reply(function (uri, body) {
          return helper.sessionStartedJSON('https://twitter.com');
        });
      var postExecute = helper.selenium()
        .post('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/execute')
        .query(true)
        .reply(function(uri, body) {
          body = JSON.parse(body);
          body.script.should.equal('return document.readyState');
          body.args.should.deep.equal([]);
          return helper.sessionStartedJSON(['complete', 'Twitter']);
        });

      helper.doSelenium.startSession(capability)
      .then(() => helper.doSelenium.sendCommand({
        method: 'POST',
        uri: '/wd/hub/session/' + helper.SESSION_ID + '/url',
        body: { url: 'https://twitter.com' },
        noAssert: true
      }))
      .then(() => helper.doSelenium.sendCommand({
        method: 'GET',
        uri: '/wd/hub/session/' + helper.SESSION_ID + '/url',
        noAssert: true
      }))
      .then(function(res) {
        openUrl.isDone().should.be.true;
        postExecute.isDone().should.be.true;
        getUrl.isDone().should.be.true;
        res.status.should.equal(200);
        done();
      });
    });

  });
  describe("Do title check after /url", function () {
    it("Does not check for title after open url for acceptSsl on ios other than ios11", function (done) {
      var capability = helper.getCapabilities({ device: 'iPhone 7', 'realMobile': true, acceptSslCerts: true });
      var getTitle = helper.appiumMini()
        .get('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/title')
        .reply(function (uri, body) {
          return helper.sessionStartedJSON({"value":"This Connection Is Not Private"});
        });

      var openUrl = helper.appiumMini()
        .post('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/url')
        .query(true)
        .reply(function(uri, body) {
          body = JSON.parse(body);
          body.url.should.equal('https://somerandomtext.badssl.com/');
          return helper.sessionStartedJSON();
        });

      helper.doSelenium.startSession(capability, helper.appiumMini())
      .then(() => new Promise(function (resolve, reject) {
        helper.request.post('/wd/hub/session/' + helper.SESSION_ID + '/url')
        .send({ url: 'https://somerandomtext.badssl.com/' })
        .end(function (err, res) {
          openUrl.isDone().should.be.true;
          getTitle.isDone().should.be.false;
          res.status.should.equal(200);
          res.body.status.should.equal(0);
          done();
        });
      }))
    });
    it("Does not check for title after open url for acceptSsl on ios11 for http url", function (done) {
      var capability = helper.getCapabilities({ device: 'iPhone 8', 'realMobile': true, acceptSslCerts: true });
      var getTitle = helper.appiumMini()
        .get('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/title')
        .reply(function (uri, body) {
          return helper.sessionStartedJSON({"value":"This Connection Is Not Private"});
        });

      var openUrl = helper.appiumMini()
        .post('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/url')
        .query(true)
        .reply(function(uri, body) {
          body = JSON.parse(body);
          body.url.should.equal('http://www.browserstack.com/local-debug/http');
          return helper.sessionStartedJSON();
        });

      helper.doSelenium.startSession(capability, helper.appiumMini())
      .then(() => new Promise(function (resolve, reject) {
        helper.request.post('/wd/hub/session/' + helper.SESSION_ID + '/url')
        .send({ url: 'http://www.browserstack.com/local-debug/http' })
        .end(function (err, res) {
          openUrl.isDone().should.be.true;
          getTitle.isDone().should.be.false;
          res.status.should.equal(200);
          res.body.status.should.equal(0);
          done();
        });
      }))
    });
    it("Should check for title after open url for acceptSsl on ios11 for https url", function (done) {
      var capability = helper.getCapabilities({ device: 'iPhone 8', 'realMobile': true, acceptSslCerts: true });
      this.timeout(0)
      var getTitle = helper.appiumMini()
        .get('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/title')
        .reply(function (uri, body) {
          return helper.sessionStartedJSON("This Connection Is Not Private");
        });

      var executeScript = helper.appiumMini()
        .post('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/execute', {"script":"CertificateWarningController.visitInsecureWebsiteWithTemporaryBypass();","args":[]})
        .reply(function(uri, body) {
          return {status: 0};
        });

      var openUrl = helper.appiumMini()
        .post('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/url')
        .query(true)
        .reply(function(uri, body) {
          body = JSON.parse(body);
          body.url.should.equal('https://www.browserstack.com/local-debug/http');
          return helper.sessionStartedJSON();
        });

      var checkPageLoadMock = helper.appiumMini()
        .post('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/execute')
        .reply(function (uri, body) {
          return {"value": "complete"};
        });

      helper.doSelenium.startSession(capability, helper.appiumMini())
      .then((res) => new Promise(function (resolve, reject) {
        helper.request.post('/wd/hub/session/' + helper.SESSION_ID + '/url')
        .send({ url: 'https://www.browserstack.com/local-debug/http' })
        .end(function (err, res) {
          openUrl.isDone().should.be.true;
          getTitle.isDone().should.be.true;
          res.status.should.equal(200);
          done();
        });
      }))
    });
    it("Should NOT inject JS after each page load in iOS NJB if safariAllowPopups is set not true.", function (done) {
      var capability = helper.getCapabilities({ device: 'iPhone 8', 'realMobile': true });
      this.timeout(0)

      var injectScriptMock = helper.appiumMini()
        .post('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/execute')
        .reply(function (uri, body) {
          return {"status": 0};
        });

      var openUrl = helper.appiumMini()
        .post('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/url')
        .query(true)
        .reply(function(uri, body) {
          body = JSON.parse(body);
          body.url.should.equal('https://www.browserstack.com/local-debug/http');
          return helper.sessionStartedJSON();
        });

      helper.doSelenium.startSession(capability, helper.appiumMini())
      .then((res) => new Promise(function (resolve, reject) {
        helper.request.post('/wd/hub/session/' + helper.SESSION_ID + '/url')
        .send({ url: 'https://www.browserstack.com/local-debug/http' })
        .end(function (err, res) {
          openUrl.isDone().should.be.true;
          injectScriptMock.isDone().should.be.false;
          res.status.should.equal(200);
          done();
        });
      }))
    });
    it("Should inject JS after each page load in iOS NJB if safariAllowPopups is set to true.", function (done) {
      var capability = helper.getCapabilities({ device: 'iPhone 8', 'realMobile': true, safariAllowPopups: true });
      this.timeout(0)

      var checkPageLoadMock = helper.appiumMini()
        .post('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/execute')
        .reply(function (uri, body) {
          return {"value": "complete"};
        });


      var injectScriptMock = helper.appiumMini()
        .post('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/execute')
        .reply(function (uri, body) {
          return {"status": 0};
        });

      var openUrl = helper.appiumMini()
        .post('/wd/hub/session/' + helper.TERMINAL_SESSION_ID + '/url')
        .query(true)
        .reply(function(uri, body) {
          body = JSON.parse(body);
          body.url.should.equal('https://www.browserstack.com/local-debug/http');
          return helper.sessionStartedJSON();
        });

      helper.doSelenium.startSession(capability, helper.appiumMini())
      .then((res) => new Promise(function (resolve, reject) {
        helper.request.post('/wd/hub/session/' + helper.SESSION_ID + '/url')
        .send({ url: 'https://www.browserstack.com/local-debug/http' })
        .end(function (err, res) {
          openUrl.isDone().should.be.true;
          checkPageLoadMock.isDone().should.be.true;
          injectScriptMock.isDone().should.be.true;
          res.status.should.equal(200);
          done();
        });
      }))
    });
  });
});

describe('Test for /wd/hub/session/:sessionId/execute/sync', function() {
  it('should return 200 for /wd/hub/session/:sessionId/execute/sync', function(done) {
    var capability = helper.getCapabilities({ browser: 'chrome' , browser_version: '45', os: 'Windows', os_version: '10' });
    helper.doSelenium.startSession(capability, helper.selenium())
    .then(() => helper.doSelenium.sendCommand({
      method: 'POST',
      uri: '/wd/hub/session/' + helper.SESSION_ID + '/execute/sync',
      body: {
        script: 'return 1;',
        args: []
      },
      noAssert: true
    })).then(function(res) {
      res.status.should.equal(200);
      done();
    });
  });

  it('should return 200 for /wd/hub/session/:sessionId/execute with click', function(done) {
    var capability = helper.getCapabilities({ browser: 'chrome' , browser_version: '55', os: 'Windows', os_version: '10' });
    helper.constants.INSTRUMENT_EXECUTE_CLICK = true;
    helper.doSelenium.startSession(capability, helper.selenium())
    .then(() => {
      helper.constants.global_registry[helper.SESSION_ID].user_id = 2;
    })
    .then(() => helper.doSelenium.sendCommand({
      method: 'POST',
      uri: '/wd/hub/session/' + helper.SESSION_ID + '/execute',
      body: {
        script: 'document.getElementById("signupModalProductButton").click();',
        args: []
      },
      noAssert: true
    })).then(function(res) {
      res.status.should.equal(200);
      done();
      helper.constants.INSTRUMENT_EXECUTE_CLICK = false;
    });
  });

  it('should return 200 for /wd/hub/session/:sessionId/execute with click with no user_id', function(done) {
    var capability = helper.getCapabilities({ browser: 'chrome' , browser_version: '55', os: 'Windows', os_version: '10' });
    helper.constants.INSTRUMENT_EXECUTE_CLICK = true;
    helper.doSelenium.startSession(capability, helper.selenium())
    .then(() => helper.doSelenium.sendCommand({
      method: 'POST',
      uri: '/wd/hub/session/' + helper.SESSION_ID + '/execute',
      body: {
        script: 'document.getElementById("signupModalProductButton").click();',
        args: []
      },
      noAssert: true
    })).then(function(res) {
      res.status.should.equal(200);
      done();
      helper.constants.INSTRUMENT_EXECUTE_CLICK = false;
    });
  });

  it('should return 200 for /wd/hub/session/:sessionId/execute/sync', function(done) {
    var capability = helper.getCapabilities({ browser: 'chrome' , browser_version: '45', os: 'Windows', os_version: '10' });
    helper.doSelenium.startSession(capability, helper.selenium())
    .then(() => helper.doSelenium.sendCommand({
      method: 'POST',
      uri: '/wd/hub/session/' + helper.SESSION_ID + '/execute/sync',
      body: {
        script: 'browserstack_executor: {"action": "fileExists", "arguments": {"fileName": "<file name>"}}',
        args: []
      },
      noAssert: true
    })).then(function(res) {
      res.status.should.equal(200);
      done();
    });
  });
});

describe('Call launchApp', function() {
  let launchAppSpy;

  beforeEach(() => {
    launchAppSpy = sinon.stub(LaunchAppHandler.prototype, 'processCommand', (requestStateObj) => {
      requestStateObj.response.end();
      done();
    });
  })

  afterEach(() => {
    launchAppSpy.restore();
  })

  it('calls launchApp for ios and resign App true', function(done) {
    launchAppSpy.restore();
    var capability = helper.getCapabilities({ device: 'iPhone 6S Plus', realMobile: true, customFullReset: 'true', "browserstack.resignApp": "true", orig_os: "ios" });

    launchAppSpy = sinon.stub(LaunchAppHandler.prototype, 'processCommand', (requestStateObj) => {
      requestStateObj.response.end();
      done();
    });

    helper.doSelenium.startSession(capability, helper.appiumMini())
    .then(() => helper.doSelenium.sendCommand({
      method: 'POST',
      uri: '/wd/hub/session/' + helper.SESSION_ID + '/app/launch',
      noAssert: true
    }));
  });

  it('calls launchApp for ios and resign App true for mobile: launchApp', function(done) {
    launchAppSpy.restore();
    var capability = helper.getCapabilities({ device: 'iPhone 6S Plus', realMobile: true, customFullReset: 'true', "browserstack.resignApp": "true", orig_os: "ios", bundleId: 'com.abcd.xyz' });

    launchAppSpy = sinon.stub(LaunchAppHandler.prototype, 'processCommand', (requestStateObj) => {
      requestStateObj.response.end();
      done();
    });

    helper.doSelenium.startSession(capability, helper.appiumMini())
    .then(() => helper.doSelenium.sendCommand({
      method: 'POST',
      uri: '/wd/hub/session/' + helper.SESSION_ID + '/execute/sync',
      body: {
        script: 'mobile: launchApp',
        args: [{ bundleId: 'com.abcd.xyz' }]
      },
      noAssert: true
    }));
  });

  it('should not call launchApp for ios and resign App true for mobile: launchApp for different bundle ids', function(done) {
    launchAppSpy.restore();
    var capability = helper.getCapabilities({ device: 'iPhone 6S Plus', realMobile: true, customFullReset: 'true', "browserstack.resignApp": "true", orig_os: "ios", bundleId: 'com.abcd.xyz' });

    helper.doSelenium.startSession(capability, helper.appiumMini())
      .then((res) => new Promise(function (resolve, reject) {
        helper.request.post('/wd/hub/session/' + helper.SESSION_ID + '/execute/sync')
          .send({ script: 'mobile: launchApp', args: [{ bundleId: 'com.abcd.wxyz' }] })
          .end(function (err, res) {
            done();
          });
      }));
  });

  it('calls launchApp for android and resign App true', function(done) {
    launchAppSpy.restore();
    var capability = helper.getCapabilities({ device: 'Google Nexus 6', realMobile: true, customFullReset: 'true', "browserstack.resignApp": "true", orig_os: "android" });

    launchAppSpy = sinon.stub(LaunchAppHandler.prototype, 'processCommand', (requestStateObj) => {
      requestStateObj.response.end();
      done();
    });

    helper.doSelenium.startSession(capability, helper.appiumZotac())
    .then(() => helper.doSelenium.sendCommand({
      method: 'POST',
      uri: '/wd/hub/session/' + helper.SESSION_ID + '/app/launch',
      noAssert: true
    }));
  });

  it('calls launchApp for ios when resign App is not sent', function(done) {
    launchAppSpy.restore();
    var capability = helper.getCapabilities({ device: 'iPhone 6S Plus', realMobile: true, customFullReset: 'true', orig_os: "ios" });

    launchAppSpy = sinon.stub(LaunchAppHandler.prototype, 'processCommand', (requestStateObj) => {
      requestStateObj.response.end();
      done();
    });

    helper.doSelenium.startSession(capability, helper.appiumMini())
    .then(() => helper.doSelenium.sendCommand({
      method: 'POST',
      uri: '/wd/hub/session/' + helper.SESSION_ID + '/app/launch',
      noAssert: true
    }))
  });

  it('calls launchApp for android and resignApp is not sent', function(done) {
    launchAppSpy.restore();
    var capability = helper.getCapabilities({ device: 'Google Nexus 6', realMobile: true, customFullReset: 'true', orig_os: "android" });

    launchAppSpy = sinon.stub(LaunchAppHandler.prototype, 'processCommand', (requestStateObj) => {
      requestStateObj.response.end();
      done();
    });

    helper.doSelenium.startSession(capability, helper.appiumZotac())
    .then(() => helper.doSelenium.sendCommand({
      method: 'POST',
      uri: '/wd/hub/session/' + helper.SESSION_ID + '/app/launch',
      noAssert: true
    }));
  });

  it('does not call launchApp for ios when resign App is false', function(done) {
    launchAppSpy.restore();
    var capability = helper.getCapabilities({ device: 'iPhone 6S Plus', realMobile: true, customFullReset: 'true', "browserstack.resignApp": "false", orig_os: "ios" });

    helper.doSelenium.startSession(capability, helper.appiumMini())
      .then((res) => new Promise(function (resolve, reject) {
        helper.request.post('/wd/hub/session/' + helper.SESSION_ID + '/app/launch')
          .end(function (err, res) {
            done();
          });
      }));
  });

  it('should call launchApp for android when resign App is false', function(done) {
    launchAppSpy.restore();
    var capability = helper.getCapabilities({ device: 'Google Nexus 6', realMobile: true, customFullReset: 'true', "browserstack.resignApp": "false", orig_os: "android" });

    launchAppSpy = sinon.stub(LaunchAppHandler.prototype, 'processCommand', (requestStateObj) => {
      requestStateObj.response.end();
      done();
    });

    helper.doSelenium.startSession(capability, helper.appiumZotac())
    .then(() => helper.doSelenium.sendCommand({
      method: 'POST',
      uri: '/wd/hub/session/' + helper.SESSION_ID + '/app/launch',
      noAssert: true
    }));
  });
});

describe('Push File', function() {

  const encodedFile = "/9j/4AAQSkZJRgABAQEAZABkAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKC";

  it('push allowed for android to whitelisted path /sdcard/Pictures/.', function(done) {
    const capability = helper.AppAutomateCapabilities();
    const filePushPath = '/sdcard/Pictures/bs_logo.jpg';

    helper.doSelenium.startSession(capability, helper.selenium(undefined, undefined, helper.APPIUM_PORT), { isAppAutomate: true })
      .then(() => helper.doSelenium.sendCommand({
        method: 'POST',
        uri: `/wd/hub/session/${helper.SESSION_ID}/appium/device/push_file`,
        body: {
          path: filePushPath,
          data: encodedFile,
        },
        noAssert: true
      })).then((res) => {
        res.status.should.equal(200);
        res.text.should.equal("");
        done();
      });
  });

  it('push allowed for android to whitelisted path /sdcard/Pictures/. for mobile: pushFile', function(done) {
    const capability = helper.AppAutomateCapabilities();
    const filePushPath = '/sdcard/Pictures/bs_logo.jpg';

    helper.doSelenium.startSession(capability, helper.selenium(undefined, undefined, helper.APPIUM_PORT), { isAppAutomate: true })
      .then(() => helper.doSelenium.sendCommand({
        method: 'POST',
        uri: `/wd/hub/session/${helper.SESSION_ID}/execute/sync`,
        body: {
          script: 'mobile: pushFile',
          args: [{ remotePath: filePushPath, payload: encodedFile }]
        },
        noAssert: true
      })).then((res) => {
        res.status.should.equal(200);
        res.text.should.equal("");
        done();
      });
  });

  it('push allowed for android to whitelisted path /sdcard/Download/.', function(done) {
    const capability = helper.AppAutomateCapabilities();
    const filePushPath = '/sdcard/Download/bs_logo.jpg';

    helper.doSelenium.startSession(capability, helper.selenium(undefined, undefined, helper.APPIUM_PORT), { isAppAutomate: true })
      .then(() => helper.doSelenium.sendCommand({
        method: 'POST',
        uri: `/wd/hub/session/${helper.SESSION_ID}/appium/device/push_file`,
        body: {
          path: filePushPath,
          data: encodedFile,
        },
        noAssert: true
      })).then((res) => {
        res.status.should.equal(200);
        res.text.should.equal("");
        done();
      });
  });

  it('push allowed for android to non-whitelisted path for Automate', function(done) {
    const capability = helper.getCapabilities({ device: 'Google Nexus 6', orig_os: 'android', realMobile: true});
    const filePushPath = '/storage/self/primary/Hello.jpg';

    helper.doSelenium.startSession(capability, helper.appiumZotac())
      .then(() => helper.doSelenium.sendCommand({
        method: 'POST',
        uri: `/wd/hub/session/${helper.SESSION_ID}/appium/device/push_file`,
        body: {
          path: filePushPath,
          data: encodedFile,
        },
        noAssert: true
      })).then((res) => {
        res.status.should.equal(200);
        res.text.should.equal("");
        done();
      });
  });


  it('push allowed for android to whitelisted path /sdcard/Android/data/.', function(done) {
    const capability = helper.AppAutomateCapabilities();
    const filePushPath = '/sdcard/Android/data/bs_logo.jpg';

    helper.doSelenium.startSession(capability, helper.selenium(undefined, undefined, helper.APPIUM_PORT), { isAppAutomate: true })
      .then(() => helper.doSelenium.sendCommand({
        method: 'POST',
        uri: `/wd/hub/session/${helper.SESSION_ID}/appium/device/push_file`,
        body: {
          path: filePushPath,
          data: encodedFile,
        },
        noAssert: true
      })).then((res) => {
        res.status.should.equal(200);
        res.text.should.equal("");
        done();
      });
  });
  it('push not allowed for android to non-whitelisted path', function(done) {
    const capability = helper.AppAutomateCapabilities();
    const filePushPath = '/data/local/templ/bs_logo.jpg';

    helper.doSelenium.startSession(capability, helper.selenium(undefined, undefined, helper.APPIUM_PORT), { isAppAutomate: true })
      .then(() => helper.doSelenium.sendCommand({
        method: 'POST',
        uri: `/wd/hub/session/${helper.SESSION_ID}/appium/device/push_file`,
        body: {
          path: filePushPath,
          data: encodedFile,
        },
        noAssert: true
      })).then((res) => {
        res.status.should.equal(200);
        JSON.parse(res.text).value.message.should.match(/<your_app_package>/);
        JSON.parse(res.text).status.should.equal(13);
        done();
      });
  });

  it('push not allowed for android to non-whitelisted path for Android 11', function(done) {
    const capability = helper.AppAutomateCapabilities({"device": "Google Pixel 5", "os_version": "11.0"});
    const filePushPath = '/data/local/templ/bs_logo.jpg';

    helper.doSelenium.startSession(capability, helper.selenium(undefined, undefined, helper.APPIUM_PORT), { isAppAutomate: true })
      .then(() => helper.doSelenium.sendCommand({
        method: 'POST',
        uri: `/wd/hub/session/${helper.SESSION_ID}/appium/device/push_file`,
        body: {
          path: filePushPath,
          data: encodedFile,
        },
        noAssert: true
      })).then((res) => {
        res.status.should.equal(200);
        JSON.parse(res.text).value.message.should.not.match(/<your_app_package>/);
        JSON.parse(res.text).status.should.equal(13);
        done();
      });
  });

  it('push allowed for android to whitelisted symlinked path /storage/self/primary/.', function(done) {
    const capability = helper.AppAutomateCapabilities();
    const filePushPath = '/storage/self/primary/Hello.jpg';

    helper.doSelenium.startSession(capability, helper.selenium(undefined, undefined, helper.APPIUM_PORT), { isAppAutomate: true })
      .then(() => helper.doSelenium.sendCommand({
        method: 'POST',
        uri: `/wd/hub/session/${helper.SESSION_ID}/appium/device/push_file`,
        body: {
          path: filePushPath,
          data: encodedFile,
        },
        noAssert: true
      })).then((res) => {
        res.status.should.equal(200);
        JSON.parse(res.text).status.should.equal(13);
        done();
      });
  });
});

describe('Install multiplApks', function() {
  it('should throw invalid JSON error for invalid json', function(done) {
    const capability = helper.AppAutomateCapabilities();

    helper.doSelenium.startSession(capability, helper.selenium(undefined, undefined, helper.APPIUM_PORT), { isAppAutomate: true })
      .then(() => helper.doSelenium.sendCommand({
        method: 'POST',
        uri: `/wd/hub/session/${helper.SESSION_ID}/execute`,
        body: "Hi",
        noAssert: true
      })).then((res) => {
        res.status.should.equal(500);
        done();
      });
  });

  it('should not throw error if request data does not have key script', function(done) {
    const capability = helper.AppAutomateCapabilities();

    helper.doSelenium.startSession(capability, helper.selenium(undefined, undefined, helper.APPIUM_PORT), { isAppAutomate: true })
      .then(() => helper.doSelenium.sendCommand({
        method: 'POST',
        uri: `/wd/hub/session/${helper.SESSION_ID}/execute`,
        body: {
          "random": "mobile:scroll"
        },
        noAssert: true
      })).then((res) => {
        res.status.should.equal(200);
        res.text.should.equal("");
        done();
      });
  });

  it('should not throw error if command is not installMultipleApks', function(done) {
    const capability = helper.AppAutomateCapabilities();

    helper.doSelenium.startSession(capability, helper.selenium(undefined, undefined, helper.APPIUM_PORT), { isAppAutomate: true })
      .then(() => helper.doSelenium.sendCommand({
        method: 'POST',
        uri: `/wd/hub/session/${helper.SESSION_ID}/execute`,
        body: {
          script: "mobile:scroll"
        },
        noAssert: true
      })).then((res) => {
        res.status.should.equal(200);
        res.text.should.equal("");
        done();
      });
  });

  it('should throw error if command is installMultipleApks', function(done) {
    const capability = helper.AppAutomateCapabilities();

    helper.doSelenium.startSession(capability, helper.selenium(undefined, undefined, helper.APPIUM_PORT), { isAppAutomate: true })
      .then(() => helper.doSelenium.sendCommand({
        method: 'POST',
        uri: `/wd/hub/session/${helper.SESSION_ID}/execute`,
        body: {
          script: "mobile:installMultipleApks"
        },
        noAssert: true
      })).then((res) => {
        res.status.should.equal(200);
        JSON.parse(res.text).status.should.equal(13);
        JSON.parse(res.text).value.message.should.equal(constants.COMMAND_ERROR_MESSAGES['multipleApksNotSupported']);
        done();
      });
  });
});

describe('Get Location', function() {
  it('should raise custom error message for ios sessions with appium >= 1.20.2 ', function(done) {
    const capability = helper.AppAutomateCapabilities({"device":"iPhone 8", "browserstack.appium_version":"1.20.2"});
    helper.doSelenium.startSession(capability, helper.selenium(undefined, undefined, helper.APPIUM_PORT), { isAppAutomate: true })
      .then(() => helper.doSelenium.sendCommand({
        method: 'GET',
        uri: `/wd/hub/session/${helper.SESSION_ID}/location`,
        body: {},
        noAssert: true
      })).then((res) => {
        res.status.should.equal(500);
        JSON.parse(res.text).status.should.equal(13);
        done();
      });
  });

  it('should not raise custom error message for ios sessions with appium < 1.20.2 ', function(done) {
    const capability = helper.AppAutomateCapabilities({"device":"iPhone 8", "browserstack.appium_version":"1.17.0"});
    helper.doSelenium.startSession(capability, helper.selenium(undefined, undefined, helper.APPIUM_PORT), { isAppAutomate: true })
      .then(() => helper.doSelenium.sendCommand({
        method: 'GET',
        uri: `/wd/hub/session/${helper.SESSION_ID}/location`,
        body: {},
        noAssert: true
      })).then((res) => {
        res.status.should.equal(200);
        res.text.should.equal("");
        done();
      });
  });

  it('should not raise custom error message for android sessions', function(done) {
    const capability = helper.AppAutomateCapabilities({"device":"Google Nexus 6"});
    helper.doSelenium.startSession(capability, helper.selenium(undefined, undefined, helper.APPIUM_PORT), { isAppAutomate: true })
      .then(() => helper.doSelenium.sendCommand({
        method: 'GET',
        uri: `/wd/hub/session/${helper.SESSION_ID}/location`,
        body: {},
        noAssert: true
      })).then((res) => {
        res.status.should.equal(200);
        res.text.should.equal("");
        done();
      });
  });
});

describe('isLockedForAppSettingsFlow', function() {
  it('should raise custom error message in case isLockedForAppSettingsFlow', function(done) {
    const capability = helper.AppAutomateCapabilities({"device":"iPhone 8", "browserstack.appium_version":"1.17.0"});
    helper.doSelenium.startSession(capability, helper.selenium(undefined, undefined, helper.APPIUM_PORT), { isAppAutomate: true })
      .then(() => {
        helper.constants.global_registry[helper.SESSION_ID].isLockedForAppSettingsFlow = true;
      })
      .then(() => helper.doSelenium.sendCommand({
        method: 'POST',
        uri: `/wd/hub/session/${helper.SESSION_ID}/execute`,
        body: {
          script: "mobile:scroll"
        },
        noAssert: true
      })).then((res) => {
        res.status.should.equal(500);
        JSON.parse(res.text).status.should.equal(13);
        done();
      });
  });

  it('should not raise custom error message in case isLockedForAppSettingsFlow is false', function(done) {
    const capability = helper.AppAutomateCapabilities({"device":"iPhone 8", "browserstack.appium_version":"1.17.0"});
    helper.doSelenium.startSession(capability, helper.selenium(undefined, undefined, helper.APPIUM_PORT), { isAppAutomate: true })
      .then(() => {
        helper.constants.global_registry[helper.SESSION_ID].isLockedForAppSettingsFlow = false;
      })
      .then(() => helper.doSelenium.sendCommand({
        method: 'POST',
        uri: `/wd/hub/session/${helper.SESSION_ID}/execute`,
        body: {
          script: "mobile:scroll"
        },
        noAssert: true
      })).then((res) => {
        res.status.should.equal(200);
        res.text.should.equal("");
        done();
      });
  });
})
