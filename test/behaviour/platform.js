var helper = require('./helper');

describe('Platform Specific Capabilities', function() {
  describe('Appium', function() {
    var appiumForceSeleniumPort = 5555;

    describe('iOS', function() {
      it('should return correct capabilities for ios', function(done) {
        var requestBody = helper.getCapabilities({ device: 'iPhone 6 Plus', realMobile: true });
        delete requestBody.desiredCapabilities['browser'];

        var railsScope = helper.buildRails(),
            macTerminalScope = helper.buildMac();

        var seleniumScope = helper.selenium(null, null, appiumForceSeleniumPort)
          .post('/wd/hub/session')
          .query(true)
          .reply(function(uri, body) {
            return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
          });

        helper.request.post('/wd/hub/session')
        .auth('test', 'test')
        .send(requestBody)
        .end(function(err, res) {
          railsScope.isDone().should.be.true;
          macTerminalScope.isDone().should.be.true;
          seleniumScope.isDone().should.be.true;
          res.status.should.equal(200);
          res.body.sessionId.should.equal(helper.SESSION_ID);
          res.body.value.browserName.should.equal('safari');
          res.body.value.platformName.should.equal('iOS');
          //res.body.value.platformVersion.should.equal('8.3');
          res.body.value.deviceName.should.equal('iPhone 6 Plus');
          res.body.value.deviceOrientation.should.equal('PORTRAIT');
          res.body.value.newCommandTimeout.should.equal(0);
          res.body.value.safariIgnoreFraudWarning.should.equal(true);
          res.body.value.noReset.should.equal(true);
          res.body.status.should.equal(0);
          done();
        });
      });
    });

    describe('Android', function() {
      it('should return correct capabilities for android', function(done) {
        var requestBody = helper.getCapabilities({ device: 'Samsung Galaxy S5 Mini' });
        delete requestBody.desiredCapabilities['browser'];

        var railsScope = helper.buildRails(),
            macTerminalScope = helper.buildMac();

        var seleniumScope = helper.selenium(null, null, appiumForceSeleniumPort)
          .post('/wd/hub/session')
          .query(true)
          .reply(function(uri, body) {
            return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
          });

        helper.request.post('/wd/hub/session')
        .auth('test', 'test')
        .send(requestBody)
        .end(function(err, res) {
          railsScope.isDone().should.be.true;
          macTerminalScope.isDone().should.be.true;
          seleniumScope.isDone().should.be.true;
          res.status.should.equal(200);
          res.body.sessionId.should.equal(helper.SESSION_ID);
          res.body.value.browserName.should.equal('Browser');
          res.body.value.platformName.should.equal('Android');
          res.body.value.platformVersion.should.equal('4.4');
          res.body.value.deviceName.should.equal('Android Emulator');
          res.body.value.newCommandTimeout.should.equal(0);
          res.body.status.should.equal(0);
          done();
        });
      });
    });
  });
  describe('Desktop', function() {
    describe('Internet Explorer', function() {
      xit('should not add ie.ensureCleanSession for ie = 6', function(done) {
        var requestBody = helper.getCapabilities({
              browser: 'INTERNET EXPLORER',
              browser_version: 6,
              os: 'Windows',
              os_version: 'XP'
            }),
            railsScope = helper.buildRails(),
            winTerminalScope = helper.buildWin();

        var seleniumScope = helper.selenium()
          .post('/wd/hub/session')
          .query(true)
          .reply(function(uri, body) {
            return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
          });

        helper.request.post('/wd/hub/session')
        .auth('test', 'test')
        .send(requestBody)
        .end(function(err, res) {
          railsScope.isDone().should.be.true;
          winTerminalScope.isDone().should.be.true;
          seleniumScope.isDone().should.be.true;
          res.status.should.equal(200);
          res.body.value.should.have.any.keys('browser', 'browser_version', 'os', 'os_version');
          res.body.sessionId.should.equal(helper.SESSION_ID);
          res.body.value.should.not.have.keys('ie.ensureCleanSession');
          res.body.value['browserstack.ie.noFlash'].should.equal('false');
          res.body.value.initialBrowserUrl.should.equal('about:blank');
          res.body.status.should.equal(0);
          done();
        });
      });
      it('adds initialBrowserUrl for ie != 6', function(done) {
        var requestBody = helper.getCapabilities({
              browser: 'INTERNET EXPLORER',
              browser_version: 11,
              os: 'Windows',
              os_version: '10'
            }),
            railsScope = helper.buildRails(),
            winTerminalScope = helper.buildWin();

        var seleniumScope = helper.selenium()
          .post('/wd/hub/session')
          .query(true)
          .reply(function(uri, body) {
            return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
          });

        helper.request.post('/wd/hub/session')
        .auth('test', 'test')
        .send(requestBody)
        .end(function(err, res) {
          railsScope.isDone().should.be.true;
          winTerminalScope.isDone().should.be.true;
          seleniumScope.isDone().should.be.true;
          res.status.should.equal(200);
          res.body.value.should.have.any.keys('browser', 'browser_version', 'os', 'os_version');
          res.body.sessionId.should.equal(helper.SESSION_ID);
          // TODO check with the team if we need it or we can skip it. ('browserstack.ie.noFlash')
          res.body.value.initialBrowserUrl.should.equal('about:blank');
          res.body.status.should.equal(0);
          done();
        });
      });
    });
    describe('Chrome', function() {
      it('should change enableTimeline in chromeOptions chrome > 41', function(done) {
        var requestBody = helper.getCapabilities(),
            railsScope = helper.buildRails(),
            macTerminalScope = helper.buildMac();
        requestBody['desiredCapabilities']['browser_version'] = 42;
        requestBody['desiredCapabilities']['chromeOptions'] = {
          perfLoggingPrefs: {
            enableTimeline: true
          }
        };

        var seleniumScope = helper.selenium()
          .post('/wd/hub/session')
          .query(true)
          .reply(function(uri, body) {
            return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
          });

        helper.request.post('/wd/hub/session')
        .auth('test', 'test')
        .send(requestBody)
        .end(function(err, res) {
          railsScope.isDone().should.be.true;
          macTerminalScope.isDone().should.be.true;
          seleniumScope.isDone().should.be.true;
          res.status.should.equal(200);
          res.body.value.should.have.any.keys('browser', 'browser_version', 'os', 'os_version');
          res.body.sessionId.should.equal(helper.SESSION_ID);
          res.body.value.chromeOptions.perfLoggingPrefs.should.not.have.keys('enableTimeline');
          res.body.value.chromeOptions.perfLoggingPrefs.traceCategories.should.equal('blink.console,disabled-by-default-devtools.timeline');
          res.body.status.should.equal(0);
          done();
        });
      });
      it('should add test-type in chromeOptions chrome > 34', function(done) {
        var requestBody = helper.getCapabilities(),
            railsScope = helper.buildRails(),
            macTerminalScope = helper.buildMac();
        requestBody['desiredCapabilities']['browser_version'] = 35;
        requestBody['desiredCapabilities']['chromeOptions'] = {};
        requestBody['desiredCapabilities']['chromeOptions']['args'] = 'random-test' //test with string instead of list

        var seleniumScope = helper.selenium()
          .post('/wd/hub/session')
          .query(true)
          .reply(function(uri, body) {
            return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
          });

        helper.request.post('/wd/hub/session')
        .auth('test', 'test')
        .send(requestBody)
        .end(function(err, res) {
          railsScope.isDone().should.be.true;
          macTerminalScope.isDone().should.be.true;
          seleniumScope.isDone().should.be.true;
          res.status.should.equal(200);
          res.body.value.should.have.any.keys('browser', 'browser_version', 'os', 'os_version');
          res.body.sessionId.should.equal(helper.SESSION_ID);
          res.body.value.chromeOptions.args.should.include('test-type');
          res.body.value.chromeOptions.args.should.include('random-test');
          res.body.status.should.equal(0);
          done();
        });
      });
      it('should send chrome binary path for chrome > 28', function(done) {
        var requestBody = helper.getCapabilities(),
            railsScope = helper.buildRails(),
            macTerminalScope = helper.buildMac();

        var seleniumScope = helper.selenium()
          .post('/wd/hub/session')
          .query(true)
          .reply(function(uri, body) {
            return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
          });

        helper.request.post('/wd/hub/session')
        .auth('test', 'test')
        .send(requestBody)
        .end(function(err, res) {
          railsScope.isDone().should.be.true;
          macTerminalScope.isDone().should.be.true;
          seleniumScope.isDone().should.be.true;
          res.status.should.equal(200);
          res.body.value.should.have.any.keys('browser', 'browser_version', 'os', 'os_version');
          res.body.sessionId.should.equal(helper.SESSION_ID);
          res.body.value.chromeOptions.binary.should.equal('/Applications/GoogleChrome45.app/Contents/MacOS/Google Chrome');
          res.body.status.should.equal(0);
          done();
        });
      });
      it('should handle prefs from chromeOptions correctly for chrome > 28', function(done) {
        var requestBody = helper.getCapabilities(),
            railsScope = helper.buildRails(),
            macTerminalScope = helper.buildMac();
        requestBody['desiredCapabilities']['chromeOptions'] = {}
        requestBody['desiredCapabilities']['chromeOptions']['prefs'] = {
          download: {
            prompt_for_download: false
          },
          profile: {
            default_content_settings: {
              popups: 0
            },
            managed_default_content_settings: {
              images: 2
            },
            random: true
          },
          randomKey: true
        };

        var seleniumScope = helper.selenium()
          .post('/wd/hub/session')
          .query(true)
          .reply(function(uri, body) {
            return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
          });

        helper.request.post('/wd/hub/session')
        .auth('test', 'test')
        .send(requestBody)
        .end(function(err, res) {
          railsScope.isDone().should.be.true;
          macTerminalScope.isDone().should.be.true;
          seleniumScope.isDone().should.be.true;
          res.status.should.equal(200);
          res.body.value.should.have.any.keys('browser', 'browser_version', 'os', 'os_version');
          res.body.sessionId.should.equal(helper.SESSION_ID);
          res.body.value.chromeOptions.prefs.browser.show_update_promotion_info_bar.should.equal(false);
          res.body.value.chromeOptions.prefs.browser.check_default_browser.should.equal(false);
          res.body.value.chromeOptions.prefs.download.prompt_for_download.should.equal(false);
          res.body.value.chromeOptions.prefs.profile.password_manager_enabled.should.equal(false);
          res.body.value.chromeOptions.prefs.profile.default_content_settings.popups.should.equal(0);
          res.body.value.chromeOptions.prefs.profile.managed_default_content_settings.images.should.equal(2);
          res.body.value.chromeOptions.prefs.profile.should.not.have.any.keys('random');
          res.body.value.chromeOptions.prefs.should.not.have.any.keys('randomKey');
          res.body.status.should.equal(0);
          done();
        });
      });
      it('should delete perfLoggingPrefs from chromeOptions if chrome version < 36', function(done) {
        var requestBody = helper.getCapabilities(),
            railsScope = helper.buildRails(),
            macTerminalScope = helper.buildMac();
        requestBody['desiredCapabilities']['browser_version'] = 35
        requestBody['desiredCapabilities']['chromeOptions'] = {}
        requestBody['desiredCapabilities']['chromeOptions']['perfLoggingPrefs'] = {
          enableNetwork: true,
          enablepage: true
        }

        var seleniumScope = helper.selenium()
          .post('/wd/hub/session')
          .query(true)
          .reply(function(uri, body) {
            return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
          });

        helper.request.post('/wd/hub/session')
        .auth('test', 'test')
        .send(requestBody)
        .end(function(err, res) {
          railsScope.isDone().should.be.true;
          macTerminalScope.isDone().should.be.true;
          seleniumScope.isDone().should.be.true;
          res.status.should.equal(200);
          res.body.value.should.have.any.keys('browser', 'browser_version', 'os', 'os_version');
          res.body.sessionId.should.equal(helper.SESSION_ID);
          res.body.value.chromeOptions.should.not.have.keys('perfLoggingPrefs');
          res.body.status.should.equal(0);
          done();
        });
      });
      describe("chromeOptions tests for real mobile android", function() {
        it('should not add no-first-run in chromeOptions for Google Nexus 6', function(done) {
          var requestBody = helper.getCapabilities({ device: 'Google Nexus 6', realMobile: true });
          delete requestBody.desiredCapabilities['browser'];

          var railsScope = helper.buildRails(),
            macTerminalScope = helper.buildMac();

          var seleniumScope = helper.appiumZotac()
            .post('/wd/hub/session')
            .query(true)
            .reply(function(uri, body) {
              return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
            });

          helper.request.post('/wd/hub/session')
            .auth('test', 'test')
            .send(requestBody)
            .end(function(err, res) {
              railsScope.isDone().should.be.true;
              macTerminalScope.isDone().should.be.true;
              seleniumScope.isDone().should.be.true;
              res.status.should.equal(200);
              res.body.sessionId.should.equal(helper.SESSION_ID);
              res.body.value.chromeOptions.args.should.not.include('--no-first-run');
              res.body.value.chromeOptions.args.should.not.include('--no-default-browser-check');
              done();
            });
        });
        it('should add no-first-run in chromeOptions for Google Nexus 5', function(done) {
          var requestBody = helper.getCapabilities({ device: 'Google Nexus 5', realMobile: true });
          delete requestBody.desiredCapabilities['browser'];

          var railsScope = helper.buildRails(),
            macTerminalScope = helper.buildMac();

          var seleniumScope = helper.appiumZotac()
            .post('/wd/hub/session')
            .query(true)
            .reply(function(uri, body) {
              return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
            });

          helper.request.post('/wd/hub/session')
            .auth('test', 'test')
            .send(requestBody)
            .end(function(err, res) {
              railsScope.isDone().should.be.true;
              macTerminalScope.isDone().should.be.true;
              seleniumScope.isDone().should.be.true;
              res.status.should.equal(200);
              res.body.sessionId.should.equal(helper.SESSION_ID);
              res.body.value.chromeOptions.args.should.include('--no-first-run');
              res.body.value.chromeOptions.args.should.include('--no-default-browser-check');
              done();
            });
        });
      });
    });
    describe('Firefox', function() {
      it('should add firefox profile for firefox', function(done) {
        var requestBody = helper.getCapabilities({
              browser: 'Firefox',
              browser_version: 43,
              os: 'OS X',
              os_version: 'El Capitan'
            }),
            railsScope = helper.buildRails(),
            macTerminalScope = helper.buildMac();

        var seleniumScope = helper.selenium()
          .post('/wd/hub/session')
          .query(true)
          .reply(function(uri, body) {
            return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
          });

        helper.request.post('/wd/hub/session')
        .auth('test', 'test')
        .send(requestBody)
        .end(function(err, res) {
          railsScope.isDone().should.be.true;
          macTerminalScope.isDone().should.be.true;
          seleniumScope.isDone().should.be.true;
          res.status.should.equal(200);
          res.body.value.should.have.any.keys('browser', 'browser_version', 'os', 'os_version');
          res.body.sessionId.should.equal(helper.SESSION_ID);
          res.body.status.should.equal(0);
          done();
        });
      });
    });
    describe('Edge', function() {
      it('should add initialBrowserUrl and change browserName for edge', function(done) {
        var requestBody = helper.getCapabilities({
              browser: 'Edge',
              browser_version: 12,
              os: 'Windows',
              os_version:  '10'
            }),
            railsScope = helper.buildRails(),
            winTerminalScope = helper.buildWin();

        var seleniumScope = helper.selenium()
          .post('/wd/hub/session')
          .query(true)
          .reply(function(uri, body) {
            return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
          });

        helper.request.post('/wd/hub/session')
        .auth('test', 'test')
        .send(requestBody)
        .end(function(err, res) {
          railsScope.isDone().should.be.true;
          winTerminalScope.isDone().should.be.true;
          seleniumScope.isDone().should.be.true;
          res.status.should.equal(200);
          res.body.value.should.have.any.keys('browser', 'browser_version', 'os', 'os_version');
          res.body.sessionId.should.equal(helper.SESSION_ID);
          res.body.value.initialBrowserUrl.should.equal('about:blank');
          res.body.value.browser.should.equal('MicrosoftEdge');
          res.body.value.browserName.should.equal('MicrosoftEdge');
          res.body.status.should.equal(0);
          done();
        });
      });
    });
    describe('Safari', function() {
      var os = 'macyos', seleniumVersion = '2.45.0';

      it('corrects capabilites if safari.options is not a hash', function(done) {
        var requestBody = helper.getCapabilities({
              browser: 'Safari',
              browser_version: 8,
              os: 'OS X',
              os_version: 'Yosemite'
            }),
            railsScope = helper.buildRails(),
            macTerminalScope = helper.buildMac();
        requestBody['desiredCapabilities']['safari.options'] = 'random'

        var seleniumScope = helper.selenium(os, seleniumVersion)
          .post('/wd/hub/session')
          .query(true)
          .reply(function(uri, body) {
            return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
          });

        helper.request.post('/wd/hub/session')
        .auth('test', 'test')
        .send(requestBody)
        .end(function(err, res) {
          railsScope.isDone().should.be.true;
          macTerminalScope.isDone().should.be.true;
          seleniumScope.isDone().should.be.true;
          res.status.should.equal(200);
          res.body.value.should.have.any.keys('browser', 'browser_version', 'os', 'os_version');
          res.body.sessionId.should.equal(helper.SESSION_ID);
          res.body.value['safari.options'].cleanSession.should.equal(true);
          res.body.value['safari.options'].port.should.equal(8884);
          res.body.status.should.equal(0);
          done();
        });
      });
      it('adds cleanSession and port to safari.options', function(done) {
        var requestBody = helper.getCapabilities({
              browser: 'Safari',
              browser_version: 8,
              os: 'OS X',
              os_version: 'Yosemite'
            }),
            railsScope = helper.buildRails(),
            macTerminalScope = helper.buildMac();

        requestBody['desiredCapabilities']['safari.options'] = {key: 'value'};

        var seleniumScope = helper.selenium(os, seleniumVersion)
          .post('/wd/hub/session')
          .query(true)
          .reply(function(uri, body) {
            return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
          });

        helper.request.post('/wd/hub/session')
        .auth('test', 'test')
        .send(requestBody)
        .end(function(err, res) {
          railsScope.isDone().should.be.true;
          macTerminalScope.isDone().should.be.true;
          seleniumScope.isDone().should.be.true;
          res.status.should.equal(200);
          res.body.value.should.have.any.keys('browser', 'browser_version', 'os', 'os_version');
          res.body.sessionId.should.equal(helper.SESSION_ID);
          res.body.value['safari.options'].cleanSession.should.equal(true);
          res.body.value['safari.options'].port.should.equal(8884);
          res.body.value['safari.options'].key.should.equal('value');
          res.body.status.should.equal(0);
          done();
        });
      });
    });
  });
  describe('Real Mobile', function() {
    describe('iOS', function() {
      it('should return correct capabilities for ios', function(done) {
        var requestBody = helper.getCapabilities({ device: 'iPhone 6S Plus', 'realMobile': true });
        delete requestBody.desiredCapabilities['browser'];

        var railsScope = helper.buildRails(),
            macTerminalScope = helper.buildMac();

        var seleniumScope = helper.appiumMini()
          .post('/wd/hub/session')
          .query(true)
          .reply(function(uri, body) {
            return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
          });

        helper.request.post('/wd/hub/session')
        .auth('test', 'test')
        .send(requestBody)
        .end(function(err, res) {
          railsScope.isDone().should.be.true;
          macTerminalScope.isDone().should.be.true;
          seleniumScope.isDone().should.be.true;
          res.status.should.equal(200);
          res.body.sessionId.should.equal(helper.SESSION_ID);
          res.body.value.browserName.should.equal('safari');
          res.body.value.platformName.should.equal('iOS');
          //res.body.value.platformVersion.should.equal('9.0');
          res.body.value.deviceName.should.equal('iPhone 6S Plus');
          // TODO check with the team if we need it or we can skip it. ('appium_port')
          res.body.value.should.have.property('deviceOrientation');
          // res.body.value.appium_port.should.equal('1234');
          res.body.value.udid.should.equal('abcdxyz');
          res.body.value.newCommandTimeout.should.equal(0);
          res.body.value.safariIgnoreFraudWarning.should.equal(true);
          res.body.value.noReset.should.equal(true);
          res.body.status.should.equal(0);
          done();
        });
      });
    });

    describe('tvOS', function() {
      it('should return correct capabilities for tvos', function(done) {
        var requestBody = helper.getCapabilities({ device: 'Apple TV 4k', 'realMobile': true });
        delete requestBody.desiredCapabilities['browser'];

        var railsScope = helper.buildRails(),
            macTerminalScope = helper.buildMac();

        var seleniumScope = helper.appiumMini()
          .post('/wd/hub/session')
          .query(true)
          .reply(function(uri, body) {
            return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
          });

        helper.request.post('/wd/hub/session')
        .auth('test', 'test')
        .send(requestBody)
        .end(function(err, res) {
          railsScope.isDone().should.be.true;
          macTerminalScope.isDone().should.be.true;
          seleniumScope.isDone().should.be.true;
          res.status.should.equal(200);
          res.body.sessionId.should.equal(helper.SESSION_ID);
          res.body.value.browserName.should.equal('safari');
          res.body.value.platformName.should.equal('tvos');
          //res.body.value.platformVersion.should.equal('9.0');
          res.body.value.deviceName.should.equal('Apple TV 4k');
          // TODO check with the team if we need it or we can skip it. ('appium_port')
          res.body.value.should.have.property('deviceOrientation');
          // res.body.value.appium_port.should.equal('1234');
          res.body.value.udid.should.equal('abcdxyz');
          res.body.value.newCommandTimeout.should.equal(0);
          res.body.value.safariIgnoreFraudWarning.should.equal(true);
          res.body.value.noReset.should.equal(true);
          res.body.status.should.equal(0);
          done();
        });
      });
    });

    describe('Android', function() {
      it('should return correct capabilities for android', function(done) {
        var requestBody = helper.getCapabilities({ device: 'Google Nexus 6', realMobile: true });
        delete requestBody.desiredCapabilities['browser'];

        var railsScope = helper.buildRails(),
            macTerminalScope = helper.buildMac();

        var seleniumScope = helper.appiumZotac()
          .post('/wd/hub/session')
          .query(true)
          .reply(function(uri, body) {
            return helper.sessionStartedJSON(JSON.parse(body)['desiredCapabilities']);
          });

        helper.request.post('/wd/hub/session')
        .auth('test', 'test')
        .send(requestBody)
        .end(function(err, res) {
          /* We cannot check for appium_port capability for real android
           * because all capabilities are overridden, but we already check if
           * it is hitting the correct port with seleniumScope.
           */
          railsScope.isDone().should.be.true;
          macTerminalScope.isDone().should.be.true;
          seleniumScope.isDone().should.be.true;
          res.status.should.equal(200);
          res.body.sessionId.should.equal(helper.SESSION_ID);
          res.body.value.browserName.should.equal('chrome');
          res.body.value.platformName.should.equal('Android');
          res.body.value.platformVersion.should.equal('5');
          res.body.value.deviceName.should.equal('Android');
          res.body.value.udid.should.equal('pqrstu');
          res.body.value.newCommandTimeout.should.equal(0);
          res.body.status.should.equal(0);
          done();
        });
      });
    });
  });
  describe('Snow Leopard', function() {
    describe('Chrome', function() {
      it('should return correct capabilities for chrome without default jar port', function(done) {
        var requestBody = helper.getCapabilities({ os_version: 'Snow Leopard'});

        helper.doSelenium.startSession(requestBody, helper.selenium('macsl'))
        .then((res) => {
          res.status.should.equal(200);
          res.body.value.should.have.any.keys('browser', 'browser_version', 'os', 'os_version');
          res.body.sessionId.should.equal(helper.SESSION_ID);
          res.body.status.should.equal(0);
          done();
        });
      });
    });
  });
});
