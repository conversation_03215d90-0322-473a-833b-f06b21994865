'use strict';

const assert = require('assert');
const transformers = require('../../../transformers/basic');
const constants = require('./../../../constants');

describe('transformers', () => {
  describe('snakeToCamel', () => {
    it('should return camelCase for snake_case passed', () => {
      assert.equal(transformers.snakeToCamel('raw_logs'), 'rawLogs');
      assert.equal(transformers.snakeToCamel('console_logs'), 'consoleLogs');
    });

    it('should return do nothing when camelCase is passed', () => {
      assert.equal(transformers.snakeToCamel('rawLogs'), 'rawLogs');
      assert.equal(transformers.snakeToCamel('consoleLogs'), 'consoleLogs');
    });
  });

  describe('originalTopicName', () => {
    it('should return extract original topic names from other topics for test environments', () => {
      constants.isTestingEnv = true;
      assert.equal(transformers.originalTopicName('raw_logs'), 'raw_logs');
    });
    it('should return extract original topic names from other topics for production environments', () => {
      constants.isProductionEnv = true;
      assert.equal(transformers.originalTopicName('raw_logs'), 'raw_logs');
    });
    it('should return extract original topic names from other topics for local environments', () => {
      constants.isLocalEnv = true;
      assert.equal(transformers.originalTopicName('raw_logs'), 'raw_logs');
    });
    it('should return extract original topic names from other topics for local environments', () => {
      constants.isTestingEnv = false;
      constants.isProductionEnv = false;
      constants.isLocalEnv = false;
      assert.equal(transformers.originalTopicName('stagautomate_raw_logs'), 'raw_logs');
    });
    it('should return exact original topic names from other topics for common regions', () => {
      constants.isTestingEnv = false;
      constants.isProductionEnv = false;
      constants.isLocalEnv = false;
      ['use', 'usw', 'euw', 'aps', 'apse'].forEach((region) => {
        assert.equal(transformers.originalTopicName(`${region}_raw_logs`), 'raw_logs');
        assert.equal(transformers.originalTopicName(`${region}_console_logs`), 'console_logs');
      });
    });
  });
});
