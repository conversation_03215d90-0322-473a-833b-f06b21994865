"use strict";

const { expect } = require("chai");
const sinon = require("sinon");
const HubLogger = require("../../log");
const { uploadScreenshotToS3 } = require("../../helpers/s3UploadHelper");

describe("uploadScreenshotToS3", function () {
  it("should upload a screenshot to S3", function () {
    const data = "base64 encode`d data";
    const keyObject = { rails_session_id: "session123", user: "testuser" };
    const fileCounter = 1;
    const framework = "selenium";
    let attempt = 0;
    const dataType = "string";

    const result = uploadScreenshotToS3(
      data,
      keyObject,
      fileCounter,
      framework,
      attempt,
      dataType
    );

    expect(result).to.be.a("string");
    expect(result).to.equal(data);
  });

  it("should handle an invalid JSON object", function () {
    const data = "{invalid json";
    const keyObject = { rails_session_id: "session123", user: "testuser" };
    const fileCounter = 1;
    const framework = "selenium";
    let attempt = 0;
    const dataType = "string";

    const result = uploadScreenshotToS3(
      data,
      keyObject,
      fileCounter,
      framework,
      attempt,
      dataType
    );

    expect(result).to.be.a("string");
    expect(result).to.equal(data);
  });

  it("should handle an empty JSON object", function () {
    const data = {};
    const keyObject = { rails_session_id: "session123", user: "testuser" };
    const fileCounter = 1;
    const framework = "selenium";
    let attempt = 0;
    const dataType = "object";

    const result = uploadScreenshotToS3(
      data,
      keyObject,
      fileCounter,
      framework,
      attempt,
      dataType
    );

    expect(result).to.be.an("object");
    expect(result).to.deep.equal({});
  });

  it("should throw an error for invalid buffer value", function () {
    const data = {
      value: 1234,
    };
    const keyObject = {
      rails_session_id: "1234",
      user: "testuser",
      key: "abc",
    };
    const originalTimeoutRegistry = require("../../constants").timeout_registry;
    require("../../constants").timeout_registry = { 1234: "mocked_value" };
    const fileCounter = 1;
    const framework = "selenium";
    let attempt = 0;
    const dataType = "object";
    const logStub = sinon.stub(HubLogger, "exceptionLogger");

    const result = uploadScreenshotToS3(
      data,
      keyObject,
      fileCounter,
      framework,
      attempt,
      dataType
    );

    sinon.assert.calledWith(
      logStub,
      `Cannot decode base64 screenshot response`,
      keyObject.key,
      "uploadScreenshotToS3"
    );

    expect(result).to.be.an("object");
    expect(result).to.deep.equal({"value": 1234});
    logStub.restore();
    require("../../constants").timeout_registry = originalTimeoutRegistry;
  });
});
