'use strict';

const TransitionChecker = require('../../../helpers/transition-check');
const { assert } = require('chai');
const helper = require('../../unit/helper');
const {
  RESPONSE_CLASS,
  SERVER_SIDE_ERROR_MESSAGE,
  SCRIPT_TIMEOUT_ERROR_MESSAGE,
  ELEMENT_NOT_FOUND_ERROR_MESSAGE,
} = require('../../../errorMessages');

describe('TransitionChecker', () => {
  let keyObject = {};
  let params = {};
  let successResponse = {};
  let errorResponse = {};

  describe('when browser is microsoft edge', () => {
    beforeEach(() => {
      keyObject = helper.getKeyObject();
      params = helper.getParamsObject();

      keyObject.browser = 'MicrosoftEdge';
      keyObject.browser_version = '10.0';
      successResponse = {
        state: 'success',
        sessionId: keyObject.rails_session_id,
        value: [],
        class: RESPONSE_CLASS,
        status: 0,
      };
      errorResponse = {
        state: 'async script timeout',
        sessionId: keyObject.rails_session_id,
        value: {
          message: SCRIPT_TIMEOUT_ERROR_MESSAGE,
          error: 'script timeout',
        },
        class: RESPONSE_CLASS,
        status: 28,
      };
    });

    describe('#isEdge()', () => {
      it('should return true', () => {
        const transitionChecker = new TransitionChecker(keyObject, params);
        assert.isTrue(transitionChecker.isEdge());
      });
    });

    describe('#generateErrorResponse()', () => {
      context('when last request is delete window & hash is finding elements', () => {
        it('should return timed out error response ', () => {
          keyObject.lastRequest = 'DELETE:window';
          params.hash = 'POST:elements';
          const transitionChecker = new TransitionChecker(keyObject, params);
          assert.equal(
            transitionChecker.generateErrorResponse(),
            JSON.stringify(errorResponse)
          );
        });
      });

      context('when last request is url blank & hash is executing script & instable true', () => {
        it('should return success response', () => {
          keyObject.lastRequest = 'POST:url-about-blank';
          keyObject.instable = true;
          params.hash = 'POST:execute';
          successResponse.value = null;

          const transitionChecker = new TransitionChecker(keyObject, params);
          assert.equal(
            transitionChecker.generateErrorResponse(),
            JSON.stringify(successResponse)
          );
        });
      });
    });

    describe('#shouldHandleTransition', () => {
      context('when post size & instable', () => {
        it('should return true', () => {
          keyObject.instable = true;
          params.hash = 'POST:size';

          const transitionChecker = new TransitionChecker(keyObject, params);
          assert.isTrue(transitionChecker.shouldHandleTransition);
        });
      });
    });

    describe('#wasExecuteSyncScriptCmd', () => {
      it('should return true', () => {
        keyObject.lastRequest = 'POST:execute';
        const transitionChecker = new TransitionChecker(keyObject, params);
        assert.isTrue(transitionChecker.wasExecuteSyncScriptCmd);
      });
    });

    describe('#wasOpenBlankUrlCmd', () => {
      it('should return true', () => {
        keyObject.lastRequest = 'POST:url-about-blank';
        const transitionChecker = new TransitionChecker(keyObject, params);
        assert.isTrue(transitionChecker.wasOpenBlankUrlCmd);
      });
    });

    describe('#wasAcceptAlertCmd', () => {
      it('should return true', () => {
        keyObject.lastRequest = 'POST:accept_alert';
        const transitionChecker = new TransitionChecker(keyObject, params);
        assert.isTrue(transitionChecker.wasAcceptAlertCmd);
      });
    });

    describe('#isGetUrlCmd', () => {
      it('should return true', () => {
        params.hash = 'GET:url';
        const transitionChecker = new TransitionChecker(keyObject, params);
        assert.isTrue(transitionChecker.isGetUrlCmd);
      });
    });

    describe('#isGetUrlCheck', () => {
      context('when last request is executing & hash is get url', () => {
        it('should return true ', () => {
          keyObject.lastRequest = 'POST:execute';
          params.hash = 'GET:url';
          const transitionChecker = new TransitionChecker(keyObject, params);
          assert.isTrue(transitionChecker.isGetUrlCheck);
        });
      });
    })

    describe('#isOpenUrlCmd', () => {
      it('should return true', () => {
        params.hash = 'POST:url';
        const transitionChecker = new TransitionChecker(keyObject, params);
        assert.isTrue(transitionChecker.isOpenUrlCmd);
      });
    });

    describe('#isGetTitleCmd', () => {
      it('should return true', () => {
        params.hash = 'GET:title';
        const transitionChecker = new TransitionChecker(keyObject, params);
        assert.isTrue(transitionChecker.isGetTitleCmd);
      });
    });

    describe('#isFetchingTitleCheck', () => {
      context('when last request url is about blank & hash is get title', () => {
        it('should return false', () => {
          keyObject.lastRequest = 'POST:url-about-blank';
          params.hash = 'GET:title';
          const transitionChecker = new TransitionChecker(keyObject, params);
          assert.isFalse(transitionChecker.isFetchingTitleCheck);
        });
      });
    });

    describe('#isTitleAndReadyStateCheck', () => {
      context('when last request is url & udpKeys present & tunnel is enabled', () => {
        it('should return true', () => {
          keyObject.tunnel = true;
          keyObject.lastRequest = 'POST:url';
          keyObject.udpKeys = {};
          const transitionChecker = new TransitionChecker(keyObject, params);
          assert.isTrue(transitionChecker.isTitleAndReadyStateCheck);
        });
      });
    });

    describe('#isFindElementCmd', () => {
      it('should return true', () => {
        params.hash = 'POST:element';
        const transitionChecker = new TransitionChecker(keyObject, params);
        assert.isTrue(transitionChecker.isFindElementCmd);
      });
    });

    describe('#isFindingElementCheck', () => {
      context('when previous page is loading & element found', () => {
        it('should return true', () => {
          keyObject.lastRequest = 'POST:url-about-blank';
          params.hash = 'POST:element';
          keyObject.instable = true;
          const transitionChecker = new TransitionChecker(keyObject, params);
          assert.isTrue(transitionChecker.isFindingElementCheck);
        });
      });
    });

    describe('#isExecuteScriptCmd', () => {
      it('should return true', () => {
        params.hash = 'POST:execute';
        const transitionChecker = new TransitionChecker(keyObject, params);
        assert.isTrue(transitionChecker.isExecuteScriptCmd);
      });
    });

    describe('#isExecutingScriptCheck', () => {
      context('when script executing & previous page loading', () => {
        it('should return true ', () => {
          keyObject.lastRequest = 'POST:url-about-blank';
          keyObject.instable = true;
          params.hash = 'POST:execute';

          const transitionChecker = new TransitionChecker(keyObject, params);
          assert.isTrue(transitionChecker.isExecutingScriptCheck);
        });
      });
    });

    describe('#isCaptureScreenshotCmd', () => {
      it('should return true', () => {
        params.hash = 'GET:screenshot';
        const transitionChecker = new TransitionChecker(keyObject, params);
        assert.isTrue(transitionChecker.isCaptureScreenshotCmd);
      });
    });

    describe('#isCaptureScreenshotCheck', () => {
      context('when instable', () => {
        it('should return true ', () => {
          keyObject.instable = true;
          params.hash = 'GET:screenshot';
          const transitionChecker = new TransitionChecker(keyObject, params);
          assert.isTrue(transitionChecker.isCaptureScreenshotCheck);
        });
      });
    });

    describe('#isClickCmd', () => {
      it('should return true', () => {
        params.hash = 'POST:click';
        const transitionChecker = new TransitionChecker(keyObject, params);
        assert.isTrue(transitionChecker.isClickCmd);
      });
    });

    describe('#ignorePreviousPageLoading', () => {
      context('when last request is data', () => {
        it('should return false ', () => {
          keyObject.lastRequest = 'POST:data';
          const transitionChecker = new TransitionChecker(keyObject, params);
          assert.isFalse(transitionChecker.ignorePreviousPageLoading);
        });
      });

      context('when last request is xml', () => {
        it('should return false', () => {
          keyObject.lastRequest = 'POST:xml';
          const transitionChecker = new TransitionChecker(keyObject, params);
          assert.isFalse(transitionChecker.ignorePreviousPageLoading);
        });
      });

      context('when last request is blank url', () => {
        it('should return false', () => {
          keyObject.lastRequest = 'POST:url-about-blank';
          const transitionChecker = new TransitionChecker(keyObject, params);
          assert.isFalse(transitionChecker.ignorePreviousPageLoading);
        });
      });
    });

    describe('#previousPageIsLoading', () => {
      context('when last request is blank url', () => {
        it('should return false', () => {
          keyObject.lastRequest = 'POST:url-about-blank';
          const transitionChecker = new TransitionChecker(keyObject, params);
          assert.isFalse(transitionChecker.previousPageIsLoading);
        });
      });
    });

    describe('#otherAffectedCommands', () => {
      context('when instable & hash is get name', () => {
        it('should return true', () => {
          keyObject.instable = true;
          params.hash = 'GET:name';
          const transitionChecker = new TransitionChecker(keyObject, params);
          assert.isTrue(transitionChecker.otherAffectedCommands);
        });
      });
    });
  });

  describe('when browser is iPhone', () => {
    beforeEach(() => {
      keyObject = helper.getKeyObject();
      params = helper.getParamsObject();

      keyObject.browser = 'iPhone';
      keyObject.browser_version = '13.0';
      keyObject.realMobile = true;

      successResponse = {
        state: 'success',
        sessionId: keyObject.rails_session_id,
        value: null,
        class: RESPONSE_CLASS,
        status: 0,
      };

      errorResponse = {
        state: 'error',
        sessionId: keyObject.rails_session_id,
        value: {
          message: SERVER_SIDE_ERROR_MESSAGE,
          error: 'unknown error',
        },
        class: RESPONSE_CLASS,
        status: 13,
      };
    });

    describe('#isIosBrowser', () => {
      it('should return true', () => {
        const transitionChecker = new TransitionChecker(keyObject, params);
        assert.isTrue(transitionChecker.isIOSBrowser);
      });
    });

    describe('#generateErrorResponse()', () => {
      context('when the only available window is closed', () => {
        it('unknown error response for getting screenshot ', () => {
          keyObject.lastRequest = 'DELETE:window';
          params.hash = 'GET:screenshot';

          const transitionChecker = new TransitionChecker(keyObject, params);
          assert.equal(
            transitionChecker.generateErrorResponse(),
            JSON.stringify(errorResponse)
          );
        });
      });

      context('when last request is delete window & hash is deleting cookie', () => {
        it('unknown error response', () => {
          keyObject.lastRequest = 'DELETE:window';
          params.hash = 'DELETE:cookie';

          const transitionChecker = new TransitionChecker(keyObject, params);
          assert.equal(
            transitionChecker.generateErrorResponse(),
            JSON.stringify(errorResponse)
          );
        });
      });

      context('when last request is delete window & hash is getting title', () => {
        it('success response ', () => {
          keyObject.lastRequest = 'DELETE:window';
          params.hash = 'GET:title';

          const transitionChecker = new TransitionChecker(keyObject, params);
          assert.equal(
            transitionChecker.generateErrorResponse(),
            JSON.stringify(successResponse)
          );
        });
      });
    });

    describe('#shouldHandleTransition', () => {
      context('when last request is refresh & getting title', () => {
        it('should return true', () => {
          keyObject.lastRequest = 'POST:refresh';
          params.hash = 'GET:title';

          const transitionChecker = new TransitionChecker(keyObject, params);
          assert.isTrue(transitionChecker.shouldHandleTransition);
        });
      });
    });
  });

  describe('when browser is chrome', () => {
    beforeEach(() => {
      keyObject = helper.getKeyObject();
      params = helper.getParamsObject();

      keyObject.browser = 'chrome';
      keyObject.browser_version = '10.0';
    });

    describe('#isChrome()', () => {
      it('should return true', () => {
        const transitionChecker = new TransitionChecker(keyObject, params);
        assert.isTrue(transitionChecker.isChrome());
      });
    });

    describe('#shouldHandleTransition', () => {
      context('when last request is refresh & hash is getting title', () => {
        it('should return true', () => {
          keyObject.lastRequest = 'POST:refresh';
          params.hash = 'GET:title';

          const transitionChecker = new TransitionChecker(keyObject, params);
          assert.isTrue(transitionChecker.shouldHandleTransition);
        });
      });
    });

    describe('#isGetUrlCheck & #isGetUrlCmd', () => {
      context('when last requested is execute & hash is an open url', () => {
        it('should return true', () => {
          keyObject.lastRequest = 'POST:execute';
          params.hash = 'GET:url';

          const transitionChecker = new TransitionChecker(keyObject, params);
          assert.isTrue(transitionChecker.isGetUrlCheck);
          assert.isTrue(transitionChecker.isGetUrlCmd);
        });
      });
    });

    describe('#ignorePreviousPageLoading', () => {
      context('when last requested is url', () => {
        it('should return true', () => {
          keyObject.lastRequest = 'POST:url';
          const transitionChecker = new TransitionChecker(keyObject, params);
          assert.isTrue(transitionChecker.ignorePreviousPageLoading);
        });
      });
    });
  });

  describe('when browser is android', () => {
    beforeEach(() => {
      keyObject = helper.getKeyObject();
      keyObject.browser = 'Android';
    });

    describe('#isAndroidBrowser', () => {
      it('should return true', () => {
        const transitionChecker = new TransitionChecker(keyObject, params);
        assert.isTrue(transitionChecker.isAndroidBrowser);
      });
    });

    describe('#shouldHandleTransition', () => {
      context('when last request is refresh & hash is getting url', () => {
        it('should return true', () => {
          keyObject.lastRequest = 'POST:refresh';
          params.hash = 'GET:title';

          const transitionChecker = new TransitionChecker(keyObject, params);
          assert.isTrue(transitionChecker.shouldHandleTransition);
        });
      });
    });
  });

  describe('when browser is safari', () => {
    beforeEach(() => {
      keyObject = helper.getKeyObject();
      keyObject.browser = 'safari';
      keyObject.browser_version = '10.0';
    });

    describe('#isSafari()', () => {
      it('should return true', () => {
        const transitionChecker = new TransitionChecker(keyObject, params);
        assert.isTrue(transitionChecker.isSafari());
      });
    });

    describe('#shouldHandleTransition', () => {
      context('when last request is refresh & hash is getting url', () => {
        it('should return true', () => {
          keyObject.lastRequest = 'POST:refresh';
          params.hash = 'GET:title';

          const transitionChecker = new TransitionChecker(keyObject, params);
          assert.isTrue(transitionChecker.shouldHandleTransition);
        });
      });
    });
  });

  describe('when browser is firefox', () => {
    beforeEach(() => {
      keyObject = helper.getKeyObject();
      keyObject.browser = 'firefox';
      keyObject.browser_version = '10.0';
      errorResponse = {
        state: 'error',
        sessionId: keyObject.rails_session_id,
        value: {
          message: ELEMENT_NOT_FOUND_ERROR_MESSAGE,
          error: 'no such element',
        },
        class: RESPONSE_CLASS,
        status: 7,
      };
    });

    describe('#isFirefox()', () => {
      it('should return true', () => {
        const transitionChecker = new TransitionChecker(keyObject, params);
        assert.isTrue(transitionChecker.isFirefox());
      });
    });

    describe('#generateErrorResponse()', () => {
      context('when last request is accept_alert & hash is finding element with instable true', () => {
        it('should return element not found', () => {
          keyObject.lastRequest = 'POST:accept_alert';
          keyObject.instable = true;
          params.hash = 'POST:element';

          const transitionChecker = new TransitionChecker(keyObject, params);
          assert.equal(
            transitionChecker.generateErrorResponse(),
            JSON.stringify(errorResponse)
          );
        });
      });

    });

    describe('#shouldHandleTransition', () => {
      context('when last request is delete window & hash is clicked with instable true', () => {
        let transitionChecker;
        beforeEach(() => {
          keyObject.lastRequest = 'DELETE:window';
          keyObject.instable = true;
          params.hash = 'POST:click';
          transitionChecker = new TransitionChecker(keyObject, params);
        });

        it('should return true', () => {
          assert.isTrue(transitionChecker.shouldHandleTransition);
        });

        it('#isOpenUrlCheck', () => {
          assert.isTrue(transitionChecker.isOpenUrlCheck);
        });

        it('#isClickCmd', () => {
          assert.isTrue(transitionChecker.isClickCmd);
        });
      });


      it('should return true when last request is refresh & hash is getting url', () => {
        keyObject.lastRequest = 'POST:refresh';
        params.hash = 'GET:title';

        const transitionChecker = new TransitionChecker(keyObject, params);
        assert.isTrue(transitionChecker.shouldHandleTransition);
      });
    });

  });

  describe('when browser is internet explorer', () => {
    beforeEach(() => {
      keyObject = helper.getKeyObject();
      keyObject.browser = 'internet explorer';
      keyObject.browser_version = '10.0';

      successResponse = {
        state: 'success',
        sessionId: keyObject.rails_session_id,
        value: [],
        class: RESPONSE_CLASS,
        status: 0,
      };
      errorResponse = {
        state: 'async script timeout',
        sessionId: keyObject.rails_session_id,
        value: {
          message: SCRIPT_TIMEOUT_ERROR_MESSAGE,
          error: 'script timeout',
        },
        class: RESPONSE_CLASS,
        status: 28,
      };
    });

    describe('#isInternetExplorer()', () => {
      it('should return true', () => {
        const transitionChecker = new TransitionChecker(keyObject, params);
        assert.isTrue(transitionChecker.isInternetExplorer());
      });
    });

    describe('#generateErrorResponse()', () => {
      context('when keyObject & params are empty', () => {
        it('should return timed out response', () => {
          const transitionChecker = new TransitionChecker(keyObject, params);
          assert.equal(
            transitionChecker.generateErrorResponse(),
            JSON.stringify(errorResponse)
          );
        });
      });

      context('when last request is execute & hash is finding elements', () => {
        it('should return success response', () => {
          keyObject.lastRequest = 'POST:execute';
          params.hash = 'POST:elements';

          const transitionChecker = new TransitionChecker(keyObject, params);
          assert.equal(
            transitionChecker.generateErrorResponse(),
            JSON.stringify(successResponse)
          );
          assert.isTrue(transitionChecker.isInternetExplorer());
          assert.isTrue(transitionChecker.wasExecutingSyncScriptOnIE(10));
        });
      });

    });

    describe('#isTitleAndReadyStateCheck & #isOpenUrlCmd', () => {
      context('when last request & hash is url with tunnel true & udpKeys empty', () => {
        it('should return true', () => {
          keyObject.tunnel = true;
          keyObject.udpKeys = {};
          keyObject.lastRequest = 'POST:url';
          params.hash = 'POST:url';

          const transitionChecker = new TransitionChecker(keyObject, params);
          assert.isTrue(transitionChecker.isTitleAndReadyStateCheck);
          assert.isTrue(transitionChecker.isOpenUrlCmd);
        });
      });
    });

    describe('#shouldHandleTransition', () => {
      context('when last request is refresh & hash is getting title', () => {
        it('should return true', () => {
          keyObject.lastRequest = 'POST:refresh';
          params.hash = 'GET:title';

          const transitionChecker = new TransitionChecker(keyObject, params);
          assert.isTrue(transitionChecker.shouldHandleTransition);
        });
      });
    });

    describe('#otherAffectedCommands', () => {
      context('when hash is empty', () => {
        it('should return undefined', () => {
          params.hash = '';
          const transitionChecker = new TransitionChecker(keyObject, params);
          assert.isUndefined(transitionChecker.otherAffectedCommands);
        });
      });
    });
  });
});
