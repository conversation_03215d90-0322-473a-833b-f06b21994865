'use strict';

/* eslint no-underscore-dangle: 0 */

const Qig = require('../../../helpers/qig');
const sinon = require('sinon');
const pubSub = require('../../../pubSub');
const { updateKeyObject } = require('../../../constants');

describe('Qig', () => {
  context('#_isSampleScriptUrl - check if url is from sample scripts', () => {
    [
      'https://google.com',
      'https://duckduckgo.com/',
      'https://bstackdemo.com',
    ].forEach((url) => {
      it(`should return true for ${url}`, () => {
        Qig._isSampleScriptUrl(url).should.eql(true);
      });
    });

    it('returns false for "non sample script" sites', () => {
      Qig._isSampleScriptUrl('https://abcd.com').should.eql(false);
    });
  });

  context('#_minimalChecks - Perform before-filter like checks/validations', () => {
    it('Returns false for app testing, i.e. AA', () => {
      const keyObject = {
        appTesting: 'app://12345',
      };

      Qig._minimalChecks(keyObject).should.eql(false);
    });

    it('Returns true if its not for app testing, i.e. AA', () => {
      const keyObject = {
        appTesting: false,
      };

      Qig._minimalChecks(keyObject).should.eql(true);
    });
  });

  context('#_publishQigData - Publishes the updated QIG data to keyObject via pubSub', () => {
    it('calls the pubSub publish method on `updateKeyObject` channel', () => {
      const keyObject = {
        rails_session_id: 'abcd',
        qig: {
          sampleScriptUrl: false,
          sessionStatusExecutorUsed: false,
        },
      };

      const publishData = {
        session: 'abcd',
        changed: {
          qig: {
            sampleScriptUrl: false,
            sessionStatusExecutorUsed: false,
          },
        },
      };

      sinon.stub(pubSub, 'publish');
      Qig._publishQigData(keyObject);
      sinon.assert.calledWith(pubSub.publish, updateKeyObject, publishData);
      pubSub.publish.restore();
    });
  });

  context('#MarkSessionStatusExecutor - Updates keyObject and publishes when session status is marked using JS executor', () => {
    beforeEach(() => {
      sinon.stub(Qig, '_publishQigData');
    });

    afterEach(() => {
      Qig._publishQigData.restore();
      Qig._minimalChecks.restore();
    });

    it('doesn\'t publish the data if minimal checks fail', () => {
      const keyObject = {};
      sinon.stub(Qig, '_minimalChecks', () => false);
      Qig.markSessionStatusExecutor(keyObject);

      sinon.assert.notCalled(Qig._publishQigData);
    });

    it('doesn\'t publish the data if `qig` object doesn\'t exist in keyObject', () => {
      const keyObject = {};
      sinon.stub(Qig, '_minimalChecks', () => true);
      Qig.markSessionStatusExecutor(keyObject);

      sinon.assert.notCalled(Qig._publishQigData);
    });

    it('doesn\'t publish the data if session status JS executor is already used and marked', () => {
      const keyObject = {
        qig: {
          sessionStatusExecutorUsed: true,
        },
      };
      sinon.stub(Qig, '_minimalChecks', () => true);
      Qig.markSessionStatusExecutor(keyObject);

      sinon.assert.notCalled(Qig._publishQigData);
    });

    it('publishes the updated keyObject when session status JS executor is used the first time for automate session', () => {
      const keyObject = {
        qig: {
          sessionStatusExecUsed: false,
        },
      };

      const updatedKeyObject = {
        qig: {
          sessionStatusExecUsed: true,
        },
      };

      sinon.stub(Qig, '_minimalChecks', () => true);
      Qig.markSessionStatusExecutor(keyObject);

      sinon.assert.calledWith(Qig._publishQigData, updatedKeyObject);
    });
  });

  context('#checkAndMarkCustomUrlUsed - Updates keyObject and publishes when a non-sample-script URL is used', () => {
    beforeEach(() => {
      sinon.stub(Qig, '_publishQigData');
    });

    afterEach(() => {
      Qig._publishQigData.restore();
      Qig._minimalChecks.restore();
    });

    it('doesn\'t publish the data if minimal checks fail', () => {
      const keyObject = {};
      sinon.stub(Qig, '_minimalChecks', () => false);
      Qig.checkAndMarkCustomUrlUsed(keyObject, 'https://abcd.com');

      sinon.assert.notCalled(Qig._publishQigData);
    });

    it('doesn\'t publish the data if `qig` object doesn\'t exist in keyObject', () => {
      const keyObject = {};
      sinon.stub(Qig, '_minimalChecks', () => true);
      Qig.checkAndMarkCustomUrlUsed(keyObject, 'https://abcd.com');

      sinon.assert.notCalled(Qig._publishQigData);
    });

    it('doesn\'t publish the data if a custom URL was already checked and marked', () => {
      const keyObject = {
        qig: {
          sampleScriptUrl: false,
        },
      };

      sinon.stub(Qig, '_minimalChecks', () => true);
      Qig.checkAndMarkCustomUrlUsed(keyObject, 'https://abcd.com');

      sinon.assert.notCalled(Qig._publishQigData);
    });

    it('doesn\'t publish the data if the URL passes the sample-script-url checks', () => {
      const keyObject = {
        qig: {
          sampleScriptUrl: true,
        },
      };
      sinon.stub(Qig, '_minimalChecks', () => true);
      sinon.stub(Qig, '_isSampleScriptUrl', () => true);
      Qig.checkAndMarkCustomUrlUsed(keyObject, 'https://bstackdemo.com');

      sinon.assert.notCalled(Qig._publishQigData);
      Qig._isSampleScriptUrl.restore();
    });

    it('publishes the updated keyObject if a custom URL is detected for the first time for Automate session', () => {
      const keyObject = {
        qig: {
          sampleScriptUrl: true,
        },
      };

      const updatedKeyObject = {
        qig: {
          sampleScriptUrl: false,
        },
      };

      sinon.stub(Qig, '_minimalChecks', () => true);
      sinon.stub(Qig, '_isSampleScriptUrl', () => false);
      Qig.checkAndMarkCustomUrlUsed(keyObject, 'https://abcd.com');

      sinon.assert.calledWith(Qig._publishQigData, updatedKeyObject);
      Qig._isSampleScriptUrl.restore();
    });
  });

  context('#shouldSendDataToRails - decides whether to include QIG data in the stop request sent to rails', () => {
    afterEach(() => {
      Qig._minimalChecks.restore();
    });

    it('returns false if the minimal checks fail', () => {
      const keyObject = {};
      sinon.stub(Qig, '_minimalChecks', () => false);

      Qig.shouldSendDataToRails(keyObject).should.eql(false);
    });

    it('returns false if qig object doesn\'t exist in keyObject', () => {
      const keyObject = {};
      sinon.stub(Qig, '_minimalChecks', () => true);

      Qig.shouldSendDataToRails(keyObject).should.eql(false);
    });

    it('returns false if no custom URL was used in the session', () => {
      const keyObject = {
        qig: {
          sampleScriptUrl: true,
        },
      };

      sinon.stub(Qig, '_minimalChecks', () => true);

      Qig.shouldSendDataToRails(keyObject).should.eql(false);
    });

    it('returns true if custom URL was used in the session', () => {
      const keyObject = {
        qig: {
          sampleScriptUrl: false,
        },
      };

      sinon.stub(Qig, '_minimalChecks', () => true);

      Qig.shouldSendDataToRails(keyObject).should.eql(true);
    });
  });
});
