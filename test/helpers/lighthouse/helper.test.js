const { expect } = require('chai');
const sinon = require('sinon');
const rewire = require('rewire');
const requestlib = require('../../../lib/request');
const HubLogger = require("../../../log");
const constants = require('../../../constants');
const { LH_ERROR_MESSAGES, THROTTLING_PROFILE } = constants;
const { PLAYWRIGHT, PUPPETEER, SELENIUM } = require('../../../config/socketConstants');
const TriggerLightHouse = rewire('../../../helpers/lighthouse/helper');
const Qig = require('../../../helpers/qig');
const RestAPIHandler = require('../../../helpers/customSeleniumHandling/restAPIHandler');
const customExecutorHelper = require("../../../helpers/customSeleniumHandling/customExecutorHelper");
const AWS = require('aws-sdk');

describe('Automate Lighthouse', () => {
  const successData = {
    statusCode: 200,
    data: '{"lhSuccess": "true", "data": "abc"}',
  };
  const failData = {
    statusCode: 500,
    data: '{"lhSuccess": "false", "data": "abc"}',
  };

  const keyObject = {
    rails_session_id: "random_session_id",
    os: "mac",
    rproxyHost: "terminal_ip",
    isPlaywright: true,
    browser: "chrome",
    browser_version: constants.LH_MIN_CHROME.toString(),
    lighthouseAutomate: { assert_limit: 5, report_limit: 5, report_counter: 1 }
  };

  const url = "https://google.com"
  const filename = "report-1"

  describe('getProduct', () => {
    let getProduct;

    beforeEach(() => {
      getProduct = TriggerLightHouse.__get__(
        "getProduct"
      );
      keyObject.isPlaywright = false;
      keyObject.isPuppeteer = false;
      keyObject.selenium_version = false;
      keyObject.realMobile = false;
    });

    it("should return PLAYWRIGHT for playwright", async () => {
      keyObject.isPlaywright = true;
      expect(getProduct(keyObject)).to.equal(PLAYWRIGHT);
    });

    it("should return PUPPETEER for puppeteer", async () => {
      keyObject.isPuppeteer = true;
      expect(getProduct(keyObject)).to.equal(PUPPETEER);
    });

    it("should return SELENIUM for selenium", async () => {
      keyObject.selenium_version = 3.14;
      expect(getProduct(keyObject)).to.equal(SELENIUM);
    });

    it("should return false for selenium with real mobile", async () => {
      keyObject.selenium_version = 3.14;
      keyObject.realMobile = true;
      expect(getProduct(keyObject)).to.equal(false);
    });

    it("should return false for everything else", async () => {
      expect(getProduct(keyObject)).to.equal(false);
    });
  });

  describe('processCdpExecutorResponse', () => {
    let processCdpExecutorResponse, jsonData, expectedResult;

    beforeEach(() => {
      processCdpExecutorResponse = TriggerLightHouse.__get__(
        "processCdpExecutorResponse"
      );
      jsonData = {
        value: {
          testing: 'abc'
        }
      }
      expectedResult = {
        enabled: false
      }
    });

    it("should return performance enabled as false if performanceData is false", async () => {
      expect(processCdpExecutorResponse(jsonData)).to.deep.equal(expectedResult);
    });

    it("should return performance enabled as true if performanceData is true and truncate is not provided", async () => {
      jsonData.value.performanceLogs = true;
      expectedResult.enabled = true;
      expect(processCdpExecutorResponse(jsonData)).to.deep.equal(expectedResult);
    });

    it("should return performance,truncate as true if performanceData and truncate is true", async () => {
      jsonData.value.performanceLogs = true;
      jsonData.value.truncatePerformanceJson = true;
      expectedResult.enabled = true;
      expectedResult.truncate = true;
      expect(processCdpExecutorResponse(jsonData)).to.deep.equal(expectedResult);
    });
  });

  describe('requestToTerminal', () => {
    let requestToTerminal;
    let requestLibCallStub;
    let opts;
    let product;

    beforeEach(() => {
      requestLibCallStub = sinon.stub(requestlib, "call");
      sinon.stub(requestlib, "appendBStackHostHeader");
      requestToTerminal = TriggerLightHouse.__get__(
        "requestToTerminal"
      );
      opts = {
        url: url,
        file_name: filename,
        devtoolsPort: 9222,
        params: {
          type: "assert"
        }
      }
      product = PLAYWRIGHT;
    });

    afterEach(() => {
      requestLibCallStub.restore();
      requestlib.appendBStackHostHeader.restore();
    });

    it("should do a request.call and return the result if terminal return success", async () => {
      requestLibCallStub.returns(successData);
      const result = await requestToTerminal(keyObject, opts, product);
      expect(JSON.parse(result).lhSuccess).equal("true");
      sinon.assert.called(requestlib.appendBStackHostHeader);
    });

    it("should do a request.call and return the result if terminal return success with custom lhConfig", async () => {
      requestLibCallStub.returns(successData);
      opts.params.body = {
        lhConfig: { abc: 123}
      }
      const result = await requestToTerminal(keyObject, opts, product);
      expect(JSON.parse(result).lhSuccess).equal("true");
      sinon.assert.called(requestlib.appendBStackHostHeader);
    });

    it("should do a request.call and return the result if terminal return success - win os", async () => {
      requestLibCallStub.returns(successData);
      keyObject.os = "win";
      const result = await requestToTerminal(keyObject, opts, product);
      keyObject.os = "mac";
      expect(JSON.parse(result).lhSuccess).equal("true");
      sinon.assert.called(requestlib.appendBStackHostHeader);
    });

    it("should through error if terminal", async () => {
      requestLibCallStub.returns(failData);
      try {
        const result = await requestToTerminal(keyObject, opts, product);
      } catch (error) {
        expect(error, `Exception raised in terminal: ${JSON.stringify(failData)}`)
      }
    });
  });

  describe('markSessionFailed', () => {
    let markSessionFailed, jsonData, apiHandlerStub, qigStub;

    beforeEach(() => {
      apiHandlerStub = sinon.stub(RestAPIHandler.prototype, "makeRequest");
      qigStub = sinon.stub(Qig, "markSessionStatusExecutor");
      sinon.stub(HubLogger, "miscLogger");
      markSessionFailed = TriggerLightHouse.__get__(
        "markSessionFailed"
      );
      jsonData = { statusCode: 200 }
    });

    afterEach(() => {
      apiHandlerStub.restore();
      qigStub.restore();
      HubLogger.miscLogger.restore();
    });

    it("should return true if makeRequest called successfully", async () => {
      apiHandlerStub.returns(jsonData);
      expect(await markSessionFailed(keyObject)).to.equal(true);
      sinon.assert.calledOnce(qigStub);
    });

    it("should return true if makeRequest called successfully for app automate as well", async () => {
      apiHandlerStub.returns(jsonData);
      keyObject.appTesting = "true";
      expect(await markSessionFailed(keyObject)).to.equal(true);
      delete keyObject.appTesting;
      sinon.assert.calledOnce(qigStub);
    });

    it("should return false if makeRequest returns non 200", async () => {
      jsonData.statusCode = 400;
      apiHandlerStub.returns(jsonData);
      expect(await markSessionFailed(keyObject)).to.equal(false);
      sinon.assert.notCalled(qigStub);
    });

    it("should return false if exception raised", async () => {
      apiHandlerStub.throws(new Error("some error"));
      expect(await markSessionFailed(keyObject)).to.equal(false);
      sinon.assert.notCalled(qigStub);
      sinon.assert.calledWith(HubLogger.miscLogger, `lighthouse`, `Error marking session status: some error`, constants.LOG_LEVEL.ERROR);
    });
  });

  describe('lighthouseExecutorValidation', () => {
    let lighthouseExecutorValidation, body, dummyKeyobject;

    beforeEach(() => {
      lighthouseExecutorValidation = TriggerLightHouse.__get__(
        "lighthouseExecutorValidation"
      );
      body = { statusCode: 200 }
      dummyKeyobject = {
        browser: "chrome",
        browser_version: "100",
        isPlaywright: true,
        lighthouseAutomate: { assert_limit: 5, report_limit: 5, report_counter: 1 }
      };
    });

    it("should return executor limit not found if assert limit not present", async () => {
      delete dummyKeyobject.lighthouseAutomate.assert_limit;
      expect(lighthouseExecutorValidation(dummyKeyobject, body)).to.equal(LH_ERROR_MESSAGES["js_executor_assert_limit_not_found"]);
    });

    it("should return executor limit not found if report limit present", async () => {
      expect(lighthouseExecutorValidation(dummyKeyobject, body)).to.equal(LH_ERROR_MESSAGES["js_executor_assert_limit_not_found"]);
    });

    it("should return framework not supported if framework details not present", async () => {
      delete dummyKeyobject.lighthouseAutomate.report_limit;
      delete dummyKeyobject.isPlaywright;
      expect(lighthouseExecutorValidation(dummyKeyobject, body)).to.equal(LH_ERROR_MESSAGES["js_executor_framework_not_supported"]);
    });

    it("should return browser not supported if unsupported browser present", async () => {
      delete dummyKeyobject.lighthouseAutomate.report_limit;
      delete dummyKeyobject.isPlaywright;
      dummyKeyobject.browser = "firefox";
      expect(lighthouseExecutorValidation(dummyKeyobject, body)).to.equal(LH_ERROR_MESSAGES["js_executor_framework_not_supported"]);
    });

    it("should return browser not supported if unsupported browser present", async () => {
      delete dummyKeyobject.lighthouseAutomate.report_limit;
      dummyKeyobject.browser = "firefox";
      expect(lighthouseExecutorValidation(dummyKeyobject, body)).to.equal(LH_ERROR_MESSAGES["browser_not_supported"]);
    });

    it("should return browser version not supported if unsupported browser version present", async () => {
      delete dummyKeyobject.lighthouseAutomate.report_limit;
      dummyKeyobject.browser_version = 50;
      expect(lighthouseExecutorValidation(dummyKeyobject, body)).to.equal(LH_ERROR_MESSAGES["chrome_version_not_supported"]);
    });

    it("should return validated message if no body present and params look fine", async () => {
      delete dummyKeyobject.lighthouseAutomate.report_limit;
      expect(lighthouseExecutorValidation(dummyKeyobject, false)).to.equal("validated");
    });

    it("should return invalid config if non json config in body", async () => {
      delete dummyKeyobject.lighthouseAutomate.report_limit;
      body.lhConfig = 'abc';
      expect(lighthouseExecutorValidation(dummyKeyobject, body)).to.equal(LH_ERROR_MESSAGES["js_executor_invalid_lh_config"]);
    });

    it("should return invalid throttling error for invalid throttling in body", async () => {
      delete dummyKeyobject.lighthouseAutomate.report_limit;
      body.throttling = '123';
      expect(lighthouseExecutorValidation(dummyKeyobject, body)).to.equal(LH_ERROR_MESSAGES["js_executor_invalid_throttling"]);
    });

    it("should return invalid catehories format error for invalid categories format in body", async () => {
      delete dummyKeyobject.lighthouseAutomate.report_limit;
      body.assertResult = { categories: '123'};
      expect(lighthouseExecutorValidation(dummyKeyobject, body)).to.equal(LH_ERROR_MESSAGES["js_executor_invalid_categories_format"]);
    });

    it("should return invalid catehories error for invalid categories in body", async () => {
      delete dummyKeyobject.lighthouseAutomate.report_limit;
      body.assertResult = { categories: { abc: '' }};
      expect(lighthouseExecutorValidation(dummyKeyobject, body)).to.equal(LH_ERROR_MESSAGES["js_executor_invalid_categories"]);
    });

    it("should return invalid catehories error for invalid categories in body", async () => {
      delete dummyKeyobject.lighthouseAutomate.report_limit;
      body.assertResult = { categories: { performance: 500 }};
      expect(lighthouseExecutorValidation(dummyKeyobject, body)).to.equal(LH_ERROR_MESSAGES["js_executor_invalid_categories_value"]);
    });

    it("should return invalid metrics format error for invalid metrics format in body", async () => {
      delete dummyKeyobject.lighthouseAutomate.report_limit;
      body.assertResult = { metrics: 'abc'};
      expect(lighthouseExecutorValidation(dummyKeyobject, body)).to.equal(LH_ERROR_MESSAGES["js_executor_invalid_metrics_format"]);
    });

    it("should return invalid metrics format error for invalid metrics parameters in body", async () => {
      delete dummyKeyobject.lighthouseAutomate.report_limit;
      body.assertResult = { metrics: {'abc': 123}};
      expect(lighthouseExecutorValidation(dummyKeyobject, body)).to.equal(LH_ERROR_MESSAGES["js_executor_invalid_metrics_format"]);
    });

    it("should return invalid metrics value error for invalid moreThan metrics value in body", async () => {
      delete dummyKeyobject.lighthouseAutomate.report_limit;
      body.assertResult = { metrics: { "first-contentful-paint": { metricUnit: "score", moreThan: 'abc'}}};
      expect(lighthouseExecutorValidation(dummyKeyobject, body)).to.equal(LH_ERROR_MESSAGES["js_executor_invalid_metrics_value"]);
    });

    it("should return invalid metrics value error for invalid lessThan metrics value in body", async () => {
      delete dummyKeyobject.lighthouseAutomate.report_limit;
      body.assertResult = { metrics: { "first-contentful-paint": { metricUnit: "score", lessThan: 'abc'}}};
      expect(lighthouseExecutorValidation(dummyKeyobject, body)).to.equal(LH_ERROR_MESSAGES["js_executor_invalid_metrics_value"]);
    });

    it("should return invalid metrics value error if moreThan and lessThan metrics value present in body", async () => {
      delete dummyKeyobject.lighthouseAutomate.report_limit;
      body.assertResult = { metrics: { "first-contentful-paint": { metricUnit: "score", moreThan: 1500, lessThan: 1000}}};
      expect(lighthouseExecutorValidation(dummyKeyobject, body)).to.equal(LH_ERROR_MESSAGES["js_executor_invalid_metrics_value"]);
    });

    it("should return validated if all values and formats are correct", async () => {
      delete dummyKeyobject.lighthouseAutomate.report_limit;
      body.assertResult = { categories: { performance: 50 }, metrics: { "first-contentful-paint": { metricUnit: "score", lessThan: 1000}}};
      expect(lighthouseExecutorValidation(dummyKeyobject, body)).to.equal("validated");
    });

    it("should set assert_limit to CENTRAL_SCANNER_LIMIT if centralScanner is true", async () => {
      dummyKeyobject.centralScanner = true;
      delete dummyKeyobject.lighthouseAutomate.assert_limit;
      delete dummyKeyobject.lighthouseAutomate.report_limit;
      expect(dummyKeyobject.lighthouseAutomate.assert_limit).to.be.undefined;
      lighthouseExecutorValidation(dummyKeyobject, {});
      expect(dummyKeyobject.lighthouseAutomate.assert_limit).to.equal(1000);
    });
  });

  describe('fixCustomBrowserstackArgs', () => {
    let fixCustomBrowserstackArgs, body;

    beforeEach(() => {
      fixCustomBrowserstackArgs = TriggerLightHouse.__get__(
        "fixCustomBrowserstackArgs"
      );
      body = { abc: 123 }
    });

    it("should return if body isn't present", async () => {
      let test = null; // please don't judge me, had to do to increase branch coverage
      fixCustomBrowserstackArgs(test)
      expect(test).to.be.null;
    });

    it("should fix body if throttling passed", async () => {
      body.throttling = 'mobileSlow4G';
      const expected = {
        abc: 123,
        lhConfig: {
          extends: "lighthouse:default",
          settings: {
            throttling: THROTTLING_PROFILE["mobileSlow4G"],
          },
        },
      };
      fixCustomBrowserstackArgs(body)
      expect(body).to.deep.equal(expected);
    });

    it("should fix body if throttling passed and use default config if not config is passed", async () => {
      body.throttling = 'mobileSlow4G';
      const expected = {
        abc: 123,
        lhConfig: {
          extends: "lighthouse:default",
          settings: {
            throttling: THROTTLING_PROFILE["mobileSlow4G"],
          },
        },
      };
      fixCustomBrowserstackArgs(body)
      expect(body).to.deep.equal(expected);
    });

    it("should fix body if non numerical categories passed", async () => {
      body.assertResult = { categories: { performance: 'abc'}}
      const expected = {abc: 123, assertResult: { categories: { performance: 50}}};
      fixCustomBrowserstackArgs(body)
      expect(body).to.deep.equal(expected);
    });

    it("should fix body if float point categories passed", async () => {
      body.assertResult = { categories: { performance: 50.45}}
      const expected = {abc: 123, assertResult: { categories: { performance: 50}}};
      fixCustomBrowserstackArgs(body)
      expect(body).to.deep.equal(expected);
    });
  });

  describe('processLighthouseAssertResult', () => {
    let processLighthouseAssertResult, lhResponse, result;

    beforeEach(() => {
      processLighthouseAssertResult = TriggerLightHouse.__get__(
        "processLighthouseAssertResult"
      );
      lhResponse = {
        finalUrl: "google.com",
        categories: { performance: { score: 0.5 } },
        audits: {
          "first-contentful-paint": {
            score: 0.6,
          },
          "largest-contentful-paint": {
            numericValue: 1000,
          },
        },
      };
      result = {
        url: "google.com",
        failTest: false
      }
    });

    it("should return performance passed if categories score passed", async () => {
      const assertResult = {
        categories: { performance: 40 }
      }
      result.categories = { performance: "passed" };
      expect(processLighthouseAssertResult(assertResult, JSON.stringify(lhResponse))).to.deep.equal(result);
    });

    it("should return performance failed if categories score passed", async () => {
      const assertResult = {
        categories: { performance: 100 }
      }
      result.categories = { performance: "failed" };
      result.failTest = true;
      expect(processLighthouseAssertResult(assertResult, JSON.stringify(lhResponse))).to.deep.equal(result);
    });

    it("should return failTest true if categories not present", async () => {
      const assertResult = {
        categories: { performance: 40, "best-practices": 50 }
      }
      result.categories = { performance: "passed", "best-practices": "failed" };
      result.failTest = true;
      expect(processLighthouseAssertResult(assertResult, JSON.stringify(lhResponse))).to.deep.equal(result);
    });

    it("should return failTest true if metrics value not present", async () => {
      const assertResult = {
        metrics: {
          "abc": {
            moreThan: 100,
            metricUnit: "score",
          },
        },
      };
      result.metrics = { abc: "failed" };
      result.failTest = true;
      expect(processLighthouseAssertResult(assertResult, JSON.stringify(lhResponse))).to.deep.equal(result);
    });


    it("should return failTest true if metrics numerical score doesn't pass", async () => {
      const assertResult = {
        metrics: {
          "first-contentful-paint": {
            moreThan: 60,
            metricUnit: "score",
          },
        },
      };
      result.metrics = { "first-contentful-paint": "failed" };
      result.failTest = true;
      expect(processLighthouseAssertResult(assertResult, JSON.stringify(lhResponse))).to.deep.equal(result);
    });

    it("should pass if sucessful metrics numerical score passes for moreThan", async () => {
      const assertResult = {
        metrics: {
          "first-contentful-paint": {
            moreThan: 40,
            metricUnit: "score",
          },
        },
      };
      result.metrics = { "first-contentful-paint": "passed" };
      expect(processLighthouseAssertResult(assertResult, JSON.stringify(lhResponse))).to.deep.equal(result);
    });

    it("should pass if sucessful metrics numerical value passes for lessThan", async () => {
      const assertResult = {
        metrics: {
          "largest-contentful-paint": {
            lessThan: 2000,
            metricUnit: "numericValue",
          },
        },
      };
      result.metrics = { "largest-contentful-paint": "passed" };
      expect(processLighthouseAssertResult(assertResult, JSON.stringify(lhResponse))).to.deep.equal(result);
    });
  });

  describe('sendResponse', () => {
    let sendResponse, requestStateObj, response, instrumentExecutorStatusAndSendResponseStub, executorStatus, errorString;

    beforeEach(() => {
      sendResponse = TriggerLightHouse.__get__(
        "sendResponse"
      );
      instrumentExecutorStatusAndSendResponseStub = sinon.stub(customExecutorHelper, "instrumentExecutorStatusAndSendResponse");
      requestStateObj = { clientSessionID: 123 }
      response = 'testing';
      executorStatus = 'success';
      errorString = '';
    });
    afterEach(() => {
      customExecutorHelper.instrumentExecutorStatusAndSendResponse.restore();
    })

    it("should call instrumentExecutorStatusAndSendResponse with modified request", async () => {
      sendResponse(keyObject, requestStateObj, {action: 'some-action'}, response, executorStatus, errorString);
      const modifiedRequestStateObj = {
        hash: "GET:value",
        data: JSON.stringify({
          sessionId: requestStateObj.clientSessionID,
          value: response,
        })
      }
      sinon.assert.calledOnce(instrumentExecutorStatusAndSendResponseStub);
      instrumentExecutorStatusAndSendResponseStub.calledWith(executorStatus, errorString, 'some-action', keyObject, modifiedRequestStateObj, false)
    });

  });

  describe('getPerformanceLogData', () => {
    let getPerformanceLogData, getSignedUrlStub;

    beforeEach(() => {
      getPerformanceLogData = TriggerLightHouse.__get__(
        "getPerformanceLogData"
      );
      getSignedUrlStub = sinon.stub(AWS.S3.prototype, "getSignedUrl");
      sinon.stub(HubLogger, "miscLogger");
    });
    afterEach(() => {
      getSignedUrlStub.restore();
      HubLogger.miscLogger.restore();
    })

    it("should return empty string if no finalList", async () => {
      expect(getPerformanceLogData(keyObject)).to.equal('');
      sinon.assert.notCalled(getSignedUrlStub);
    });

    it("should return last url for limit reached", async () => {
      keyObject.lighthouseAutomate.finalList = [{ report_count: 1}];
      keyObject.lighthouseAutomate.last_url = 'some_url';
      keyObject.lighthouseAutomate.limit_reached = true;
      const result = '{"url":"some_url"}';
      expect(getPerformanceLogData(keyObject)).to.equal(result);
      sinon.assert.notCalled(getSignedUrlStub);
    });

    it("should return presigned s3 url for success case", async () => {
      keyObject.lighthouseAutomate.finalList = [{ report_count: 1}];
      keyObject.lighthouseAutomate.last_url = 'some_url';
      keyObject.lighthouseAutomate.limit_reached = false;
      getSignedUrlStub.returns('s3_url');
      const result = '{"url":"some_url","s3Url":"s3_url"}';
      expect(getPerformanceLogData(keyObject)).to.equal(result);
      sinon.assert.calledOnce(getSignedUrlStub);
    });

    it("should handle errors in s3 generate url", async () => {
      keyObject.lighthouseAutomate.finalList = [{ report_count: 1}];
      keyObject.lighthouseAutomate.last_url = 'some_url';
      keyObject.lighthouseAutomate.limit_reached = false;
      getSignedUrlStub.throws(new Error("some error"));
      const result = '{"url":"some_url"}';
      expect(getPerformanceLogData(keyObject)).to.equal(result);
      sinon.assert.calledOnce(getSignedUrlStub);
      sinon.assert.calledWith(HubLogger.miscLogger, `lighthouse`, `Error generating url: some error`, constants.LOG_LEVEL.ERROR);
    });
  });

  describe('triggerLighthouse', () => {
    let triggerLighthouse;
    let requestToTerminalOg = TriggerLightHouse.__get__(
      "requestToTerminal"
    );

    beforeEach(() => {
      triggerLighthouse = TriggerLightHouse.__get__(
        "triggerLighthouse"
      );
      sinon.stub(HubLogger, "miscLogger");
    });

    afterEach(() => {
      TriggerLightHouse.__set__(
        "requestToTerminal",
        requestToTerminalOg
      );
      HubLogger.miscLogger.restore();
    });

    it("should log if requestToTerminal throws error", async () => {
      keyObject.sessionId = 123;
      const requestToTerminalObject = {
        requestToTerminal: TriggerLightHouse.__get__(
          "requestToTerminal"
        ),
      };
      const requestToTerminalStub = sinon
        .stub(requestToTerminalObject, "requestToTerminal")
        .throws(new Error("some error"));
      TriggerLightHouse.__set__(
        "requestToTerminal",
        requestToTerminalStub
      );
      await triggerLighthouse(url, keyObject, "SELENIUM");
      sinon.assert.called(requestToTerminalStub);
      sinon.assert.calledWith(HubLogger.miscLogger, `lighthouse`, `lhSELENIUM - session: 123 - url: ${url} - error: Error: some error`, constants.LOG_LEVEL.ERROR);
    });

    it("should call requestToTerminal if counter is valid", async () => {
      const requestToTerminalObject = {
        requestToTerminal: TriggerLightHouse.__get__(
          "requestToTerminal"
        ),
      };
      const requestToTerminalStub = sinon
        .stub(requestToTerminalObject, "requestToTerminal")
        .returns(successData);
      TriggerLightHouse.__set__(
        "requestToTerminal",
        requestToTerminalStub
      );
      await triggerLighthouse(url, keyObject, "SELENIUM");
      sinon.assert.called(requestToTerminalStub);
    });

    it("should call requestToTerminal if counter is valid and assert param is passed", async () => {
      const requestToTerminalObject = {
        requestToTerminal: TriggerLightHouse.__get__(
          "requestToTerminal"
        ),
      };
      const requestToTerminalStub = sinon
        .stub(requestToTerminalObject, "requestToTerminal")
        .returns(successData);
      TriggerLightHouse.__set__(
        "requestToTerminal",
        requestToTerminalStub
      );
      await triggerLighthouse(url, keyObject, "SELENIUM", {type: "assert"});
      sinon.assert.called(requestToTerminalStub);
    });

    it("should call requestToTerminal if counter is valid and assert param is passed and report counter isn't passed", async () => {
      const requestToTerminalObject = {
        requestToTerminal: TriggerLightHouse.__get__(
          "requestToTerminal"
        ),
      };
      const requestToTerminalStub = sinon
        .stub(requestToTerminalObject, "requestToTerminal")
        .returns(successData);
      TriggerLightHouse.__set__(
        "requestToTerminal",
        requestToTerminalStub
      );
      const ogReportCounter = keyObject.lighthouseAutomate.report_counter;
      delete keyObject.lighthouseAutomate.report_counter;
      await triggerLighthouse(url, keyObject, "SELENIUM", {type: "assert"});
      keyObject.lighthouseAutomate.report_counter = ogReportCounter;
      sinon.assert.called(requestToTerminalStub);
    });

    it("should call not requestToTerminal if counter is not valid", async () => {
      const requestToTerminalObject = {
        requestToTerminal: TriggerLightHouse.__get__(
          "requestToTerminal"
        ),
      };
      const requestToTerminalStub = sinon
        .stub(requestToTerminalObject, "requestToTerminal")
        .returns(successData);
      TriggerLightHouse.__set__(
        "requestToTerminal",
        requestToTerminalStub
      );

      keyObject.lighthouseAutomate.report_counter = 5;
      await triggerLighthouse(url, keyObject, "SELENIUM");
      sinon.assert.notCalled(requestToTerminalStub);
      keyObject.lighthouseAutomate.report_counter = 1;
    });

    it("should return undefined url error if undefined url passed", async () => {
      const requestToTerminalObject = {
        requestToTerminal: TriggerLightHouse.__get__(
          "requestToTerminal"
        ),
      };
      const requestToTerminalStub = sinon
        .stub(requestToTerminalObject, "requestToTerminal")
        .returns(successData);
      TriggerLightHouse.__set__(
        "requestToTerminal",
        requestToTerminalStub
      );

      let url;
      expect(await triggerLighthouse(url, keyObject, "SELENIUM")).to.equal(LH_ERROR_MESSAGES["undefined_test_url"]);
      sinon.assert.notCalled(requestToTerminalStub);
    });

    it("should return lh error if limit is not defined", async () => {
      const requestToTerminalObject = {
        requestToTerminal: TriggerLightHouse.__get__(
          "requestToTerminal"
        ),
      };
      const requestToTerminalStub = sinon
        .stub(requestToTerminalObject, "requestToTerminal")
        .returns(successData);
      TriggerLightHouse.__set__(
        "requestToTerminal",
        requestToTerminalStub
      );

      delete keyObject.lighthouseAutomate.report_limit;
      expect(await triggerLighthouse(url, keyObject, "SELENIUM")).to.equal(LH_ERROR_MESSAGES["lh_error"]);
      sinon.assert.notCalled(requestToTerminalStub);
    });
  });

  describe('getValidDevtoolsPort', () => {
    let getValidDevtoolsPort;

    beforeEach(() => {
      getValidDevtoolsPort = TriggerLightHouse.__get__(
        "getValidDevtoolsPort"
      );
    });

    it("should return 9222 for selenium", async () => {
      expect(getValidDevtoolsPort("selenium", keyObject)).to.equal(9222);
    });

    it("should return 9225 for playright if not isLaunchPersistentContext", async () => {
      expect(getValidDevtoolsPort("playwright", keyObject)).to.equal("9225");
    });

    it("should return 9225 for playright if not isLaunchPersistentContext", async () => {
      expect(getValidDevtoolsPort("playwright", keyObject)).to.equal("9225");
    });

    it("should return 9225 for playright if isLaunchPersistentContext", async () => {
      keyObject.isLaunchPersistentContext = true;
      expect(getValidDevtoolsPort("playwright", keyObject)).to.equal(9222);
      delete keyObject.isLaunchPersistentContext;
    });
  });

  describe('checkValidPlaywrightCommand', () => {
    let checkValidPlaywrightCommand;

    beforeEach(() => {
      checkValidPlaywrightCommand = TriggerLightHouse.__get__(
        "checkValidPlaywrightCommand"
      );
    });

    it("should return true on goto command", async () => {
      expect(checkValidPlaywrightCommand({method: "goto"})).to.equal(true);
    });

    it("should return true on find command", async () => {
      expect(checkValidPlaywrightCommand({method: "Page.navigate"})).to.equal(false);
    });
  });

  describe('checkValidPuppeteerCommand', () => {
    let checkValidPuppeteerCommand;

    beforeEach(() => {
      checkValidPuppeteerCommand = TriggerLightHouse.__get__(
        "checkValidPuppeteerCommand"
      );
    });

    it("should return false on goto command", async () => {
      expect(checkValidPuppeteerCommand({method: "goto"})).to.equal(false);
    });

    it("should return true on find command", async () => {
      expect(checkValidPuppeteerCommand({method: "Page.navigate"})).to.equal(true);
    });
  });

  describe('getCurrentOpenUrlOnChromium', () => {
    let getCurrentOpenUrlOnChromium;
    let requestLibCallStub;

    const openUrlSuccessResponse = {
      statusCode: 200,
      data: `{"cmd_executed":"true","data":[{"description":"",
      "devtoolsFrontendUrl":"/devtools/inspector.html?ws=localhost:9222/devtools/page/F21B029EFBE33B718B937000EE85E4C4",
      "faviconUrl":"https://github.githubassets.com/favicons/favicon.svg","id":"F21B029EFBE33B718B937000EE85E4C4",
      "title":"GitHub: Lets build from here · GitHub","type":"page","url":"https://github.com/",
      "webSocketDebuggerUrl":"ws://localhost:9222/devtools/page/F21B029EFBE33B718B937000EE85E4C4"}]}`
    };

    const openUrlTerminalNon200Response = {
      statusCode: 404,
      data: `something`
    };

    const openUrlTerminalUndefinedData = {
      statusCode: 200
    };

    const openUrlCDPCommandNotExecutedResponse = {
      statusCode: 200,
      data: `{"cmd_executed":"false"}`
    };

    const openUrlNoDataResponse = {
      statusCode: 200,
      data: `{"cmd_executed":"true"}`
    };

    const openUrlNonArrayDataResponse = {
      statusCode: 200,
      data: `{"cmd_executed":"true","data":{"key":"value"}}`
    };

    const openUrlEmptyArrayDataResponse = {
      statusCode: 200,
      data: `{"cmd_executed":"true","data":[]}`
    };

    const openUrlNoPageDataResponse = {
      statusCode: 200,
      data: `{"cmd_executed":"true","data":[{"description":"",
      "devtoolsFrontendUrl":"/devtools/inspector.html?ws=localhost:9222/devtools/page/F21B029EFBE33B718B937000EE85E4C4",
      "faviconUrl":"https://github.githubassets.com/favicons/favicon.svg","id":"F21B029EFBE33B718B937000EE85E4C4",
      "title":"GitHub: Lets build from here · GitHub","type":"service_worker","url":"https://github.com/",
      "webSocketDebuggerUrl":"ws://localhost:9222/devtools/page/F21B029EFBE33B718B937000EE85E4C4"}]}`
    };

    const openUrlMalformedPageDataResponse = {
      statusCode: 200,
      data: `{"cmd_executed":"true","data":[{"description":"",
      "devtoolsFrontendUrl":"/devtools/inspector.html?ws=localhost:9222/devtools/page/F21B029EFBE33B718B937000EE85E4C4",
      "faviconUrl":"https://github.githubassets.com/favicons/favicon.svg","id":"F21B029EFBE33B718B937000EE85E4C4",
      "title":"GitHub: Lets build from here · GitHub","type":"page",
      "webSocketDebuggerUrl":"ws://localhost:9222/devtools/page/F21B029EFBE33B718B937000EE85E4C4"}]}`
    };

    beforeEach(() => {
      requestLibCallStub = sinon.stub(requestlib, "call");
      sinon.stub(requestlib, "appendBStackHostHeader");
      sinon.stub(HubLogger, "miscLogger");
      getCurrentOpenUrlOnChromium = TriggerLightHouse.__get__(
        "getCurrentOpenUrlOnChromium"
      );
    });

    afterEach(() => {
      requestLibCallStub.restore();
      requestlib.appendBStackHostHeader.restore();
      HubLogger.miscLogger.restore();
    });

    it("should do a request.call and return the url if everything goes well for playwright - os x", async () => {
      requestLibCallStub.returns(openUrlSuccessResponse);
      const url = await getCurrentOpenUrlOnChromium("playwright", keyObject);
      expect(typeof(url)).equal("string");
      sinon.assert.called(requestlib.appendBStackHostHeader);
    });

    it("should do a request.call and return the url if everything goes well for puppeteer - os x", async () => {
      requestLibCallStub.returns(openUrlSuccessResponse);
      const url = await getCurrentOpenUrlOnChromium("puppeteer", keyObject);
      expect(typeof(url)).equal("string");
      sinon.assert.called(requestlib.appendBStackHostHeader);
    });

    it("should do a request.call and return the url if everything goes well for playwright - windows", async () => {
      requestLibCallStub.returns(openUrlSuccessResponse);
      keyObject.os = "win";
      const url = await getCurrentOpenUrlOnChromium("playwright", keyObject);
      keyObject.os = "mac";
      expect(typeof(url)).equal("string");
      sinon.assert.called(requestlib.appendBStackHostHeader);
    });

    it("should do a request.call and return the url if everything goes well for puppeteer - windows", async () => {
      requestLibCallStub.returns(openUrlSuccessResponse);
      keyObject.os = "win";
      const url = await getCurrentOpenUrlOnChromium("puppeteer", keyObject);
      keyObject.os = "mac";
      expect(typeof(url)).equal("string");
      sinon.assert.called(requestlib.appendBStackHostHeader);
    });

    it("should do a request.call and return undefined and throw error if terminal returns an non 200 reponse code", async () => {
      requestLibCallStub.returns(openUrlTerminalNon200Response);
      const url = await getCurrentOpenUrlOnChromium("playwright", keyObject);
      expect(url).equal(undefined);
      sinon.assert.called(requestlib.appendBStackHostHeader);
      sinon.assert.calledWith(HubLogger.miscLogger, `lighthouse`, `Error occurred while getting open url. Error Details: Exception raised in terminal: ${JSON.stringify(openUrlTerminalNon200Response.data)}`, constants.LOG_LEVEL.ERROR);
    });

    it("should do a request.call and return undefined and throw error if terminal returns no data object", async () => {
      requestLibCallStub.returns(openUrlTerminalUndefinedData);
      const url = await getCurrentOpenUrlOnChromium("playwright", keyObject);
      expect(url).equal(undefined);
      sinon.assert.called(requestlib.appendBStackHostHeader);
      sinon.assert.calledWith(HubLogger.miscLogger, `lighthouse`, `Error occurred while getting open url. Error Details: Exception raised in terminal: ${JSON.stringify(openUrlTerminalUndefinedData.data)}`, constants.LOG_LEVEL.ERROR);
    });

    it("should do a request.call and return undefined and throw error if terminal does not execute the command data", async () => {
      requestLibCallStub.returns(openUrlCDPCommandNotExecutedResponse);
      const url = await getCurrentOpenUrlOnChromium("playwright", keyObject);
      expect(url).equal(undefined);
      sinon.assert.called(requestlib.appendBStackHostHeader);
      sinon.assert.calledWith(HubLogger.miscLogger, `lighthouse`, `Error occurred while getting open url. Error Details: CDP command not executed`, constants.LOG_LEVEL.ERROR);
    });

    it("should do a request.call and return undefined and throw error if terminal does not return data", async () => {
      requestLibCallStub.returns(openUrlNoDataResponse);
      const url = await getCurrentOpenUrlOnChromium("playwright", keyObject);
      expect(url).equal(undefined);
      sinon.assert.called(requestlib.appendBStackHostHeader);
      sinon.assert.calledWith(HubLogger.miscLogger, `lighthouse`, `Error occurred while getting open url. Error Details: Received no data from Terminal`, constants.LOG_LEVEL.ERROR);
    });

    it("should do a request.call and return undefined and throw error if terminal does not return data in proper format (Non Array)", async () => {
      requestLibCallStub.returns(openUrlNonArrayDataResponse);
      const url = await getCurrentOpenUrlOnChromium("playwright", keyObject);
      expect(url).equal(undefined);
      sinon.assert.called(requestlib.appendBStackHostHeader);
      sinon.assert.calledWith(HubLogger.miscLogger, `lighthouse`, `Error occurred while getting open url. Error Details: Received no data from Terminal`, constants.LOG_LEVEL.ERROR);
    });

    it("should do a request.call and return undefined and throw error if terminal returns empty data array", async () => {
      requestLibCallStub.returns(openUrlEmptyArrayDataResponse);
      const url = await getCurrentOpenUrlOnChromium("playwright", keyObject);
      expect(url).equal(undefined);
      sinon.assert.called(requestlib.appendBStackHostHeader);
      sinon.assert.calledWith(HubLogger.miscLogger, `lighthouse`, `Error occurred while getting open url. Error Details: Received no data from Terminal`, constants.LOG_LEVEL.ERROR);
    });

    it("should do a request.call and return undefined and throw error if terminal returns no page type object data", async () => {
      requestLibCallStub.returns(openUrlNoPageDataResponse);
      const url = await getCurrentOpenUrlOnChromium("playwright", keyObject);
      expect(url).equal(undefined);
      sinon.assert.called(requestlib.appendBStackHostHeader);
      sinon.assert.calledWith(HubLogger.miscLogger, `lighthouse`, `Error occurred while getting open url. Error Details: Received no page data from Terminal`, constants.LOG_LEVEL.ERROR);
    });

    it("should do a request.call and return undefined and throw error if terminal returns corrupted (no url key) page type object data", async () => {
      requestLibCallStub.returns(openUrlMalformedPageDataResponse);
      const url = await getCurrentOpenUrlOnChromium("playwright", keyObject);
      expect(url).equal(undefined);
      sinon.assert.called(requestlib.appendBStackHostHeader);
      sinon.assert.calledWith(HubLogger.miscLogger, `lighthouse`, `Error occurred while getting open url. Error Details: Received corrupt page data from Terminal`, constants.LOG_LEVEL.ERROR);
    });
  });

  describe('lighthouseExecutorHandler', () => {
    let lighthouseExecutorHandler, fixCustomBrowserstackArgsStub, lighthouseExecutorValidationStub, lighthouseExecutorStub, sendResponseStub;
    let sendResponseOg = TriggerLightHouse.__get__(
      "sendResponse"
    );
    let fixCustomBrowserstackArgsOg = TriggerLightHouse.__get__(
      "fixCustomBrowserstackArgs"
    );
    let lighthouseExecutorOg = TriggerLightHouse.__get__(
      "lighthouseExecutor"
    );
    let lighthouseExecutorValidationOg = TriggerLightHouse.__get__(
      "lighthouseExecutorValidation"
    );

    beforeEach(() => {
      lighthouseExecutorHandler = TriggerLightHouse.__get__(
        "lighthouseExecutorHandler"
      );
      const fixCustomBrowserstackArgsObject = {
        fixCustomBrowserstackArgs: TriggerLightHouse.__get__(
          "fixCustomBrowserstackArgs"
        ),
      };
      fixCustomBrowserstackArgsStub = sinon
        .stub(fixCustomBrowserstackArgsObject, "fixCustomBrowserstackArgs");
      TriggerLightHouse.__set__(
        "fixCustomBrowserstackArgs",
        fixCustomBrowserstackArgsStub
      );

      const lighthouseExecutorValidationObject = {
        lighthouseExecutorValidation: TriggerLightHouse.__get__(
          "lighthouseExecutorValidation"
        ),
      };
      lighthouseExecutorValidationStub = sinon
        .stub(lighthouseExecutorValidationObject, "lighthouseExecutorValidation")
        .returns("validated");
      TriggerLightHouse.__set__(
        "lighthouseExecutorValidation",
        lighthouseExecutorValidationStub
      );

      const lighthouseExecutorObject = {
        lighthouseExecutor: TriggerLightHouse.__get__(
          "lighthouseExecutor"
        ),
      };
      lighthouseExecutorStub = sinon
        .stub(lighthouseExecutorObject, "lighthouseExecutor");
      TriggerLightHouse.__set__(
        "lighthouseExecutor",
        lighthouseExecutorStub
      );

      const sendResponseObject = {
        sendResponse: TriggerLightHouse.__get__(
          "sendResponse"
        ),
      };
      sendResponseStub = sinon
        .stub(sendResponseObject, "sendResponse");
      TriggerLightHouse.__set__(
        "sendResponse",
        sendResponseStub
      );
    });

    afterEach(() => {
      TriggerLightHouse.__set__(
        "sendResponse",
        sendResponseOg
      );
      TriggerLightHouse.__set__(
        "fixCustomBrowserstackArgs",
        fixCustomBrowserstackArgsOg
      );
      TriggerLightHouse.__set__(
        "lighthouseExecutor",
        lighthouseExecutorOg
      );
      TriggerLightHouse.__set__(
        "lighthouseExecutorValidation",
        lighthouseExecutorValidationOg
      );
    });

    it("should call the executor if validation succeeds", async () => {
      lighthouseExecutorValidationStub.returns('validated');
      lighthouseExecutorHandler(keyObject, {}, {});
      sinon.assert.calledOnce(lighthouseExecutorStub);
      sinon.assert.notCalled(sendResponseStub);
    });

    it("should not call the executor if validation fails", async () => {
      lighthouseExecutorValidationStub.returns('validation error');
      lighthouseExecutorHandler(keyObject, {}, {});
      sinon.assert.calledOnce(sendResponseStub);
      sendResponseStub.calledWith(keyObject, {}, {}, {status: "error", value: "validation error"});
      sinon.assert.notCalled(lighthouseExecutorStub);
    });

  });

  describe('lighthouseExecutor', () => {
    let lighthouseExecutor, getCurrentOpenUrlOnChromiumStub, triggerLighthouseStub, processLighthouseAssertResultStub, sendResponseStub, markSessionFailedStub, getOpenUrlStub, parsedCommand;
    let sendResponseOg = TriggerLightHouse.__get__(
      "sendResponse"
    );
    let getCurrentOpenUrlOnChromiumOg = TriggerLightHouse.__get__(
      "getCurrentOpenUrlOnChromium"
    );
    let processLighthouseAssertResultOg = TriggerLightHouse.__get__(
      "processLighthouseAssertResult"
    );
    let triggerLighthouseOg = TriggerLightHouse.__get__(
      "triggerLighthouse"
    );
    let markSessionFailedOg = TriggerLightHouse.__get__(
      "markSessionFailed"
    );
    let getOpenUrlOg = TriggerLightHouse.__get__(
      "getOpenUrl"
    );

    beforeEach(() => {
      lighthouseExecutor = TriggerLightHouse.__get__(
        "lighthouseExecutor"
      );
      const getCurrentOpenUrlOnChromiumObject = {
        getCurrentOpenUrlOnChromium: TriggerLightHouse.__get__(
          "getCurrentOpenUrlOnChromium"
        ),
      };
      getCurrentOpenUrlOnChromiumStub = sinon
        .stub(getCurrentOpenUrlOnChromiumObject, "getCurrentOpenUrlOnChromium");
      TriggerLightHouse.__set__(
        "getCurrentOpenUrlOnChromium",
        getCurrentOpenUrlOnChromiumStub
      );

      const triggerLighthouseObject = {
        triggerLighthouse: TriggerLightHouse.__get__(
          "triggerLighthouse"
        ),
      };
      triggerLighthouseStub = sinon
        .stub(triggerLighthouseObject, "triggerLighthouse");
      TriggerLightHouse.__set__(
        "triggerLighthouse",
        triggerLighthouseStub
      );

      const processLighthouseAssertResultObject = {
        processLighthouseAssertResult: TriggerLightHouse.__get__(
          "processLighthouseAssertResult"
        ),
      };
      processLighthouseAssertResultStub = sinon
        .stub(processLighthouseAssertResultObject, "processLighthouseAssertResult");
      TriggerLightHouse.__set__(
        "processLighthouseAssertResult",
        processLighthouseAssertResultStub
      );

      const sendResponseObject = {
        sendResponse: TriggerLightHouse.__get__(
          "sendResponse"
        ),
      };
      sendResponseStub = sinon
        .stub(sendResponseObject, "sendResponse");
      TriggerLightHouse.__set__(
        "sendResponse",
        sendResponseStub
      );

      const markSessionFailedObject = {
        markSessionFailed: TriggerLightHouse.__get__(
          "markSessionFailed"
        ),
      };
      markSessionFailedStub = sinon
        .stub(markSessionFailedObject, "markSessionFailed");
      TriggerLightHouse.__set__(
        "markSessionFailed",
        markSessionFailedStub
      );

      const getOpenUrlObject = {
        getOpenUrl: TriggerLightHouse.__get__(
          "getOpenUrl"
        ),
      };
      getOpenUrlStub = sinon
        .stub(getOpenUrlObject, "getOpenUrl");
      TriggerLightHouse.__set__(
        "getOpenUrl",
        getOpenUrlStub
      );

      sinon.stub(HubLogger, "miscLogger");
      parsedCommand = {
        arguments: {
          url: url,
          executorOutput: "json"
        }
      }
    });

    afterEach(() => {
      getCurrentOpenUrlOnChromiumStub.restore()
      triggerLighthouseStub.restore()
      processLighthouseAssertResultStub.restore()
      sendResponseStub.restore()
      markSessionFailedStub.restore()
      getOpenUrlStub.restore()
      TriggerLightHouse.__set__(
        "sendResponse",
        sendResponseOg
      );
      TriggerLightHouse.__set__(
        "getCurrentOpenUrlOnChromium",
        getCurrentOpenUrlOnChromiumOg
      );
      TriggerLightHouse.__set__(
        "processLighthouseAssertResult",
        processLighthouseAssertResultOg
      );
      TriggerLightHouse.__set__(
        "triggerLighthouse",
        triggerLighthouseOg
      );
      TriggerLightHouse.__set__(
        "markSessionFailed",
        markSessionFailedOg
      );
      TriggerLightHouse.__set__(
        "getOpenUrl",
        getOpenUrlOg
      );
      HubLogger.miscLogger.restore();

    });

    it("playwright: return error if no URL found and cannot get the URL", async () => {
      delete parsedCommand.arguments.url;
      keyObject.isPlaywright = true;
      getCurrentOpenUrlOnChromiumStub.throws(new Error('some error'));
      await lighthouseExecutor(keyObject, {}, parsedCommand);
      sinon.assert.calledOnce(getCurrentOpenUrlOnChromiumStub);
      sinon.assert.calledOnce(sendResponseStub);
      sinon.assert.notCalled(triggerLighthouseStub);
    });

    it("selenium: return error if no URL found and cannot get the URL", async () => {
      keyObject.isPlaywright = false;
      keyObject.selenium_version = 123;
      delete parsedCommand.arguments.url;
      getOpenUrlStub.throws(new Error('some error'));
      await lighthouseExecutor(keyObject, {}, parsedCommand);
      sinon.assert.notCalled(triggerLighthouseStub);
      sinon.assert.calledOnce(getOpenUrlStub);
      sinon.assert.calledOnce(sendResponseStub);
    });

    it("selenium: returns error data when triggerLighthouse doesn't return lhSuccess", async () => {
      keyObject.isPlaywright = false;
      keyObject.selenium_version = 123;
      delete parsedCommand.arguments.url;
      getOpenUrlStub.returns({ data: `{ "value": "${url}" }`});
      triggerLighthouseStub.returns('{"report": "some_json"}')
      await lighthouseExecutor(keyObject, {}, parsedCommand);
      sinon.assert.calledOnce(getOpenUrlStub);
      sinon.assert.calledOnce(triggerLighthouseStub);
      sinon.assert.calledOnce(sendResponseStub);
    });

    it("selenium: returns error data when triggerLighthouse is not JSON", async () => {
      keyObject.isPlaywright = true;
      triggerLighthouseStub.returns('abc')
      await lighthouseExecutor(keyObject, {}, parsedCommand);
      const response = { status: "error", value: LH_ERROR_MESSAGES["js_executor_url_not_found"], performanceLogs: true };
      sendResponseStub.calledWith(keyObject, {}, parsedCommand, response);
      sinon.assert.calledOnce(triggerLighthouseStub);
      sinon.assert.calledOnce(sendResponseStub);
    });

    it("selenium: returns success data when triggerLighthouse is successful", async () => {
      keyObject.isPlaywright = true;
      triggerLighthouseStub.returns('{"lhSuccess": "true", "data": "some_json"}')
      await lighthouseExecutor(keyObject, {}, parsedCommand);
      sinon.assert.calledOnce(triggerLighthouseStub);
      sinon.assert.calledOnce(sendResponseStub);
    });

    it("returns success data alongwith executorOutput message when triggerLighthouse is successful and executor output specified is not json", async () => {
      keyObject.isPlaywright = true;
      delete parsedCommand.arguments.executorOutput;
      triggerLighthouseStub.returns('{"lhSuccess": "true", "data": "some_json"}')
      await lighthouseExecutor(keyObject, {}, parsedCommand);
      sinon.assert.calledOnce(sendResponseStub);
      sinon.assert.calledOnce(triggerLighthouseStub);
    });

    it("returns success data alongwith executorOutput message when triggerLighthouse is successful and executor output specified is not json", async () => {
      keyObject.isPlaywright = true;
      delete parsedCommand.arguments.executorOutput;
      triggerLighthouseStub.returns('{"lhSuccess": "true", "data": "some_json"}')
      await lighthouseExecutor(keyObject, {}, parsedCommand);
      const response = { status: "passed", value: { url: url }, performanceLogs: true };
      response.report = "Put executorOutput as json in arguments to get json report of the audit.";
      sendResponseStub.calledWith(keyObject, {}, parsedCommand, response);
      sinon.assert.calledOnce(sendResponseStub);
      sinon.assert.calledOnce(triggerLighthouseStub);
    });

    it("call processLighthouseAssertResult is assert result is passed", async () => {
      keyObject.isPlaywright = true;
      delete parsedCommand.arguments.executorOutput;
      triggerLighthouseStub.returns('{"lhSuccess": "true", "data": "some_json"}');
      parsedCommand.arguments.assertResult = {
        sessionFail: true,
        categories: {
          performance: 50
        }
      }
      processLighthouseAssertResultStub.returns({failTest: false});
      await lighthouseExecutor(keyObject, {}, parsedCommand);
      sinon.assert.calledOnce(triggerLighthouseStub);
      sinon.assert.calledOnce(processLighthouseAssertResultStub);
      sinon.assert.calledOnce(sendResponseStub);
      sinon.assert.notCalled(markSessionFailedStub);
    });

    it("call markSessionFailed is assert result is failed", async () => {
      keyObject.isPlaywright = true;
      delete parsedCommand.arguments.executorOutput;
      triggerLighthouseStub.returns('{"lhSuccess": "true", "data": "some_json"}');
      parsedCommand.arguments.assertResult = {
        sessionFail: true,
        categories: {
          performance: 50
        }
      }
      processLighthouseAssertResultStub.returns({failTest: true});
      await lighthouseExecutor(keyObject, {}, parsedCommand);
      sinon.assert.calledOnce(triggerLighthouseStub);
      sinon.assert.calledOnce(processLighthouseAssertResultStub);
      sinon.assert.calledOnce(sendResponseStub);
      sinon.assert.calledOnce(markSessionFailedStub);
    });

    it("call markSessionFailed is assert result is failed for metrics as well", async () => {
      keyObject.isPlaywright = true;
      delete parsedCommand.arguments.executorOutput;
      triggerLighthouseStub.returns('{"lhSuccess": "true", "data": "some_json"}');
      parsedCommand.arguments.assertResult = {
        sessionFail: true,
        metrics: {
          performance: 50
        }
      }
      processLighthouseAssertResultStub.returns({failTest: true});
      await lighthouseExecutor(keyObject, {}, parsedCommand);
      sinon.assert.calledOnce(triggerLighthouseStub);
      sinon.assert.calledOnce(processLighthouseAssertResultStub);
      sinon.assert.calledOnce(sendResponseStub);
      sinon.assert.calledOnce(markSessionFailedStub);
    });

    it("log exception if processLighthouseAssertResult throws exception", async () => {
      keyObject.isPlaywright = true;
      delete parsedCommand.arguments.executorOutput;
      triggerLighthouseStub.returns('{"lhSuccess": "true", "data": "some_json"}');
      parsedCommand.arguments.sessionFail = true;
      parsedCommand.arguments.assertResult = {
        categories: {
          performance: 50
        }
      }
      processLighthouseAssertResultStub.throws(new Error("some error"));
      await lighthouseExecutor(keyObject, {}, parsedCommand);
      sinon.assert.calledOnce(triggerLighthouseStub);
      sinon.assert.calledOnce(processLighthouseAssertResultStub);
      sinon.assert.calledOnce(sendResponseStub);
      sinon.assert.notCalled(markSessionFailedStub);
      sinon.assert.calledWith(HubLogger.miscLogger, `lighthouse`, `Error performing assertion: some error :: lhResponse: {"lhSuccess": "true", "data": "some_json"}`, constants.LOG_LEVEL.ERROR);
    });

    it("log exception if markSessionFailed throws exception", async () => {
      keyObject.isPlaywright = true;
      delete parsedCommand.arguments.executorOutput;
      triggerLighthouseStub.returns('{"lhSuccess": "true", "data": "some_json"}');
      parsedCommand.arguments.assertResult = {
        sessionFail: true,
        categories: {
          performance: 50
        }
      }
      processLighthouseAssertResultStub.returns({failTest: true});
      markSessionFailedStub.throws(new Error("some error"));
      await lighthouseExecutor(keyObject, {}, parsedCommand);
      sinon.assert.calledOnce(triggerLighthouseStub);
      sinon.assert.calledOnce(processLighthouseAssertResultStub);
      sinon.assert.calledOnce(sendResponseStub);
      sinon.assert.calledOnce(markSessionFailedStub);
      sinon.assert.calledWith(HubLogger.miscLogger, `lighthouse`, `Error performing assertion: some error :: lhResponse: {"lhSuccess": "true", "data": "some_json"}`, constants.LOG_LEVEL.ERROR);
    });

  });

});
