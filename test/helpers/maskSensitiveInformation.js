var maskSensitiveInformation = require('../../helpers/maskSensitiveInformation'),
  helper = require('../unit/helper');

describe('maskSensitiveInformation function', () => {
  it('masksendKeys and maskResponse', () => {
    let keyObject = helper.getKeyObject();
    let params = helper.getParamsObject();
    params.request.url = '/keys';
    let responseData = {
      string: JSON.stringify({ value: 'non_redacted_value' })
    };
    keyObject.maskSendKeys = true;
    keyObject.maskResponse = true;
    maskSensitiveInformation(params.request, keyObject, responseData);

    JSON.parse(params.request.log_data).value.should.deep.equal(['[REDACTED]']);
    JSON.parse(responseData.string).value.should.equal('[REDACTED]');
  });

  it('maskBasicAuth', () => {
    let keyObject = helper.getKeyObject();
    let params = helper.getParamsObject();
    keyObject.maskBasicAuth = true;
    params.request.url = '/url';
    params.request.log_data = '{"url":"http://testusername:<EMAIL>/testpath"}'
    let responseData = {};
    maskSensitiveInformation(params.request, keyObject, responseData);

    JSON.parse(params.request.log_data).url.should.equal('http://[REDACTED]@example.com/testpath');
  });
});
