'use strict';

const assert = require('assert');

const browserstackErrorBucketUtilHelper = require('../../helpers/browserstackErrorBucketUtil');
describe('detectEconnrefusedMidSession', () => {
    it('should return false if data does not contains econnrefused pattern', (done) => {
        let data = "random string";
  
        assert.strictEqual(browserstackErrorBucketUtilHelper.detectEconnrefusedMidSession(data), false);
        done();
      });

      it('should return true if data contains econnrefused pattern', (done) => {
        let data = "Appium error: An unknown server-side error occurred while processing the command. Original error: Could not proxy. Proxy error: Could not proxy command to remote server. Original error: Error: connect ECONNREFUSED 127.0.0.1:8403";
  
        assert.strictEqual(browserstackErrorBucketUtilHelper.detectEconnrefusedMidSession(data), true);
        done();
      });
});

describe('detectMissingAppIdKey', () => {
  it('should return false if data does not contains missing app id key pattern', (done) => {
      let data = "random string";

      assert.strictEqual(browserstackErrorBucketUtilHelper.detectMissingAppIdKey(data), false);
      done();
    });

    it('should return true if data contains missing app id key pattern', (done) => {
      let data = "Encountered internal error running command: Error: Missing parameter: appIdKey";

      assert.strictEqual(browserstackErrorBucketUtilHelper.detectMissingAppIdKey(data), true);
      done();
    });

    it('should return false if data is missing', (done) => {
      let data = undefined;

      assert.strictEqual(browserstackErrorBucketUtilHelper.detectMissingAppIdKey(data), false);
      done();
    });
});
