'use strict';

const assert = require('assert');

const appAutomateW3CHelper = require('../../helpers/w3CHelper');

describe('w3CHelper', () => {
  context('isW3CAppAutomateSession', () => {
    it('should return false if capabilities object is absent', (done) => {
      let desiredCapabilties = {
        "app": "bs://random_url",
        "device": "iphone X"
      }

      let caps = {
        "desiredCapabilities": desiredCapabilties
      }

      assert.strictEqual(appAutomateW3CHelper.isW3CAppAutomateSession(caps), false);
      done();
    });

    it('should return false if capabilities object do not have appium:app cap', (done) => {
      let capabilities = {
        "alwaysMatch": {
          "platform": "android",
          "bstack:options": {
          }
        },
        "firstMatch": [{}]
      };

      let desiredCapabilties = {
        "app": "bs://random_url",
        "device": "iphone X"
      }

      let caps = {
        "capabilities": capabilities,
        "desiredCapabilities": desiredCapabilties
      }

      assert.strictEqual(appAutomateW3CHelper.isW3CAppAutomateSession(caps), false);
      done();
    });

    it('should return true if capabilities object have appium:options and app cap', (done) => {
      let capabilities = {
        "alwaysMatch": {
          "platform": "android",
          "bstack:options": {
          },
          "appium:options": {
            "app": "bs://some_random_app_id"
          }
        },
        "firstMatch": [{}]
      };

      let desiredCapabilties = {
        "device": "iphone X"
      }

      let caps = {
        "capabilities": capabilities,
        "desiredCapabilities": desiredCapabilties
      }

      assert.strictEqual(appAutomateW3CHelper.isW3CAppAutomateSession(caps), true);
      done();
    });

    it('should return true if capabilities object have appium:options and appium:app cap', (done) => {
      let capabilities = {
        "alwaysMatch": {
          "platform": "android",
          "bstack:options": {
          },
          "appium:options": {
            "appium:app": "bs://some_random_app_id"
          }
        },
        "firstMatch": [{}]
      };

      let desiredCapabilties = {
        "device": "iphone X"
      }

      let caps = {
        "capabilities": capabilities,
        "desiredCapabilities": desiredCapabilties
      }

      assert.strictEqual(appAutomateW3CHelper.isW3CAppAutomateSession(caps), true);
      done();
    });

    it('should return true if capabilities object have appium:options and app cap outside', (done) => {
      let capabilities = {
        "alwaysMatch": {
          "platform": "android",
          "bstack:options": {
          },
          "appium:options": {
          }
        },
        "firstMatch": [{
          "appium:app": "bs://some_random_app_id"
        }]
      };

      let desiredCapabilties = {
        "device": "iphone X",
      }

      let caps = {
        "capabilities": capabilities,
        "desiredCapabilities": desiredCapabilties
      }

      assert.strictEqual(appAutomateW3CHelper.isW3CAppAutomateSession(caps), true);
      done();
    });

    it('should return false if capabilities object does not have appium:options and app cap', (done) => {
      let capabilities = {
        "alwaysMatch": {
          "platform": "android",
          "bstack:options": {
          }
        },
        "firstMatch": [{}]
      };

      let desiredCapabilties = {
        "device": "iphone X"
      }

      let caps = {
        "capabilities": capabilities,
        "desiredCapabilities": desiredCapabilties
      }

      assert.strictEqual(appAutomateW3CHelper.isW3CAppAutomateSession(caps), false);
      done();
    });

    it('should return false if capabilities object does have appium:options but not app cap', (done) => {
      let capabilities = {
        "alwaysMatch": {
          "platform": "android",
          "bstack:options": {
          },
          "appium:options": {
          }
        },
        "firstMatch": [{}]
      };

      let desiredCapabilties = {
        "device": "iphone X"
      }

      let caps = {
        "capabilities": capabilities,
        "desiredCapabilities": desiredCapabilties
      }

      assert.strictEqual(appAutomateW3CHelper.isW3CAppAutomateSession(caps), false);
      done();
    });

    it('should return true if capabilties object is present and desiredCapabilities is absent', (done) => {
      let capabilities = {
        "alwaysMatch": {
          "bstack:options": {
          }
        },
        "firstMatch": [{
          "appium:app": "bs://random_url"
        }]
      };

      let caps = {
        "capabilities": capabilities
      }

      assert.strictEqual(appAutomateW3CHelper.isW3CAppAutomateSession(caps), true);
      done();
    });

    it('should return true if app automate session and capabilities object has bstack:options', (done) => {
      let capabilities = {
        "alwaysMatch": {
          "bstack:options": {
          }
        },
        "firstMatch": [{
          "appium:app": "bs://random_url"
        }]
      };

      let desiredCapabilties = {
        "app": "bs://random_url",
        "device": "iphone X"
      }

      let caps = {
        "capabilities": capabilities,
        "desiredCapabilities": desiredCapabilties
      }

      assert.strictEqual(appAutomateW3CHelper.isW3CAppAutomateSession(caps), true);
      done();
    });

    it('should return true if bstack:options not present in capabiltiies but browserstack specific cap is also not present in desiredCaps', (done) => {
      let capabilities = {
        "alwaysMatch": {
          "device": "iPhone 11"
        },
        "firstMatch": [{
          "appium:app": "bs://random_url"
        }]
      };

      let desiredCapabilties = {
        "app": "bs://random_url"
      }

      let caps = {
        "capabilities": capabilities,
        "desiredCapabilities": desiredCapabilties
      }

      assert.strictEqual(appAutomateW3CHelper.isW3CAppAutomateSession(caps), true);
      done();
    });

    it('should return false if bstack:options not present in capabiltiies but browserstack specific cap is present in desiredCaps', (done) => {
      let capabilities = {
        "alwaysMatch": {
          "device": "iPhone 11"
        },
        "firstMatch": [{
          "appium:app": "bs://random_url"
        }]
      };

      let desiredCapabilties = {
        "app": "bs://random_url",
        "device": "iphone X",
        "browserstack.deviceLogs": "true"
      }

      let caps = {
        "capabilities": capabilities,
        "desiredCapabilities": desiredCapabilties
      }

      assert.strictEqual(appAutomateW3CHelper.isW3CAppAutomateSession(caps), false);
      done();
    });

    it('should return false if automation name doesnt support W3C', (done) => {
      let capabilities = {
        "alwaysMatch": {
          "bstack:options": {
          }
        },
        "firstMatch": [{
          "appium:app": "bs://random_url"
        }]
      };

      let desiredCapabilties = {
        "app": "bs://random_url",
        "device": "iphone X",
        "automationName": "YouIengine"
      }

      let caps = {
        "capabilities": capabilities,
        "desiredCapabilities": desiredCapabilties
      }

      assert.strictEqual(appAutomateW3CHelper.isW3CAppAutomateSession(caps), false);
      done();
    });
  });

  context('doDesiredCapsHasBrowserStackCapabilities', () => {
    it('should return false if desiredCapabilities are not present in caps', (done) => {
      let caps = {};
      assert.strictEqual(appAutomateW3CHelper.doDesiredCapsHasBrowserStackCapabilities(caps), false);
      done();
    });

    it('should return false if desiredCapabilities is empty hash', (done) => {
      let caps = {
        "desiredCapabilities": {}
      };
      assert.strictEqual(appAutomateW3CHelper.doDesiredCapsHasBrowserStackCapabilities(caps), false);
      done();
    });

    it('should return false if desiredCapabilities is not an hash', (done) => {
      let caps = {
        "desiredCapabilities": "random_string"
      };
      assert.strictEqual(appAutomateW3CHelper.doDesiredCapsHasBrowserStackCapabilities(caps), false);
      done();
    });

    it('should return true if desiredCapabilities is contains at least one browserstack cap from array', (done) => {
      let caps = {
        "desiredCapabilities": {
          "os_version": "7.1",
          "os": "android"
        }
      };
      assert.strictEqual(appAutomateW3CHelper.doDesiredCapsHasBrowserStackCapabilities(caps), true);
      done();
    });

    it('should return true if desiredCapabilities is contains at least one cap starting with browserstack.', (done) => {
      let caps = {
        "desiredCapabilities": {
          "browserstack.networkLogs": "true"
        }
      };
      assert.strictEqual(appAutomateW3CHelper.doDesiredCapsHasBrowserStackCapabilities(caps), true);
      done();
    });

    it('should return false if desiredCapabilties has browserstack.useW3c as browserstack specific cap only', (done) => {
      let desiredCapabilties = {
        "app": "bs://random_url",
        "browserstack.useW3c": true
      }

      let caps = {
        "desiredCapabilities": desiredCapabilties
      }

      assert.strictEqual(appAutomateW3CHelper.doDesiredCapsHasBrowserStackCapabilities(caps), false);
      done();
    });

    it('should return false if desiredCapabilties has browserstack.use_w3c as browserstack specific cap only', (done) => {
      let desiredCapabilties = {
        "app": "bs://random_url",
        "browserstack.use_w3c": true
      }

      let caps = {
        "desiredCapabilities": desiredCapabilties
      }

      assert.strictEqual(appAutomateW3CHelper.doDesiredCapsHasBrowserStackCapabilities(caps), false);
      done();
    });

    it('should return true if desiredCapabilties has browserstack.useW3c and some other browserstack specific cap', (done) => {
      let desiredCapabilties = {
        "app": "bs://random_url",
        "browserstack.useW3c": true,
        "browserstack.debug": true
      }

      let caps = {
        "desiredCapabilities": desiredCapabilties
      }

      assert.strictEqual(appAutomateW3CHelper.doDesiredCapsHasBrowserStackCapabilities(caps), true);
      done();
    });

    it('should return true if desiredCapabilties has browserstack.use_w3c and some other browserstack specific cap', (done) => {
      let desiredCapabilties = {
        "app": "bs://random_url",
        "browserstack.use_w3c": true,
        "browserstack.debug": true
      }

      let caps = {
        "desiredCapabilities": desiredCapabilties
      }

      assert.strictEqual(appAutomateW3CHelper.doDesiredCapsHasBrowserStackCapabilities(caps), true);
      done();
    });
  });

  context('getFirstMatchAndAlwaysMatch', () => {
    it('should return alwaysMatch and firstMatch as it is if present in capabilities', (done) => {
      let alwaysMatch = {
        "platformName": "android",
        "deviceName": "Google Pixel",
        "appium:app": "bs://some_random_string"
      };
  
      let firstMatch = [
        {
          "platformVersion": "7.1"
        },
        {
          "platformVersion": "8.0"
        }
      ];
  
      let caps = {
        "capabilities": {
          "alwaysMatch": alwaysMatch,
          "firstMatch": firstMatch
        }
      }
      const response = appAutomateW3CHelper.getFirstMatchAndAlwaysMatch(caps);
      assert.deepEqual(response.firstMatch, firstMatch);
      assert.deepEqual(response.alwaysMatch, alwaysMatch);
      done();
    });
    it('should return empty hash if alwaysMatch is absent', (done) => {
      let firstMatch = [
        {
          "platformVersion": "7.1"
        },
        {
          "platformVersion": "8.0"
        }
      ];
  
      let caps = {
        "capabilities": {
          "firstMatch": firstMatch
        }
      }
      const response = appAutomateW3CHelper.getFirstMatchAndAlwaysMatch(caps);
      assert.deepEqual(response.firstMatch, firstMatch);
      assert.deepEqual(response.alwaysMatch, {});
      done();
    });
  
    it('should return empty array if firstMatch is absent', (done) => {
      let alwaysMatch = {
        "platformName": "android",
        "deviceName": "Google Pixel",
        "appium:app": "bs://some_random_string"
      };
  
      let caps = {
        "capabilities": {
          "alwaysMatch": alwaysMatch
        }
      }
      const response = appAutomateW3CHelper.getFirstMatchAndAlwaysMatch(caps);
      assert.deepEqual(response.firstMatch, []);
      assert.deepEqual(response.alwaysMatch, alwaysMatch);
      done();
    });
    it('should return alwaysMatch and firstMatch as it is if present in capabilities', (done) => {
      let alwaysMatch = {
        "platformName": "android",
        "deviceName": "Google Pixel",
        "appium:app": "bs://some_random_string"
      };

      let firstMatch = [
        {
          "platformVersion": "7.1"
        },
        {
          "platformVersion": "8.0"
        }
      ];

      let caps = {
        "capabilities": {
          "alwaysMatch": alwaysMatch,
          "firstMatch": firstMatch
        }
      }
      const response = appAutomateW3CHelper.getFirstMatchAndAlwaysMatch(caps);
      assert.deepEqual(response.firstMatch, firstMatch);
      assert.deepEqual(response.alwaysMatch, alwaysMatch);
      done();
    });


    it('should return empty hash if alwaysMatch is absent', (done) => {
      let firstMatch = [
        {
          "platformVersion": "7.1"
        },
        {
          "platformVersion": "8.0"
        }
      ];

      let caps = {
        "capabilities": {
          "firstMatch": firstMatch
        }
      }
      const response = appAutomateW3CHelper.getFirstMatchAndAlwaysMatch(caps);
      assert.deepEqual(response.firstMatch, firstMatch);
      assert.deepEqual(response.alwaysMatch, {});
      done();
    });

    it('should return empty array if firstMatch is absent', (done) => {
      let alwaysMatch = {
        "platformName": "android",
        "deviceName": "Google Pixel",
        "appium:app": "bs://some_random_string"
      };

      let caps = {
        "capabilities": {
          "alwaysMatch": alwaysMatch
        }
      }
      const response = appAutomateW3CHelper.getFirstMatchAndAlwaysMatch(caps);
      assert.deepEqual(response.firstMatch, []);
      assert.deepEqual(response.alwaysMatch, alwaysMatch);
      done();
    });
  });

  context('doW3CHasCapability', () => {
    it('should return true if alwaysMatch in w3c caps has capability', (done) => {
      let alwaysMatch = {
        "platformName": "android",
        "deviceName": "Google Pixel",
        "appium:app": "bs://some_random_string"
      };

      let firstMatch = [
        {
          "platformVersion": "7.1"
        },
        {
          "platformVersion": "8.0"
        }
      ];

      let caps = {
        "capabilities": {
          "alwaysMatch": alwaysMatch,
          "firstMatch": firstMatch
        }
      }

      assert.equal(appAutomateW3CHelper.doW3CHasCapability(caps, "appium:app"), true);
      done();
    });

    it('should return true if first object of firstMatch in w3c caps has capability', (done) => {
      let alwaysMatch = {
        "platformName": "android",
        "deviceName": "Google Pixel"
      };

      let firstMatch = [
        {
          "platformVersion": "7.1",
          "appium:app": "bs://some_random_string"
        },
        {
          "platformVersion": "8.0"
        }
      ];

      let caps = {
        "capabilities": {
          "alwaysMatch": alwaysMatch,
          "firstMatch": firstMatch
        }
      }

      assert.equal(appAutomateW3CHelper.doW3CHasCapability(caps, "appium:app"), true);
      done();
    });

    it('should return false if capability is not present in firstMatch and alwaysMatch', (done) => {
      let alwaysMatch = {
        "platformName": "android",
        "deviceName": "Google Pixel"
      };

      let firstMatch = [
        {
          "platformVersion": "7.1"
        },
        {
          "platformVersion": "8.0",
          "appium:app": "bs://some_random_string"
        }
      ];

      let caps = {
        "capabilities": {
          "alwaysMatch": alwaysMatch,
          "firstMatch": firstMatch
        }
      }

      assert.equal(appAutomateW3CHelper.doW3CHasCapability(caps, "appium:app"), false);
      done();
    });
  });

  context('doW3CHasNestedCapability', () => {
    it('should return true if alwaysMatch in w3c caps has nested capability app', (done) => {
      let alwaysMatch = {
        "platformName": "android",
        "deviceName": "Google Pixel",
        "appium:options": {
          "app": "bs://some_random_string"
        }
      };

      let firstMatch = [
        {
          "platformVersion": "7.1"
        },
        {
          "platformVersion": "8.0"
        }
      ];

      let caps = {
        "capabilities": {
          "alwaysMatch": alwaysMatch,
          "firstMatch": firstMatch
        }
      }

      assert.equal(appAutomateW3CHelper.doW3CHasNestedCapability(caps, "appium:options", "app"), true);
      done();
    });

    it('should return true if alwaysMatch in w3c caps has nested capability appium:app', (done) => {
      let alwaysMatch = {
        "platformName": "android",
        "deviceName": "Google Pixel",
        "appium:options": {
          "appium:app": "bs://some_random_string"
        }
      };

      let firstMatch = [
        {
          "platformVersion": "7.1"
        },
        {
          "platformVersion": "8.0"
        }
      ];

      let caps = {
        "capabilities": {
          "alwaysMatch": alwaysMatch,
          "firstMatch": firstMatch
        }
      }

      assert.equal(appAutomateW3CHelper.doW3CHasNestedCapability(caps, "appium:options", "appium:app"), true);
      done();
    });

    it('should return true if first object of firstMatch in w3c caps has nested capability', (done) => {
      let alwaysMatch = {
        "platformName": "android",
        "deviceName": "Google Pixel"
      };

      let firstMatch = [
        {
          "platformVersion": "7.1",
          "appium:options": {
            "appium:app": "bs://some_random_string"
          }
        },
        {
          "platformVersion": "8.0"
        }
      ];

      let caps = {
        "capabilities": {
          "alwaysMatch": alwaysMatch,
          "firstMatch": firstMatch
        }
      }

      assert.equal(appAutomateW3CHelper.doW3CHasNestedCapability(caps, "appium:options", "appium:app"), true);
      done();
    });

    it('should return false if parent capability is not present in firstMatch and alwaysMatch', (done) => {
      let alwaysMatch = {
        "platformName": "android",
        "deviceName": "Google Pixel"
      };

      let firstMatch = [
        {
          "platformVersion": "7.1"
        },
        {
          "platformVersion": "8.0",
          "appium:app": "bs://some_random_string"
        }
      ];

      let caps = {
        "capabilities": {
          "alwaysMatch": alwaysMatch,
          "firstMatch": firstMatch
        }
      }

      assert.equal(appAutomateW3CHelper.doW3CHasNestedCapability(caps, "appium:options", "appium:app"), undefined);
      done();
    });

    it('should return false if nested capability is not present in firstMatch and alwaysMatch', (done) => {
      let alwaysMatch = {
        "platformName": "android",
        "deviceName": "Google Pixel"
      };

      let firstMatch = [
        {
          "platformVersion": "7.1"
        },
        {
          "platformVersion": "8.0",
          "appium:options": "bs://some_random_string"
        }
      ];

      let caps = {
        "capabilities": {
          "alwaysMatch": alwaysMatch,
          "firstMatch": firstMatch
        }
      }

      assert.equal(appAutomateW3CHelper.doW3CHasNestedCapability(caps, "appium:options", "appium:app"), undefined);
      done();
    });
  });

  context('#mapW3CErrorToJSONWPStatus function', () => {
    it('should return appropriate statusCode if error is present in mapping', () => {
      appAutomateW3CHelper.mapW3CErrorToJSONWPStatus('NoSuchElementError: An element could not be located on the page using the given search parameters').should.equal(7);
    });
    it('should return zero as statusCode if error is not present in mapping', () => {
      appAutomateW3CHelper.mapW3CErrorToJSONWPStatus('NoSuchRandomError: An element could not be located on the page using the given search parameters').should.equal(0);
    });
    it('should return zero as statusCode if error is empty', () => {
      appAutomateW3CHelper.mapW3CErrorToJSONWPStatus('').should.equal(0);
    });
    it('should return zero as statusCode if error is undefined ', () => {
      appAutomateW3CHelper.mapW3CErrorToJSONWPStatus(undefined).should.equal(0);
    });
  });
});
