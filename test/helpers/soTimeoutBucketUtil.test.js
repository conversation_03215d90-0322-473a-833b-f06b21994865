'use strict';

const soTimeoutBucketer = require('../../helpers/soTimeoutBucketUtil');
const constants = require('../../constants');

describe('getSoTimeoutBucket function', () => {
  it('should return bucket for user staging website', () => {
    const sessionKeyObj = {
      lastRequestDetails: 'POST:url::POST:maximize',
      lastOpenedUrl: 'https://www-regression.random.io/',
    };
    const soTimeoutBucket = soTimeoutBucketer.getSoTimeoutBucket(sessionKeyObj);
    soTimeoutBucket.should.eql([constants.SO_TIMEOUT_BUCKET.stagingWebsite]);
  });

  it('should return bucket for user delete window', () => {
    const sessionKeyObj = {
      lastRequestDetails: 'DELETE:window::POST:url',
      lastOpenedUrl: 'https://www-regression.random.io/',
    };
    const soTimeoutBucket = soTimeoutBucketer.getSoTimeoutBucket(sessionKeyObj);
    soTimeoutBucket.should.eql([constants.SO_TIMEOUT_BUCKET.deleteWindow, constants.SO_TIMEOUT_BUCKET.nonNetworkCommand]);
  });

  it('should return bucket for user basic auth', () => {
    const sessionKeyObj = {
      lastRequestDetails: 'POST:url::POST:value',
      lastOpenedUrl: 'https://admin:<EMAIL>/',
      browser: 'chrome',
    };
    const soTimeoutBucket = soTimeoutBucketer.getSoTimeoutBucket(sessionKeyObj);
    soTimeoutBucket.should.eql([constants.SO_TIMEOUT_BUCKET.basicAuth]);
  });

  it('should return bucket for user not network command', () => {
    const sessionKeyObj = {
      lastRequestDetails: 'GET:cookie::POST:cookie',
      lastOpenedUrl: 'https://admin:<EMAIL>/',
    };
    const soTimeoutBucket = soTimeoutBucketer.getSoTimeoutBucket(sessionKeyObj);
    soTimeoutBucket.should.eql([constants.SO_TIMEOUT_BUCKET.nonNetworkCommand]);
  });

  it('should return bucket for user basic auth and staging website', () => {
    const sessionKeyObj = {
      lastRequestDetails: 'POST:url::POST:value',
      lastOpenedUrl: 'https://admin:<EMAIL>/',
      browser: 'chrome',
    };
    const soTimeoutBucket = soTimeoutBucketer.getSoTimeoutBucket(sessionKeyObj);
    soTimeoutBucket.should.eql([constants.SO_TIMEOUT_BUCKET.basicAuth, constants.SO_TIMEOUT_BUCKET.stagingWebsite]);
  });

  it('should return bucket for user basic auth safari and staging website', () => {
    const sessionKeyObj = {
      lastRequestDetails: 'POST:url::POST:value',
      lastOpenedUrl: 'https://admin:<EMAIL>/',
      browser: 'safari',
    };
    const soTimeoutBucket = soTimeoutBucketer.getSoTimeoutBucket(sessionKeyObj);
    soTimeoutBucket.should.eql([constants.SO_TIMEOUT_BUCKET.basicAuthSafari, constants.SO_TIMEOUT_BUCKET.stagingWebsite]);
  });
});
