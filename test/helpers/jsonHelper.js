'use strict';

const assert = require('assert');
const sinon = require('sinon');
const { expect } = require('chai');

const jsonHelper = require('../../helpers/jsonHelper');

describe('#splitJSON function', () => {
  const json = {
    'POST:element': {
      d: ['2417', '3129', '1000', '2000', '3000', '1500'],
      s: ['200', '400', '201', '499', '422', '13'],
      m: ['xpath', 'id', 'accessibility id', 'image', 'css', 'div'],
    },
    'POST:reset': {
      d: ['5000', '6000', '7000', '8000', '9000'],
      s: ['302', '500', '100', '502', '504'],
      m: ['', '', '', '', ''],
    },
    'GET:url': {
      d: ['2500', '3500'],
      s: ['204', '301'],
      m: ['', ''],
    },
  };

  describe('splitSingleElementJSON', () => {
    it('should split a JSON object into smaller parts', () => {
      const json_res = {
        'POST:element': {
          d: ['2417', '3129', '1000', '2000', '3000'],
          s: ['200', '400', '201', '499', '422'],
        },
      };
      const results = [];
      const limit = 10;
      jsonHelper.splitSingleElementJSON(json_res, results, limit, false);
      expect(results).to.be.an('array');
      expect(results.length).to.equal(5);
    });

    it('should split a JSON object into smaller parts when isAppAutomate is false', () => {
      const json_res = {
        'POST:element': {
          d: ['2417', '3129', '1000', '2000', '3000'],
          s: ['200', '400', '201', '499', '422'],
        },
      };
      const results = [];
      const limit = 10;
      jsonHelper.splitSingleElementJSON(json_res, results, limit, false);
      expect(results).to.be.an('array');
      expect(results.length).to.equal(5);
    });

    it('should split a JSON object with "m" property into smaller parts when isAppAutomate is true', () => {
      const json_res = {
        'POST:element': {
          d: ['2417', '3129', '1000', '2000', '3000'],
          s: ['200', '400', '201', '499', '422'],
          m: ['200', '400', '201', '499', '422'],
        },
      };
      const results = [];
      const limit = 10;
      jsonHelper.splitSingleElementJSON(json_res, results, limit, true);
      expect(results).to.be.an('array');
      expect(results.length).to.equal(5);
    });
    it('should split a JSON object into multiple parts when exceeding the limit', () => {
      const json_res = {
        'POST:element': {
          d: ['2417', '3129', '1000', '2000', '3000', '2417', '3129', '1000'],
          s: ['200', '400', '201', '499', '422', '200', '400', '201'],
        },
      };
      const results = [];
      const limit = 40;
      jsonHelper.splitSingleElementJSON(json_res, results, limit, false);
      expect(results).to.be.an('array');
      expect(results.length).to.equal(4);
    });
  });
  it('should return same json back if limit is not reached', () => {
    const splitSingleElementJSON = sinon.stub(jsonHelper, 'splitSingleElementJSON');

    jsonHelper.splitJSON(json, 7500)[0].should.equal(json);
    assert(splitSingleElementJSON.calledOnce === false);

    jsonHelper.splitSingleElementJSON.restore();
  });

  it('should return 2 jsons back without calling splitSingleElementJSON', () => {
    const splitSingleElementJSON = sinon.stub(jsonHelper, 'splitSingleElementJSON');

    jsonHelper.splitJSON(json, 300).length.should.equal(Math.ceil(JSON.stringify(json).length / 300));
    assert(splitSingleElementJSON.calledOnce === false);

    jsonHelper.splitSingleElementJSON.restore();
  });

  it('should return mutiple jsons back with splitSingleElementJSON if limit is less', () => {
    assert(jsonHelper.splitJSON(json, 70).length >= (Math.ceil(JSON.stringify(json).length / 70)));
  });

  it('should return mutiple jsons back with splitSingleElementJSON if limit is less and json has only single element', () => {
    const clonedJson = Object.assign({}, json);
    delete clonedJson['POST:reset'];
    delete clonedJson['GET:url'];

    assert(jsonHelper.splitJSON(clonedJson, 70).length >= (Math.ceil(JSON.stringify(clonedJson).length / 70)));
  });
});
