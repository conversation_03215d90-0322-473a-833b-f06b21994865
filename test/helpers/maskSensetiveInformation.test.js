'use strict';

const assert = require('assert');

const maskSensitiveInformation = require('../../helpers/maskSensitiveInformation');


describe('maskSensitiveInformation', () => {
  it('should redact response when a url matching redactEndpoint is passed', (done) => {
    let request = {
      url: '/wd/hub/session/abcde12345/element/abc123/value',
      log_data: '{"url":"https://username=automationbs:password=<EMAIL>/automate/capabilities"}'
    }
    let keyObject = {
      maskBasicAuth: 'true',
      maskSendKeys: true,
      maskResponse: true,
    }
    let response = {
      string: '{"state":"success","sessionId":"abcde12345","hCode":17957502,"value":"som3Valu3","class":"org.openqa.selenium.remote.Response","status":0}'
    }
    maskSensitiveInformation(request, keyObject, response);
    assert.strictEqual(request.log_data,'{"value":["[REDACTED]"]}')
    assert.strictEqual(response.string,'{"state":"success","sessionId":"abcde12345","hCode":17957502,"value":"[REDACTED]","class":"org.openqa.selenium.remote.Response","status":0}')
    done();
  });

  it('should not mask getText Request', () => {
    let request = {
      url: '/wd/hub/session/abcde12345/element/abc123/text',
      log_data: ''
    }
    let keyObject = {
      maskBasicAuth: 'true',
      maskSendKeys: true,
    }
    let response = {
      string: '{"state":"success","sessionId":"abcde12345","hCode":17957502,"value":"som3Valu3","class":"org.openqa.selenium.remote.Response","status":0}'
    }

    maskSensitiveInformation(request, keyObject, response);
    assert.notStrictEqual(request.log_data,'{"value":["[REDACTED]"]}')
  });

  it('should mask getText Response', () => {
    let request = {
      url: '/wd/hub/session/abcde12345/element/abc123/text',
      log_data: ''
    }
    let keyObject = {
      maskBasicAuth: 'true',
      maskSendKeys: true,
      maskResponse: true,
    }
    let response = {
      string: '{"state":"success","sessionId":"abcde12345","hCode":17957502,"value":"som3Valu3","class":"org.openqa.selenium.remote.Response","status":0}'
    }

    maskSensitiveInformation(request, keyObject, response);
    assert.strictEqual(response.string,'{"state":"success","sessionId":"abcde12345","hCode":17957502,"value":"[REDACTED]","class":"org.openqa.selenium.remote.Response","status":0}')
  });

  it('should redact auth when a url matching /url/ is passed', (done) => {
    let request = {
      url: '/wd/hub/session/abcde12345/url',
      log_data: '{"url":"https://username=automationbs:password=<EMAIL>/automate/capabilities"}'
    }
    let keyObject = {
      maskBasicAuth: 'true',
      maskSendKeys: true,
      maskResponse: true,
    }
    let response = {
      string: '{"state":"success","sessionId":"abcde12345","hCode":17957502,"value":"som3Valu3","class":"org.openqa.selenium.remote.Response","status":0}'
    }

    maskSensitiveInformation(request, keyObject, response);
    assert.strictEqual(request.log_data,'{"url":"https://[REDACTED]@browserstack.com/automate/capabilities"}')

    done();
  });
});
