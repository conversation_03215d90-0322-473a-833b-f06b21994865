"use strict";

const rewire = require("rewire");
const cdpRedactorRewire = rewire("../../../helpers/redactor/cdpRedactor");
const {
  REDACTED_VALUE,
} = require("../../../helpers/redactor/cdpRedactorMaskConstants");
const { PLAYWRIGHT } = require("../../../config/socketConstants");
const { expect, assert } = require("chai");
const sinon = require("sinon");

describe("CDPRedactor methods", () => {
  context("setIfNested", () => {
    it("it should replace if nested key is valid", () => {
      const setIfNested = cdpRedactorRewire.__get__("setIfNested");
      let data = { a: "a", b: { b1: "b1", b2: "b2" }, c: "c" };
      let expectedData = { a: "a", b: { b1: "REDACT", b2: "b2" }, c: "c" };
      const value = "REDACT";
      const keyList = ["b", "b1"];
      setIfNested(data, keyList, value);
      expect(data).to.deep.equal(expectedData);
    });

    it("it shouldn't change anything if nested key is invalid", () => {
      const setIfNested = cdpRedactorRewire.__get__("setIfNested");
      let data = { a: "a", b: { b1: "b1", b2: "b2" }, c: "c" };
      let expectedData = { a: "a", b: { b1: "b1", b2: "b2" }, c: "c" };
      const value = "REDACT";
      const keyList = ["c", "b1"];
      setIfNested(data, keyList, value);
      expect(data).to.deep.equal(expectedData);
    });
  });

  context("jsonRedactor", () => {
    let setIfNestedMethod = cdpRedactorRewire.__get__("setIfNested");;

    afterEach(() => {
      cdpRedactorRewire.__set__("setIfNested", setIfNestedMethod);
    });
    it("should call setIfNested if data and maskableFieldList is present", () => {
      const jsonRedactor = cdpRedactorRewire.__get__("jsonRedactor");
      let data = {
        a: "a",
        b: { b1: "b1", b2: { b21: "b21", b22: "b22" }, b3: "b3" },
        c: "c",
      };
      const keyList = [
        ["b", "b3"],
        ["b", "b2", "b21"],
        ["b", "b3", "c"],
        ["b4", "b2", "b21"],
      ];
      const setIfNestedObject = {
        setIfNested: cdpRedactorRewire.__get__("setIfNested"),
      };
      const setIfNestedSpy = sinon.spy(setIfNestedObject, "setIfNested");

      cdpRedactorRewire.__set__("setIfNested", setIfNestedSpy);
      jsonRedactor(data, keyList);
      expect(setIfNestedSpy.called).to.be.true;

      setIfNestedSpy.restore();
    });

    it("should call setIfNested if data is null", () => {
      const jsonRedactor = cdpRedactorRewire.__get__("jsonRedactor");
      let data = null;
      const keyList = [["b", "b3"]];
      const setIfNestedObject = {
        setIfNested: cdpRedactorRewire.__get__("setIfNested"),
      };
      const setIfNestedSpy = sinon.spy(setIfNestedObject, "setIfNested");

      cdpRedactorRewire.__set__("setIfNested", setIfNestedSpy);
      jsonRedactor(data, keyList);
      expect(setIfNestedSpy.notCalled).to.be.true;

      setIfNestedSpy.restore();
    });

    it("should call setIfNested if maskableFieldsList is empty", () => {
      const jsonRedactor = cdpRedactorRewire.__get__("jsonRedactor");
      let data = {};
      const keyList = [];
      const setIfNestedObject = {
        setIfNested: cdpRedactorRewire.__get__("setIfNested"),
      };
      const setIfNestedSpy = sinon.spy(setIfNestedObject, "setIfNested");

      cdpRedactorRewire.__set__("setIfNested", setIfNestedSpy);
      jsonRedactor(data, keyList);
      expect(setIfNestedSpy.notCalled).to.be.true;

      setIfNestedSpy.restore();
    });
  });

  context("redact", () => {
    const data = {};
    const maskCmds = ["sendtype"];
    let playwrightRedactorMethod = cdpRedactorRewire.__get__("playwrightRedactor");;

    afterEach(() => {
      cdpRedactorRewire.__set__("playwrightRedactor", playwrightRedactorMethod);
    });

    it("should select playwrightRedactor for framework playwright", () => {
      const framework = PLAYWRIGHT;
      const cdpRedactorObject = {
        playwrightRedactor: cdpRedactorRewire.__get__("playwrightRedactor"),
      };

      const playwrightRedactorSpy = sinon
        .spy(cdpRedactorObject, "playwrightRedactor");

      cdpRedactorRewire.__set__("playwrightRedactor", playwrightRedactorSpy);
      cdpRedactorRewire.redact(data, maskCmds, framework);
      expect(playwrightRedactorSpy.calledOnce).to.be.true;

      playwrightRedactorSpy.restore();
    });

    it("should not select playwrightRedactor for framework null", () => {
      const framework = "abcd";
      const cdpRedactorObject = {
        playwrightRedactor: cdpRedactorRewire.__get__("playwrightRedactor"),
      };

      const playwrightRedactorSpy = sinon
        .spy(cdpRedactorObject, "playwrightRedactor");

      cdpRedactorRewire.__set__("playwrightRedactor", playwrightRedactorSpy);
      cdpRedactorRewire.redact(data, maskCmds, framework);
      expect(playwrightRedactorSpy.notCalled).to.be.true;

      playwrightRedactorSpy.restore();
    });
  });
});

describe("Playwright redactor functionality", () => {
  let playwrightRedactor = cdpRedactorRewire.__get__("playwrightRedactor");
  let data = {
    id: 8,
    guid: "abcd",
    params: {},
  };

  context("should redact for single methods", () => {

    it("redacts for sendtype", () => {
      data["params"]["text"] = "Browserstack";
      data["params"]["value"] = "Browserstack";
      playwrightRedactor(data, ["sendtype"]);
      expect(data["params"]["text"]).to.be.equal(REDACTED_VALUE);
      expect(data["params"]["value"]).to.be.equal(REDACTED_VALUE);
    });

    it("redacts for sendpress", () => {
      data["params"]["key"] = "Enter";
      playwrightRedactor(data, ["sendpress"]);
      expect(data["params"]["key"]).to.be.equal(REDACTED_VALUE);
    });

    it("redacts for sethttpcredentials", () => {
      data["params"]["httpCredentials"] = { admin: "admin", password: "pass" };
      playwrightRedactor(data, ["sethttpcredentials"]);
      expect(data["params"]["httpCredentials"]).to.be.equal(REDACTED_VALUE);
    });

    it("redacts for setstoragestate", () => {
      data["params"]["storageState"] = "lots of cookies";
      playwrightRedactor(data, ["setstoragestate"]);
      expect(data["params"]["storageState"]).to.be.equal(REDACTED_VALUE);
    });

    it("redacts for setgeolocation", () => {
      data["params"]["geolocation"] = "coordinates";
      playwrightRedactor(data, ["setgeolocation"]);
      expect(data["params"]["geolocation"]).to.be.equal(REDACTED_VALUE);
    });

    it("redacts for setwebauthncredentials", () => {
      data["params"]["params"] = {};
      data["params"]["params"]["credential"] = {};
      data["params"]["params"]["properties"] = {};
      data["params"]["params"]["authenticatorId"] = "BrowserstackAuthenticator";
      data["params"]["params"]["credentialId"] = "BrowserstackCredentialID";
      data["params"]["params"]["credential"]["credentialId"] = "BrowserstackCredential";
      data["params"]["params"]["credential"]["privateKey"] = "BrowserstackPrivateKey";
      data["params"]["params"]["properties"]["privateKey"] = "BrowserstackPrivateKey";
      playwrightRedactor(data, ["setwebauthncredentials"]);
      expect(data["params"]["params"]["authenticatorId"]).to.be.equal(REDACTED_VALUE);
      expect(data["params"]["params"]["credentialId"]).to.be.equal(REDACTED_VALUE);
      expect(data["params"]["params"]["credential"]["credentialId"]).to.be.equal(REDACTED_VALUE);
      expect(data["params"]["params"]["credential"]["privateKey"]).to.be.equal(REDACTED_VALUE);
      expect(data["params"]["params"]["properties"]["privateKey"]).to.be.equal(REDACTED_VALUE);
    });

  });

  context("should selectively redact for multiple methods", () => {
    beforeEach(() => {
      data["params"] = {
        value: "What is",
        text: "Browserstack",
        key: "Enter",
        httpCredentials: { admin: "admin", password: "pass" },
        storageState: "lots of cookies",
        geolocation: ["some", "location"],
      };
    });

    it("redacts for sendtype and sendpress but not for others", () => {
      playwrightRedactor(data, ["sendtype", "sendpress"]);
      expect(data["params"]["value"]).to.be.equal(REDACTED_VALUE);
      expect(data["params"]["text"]).to.be.equal(REDACTED_VALUE);
      expect(data["params"]["key"]).to.be.equal(REDACTED_VALUE);
      expect(data["params"]["httpCredentials"]).to.deep.equal({
        admin: "admin",
        password: "pass",
      });
      expect(data["params"]["storageState"]).to.be.equal("lots of cookies");
      expect(data["params"]["geolocation"]).to.deep.equal(["some", "location"]);
    });

    it("redacts for sethttpcredentials, setstoragestate, setgeolocation and sendpress but not for others", () => {
      playwrightRedactor(data, [
        "sethttpcredentials",
        "setstoragestate",
        "setgeolocation",
      ]);
      expect(data["params"]["text"]).to.be.equal("Browserstack");
      expect(data["params"]["value"]).to.be.equal("What is");
      expect(data["params"]["key"]).to.be.equal("Enter");
      expect(data["params"]["httpCredentials"]).to.be.equal(REDACTED_VALUE);
      expect(data["params"]["storageState"]).to.be.equal(REDACTED_VALUE);
      expect(data["params"]["geolocation"]).to.be.equal(REDACTED_VALUE);
    });

    it("redacts for all methods", () => {
      playwrightRedactor(data, [
        "sendtype",
        "sendpress",
        "sethttpcredentials",
        "setstoragestate",
        "setgeolocation",
      ]);
      expect(data["params"]["value"]).to.be.equal(REDACTED_VALUE);
      expect(data["params"]["text"]).to.be.equal(REDACTED_VALUE);
      expect(data["params"]["key"]).to.be.equal(REDACTED_VALUE);
      expect(data["params"]["httpCredentials"]).to.be.equal(REDACTED_VALUE);
      expect(data["params"]["storageState"]).to.be.equal(REDACTED_VALUE);
      expect(data["params"]["geolocation"]).to.be.equal(REDACTED_VALUE);
    });
  });
});
