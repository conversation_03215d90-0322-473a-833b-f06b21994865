{"normalCapabilities": {"actual": {"os": "OS X", "os_version": "Mojave", "browserName": "Chrome", "browser_version": "75.0", "browserstack.local": "false"}, "expected": {"os": "OS X", "os_version": "Mojave", "browserName": "Chrome", "browser_version": "75.0", "browserstack.local": "false"}}, "PIICapabilities": {"actual": {"os": "OS X", "os_version": "Mojave", "browserName": "Chrome", "browser_version": "75.0", "browserstack.local": "false", "browserstack.user": "username123", "browserstack.key": "abcdefghij1234567890", "user": "username123", "url": "browserstack.com", "browserstack.tunnelIdentifier": "myTunnelIdentifier", "browserstack.localIdentifier": "myLocalIdentifier"}, "expected": {"os": "OS X", "os_version": "Mojave", "browserName": "Chrome", "browser_version": "75.0", "browserstack.local": "false", "browserstack.user": "[REDACTED]", "browserstack.key": "[REDACTED]", "user": "[REDACTED]", "url": "[REDACTED]", "browserstack.tunnelIdentifier": "[REDACTED]", "browserstack.localIdentifier": "[REDACTED]"}}, "BrowserOptionsCapabilities": {"actual": {"os": "OS X", "os_version": "Mojave", "browserName": "Chrome", "browser_version": "75.0", "browserstack.local": "true", "chromeOptions": "random ChromeOptions", "goog:chromeOptions": "random goog:chromeOptions", "firefoxProfile": "random firefoxProfile", "moz:firefoxOptions": "random moz:firefoxOptions", "EdgeOptions": "random moz:firefoxOptions"}, "expected": {"os": "OS X", "os_version": "Mojave", "browserName": "Chrome", "browser_version": "75.0", "browserstack.local": "true", "chromeOptions": "[REDACTED]", "goog:chromeOptions": "[REDACTED]", "firefoxProfile": "[REDACTED]", "moz:firefoxOptions": "[REDACTED]", "EdgeOptions": "[REDACTED]"}}, "W3CCapabilities": {"actual": {"firstMatch": [{"appium:os_version": "6.0", "appium:locale": "Fr_CA", "appium:build": "mobile appium test", "appium:browserstack.user": "username123", "appium:name": "test1234", "appium:browserstack.localIdentifier": "myLocalIdentifier", "appium:user": "username123", "appium:project": "mobile appium test", "bstack:options": {"key": "abcd"}, "appium:device": "Google Nexus 6", "appium:app": "bs://5ec80b43f3ac04044a5d100e446e13d43f4b1e4f", "appium:browserstack.networkLogs": "true", "appium:browserstack.appProfiling": "true", "appium:browserstack.key": "abcdefghij1234567890", "browserstack.key": "abcdefghij1234567890", "appium:browserstack.tunnelIdentifier": "myTunnelIdentifier"}], "alwaysMatch": {"appium:url": "browserstack.com", "url": "zyz.com", "bstack:options": {"user": "username"}}, "bstack:options": {"userName": "userName", "accessKey": "accessKey", "sessionName": "<PERSON><PERSON><PERSON>"}}, "expected": {"firstMatch": [{"appium:os_version": "6.0", "appium:locale": "Fr_CA", "appium:build": "mobile appium test", "appium:browserstack.user": "[REDACTED]", "appium:name": "test1234", "appium:browserstack.localIdentifier": "[REDACTED]", "appium:user": "[REDACTED]", "appium:project": "mobile appium test", "bstack:options": {"key": "[REDACTED]"}, "appium:device": "Google Nexus 6", "appium:app": "bs://5ec80b43f3ac04044a5d100e446e13d43f4b1e4f", "appium:browserstack.networkLogs": "true", "appium:browserstack.appProfiling": "true", "appium:browserstack.key": "[REDACTED]", "browserstack.key": "[REDACTED]", "appium:browserstack.tunnelIdentifier": "[REDACTED]"}], "alwaysMatch": {"appium:url": "[REDACTED]", "url": "[REDACTED]", "bstack:options": {"user": "[REDACTED]"}}, "bstack:options": {"userName": "[REDACTED]", "accessKey": "[REDACTED]", "sessionName": "<PERSON><PERSON><PERSON>"}}}}