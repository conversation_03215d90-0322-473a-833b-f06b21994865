const redactor = require('../../../helpers/redactor/index');
const { expect } = require('chai');
const constants = require('../../../constants');

describe('redactCustomExecutorData function', () => {
  it('redacts username and password for sendBasicAuth', () => {
    let customExecutorData = {
      'action': 'sendBasicAuth',
      'arguments': {
        'username': 'testuser',
        'password': 'testpassword',
      }
    }
    let redactedCustomExecutorData = redactor.redactCustomExecutorData('', customExecutorData);

    let expectedCustomExecutorData = customExecutorData;
    expectedCustomExecutorData.arguments.username = '[REDACTED]';
    expectedCustomExecutorData.arguments.password = '[REDACTED]';
    expectedCustomExecutorData = JSON.stringify(expectedCustomExecutorData);
    redactedCustomExecutorData.should.equal(expectedCustomExecutorData);
  });
});

describe('redact function', () => {

  it('redacts fields from get session command response', () => {
    let request = {
      url: '/wd/hub/session/xxxxxxxxxxxxxx12345',
      method: 'GET',
    };
    let response = {
      string: JSON.stringify({
        value: {
          key1: 'value1',
          key2: 'value2',
          keychainPassword: 'keychainPassword',
          keychainPath: 'keychainPath',
        },
      }),
    };
    let maskCommands = ['ios_getsession'];

    let redactedSessionResponse = redactor.redact(request, response, maskCommands, {});

    let expectedResponse = JSON.parse(response.string);
    expectedResponse.value.key1 = 'value1';
    expectedResponse.value.key2 = 'value2';
    expectedResponse.value.keychainPassword = '[REDACTED VALUE]';
    expectedResponse.value.keychainPath = '[REDACTED VALUE]';
    expectedResponse = JSON.stringify(expectedResponse);
    redactedSessionResponse.response.string.should.equal(expectedResponse);
  });

  it('redacts fields from get capabilites command response', () => {
    let request = {
      url: '/wd/hub/session/xxxxxxxxxxxxxx12345',
      method: 'GET',
    };
    let response = {
      string: JSON.stringify({
        value: {
          capabilities: {
            key1: 'value1',
            key2: 'value2',
            keychainPassword: 'keychainPassword',
            keychainPath: 'keychainPath',
          },
        },
      }),
    };
    let maskCommands = ['ios_getsession'];

    let redactedSessionResponse = redactor.redact(request, response, maskCommands, {});

    let expectedResponse = JSON.parse(response.string);
    expectedResponse.value.key1 = 'value1';
    expectedResponse.value.key2 = 'value2';
    expectedResponse.value.keychainPassword = '[REDACTED VALUE]';
    expectedResponse.value.keychainPath = '[REDACTED VALUE]';
    expectedResponse = JSON.stringify(expectedResponse);
    redactedSessionResponse.response.string.should.equal(expectedResponse);
  });

  it('do not redact fields from get session command response if mask command not present', () => {
    let request = {
      url: '/wd/hub/session/xxxxxxxxxxxxxx12345',
      method: 'GET',
    };
    let response = {
      string: JSON.stringify({
        value: {
          key1: 'value1',
          key2: 'value2',
          keychainPassword: 'keychainPassword',
          keychainPath: 'keychainPath',
        },
      }),
    };
    let maskCommands = ['getvalues'];

    let redactedSessionResponse = redactor.redact(request, response, maskCommands);

    let expectedResponse = JSON.parse(response.string);
    expectedResponse.value.key1 = 'value1';
    expectedResponse.value.key2 = 'value2';
    expectedResponse.value.keychainPassword = 'keychainPassword';
    expectedResponse.value.keychainPath = 'keychainPath';
    expectedResponse = JSON.stringify(expectedResponse);
    redactedSessionResponse.response.string.should.equal(expectedResponse);
  });

  it('do not redact fields from get session command response if url is invalid', () => {
    let request = {
      url: '/wd/hub/session/',
      method: 'GET',
    };
    let response = {
      string: JSON.stringify({
        value: {
          key1: 'value1',
          key2: 'value2',
          keychainPassword: 'keychainPassword',
          keychainPath: 'keychainPath',
        },
      }),
    };
    let maskCommands = ['ios_getsession'];

    let redactedSessionResponse = redactor.redact(request, response, maskCommands);

    let expectedResponse = JSON.parse(response.string);
    expectedResponse.value.key1 = 'value1';
    expectedResponse.value.key2 = 'value2';
    expectedResponse.value.keychainPassword = 'keychainPassword';
    expectedResponse.value.keychainPath = 'keychainPath';
    expectedResponse = JSON.stringify(expectedResponse);
    redactedSessionResponse.response.string.should.equal(expectedResponse);
  });

  it('do not redact fields from get session command response if METHOD is invalid', () => {
    let request = {
      url: '/wd/hub/session/xxxxxxxxxx12345',
      method: 'POST',
    };
    let response = {
      string: JSON.stringify({
        value: {
          key1: 'value1',
          key2: 'value2',
          keychainPassword: 'keychainPassword',
          keychainPath: 'keychainPath',
        },
      }),
    };
    let maskCommands = ['ios_getsession'];

    let redactedSessionResponse = redactor.redact(request, response, maskCommands);

    let expectedResponse = JSON.parse(response.string);
    expectedResponse.value.key1 = 'value1';
    expectedResponse.value.key2 = 'value2';
    expectedResponse.value.keychainPassword = 'keychainPassword';
    expectedResponse.value.keychainPath = 'keychainPath';
    expectedResponse = JSON.stringify(expectedResponse);
    redactedSessionResponse.response.string.should.equal(expectedResponse);
  });

});

describe('redactCookie', () => {
  it('should redact the "value" field from a cookie', () => {
    const cookie = {
      name: 'exampleCookie',
      value: 'sensitiveValue',
      domain: 'example.com',
    };
    const redactedCookie = redactor.redactCookie(cookie);
    expect(redactedCookie.value).to.equal('[REDACTED VALUE]');
  });
});

describe('redactScreenshot', () => {
  it('should redact the "value" field from a screenshot response', () => {
    let response = {
      string: JSON.stringify({
        value: "base64 image string",
      }),
    };
    let expected_response = {
      string: JSON.stringify({
        value: '[REDACTED VALUE]',
      }),
    };
    const redactedImageStringResponse = redactor.redactScreenshot(response);
    expect(redactedImageStringResponse.string).to.equal(expected_response.string);
  });
});
