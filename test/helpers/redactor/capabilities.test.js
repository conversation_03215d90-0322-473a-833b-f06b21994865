const constants = require('../../../constants')
const capabilitiesRedactor = require('../../../helpers/redactor/capabilities')
const data = require('./capabilities.data.json')

describe('redactCapabilities function', () => {
  it('redacts nothing for normal capabilities', () => {
    let redactedCapabilities = JSON.stringify(capabilitiesRedactor.redactCapabilities(data.normalCapabilities.actual, constants.PII_CAPABILITIES));
    let expectedCapabilities = JSON.stringify(data.normalCapabilities.expected);
    redactedCapabilities.should.equal(expectedCapabilities);
  });
  it('redacts PII from capabilities', () => {
    let redactedCapabilities = JSON.stringify(capabilitiesRedactor.redactCapabilities(data.PIICapabilities.actual, constants.PII_CAPABILITIES));
    let expectedCapabilities = JSON.stringify(data.PIICapabilities.expected);
    redactedCapabilities.should.equal(expectedCapabilities);
  });
  it('redacts BrowserOptions from capabilities', () => {
    let redactedCapabilities = JSON.stringify(capabilitiesRedactor.redactCapabilities(data.BrowserOptionsCapabilities.actual, constants.BROWSER_OPTIONS));
    let expectedCapabilities = JSON.stringify(data.BrowserOptionsCapabilities.expected);
    redactedCapabilities.should.equal(expectedCapabilities);
  });
});

describe('redactW3CCapabilities function', () => {
  it('redacts PII capabilities for W3C', () => {
    let redactedCapabilities = JSON.stringify(capabilitiesRedactor.redactW3CCapabilities(data.W3CCapabilities.actual, constants.PII_CAPABILITIES, constants.BSTACK_OPTIONS_PII_CAPABILITIES));
    let expectedCapabilities = JSON.stringify(data.W3CCapabilities.expected);
    redactedCapabilities.should.equal(expectedCapabilities);
  });
});
