const defender = require('../../railsRequests/railsDefender');
const redisClient = require('../../redisUtils').redisClient;
const assert = require('assert');
const sinon = require('sinon');
describe('railsDefender', () => {

  beforeEach(() => {
    setSpy = sinon.spy(redisClient, 'set');
  });

  afterEach(() => {
    setSpy.restore();
  });

  describe('defender methods testing', () => {
    it('check if redis call is made for binary disconnect', (done) => {
      defender.AddToCacheForBinaryDisconnected('randomusername', 'random-request-id').then(() => {
        assert(setSpy.calledOnce === true);
        done();
      });
    });
  });

  describe('defender methods testing for AA', () => {
    it('check if redis call is made for binary disconnect', (done) => {
      defender.AddToCacheForBinaryDisconnected('randomusername', 'random-request-id', true).then(() => {
        assert(setSpy.calledOnce === true);
        sinon.assert.calledWith(setSpy,'temp_blocked_user_binary_disconnected:randomusername__', true, 'ex', 60, sinon.match.func);
        done();
      });
    });

    it('check if redis call is made for expired testing time', (done) => {
      defender.AddToCacheForExpiredTestingTime('randomusername', 'random-request-id', true).then(() => {
        assert(setSpy.calledOnce === true);
        sinon.assert.calledWith(setSpy,'temp_blocked_user_expired_testing_time:randomusername__', true, 'ex', 60, sinon.match.func);
        done();
      });
    });

    it('check if redis call is made for expired plan', (done) => {
      defender.AddToCacheForExpiredPlan('randomusername', 'random-request-id', true).then(() => {
        assert(setSpy.calledOnce === true);
        sinon.assert.calledWith(setSpy,'temp_blocked_user_expired_plan:randomusername__', true, 'ex', 60, sinon.match.func);
        done();
      });
    });

    it('check if redis call is made for bad auth', (done) => {
      defender.AddToCacheForBadAuth('randomusername', 'pass', 'random-request-id', true).then(() => {
        assert(setSpy.calledOnce === true);
        sinon.assert.calledWith(setSpy,sinon.match.any, true, 'ex', 60, sinon.match.func);
        done();
      });
    });
    it('check if redis call is made for rails error', (done) => {
      data = {
        error: 'random_error',
        automate_error_data: {
          user_id: 'randon_user_id',
          error_message: 'randomerror_message',
          error_code: 'randomerror_code',
          error_code_str: 'randomerror_code_str',
          request_id: 'randomrequest_id',
          requestReceivedAt: 'randomrequest_received_at',
          raw_capabilities: {
            'browserstack.machine': null
          }
        }
      }
      defender.AddToCacheForRailsError('randomusername', 'pass', 'random-request-id', {}, data, true).then(() => {
        assert(setSpy.calledOnce === true);
        done();
      });
    });
  });
});
