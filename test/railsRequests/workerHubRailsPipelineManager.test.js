const { expect } = require('chai');
const sinon = require('sinon');
const { workerExitHardTimeout, workerExitPollInterval } = require('../../constants');
const rewire = require('rewire');

let workerHubRailsPipelineManager = require('../../railsRequests/workerRailsPipelineManager');
describe('workerHubRailsPipelineManager', function() {

  this.beforeEach(() => {
    workerHubRailsPipelineManager = rewire('../../railsRequests/workerRailsPipelineManager');
  });

  describe('addToWorkerHubRailsPipelineCounter', function() {
    it('should increment the counter value for the worker', function() {
      const instance = workerHubRailsPipelineManager;
      instance.addToWorkerHubRailsPipelineCounter();
      const counterValue = instance.getRequestCounterValue();

      expect(counterValue).to.equal(1);
    });
  });

  describe('removeFromWorkerHubRailsPipelineCounter', function() {
    it('should decrement the counter value for the worker', function() {
      const instance = workerHubRailsPipelineManager;
      instance.addToWorkerHubRailsPipelineCounter(); // Increment to 1
      instance.removeFromWorkerHubRailsPipelineCounter(); // Decrement to 0
      const counterValue = instance.getRequestCounterValue();
      expect(counterValue).to.equal(0);
    });

    it('should not allow negative counter values', function() {
      const instance = workerHubRailsPipelineManager;
      instance.addToWorkerHubRailsPipelineCounter(); // Increment to 1
      instance.removeFromWorkerHubRailsPipelineCounter(); // Decrement to 0
      instance.removeFromWorkerHubRailsPipelineCounter(); // Attempt to decrement below 0
      const counterValue = instance.getRequestCounterValue();

      expect(counterValue).to.equal(0);
    });
  });

  describe('startPollingAndExit', function() {
    it('should exit when counter becomes empty', function(done) {
      this.timeout(workerExitPollInterval * 5)
      const instance = workerHubRailsPipelineManager;
      const workerExitStub = sinon.stub(instance, 'workerExit');

      // Simulate counter becoming empty
      instance.addToWorkerHubRailsPipelineCounter();
      instance.startPollingAndExit();
      instance.removeFromWorkerHubRailsPipelineCounter();

      setTimeout(() => {
        expect(instance.getRequestCounterValue()).equal(0)
        expect(workerExitStub.calledOnce).to.be.true;
        workerExitStub.restore();
        done();
      }, workerExitPollInterval * 4); // Add a buffer time for the polling interval
    });

    it('should exit when hard timeout is reached', function(done) {
      this.timeout(workerExitHardTimeout * 2)
      const instance = workerHubRailsPipelineManager;
      const workerExitStub = sinon.stub(instance, 'workerExit');

      // Simulate counter being non-empty
      instance.addToWorkerHubRailsPipelineCounter();
      instance.startPollingAndExit();

      setTimeout(() => {
        expect(instance.getRequestCounterValue()).equal(1)
        expect(workerExitStub.calledOnce).to.be.true;
        workerExitStub.restore();
        done();
      }, workerExitHardTimeout + 1000); // Add a buffer time for the hard timeout
    });
  });
});
