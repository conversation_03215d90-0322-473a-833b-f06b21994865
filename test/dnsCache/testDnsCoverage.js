/* global describe, it */

'use strict';

const globalOptions = {
  enable: true,
  ttl: 300,
  cachesize: 1000,
};
const assert = require('assert');
const async = require('async');
let dns = require('../../helpers/dnscache')(globalOptions);
const { deepCopy } = require('../../helpers/dnsCacheUtil');

function prepare(options) {
  let conf = options;
  if (!conf) {
    conf = globalOptions;
  }
  // if created from other tests
  if (dns.internalCache) {
    delete dns.internalCache;
  }
  dns = require('../../helpers/dnscache')(conf); // eslint-disable-line global-require
  return dns;
}

describe('dnscache additional tests for full coverage', function fn() {
  this.timeout(10000); // dns queries are slow..

  it('should convert family string to number', (done) => {
    prepare();
    dns.lookup('127.0.0.1', '4', () => {
      dns.lookup('127.0.0.1', '6', () => {
        done();
      });
    });
  });

  it('coverage: options is undefined', (done) => {
    prepare();
    dns.lookup('127.0.0.1', undefined, () => {
      done();
    });
  });

  it('coverage: custom cache object', (done) => {
    const { CacheObject: cacheClass } = require('../../helpers/dnsCacheObject'); // eslint-disable-line global-require
    prepare({ enable: true, cache: cacheClass });
    done();
  });

  it('coverage: simultaneous requests', (done) => {
    prepare();
    const methods = [
      ['lookup', '127.0.0.1'],
      ['resolve', 'localhost'],
      ['resolve4', 'localhost'],
      ['resolve6', 'localhost'],
      ['resolveMx', 'yahoo.com'],
      ['resolveTxt', 'yahoo.com'],
      ['resolveNs', 'yahoo.com'],
      ['resolveCname', 'www.yahoo.com'],
      ['reverse', '*******'],
    ];

    async.eachSeries(methods, (method, itemDone) => {
      async.times(2, (i, callback) => {
        dns[method[0]](method[1], (err, result) => {
          callback(null, result);
        });
      }, (err, results) => {
        (assert.deepStrictEqual || assert.deepEqual)(results[0], results[1], 'expected same result from cached query');
        itemDone(null);
      });
    }, () => {
      done();
    });
  });
});

describe('dnsCacheUtilTests', () => {
  it('deepCopy should create a copy of the object', (done) => {
    const obj = {
      a: [1, 2, 3],
      b: {
        c: 'test',
        d: 1,
      },
    };

    const deepCopyObj = deepCopy(obj);
    assert.deepEqual(obj, deepCopyObj);
    done();
  });

  it('deepCopy return undefined if value is undefined', (done) => {
    const deepCopyObj = deepCopy(undefined);
    assert.equal('undefined', typeof deepCopyObj);
    done();
  });
});
