'use strict';

const assert = require('assert');
const async = require('async');
const sinon = require('sinon');
const mod = require('../../helpers/dnscache')({
  enable: true,
  ttl: 300,
  cachesize: 1000,
});

const dns = require('dns');

const methods = [dns.lookup, dns.resolve, dns.resolve4, dns.resolve6, dns.resolveMx, dns.resolveTxt,
  dns.resolveSrv, dns.resolveNs, dns.resolveCname, dns.reverse];
const params = ['www.yahoo.com', 'www.google.com', 'www.google.com', 'ipv6.google.com', 'yahoo.com',
  'google.com', 'www.yahoo.com', 'yahoo.com', 'www.yahoo.com', '*************'];
const prefix = ['lookup_', 'resolve_', 'resolve4_', 'resolve6_', 'resolveMx_', 'resolveTxt_',
  'resolveSrv_', 'resolveNs_', 'resolveCname_', 'reverse_'];
const suffix = ['_0_0_false', '_A', 'none', 'none', 'none', 'none', 'none', 'none', 'none', 'none'];

// node v4+ dns.lookup support "all" option.
const nodeSupportLookupAll = process.versions.node.split('.')[0] >= 4;
// node v0.12+ dns.lookup support "hints" option.
const nodeSupportLookupHints = process.versions.node.split('.')[0] > 0 || process.versions.node.split('.')[1] >= 12;

describe('dnscache main test suite', function fn() {
  afterEach(() => {
    sinon.restore();
  });
  this.timeout(10000); // dns queries are slow..

  it('should export an Object', () => {
    assert.equal(typeof mod, 'object');
  });

  it('should verify internal cache is create for each call', (done) => {
    let index = 0;
    async.eachSeries(methods, (method, cb) => {
      method(params[index], (err, result) => {
        index += 1;
        cb(err, result);
      });
    }, () => {
      assert.ok(dns.internalCache);
      methods.forEach((name, indexKey) => {
        const key = suffix[indexKey] !== 'none' ? prefix[indexKey] + params[indexKey] + suffix[indexKey] : prefix[indexKey] + params[indexKey];
        assert.ok(dns.internalCache.data[key], `entry not there for ${key}`);
        assert.equal(dns.internalCache.data[key].hit, 0, `hit should be 0 for ${key}`);
      });
      done();
    });
  });

  it('verify hits are incremented', (done) => {
    let index = 0;
    async.eachSeries(methods, (method, cb) => {
      method(params[index], (err, result) => {
        index += 1;
        cb(err, result);
      });
    }, () => {
      assert.ok(dns.internalCache);
      methods.forEach((name, indexKey) => {
        const key = suffix[indexKey] !== 'none' ? prefix[indexKey] + params[indexKey] + suffix[indexKey] : prefix[indexKey] + params[indexKey];
        assert.ok(dns.internalCache.data[key], `entry not there for ${key}`);
        assert.equal(dns.internalCache.data[key].hit, 1, `hit should be 1 for ${key}`);
      });
      done();
    });
  });

  it('require again and verify cache is same as before', (done) => {
    require('../../helpers/dnscache')({ // eslint-disable-line global-require
      enable: true,
      ttl: 300,
      cachesize: 1000,
    });
    assert.ok(dns.internalCache);
    methods.forEach((name, index) => {
      const key = suffix[index] !== 'none' ? prefix[index] + params[index] + suffix[index] : prefix[index] + params[index];
      assert.ok(dns.internalCache.data[key], `entry not there for ${key}`);
      assert.equal(dns.internalCache.data[key].hit, 1, `hit should be 1 for ${key}`);
    });
    done();
  });

  it('should verify family4/family6 cache is created for local addresses', (done) => {
    dns.lookup('127.0.0.1', 4, () => {
      dns.lookup('::1', 6, () => {
        dns.lookup('127.0.0.1', { family: 4, hints: dns.ADDRCONFIG }, () => {
          dns.lookup('127.0.0.1', { hints: dns.ADDRCONFIG }, () => {
            dns.lookup('::1', { family: 6, hints: dns.ADDRCONFIG }, () => {
              assert.ok(dns.internalCache);
              const hit = nodeSupportLookupHints ? 0 : 1;
              assert.equal(dns.internalCache.data['lookup_127.0.0.1_4_0_false'].hit, hit, `hit should be ${hit} for family4`);
              assert.equal(dns.internalCache.data['lookup_::1_6_0_false'].hit, hit, `hit should be ${hit} for family6`);
              done();
            });
          });
        });
      });
    });
  });

  it('should error if the underlying dns method throws', (done) => {
    const errors = [];
    async.each(methods, (method, cb) => {
      method([], (err) => {
        errors.push(err);
        cb(null);
      });
    }, (err) => {
      assert.ok(!err);
      assert.ok(Array.isArray(errors));
      assert.ok(errors.length > 0);
      errors.forEach((e) => {
        if (e) { // one node 0.10 method doens't throw
          assert.ok((e instanceof Error));
        }
      });
      done();
    });
  });

  it('should error on invalid reverse lookup', (done) => {
    // from the TEST-NET-1 block, as specified in https://tools.ietf.org/html/rfc5737
    dns.reverse('192.0.2.0', (err) => {
      assert.ok((err instanceof Error));
      done();
    });
  });

  it('should error on invalid family', (done) => {
    dns.lookup('127.0.0.1', 7, (err) => {
      assert.ok((err instanceof Error));
      done();
    });
  });

  it('should error on invalid family Object', () => {
    dns.lookup('127.0.0.1', { family: 7 }, (err) => {
      assert.ok((err instanceof Error));
    });
  });

  it('should create resolve cache with type', (done) => {
    dns.resolve('www.yahoo.com', 'A', (err, result) => {
      assert.ok(dns.internalCache);
      assert.ok(result);
      assert.equal(dns.internalCache.data['resolve_www.yahoo.com_A'].hit, 0, 'hit should be 0 for resolve');
      done();
    });
  });

  it('not create a cache from an error in a lookup', (done) => {
    let index = 0; // eslint-disable-line no-unused-vars
    async.eachSeries(methods, (method, cb) => {
      method('someerrordata', (err) => {
        index += 1;
        cb(null, err);
      });
    }, () => {
      methods.forEach((name, indexKey) => {
        const key = suffix[indexKey] !== 'none' ? `${prefix[indexKey]}someerrordata${suffix[indexKey]}` : `${prefix[indexKey]}someerrordata`;
        assert.equal(dns.internalCache.data[key], undefined, `entry should not there for ${key}`);
      });
      done();
    });
  });

  it('should not cache by default', (done) => {
    // if created from other tests
    if (require('dns').internalCache) { // eslint-disable-line global-require
      delete require('dns').internalCache; // eslint-disable-line global-require
    }
    const testee = require('../../helpers/dnscache')(); // eslint-disable-line global-require
    testee.lookup('127.0.0.1', () => {
      assert.ok(!dns.internalCache);
      assert.ok(!require('dns').internalCache); // eslint-disable-line global-require
      done();
    });
  });

  it('should not cache if enabled: false', (done) => {
    // if created from other tests
    if (require('dns').internalCache) { // eslint-disable-line global-require
      delete require('dns').internalCache; // eslint-disable-line global-require
    }
    const testee = require('../../helpers/dnscache')({ // eslint-disable-line global-require
      enable: false,
    });

    testee.lookup('127.0.0.1', () => {
      assert.ok(!testee.internalCache);
      assert.ok(!require('dns').internalCache); // eslint-disable-line global-require
      done();
    });
  });

  it('should not cache if cachsize is 0', (done) => {
    // if created from other tests
    if (require('dns').internalCache) { // eslint-disable-line global-require
      delete require('dns').internalCache; // eslint-disable-line global-require
    }
    const testee = require('../../helpers/dnscache')({ // eslint-disable-line global-require
      enable: true,
      cachesize: 0,
    });
    testee.lookup('127.0.0.1', () => {
      assert.ok(!testee.internalCache);
      assert.ok(!require('dns').internalCache); // eslint-disable-line global-require
      done();
    });
  });

  it('should create a cache with default settings', (done) => {
    // if created from other tests
    if (require('dns').internalCache) { // eslint-disable-line global-require
      delete require('dns').internalCache; // eslint-disable-line global-require
    }
    const conf = {
      enable: true,
    };
    const testee = require('../../helpers/dnscache')(conf); // eslint-disable-line global-require

    testee.lookup('127.0.0.1', () => {
      // verify cache is created
      assert.ok(testee.internalCache);
      assert.equal(testee.internalCache.data['lookup_127.0.0.1_0_0_false'].hit, 0, 'hit should be 0 for family4');
      assert.ok(dns.internalCache);
      assert.equal(dns.internalCache.data['lookup_127.0.0.1_0_0_false'].hit, 0, 'hit should be 0 for family4');

      // verify default values
      assert.ok(conf);
      assert.equal(conf.ttl, 300);
      assert.equal(conf.cachesize, 1000);
      done();
    });
  });

  // lookup's all option require node v4+.
  if (nodeSupportLookupAll) {
    it('should return array if lookup all', (done) => {
      // if created from other tests
      if (require('dns').internalCache) { // eslint-disable-line global-require
        delete require('dns').internalCache; // eslint-disable-line global-require
      }
      const conf = {
        enable: true,
      };
      const testee = require('../../helpers/dnscache')(conf); // eslint-disable-line global-require
      dns.lookup('127.0.0.1', { all: true }, (err, addresses) => {
        assert.ok(Array.isArray(addresses));
        assert.equal(testee.internalCache.data['lookup_127.0.0.1_0_0_true'].hit, 0, 'hit should be 0');
        dns.lookup('127.0.0.1', { all: true }, (error, _addresses) => {
          assert.ok(Array.isArray(_addresses));
          assert.equal(testee.internalCache.data['lookup_127.0.0.1_0_0_true'].hit, 1, 'hit should be 1');
          done();
        });
      });
    });

    it('should return an array copy if lookup all', (done) => {
      // if created from other tests
      if (require('dns').internalCache) { // eslint-disable-line global-require
        delete require('dns').internalCache; // eslint-disable-line global-require
      }
      const conf = {
        enable: true,
      };
      const testee = require('../../helpers/dnscache')(conf); // eslint-disable-line global-require
      dns.lookup('127.0.0.1', { all: true }, (err, addresses) => {
        assert.ok(Array.isArray(addresses));
        addresses.pop();
        assert.equal(testee.internalCache.data['lookup_127.0.0.1_0_0_true'].val.length, 1, 'length should be 1');
        done();
      });
    });

    it('should use expired cache if dns cache lookup throws error', (done) => {
      // if created from other tests
      if (require('dns').internalCache) { // eslint-disable-line global-require
        delete require('dns').internalCache; // eslint-disable-line global-require
      }
      const conf = {
        enable: true,
      };
      const testee = require('../../helpers/dnscache')(conf); // eslint-disable-line global-require
      const cache = testee.internalCache;
      const cacheGetStub = sinon.stub(cache, 'get');
      cacheGetStub.callsArgWith(1, undefined, {
        value: { address: '127.0.0.1', family: 4 },
        isExpired: true,
      });
      testee.lookup('mylocaltest', (err, address, family) => {
        assert.ok(testee.internalCache);
        assert.equal(address, '127.0.0.1');
        assert.equal(family, 4);
        cacheGetStub.restore();
        done();
      });
    });

    it('should use cache if dns cache if not expired', (done) => {
      // if created from other tests
      if (require('dns').internalCache) { // eslint-disable-line global-require
        delete require('dns').internalCache; // eslint-disable-line global-require
      }
      const conf = {
        enable: true,
        timeout: 25000,
      };
      const testee = require('../../helpers/dnscache')(conf); // eslint-disable-line global-require
      const cache = testee.internalCache;
      const cacheGetStub = sinon.stub(cache, 'get');
      cacheGetStub.callsArgWith(1, undefined, {
        value: { address: '*********', family: 4 },
        isExpired: false,
      });
      testee.lookup('localhost', (err, address, family) => {
        assert.ok(testee.internalCache);
        assert.equal(address, '*********');
        assert.equal(family, 4);
        cacheGetStub.restore();
        done();
      });
    });

    it('should timeout lookup within configured limit', (done) => {
      // if created from other tests
      if (require('dns').internalCache) { // eslint-disable-line global-require
        delete require('dns').internalCache; // eslint-disable-line global-require
      }
      const conf = {
        enable: true,
        timeout: 0,
      };
      // eslint-disable-next-line global-require
      const testee = require('../../helpers/dnscache')(conf);
      const cache = testee.internalCache;
      const cacheGetStub = sinon.stub(cache, 'get');
      cacheGetStub.callsArgWith(1, undefined, undefined);
      testee.lookup('some-random-site-that-doesnt-work', (err) => {
        try {
          assert.equal(err.message, 'DNS Timedout');
          cacheGetStub.restore();
          done();
        } catch (e) {
          done(e);
        }
      });
    });

    it('should add in cache even if timesout', (done) => {
      // if created from other tests
      if (require('dns').internalCache) { // eslint-disable-line global-require
        delete require('dns').internalCache; // eslint-disable-line global-require
      }
      const conf = {
        enable: true,
        timeout: 0,
      };
      // eslint-disable-next-line global-require
      const testee = require('../../helpers/dnscache')(conf);
      const cache = testee.internalCache;
      const cacheGetStub = sinon.stub(cache, 'get');
      cacheGetStub.callsArgWith(1, undefined, undefined);
      testee.lookup('some-site.com', (err) => {
        try {
          assert.equal(err.message, 'DNS Timedout');
          assert.ok(testee.internalCache);
          cacheGetStub.restore();
          done();
        } catch (e) {
          done(e);
        }
      });
    });

    it('should return expired record if lookup timedout', (done) => {
      // if created from other tests
      if (require('dns').internalCache) { // eslint-disable-line global-require
        delete require('dns').internalCache; // eslint-disable-line global-require
      }
      const conf = {
        enable: true,
        timeout: 25000,
      };
      // eslint-disable-next-line global-require
      const testee = require('../../helpers/dnscache')(conf);
      const cache = testee.internalCache;
      const cacheGetStub = sinon.stub(cache, 'get');
      cacheGetStub.callsArgWith(1, undefined, {
        value: { address: '127.0.0.1', family: 4 },
        isExpired: true,
      });
      testee.lookup('mylocaltest', (err, address, family) => {
        assert.ok(testee.internalCache);
        assert.equal(address, '127.0.0.1');
        assert.equal(family, 4);
        cacheGetStub.restore();
        done();
      });
    });
  }
});
