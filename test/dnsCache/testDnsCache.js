'use strict';

const assert = require('assert');
const async = require('async');
const { CacheObject: ModCache } = require('../../helpers/dnsCacheObject');
const { setIsExpiredHitStatsForAMethod } = require('../../helpers/dnsCacheObject');

describe('caching tests', () => {
  it('should return a Cache Object with defaults', () => {
    const conf = {};
    const mod = new ModCache(conf);
    assert.ok(conf);
    assert.equal(conf.ttl, 30);
    assert.equal(conf.cachesize, 1000);
    assert.equal(mod.ttl, 30 * 1000);
  });

  it('should return a Cache Object with defaults without config', () => {
    const mod = new ModCache();
    assert.equal(mod.ttl, 30 * 1000);
    assert.equal(mod.max, 1000);
  });

  it('should cache entries for lru', (done) => {
    const CacheObject = new ModCache({ ttl: 30, cachesize: 5 });
    const array = Array(...Array(5)).map((v, k) => k);

    async.each(array, (name, callback) => {
      CacheObject.set(name, name, () => {
        callback(null);
      });
    }, () => {
      CacheObject.get(0, (err0) => {
        CacheObject.get(1, (err1, data1) => {
          CacheObject.get('unknownkey', (err2, data2) => {
            assert.equal(true, data1 !== undefined && data2 === undefined);
            assert.equal(null, err0);
            assert.equal(null, err1);
            assert.equal(null, err2);
            done();
          });
        });
      });
    });
  });

  it('should update multiple keys', (done) => {
    const CacheObject = new ModCache({
      ttl: 30,
      cachesize: 5,
    });

    CacheObject.set(1, 1, () => {
      CacheObject.set(2, 2, () => {
        CacheObject.set(3, 30, () => {
          CacheObject.set(3, 31, () => {
            CacheObject.set(2, 4, () => {
              CacheObject.set(2, 5, () => {
                CacheObject.set(1, 6, () => {
                  assert.equal(CacheObject.count, 3);
                  assert.equal(CacheObject.tail.key, 3);
                  assert.equal(CacheObject.tail.val, 31);
                  assert.equal(CacheObject.head.key, 1);
                  assert.equal(CacheObject.head.val, 6);
                  done();
                });
              });
            });
          });
        });
      });
    });
  });

  it('should get a key', (done) => {
    const CacheObject = new ModCache({
      ttl: 30,
      cachesize: 5,
    });
    CacheObject.set(1, 1, () => {
      CacheObject.set(1, 2, () => {
        CacheObject.get(1, (err, rec) => {
          assert.equal(rec.value, 2);
          done();
        });
      });
    });
  });

  it('should allow to see hit stats', (done) => {
    const CacheObject = new ModCache({
      ttl: 30,
      cachesize: 5,
    });

    CacheObject.set(1, 1, () => {
      CacheObject.get(1, () => {
        CacheObject.get(1, () => {
          assert.equal(CacheObject.data['1'].hit, 2);
          done();
        });
      });
    });
  });

  it('should set expire false to true for a single item', (done) => {
    const CacheObject = new ModCache({
      ttl: 1,
      cachesize: 5,
    });
    const noop = function fn() {};
    CacheObject.set(1, 1);
    CacheObject.get(1, noop);
    CacheObject.get(1, noop);
    setTimeout(() => {
      CacheObject.get(1, (err, result) => {
        assert.equal(true, result.isExpired);
        done();
      });
    }, 1200);
  });

  it('should not expire a key', (done) => {
    const CacheObject = new ModCache({
      ttl: 0,
      cachesize: 5,
    });
    CacheObject.set(2, 2);
    CacheObject.set(1, 1);
    setTimeout(() => {
      CacheObject.get(1, () => {
        assert.ok(CacheObject.data['1']);
        assert.equal(CacheObject.data['1'].val, 1);
        done();
      });
    }, 1200);
  });

  it('should expire head via ttl', (done) => {
    const CacheObject = new ModCache({
      ttl: 1,
      cachesize: 5,
    });
    CacheObject.set(2, 2);
    CacheObject.set(1, 1);
    setTimeout(() => {
      CacheObject.get(1, (err, result) => {
        assert.equal(true, result.isExpired);
        done();
      });
    }, 1200);
  });

  it('should expire tail via ttl', (done) => {
    const CacheObject = new ModCache({
      ttl: 1,
      cachesize: 5,
    });
    CacheObject.set(1, 1);
    CacheObject.set(2, 2);
    setTimeout(() => {
      CacheObject.get(1, (err, result) => {
        assert.equal(true, result.isExpired);
        done();
      });
    }, 1200);
  });

  it('should expire middle via ttl', (done) => {
    const CacheObject = new ModCache({
      ttl: 1,
      cachesize: 5,
    });
    CacheObject.set(3, 3);
    CacheObject.set(1, 1);
    CacheObject.set(2, 2);
    setTimeout(() => {
      CacheObject.get(1, (err, result) => {
        assert.equal(true, result.isExpired);
        done();
      });
    }, 1200);
  });

  it('should throw if cache.get does not get a callback', () => {
    const CacheObject = new ModCache({
      ttl: 1,
      cachesize: 5,
    });

    assert.throws(() => {
      CacheObject.get(1);
    }, /callback is required/);
  });

  it('should remove items from cache when cache limit is hit', (done) => {
    const CacheObject = new ModCache({
      ttl: 1,
      cachesize: 2,
    });

    CacheObject.set(1, 1, () => {
      assert.equal(CacheObject.count, 1);
      CacheObject.set(2, 1, () => {
        assert.equal(CacheObject.count, 2);
        CacheObject.set(3, 1, () => {
          assert.equal(CacheObject.count, 2);
          done();
        });
      });
    });
  });

  it('should not error when calling cache.get on an expired key twice in the same tick', (done) => {
    const CacheObject = new ModCache({
      ttl: 1,
      cachesize: 5,
    });
    CacheObject.set(1, 1);
    setTimeout(() => {
      CacheObject.get(1, Function.prototype);
      CacheObject.get(1, () => {
        done();
      });
    }, 1200);
  });
});

describe('miscelleanous methods', () => {
  it('setIsExpiredHitStatsForAMethod should execute without error', (done) => {
    assert.doesNotThrow(() => {
      setIsExpiredHitStatsForAMethod('random_dns_method');
    });
    done();
  });
});
