'use strict';

const instrumentExecutor = require('../../helpers/customSeleniumHandling/instrumentExecutor');
const assert = require('assert');

describe('getInstrumentationData', () => {
  it('should return custom executor instrumentation data from keyObject', function () {
    const inputKeyObject = { customExecutorInstrumentation: { count: 0 } };
    assert.deepEqual(instrumentExecutor.getInstrumentationData(inputKeyObject), { count: 0 });
  });

  it('should not return custom executor instrumentation data from keyObject if absent', function () {
    const inputKeyObject = {};
    assert.strictEqual(instrumentExecutor.getInstrumentationData(inputKeyObject), undefined);
  });
});

describe('setInstrumentationData', () => {
  it('should increment total count of executor called incase of success', function () {
    const inputKeyObject = {};
    const outputKeyObject = {
      'customExecutorInstrumentation': { 'custom_executor': {
        'random executor': { 'count': 1, 'success': { 'count': 1 },
        }}}};
    instrumentExecutor.setInstrumentationData(inputKeyObject, 'random executor', 'success');
    assert.deepEqual(inputKeyObject, outputKeyObject);
  });

  it('should increment total count of executor called incase of failure', function () {
    const inputKeyObject = {};
    const outputKeyObject = {
      'customExecutorInstrumentation': { 'custom_executor': {
          'random executor': { 'count': 1, 'error': { 'count': 1, 'error_types': {'randomType': 1} },
          }}}};
    instrumentExecutor.setInstrumentationData(inputKeyObject, 'random executor', 'error', 'randomType');
    assert.deepEqual(inputKeyObject, outputKeyObject);
  });

  it('should increment total count of executor called incase of success and error', function () {
    const inputKeyObject = {};
    const outputKeyObject = {
      'customExecutorInstrumentation': { 'custom_executor': {
          'random executor': { 'count': 2, 'success': { 'count': 1 },
            'error': { 'count': 1, 'error_types': {'randomType': 1} },
          }}}};
    instrumentExecutor.setInstrumentationData(inputKeyObject, 'random executor', 'success');
    instrumentExecutor.setInstrumentationData(inputKeyObject, 'random executor', 'error', 'randomType');
    assert.deepEqual(inputKeyObject, outputKeyObject);
  });

  it('should set instrumentation key incase key not present', function () {
    const inputKeyObject = {};
    const outputKeyObject = {
      'customExecutorInstrumentation': { 'custom_executor': {
          'random executor': { 'count': 1, 'success': { 'count': 1 },
          }}}};
    instrumentExecutor.setInstrumentationData(inputKeyObject, 'random executor', 'success');
    assert.deepEqual(inputKeyObject, outputKeyObject);
  });

  it('should increment the instrumentation key count incase key already present', function () {
    const inputKeyObject = {};
    const outputKeyObject = {
      'customExecutorInstrumentation': { 'custom_executor': {
          'random executor': { 'count': 2, 'success': { 'count': 2 },
          }}}};
    instrumentExecutor.setInstrumentationData(inputKeyObject, 'random executor', 'success');
    instrumentExecutor.setInstrumentationData(inputKeyObject, 'random executor', 'success');
    assert.deepEqual(inputKeyObject, outputKeyObject);
  });

  it('should not set error instrumentation if statusType absent', function () {
    const inputKeyObject = {};
    const outputKeyObject = {};
    instrumentExecutor.setInstrumentationData(inputKeyObject, 'random executor', 'error');
    assert.deepEqual(inputKeyObject, outputKeyObject);
  });

  it('should not set instrumentation data if not a valid executor status', function () {
    const inputKeyObject = {};
    const outputKeyObject = {};
    instrumentExecutor.setInstrumentationData(inputKeyObject, 'random executor', 'randomstatus');
    assert.deepEqual(inputKeyObject, outputKeyObject);
  });
});
