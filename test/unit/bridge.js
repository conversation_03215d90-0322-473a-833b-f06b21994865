const helper = require('./helper');
const hub = require('../../hub');
const ha = require('../../ha');
const pubSub = require('../../pubSub');
const debug = require('../../debug');
const HubLogger = require('../../log');
const browserstack = require('../../browserstack');
const supporting = require('../../supporting');
const bridge = require('../../bridge');
const constants = require('../../constants');
const requestlib = require('../../lib/request');
const origHelper = require('../../helper');
const sinon = require('sinon');
const assert = require('assert');
const { should, expect } = require('chai');
const LL = constants.LOG_LEVEL;
const ERROR = constants.ERROR;

var testSuccessAndFailure = function(name, params, keyObject, value, statusCode, state, clock, date) {
  describe("Test: " + name, function() {
    var clock, date = Date.parse('01/01/2001');

    beforeEach(function() {
      keyObject.autoWait = true;
      params.request.lastRequestLogicDone = false;

      constants['global_registry'][keyObject.rails_session_id] = {
        secondary_state: constants.secondary_states.SUCCESS
      };
      constants['timeout_registry'][keyObject.rails_session_id] = {
        key: 'value'
      };

      clock = sinon.useFakeTimers();
      sinon.stub(HubLogger, 'tempExceptionLogger');
      sinon.stub(Date.prototype, 'getTime').returns(date);
      sinon.stub(supporting, 'checkPageLoadOnTerminal');
      sinon.stub(hub, 'sessionNotFound').returns('sessionNotFound');
      sinon.stub(hub, 'createBridgeClientAndNode').returns('returnValue');
    });
    it('Returns on success', function() {
      var requestObject = params.request;

      supporting.checkPageLoadOnTerminal.callsArg(1);
      bridge.transitionCheck(keyObject, params);

      requestObject.recurseCount = 0;
      requestObject.lastRequestLogicDone = true;

      supporting.checkPageLoadOnTerminal.called.should.be.true;
      supporting.checkPageLoadOnTerminal.calledWith(keyObject).should.be.true;

      hub.createBridgeClientAndNode.called.should.be.true;
      hub.createBridgeClientAndNode.calledWith(keyObject, requestObject, params.response, params.callbacks).should.be.true;
    });
    it('Returns on no session found', function() {
      var requestObject = params.request;

      delete constants.global_registry[keyObject.rails_session_id]
      supporting.checkPageLoadOnTerminal.callsArgWith(2, 'err', 'output');
      bridge.transitionCheck(keyObject, params);
      clock.tick(1000);

      requestObject.recurseCount = 0;
      requestObject.lastRequestLogicDone = true;

      supporting.checkPageLoadOnTerminal.called.should.be.true;
      supporting.checkPageLoadOnTerminal.calledWith(keyObject).should.be.true;

      hub.sessionNotFound.called.should.be.true;
    });
    it('Returns createBridgeClientAndNode', function() {
      var requestObject = params.request;

      supporting.checkPageLoadOnTerminal.callsArgWith(2, 'err', 'output');
      bridge.transitionCheck(keyObject, params);
      clock.tick(1000);

      requestObject.recurseCount = 0;
      requestObject.lastRequestLogicDone = true;

      supporting.checkPageLoadOnTerminal.called.should.be.true;
      supporting.checkPageLoadOnTerminal.calledWith(keyObject).should.be.true;

      hub.createBridgeClientAndNode.called.should.be.true;
      hub.createBridgeClientAndNode.calledWith(keyObject, requestObject, params.response, params.callbacks).should.be.true;
    });
    it('Returns processResponse', function() {
      params.request.recurseCount = 50;
      params.request.headers['x-rtt'] = 100;
      var requestObject = params.request;
      var outData = {
        state: state,
        sessionId: keyObject.rails_session_id,
        value: value,
        class: "org.openqa.selenium.remote.Response",
        status: statusCode
      };
      outData = JSON.stringify(outData);

      var promiseStub = sinon.stub().returns({
        catch: sinon.stub().callsArgWith(0, 'err')
      });
      sinon.stub(bridge, 'loadRequestData').returns({
        then: promiseStub
      });
      promiseStub.callsArgWith(0, {
        data: 'data'
      });

      sinon.stub(ha, 'setData');
      sinon.stub(pubSub, 'publish');
      sinon.stub(hub, 'processResponse');
      sinon.stub(origHelper, 'getDate').returns(date);
      sinon.stub(HubLogger, 'seleniumStats');
      supporting.checkPageLoadOnTerminal.callsArgWith(2, 'err', 'output');
      bridge.transitionCheck(keyObject, params);
      clock.tick(1000);

      requestObject.recurseCount = 0;
      requestObject.lastRequestLogicDone = true;

      supporting.checkPageLoadOnTerminal.called.should.be.true;
      supporting.checkPageLoadOnTerminal.calledWith(keyObject).should.be.true;

      keyObject.timestamp.should.equal(date);
      ha.setData.called.should.be.true;
      ha.setData.calledWith(keyObject.rails_session_id, keyObject).should.be.true;
      pubSub.publish.calledWith(constants.updateKeyObject, {
        session: keyObject.rails_session_id,
        changed: {
          timestamp: date,
          instable: true
        }
      }).should.be.true;
      params.request.log_date.should.equal(date);
      HubLogger.seleniumStats.called.should.be.true;
      HubLogger.seleniumStats.calledWith("automate-page-still-loading", keyObject, "Action requested when page is still loading.", outData, "selenium-command-error-prevention").should.be.true;
      hub.processResponse.called.should.be.true;
      hub.processResponse.calledWith(params.request, params.response, keyObject, {
        data: outData,
        output: 'output',
        remoteSessionID: params.remoteSessionID,
        clientSessionID: params.clientSessionID,
        index_counter: params.index_counter,
        callbacks: params.callbacks,
        hostname: params.hostname,
        originalUrl: params.originalUrl
      }).should.be.true;

      ha.setData.restore();
      pubSub.publish.restore();
      origHelper.getDate.restore();
      hub.processResponse.restore();
      HubLogger.seleniumStats.restore();
      bridge.loadRequestData.restore();
    });

    afterEach(function() {
      clock.restore();
      hub.sessionNotFound.restore();
      Date.prototype.getTime.restore();
      HubLogger.tempExceptionLogger.restore();
      hub.createBridgeClientAndNode.restore();
      supporting.checkPageLoadOnTerminal.restore();
      delete constants.global_registry[keyObject.rails_session_id]
      delete constants.timeout_registry[keyObject.rails_session_id]
    });
  });
};

var testPostWindowDeleteCommandsIOS = function (name, params, keyObject, value, statusCode, state, clock, date) {
  describe("Test: " + name, function () {
    var clock, date = Date.parse('01/01/2001');

    beforeEach(function () {
      keyObject.autoWait = true;
      params.request.lastRequestLogicDone = false;

      constants['global_registry'][keyObject.rails_session_id] = {
        secondary_state: constants.secondary_states.SUCCESS
      };
      constants['timeout_registry'][keyObject.rails_session_id] = {
        key: 'value'
      };

      clock = sinon.useFakeTimers();
      sinon.stub(HubLogger, 'tempExceptionLogger');
      sinon.stub(Date.prototype, 'getTime').returns(date);
      sinon.stub(supporting, 'checkPageLoadOnTerminal');
      sinon.stub(hub, 'processResponse').returns('responseProcessed');
      sinon.stub(hub, 'sessionNotFound').returns('sessionNotFound');
      sinon.stub(hub, 'createBridgeClientAndNode').returns('returnValue');
    });

    it('Returns the response without calling `checkPageLoadOnTerminal` and `createBridgeClientAndNode`', function () {
      var requestObject = params.request;
      bridge.transitionCheck(keyObject, params);

      requestObject.recurseCount = 0;
      requestObject.lastRequestLogicDone = true;

      supporting.checkPageLoadOnTerminal.called.should.be.false;
      hub.createBridgeClientAndNode.called.should.be.false;
    });

    afterEach(function () {
      clock.restore();
      hub.sessionNotFound.restore();
      Date.prototype.getTime.restore();
      HubLogger.tempExceptionLogger.restore();
      hub.createBridgeClientAndNode.restore();
      supporting.checkPageLoadOnTerminal.restore();
      hub.processResponse.restore();
      delete constants.global_registry[keyObject.rails_session_id]
      delete constants.timeout_registry[keyObject.rails_session_id]
    });
  });
};

var testSuccessAndFailureIgnored = function(name, params, keyObject, value, statusCode, state, clock, date) {
  describe("Test: " + name, function() {
    var clock;

    beforeEach(function() {
      keyObject.autoWait = true;
      params.request.lastRequestLogicDone = false;

      constants['global_registry'][keyObject.rails_session_id] = {
        secondary_state: constants.secondary_states.SUCCESS
      };
      constants['timeout_registry'][keyObject.rails_session_id] = {
        key: 'value'
      };

      clock = sinon.useFakeTimers();
      sinon.stub(supporting, 'checkPageLoadOnTerminal');
      sinon.stub(hub, 'createBridgeClientAndNode').returns('returnValue');
    });
    it('Returns on success', function() {
      var requestObject = params.request;

      supporting.checkPageLoadOnTerminal.callsArg(1);
      bridge.transitionCheck(keyObject, params);

      requestObject.recurseCount = 0;
      requestObject.lastRequestLogicDone = true;

      supporting.checkPageLoadOnTerminal.called.should.be.false;

      hub.createBridgeClientAndNode.called.should.be.false;
    });

    afterEach(function() {
      clock.restore();
      hub.createBridgeClientAndNode.restore();
      supporting.checkPageLoadOnTerminal.restore();
      delete constants.global_registry[keyObject.rails_session_id]
      delete constants.timeout_registry[keyObject.rails_session_id]
    });
  });
};

describe('Bridge', function() {
  describe('basicAuthPopupExists', function() {
    xit('Returns correct JSON response if a popup is open');
  });
  describe('transitionCheck', function() {
    var keyObject = helper.getKeyObject(), params = helper.getParamsObject();

    describe('Last loading ignore', function() {
      describe('Edge and about:blank open URL', function() {
        var value = [];
        var statusCode = 0;
        var state = 'success';

        beforeEach(function() {
          keyObject.browser = "MicrosoftEdge";
          keyObject.browser_version = "10";
          keyObject.lastRequest = 'POST:url-about-blank';
          params.hash = 'POST:url-data';
        });

        testSuccessAndFailureIgnored('Edge and about:blank open URL', params, keyObject, value, statusCode, state);
      });
      describe('Edge and basic-auth open URL', function() {
        var value = [];
        var statusCode = 0;
        var state = 'success';

        beforeEach(function() {
          keyObject.browser = "MicrosoftEdge";
          keyObject.browser_version = "10";
          keyObject.lastRequest = 'POST:url-data';
          params.hash = 'POST:basic-auth-edge';
        });

        testSuccessAndFailureIgnored('Edge and about:blank open URL', params, keyObject, value, statusCode, state);
      });
      describe('iOS 10 and basic-auth open URL', function() {
        var value = [];
        var statusCode = 0;
        var state = 'success';

        beforeEach(function() {
          keyObject.device = "iPhone 7 Plus";
          keyObject.os_version = "10.0";
          keyObject.realMobile = "true";
          keyObject.lastRequest = 'POST:url-data';
          params.hash = 'POST:basic-auth-ios';
        });

        testSuccessAndFailureIgnored('iOS10 and basic auth open URL', params, keyObject, value, statusCode, state);
      });
      describe('Open a data: URL', function() {
        var value = [];
        var statusCode = 0;
        var state = 'success';

        beforeEach(function() {
          keyObject.browser = "chrome";
          keyObject.browser_version = "44";
          keyObject.lastRequest = 'POST:url-data';
          params.hash = 'POST:url-data';
        });

        testSuccessAndFailureIgnored('Open a data: URL', params, keyObject, value, statusCode, state);
      });
      describe('Open an URL ending with .xml', function(){
        var value = [];
        var statusCode = 0;
        var state = 'success';

        beforeEach(function() {
          keyObject.browser = "chrome";
          keyObject.browser_version = "44";
          keyObject.lastRequest = 'POST:url-xml';
          params.hash = 'POST:url-data';
        });

        testSuccessAndFailureIgnored('Open a data: URL', params, keyObject, value, statusCode, state);
      });
    });
    describe('Loading check triggers', function() {
      describe('Click', function(){
        var value = [];
        var statusCode = 0;
        var state = 'success';

        beforeEach(function() {
          keyObject.browser = "chrome";
          keyObject.browser_version = "44";
          keyObject.lastRequest = 'POST:click';
          params.hash = 'POST:elements';
        });

        testSuccessAndFailure('Click', params, keyObject, value, statusCode, state);
      });
      describe('Open URL', function(){
        var value = [];
        var statusCode = 0;
        var state = 'success';

        beforeEach(function() {
          keyObject.browser = "chrome";
          keyObject.browser_version = "44";
          keyObject.lastRequest = 'POST:url';
          params.hash = 'POST:elements';
        });

        testSuccessAndFailure('Open URL', params, keyObject, value, statusCode, state);
      });
      describe('Refresh', function(){
        var value = [];
        var statusCode = 0;
        var state = 'success';

        beforeEach(function() {
          keyObject.browser = "chrome";
          keyObject.browser_version = "44";
          keyObject.lastRequest = 'POST:refresh';
          params.hash = 'POST:elements';
        });

        testSuccessAndFailure('Refresh', params, keyObject, value, statusCode, state);
      });
      describe('Submit form', function(){
        var value = [];
        var statusCode = 0;
        var state = 'success';

        beforeEach(function() {
          keyObject.browser = "chrome";
          keyObject.browser_version = "44";
          keyObject.lastRequest = 'POST:submit';
          params.hash = 'POST:elements';
        });

        testSuccessAndFailure('Submit form', params, keyObject, value, statusCode, state);
      });
    });
    describe('Commands that get stuck after page load', function() {
      describe('Implicit wait', function(){
        var value = {"message":"An unknown server-side error occurred while processing the command.", "error": "unknown error"};
        var statusCode = 13;
        var state = 'error';

        beforeEach(function() {
          keyObject.browser = "chrome";
          keyObject.browser_version = "44";
          keyObject.lastRequest = 'POST:submit';
          params.hash = 'POST:implicit_wait';
        });

        testSuccessAndFailure('Implicit wait', params, keyObject, value, statusCode, state);
      });
      describe('Get source', function(){
        var value = {"message":"An unknown server-side error occurred while processing the command.", "error": "unknown error"};
        var statusCode = 13;
        var state = 'error';

        beforeEach(function() {
          keyObject.browser = "chrome";
          keyObject.browser_version = "44";
          keyObject.lastRequest = 'POST:submit';
          params.hash = 'GET:source';
        });

        testSuccessAndFailure('Get source', params, keyObject, value, statusCode, state);
      });
      describe('Delete cookie', function() {
        var value = {"message":"An unknown server-side error occurred while processing the command.", "error": "unknown error"};
        var statusCode = 13;
        var state = 'error';

        beforeEach(function() {
          keyObject.browser = "chrome";
          keyObject.browser_version = "44";
          keyObject.lastRequest = 'POST:submit';
          params.hash = 'DELETE:cookie';
        });

        testSuccessAndFailure('Delete cookie', params, keyObject, value, statusCode, state);
      });
      describe('Select frame', function(){
        var value = {"message":"An unknown server-side error occurred while processing the command.", "error": "unknown error"};
        var statusCode = 13;
        var state = 'error';

        beforeEach(function() {
          keyObject.browser = "chrome";
          keyObject.browser_version = "44";
          keyObject.lastRequest = 'POST:submit';
          params.hash = 'POST:frame';
        });

        testSuccessAndFailure('Select frame', params, keyObject, value, statusCode, state);
      });
      describe('Delete window', function(){
        var value = {"message":"An unknown server-side error occurred while processing the command.", "error":"unknown error"};
        var statusCode = 13;
        var state = 'error';

        beforeEach(function() {
          keyObject.browser = "chrome";
          keyObject.browser_version = "44";
          keyObject.lastRequest = 'POST:submit';
          params.hash = 'POST:frame';
        });

        testSuccessAndFailure('Delete window', params, keyObject, value, statusCode, state);
      });
      describe('Maximize window', function(){
        var value = {"message":"An unknown server-side error occurred while processing the command.", "error": "unknown error"};
        var statusCode = 13;
        var state = 'error';

        beforeEach(function() {
          keyObject.browser = "chrome";
          keyObject.browser_version = "44";
          keyObject.lastRequest = 'POST:submit';
          params.hash = 'POST:maximize';
        });

        testSuccessAndFailure('Maximize window', params, keyObject, value, statusCode, state);
      });
      describe('Fetch cookie', function(){
        var value = {"message":"An unknown server-side error occurred while processing the command.", "error": "unknown error"};
        var statusCode = 13;
        var state = 'error';

        beforeEach(function() {
          keyObject.browser = "chrome";
          keyObject.browser_version = "44";
          keyObject.lastRequest = 'POST:submit';
          params.hash = 'GET:cookie';
        });

        testSuccessAndFailure('Fetch cookie', params, keyObject, value, statusCode, state);
      });
      describe('Get window handle', function(){
        var value = {"message":"An unknown server-side error occurred while processing the command.", "error": "unknown error"};
        var statusCode = 13;
        var state = 'error';

        beforeEach(function() {
          keyObject.browser = "chrome";
          keyObject.browser_version = "44";
          keyObject.lastRequest = 'POST:submit';
          params.hash = 'GET:window_handle';
        });

        testSuccessAndFailure('Get window handle', params, keyObject, value, statusCode, state);
      });
      describe('Get name', function(){
        var value = {"message":"An unknown server-side error occurred while processing the command.", "error": "unknown error"};
        var statusCode = 13;
        var state = 'error';

        beforeEach(function() {
          keyObject.browser = "chrome";
          keyObject.browser_version = "44";
          keyObject.lastRequest = 'POST:submit';
          params.hash = 'GET:name';
        });

        testSuccessAndFailure('Get name', params, keyObject, value, statusCode, state);
      });
      describe('Get size', function(){
        var value = {"message":"An unknown server-side error occurred while processing the command.", "error": "unknown error"};
        var statusCode = 13;
        var state = 'error';

        beforeEach(function() {
          keyObject.browser = "chrome";
          keyObject.browser_version = "44";
          keyObject.lastRequest = 'POST:submit';
          params.hash = 'POST:size';
        });

        testSuccessAndFailure('Get size', params, keyObject, value, statusCode, state);
      });
      describe('Set timeouts', function(){
        var value = {"message":"An unknown server-side error occurred while processing the command.", "error": "unknown error"};
        var statusCode = 13;
        var state = 'error';

        beforeEach(function() {
          keyObject.browser = "chrome";
          keyObject.browser_version = "44";
          keyObject.lastRequest = 'POST:submit';
          params.hash = 'POST:timeouts';
        });

        testSuccessAndFailure('Set timeouts', params, keyObject, value, statusCode, state);
      });
    })
    describe('Other commands', function() {
      describe('IE 10 script execute', function() {
        var value = [];
        var statusCode = 0;
        var state = 'success';

        beforeEach(function() {
          keyObject.browser = "internet explorer";
          keyObject.browser_version = "10";
          keyObject.lastRequest = 'POST:execute';
          params.hash = 'POST:elements';
        });

        testSuccessAndFailure('IE 10 script execute', params, keyObject, value, statusCode, state);
      });
      describe('Firefox script execute', function() {
        var value = [];
        var statusCode = 0;
        var state = 'success';

        beforeEach(function() {
          keyObject.browser = "firefox";
          keyObject.browser_version = "40";
          keyObject.lastRequest = 'POST:accept_alert';
          params.hash = 'POST:elements';
        });

        testSuccessAndFailure('Firefox script execute', params, keyObject, value, statusCode, state);
      });
      describe('Accepts alert', function() {
        var value = {"message":"An element could not be located on the page using the given search parameters.", "error": "no such element"};
        var statusCode = 7;
        var state = 'error';

        beforeEach(function() {
          keyObject.browser = "chrome";
          keyObject.browser_version = "44";
          keyObject.lastRequest = 'POST:accept_alert';
          params.hash = 'POST:element';
        });

        testSuccessAndFailure('Accepts Alert', params, keyObject, value, statusCode, state);
      });
      describe('Gets title', function() {
        var value = null;
        var statusCode = 0;
        var state = 'success';

        beforeEach(function() {
          keyObject.browser = "internet explorer";
          keyObject.browser_version = "10";
          keyObject.lastRequest = 'POST:execute';
          params.hash = 'GET:title';
        });

        testSuccessAndFailure('Gets title', params, keyObject, value, statusCode, state);
      });
      describe('Finds element', function() {
        var value = {"message":"An element could not be located on the page using the given search parameters.", "error": "no such element"};
        var statusCode = 7;
        var state = 'error';

        beforeEach(function() {
          keyObject.browser = "internet explorer";
          keyObject.browser_version = '10';
          keyObject.lastRequest = "POST:execute";
          params.hash = "POST:element";
        });

        testSuccessAndFailure('Finds element', params, keyObject, value, statusCode, state);
      });
    });

    describe('iOS|tvOS Post Window Delete Commands', () => {
      describe('Any Command', function () {
        var value = [];
        var statusCode = 0;
        var state = 'success';

        beforeEach(function () {
          keyObject.realMobile = "true";
          keyObject.lastRequest = 'DELETE:window';
          params.hash = 'POST:click';
        });
        for (let os of ['ios', 'tvos']){
          keyObject.os = os;
          keyObject.device = os === 'ios' ? "iPhone 11" : "Apple TV 4K";
          keyObject.os_version = os === 'ios' ? "13.0" : "15.6";
          for (let appiumVersion of constants.preventPostWindowDeleteCommandsIOS) {
            keyObject.appium_version = appiumVersion;
            testPostWindowDeleteCommandsIOS(`Prevents sending the command to Appium : ${appiumVersion}, OS: ${os}`, params, keyObject, value, statusCode, state);
          }
        }
      });
    });
  });
  describe('activeElementCheck', function() {
    var keyObject, params;

    beforeEach(function() {
      keyObject = helper.getKeyObject();
      params = helper.getParamsObject();
      sinon.stub(hub, 'processResponse');
      sinon.stub(origHelper, 'getDate');
      sinon.stub(supporting, 'checkActiveElementOnTerminal');
    });

    it('returns false if activeElement is not checked', function() {
      params.request.method = 'GET';
      bridge.activeElementCheck(keyObject, params).should.be.false;
      params.request = helper.getRequestsObject();

      params.request.url = 'google.com';
      bridge.activeElementCheck(keyObject, params).should.be.false;
      params.request = helper.getRequestsObject();

      keyObject.browser = 'chrome';
      bridge.activeElementCheck(keyObject, params).should.be.false;
      keyObject = helper.getKeyObject();

      params.request.checkedActive = true;
      bridge.activeElementCheck(keyObject, params).should.be.false;
      params.request = helper.getRequestsObject();
    });
    describe('IE 10', function() {
      it('On success callback', function() {
        var date = 'Today\'s Date',
          data = { 'key': 'value' },
          output = 'output',
          processResponseString = 'processResponse',
          successObject = {
            data: JSON.stringify(data),
            output: output,
            remoteSessionID: keyObject.key,
            clientSessionID: keyObject.rails_session_id,
            index_counter: params.index_counter,
            callbacks: params.callbacks,
            hostname: params.hostname,
            originalUrl: params.originalUrl
          };
        keyObject.browser = "internet explorer";
        keyObject.browser_version = "10";

        origHelper.getDate.returns(date);
        hub.processResponse.returns(processResponseString);
        supporting.checkActiveElementOnTerminal.returns(true);
        supporting.checkActiveElementOnTerminal.callsArgWith(1, data, output);

        bridge.activeElementCheck(keyObject, params).should.be.true;

        params.request.log_date.should.equal(date);
        params.request.log_data.should.equal((params.request.log_data || ''));
        supporting.checkActiveElementOnTerminal.called.should.be.true;
        supporting.checkActiveElementOnTerminal.calledWith(keyObject).should.be.true;

        hub.processResponse.called.should.be.true;
        hub.processResponse.calledWith(params.request, params.response, keyObject, successObject).should.be.true;
        params.request.checkedActive.should.be.true;
      });
      it('On error callback', function() {
        var date = 'Today\'s Date',
          err = { 'key': 'value' },
          output = 'output',
          processResponseString = 'processResponse',
          errorObject = {
            data: '{"state":"success","sessionId":"'+keyObject.rails_session_id+'","value":{},"class":"org.openqa.selenium.remote.Response","status":0}',
            output: output,
            remoteSessionID: keyObject.key,
            clientSessionID: keyObject.rails_session_id,
            index_counter: params.index_counter,
            callbacks: params.callbacks,
            hostname: params.hostname,
            originalUrl: params.originalUrl
          };
        keyObject.browser = "internet explorer";
        keyObject.browser_version = "10";

        origHelper.getDate.returns(date);
        hub.processResponse.returns(processResponseString);
        supporting.checkActiveElementOnTerminal.returns(true);
        supporting.checkActiveElementOnTerminal.callsArgWith(2, err, output);

        bridge.activeElementCheck(keyObject, params).should.be.true;

        params.request.log_date.should.equal(date);
        params.request.log_data.should.equal((params.request.log_data || ''));
        supporting.checkActiveElementOnTerminal.called.should.be.true;
        supporting.checkActiveElementOnTerminal.calledWith(keyObject).should.be.true;

        hub.processResponse.called.should.be.true;
        hub.processResponse.calledWith(params.request, params.response, keyObject, errorObject).should.be.true;
        params.request.checkedActive.should.be.false;
      });
    });
    describe('IE 11', function() {
      it('On success callback', function() {
        var date = 'Today\'s Date',
          data = { 'key': 'value' },
          output = 'output',
          processResponseString = 'processResponse',
          successObject = {
            data: JSON.stringify(data),
            output: output,
            remoteSessionID: keyObject.key,
            clientSessionID: keyObject.rails_session_id,
            index_counter: params.index_counter,
            callbacks: params.callbacks,
            hostname: params.hostname,
            originalUrl: params.originalUrl
          };
        keyObject.browser = "internet explorer";
        keyObject.browser_version = "11";

        origHelper.getDate.returns(date);
        hub.processResponse.returns(processResponseString);
        supporting.checkActiveElementOnTerminal.returns(true);
        supporting.checkActiveElementOnTerminal.callsArgWith(1, data, output);

        bridge.activeElementCheck(keyObject, params).should.be.true;

        params.request.log_date.should.equal(date);
        params.request.log_data.should.equal((params.request.log_data || ''));
        supporting.checkActiveElementOnTerminal.called.should.be.true;
        supporting.checkActiveElementOnTerminal.calledWith(keyObject).should.be.true;

        hub.processResponse.called.should.be.true;
        hub.processResponse.calledWith(params.request, params.response, keyObject, successObject).should.be.true;
        params.request.checkedActive.should.be.true;
      });
      it('On error callback', function() {
        var date = 'Today\'s Date',
          err = { 'key': 'value' },
          output = 'output',
          processResponseString = 'processResponse',
          errorObject = {
            data: '{"state":"success","sessionId":"'+keyObject.rails_session_id+'","value":{},"class":"org.openqa.selenium.remote.Response","status":0}',
            output: output,
            remoteSessionID: keyObject.key,
            clientSessionID: keyObject.rails_session_id,
            index_counter: params.index_counter,
            callbacks: params.callbacks,
            hostname: params.hostname,
            originalUrl: params.originalUrl
          };
        keyObject.browser = "internet explorer";
        keyObject.browser_version = "11";

        origHelper.getDate.returns(date);
        hub.processResponse.returns(processResponseString);
        supporting.checkActiveElementOnTerminal.returns(true);
        supporting.checkActiveElementOnTerminal.callsArgWith(2, err, output);

        bridge.activeElementCheck(keyObject, params).should.be.true;

        params.request.log_date.should.equal(date);
        params.request.log_data.should.equal((params.request.log_data || ''));
        supporting.checkActiveElementOnTerminal.called.should.be.true;
        supporting.checkActiveElementOnTerminal.calledWith(keyObject).should.be.true;

        hub.processResponse.called.should.be.true;
        hub.processResponse.calledWith(params.request, params.response, keyObject, errorObject).should.be.true;
        params.request.checkedActive.should.be.false;
      });
    });
    describe('FF 13', function() {
      it('On success callback', function() {
        var date = 'Today\'s Date',
          data = { 'key': 'value' },
          output = 'output',
          processResponseString = 'processResponse',
          successObject = {
            data: JSON.stringify(data),
            output: output,
            remoteSessionID: keyObject.key,
            clientSessionID: keyObject.rails_session_id,
            index_counter: params.index_counter,
            callbacks: params.callbacks,
            hostname: params.hostname,
            originalUrl: params.originalUrl
          };
        keyObject.browser = "firefox";
        keyObject.browser_version = "13";

        origHelper.getDate.returns(date);
        hub.processResponse.returns(processResponseString);
        supporting.checkActiveElementOnTerminal.returns(true);
        supporting.checkActiveElementOnTerminal.callsArgWith(1, data, output);

        bridge.activeElementCheck(keyObject, params).should.be.true;

        params.request.log_date.should.equal(date);
        params.request.log_data.should.equal((params.request.log_data || ''));
        supporting.checkActiveElementOnTerminal.called.should.be.true;
        supporting.checkActiveElementOnTerminal.calledWith(keyObject).should.be.true;

        hub.processResponse.called.should.be.true;
        hub.processResponse.calledWith(params.request, params.response, keyObject, successObject).should.be.true;
        params.request.checkedActive.should.be.true;
      });
      it('On error callback', function() {
        var date = 'Today\'s Date',
          err = { 'key': 'value' },
          output = 'output',
          processResponseString = 'processResponse',
          errorObject = {
            data: '{"state":"success","sessionId":"'+keyObject.rails_session_id+'","value":{},"class":"org.openqa.selenium.remote.Response","status":0}',
            output: output,
            remoteSessionID: keyObject.key,
            clientSessionID: keyObject.rails_session_id,
            index_counter: params.index_counter,
            callbacks: params.callbacks,
            hostname: params.hostname,
            originalUrl: params.originalUrl
          };
        keyObject.browser = "firefox";
        keyObject.browser_version = "13";

        origHelper.getDate.returns(date);
        hub.processResponse.returns(processResponseString);
        supporting.checkActiveElementOnTerminal.returns(true);
        supporting.checkActiveElementOnTerminal.callsArgWith(2, err, output);

        bridge.activeElementCheck(keyObject, params).should.be.true;

        params.request.log_date.should.equal(date);
        params.request.log_data.should.equal((params.request.log_data || ''));
        supporting.checkActiveElementOnTerminal.called.should.be.true;
        supporting.checkActiveElementOnTerminal.calledWith(keyObject).should.be.true;

        hub.processResponse.called.should.be.true;
        hub.processResponse.calledWith(params.request, params.response, keyObject, errorObject).should.be.true;
        params.request.checkedActive.should.be.false;
      });
    });

    afterEach(function() {
      hub.processResponse.restore();
      origHelper.getDate.restore();
      supporting.checkActiveElementOnTerminal.restore();
    });
  });

  describe('edge18FileUpload', function() {
    var dateObject,
        replacedSessionId = 'replaced';

    beforeEach(function() {
      dateObject = Date.parse('01/01/2001');
      params = helper.getEdgeFileObject();
      keyObject = params.keyObject;
      constants['global_registry'][keyObject.rails_session_id] = {
        secondary_state: constants.secondary_states.SUCCESS
      };
      sinon.stub(Date, 'now').returns(dateObject);
    });

    it('Test file upload on edge 18 after get url', function() {
      params = helper.getEdgeFileObject();
      params.keyObject.lastRequest = "POST:file";
      params.keyObject.browser = "MicrosoftEdge";
      params.keyObject.browser_version = "43.0";
      const data = JSON.parse(params.requestData);
      delete data.value;
      bridge.getRequestData(params.requestData, params).trim().should.equal(JSON.stringify(data));
    });

    afterEach(function() {
      delete constants.global_registry[keyObject.rails_session_id]
      Date.now.restore();
    });
  });

  describe('getRequestData', function() {
    var dateObject,
      replacedSessionId = 'replaced';

    beforeEach(function() {
      dateObject = Date.parse('01/01/2001');
      keyObject = helper.getKeyObject();
      params = helper.getParamsObject();
      params.keyObject = keyObject;
      params.hash = 'POST:timeouts';
      constants['global_registry'][keyObject.rails_session_id] = {
        secondary_state: constants.secondary_states.SUCCESS
      };
      sinon.stub(Date, 'now').returns(dateObject);
      sinon.stub(hub, 'replaceSessionID').returns(replacedSessionId);
    });
    it('Deletes session ID', function() {
      var value = JSON.parse(params.requestData);
      delete value['sessionId'];
      bridge.getRequestData(params.requestData, params).trim().should.equal(JSON.stringify(value));
    });

    it('Should not json parse stringify', function() {
      const s = sinon.stub();
      params.requestData = s;
      params.hash = 'POST:file';
      params.non_pipe_url = false;
      assert(bridge.getRequestData(params.requestData, params) === s);
    });

    it('Changes the time value if it exceeds limit', function() {
      var data = JSON.parse(params.requestData),
        value = JSON.parse(params.requestData);
      data["ms"] = constants.NODE_DIED_IN + 99999;
      params.requestData = JSON.stringify(data);

      delete value['sessionId'];
      value["ms"] = constants.NODE_DIED_IN - 10000;

      bridge.getRequestData(params.requestData, params).trim().should.equal(JSON.stringify(value));
    });

    it('Change filename for file upload or send keys', function() {
      keyObject.lastRequest = "POST:file";
      bridge.getRequestData(params.requestData, params).trim().should.equal(replacedSessionId);

      var data = JSON.parse(params.requestData);
      data["value"] = "upload";
      params.requestData = JSON.stringify(data);
      keyObject.lastRequest = "something else";
      bridge.getRequestData(params.requestData, params).trim().should.equal(replacedSessionId);
    });
    it('Padding with extra spaces', function() {
      var data = JSON.parse(params.requestData),
        value = JSON.parse(params.requestData);
      data["ms"] = constants.NODE_DIED_IN + 99999;
      params.requestData = JSON.stringify(data);

      delete value['sessionId'];
      value["ms"] = constants.NODE_DIED_IN - 10000;

      var stringValue = JSON.stringify(value);
      var newLength = Buffer.byteLength(stringValue, 'utf8');
      var originalLength = Buffer.byteLength(params.requestData, 'utf8');
      for(var i = 0; i < (originalLength - newLength); i++) {
        stringValue += ' ';
      }

      bridge.getRequestData(params.requestData, params).should.equal(stringValue);
    });
    afterEach(function() {
      delete constants.global_registry[keyObject.rails_session_id]
      Date.now.restore();
      hub.replaceSessionID.restore();
    });
  });

  describe('loadRequestData', function() {
    let keyObject;
    let params;
    let promiseStub;

    beforeEach(() => {
      keyObject = helper.getKeyObject();
      params = helper.getParamsObject();
      params.attempt = 1;
      params.request.url = '/url';
      constants['global_registry'][keyObject.rails_session_id] = {};
      sinon.stub(HubLogger, 'tempExceptionLogger');
      sinon.stub(HubLogger, 'miscLogger');
      promiseStub = sinon.stub().returns({
        catch: sinon.stub().callsArgWith(0, 'err')
      });
      sinon.stub(requestlib, 'readRequest').returns({
        then: promiseStub
      });
    });

    afterEach(() => {
      HubLogger.tempExceptionLogger.restore();
      HubLogger.miscLogger.restore();
      requestlib.readRequest.restore();
    });

    xit('Sets the hash and resolves to the request data for attempt 1');

    xit('For attempt 2, no read from request but reload data');

    it('Sets the hash to `POST:safari-11-certs` for macbsr', (done) => {
      keyObject.browser = 'Safari';
      keyObject.os = 'macbsr';
      keyObject.certs = true;
      promiseStub.callsArgWith(0, '');
      bridge.loadRequestData(keyObject, params).then((req_data) => {
        assert.equal(params.hash, 'POST:safari-11-certs');
        done();
      });
    });

    it('Sets the hash to `POST:url-data` for data protocol', (done) => {
      promiseStub.callsArgWith(0, '{"url": "data:example.com"}');
      bridge.loadRequestData(keyObject, params).then((req_data) => {
        assert.equal(params.hash, 'POST:url-data');
        done();
      });
    });

    it('Sets the hash to `POST:url-xml` for url ending with xml', (done) => {
      promiseStub.callsArgWith(0, '{"url": "http://example.com/file.xml"}');
      bridge.loadRequestData(keyObject, params).then((req_data) => {
        assert.equal(params.hash, 'POST:url-xml');
        done();
      });
    });

    it('Sets the hash to `POST:url-about-blank` for blank url', (done) => {
      promiseStub.callsArgWith(0, '{"url": "about:blank"}');
      bridge.loadRequestData(keyObject, params).then((req_data) => {
        assert.equal(params.hash, 'POST:url-about-blank');
        done();
      });
    });

    it('should call triggerLighthouse if keyobject is set', (done) => {
      keyObject.lighthouseAutomate = { report_limit: 5 };
      promiseStub.callsArgWith(0, '{"url": "about:blank"}');
      bridge.loadRequestData(keyObject, params).then((req_data) => {
        assert.equal(params.hash, 'POST:url-about-blank');
        done();
      });
    });
    
    context('Modify implicit timeout for AI if set via LD', () => {
      let ogReqUrl;
      let ogHash;
      beforeEach(() => {
        ogReqUrl = params.request.url;
        ogHash = params.hash;
        params.request.url = '/timeouts';
        params.hash = 'POST:timeouts';
      });

      afterEach(() => {
        params.request.url = ogReqUrl;
        params.hash = ogHash;
      });

      it('should not modify implicit wait for implicit timeouts and aiSessionDetails if LD value less than user timeout', (done) => {
        keyObject.aiSessionDetails = { implicit_delay_enabled: 'true', find_element_timeout: 2 };
        promiseStub.callsArgWith(0, '{"implicit":5000}');
        bridge.loadRequestData(keyObject, params).then((req_data) => {
          assert.equal(params.hash, 'POST:timeouts');
          assert.equal(req_data, '{"implicit":5000}');
          done();
        });
      });

      it('should modify implicit wait for implicit timeouts and aiSessionDetails if LD value more than user timeout', (done) => {
        keyObject.aiSessionDetails = { implicit_delay_enabled: 'true', find_element_timeout: 10 };
        promiseStub.callsArgWith(0, '{"implicit":5000}');
        bridge.loadRequestData(keyObject, params).then((req_data) => {
          assert.equal(params.hash, 'POST:timeouts');
          assert.equal(req_data, '{"implicit":10000}');
          done();
        });
      });

      it('should not modify implicit wait for implicit timeouts and aiSessionDetails if LD value not set', (done) => {
        keyObject.aiSessionDetails = { implicit_delay_enabled: 'true' };
        promiseStub.callsArgWith(0, '{"implicit":5000}');
        bridge.loadRequestData(keyObject, params).then((req_data) => {
          assert.equal(params.hash, 'POST:timeouts');
          assert.equal(req_data, '{"implicit":5000}');
          done();
        });
      });
    });
  });

  describe('requestError', function() {
    var keyObject, params;

    beforeEach(function() {
      keyObject = helper.getKeyObject();
      params = helper.getParamsObject();
      constants['global_registry'][keyObject.rails_session_id] = {
        secondary_state: constants.secondary_states.SUCCESS
      };
    });

    it('Session not found if invalid session ID', function() {
      sinon.stub(HubLogger, 'tempExceptionLogger');
      sinon.stub(hub, 'sessionNotFound');
      delete constants['global_registry'][keyObject.rails_session_id]

      bridge.requestError('ERROR', keyObject, params);

      hub.sessionNotFound.called.should.be.true;
      hub.sessionNotFound.calledWith(params.response, keyObject).should.be.true;

      hub.sessionNotFound.restore();
      HubLogger.tempExceptionLogger.restore();
    });
    it('retryData is not undefined', function() {
      var retryData = {'key':'value' };
      params.retryData = retryData;
      var data = {
        data: params.retryData,
        output: params.retryOutput,
        remoteSessionID: params.remoteSessionID,
        clientSessionID: params.clientSessionID,
        index_counter: params.index_counter,
        callbacks: params.callbacks,
        hostname: params.hostname,
        originalUrl: params.originalUrl
      };

      sinon.stub(HubLogger, 'miscLogger');
      sinon.stub(hub, 'processResponse').returns('processResponse');

      bridge.requestError('ERROR', keyObject, params).should.equal('processResponse');

      HubLogger.miscLogger.called.should.be.true;
      hub.processResponse.called.should.be.true;
      hub.processResponse.calledWith(params.request, params.response, keyObject, data).should.be.true;

      HubLogger.miscLogger.restore();
      hub.processResponse.restore();
    });
    describe('ECONNRESET/ETIMEDOUT in the request', function() {
      it('Delete Request', function() {
        var data = {
          data: '{"state":"success","sessionId":"'+params.clientSessionID+'","value":null,"class":"org.openqa.selenium.remote.Response","status":0}',
          output: {statusCode: 200, headers: constants.CHUNKED_HEADER},
          remoteSessionID: params.remoteSessionID,
          clientSessionID: params.clientSessionID,
          index_counter: params.index_counter,
          callbacks: params.callbacks,
          hostname: params.hostname,
          originalUrl: params.originalUrl
        };
        delete params['retryData'];
        params.attempt = 2;
        params.hash = 'DELETE:'+params.clientSessionID;

        sinon.stub(hub, 'processResponse');

        bridge.requestError({ 'code': 'ECONNRESET' }, keyObject, params);

        hub.processResponse.called.should.be.true;
        hub.processResponse.calledWith(params.request, params.response, keyObject, data).should.be.true;

        hub.processResponse.restore();
      });
      it('SO TIMEOUT', function() {
        var data = {
          remoteSessionID: params.remoteSessionID,
          clientSessionID: params.clientSessionID,
          index_counter: params.index_counter,
          callbacks: params.callbacks,
          hostname: params.hostname,
          originalUrl: params.originalUrl
        };
        delete params['retryData'];
        params.attempt = 3;

        sinon.stub(hub, 'checkPossibleToProceed').callsArg(4);
        sinon.stub(hub, 'checkFailureReason');

        bridge.requestError({ 'code': 'ECONNRESET' }, keyObject, params);

        hub.checkPossibleToProceed.called.should.be.true;
        hub.checkPossibleToProceed.calledWith(keyObject, params.request, params.response, data).should.be.true;
        hub.checkFailureReason.called.should.be.true;
        hub.checkFailureReason.calledWith(keyObject, params.request, params.response, params.attempt, params.index_counter, ERROR.HUB_TIMEOUT).should.be.true;

        hub.checkFailureReason.restore();
        hub.checkPossibleToProceed.restore();
      });
      describe('Attempts < 3', function() {
        it('Network Down', function(){
          var data = {
            remoteSessionID: params.remoteSessionID,
            clientSessionID: params.clientSessionID,
            index_counter: params.index_counter,
            callbacks: params.callbacks,
            hostname: params.hostname,
            originalUrl: params.originalUrl
          };
          delete params['retryData'];
          params.attempt = 2;

          sinon.stub(debug, 'isPortOpen').callsArgWith(2, false);
          sinon.stub(hub, 'checkFailureReason');
          sinon.stub(HubLogger, 'miscLogger');

          bridge.requestError({ 'code': 'ECONNRESET' }, keyObject, params);

          HubLogger.miscLogger.called.should.be.true;
          debug.isPortOpen.called.should.be.true;
          debug.isPortOpen.calledWith(keyObject.name, keyObject.port).should.be.true;
          hub.checkFailureReason.called.should.be.true;
          hub.checkFailureReason.calledWith(keyObject, params.request, params.response, params.attempt, params.index_counter, ERROR.NETWORK_DOWN).should.be.true;

          hub.checkFailureReason.restore();
          debug.isPortOpen.restore();
          HubLogger.miscLogger.restore();
        });
        xit('5555 Down', function() {
          var data = {
            remoteSessionID: params.remoteSessionID,
            clientSessionID: params.clientSessionID,
            index_counter: params.index_counter,
            callbacks: params.callbacks,
            hostname: params.hostname,
            originalUrl: params.originalUrl
          };
          delete params['retryData'];
          params.attempt = 2;

          var bool = sinon.stub();
          bool.onFirstCall().returns(true);
          bool.onSecondCall().returns(false);
          sinon.stub(debug, 'isPortOpen').callsArgWith(2, bool());
          sinon.stub(hub, 'checkFailureReason');
          sinon.stub(HubLogger, 'miscLogger');

          bridge.requestError({ 'code': 'ECONNRESET' }, keyObject, params);

          HubLogger.miscLogger.called.should.be.true;
          debug.isPortOpen.called.should.be.true;
          debug.isPortOpen.calledWith(keyObject.name, keyObject.port).should.be.true;
          hub.checkFailureReason.called.should.be.true;
          hub.checkFailureReason.calledWith(keyObject, params.request, params.response, params.attempt, params.index_counter, ERROR.CONN_FAILED_TWICE).should.be.true;

          hub.checkFailureReason.restore();
          debug.isPortOpen.restore();
          HubLogger.miscLogger.restore();
        });
        it('Connection Failed Twice', function() {
          var data = {
            remoteSessionID: params.remoteSessionID,
            clientSessionID: params.clientSessionID,
            index_counter: params.index_counter,
            callbacks: params.callbacks,
            hostname: params.hostname,
            originalUrl: params.originalUrl
          };
          delete params['retryData'];
          params.attempt = 2;

          sinon.stub(debug, 'isPortOpen').callsArgWith(2, true);
          sinon.stub(hub, 'checkFailureReason');
          sinon.stub(HubLogger, 'miscLogger');

          bridge.requestError({ 'code': 'ECONNRESET' }, keyObject, params);

          HubLogger.miscLogger.called.should.be.true;
          debug.isPortOpen.called.should.be.true;
          debug.isPortOpen.calledWith(keyObject.name, keyObject.port).should.be.true;
          hub.checkFailureReason.called.should.be.true;
          hub.checkFailureReason.calledWith(keyObject, params.request, params.response, params.attempt, params.index_counter, ERROR.CONN_FAILED_TWICE).should.be.true;

          hub.checkFailureReason.restore();
          debug.isPortOpen.restore();
          HubLogger.miscLogger.restore();
        });
      });
    });
    it('Retries if error has happened once', function() {
      const keyObj2 = keyObject;
      keyObj2.port = 4022;
      delete params['retryData'];
      params['attempt'] = 0;
      var date = new Date(2001, 1, 1);
      var clock = sinon.useFakeTimers(date.getTime());

      sinon.stub(HubLogger, 'seleniumStats');
      sinon.stub(hub, 'createBridgeClientAndNode');

      bridge.requestError({ 'code': 'ECONNRESET', 'port': 4555 }, keyObject, params);
      clock.tick(1000);

      HubLogger.seleniumStats.called.should.be.true;
      sinon.assert.calledWith(HubLogger.seleniumStats, "selenium-node-failure", keyObject, "Connection failed once", `port=4555,err=${JSON.stringify({ 'code': 'ECONNRESET', 'port': 4555 })}`, keyObject.rails_session_id || '', "nodeError")

      hub.createBridgeClientAndNode.called.should.be.true;
      hub.createBridgeClientAndNode.calledWith(keyObject, params.request, params.response, params.callbacks, params.attempt+1, params.index_counter, params.req_data).should.be.true;

      clock.restore();
      HubLogger.seleniumStats.restore();
      hub.createBridgeClientAndNode.restore();
    });
    it('JSON parse error', function() {
      var data = {
        data: '{"state":"success","sessionId":"'+params.clientSessionID+'","value":null,"class":"org.openqa.selenium.remote.Response","status":0}',
        output: {statusCode: 200, headers: constants.CHUNKED_HEADER},
        remoteSessionID: params.remoteSessionID,
        clientSessionID: params.clientSessionID,
        index_counter: params.index_counter,
        callbacks: params.callbacks,
        hostname: params.hostname,
        originalUrl: params.originalUrl
      };

      sinon.stub(HubLogger, 'miscLogger');
      sinon.stub(hub, 'processResponse').returns('processResponse');

      bridge.requestError('Error: Parse Error', keyObject, params).should.equal('processResponse');
      HubLogger.miscLogger.called.should.be.true;

      hub.processResponse.called.should.be.true;
      hub.processResponse.calledWith(params.request, params.response, keyObject, data).should.be.true;

      HubLogger.miscLogger.restore();
      hub.processResponse.restore();
    });
    it('Unknown failure', function() {
      var data = {
        data: '{"state":"success","sessionId":"'+params.clientSessionID+'","value":null,"class":"org.openqa.selenium.remote.Response","status":0}',
        output: {statusCode: 200, headers: constants.CHUNKED_HEADER},
        remoteSessionID: params.remoteSessionID,
        clientSessionID: params.clientSessionID,
        index_counter: params.index_counter,
        callbacks: params.callbacks,
        hostname: params.hostname,
        originalUrl: params.originalUrl
      };

      sinon.stub(hub, 'checkFailureReason').returns('processResponse');

      bridge.requestError('Error: Unknown', keyObject, params);

      hub.checkFailureReason.called.should.be.true;
      hub.checkFailureReason.calledWith(keyObject, params.request, params.response, params.attempt, params.index_counter, ERROR.OTHER).should.be.true;

      hub.checkFailureReason.restore();
    });

    afterEach(function() {
      delete constants.global_registry[keyObject.rails_session_id]
    });
  });
  describe('checkDebugLogging', () => {
    it('should call cb if logging is disabled', (done) => {
      const keyObject = {};
      bridge.checkDebugLogging('', '', keyObject, () => {
        done();
      });
    });

    it('get debug logs should be called when logging is enabled', (done) => {
      const keyObject = {
        logging: 'server',
        browser: 'firefox',
      };
      const x = sinon.spy(bridge, 'getDebugLogs');
      bridge.checkDebugLogging('', '', keyObject, () => {});
      assert(x.calledOnce === true);
      bridge.getDebugLogs.restore();
      done();
    });
  });

  // Adding for coverage
  describe('getDebugLogs', () => {
    it('should call cb if logtypes is empty', (done) => {
      const keyObject = {};
      bridge.getDebugLogs(keyObject,[], () => {
        done();
      });
    });

    it('get debug logs should be called when logtypes is not empty', (done) => {
      const keyObject = {
        logging: 'server',
        browser: 'firefox',
      };
      var log_types = [];
      log_types.push('driver');
      log_types.push('client');
      bridge.getDebugLogs(keyObject,log_types, () => {
        done();
      });
    });
  });

  describe('autoItSendKeys', () => {
    const keyObject = helper.getKeyObject();
    const params = { clientSessionID: 'session_id1' };
    const options = { timeout: 20 };
    const text = 'some_text *^#$';
    it('should send keys if text is empty', (done) => {
      keyObject.autoitSendKeys = '5';
      const requestLibCall = sinon.stub(requestlib, 'call').returns(Promise.resolve({ data: 'some_data' }));
      sinon.stub(HubLogger, 'miscLogger');
      bridge.autoItSendKeys(keyObject,params,options,text,() => {
        sinon.assert.calledOnce(requestLibCall);
        done();
      },() => {});
      requestLibCall.restore();
      HubLogger.miscLogger.restore();
    });
    it('should not send keys if text is empty',(done) => {
      keyObject.autoitSendKeys = '5';
      const requestLibCall = sinon.stub(requestlib, 'call').returns(Promise.resolve({ data: 'some_data' }));
      sinon.stub(HubLogger, 'miscLogger');
      bridge.autoItSendKeys(keyObject,params,options,'',() => {
      HubLogger.miscLogger.called.should.be.true;
      done();
      },() => {});
      requestLibCall.restore();
      HubLogger.miscLogger.restore();
    });
  });

  describe('Stop taking screenshots for find_element exceptions', function() {
    var keyObject, params;

    beforeEach(function() {
      keyObject = helper.getKeyObject();
      params = helper.getParamsObject();
      constants['global_registry'][keyObject.rails_session_id] = {
        secondary_state: constants.secondary_states.SUCCESS
      };
      sinon.stub(supporting, 'checkURLStatus');
      sinon.stub(HubLogger, 'tempExceptionLogger');
    });

    it('Debug screenshots when Exception in /element requests and debug is true', function() {
      sinon.stub(origHelper, 'takeScreenshotAndUpload');
      var promiseStub = sinon.stub().returns({
        catch: sinon.stub().callsArgWith(0, 'err')
      });
      sinon.stub(requestlib, 'call').returns({
        then: promiseStub
      });
      promiseStub.callsArgWith(0, {
        data: 'data'
      });
      params.request.url = 'session/21e3/element';
      params.callbacks = {'callbackEnd': function(){}}
      keyObject.debug = true;
      keyObject.appTesting = true;
      params.data = '{"key":"value","status":1,"value":{"message":"xxx"}}'
      bridge.sendResponse(keyObject, params);
      origHelper.takeScreenshotAndUpload.called.should.be.true;
    });

    it('No Debug screenshots when Exception in /element requests and debug is false', function() {
      sinon.stub(origHelper, 'takeScreenshotAndUpload');
      var promiseStub = sinon.stub().returns({
        catch: sinon.stub().callsArgWith(0, 'err')
      });
      sinon.stub(requestlib, 'call').returns({
        then: promiseStub
      });
      promiseStub.callsArgWith(0, {
        data: 'data'
      });
      params.request.url = 'session/21e3/element';
      params.callbacks = {'callbackEnd': function(){}}
      keyObject.debug = false;
      keyObject.appTesting = true;
      params.data = '{"key":"value","status":1,"value":{"message":"xxx"}}'
      bridge.sendResponse(keyObject, params);
      origHelper.takeScreenshotAndUpload.called.should.be.false;
    });

    it('Debug screenshots when Exception not in  /element requests and debug is true', function() {
      sinon.stub(origHelper, 'takeScreenshotAndUpload');
      var promiseStub = sinon.stub().returns({
        catch: sinon.stub().callsArgWith(0, 'err')
      });
      sinon.stub(requestlib, 'call').returns({
        then: promiseStub
      });
      promiseStub.callsArgWith(0, {
        data: 'data'
      });
      params.request.url = 'session/21e3/323fewd/click';
      params.callbacks = {'callbackEnd': function(){}}
      keyObject.debug = true;
      keyObject.appTesting = true;
      params.data = '{"key":"value","status":1,"value":{"message":"xxx"}}'
      bridge.sendResponse(keyObject, params);
      origHelper.takeScreenshotAndUpload.called.should.be.true;
    });

    it('Debug screenshots when Exception not in  /element requests and debug is false', function() {
      sinon.stub(origHelper, 'takeScreenshotAndUpload');
      var promiseStub = sinon.stub().returns({
        catch: sinon.stub().callsArgWith(0, 'err')
      });
      sinon.stub(requestlib, 'call').returns({
        then: promiseStub
      });
      promiseStub.callsArgWith(0, {
        data: 'data'
      });
      params.request.url = 'session/21e3/esfs/dfs';
      params.callbacks = {'callbackEnd': function(){}}
      keyObject.debug = false;
      keyObject.appTesting = true;
      params.data = '{"key":"value","status":1,"value":{"message":"xxx"}}'
      bridge.sendResponse(keyObject, params);
      origHelper.takeScreenshotAndUpload.called.should.be.true;
    });

    afterEach(function() {
      delete constants.global_registry[keyObject.rails_session_id]
      HubLogger.tempExceptionLogger.restore();
      supporting.checkURLStatus.restore();
      requestlib.call.restore();
      origHelper.takeScreenshotAndUpload.restore();
    });

  })

  describe('sendResponse', function() {
    var keyObject, params;

    beforeEach(function() {
      keyObject = helper.getKeyObject();
      params = helper.getParamsObject();
      constants['global_registry'][keyObject.rails_session_id] = {
        secondary_state: constants.secondary_states.SUCCESS
      };
      sinon.stub(supporting, 'checkURLStatus');
      sinon.stub(hub, 'processResponse');
      sinon.stub(HubLogger, 'tempExceptionLogger');
    });

    it('Session not found if invalid session ID', function() {
      sinon.stub(hub, 'sessionNotFound');
      delete constants['global_registry'][keyObject.rails_session_id]

      bridge.sendResponse(keyObject, params);

      hub.sessionNotFound.called.should.be.true;
      hub.sessionNotFound.calledWith(params.response, keyObject).should.be.true;

      hub.sessionNotFound.restore();
    });
    it('Terminal request for screenshot in Android', function() {
      var promiseStub = sinon.stub().returns({
        catch: sinon.stub().callsArgWith(0, 'err')
      });
      sinon.stub(requestlib, 'call').returns({
        then: promiseStub
      });
      promiseStub.callsArgWith(0, {
        data: 'data'
      });

      params.request.url = 'google.com/screenshot';
      keyObject.browser = 'android';
      keyObject.deviceName = 'Samsung Galaxy S5-4.4-1080x1920';
      keyObject.deviceOrientation = 'portrait';
      var payload = {
        data: 'data',
        parsed_data: undefined,
        output: params.output,
        remoteSessionID: params.remoteSessionID,
        clientSessionID: params.clientSessionID,
        index_counter: params.index_counter,
        callbacks: params.callbacks,
        hostname: params.hostname,
        originalUrl: params.originalUrl
      };
      bridge.sendResponse(keyObject, params);

      //supporting.checkURLStatus.called.should.be.true;
      //supporting.checkURLStatus.calledWith(JSON.parse(params['requestData']), params.clientSessionID, params.hostname, params.browserstackTunnel, undefined, params.request).should.be.true;
      requestlib.call.called.should.be.true;
      requestlib.call.calledWith({
        method: 'GET',
        hostname: params.rproxyHost,
        port: 45671,
        path: '/custom_screenshot?sessionId=' +params.clientSessionID+'&x=95&y=172&width=498&height=885&orientation=portrait&device=undefined&os=android',
        headers: requestlib.appendBStackHostHeader(params.hostname),
      }).should.be.true;

      promiseStub.called.should.be.true;

      hub.processResponse.called.should.be.true;
      hub.processResponse.calledWith(params.request, params.response, keyObject, payload).should.be.true;
      requestlib.call.restore();
    });
    it('Terminal request for screenshot in iOS 9', function() {
      var promiseStub = sinon.stub().returns({
        catch: sinon.stub().callsArgWith(0, 'err')
      });
      sinon.stub(requestlib, 'call').returns({
        then: promiseStub
      });
      promiseStub.callsArgWith(0, {
        data: 'data'
      });

      params.request.url = 'google.com/screenshot';
      keyObject.browser = 'iPhone';
      keyObject.os = 'macelc';
      keyObject.deviceName = 'iPhone 6S Plus-9.1';
      keyObject.deviceOrientation = 'portrait';
      var payload = {
        data: 'data',
        parsed_data: undefined,
        output: params.output,
        remoteSessionID: params.remoteSessionID,
        clientSessionID: params.clientSessionID,
        index_counter: params.index_counter,
        callbacks: params.callbacks,
        hostname: params.hostname,
        originalUrl: params.originalUrl
      };
      bridge.sendResponse(keyObject, params);

      //supporting.checkURLStatus.called.should.be.true;
      //supporting.checkURLStatus.calledWith(JSON.parse(params['requestData']), params.clientSessionID, params.hostname, params.browserstackTunnel, undefined, params.request).should.be.true;
      requestlib.call.called.should.be.true;
      requestlib.call.calledWith({
        method: 'GET',
        hostname: params.rproxyHost,
        port: 45671,
        path: '/custom_screenshot?sessionId=' + params.clientSessionID + '&x=56&y=54&width=623&height=1106&orientation=portrait&device=undefined&os=ios',
        headers: requestlib.appendBStackHostHeader(params.hostname),
      }).should.be.true;

      promiseStub.called.should.be.true;

      hub.processResponse.called.should.be.true;
      hub.processResponse.calledWith(params.request, params.response, keyObject, payload).should.be.true;
      requestlib.call.restore();
    });
    it('Forwards response data to process response', function() {
      var payload = {
        data: params.data,
        parsed_data: undefined,
        output: params.output,
        remoteSessionID: params.remoteSessionID,
        clientSessionID: params.clientSessionID,
        index_counter: params.index_counter,
        callbacks: params.callbacks,
        hostname: params.hostname,
        originalUrl: params.originalUrl
      };
      bridge.sendResponse(keyObject, params);

      //supporting.checkURLStatus.called.should.be.true;
      //supporting.checkURLStatus.calledWith(JSON.parse(params['requestData']), params.clientSessionID, params.hostname, params.browserstackTunnel, undefined, params.request).should.be.true;
      hub.processResponse.called.should.be.true;
      hub.processResponse.calledWith(params.request, params.response, keyObject, payload).should.be.true;
    });

    afterEach(function() {
      delete constants.global_registry[keyObject.rails_session_id]
      HubLogger.tempExceptionLogger.restore();
      supporting.checkURLStatus.restore();
      hub.processResponse.restore();
    });
  });

  describe('basicAuthMac', () => {
    context("Handle Basic Auth", () => {
      let keyObject;
      let macBrowsers = ["chrome", "firefox", "edge"];
      let macUnsupportedBrowsers = ["safari"];

      beforeEach(() => {
        keyObject = {
          rails_session_id: "random_session_id",
          appTesting: false,
          os: 'macmo',
          rProxyHost: "some_host",
          name: "some_host"
        }
        sinon.stub(HubLogger, "miscLogger");
        sinon.stub(HubLogger, "tempExceptionLogger");
      });

      afterEach(() => {
        HubLogger.miscLogger.restore();
        HubLogger.tempExceptionLogger.restore();
        requestlib.call.restore();
      });

      macBrowsers.forEach((browser) => {
        it (`Works successfully for browser: ${browser}`, () => {
          keyObject['browser'] = browser;
          const operationResult = {
            data: "done"
          }

          sinon.stub(requestlib, "call").returns(operationResult);
          return bridge.basicAuthMac(keyObject, "random_session_id", "oneUser", "onePass").then((res) => {
            res.should.eql(true);
          });
        });
      });

      macBrowsers.forEach((browser) => {
        it(`Throws error if username or password is empty for: ${browser}`, () => {
          keyObject['browser'] = browser;

          sinon.stub(requestlib, "call");
          return bridge.basicAuthMac(keyObject, "random_session_id", "", "")
            .then()
            .catch((err) => {
              assert(err)
            })
        });
      });

      macBrowsers.forEach((browser) => {
        it(`Throws error when no 'done' received in response data for: ${browser}`, () => {
          keyObject['browser'] = browser;
          const operationResult = {
            data: "Some Error"
          }

          sinon.stub(requestlib, "call").returns(operationResult);
          return bridge.basicAuthMac(keyObject, "random_session_id", "some", "value")
            .then()
            .catch((err) => {
              assert(err)
            })
        });
      });

      macBrowsers.forEach((browser) => {
        it(`Throws error when no data received in response for: ${browser}`, () => {
          keyObject['browser'] = browser;
          const operationResult = {
            key: "value"
          }

          sinon.stub(requestlib, "call").returns(operationResult);
          return bridge.basicAuthMac(keyObject, "random_session_id", "some", "value")
            .then()
            .catch((err) => {
              assert(err)
            })
        });
      });

      macUnsupportedBrowsers.forEach((browser) => {
        it(`Throws error when unsupported browser used: ${browser}`, () => {
          keyObject['browser'] = browser;
          const operationResult = {
            data: "value"
          }

          sinon.stub(requestlib, "call").returns(operationResult);
          return bridge.basicAuthMac(keyObject, "random_session_id", "some", "value")
            .then()
            .catch((err) => {
              assert(err)
            })
        });
      });
    });

    context("Dismiss Basic Auth", () => {
      let keyObject;
      let macBrowsers = ["chrome", "firefox", "edge"];
      let macUnsupportedBrowsers = ["safari"];

      beforeEach(() => {
        keyObject = {
          rails_session_id: "random_session_id",
          appTesting: false,
          os: 'macmo',
          rProxyHost: "some_host",
          name: "some_host"
        }
        sinon.stub(HubLogger, "miscLogger");
        sinon.stub(HubLogger, "tempExceptionLogger");
      });

      afterEach(() => {
        HubLogger.miscLogger.restore();
        HubLogger.tempExceptionLogger.restore();
        requestlib.call.restore();
      });

      macBrowsers.forEach((browser) => {
        it(`Works successfully for browser: ${browser}`, () => {
          keyObject['browser'] = browser;
          const operationResult = {
            data: "done"
          }

          sinon.stub(requestlib, "call").returns(operationResult);
          return bridge.basicAuthMac(keyObject, "random_session_id", "oneUser", "onePass", "dismiss").then((res) => {
            res.should.eql(true);
          });
        });
      });

      macBrowsers.forEach((browser) => {
        it(`Does not throw error if username or password is empty for: ${browser}`, () => {
          keyObject['browser'] = browser;
          const operationResult = {
            data: "done"
          }

          sinon.stub(requestlib, "call");
          return bridge.basicAuthMac(keyObject, "random_session_id", "", "", "dismiss")
            .then((res) => {
              res.should.eql(true);
            })
            .catch((err) => {})
        });
      });

      macBrowsers.forEach((browser) => {
        it(`Throws error when no 'done' received in response data for: ${browser}`, () => {
          keyObject['browser'] = browser;
          const operationResult = {
            data: "Some Error"
          }

          sinon.stub(requestlib, "call").returns(operationResult);
          return bridge.basicAuthMac(keyObject, "random_session_id", "some", "value", "dismiss")
            .then()
            .catch((err) => {
              assert(err)
            })
        });
      });

      macBrowsers.forEach((browser) => {
        it(`Throws error when no data received in response for: ${browser}`, () => {
          keyObject['browser'] = browser;
          const operationResult = {
            key: "value"
          }

          sinon.stub(requestlib, "call").returns(operationResult);
          return bridge.basicAuthMac(keyObject, "random_session_id", "some", "value", "dismiss")
            .then()
            .catch((err) => {
              assert(err)
            })
        });
      });

      macUnsupportedBrowsers.forEach((browser) => {
        it(`Throws error when unsupported browser used: ${browser}`, () => {
          keyObject['browser'] = browser;
          const operationResult = {
            data: "value"
          }

          sinon.stub(requestlib, "call").returns(operationResult);
          return bridge.basicAuthMac(keyObject, "random_session_id", "some", "value", "dismiss")
            .then()
            .catch((err) => {
              assert(err)
            })
        });
      });
    });
  });

  describe('setNetworkProfile', () => {
    const keyObject = {
      rails_session_id: 'random-session-id',
      rproxyHost: 'some-reverse-proxy-host',
      name: 'some-terminal-ip',
    };
    const caps = {
      udid: 'some-device-id',
    };

    const browserstackParmas = {
      network_airplane_mode: false,
      network_bw_dwld: '1000',
      network_bw_upld: '700',
      network_latency: '50',
    };

    it('invokes callback if succeeded', (done) => {
      sinon.stub(requestlib, 'call');
      supporting.setNetworkProfile(keyObject, caps, browserstackParmas, done);
      requestlib.call.called.should.be.true;
      requestlib.call.restore();
    });

    it('logs error and invokes callback if failed', (done) => {
      sinon.stub(requestlib, 'call').throws(new Error('Request Failure'));
      sinon.stub(HubLogger, 'miscLogger');
      supporting.setNetworkProfile(keyObject, caps, browserstackParmas, done);
      requestlib.call.called.should.be.true;
      HubLogger.miscLogger.called.should.be.true;
      requestlib.call.restore();
      HubLogger.miscLogger.restore();
    });
  });

  describe('blockAppiumWindowCommands', () => {
    var keyObject, params;

    beforeEach(function() {
      keyObject = helper.getKeyObject();
      params = helper.getParamsObject();
    });

    it('should block appium window command & notify timeout manager to update timeout', (done) => {
      sinon.stub(origHelper, 'timeoutManagerUpdateTimeout');
      bridge.blockAppiumWindowCommands(keyObject, params.response);
      expect(params.response.statusCode).to.equal(400);
      origHelper.timeoutManagerUpdateTimeout.called.should.be.true;
      origHelper.timeoutManagerUpdateTimeout.restore();
      done();
    });
  });
});
