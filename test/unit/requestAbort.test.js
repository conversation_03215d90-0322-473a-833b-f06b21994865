'use strict';

/* eslint-disable no-underscore-dangle */

const sinon = require('sinon');
const { expect } = require('chai');
const rewire = require('rewire');

const rewired = rewire('../../lib/request');
const http = require('http');

describe('Abort hooks for request', () => {
  let map;
  let req;
  const socketId = '1234';

  beforeEach(() => {
    map = new Map();
    sinon.spy(map, 'set');
    sinon.spy(map, 'delete');
    req = new http.IncomingMessage();
  });

  describe('addRequestAbortHook', () => {
    const addRequestAbortHook = rewired.__get__('addRequestAbortHook');

    it('Should not set for undefined map', () => {
      addRequestAbortHook(undefined, req, socketId);
      expect(map.set.notCalled).to.be.true;
    });

    it('Should not set for undefined request', () => {
      addRequestAbortHook(map, undefined, socketId);
      expect(map.set.notCalled).to.be.true;
    });

    it('Should not set for undefined sessionId', () => {
      addRequestAbortHook(map, req, undefined);
      expect(map.set.notCalled).to.be.true;
    });

    it('Should not set if map, request and sessionId are not defined', () => {
      addRequestAbortHook(undefined, undefined, undefined);
      expect(map.set.notCalled).to.be.true;
    });

    it('Should set for valid request and map', () => {
      addRequestAbortHook(map, req, socketId);
      expect(map.set.calledOnce).to.be.true;
      expect(map.get(socketId)).to.be.eql(req);
    });
  });

  describe('removeAbortHook', () => {
    const removeAbortHook = rewired.__get__('removeAbortHook');

    it('Should not remove for undefined map', () => {
      removeAbortHook(undefined, socketId);
      expect(map.delete.notCalled).to.be.true;
    });

    it('Should not remove for undefined sessionId', () => {
      removeAbortHook(map, undefined);
      expect(map.delete.notCalled).to.be.true;
    });

    it('Should not remove if map and sessionId is not defined', () => {
      removeAbortHook(undefined, undefined);
      expect(map.delete.notCalled).to.be.true;
    });

    it('Should remove for valid map and sessionId', () => {
      removeAbortHook(map, socketId);
      expect(map.delete.calledOnce).to.be.true;
    });
  });
});
