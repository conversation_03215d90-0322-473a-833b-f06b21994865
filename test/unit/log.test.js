'use strict';

/* eslint-disable no-underscore-dangle */

const log = require('../../log');
const helper = require('./helper');
const myLock = require('../../semaphore');
const browserstack = require('../../browserstack');
const errorMessageConstants = require('../../errorMessages');
const originalHelper = require('../../helper');
const constants = require('../../constants');
const { assert } = require('chai');
const sinon = require('sinon');
const kafka = require('../../clients/kafka/kafkaClient');
const randomstring = require('randomstring');
const requestlib = require('../../lib/request');
const rewire = require('rewire');

const rewiredLog = rewire('../../log');

const LL = constants.LOG_LEVEL;

describe('log.js', () => {
  it('should add last rawlog to global registry in case before stopping', () => {
    const keyObject = helper.getKeyObject();
    const key = keyObject;
    constants.global_registry[keyObject.rails_session_id] = key;
    const reason = 'SO_TIMEOUT';
    const status = 1;
    const delay = true;
    log.addStopToRawLogs(keyObject, key, reason, status, delay);
    constants.global_registry[keyObject.rails_session_id].lastRawLog.should.equal('SO_TIMEOUT');
    delete constants.global_registry[keyObject.rails_session_id];
  });

  it('should not add last rawlog to global registry if keyobject not present in global registry', () => {
    const keyObject = helper.getKeyObject();
    const key = keyObject;
    constants.global_registry[keyObject.rails_session_id] = key;
    const reason = 'SO_TIMEOUT';
    const status = 1;
    const delay = true;
    log.addStopToRawLogs(keyObject, key, reason, status, delay);
    constants.global_registry[keyObject.rails_session_id].lastRawLog.should.equal('SO_TIMEOUT');
    delete constants.global_registry[keyObject.rails_session_id];
  });

  it('should upload the message to kafka when stop is received and should set keyObject.stopLogUploaded to true', () => {
    const keyObject = helper.getKeyObject();
    const type = 'STOP_SESSION';
    const logdata = '{"log": "data"}';
    const topic = 'raw_logs';
    const kafkaSendMessage = sinon.stub(kafka, 'sendMessage');
    log.uploadLogPartToKafka(keyObject, type, logdata, topic);
    assert(kafkaSendMessage.calledOnce === true);
    kafkaSendMessage.restore();
  });

  it('should upload the atomic message to kafka when an atomic message is recieved', () => {
    const keyObject = helper.getKeyObject();
    const type = 'REQUEST_RESPONSE';
    const logdata = '{"log": "data"}';
    const topic = 'raw_logs';
    const kafkaSendMessage = sinon.stub(kafka, 'sendMessage');
    log.uploadLogPartToKafka(keyObject, type, logdata, topic);
    keyObject.should.not.include.keys('stopLogUploaded');
    assert(kafkaSendMessage.calledOnce === true);
    kafkaSendMessage.restore();
  });

  it('should upload logdata in chunks when logdata size > kafkaConfig.LOG_CHUNK_SIZE', () => {
    const keyObject = helper.getKeyObject();
    const type = 'REQUEST_RESPONSE';
    const logdata = `{'log': '${randomstring.generate(307200)}'}`;
    const topic = 'raw_logs';
    const kafkaSendMessages = sinon.stub(kafka, 'sendMessages');
    log.uploadLogPartToKafka(keyObject, type, logdata, topic);
    keyObject.should.not.include.keys('stopLogUploaded');
    assert(kafkaSendMessages.calledOnce === true);
    kafkaSendMessages.restore();
  });

  describe('#sessionLog()', () => {
    it('should log session data when session stop', () => {
      const keyObject = helper.getKeyObject();
      keyObject.stopLogUploaded = true;
      const type = 'STOP_SESSION';
      const logsData = 'Stopping Session Bye';
      const callback = sinon.spy();
      sinon.stub(kafka, 'sendMessage');
      log.sessionLog(keyObject, type, logsData, callback);
      kafka.sendMessage.called.should.eql(true);
      assert(callback.calledOnce);
      kafka.sendMessage.restore();
    });

    it('should send logs to extended session partition', () => {
      const keyObject = helper.getKeyObject();
      keyObject.extendedSessionDuration = true;
      const type = 'STOP_SESSION';
      const logsData = 'Stopping Session Bye';
      const callback = () => {};
      sinon.stub(kafka, 'sendMessage');
      log.sessionLog(keyObject, type, logsData, callback);
      kafka.sendMessage.called.should.eql(true);
      kafka.sendMessage.restore();
    });

    it('should send logs to both normal and extended session partition', () => {
      const keyObject = helper.getKeyObject();
      keyObject.extendedSessionDuration = true;
      const originalSendBoth = constants.SEND_RAW_LOGS_BOTH;
      constants.SEND_RAW_LOGS_BOTH = !originalSendBoth;
      const type = 'STOP_SESSION';
      const logsData = 'Stopping Session Bye';
      const callback = () => {};
      sinon.stub(kafka, 'sendMessage');
      log.sessionLog(keyObject, type, logsData, callback);
      kafka.sendMessage.called.should.eql(true);
      constants.SEND_RAW_LOGS_BOTH = originalSendBoth;
      kafka.sendMessage.restore();
    });
  });

  describe('#terminalRequest()', () => {
    it('should log to selenium stats as large upload time if response success', () => {
      const url = 'http://sample.test';
      const callback = sinon.spy();
      const options = helper.getKeyObject();
      options.os = 'win';
      sinon.stub(requestlib, 'call').returns({
        then: sinon.stub().callsArgWith(0, { data: '{"file_size": "10", "time": "1000", "active_window": "facebook"}' }).returns({
          catch: () => { },
        }),
      });
      sinon.stub(originalHelper, 'pushToCLS');
      log.terminalRequest(url, options, callback, 1, false);
      assert(requestlib.call.calledOnce);
      assert(originalHelper.pushToCLS.called.should.to.be.true);
      assert(callback.calledOnce);
      requestlib.call.restore();
      originalHelper.pushToCLS.restore();
    });

    it('should log exception if response error', () => {
      const url = 'http://sample.test';
      const callback = sinon.spy();
      const options = helper.getKeyObject();
      options.os = 'win';
      sinon.stub(requestlib, 'call').returns({
        then: sinon.stub().callsArgWith(0, {
          data: '{"error": "I cannot capture video","file_size": "10", "time": "1000", "active_window": "facebook"}',
        }).returns({
          catch: () => { },
        }),
      });
      sinon.stub(originalHelper, 'pushToCLS');
      log.terminalRequest(url, options, callback, 1, false);
      assert(requestlib.call.calledOnce);
      assert(originalHelper.pushToCLS.called.should.to.be.true);
      requestlib.call.restore();
      originalHelper.pushToCLS.restore();
    });

    it('should call callback if url empty', () => {
      const url = '';
      const callback = sinon.spy();
      const options = helper.getKeyObject();
      options.os = 'mac';
      log.terminalRequest(url, options, callback, 0, false);
      assert(callback.calledOnce);
    });

    it('should log exception if terminal request fails', () => {
      const url = 'http://sample.test';
      const callback = sinon.spy();
      const options = helper.getKeyObject();
      options.os = 'win';
      sinon.stub(requestlib, 'call').returns({
        then: sinon.stub().callsArgWith(0, {}).returns({
          catch: sinon.stub().callsArgWith(0, { type: 'RequestError', error: '' }),
        }),
      });
      log.terminalRequest(url, options, callback, 1, false);
      assert(requestlib.call.calledOnce);
      requestlib.call.restore();
    });
  });

  describe('#stopVideoRecording()', () => {
    describe('when no errors', () => {
      let keyObject;
      let videoRecParams;
      const source = 'stopByBs';

      beforeEach(() => {
        keyObject = helper.getKeyObject();
        keyObject.os = 'win';
        videoRecParams = {
          video: keyObject.video,
          video_aws_keys: keyObject.video_aws_keys,
          video_aws_secret: keyObject.video_aws_secret,
          video_aws_bucket: keyObject.video_aws_bucket,
          video_aws_region: keyObject.video_aws_region,
          video_aws_storage_class: keyObject.video_aws_storage_class,
          logs_new_bucketing: keyObject.logs_new_bucketing,
          logs_aws_keys: keyObject.logs_aws_keys,
          logs_aws_secret: keyObject.logs_aws_secret,
          logs_aws_bucket: keyObject.logs_aws_bucket,
          logs_aws_region: keyObject.logs_aws_region,
          logs_aws_storage_class: keyObject.logs_aws_storage_class,
          video_file: keyObject.video_file,
          upload_fg: false,
          logHost: constants.zombie_server,
          edsHost: constants.eds_server,
          edsPort: constants.eds_port,
          edsKey: constants.eds_key,
          selenium_version: keyObject.selenium_version,
          session_id: keyObject.sessionId || keyObject.rails_session_id,
          uploadWebDriverLogs: keyObject.webDriverLogs && constants.uploadWebDriverLogs,
          webdriver_logs_key: 'selenium-logs.txt',
          modifyLogsForAi: 'false',
        };
      });

      it('should stopVideoRecording if stopByBS', () => {
        const callback = sinon.spy();
        const originalTerminalRequest = rewiredLog.__get__('terminalRequest');
        const fakeTerminalRequest = sinon.spy();
        const stopVideoRecording = rewiredLog.__get__('stopVideoRecording');
        rewiredLog.__set__('terminalRequest', fakeTerminalRequest);
        sinon.stub(originalHelper, 'pushToCLS');
        stopVideoRecording(keyObject, callback, source);
        sinon.assert.calledWith(fakeTerminalRequest, `/stop_video_rec?${requestlib.getEncodedURLParams(videoRecParams)}`, keyObject, callback, 0, true);
        assert(originalHelper.pushToCLS.called.should.to.be.true);
        originalHelper.pushToCLS.restore();
        rewiredLog.__set__('terminalRequest', originalTerminalRequest);
      });

      it('should stopVideoRecording if stopByBS and active_window, local is true', () => {
        const callback = sinon.spy();
        keyObject.tunnel = true;
        videoRecParams.active_window = true;
        videoRecParams.local = true;
        const originalTerminalRequest = rewiredLog.__get__('terminalRequest');
        const fakeTerminalRequest = sinon.spy();
        const stopVideoRecording = rewiredLog.__get__('stopVideoRecording');
        rewiredLog.__set__('terminalRequest', fakeTerminalRequest);
        sinon.stub(originalHelper, 'pushToCLS');
        stopVideoRecording(keyObject, callback, source, true);
        sinon.assert.calledWith(fakeTerminalRequest, `/stop_video_rec?${requestlib.getEncodedURLParams(videoRecParams)}`, keyObject, callback, 0, true);
        assert(originalHelper.pushToCLS.called.should.to.be.true);
        originalHelper.pushToCLS.restore();
        rewiredLog.__set__('terminalRequest', originalTerminalRequest);
      });

      it('should stopVideoRecording if stopByBS and userRequestStartTimeDiff is present', () => {
        const callback = sinon.spy();
        const postParams = { userRequestStartTimeDiff: 1234 };
        videoRecParams.userRequestStartTimeDiff = 1234;
        const originalTerminalRequest = rewiredLog.__get__('terminalRequest');
        const fakeTerminalRequest = sinon.spy();
        const stopVideoRecording = rewiredLog.__get__('stopVideoRecording');
        rewiredLog.__set__('terminalRequest', fakeTerminalRequest);
        sinon.stub(originalHelper, 'pushToCLS');
        stopVideoRecording(keyObject, callback, source, false, postParams);
        sinon.assert.calledWith(fakeTerminalRequest, `/stop_video_rec?${requestlib.getEncodedURLParams(videoRecParams)}`, keyObject, callback, 0, true);
        assert(originalHelper.pushToCLS.called.should.to.be.true);
        originalHelper.pushToCLS.restore();
        rewiredLog.__set__('terminalRequest', originalTerminalRequest);
      });
    });

    it('should stopVideoRecording if start-error', () => {
      const keyObject = helper.getKeyObject();
      keyObject.os = 'win';
      keyObject.browserstackParams = constants.browserstackParams;
      keyObject.bsCaps = { new_bucketing: true, orig_os: 'win' };
      keyObject.post_params = { u: 'test_user' };
      const extractAttributesWhenBSF = rewiredLog.__get__('extractAttributesWhenBSF');
      const newKeyObject = extractAttributesWhenBSF(keyObject);
      const videoRecParams = {
        video: newKeyObject.video,
        video_aws_keys: newKeyObject.video_aws_keys,
        video_aws_secret: newKeyObject.video_aws_secret,
        video_aws_bucket: newKeyObject.video_aws_bucket,
        video_aws_region: newKeyObject.video_aws_region,
        video_aws_storage_class: newKeyObject.video_aws_storage_class,
        logs_new_bucketing: newKeyObject.logs_new_bucketing,
        logs_aws_keys: newKeyObject.logs_aws_keys,
        logs_aws_secret: newKeyObject.logs_aws_secret,
        logs_aws_bucket: newKeyObject.logs_aws_bucket,
        logs_aws_region: newKeyObject.logs_aws_region,
        logs_aws_storage_class: newKeyObject.logs_aws_storage_class,
        video_file: newKeyObject.video_file,
        upload_fg: false,
        logHost: constants.zombie_server,
        edsHost: constants.eds_server,
        edsPort: constants.eds_port,
        edsKey: constants.eds_key,
        selenium_version: newKeyObject.selenium_version,
        session_id: newKeyObject.sessionId || newKeyObject.rails_session_id,
        uploadWebDriverLogs: newKeyObject.webDriverLogs && constants.uploadWebDriverLogs,
        webdriver_logs_key: 'selenium-logs.txt',
        modifyLogsForAi: 'false',
        capture_crash: true,
      };
      const callback = sinon.spy();
      const source = 'start-error';
      const originalTerminalRequest = rewiredLog.__get__('terminalRequest');
      const fakeTerminalRequest = sinon.spy();
      const stopVideoRecording = rewiredLog.__get__('stopVideoRecording');
      rewiredLog.__set__('terminalRequest', fakeTerminalRequest);
      sinon.stub(originalHelper, 'pushToCLS');
      stopVideoRecording(keyObject, callback, source);
      sinon.assert.calledWith(fakeTerminalRequest, `/stop_video_rec?${requestlib.getEncodedURLParams(videoRecParams)}`, newKeyObject, callback, 0, true);
      assert(originalHelper.pushToCLS.called.should.to.be.true);
      originalHelper.pushToCLS.restore();
      rewiredLog.__set__('terminalRequest', originalTerminalRequest);
    });

    it('should call callback if keyObject is not present', () => {
      const keyObject = '';
      const callback = sinon.spy();
      const source = 'stopByBS';
      log.stopVideoRecording(keyObject, callback, source, true);
      assert(callback.calledOnce);
    });
  });

  describe('#handleError()', () => {
    beforeEach(() => {
      sinon.stub(browserstack, 'postBrowserStack').yields(JSON.stringify({ noErrorScreenshot: true }));
      sinon.stub(log, 'sessionLog').returns('Logged Successfully');
      sinon.stub(log, 'addStopToRawLogs');
    });

    afterEach(() => {
      browserstack.postBrowserStack.restore();
      log.sessionLog.restore();
      log.addStopToRawLogs.restore();
    });

    it('should send different error message for app automate in raw logs', async () => {
      const keyObject = helper.getKeyObject();
      keyObject.appTesting = true;
      keyObject.device = 'Google Pixel 3';
      constants.global_registry[keyObject.rails_session_id] = keyObject;
      const hostname = '127.0.0.1';
      const opts = { device: keyObject.device };
      const errorMessage = errorMessageConstants.APP_AUTOMATE_ERROR_MESSAGES.SO_TIMEOUT;
      const request = {
        url: 'http://127.0.0.1',
        log_data: JSON.stringify({ url: 'http://127.0.0.1' }),
      };
      const response = {
        writeHead: sinon.stub(),
        write: sinon.stub(),
        end: sinon.stub(),
      };
      sinon.stub(myLock, 'ttlSemaphore').returns(Promise.resolve(1));

      await log.handleError(request, response, null, hostname, keyObject.rails_session_id, errorMessage, null, '10-12-2021', keyObject, null, opts);
      sinon.assert.calledOnce(log.addStopToRawLogs);
      sinon.assert.calledWith(
        log.addStopToRawLogs,
        sinon.match.any,
        keyObject.rails_session_id,
        errorMessage,
        sinon.match.any,
        sinon.match.any,
        sinon.match.any,
        sinon.match.any
      );

      myLock.ttlSemaphore.restore();
    });

    it('should send SO_TIMEOUT for others in raw logs', async () => {
      const keyObject = helper.getKeyObject();
      keyObject.appTesting = false;
      keyObject.device = 'Google Pixel 3';
      constants.global_registry[keyObject.rails_session_id] = keyObject;
      const hostname = '127.0.0.1';
      const opts = { device: keyObject.device };
      const errorMessage = 'SO_TIMEOUT';
      const request = {
        url: 'http://127.0.0.1',
        log_data: JSON.stringify({ url: 'http://127.0.0.1' }),
      };
      const response = {
        writeHead: sinon.stub(),
        write: sinon.stub(),
        end: sinon.stub(),
      };
      sinon.stub(myLock, 'ttlSemaphore').returns(Promise.resolve(1));

      await log.handleError(request, response, null, hostname, keyObject.rails_session_id, errorMessage, null, '10-12-2021', keyObject, null, opts);
      sinon.assert.calledOnce(log.addStopToRawLogs);
      sinon.assert.calledWith(
        log.addStopToRawLogs,
        sinon.match.any,
        keyObject.rails_session_id,
        errorMessage,
        sinon.match.any,
        sinon.match.any,
        sinon.match.any,
        sinon.match.any
      );

      myLock.ttlSemaphore.restore();
    });
  });

  describe('#fetchAdditionalLogs()', () => {
    it('should make requestlib call when sessionKeyObj is not null/undefined', async () => {
      const callback = sinon.spy();
      const options = helper.getKeyObject();
      options.os = 'android';
      options.networkLogs = true;
      options.rails_session_id = 'abcd';
      sinon.stub(requestlib, 'call').returns({
        then: sinon.stub().callsArgWith(0, { data: '{"test":"response"}' }).returns({ catch: () => { } }),
      });
      log.fetchAdditionalLogs(options, callback);
      assert(requestlib.call.calledOnce);
      assert(callback.calledOnce);
      requestlib.call.restore();
    });
  });

  describe('newCGLogger', () => {
    it('should set custom variables', () => {
      const fakelogger = sinon.spy();
      const newCGLogger = rewiredLog.__get__('newCGLogger');
      const originallogger = rewiredLog.__get__('logger');
      rewiredLog.__set__('logger', fakelogger);
      newCGLogger('test', 'test', LL.REQUEST, 'test', false, 2, 'try');
      fakelogger.called.should.be.true;
      rewiredLog.__set__('logger', originallogger);
    });
  });
});
