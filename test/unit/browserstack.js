const helper = require('./helper');
const constants = require('../../constants');
const origHelper = require('../../helper');
const requestlib = require('../../lib/request');
const HubLogger = require('../../log');
const browserstack = require('../../browserstack');
const pubSub = require('../../pubSub');
const sinon = require('sinon');
const assert = require('assert');
const testConf = require('./../../conf.test.json');
const { expect } = require('chai');
const should = require('chai').should();
const hubPortForTest = testConf.hub_port;
const rewire = require('rewire');
const browserstackRewire = rewire('../../browserstack');
const childProcess = require('child_process');
const railsPipeline = require("../../railsRequests/railsPipeline");

describe('Browserstack', function () {
  describe('_getLoggingOpts', function () {
    it('returns empty json if bsCaps is undefined', function () {
      opts = browserstack._getLoggingOpts(undefined)
      opts.should.eql({});
    });

    it('returns object with firecmd errors from bstack', function () {
      var keyObject = helper.getAppAutomateKeyObject();
      keyObject.error_type = "app_download_install_failure";
      keyObject.firecmd_error_message = "some stacktrace";
      keyObject.error_from = "browserstack_error";

      opts = browserstack._getLoggingOpts(keyObject, {});

      opts.error_type.should.eql(keyObject.error_type);
      opts.should.not.have.property('error_from')
    });

    it('returns object with firecmd errors from user_error', function () {
      var keyObject = helper.getAppAutomateKeyObject();
      keyObject.error_type = "app_download_install_failure";
      keyObject.firecmd_error_message = "some stacktrace";
      keyObject.error_from = "user_error";

      opts = browserstack._getLoggingOpts(keyObject, {});

      opts.error_type.should.eql(keyObject.error_type);
      opts.should.have.property('error_from');
      opts.error_from.should.eql('start-error-firecmd-user');
    });
  });

  describe('startSession', function () {
    it('displays message properly on start error for desktop', function () {
      var keyObject = helper.getKeyObject(), params = helper.getParamsObject();

      var catch_stub = sinon.stub().callsArgWith(0, { type: 'RequestError', error: '' });
      sinon.stub(requestlib, 'call').returns({
        then: sinon.stub().callsArgWith(0, {}).returns({
          catch: catch_stub
        })
      });
      sinon.stub(HubLogger, 'addStopToRawLogs');
      sinon.stub(HubLogger, 'nodeErrorHandler').callsArgWith(11, {});

      keyObject["attempt"] = 2
      keyObject.post_params = { framework: "selenium" }
      keyObject["bsCaps"] = {};
      keyObject["browserstackParams"] = { "browserstack.aws.save": "bs-stag/" + keyObject.rails_session_id };
      browserstack.startSession(params.request, params.response, '{"desiredCapabilities":{}}', helper.TERMINAL_IP, 5555, sinon.stub(), keyObject);

      HubLogger.addStopToRawLogs.calledWith(keyObject, keyObject.rails_session_id, 'BROWSER START-UP FAILURE', 1, true, undefined, undefined).should.be.true;

      requestlib.call.restore();
      HubLogger.addStopToRawLogs.restore();
      HubLogger.nodeErrorHandler.restore();
      delete constants.global_registry[keyObject.rails_session_id];
    });

    it('displays response error message on start for desktop', function () {
      var keyObject = helper.getKeyObject(), params = helper.getParamsObject();

      var catch_stub = sinon.stub().callsArgWith(0, { type: 'ResponseError', error: '' });
      sinon.stub(requestlib, 'call').returns({
        then: sinon.stub().callsArgWith(0, {}).returns({
          catch: catch_stub
        })
      });
      sinon.stub(HubLogger, 'exceptionLogger');
      sinon.stub(origHelper, 'sendAlerts');

      keyObject["attempt"] = 2
      keyObject["bsCaps"] = {};
      keyObject["browserstackParams"] = { "browserstack.aws.save": "bs-stag/" + keyObject.rails_session_id };
      keyObject.post_params = { framework: "selenium" }
      browserstack.startSession(params.request, params.response, '{"desiredCapabilities":{}}', helper.TERMINAL_IP, 5555, sinon.stub(), keyObject);

      HubLogger.exceptionLogger.called.should.be.true;
      origHelper.sendAlerts.called.should.be.true;

      requestlib.call.restore();
      HubLogger.exceptionLogger.restore();
      origHelper.sendAlerts.restore();
      delete constants.global_registry[keyObject.rails_session_id];
    });

    it('displays message properly on start error for emulators', function () {
      var keyObject = helper.getKeyObject(), params = helper.getParamsObject();

      var catch_stub = sinon.stub().callsArgWith(0, { type: 'RequestError', error: '' });
      sinon.stub(requestlib, 'call').returns({
        then: sinon.stub().callsArgWith(0, {}).returns({
          catch: catch_stub
        })
      });
      sinon.stub(HubLogger, 'addStopToRawLogs');
      sinon.stub(HubLogger, 'nodeErrorHandler').callsArgWith(11, {});

      keyObject["attempt"] = 2;
      keyObject["browserstackParams"] = { "browserstack.aws.save": "bs-stag/" + keyObject.rails_session_id };
      keyObject["bsCaps"] = { "mobile": { "version": "Google Nexus 6" } };
      keyObject.post_params = { framework: "selenium" }
      browserstack.startSession(params.request, params.response, '{"desiredCapabilities":{}}', helper.TERMINAL_IP, 45693, sinon.stub(), keyObject);

      HubLogger.addStopToRawLogs.calledWith(keyObject, keyObject.rails_session_id, 'COULD NOT BOOT EMULATOR', 1, true, undefined, undefined).should.be.true;

      requestlib.call.restore();
      HubLogger.addStopToRawLogs.restore();
      HubLogger.nodeErrorHandler.restore();
      delete constants.global_registry[keyObject.rails_session_id];
    });

    it('displays message properly on start error for web testing on real mobile', function () {
      var keyObject = helper.getKeyObject(), params = helper.getParamsObject();

      var catch_stub = sinon.stub().callsArgWith(0, { type: 'RequestError', error: '' });
      sinon.stub(requestlib, 'call').returns({
        then: sinon.stub().callsArgWith(0, {}).returns({
          catch: catch_stub
        })
      });
      sinon.stub(HubLogger, 'addStopToRawLogs');
      sinon.stub(HubLogger, 'nodeErrorHandler').callsArgWith(11, {});

      keyObject["attempt"] = 2;
      keyObject["realMobile"] = true;
      keyObject.post_params = { framework: "selenium" }
      keyObject["browserstackParams"] = { "browserstack.aws.save": "bs-stag/" + keyObject.rails_session_id };
      keyObject["bsCaps"] = { "mobile": { "version": "Google Nexus 6" } };
      browserstack.startSession(params.request, params.response, '{"desiredCapabilities":{}}', helper.TERMINAL_IP, hubPortForTest, sinon.stub(), keyObject);

      HubLogger.addStopToRawLogs.calledWith(keyObject, keyObject.rails_session_id, 'COULD NOT START MOBILE BROWSER', 1, true, undefined, undefined).should.be.true;

      requestlib.call.restore();
      HubLogger.addStopToRawLogs.restore();
      HubLogger.nodeErrorHandler.restore();
      delete constants.global_registry[keyObject.rails_session_id];
    });

    it('displays message properly on start error for app testing on real mobile', function () {
      var keyObject = helper.getKeyObject(), params = helper.getParamsObject();

      var catch_stub = sinon.stub().callsArgWith(0, { type: 'RequestError', error: '' });
      sinon.stub(requestlib, 'call').returns({
        then: sinon.stub().callsArgWith(0, {}).returns({
          catch: catch_stub
        })
      });
      sinon.stub(HubLogger, 'addStopToRawLogs');
      sinon.stub(HubLogger, 'nodeErrorHandler').callsArgWith(11, {});

      keyObject["attempt"] = 2;
      keyObject["realMobile"] = true;
      keyObject["appTesting"] = true;
      keyObject["browserstackParams"] = { "browserstack.aws.save": "bs-stag/" + keyObject.rails_session_id };
      keyObject["bsCaps"] = { "mobile": { "version": "Google Nexus 6" }, "app": "http://app.com/debug.apk", "platform": "android" };
      keyObject.post_params = { framework: "selenium" }
      browserstack.startSession(params.request, params.response, '{"desiredCapabilities":{}}', helper.TERMINAL_IP, hubPortForTest, sinon.stub(), keyObject);

      HubLogger.addStopToRawLogs.calledWith(keyObject, keyObject.rails_session_id, 'Could not start a session. Something went wrong with app launch. Please try to run the test again.', 1, true, undefined, undefined).should.be.true;

      requestlib.call.restore();
      HubLogger.addStopToRawLogs.restore();
      HubLogger.nodeErrorHandler.restore();
      delete constants.global_registry[keyObject.rails_session_id];
    });

    it('displays message properly on start error for app testing on real mobile even if retries are left but client has disconnected', function () {
      var keyObject = helper.getKeyObject(), params = helper.getParamsObject();

      var catch_stub = sinon.stub().callsArgWith(0, { type: 'RequestError', error: '', message: "something went wrong", stack: "something/\nsomething\nsomething" });
      sinon.stub(requestlib, 'call').returns({
        then: sinon.stub().callsArgWith(0, {}).returns({
          catch: catch_stub
        })
      });
      sinon.stub(HubLogger, 'addStopToRawLogs');
      sinon.stub(HubLogger, 'nodeErrorHandler').callsArgWith(11, {});

      keyObject["attempt"] = 0;
      keyObject["realMobile"] = true;
      keyObject["appTesting"] = true;
      keyObject["browserstackParams"] = { "browserstack.aws.save": "bs-stag/" + keyObject.rails_session_id };
      keyObject["bsCaps"] = { "mobile": { "version": "Google Nexus 6" }, "app": "http://app.com/debug.apk", "platform": "android", "device": "Google Nexus 6" };
      keyObject.response = {
        writeHead: function() {},
        write: function() {},
        end: function() {},
        clientOnline: function() {},
      };
      keyObject.request = {
        method: 'POST',
        url: '/wd/hub/session'
      };
      keyObject.url = ""
      keyObject.post_params = { start: "true", framework: "selenium" }
      let responseStub = sinon.stub(keyObject.response, 'clientOnline');
      responseStub.returns(false);

      browserstack.startSession(params.request, params.response, '{"desiredCapabilities":{}}', helper.TERMINAL_IP, hubPortForTest, sinon.stub(), keyObject);

      HubLogger.addStopToRawLogs.calledWith(keyObject, keyObject.rails_session_id, 'Could not start a session. Something went wrong with app launch. Please try to run the test again.', 1, true, undefined, undefined).should.be.true;

      requestlib.call.restore();
      HubLogger.addStopToRawLogs.restore();
      HubLogger.nodeErrorHandler.restore();
      delete constants.global_registry[keyObject.rails_session_id];
    });

    context('chromeOptions in W3C', () => {
      const request = { id: ' random-id', headers: { 'x-forwarded-for': '127.0.0.1' } };
      const rproxyHost = '************';
      const port = 45691;
      const params = {
        method: 'POST',
        path: '/wd/hub/session',
        hostname: rproxyHost,
        port: 45691,
        headers: {
          'content-type': 'application/json; charset=utf-8',
          accept: 'application/json',
          'BStack-Host': '************',
          'X-Source-Env-Type': 'development',
          connection: 'close'
        },
        timeout: 60000
      };

      before(() => {
        sinon.stub(requestlib, 'call').returns({
          then: sinon.stub().callsArgWith(0, {}).returns({ catch: () => { } })
        });
      })

      after(() => {
        delete params.headers['content-length'];
        delete params.body;
        requestlib.call.restore();
      })

      it('does not modify/copy goog:chromeOptions in capabilities object if absent in desiredCapabilities', function () {
        const caps = { desiredCapabilities: {}, capabilities: { firstMatch: [{ 'goog:chromeOptions': { y: 1 } }], alwaysMatch: [] } };
        params.headers['content-length'] = 81;
        params.body = '{"capabilities":{"firstMatch":[{"goog:chromeOptions":{"y":1}}],"alwaysMatch":[]}}';

        browserstack.startSession(request, {}, JSON.stringify(caps), '************', port, () => { }, { rproxyHost, post_params: { framework: 'selenium' } });
        requestlib.call.calledWith(params).should.be.true;
      });

      it('copies chromeOptions from desiredCapabilities to goog:chromeOptions in capabilities object', function () {
        const caps = { desiredCapabilities: { chromeOptions: {} }, capabilities: { firstMatch: [{}], alwaysMatch: [] } };
        params.headers['content-length'] = 76;
        params.body = '{"capabilities":{"firstMatch":[{"goog:chromeOptions":{}}],"alwaysMatch":[]}}';

        browserstack.startSession(request, {}, JSON.stringify(caps), '************', port, () => { }, { rproxyHost, post_params: { framework: 'selenium' } });
        requestlib.call.calledWith(params).should.be.true;
      });

      it('copies goog:chromeOptions from desiredCapabilities to goog:chromeOptions in capabilities object', function () {
        const caps = { desiredCapabilities: { 'goog:chromeOptions': {} }, capabilities: { firstMatch: [{}], alwaysMatch: [] } };
        params.headers['content-length'] = 76;
        params.body = '{"capabilities":{"firstMatch":[{"goog:chromeOptions":{}}],"alwaysMatch":[]}}';

        browserstack.startSession(request, {}, JSON.stringify(caps), '************', port, () => { }, { rproxyHost, post_params: { framework: 'selenium' } });
        requestlib.call.calledWith(params).should.be.true;
      });

      it('prefers and copies chromeOptions from desiredCapabilities to goog:chromeOptions in capabilities object', function () {
        const caps = { desiredCapabilities: { 'goog:chromeOptions': {}, chromeOptions: { x: 1 } }, capabilities: { firstMatch: [{}], alwaysMatch: [] } };
        params.headers['content-length'] = 81;
        params.body = '{"capabilities":{"firstMatch":[{"goog:chromeOptions":{"x":1}}],"alwaysMatch":[]}}';

        browserstack.startSession(request, {}, JSON.stringify(caps), '************', port, () => { }, { rproxyHost, post_params: { framework: 'selenium' } });
        requestlib.call.calledWith(params).should.be.true;
      });

      it('should work with browserstack.seleniumBidi set to true and copies chromeOptions from desiredCapabilities to goog:chromeOptions in capabilities object', function () {
        const caps = { desiredCapabilities: { 'goog:chromeOptions': {}, chromeOptions: { x: 1 } }, capabilities: { firstMatch: [{}], alwaysMatch: [] } };
        params.headers['content-length'] = 81;
        params.body = '{"capabilities":{"firstMatch":[{"goog:chromeOptions":{"x":1}}],"alwaysMatch":[]}}';

        browserstack.startSession(request, {}, JSON.stringify(caps), '************', port, () => { }, { rproxyHost, post_params: { framework: 'selenium' }, browserstackParams: { 'browserstack.seleniumBidi': 'true', 'browserstack.selenium.jar.version': '4.20.0'} });
        requestlib.call.calledWith(params).should.be.true;
      });

      it('should work with browserstack.seleniumCdp set to true and copies chromeOptions from desiredCapabilities to goog:chromeOptions in capabilities object', function () {
        const caps = { desiredCapabilities: { 'goog:chromeOptions': {}, chromeOptions: { x: 1 } }, capabilities: { firstMatch: [{}], alwaysMatch: [] } };
        params.headers['content-length'] = 81;
        params.body = '{"capabilities":{"firstMatch":[{"goog:chromeOptions":{"x":1}}],"alwaysMatch":[]}}';

        browserstack.startSession(request, {}, JSON.stringify(caps), '************', port, () => { }, { rproxyHost, post_params: { framework: 'selenium' }, browserstackParams: { 'browserstack.seleniumCdp': 'true', 'browserstack.selenium.jar.version': '4.20.0'} });
        requestlib.call.calledWith(params).should.be.true;
      });
    })

    context('keep-alive in dataToSend for EDS', () => {
      const request = { id: ' random-id', headers: { 'x-forwarded-for': '127.0.0.1' } };
      const rproxyHost = '************';
      const port = 45691;

      it('should not send keep-alive to Zombie for automate', function () {
        const caps = { desiredCapabilities: {}, capabilities: { firstMatch: [{ 'goog:chromeOptions': { y: 1 } }], alwaysMatch: [] } };
        const pingZombieStub = sinon.stub(origHelper, 'PingZombie');
        origHelper.PingZombie = pingZombieStub;

        browserstack.startSession(request, {}, JSON.stringify(caps), '************', port, () => { }, { rproxyHost, post_params: { framework: 'selenium' } });

        pingZombieStub.calledWith({
          sessionid: undefined,
          client_ip: '127.0.0.1',
          cloudflare: '',
          hub_request_id: ' random-id',
          product_package: 'cbt',
          kind: 'automation_session_stats'
        }).should.be.true;

        origHelper.PingZombie.restore();
      });
    });

    context('ms:edgeOptions in W3C', () => {
      const request = { id: ' random-id', headers: { 'x-forwarded-for': '127.0.0.1' } };
      const rproxyHost = '************';
      const port = 45691;
      const params = {
        method: 'POST',
        path: '/wd/hub/session',
        hostname: rproxyHost,
        port: 45691,
        headers: {
          'content-type': 'application/json; charset=utf-8',
          accept: 'application/json',
          'BStack-Host': '************',
          'X-Source-Env-Type': 'development',
          connection: 'close'
        },
        timeout: 60000
      };

      before(() => {
        sinon.stub(requestlib, 'call').returns({
          then: sinon.stub().callsArgWith(0, {}).returns({ catch: () => { } })
        });
      })

      after(() => {
        delete params.headers['content-length'];
        delete params.body;
        requestlib.call.restore();
      })

      it('copies ms:edgeOptions from desiredCapabilities to ms:edgeOptions in capabilities object', function () {
        const caps = { desiredCapabilities: { 'ms:edgeOptions': { x: 1 } }, capabilities: { firstMatch: [{}], alwaysMatch: [] } };
        params.headers['content-length'] = 77;
        params.body = '{"capabilities":{"firstMatch":[{"ms:edgeOptions":{"x":1}}],"alwaysMatch":[]}}';

        browserstack.startSession(request, {}, JSON.stringify(caps), '************', port, () => { }, { rproxyHost, post_params: { framework: 'selenium' } });
        requestlib.call.calledWith(params).should.be.true;
      });
    })
  });

  describe('#appendOpts()', () => {
    it('should return new options', () => {
      const originalOpts = {
        key1: 'value1',
        key2: 'value2'
      }
      const newOpts = {
        key3: 'value3',
        key4: 'value4'
      }
      const expectedOpts = {
        key1: 'value1',
        key2: 'value2',
        key3: 'value3',
        key4: 'value4',
      }
      expect(JSON.stringify(browserstack.appendOpts(originalOpts, newOpts))).to.be.equal(JSON.stringify(expectedOpts));
    });

    it('should return original options if no new opts', () => {
      const originalOpts = {
        key1: 'value1',
        key2: 'value2'
      }
      expect(JSON.stringify(browserstack.appendOpts(originalOpts, undefined))).to.be.equal(JSON.stringify(originalOpts));
    });
  });

  describe('#modifyEdgeCaps()', () => {
    it('#should return desired capabilities when acceptInsecureCerts', () => {
      const desiredCapabilities = {
        browser: 'MicrosoftEdge',
        os: 'windows',
        os_version: '10',
        version: '81.0',
        acceptInsecureCerts: 'true',
      };
      const expectedValue = {
        browser: "edge",
        os: "windows",
        os_version: "10",
        version: "81.0",
        browserName: "edge",
        initialBrowserUrl: "about:blank",
        acceptSslCerts: true
      }
      expect(JSON.stringify(browserstack.modifyEdgeCaps(desiredCapabilities))).to.be.equal(JSON.stringify(expectedValue))
    });

    it('#should return capabilities when browser undefined', () => {
      const desiredCapabilities = {
        browser: undefined,
        os: 'windows',
        os_version: '10',
        version: '18.0',
      };
      const expectedValue = {
        browser: "MicrosoftEdge",
        os: "windows",
        os_version: "10",
        version: "18.0",
        browserName: "MicrosoftEdge",
        initialBrowserUrl: "about:blank"
      }
      expect(JSON.stringify(browserstack.modifyEdgeCaps(desiredCapabilities))).to.be.equal(JSON.stringify(expectedValue))
    });
    it('#should return capabilities & delete acceptInsecureCerts', () => {
      const capabilities = {
        browser: undefined,
        os: 'windows',
        os_version: '10',
        version: '43.0',
        acceptInsecureCerts: true,
        W3C_capabilities: {
          firstMatch: [{ browserVersion: '43.0' }]
        }
      };
      const expectedValue = {
        browser: "MicrosoftEdge",
        os: "windows",
        os_version: "10",
        version: "43.0",
        W3C_capabilities: {
          firstMatch: [{ browserName: "MicrosoftEdge" }]
        },
        browserName: "MicrosoftEdge",
        initialBrowserUrl: "about:blank",
        acceptSslCerts: true
      }
      expect(JSON.stringify(browserstack.modifyEdgeCaps(capabilities))).to.be.equal(JSON.stringify(expectedValue));
    });
  });
});

describe('modify caps function', () => {
  describe('checks behaviour for windows devices', () => {
    const desiredCapabilities = {
      browser: 'MicrosoftEdge',
      os: 'windows',
      os_version: '10',
      version: '81.0',
    };

    it('modifies edge capabilities for chromium 81', (done) => {
      const capabilities = Object.assign({}, desiredCapabilities);
      browserstack.modifyEdgeCaps(capabilities);
      assert(capabilities.browser === 'edge');
      done();
    });

    it('modifies edge capabilities for chromium 80', (done) => {
      const capabilities = Object.assign({}, desiredCapabilities);
      capabilities.version = '80.0';
      browserstack.modifyEdgeCaps(capabilities);
      assert(capabilities.browser === 'edge');
      done();
    });

    it('modifies edge capabilities for chromium 81 beta', (done) => {
      const capabilities = Object.assign({}, desiredCapabilities);
      capabilities.version = '81.0 beta';
      browserstack.modifyEdgeCaps(capabilities);
      assert(capabilities.browser === 'edge');
      done();
    });

    it('does not modify edge capabilites for non chromium version 17.0', (done) => {
      const capabilities = Object.assign({}, desiredCapabilities);
      capabilities.version = '17.0';
      browserstack.modifyEdgeCaps(capabilities);
      assert(capabilities.browser === 'MicrosoftEdge');
      done();
    });

    it('does not modify edge capabilites for non chromium version 15.0', (done) => {
      const capabilities = Object.assign({}, desiredCapabilities);
      capabilities.version = '15.0';
      browserstack.modifyEdgeCaps(capabilities);
      assert(capabilities.browser === 'MicrosoftEdge');
      done();
    });

    it('does not modify edge capabilites for non chromium version 18.0', (done) => {
      const capabilities = Object.assign({}, desiredCapabilities);
      capabilities.version = '18.0';
      browserstack.modifyEdgeCaps(capabilities);
      assert(capabilities.browser === 'MicrosoftEdge');
      done();
    });
  });
});

describe('handle_user_disconnected_during_start', function () {
  it('should result in respondWithError when user disconnects during rails start', function () {
    let request = {aborted: true};
    let response = {};
    const mockRespondWithError = {};
    mockRespondWithError.stub = sinon.stub(origHelper, 'respondWithError');
    const mockMarkRequestEnd = {};
    mockMarkRequestEnd.stub = sinon.stub(origHelper, 'markRequestEnd');
    browserstack.handle_user_disconnected_during_start({ automation_session_id: 'session_id', u: 'user1' }, request, response, false, 1, 1, false,"", "message");
    origHelper.sendToEDS.should.have.been.called;
    origHelper.respondWithError.calledWith(request, response, "ClientNotOnline", true, false).should.be.true;
    origHelper.markRequestEnd.calledWith(request).should.be.true;
    mockRespondWithError.stub.restore();
    mockMarkRequestEnd.stub.restore();
  });
});

describe('handle_error_fire_command', function () {
  it('displays custom message on start error for app testing on realMobile', function () {
    var keyObject = helper.getKeyObject(), params = helper.getParamsObject();
    var catch_stub = sinon.stub().callsArgWith(0, { type: 'RequestError', error: '' });
    sinon.stub(HubLogger, 'addStopToRawLogs');
    sinon.stub(HubLogger, 'nodeErrorHandler').callsArgWith(11, {});
    const uniqueUserEvent = sinon.stub(HubLogger.hoothoot_user, 'uniqueUserEvent');

    keyObject["attempt"] = 1;
    keyObject["realMobile"] = true;
    keyObject["appTesting"] = true;
    keyObject["bsCaps"] = { "browserstack.aws.save": "bs-stag/" + keyObject.sessionId, "mobile": { "version": "Google Nexus 6" }, "app": "http://app.com/debug.apk", "platform": "android" };
    keyObject["hoothootCanaryTags"] = { "tag": "test" }

    browserstack.handle_error_fire_command(keyObject, 'start-error-firecmd', 1, function () { }, JSON.stringify({ "error": "App Download Failed", "kind": "app_download_install_failure", "type": "browserstack_error" }));

    HubLogger.addStopToRawLogs.calledWith(keyObject, keyObject.sessionId, "Could not start a session. Something went wrong with app launch. Please try to run the test again.", 1, true, undefined, undefined).should.be.true;

    HubLogger.addStopToRawLogs.restore();
    HubLogger.nodeErrorHandler.restore();
    uniqueUserEvent.restore();
    delete constants.global_registry[keyObject.rails_session_id];
  });

  it('displays custom message on start error for app testing on realMobile when browsermob fails starting', function () {
    var keyObject = helper.getKeyObject(), params = helper.getParamsObject();
    var catch_stub = sinon.stub().callsArgWith(0, { type: 'RequestError', error: '' });
    sinon.stub(HubLogger, 'addStopToRawLogs');
    sinon.stub(HubLogger, 'nodeErrorHandler').callsArgWith(11, {});
    const uniqueUserEvent = sinon.stub(HubLogger.hoothoot_user, 'uniqueUserEvent');

    keyObject["attempt"] = 1;
    keyObject["realMobile"] = true;
    keyObject["appTesting"] = true;
    keyObject["bsCaps"] = { "browserstack.aws.save": "bs-stag/" + keyObject.sessionId, "mobile": { "version": "Google Nexus 6" }, "app": "http://app.com/debug.apk", "platform": "android" };
    keyObject["hoothootCanaryTags"] = { "tag": "test" }

    browserstack.handle_error_fire_command(keyObject, 'start-error-firecmd', 1, function () { }, JSON.stringify({ "error": "Error while starting browsermob", "kind": "app_download_install_failure", "type": "browserstack_error" }));

    HubLogger.addStopToRawLogs.calledWith(keyObject, keyObject.sessionId, "Could not start a session. Something went wrong with app launch. Please try to run the test again.", 1, true, undefined, undefined).should.be.true;

    HubLogger.addStopToRawLogs.restore();
    HubLogger.nodeErrorHandler.restore();
    uniqueUserEvent.restore();
    delete constants.global_registry[keyObject.rails_session_id];
  });

  it('displays custom message on start error for app testing on realMobile when attempt > 1', function () {
    var keyObject = helper.getKeyObject(), params = helper.getParamsObject();
    var catch_stub = sinon.stub().callsArgWith(0, { type: 'RequestError', error: '' });
    sinon.stub(origHelper, 'pushToCLS');
    sinon.stub(HubLogger, 'miscLogger');

    keyObject["attempt"] = 2;
    keyObject["realMobile"] = true;
    keyObject["appTesting"] = true;
    keyObject["bsCaps"] = { "browserstack.aws.save": "bs-stag/" + keyObject.sessionId, "mobile": { "version": "Google Nexus 6" }, "app": "http://app.com/debug.apk", "platform": "android" };
    keyObject["hoothootCanaryTags"] = { "tag": "test" }

    browserstack.handle_error_fire_command(keyObject, 'start-error-firecmd', 2, function () { }, undefined);

    origHelper.pushToCLS.called.should.be.true;
    HubLogger.miscLogger.called.should.be.true;
    origHelper.pushToCLS.restore();
    HubLogger.miscLogger.restore();

    delete constants.global_registry[keyObject.rails_session_id];
  });

  it('displays custom message on start error for app testing on realMobile even if error response is undefined', function () {
    var keyObject = helper.getKeyObject(), params = helper.getParamsObject();
    var catch_stub = sinon.stub().callsArgWith(0, { type: 'RequestError', error: '' });
    sinon.stub(HubLogger, 'addStopToRawLogs');
    sinon.stub(HubLogger, 'nodeErrorHandler').callsArgWith(11, {});

    keyObject["attempt"] = 1;
    keyObject["realMobile"] = true;
    keyObject["appTesting"] = true;
    keyObject["bsCaps"] = { "browserstack.aws.save": "bs-stag/" + keyObject.sessionId, "mobile": { "version": "Google Nexus 6" }, "app": "http://app.com/debug.apk", "platform": "android" };
    browserstack.handle_error_fire_command(keyObject, 'start-error-firecmd', 1, function () { }, JSON.stringify({}));

    HubLogger.addStopToRawLogs.calledWith(keyObject, keyObject.sessionId, "Could not start a session. Something went wrong with app launch. Please try to run the test again.", 1, true, undefined, undefined).should.be.true;

    HubLogger.addStopToRawLogs.restore();
    HubLogger.nodeErrorHandler.restore();
    delete constants.global_registry[keyObject.rails_session_id];
  });

  it('displays custom message on start error for app testing on realMobile even if error response contains network connectivity errors', function () {
    var keyObject = helper.getKeyObject(), params = helper.getParamsObject();
    var catch_stub = sinon.stub().callsArgWith(0, { type: 'RequestError', error: '' });
    sinon.stub(HubLogger, 'addStopToRawLogs');
    sinon.stub(HubLogger, 'nodeErrorHandler').callsArgWith(11, {});

    keyObject["attempt"] = 1;
    keyObject["realMobile"] = true;
    keyObject["appTesting"] = true;
    keyObject["bsCaps"] = { "browserstack.aws.save": "bs-stag/" + keyObject.sessionId, "mobile": { "version": "Google Nexus 6" }, "app": "http://app.com/debug.apk", "platform": "android" };
    browserstack.handle_error_fire_command(keyObject, 'start-error-firecmd', 1, function () { }, "Error: connect ETIMEDOUT 1.2.3.4:45671");

    HubLogger.addStopToRawLogs.calledWith(keyObject, keyObject.sessionId, "Could not start a session. Something went wrong with app launch. Please try to run the test again.", 1, true, undefined, undefined).should.be.true;

    HubLogger.addStopToRawLogs.restore();
    HubLogger.nodeErrorHandler.restore();
    delete constants.global_registry[keyObject.rails_session_id];
  });

  it('displays custom templated message on start error for app testing when response had meta_data', function () {
    var keyObject = helper.getKeyObject(), params = helper.getParamsObject();
    var catch_stub = sinon.stub().callsArgWith(0, { type: 'RequestError', error: '' });
    sinon.stub(HubLogger, 'addStopToRawLogs');
    sinon.stub(HubLogger, 'nodeErrorHandler').callsArgWith(11, {});

    keyObject["attempt"] = 1;
    keyObject["realMobile"] = true;
    keyObject["appTesting"] = true;
    keyObject["bsCaps"] = { "browserstack.aws.save": "bs-stag/" + keyObject.sessionId, "mobile": { "version": "Google Nexus 6" }, "app": "http://app.com/debug.apk", "platform": "android" };
    browserstack.handle_error_fire_command(keyObject, 'start-error-firecmd', 1, function () { }, JSON.stringify({ "error": "Invalid value passed in DSL", "kind": "app_settings_invalid_value", "type": "browserstack_error", "meta_data": { key: "otp", value: "1234"} }));

    HubLogger.addStopToRawLogs.calledWith(keyObject, keyObject.sessionId, "Could not start a session : '1234' value for 'otp' setting in App Settings DSL is not a valid value as per App's Settings Bundle. Please check the DSL and try again. If the error persists, please reach out to support.", 1, true, undefined, undefined).should.be.true;

    HubLogger.addStopToRawLogs.restore();
    HubLogger.nodeErrorHandler.restore();
    delete constants.global_registry[keyObject.rails_session_id];
  });

  let all_playstore_tests = Object.keys(constants.firecmd_custom_exceptions).filter(v => v.startsWith('google-play-store-'));
  let user_realted_playstore_error_tests = constants.googleLoginUserErrors;
  let browserstack_realted_playstore_error_tests = all_playstore_tests.filter(n => !user_realted_playstore_error_tests.includes(n));
  user_realted_playstore_error_tests.forEach(function (test) {
    it(`displays custom message on start error for app testing on realMobile for user related google login failures - ${test}`, function () {
      var keyObject = helper.getKeyObject(), params = helper.getParamsObject();
      var catch_stub = sinon.stub().callsArgWith(0, { type: 'RequestError', error: '' });
      sinon.stub(HubLogger, 'addStopToRawLogs');
      sinon.stub(HubLogger, 'nodeErrorHandler').callsArgWith(11, {});
      sinon.stub(HubLogger.hoothoot_user, "uniqueUserEvent");

      keyObject["attempt"] = 1;
      keyObject["realMobile"] = true;
      keyObject["appTesting"] = true;
      keyObject["bsCaps"] = { "browserstack.aws.save": "bs-stag/" + keyObject.sessionId, "mobile": { "version": "Google Nexus 6" }, "app": "http://app.com/debug.apk", "platform": "android" };
      browserstack.handle_error_fire_command(keyObject, test, 1, function () { }, JSON.stringify({error: "", kind: test, type: "user_error"}));

      HubLogger.addStopToRawLogs.calledWith(keyObject, keyObject.sessionId, constants.firecmd_custom_exceptions[test], 1, true, undefined, undefined).should.be.true;
      HubLogger.hoothoot_user.uniqueUserEvent.called.should.be.false;
      HubLogger.addStopToRawLogs.restore();
      HubLogger.nodeErrorHandler.restore();
      HubLogger.hoothoot_user.uniqueUserEvent.restore();
      delete constants.global_registry[keyObject.rails_session_id];
    });
  });

  browserstack_realted_playstore_error_tests.forEach(function (test) {
    it(`displays custom message on start error for app testing on realMobile for browserstack related google login failures - ${test}`, function () {
      var keyObject = helper.getKeyObject(), params = helper.getParamsObject();
      var catch_stub = sinon.stub().callsArgWith(0, { type: 'RequestError', error: '' });
      sinon.stub(HubLogger, 'addStopToRawLogs');
      sinon.stub(HubLogger, 'nodeErrorHandler').callsArgWith(11, {});
      sinon.stub(HubLogger.hoothoot_user, "uniqueUserEvent");

      keyObject["attempt"] = 1;
      keyObject["realMobile"] = true;
      keyObject["appTesting"] = true;
      keyObject["bsCaps"] = { "browserstack.aws.save": "bs-stag/" + keyObject.sessionId, "mobile": { "version": "Google Nexus 6" }, "app": "http://app.com/debug.apk", "platform": "android" };
      browserstack.handle_error_fire_command(keyObject, test, 1, function () { }, JSON.stringify({error: "", kind: test, type: "browserstack_error"}));
      HubLogger.addStopToRawLogs.calledWith(keyObject, keyObject.sessionId, constants.firecmd_custom_exceptions[test], 1, true, undefined, undefined).should.be.true;

      HubLogger.addStopToRawLogs.restore();
      HubLogger.nodeErrorHandler.restore();
      HubLogger.hoothoot_user.uniqueUserEvent.restore();
      delete constants.global_registry[keyObject.rails_session_id];
    });
  });
});

describe('Helper', function () {
  describe('getAutoitText', function () {
    var sandbox;
    beforeEach(function () {
      sandbox = sinon.sandbox.create();
    });

    afterEach(function () {
      sandbox.restore();
    });

    it("correctly escapes special characters like # ", function () {
      var keyObject = helper.getKeyObject();
      var pubsub_stub = sandbox.stub(pubSub, "publish").returns("");
      encoded_for_autoit = origHelper.getAutoitText(keyObject, ["#", "a", "#"], pubsub_stub);
      encoded_for_autoit.should.be.equal("{#}#-#a#-#{#}#-#");
    });

    it("correctly escapes special characters like ^ ", function () {
      var keyObject = helper.getKeyObject();
      var pubsub_stub = sandbox.stub(pubSub, "publish").returns("");
      encoded_for_autoit = origHelper.getAutoitText(keyObject, ["^", "^", "^", "#"], pubsub_stub);
      encoded_for_autoit.should.be.equal("{^}#-#{^}#-#{^}#-#{#}#-#");
    });

    it("correctly escapes special characters like { } ", function () {
      var keyObject = helper.getKeyObject();
      var pubsub_stub = sandbox.stub(pubSub, "publish").returns("");
      encoded_for_autoit = origHelper.getAutoitText(keyObject, ["#", "a", "b", "#", "^", "{", "}"], pubsub_stub);
      encoded_for_autoit.should.be.equal("{#}#-#a#-#b#-#{#}#-#{^}#-#{{}#-#{}}#-#");
    });

    it("correctly escapes special characters like ESC, CTRL ", function () {
      var keyObject = helper.getKeyObject();
      var pubsub_stub = sandbox.stub(pubSub);

      encoded_for_autoit = origHelper.getAutoitText(keyObject, ["\uE00C", "#", "\uE009", "#", "a"], pubsub_stub);
      encoded_for_autoit.should.be.equal("{ESC}#-#{#}#-#{CTRLDOWN}#-#{#}#-#a#-#{CTRLUP}#-#");
    });
  });
});

describe('filterAndSendToZombie', function() {
  let data, post_params, options, seleniumStatsStub;

  beforeEach(function() {
    data = { error: '' };
    post_params = { someKey: 'someValue' };
    options = { isAppAutomate: false, isDetox: false };
    seleniumStatsStub = sinon.stub(HubLogger, 'seleniumStats');
    constants.global_registry = {};
  });

  afterEach(function() {
    seleniumStatsStub.restore();
  });

  it('should call seleniumStats if error is "OS/Browser combination invalid."', function() {
    data.error = 'OS/Browser combination invalid.';
    browserstack.filterAndSendToZombie(data, post_params, options);
    assert(seleniumStatsStub.calledOnce);
  });

  it('should call seleniumStats if error is "Parallel limit reached" and global_registry length is less than 2', function() {
    data.error = 'Parallel limit reached';
    constants.global_registry = { '1': 'test' };
    browserstack.filterAndSendToZombie(data, post_params, options);
    assert(seleniumStatsStub.calledOnce);
  });

  it('should not call seleniumStats if error is "Parallel limit reached" and global_registry length is 2 or more', function() {
    data.error = 'Parallel limit reached';
    constants.global_registry = { '1': 'test', '2': 'test' };
    browserstack.filterAndSendToZombie(data, post_params, options);
    assert(seleniumStatsStub.notCalled);
  });

  it('should not call seleniumStats if error is not in sendOnly array and is not "Parallel limit reached"', function() {
    data.error = 'Some other error';
    options = null;
    browserstack.filterAndSendToZombie(data, post_params, options);
    assert(seleniumStatsStub.notCalled);
  });
});

describe('#checkProxyForwarding', () => {
  let checkProxyForwarding;
  before(() => {
    checkProxyForwarding = browserstackRewire.__get__("checkProxyForwarding")
  })

  it('should return false when invalid caps', () => {
    let capabilites = {}
    let result = checkProxyForwarding(capabilites);
    expect(result).to.be.equal(false);
    capabilites = {firstMatch: []}
    result = checkProxyForwarding(capabilites);
    expect(result).to.be.equal(false);
    capabilites = {firstMatch: [{}]}
    result = checkProxyForwarding(capabilites);
    expect(result).to.be.equal(false);
  })

  it('should return true when browser is chrome & version is between 61 to 70', () => {
    let capabilites = {firstMatch: [{browserName: 'Chrome', browserVersion: "64.0"}]};
    let result = checkProxyForwarding(capabilites);
    expect(result).to.be.equal(true);

    capabilites = {firstMatch: [{browserName: 'Chrome', browserVersion: "61.0"}]};
    result = checkProxyForwarding(capabilites);
    expect(result).to.be.equal(true);

    capabilites = {firstMatch: [{browserName: 'Chrome', browserVersion: "70.0"}]};
    result = checkProxyForwarding(capabilites);
    expect(result).to.be.equal(true);
  })

  it('should return false when browser is chrome & version is not between 61 to 70', () => {
    let capabilites = {firstMatch: [{browserName: 'Chrome', browserVersion: "60.0"}]};
    let result = checkProxyForwarding(capabilites);
    expect(result).to.be.equal(false);

    capabilites = {firstMatch: [{browserName: 'Chrome', browserVersion: "71.0"}]};
    result = checkProxyForwarding(capabilites);
    expect(result).to.be.equal(false);
  })

  it('should return true when browser is firefox', () => {
    let capabilites = {firstMatch: [{browserName: 'Firefox', browserVersion: '74.0'}]};
    let result = checkProxyForwarding(capabilites);
    expect(result).to.be.equal(false);
  })

  it('should return true when browser is edgeChromium', () => {
    let capabilites = {firstMatch: [{browserName: 'Edge', browserVersion: '85.0'}]};
    let result = checkProxyForwarding(capabilites);
    expect(result).to.be.equal(false);
  })

  it('should return false when browser is safari', () => {
    let capabilites = {firstMatch: [{browserName: 'safari', browserVersion: '13'}]};
    let result = checkProxyForwarding(capabilites);
    expect(result).to.be.equal(false);
  })

  describe('#chromeOptionsPrefs', () => {
    it('should add modify chrome options with newPrefs', () => {
      const chromeOptionsPrefs = browserstackRewire.__get__('chromeOptionsPrefs');
      let prefs = {
        'credentials_enable_service': true,
        'password_manager_enabled': true,
        'safebrowsing': {'enabled': true},
        'intl': {'accept_languages': []},
        'plugins': {
          'always_open_pdf_externally': true,
          'plugins_disabled' : []
        }
      }
      let lcaps = {'chromeOptions': {'prefs': prefs}, 'browserName': 'chrome', 'version': '89.0'}
      chromeOptionsPrefs(lcaps)
      expect(lcaps.chromeOptions.prefs).to.have.any.keys('credentials_enable_service', 'password_manager_enabled', 'plugins', 'safebrowsing')
      expect(lcaps.chromeOptions.prefs).to.not.have.all.keys('shown_close_prompt_promotion')
    })

    it('should add modify edge 91 beta options with newPrefs', () => {
      const chromeOptionsPrefs = browserstackRewire.__get__('chromeOptionsPrefs');
      let prefs = {
        'credentials_enable_service': true,
        'password_manager_enabled': true,
        'safebrowsing': {'enabled': true},
        'intl': {'accept_languages': []},
        'plugins': {
          'always_open_pdf_externally': true,
          'plugins_disabled' : []
        }
      }
      let lcaps = {'chromeOptions': {'prefs': prefs}, 'browserName': 'edge', 'version': '91.0 beta'}
      chromeOptionsPrefs(lcaps)
      expect(lcaps.chromeOptions.prefs).to.have.any.keys('credentials_enable_service', 'password_manager_enabled', 'plugins', 'safebrowsing', 'shown_close_prompt_promotion')
    })

    it('should add modify edge 114 options with newPrefs', () => {
      const chromeOptionsPrefs = browserstackRewire.__get__('chromeOptionsPrefs');
      let prefs = {
        'credentials_enable_service': true,
        'password_manager_enabled': true,
        'safebrowsing': {'enabled': true},
        'intl': {'accept_languages': []},
        'plugins': {
          'always_open_pdf_externally': true,
          'plugins_disabled' : []
        }
      }
      let lcaps = {'chromeOptions': {'prefs': prefs}, 'browserName': 'edge', 'version': '114'}
      chromeOptionsPrefs(lcaps)
      expect(lcaps.chromeOptions.prefs).to.have.any.keys('credentials_enable_service', 'password_manager_enabled', 'plugins', 'safebrowsing', 'shown_close_prompt_promotion', 'user_experience_metrics')
    })

    it('should add modify edge 118 and above with options to disable popup', () => {
      const chromeOptionsPrefs = browserstackRewire.__get__('chromeOptionsPrefs');
      let prefs = {
        'credentials_enable_service': true,
        'password_manager_enabled': true,
        'safebrowsing': {'enabled': true},
        'intl': {'accept_languages': []},
        'plugins': {
          'always_open_pdf_externally': true,
          'plugins_disabled' : []
        }
      }
      let lcaps = {'chromeOptions': {'prefs': prefs}, 'browserName': 'edge', 'version': '119'}
      chromeOptionsPrefs(lcaps)
      expect(lcaps.chromeOptions.prefs).to.have.any.keys('credentials_enable_service', 'password_manager_enabled', 'plugins', 'safebrowsing', 'shown_close_prompt_promotion', 'user_experience_metrics', 'third_party_search')
    })
  })
})

describe('validateCustomMobileStartSessionTimeout', () => {
  let validateCustomMobileStartSessionTimeout;
  before(() => {
    validateCustomMobileStartSessionTimeout = browserstackRewire.__get__("validateCustomMobileStartSessionTimeout");
  })
  it('should return the default timeout if realMobile is falsy', () => {
    const realMobile = false;
    const customMobileStartSessionTimeout = 10000;
    const timeout = 5000;
    const result = validateCustomMobileStartSessionTimeout(realMobile, customMobileStartSessionTimeout, timeout);
    expect(result).to.equal(timeout);
  });

  it('should return the default timeout if customMobileStartSessionTimeout is undefined', () => {
    const realMobile = true;
    const customMobileStartSessionTimeout = undefined;
    const timeout = 5000;
    const result = validateCustomMobileStartSessionTimeout(realMobile, customMobileStartSessionTimeout, timeout);
    expect(result).to.equal(timeout);
  });

  it('should return the customMobileStartSessionTimeout if it is defined and realMobile is truthy', () => {
    const realMobile = true;
    const customMobileStartSessionTimeout = 10000;
    const timeout = 5000;
    const result = validateCustomMobileStartSessionTimeout(realMobile, customMobileStartSessionTimeout, timeout);
    expect(result).to.equal(customMobileStartSessionTimeout);
  });

  it('should return the maximum of the customMobileStartSessionTimeout and 5000 if both are defined and realMobile is truthy', () => {
    const realMobile = true;
    const customMobileStartSessionTimeout = 3000;
    const timeout = 5000;
    const result = validateCustomMobileStartSessionTimeout(realMobile, customMobileStartSessionTimeout, timeout);
    expect(result).to.equal(5000);
  });
});

describe('#enableHighContrast', () => {
  let enableHighContrast;
  before(() => {
    enableHighContrast = browserstackRewire.__get__("enableHighContrast");
  })

  const orig_os_windows_enabled = ["win7", "win8", "win8.1", "win10", "win11"];
  orig_os_windows_enabled.forEach((orig_os) => {
    it(`should return true for orig_os ${orig_os} with high contrast cap`, ()=> {
      let options = {bsCaps: {orig_os}, browserstackParams: {"browserstack.highContrast": "true"}};
      expect(enableHighContrast(options)).to.be.equal(true);
      options = {orig_os, bsCaps: {orig_os}, browserstackParams: {"browserstack.high_contrast": "true"}};
      expect(enableHighContrast(options)).to.be.equal(true);
    })
  })

  const orig_os_others = ["winxp", "macsl", "maclion", "macml", "macmav", "macyos", "macelc", "macsie", "machs", "macmo", "maccat", "macbsr", "macmty", "macven", "macson", "macsqa"]
  orig_os_others.forEach((orig_os) => {
    it(`should return false for orig_os ${orig_os} with high contrast cap`, ()=> {
      let options = {bsCaps: {orig_os}, browserstackParams: {"browserstack.highContrast": "true"}};
      expect(enableHighContrast(options)).to.be.equal(false);
      options = {bsCaps:{orig_os}, browserstackParams: {"browserstack.high_contrast": "true"}};
      expect(enableHighContrast(options)).to.be.equal(false);
    })
  })

  describe('getBrowserPath', () => {
    let getBrowserPath;
    before(() => {
      getBrowserPath = browserstackRewire.__get__("getBrowserPath");
    })

    it('should return browser path for chrome', () => {
      const lcaps = { orig_os: "win11", browserName: "chrome", "browserstack.edgeVersion": "117.0" };
      const browserObj = constants.browserConfig[lcaps.browserName][0];
      expectedPath = getBrowserPath(lcaps, browserObj);
      expect(expectedPath).to.include("Chrome");
    });

    it('should return browser path for chrome', () => {
      const lcaps = { orig_os: "macbsr", browserName: "chrome", "browserstack.edgeVersion": "117.0" };
      const browserObj = constants.browserConfig[lcaps.browserName][0];
      expectedPath = getBrowserPath(lcaps, browserObj);
      expect(expectedPath).to.include("Chrome");
    });

    it('should return browser path for ie', () => {
      const lcaps = { orig_os: "win11", browserName: "ie", "browserstack.edgeVersion": "117.0" };
      const browserObj = constants.browserConfig[lcaps.browserName][0];
      expectedPath = getBrowserPath(lcaps, browserObj);
      expect(expectedPath).to.include("Internet Explorer");
    });

    it('should return browser path for edge', () => {
      const lcaps = { orig_os: "win11", browserName: "edge", "browserstack.edgeVersion": "117.0" };
      const n = constants.browserConfig[lcaps.browserName].length;
      const browserObj = constants.browserConfig[lcaps.browserName][n-1];
      expectedPath = getBrowserPath(lcaps, browserObj);
      expect(expectedPath).to.include("Edge");
    });

    it('should return browser path for edge when 64bit is true', () => {
      const lcaps = { orig_os: "win11", browserName: "edge", "browserstack.edgeVersion": "117.0", "64bit": true };
      const n = constants.browserConfig[lcaps.browserName].length;
      const browserObj = constants.browserConfig[lcaps.browserName][n-1];
      expectedPath = getBrowserPath(lcaps, browserObj);
      expect(expectedPath).to.include("Edge");
      expect(expectedPath).to.include("Program Files (x86)");
    });
  })

  describe('addEdgeDriverPathForIEOnEdgeMode', () => {
    let addEdgeDriverPathForIEOnEdgeMode;
    before(() => {
      addEdgeDriverPathForIEOnEdgeMode = browserstackRewire.__get__("addEdgeDriverPathForIEOnEdgeMode");
    })

    it('should return browser path for IE on edge mode', () => {
      const lcaps = { orig_os: "win11", browserName: "edge", "browserstack.edgeVersion": "117.0" };
      const browserstackParams = { "browserstack.edge.driver": "117.0" };

      addEdgeDriverPathForIEOnEdgeMode(lcaps, browserstackParams);
      expect(lcaps["se:ieOptions"]["ie.edgepath"]).to.include("Edge");
    });

    it('should not return browser path for IE on edge mode', () => {
      const lcaps = { orig_os: "win11", browserName: "edge", "se:ieOptions": {} };
      const browserstackParams = { "browserstack.edge.driver": "117.0" };

      addEdgeDriverPathForIEOnEdgeMode(lcaps, browserstackParams);
      expect(lcaps["se:ieOptions"]["ie.edgepath"]).to.be.undefined;
    });
  })

  describe('#add_rails_omitted_caps', () => {
    let add_rails_omitted_caps;
    before(() => {
      add_rails_omitted_caps = browserstackRewire.__get__('add_rails_omitted_caps');
    })

    it('return caps when rails omitted caps are empty', () => {
      let caps = {browser: 'chrome', browser_version: '85.0', os: 'windows', os_version: '10'};
      let result = add_rails_omitted_caps(caps, undefined);
      expect(JSON.stringify(caps) == JSON.stringify(result)).to.eq(true);
    })

    it('should add firefox options if present', () => {
      let caps = {browser: 'firefox', browser_version: '80.0', os: 'windows', os_version: '10'};
      let rails_omitted_caps = {mozfirefoxProfile: {}};
      let result = add_rails_omitted_caps(caps, rails_omitted_caps);
      expect(result).to.have.all.keys(['browser', 'browser_version', 'os', 'os_version', 'moz:firefoxOptions']);
    })

    it('should add chrome options if present', () => {
      let caps = {browser: 'chrome', browser_version: '85.0', os: 'windows', os_version: '10'};
      let rails_omitted_caps = {chromeExtension: ['ZXh0ZW5zaW9u']};
      let result = add_rails_omitted_caps(caps, rails_omitted_caps);
      expect(result).to.have.all.keys(['browser', 'browser_version', 'os', 'os_version', 'chromeOptions']);
      expect(result.chromeOptions).to.have.property('extensions').that.deep.equals(['ZXh0ZW5zaW9u']);
    })

    it('should not add chrome extensions options if present empty string', () => {
      let caps = {browser: 'chrome', browser_version: '85.0', os: 'windows', os_version: '10'};
      let rails_omitted_caps = { chromeExtension: [''] };
      let result = add_rails_omitted_caps(caps, rails_omitted_caps);
      expect(result).to.have.all.keys(['browser', 'browser_version', 'os', 'os_version', 'chromeOptions']);
      expect(result.chromeOptions).to.have.property('extensions').that.is.an('array').with.lengthOf(0);
    })

    it('should not add chrome extensions options if present boolean values', () => {
      let caps = {browser: 'chrome', browser_version: '85.0', os: 'windows', os_version: '10'};
      let rails_omitted_caps = { chromeExtension: [true] };
      let result = add_rails_omitted_caps(caps, rails_omitted_caps);
      expect(result).to.have.all.keys(['browser', 'browser_version', 'os', 'os_version', 'chromeOptions']);
      expect(result.chromeOptions).to.have.property('extensions').that.is.an('array').with.lengthOf(0);
    })

    it('should not add chrome extensions options if present boolean values', () => {
      let caps = {browser: 'chrome', browser_version: '85.0', os: 'windows', os_version: '10'};
      let rails_omitted_caps = { chromeExtension: [false] };
      let result = add_rails_omitted_caps(caps, rails_omitted_caps);
      expect(result).to.have.all.keys(['browser', 'browser_version', 'os', 'os_version', 'chromeOptions']);
      expect(result.chromeOptions).to.have.property('extensions').that.is.an('array').with.lengthOf(0);
    })
  })

  describe('#modify_safari_caps', () => {
    let modify_safari_caps;
    before(() => {
      modify_safari_caps = browserstackRewire.__get__('modify_safari_caps');
    })

    it('should add safari.options if not present', () => {
      let lcaps = {browserName: 'safari', browserVersion: '13'};
      let browserstackParams = {'browserstack.safari.enablePopups': true};
      modify_safari_caps(lcaps, browserstackParams);
      expect(lcaps["safari.options"]).to.be.an('object');
      expect(lcaps["safari.options"].cleanSession).to.be.true;
    })
  })

  describe('#modify_ie_caps', () => {
    let modify_ie_caps;
    before(() => {
      modify_ie_caps = browserstackRewire.__get__('modify_ie_caps');
    })

    it('should remove acceptInsecureCerts and assign to acceptSslCerts', () => {
      let lcaps = {acceptInsecureCerts : true};
      let result = modify_ie_caps(lcaps);
      expect(result.acceptSslCerts).to.eq(true);
    })

    it('should add ie info to w3c first match', () => {
      let lcaps = {W3C_capabilities: {firstMatch : [{}]}};
      let result = modify_ie_caps(lcaps);
      expect(result).to.be.an('object');
      expect(result.W3C_capabilities).to.be.an('object');
      expect(result.W3C_capabilities.firstMatch).to.be.an('array');
      expect(result.W3C_capabilities.firstMatch.length > 0).to.eq(true);
      expect(result.W3C_capabilities.firstMatch[0]).to.have.all.keys(['browserName', 'se:ieOptions']);
    })
  })

  describe('fixChrome', () => {
    let fixChrome;
    before(() => {
      fixChrome = browserstackRewire.__get__('fixChrome');
    })

    it('should set chromeOptions prefs when version is greater than 28', () => {
      const lcaps = { chromeOptions: {}, version: '29' };
      const browserstackParams = {};
      fixChrome(lcaps, browserstackParams);
      expect(lcaps.chromeOptions.prefs).to.exist;
    });

    it('should convert args to array if it is a string and version is greater than 34', () => {
      const lcaps = { chromeOptions: { args: 'test-arg' }, version: '35' };
      const browserstackParams = {};
      fixChrome(lcaps, browserstackParams);
      expect(lcaps.chromeOptions.args).to.be.an('array').that.includes('test-arg');
    });

    it('should add specific args when browserstack.noPipeline is undefined or false', () => {
      const lcaps = { chromeOptions: { args: [] }, version: '35' };
      const browserstackParams = { 'browserstack.noPipeline': 'false' };
      fixChrome(lcaps, browserstackParams);
      expect(lcaps.chromeOptions.args).to.include('--disable-application-cache');
    });

    it('should add remote-debugging-port arg when networkLogsV2 or performance is true', () => {
      const lcaps = { chromeOptions: { args: [] }, version: '35' };
      const browserstackParams = { 'browserstack.networkLogsV2': 'true' };
      fixChrome(lcaps, browserstackParams);
      expect(lcaps.chromeOptions.args).to.include('--remote-debugging-port=9222');
    });

    it('should modify perfLoggingPrefs when version is greater than 41', () => {
      const lcaps = { chromeOptions: { perfLoggingPrefs: { enableTimeline: true } }, version: '42' };
      const browserstackParams = {};
      fixChrome(lcaps, browserstackParams);
      expect(lcaps.chromeOptions.perfLoggingPrefs.traceCategories).to.equal('blink.console,disabled-by-default-devtools.timeline');
    });

    it('should add ignore-certificate-errors arg when version is greater than or equal to 79 and acceptSslCerts is true', () => {
      const lcaps = { chromeOptions: { args: [] }, version: '79', acceptSslCerts: 'true' };
      const browserstackParams = {};
      fixChrome(lcaps, browserstackParams);
      expect(lcaps.chromeOptions.args).to.include('--ignore-certificate-errors');
    });

    it('should filter out --headless=old arg when version is greater than or equal to 132', () => {
      const lcaps = { chromeOptions: { args: ['--headless=old'] }, version: '132' };
      const browserstackParams = {};
      fixChrome(lcaps, browserstackParams);
      expect(lcaps.chromeOptions.args).to.not.include('--headless=old');
    });

    it('should delete perfLoggingPrefs when version is less than 36', () => {
      const lcaps = { chromeOptions: { perfLoggingPrefs: {} }, version: '35' };
      const browserstackParams = {};
      fixChrome(lcaps, browserstackParams);
      expect(lcaps.chromeOptions.perfLoggingPrefs).to.be.undefined;
    });

    it('should add user-data-dir arg when version is between 22 and 25 and platform is Windows', () => {
      const lcaps = { chromeOptions: { args: [] }, version: '23', platform: 'Windows' };
      const browserstackParams = {};
      fixChrome(lcaps, browserstackParams);
      expect(lcaps.chromeOptions.args).to.include('user-data-dir=C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\User Data 23');
    });

    it('should add --disable-features=DisableLoadExtensionCommandLineSwitch arg when version is 137 and above', () => {
      const lcaps = { chromeOptions: { args: [] }, version: '137', platform: 'Windows' };
      const browserstackParams = {};
      fixChrome(lcaps, browserstackParams);
      expect(lcaps.chromeOptions.args).to.include('--disable-features=DisableLoadExtensionCommandLineSwitch');
    });
  });

  describe('#fixEdge', () => {
    let fixEdge;
    before(() => {
      fixEdge = browserstackRewire.__get__('fixEdge');
    })

    it('should add args in chromeOptions to disable sidebar for Edge 106+, OS X if enableSidebar is set to false', () => {
      let lcaps = {orig_os : "mac", chromeOptions: { prefs: { browser: {}}}, browserName: "Edge", version: 107};
      let browserstackParams = {'browserstack.edge.enableSidebar': false};
      fixEdge(lcaps, browserstackParams);
      expect(lcaps.chromeOptions.args[0]).to.be.equal('--disable-features=msHubApps');
      expect(lcaps.chromeOptions.prefs.browser).to.deep.equal({show_hub_apps_tower: false, show_hub_apps_tower_pinned: false});
    })

    it('should add args in chromeOptions to disable sidebar for Edge 106+, OS X if enableSidebar is not set', () => {
      let lcaps = {orig_os : "mac", chromeOptions: { prefs: { browser: {}}}, browserName: "Edge", version: 109};
      let browserstackParams = {};
      fixEdge(lcaps, browserstackParams);
      expect(lcaps.chromeOptions.args[0]).to.be.equal('--disable-features=msHubApps');
    })

    it('should not add args in chromeOptions to disable sidebar if enableSidebar is set to true', () => {
      let lcaps = {orig_os : "mac", chromeOptions: {}, browserName: "Edge", version: 107};
      let browserstackParams = {'browserstack.edge.enableSidebar': true};
      fixEdge(lcaps, browserstackParams);
      expect(lcaps.chromeOptions).to.eql({});
    })

    it('should not add args in chromeOptions to disable sidebar for Edge 106+, OS X if version < 106', () => {
      let lcaps = {orig_os : "mac", chromeOptions: {}, browserName: "Edge", version: 105};
      let browserstackParams = {'browserstack.edge.enableSidebar': false};
      fixEdge(lcaps, browserstackParams);
      expect(lcaps.chromeOptions).to.eql({});
    })

    it('should add disable-sync option if edge version is 119 beta+ for mac', () => {
      let lcaps = {orig_os : "mac", chromeOptions: { prefs: { browser: {}}}, browserName: "Edge", version: 119};
      fixEdge(lcaps, {});
      expect(lcaps.chromeOptions.excludeSwitches).to.eql(["disable-sync"]);
    })

    it('should add disable-sync option if edge version is 119+ for win', () => {
      let lcaps = {orig_os : "win", chromeOptions: { prefs: { browser: {}}}, browserName: "Edge", version: 119};
      fixEdge(lcaps, {});
      expect(lcaps.chromeOptions.excludeSwitches).to.eql(["disable-sync"]);
    })

    it('should not add disable-sync option if edge version is less than 119 for mac', () => {
      let lcaps = {orig_os : "mac", chromeOptions: { prefs: { browser: {}}}, browserName: "Edge", version: 118};
      fixEdge(lcaps, {});
      expect(lcaps.chromeOptions.excludeSwitches).to.eql(undefined);
    })

    it('should not add disable-sync option if edge version is less than 119 for win', () => {
      let lcaps = {orig_os : "win", chromeOptions: { prefs: { browser: {}}}, browserName: "Edge", version: 118};
      fixEdge(lcaps, {});
      expect(lcaps.chromeOptions.excludeSwitches).to.eql(undefined);
    })
  })

  describe('#modify_firefox_caps', () => {
    let modify_firefox_caps;
    before(() => {
      modify_firefox_caps = browserstackRewire.__get__('modify_firefox_caps');
    })

    it('should remove proxy and remove unexpectedAlertBehaviour', () => {
      let lcaps = {version : '53', unexpectedAlertBehaviour: true, proxy: {}};
      let browserstackParams = {'browserstack.selenium.jar.version': '3.141.59', 'browserstack.video.filename': 'filename'};
      modify_firefox_caps(lcaps, browserstackParams);
      expect(lcaps).to.not.have.any.keys(['proxy', 'unexpectedAlertBehaviour']);
    })
  })

  describe('#addHeadlessOptions', () => {
    let addHeadlessOptions;
    before(() => {
      addHeadlessOptions = browserstackRewire.__get__('addHeadlessOptions');
    })

    it('should add chromeOptions', () => {
      let lcaps = {};
      let browserstackParams = {'browserstack.headless': 'true'};
      let browserName = 'CHROME';
      addHeadlessOptions(lcaps, browserstackParams, browserName);
      expect(lcaps).to.have.all.keys(['chromeOptions']);
    })
  })

  describe('#fixLocalParams', () => {
    let fixLocalParams, exceptionLogger;
    before(() => {
      fixLocalParams = browserstackRewire.__get__('fixLocalParams');
      exceptionLogger = sinon.stub(HubLogger, "exceptionLogger");
    })

    after(() => {
      exceptionLogger.restore();
    })

    it('should remove proxy and remove unexpectedAlertBehaviour', () => {
      let browserstackParams = {'local_params': "random"};
      fixLocalParams(browserstackParams);
      expect(exceptionLogger.called).to.eq(true);
    })
  })

  describe('#postBrowserstack', () => {
    describe('#markAsPercy request', () => {
      let bsErrorHandlerSpy;
      before(() => {
        bsErrorHandlerSpy = sinon.stub(HubLogger, "bsErrorHandler");
      })

      after(() => {
        bsErrorHandlerSpy.restore();
      })

      it('should return failed if attempt number is > 3', () => {
        callback = sinon.stub();
        error = 'Some error'
        browserstack.postBrowserStack('&mark_as_percy=true',
          {}, null, null,
          callback, // callback
          null, null,
          3, // attempt
          null, null,
          error, // error
          null,
          { attempt: 3 } // retry
        );
        expect(bsErrorHandlerSpy.called).to.eq(true);
        sinon.assert.called(callback)
        sinon.assert.calledWith(
          callback,
          JSON.stringify({ success: false, message: error })
        )
      })
    })

    describe('#set max_firecmd_timeout request', () => {
      let reqLibStub;
      let requestRailsStub;
      let mocklogger;
      let sandbox;
      let options;

      beforeEach(() => {
        sandbox = sinon.sandbox.create();

        let callData = {
          ip: "127.0.0.1",
          created_at: "now"
        };

        options = {
          request: {
            method: "POST",
            url: "/session/abcd-efgh-ijkl-mnop-qrst-uvwx-yz/active",
            checkedActive: false,
            log_data: "log_data",
            headers: { "x-forwarded-for": "127.0.0.1" },
            start_up_request_time: "now",
            session_created_at: "now",
            firecmd_time: "now",
          },
          response: null,
          post_data:
            '{"desiredCapabilities":{"realMobile":true,"orig_os":"","platformName":"ios","appium:settings[waitForIdleTimeout]":"2000","appium:deviceName":"iPhone XS","appium:platformVersion":"15.3","app":"bs://b97182d72981036c53c17b29f34219452a8fcc34","mobile":{"version":"Iphone 13 -15.0"},"isRealMobile":true,"acceptSslCert":false,"acceptSslCerts":false}}',
          bsCaps: {
            realMobile: true,
            app_automate_custom_params: { max_firecmd_timeout: 61 },
            orig_os: "",
            platformName: "ios",
            "appium:settings[waitForIdleTimeout]": "2000",
            "appium:deviceName": "iPhone XS",
            "appium:platformVersion": "15.3",
            app: "bs://b97182d72981036c53c17b29f34219452a8fcc34",
            mobile: { version: "Iphone 13 -15.0" },
          },
          browserstackParams: {
            "browserstack.appDownloadTimeout": 100000000,
            "browserstack.headless": false,
            "browserstack.projectName": "wdio AA Non-SDK Project",
            "browserstack.buildName": "WDIO: Appium",
            "browserstack.debug": true,
            "browserstack.appiumVersion": "1.18.0",
            "browserstack.sessionName": "gps location spec",
            "browserstack.gpsLocation": "18,72",
            "browserstack.deviceLogs": true,
            "browserstack.networkLogs": "true",
            "browserstack.ie.noFlash": "false",
          },
          post_params: {
            framework: "selenium",
            isAppAutomate: true,
            pipelineIdentifier: "2a510243ed9cd4bc4fa816eba044001510836138",
            selauth_request_ever_queued: false,
          },
          host_name: "127.0.0.1",
          rproxyHost: "127.0.0.1",
          callback: sandbox.stub(),
          url: "",
          port: 5555,
          attempt: 1,
          mobile: { version: "Iphone 13 -15.0" },
          rails_omitted_caps: { isRealMobile: true },
          indexCounter: 0,
          sessionId: "",
          rails_session_id: "",
          realMobile: true,
          terminal_type: "realMobile",
          proxy_type: undefined,
          automation_session_id: undefined,
          build_hash: undefined,
          collection_number: undefined,
          user_id: undefined,
          group_id: undefined,
          group_plan_type: undefined,
          privoxy_domain_control_flag: undefined,
          group_risk_bucket: undefined,
          bundle_id_block_flag: undefined,
          ios_msg_logging_flag: undefined,
          iosVersion: undefined,
          has_edge_extension: false,
          shouldForceChangeJar: false,
          avoidAppium: true,
          start_session_retry: 0,
          appTesting: "bs://b97182d72981036c53c17b29f34219452a8fcc34",
          originRegion: "us-east-1",
          prev_session_id: undefined,
          request_count: 0,
          doNotSetProxy: false,
          hoothootCanaryTags: undefined,
          platformDetails: {
            platformName: undefined,
            platformVersion: undefined,
          },
          browserVersion: null,
          browserName: "ANY",
        };

        fireCommands = browserstackRewire.__get__("fireCommands");

        requestRailsStub = sandbox.stub(railsPipeline, "railsRequestPipeline").returns(
          new Promise((resolve) => {
            resolve(false);
          })
        );
        reqLibStub = sandbox.stub(requestlib, "call")
        .returns(
          new Promise((resolve) => {
            resolve({ statusCode: 200, data: JSON.stringify(callData) });
          })
        );
        mocklogger = sandbox.stub(HubLogger, 'miscLogger');
      });

      afterEach(()=>{
        sandbox.restore();
      });

      it('uses  max_firecmd_timeout from app_automate_custom_params', function () {
        var params = helper.getParamsObject();
        params.response["clientOnline"] = function () {};
        options.response = params.response
        fireCommands(options, sinon.stub().returns(true), null);
        expect(mocklogger.calledWith('fireCommands', 'Setting max_firecmd_timeout as 1', 4)).to.eq(true);
      });

    })
  })

  describe('check_edge_extension_sel3', () => {
    it('returns false if rails omited caps not there', () => {
      const res = browserstack.check_edge_extension_sel3({})
      expect(res).to.eq(false)
    });

    it('returns false if not chrome extension', () => {
      const res = browserstack.check_edge_extension_sel3({}, {'random': 123})
      expect(res).to.eq(false)
    });

    it('returns true if edge chromium with extension', () => {
      const res = browserstack.check_edge_extension_sel3({browserName: 'Edge', browser_version: 100}, {'chromeExtension': 123})
      expect(res).to.eq(true)
    });
  });

  describe('#removeChromeOptionsDownloadLocation', () => {
    let removeChromeOptionsDownloadLocation;
    let chromeOptionsCaps1;
    let chromeOptionsCaps2;
    let googChromeOptionsCaps1;
    let googChromeOptionsCaps2;
    let w3cCaps;
    before(() => {
      w3cCaps = {firstMatch: [{browserName:"chrome", "goog:chromeOptions":{prefs:{"download.default_directory":"c:\\Users\\<USER>\\Downloads"}, w3c:true}}]};
      chromeOptionsCaps1 = { chromeOptions: { prefs: { download: { default_directory: "test" }}}, browserName: "chrome", W3C_capabilities: w3cCaps};
      chromeOptionsCaps2 = { chromeOptions: { prefs: { "download.default_directory": "test" }}, browserName: "chrome", W3C_capabilities: w3cCaps};
      googChromeOptionsCaps1 = { "goog:chromeOptions": { prefs: { download: { default_directory: "test"}}}, browserName: "chrome", W3C_capabilities: w3cCaps};
      googChromeOptionsCaps2 = { "goog:chromeOptions": { prefs: { "download.default_directory": "test" }}, browserName: "chrome", W3C_capabilities: w3cCaps};
      removeChromeOptionsDownloadLocation = browserstackRewire.__get__('removeChromeOptionsDownloadLocation');
    })

    it('should delete default_directory from chromeOptions of chromeOptionsCaps1', () => {
      removeChromeOptionsDownloadLocation(chromeOptionsCaps1);
      expect(Object.keys(chromeOptionsCaps1.chromeOptions.prefs.download).length).to.be.equal(0);
      expect(chromeOptionsCaps1.W3C_capabilities["firstMatch"][0]["goog:chromeOptions"].prefs["download.default_directory"]).to.be.equal(undefined);
    })

    it('should delete default_directory from goog:chromeOptions of chromeOptionsCaps2', () => {
      removeChromeOptionsDownloadLocation(chromeOptionsCaps2);
      expect(chromeOptionsCaps2.chromeOptions.prefs["download.default_directory"]).to.be.equal(undefined);
      expect(chromeOptionsCaps2.W3C_capabilities["firstMatch"][0]["goog:chromeOptions"].prefs["download.default_directory"]).to.be.equal(undefined);
    })

    it('should delete default_directory from chromeOptions of googChromeOptionsCaps1', () => {
      removeChromeOptionsDownloadLocation(googChromeOptionsCaps1);
      expect(Object.keys(googChromeOptionsCaps1["goog:chromeOptions"].prefs.download).length).to.be.equal(0);
      expect(googChromeOptionsCaps1.W3C_capabilities["firstMatch"][0]["goog:chromeOptions"].prefs["download.default_directory"]).to.be.equal(undefined);
    })

    it('should delete default_directory from goog:chromeOptions of googChromeOptionsCaps2', () => {
      removeChromeOptionsDownloadLocation(googChromeOptionsCaps2);
      expect(googChromeOptionsCaps2["goog:chromeOptions"].prefs["download.default_directory"]).to.be.equal(undefined);
      expect(googChromeOptionsCaps2.W3C_capabilities["firstMatch"][0]["goog:chromeOptions"].prefs["download.default_directory"]).to.be.equal(undefined);
    })

    it('should delete default_directory from goog:chromeOptions of googChromeOptionsCaps2', () => {
      googChromeOptionsCaps2["goog:chromeOptions"].prefs["download"] = {"default_directory": "dir"};
      googChromeOptionsCaps2.W3C_capabilities["firstMatch"][0]["goog:chromeOptions"].prefs["download"] = {"default_directory": "dir"};
      removeChromeOptionsDownloadLocation(googChromeOptionsCaps2);
      expect(googChromeOptionsCaps2["goog:chromeOptions"].prefs["download"]["default_directory"]).to.be.equal(undefined);
      expect(googChromeOptionsCaps2.W3C_capabilities["firstMatch"][0]["goog:chromeOptions"].prefs["download"]["default_directory"]).to.be.equal(undefined);

    })
  })
})


describe('checkRproxy5xxError', () => {
  let startOptionsClone;
  let options;
  before(() => {
    startOptionsClone = {
      hostname: 'rproxy.example.com'
    };
    options = {
      browserstackParams: { "browserstack.terminal_sub_region": "us-east-1" },
    };
  })
  it('should return true when status code is 5xx and response data includes "nginx"', function() {
    const response = {
      statusCode: 502,
      data: 'nginx error'
    };
    const result = browserstack.checkRproxy5xxError(response, startOptionsClone, options);
    assert.strictEqual(result, true);
  });

  it('should return false when status code is not 5xx', function() {
    const response = {
      statusCode: 404,
      data: 'nginx error'
    };
    const result = browserstack.checkRproxy5xxError(response, startOptionsClone, options);
    assert.strictEqual(result, false);
  });

  it('should return false when response data does not include "nginx"', function() {
    const response = {
      statusCode: 502,
      data: 'some other error'
    };
    const result = browserstack.checkRproxy5xxError(response, startOptionsClone, options);
    assert.strictEqual(result, false);
  });

  it('should return false when response data includes "nginx" for app automate and mobile', function() {
    const response = {
      statusCode: 502,
      data: 'nginx error'
    };
    options.post_params = { isAppAutomate: true };
    options.realMobile = true;
    const result = browserstack.checkRproxy5xxError(response, startOptionsClone, options);
    assert.strictEqual(result, true);
  });
});

describe('#addOrUpdateUrlToProxyBypassArg', () => {
  let addOrUpdateUrlToProxyBypassArg;
  before(() => {
    addOrUpdateUrlToProxyBypassArg = browserstackRewire.__get__("addOrUpdateUrlToProxyBypassArg");
  })

  it('should add proxy bypass list when chromeOptions args does not exist', () => {
    const caps = {
      'chromeOptions': {}
    };
    addOrUpdateUrlToProxyBypassArg(caps, 'test.com');
    expect(caps.chromeOptions.args).to.be.an('array');
    expect(caps.chromeOptions.args[0]).to.equal('--proxy-bypass-list=test.com');
  });

  it('should replace <-loopback> with new value', () => {
    const caps = {
      'chromeOptions': {
        'args': ['--proxy-bypass-list=<-loopback>']
      }
    };
    addOrUpdateUrlToProxyBypassArg(caps, 'test.com');
    expect(caps.chromeOptions.args[0]).to.equal('--proxy-bypass-list=<-loopback>;test.com');
  });

  it('should append new value to existing proxy bypass list', () => {
    const caps = {
      'chromeOptions': {
        'args': ['--proxy-bypass-list=existing.com']
      }
    };
    addOrUpdateUrlToProxyBypassArg(caps, 'test.com');
    expect(caps.chromeOptions.args[0]).to.equal('--proxy-bypass-list=existing.com;test.com');
  });

  it('should not add duplicate values to proxy bypass list', () => {
    const caps = {
      'chromeOptions': {
        'args': ['--proxy-bypass-list=test.com']
      }
    };
    addOrUpdateUrlToProxyBypassArg(caps, 'test.com');
    expect(caps.chromeOptions.args[0]).to.equal('--proxy-bypass-list=test.com');
  });

  it('should add proxy bypass list when chromeOptions does not exist', () => {
    const caps = {};
    addOrUpdateUrlToProxyBypassArg(caps, 'test.com');
    expect(caps.chromeOptions.args[0]).to.equal('--proxy-bypass-list=test.com');
  });

  it('should handle multiple comma-separated values in existing list', () => {
    const caps = {
      'chromeOptions': {
        'args': ['--proxy-bypass-list=one.com;two.com']
      }
    };
    addOrUpdateUrlToProxyBypassArg(caps, 'three.com');
    expect(caps.chromeOptions.args[0]).to.equal('--proxy-bypass-list=one.com;two.com;three.com');
  });
});
