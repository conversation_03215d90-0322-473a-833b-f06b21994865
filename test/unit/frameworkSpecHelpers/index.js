'use strict';

const frameworkConstants = require('../../../helpers/customSeleniumHandling/frameworkConstants');

const RANDOM_SESSION_ID = '12345';
const EXECUTE_URL = `/wd/hub/session/${RANDOM_SESSION_ID}/execute`;
const actionPayload = {
  action: 'annotate',
  arguments: {
    type: 'Annotation',
    data: 'randomText',
  }
};

const scriptCommand = `browserstack_executor: ${JSON.stringify(actionPayload)}`;

const requestData = { script: scriptCommand };
const generateRequestObject = () => ({
  clientSessionID: 'somerandomid',
  originalUrl: EXECUTE_URL,
  req_data: JSON.stringify(requestData),
  request: {
    url: EXECUTE_URL,
  },
});

const generateCDPRequestObject = () => ({
  req_data: JSON.stringify(requestData),
});

const TEXT_PAYLOAD = {
  arguments: {
    type: 'Annotation',
    data: 'somerandomtext',
  }
};

module.exports = {
  generateRequestObject,
  generateCDPRequestObject,
  TEXT_PAYLOAD,
  EXECUTE_URL,
  RA<PERSON>OM_SESSION_ID,
};
