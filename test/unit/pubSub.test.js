'use strict';

const rewire = require('rewire');
const sinon = require('sinon');
const redis = require('ioredis');
const constants = require('../../constants');
const helper = require('../../helper');
const Redis = require('ioredis-mock');
const HubLogger = require('../../log');
const assert = require('chai').assert;
const { describe, beforeEach, it } = require('mocha');

const pubSub = rewire('../../pubSub');

describe('pubSub tests', () => {
  let pubSubClientMock;
  let publisherMock;
  let subscriberMock;
  let channelName;
  let sampleData;

  beforeEach(() => {
    publisherMock = new Redis();
    subscriberMock = new Redis();
    pubSubClientMock = {
      finalPublisher: publisherMock,
      subscriber: subscriberMock,
    };
    channelName = 'someChannel';
    sampleData = { key: 'some valid value' };
    // eslint-disable-next-line no-underscore-dangle
    pubSub.__set__('publisher', publisherMock);
    // eslint-disable-next-line no-underscore-dangle
    pubSub.__set__('pubSubClient', pubSubClientMock);
    // eslint-disable-next-line no-underscore-dangle
    pubSub.__set__('subscriber', subscriberMock);
  });

  it('should call publisher with valid args', () => {
    const publisherSpy = sinon.spy(publisherMock, 'publish');
    pubSub.publish(channelName, sampleData);
    assert(publisherSpy.calledOnce === true);
    assert(publisherSpy.args[0][0] === channelName);
    assert(publisherSpy.args[0][1] === JSON.stringify(sampleData));
  });

  it('should call unsubscribe with valid arguments', () => {
    const subscriberSpy = sinon.spy(subscriberMock, 'unsubscribe');
    pubSub.unsubscribe(channelName);
    assert(subscriberSpy.calledOnce === true);
    assert(subscriberSpy.args[0][0] === channelName);
  });

  it('publisher error should be catched in publish', () => {
    const logSpy = sinon.spy(HubLogger, 'exceptionLogger');
    sinon.stub(publisherMock, 'publish').throws('Unable to publish');
    pubSub.publish(channelName, sampleData);
    assert(logSpy.calledOnce === true);
    logSpy.restore();
  });

  it('error callback is logged when publishing failed', () => {
    const logSpy = sinon.spy(HubLogger, 'exceptionLogger');
    sinon.stub(pubSubClientMock.finalPublisher, 'publish', () => {
      publisherMock.emit('error', 'error event in publishing');
      return { then: () => {} };
    });

    pubSub.publish(channelName, sampleData);
    assert(logSpy.calledOnce === true);
    logSpy.restore();
  });

  it('error callback is logged for subsriber method', () => {
    const logSpy = sinon.spy(HubLogger, 'exceptionLogger');
    const PubSubClient = pubSub.__get__('PubSubClient');
    redis.createClient.restore();
    sinon.stub(redis, 'createClient').returns(subscriberMock);
    PubSubClient.createSubscriberClient({});

    sinon.stub(pubSubClientMock.subscriber, 'unsubscribe', () => {
      subscriberMock.emit('error', 'error event in unsubscribing');
      return { then: () => {} };
    });

    pubSub.unsubscribe(channelName);
    assert(logSpy.calledOnce === true);
    logSpy.restore();
  });

  it('should modify update session data to have key codes and return updated data', () => {
    const sample = {
      session: 'test',
      changed: {
        random: 1,
        lastRequestTime: 1234567,
      },
    };

    const updatedData = pubSub.convertKeysToCodes(sample);

    // should be present
    assert(!!updatedData.s);
    assert(!!updatedData.c);
    assert(!!updatedData.c.random);
    assert(!!updatedData.c['0']);

    // should not be present
    assert(!updatedData.session);
    assert(!updatedData.changed);
    assert(!updatedData.c.lastRequestTime);
  });

  it('should use the second client if multi cluster setup', () => {
    const redisMock = new Redis();
    constants.multiRedisClusterSetup = true;
    redis.createClient.restore();
    sinon.stub(redis, 'createClient').returns(redisMock);
    sinon.stub(helper, 'PingZombie');
    pubSub.pubSubClient.setupClients();
    assert(pubSub.pubSubClient._subscriberSecond === redisMock);
    assert(pubSub.pubSubClient._publisherSecond === redisMock);
    assert(pubSub.pubSubClient.publisherSecond === redisMock);
    assert(pubSub.pubSubClient.finalPublisher === redisMock);
    redisMock.emit('close', 'test');
    assert(helper.PingZombie);
    helper.PingZombie.restore();
    constants.multiRedisClusterSetup = false;
  });
});
