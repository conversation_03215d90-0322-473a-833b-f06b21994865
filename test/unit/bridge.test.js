'use strict';

/* eslint-disable no-unused-expressions */

const sinon = require('sinon');
const { assert, expect } = require('chai');
const {
  describe, it, beforeEach, afterEach,
} = require('mocha');
const bridge = require('../../bridge');
const ha = require('../../ha');
const hub = require('../../hub');
const pubsub = require('../../pubSub');
const HubLogger = require('../../log');
const constants = require('../../constants');
const requestLib = require('../../lib/request');
const SeleniumClient = require('../../seleniumClient');
const helper = require('../../helper');
const rewire = require('rewire');
const rewiredBridge = rewire('../../bridge');
const WdaClient = require("../../wdaClient");

describe('basicAuthPopup existence check', () => {
  let keyObject;
  let params;

  beforeEach(() => {
    keyObject = {
      key: '1234',
      rails_session_id: 'abcdefg',
      secondary_state: 'basic-auth_required',
    };

    params = {
      hash: {},
      request: {},
      response: {},
      output: {},
      index_counter: 0,
      callbacks: {},
      hostname: 'hub-cloud',
      originalUrl: 'test.com',
    };

    sinon.stub(ha, 'setData', () => {});
    sinon.stub(pubsub, 'publish', () => {});
    sinon.stub(hub, 'processResponse', () => {});
    sinon.stub(HubLogger, 'seleniumStats', () => {});
  });

  afterEach(() => {
    ha.setData.restore();
    pubsub.publish.restore();
    hub.processResponse.restore();
    HubLogger.seleniumStats.restore();
  });

  it('should send true if state is basic auth required', () => {
    const result = bridge.basicAuthPopupExists(keyObject, params);
    assert(result);
    assert(hub.processResponse.calledOnce);
    assert(HubLogger.seleniumStats.calledOnce);
    sinon.assert.notCalled(ha.setData);
    sinon.assert.notCalled(pubsub.publish);
  });

  it('should update the keyobject when present in timeout registry', () => {
    constants.timeout_registry[keyObject.rails_session_id] = true;
    const result = bridge.basicAuthPopupExists(keyObject, params);
    assert(result);
    assert(ha.setData.calledOnce);
    assert(HubLogger.seleniumStats.calledOnce);
    assert(pubsub.publish.calledOnce);
    assert(hub.processResponse.calledOnce);

    delete constants.timeout_registry[keyObject.rails_session_id];
  });

  it('should return false when secondaryState not set', () => {
    delete keyObject.secondary_state;
    const result = bridge.basicAuthPopupExists(keyObject, params);
    assert(result !== true);

    assert(ha.setData.notCalled);
    assert(pubsub.publish.notCalled);
    assert(hub.processResponse.notCalled);
    assert(HubLogger.seleniumStats.notCalled);
  });
});

describe('resetApp', () => {
  let keyObject;
  let params;
  let resetAppSpy;

  beforeEach(() => {
    keyObject = {
      name: 'test.com',
      port: 9999,
      key: '1234',
      selenium_version: '3.141.59',
      rproxyHost: 'localterminal.com',
      dialect: 'W3C',
    };

    params = {
      clientSessionID: '12345',
      hostname: 'local.com',
      rproxyHost: 'localterminal.com',
    };

    sinon.stub(requestLib, 'call', () => {});
    sinon.stub(HubLogger, 'miscLogger', () => {});
    sinon.stub(HubLogger, 'tempExceptionLogger', () => {});
    sinon.stub(bridge, 'sendResponse', () => {});
    sinon.stub(requestLib, 'appendBStackHostHeader', () => '');

    resetAppSpy = sinon.stub(SeleniumClient.prototype, 'resetApp', () => {
    });
  });

  afterEach(() => {
    requestLib.call.restore();
    HubLogger.miscLogger.restore();
    HubLogger.tempExceptionLogger.restore();
    bridge.sendResponse.restore();
    requestLib.appendBStackHostHeader.restore();
    resetAppSpy.restore();
  });

  it('should catch error while selenium client resets the app', async () => {
    resetAppSpy.restore();
    resetAppSpy = sinon.stub(SeleniumClient.prototype, 'resetApp', () => {
      throw new Error('unable to reset app');
    });
    await bridge.resetApp(keyObject, params);
    assert(HubLogger.tempExceptionLogger.calledOnce);
    assert(JSON.parse(params.data).status === 13);
  });

  it('should be successfully setting data', async () => {
    await bridge.resetApp(keyObject, params);
    assert(HubLogger.tempExceptionLogger.notCalled);
    assert(JSON.parse(params.data).status === 0);
  });

  it('should handle error in request', async () => {
    requestLib.call.restore();
    sinon.stub(requestLib, 'call', () => {
      throw new Error('Unable to request to terminal');
    });

    await bridge.resetApp(keyObject, params);
    assert(HubLogger.tempExceptionLogger.calledOnce);
    assert(JSON.parse(params.data).status === 13);
  });
});

describe('installApp', () => {
  let keyObject;
  let params;

  beforeEach(() => {
    keyObject = {
      name: 'test.com',
      port: 9999,
      key: '1234',
      selenium_version: '3.141.59',
      rproxyHost: 'localterminal.com',
      dialect: 'W3C',
      idle_timeout: '90'
    };

    params = {
      clientSessionID: '12345',
      hostname: 'local.com',
      rproxyHost: 'localterminal.com',
      req_data: "{ \"appPath\": \"bs://hashedId\"}"
    };

    sinon.stub(requestLib, 'call', () => {});
    sinon.stub(HubLogger, 'miscLogger', () => {});
    sinon.stub(HubLogger, 'tempExceptionLogger', () => {});
    sinon.stub(bridge, 'sendResponse', () => {});
    sinon.stub(requestLib, 'appendBStackHostHeader', () => JSON.parse("{\"BStack-Host\": \"localterminal.com\"}"));

  });

  afterEach(() => {
    requestLib.call.restore();
    HubLogger.miscLogger.restore();
    HubLogger.tempExceptionLogger.restore();
    bridge.sendResponse.restore();
    requestLib.appendBStackHostHeader.restore();
  });

  it('should be successfully setting data', async () => {
    requestLib.call.restore();
    const response = {
      statusCode: 200,
      data: { message: "Success"}
    }

    sinon.stub(requestLib, 'call', () => {
      return response;
    });

    await bridge.installApp(keyObject, params);

    assert(JSON.parse(params.data).status === 0);
    assert(JSON.parse(params.data).value.message === "Success");
  });

  it('should be successfully setting data for 500 response', async () => {
    requestLib.call.restore();
    const response = {
      statusCode: 500,
      data: { error: "Something went wrong"}
    }

    sinon.stub(requestLib, 'call', () => {
      return response;
    });

    await bridge.installApp(keyObject, params);

    assert(JSON.parse(params.data).status === 13);
    assert(params.output.statusCode === 500);
  });

  it('should handle error in request', async () => {
    requestLib.call.restore();
    sinon.stub(requestLib, 'call', () => {
      throw new Error('Unable to request to terminal');
    });

    await bridge.installApp(keyObject, params);
    assert(HubLogger.tempExceptionLogger.calledOnce);
    assert(JSON.parse(params.data).status === 13);
    assert(params.output.statusCode === 500);
  });
});

describe('getWindowSize', () => {
  let keyObject;
  let params;
  let getWindowSizeSpy;

  beforeEach(() => {
    keyObject = {
      name: 'test.com',
      port: 9999,
      key: '1234',
      selenium_version: '3.141.59',
      rproxyHost: 'localterminal.com',
      dialect: 'W3C',
    };

    params = {
      clientSessionID: '12345',
      hostname: 'local.com',
      rproxyHost: 'localterminal.com',
    };

    sinon.stub(bridge, 'sendResponse', () => {});
    getWindowSizeSpy = sinon.stub(SeleniumClient.prototype, 'getWindowSize', () => {});
  });

  afterEach(() => {
    bridge.sendResponse.restore();
    getWindowSizeSpy.restore();
  });

  it('should handle error in getWindowSize', async () => {
    getWindowSizeSpy.restore();
    getWindowSizeSpy = sinon.stub(SeleniumClient.prototype, 'getWindowSize', () => {
      throw new Error('Unable to fetch window size');
    });
    await bridge.getWindowSize(keyObject, params);
    assert(JSON.parse(params.data).status === 13);
  });

  it('should be able to successfully complete', async () => {
    getWindowSizeSpy.restore();
    getWindowSizeSpy = sinon.stub(SeleniumClient.prototype, 'getWindowSize', () => ({ res: { x: 123, y: 123 } }));
    await bridge.getWindowSize(keyObject, params);
    assert(JSON.parse(params.data).status === 0);
  });
});

describe('launchApp', () => {
  let keyObject;
  let params;
  let launchAppSpy;

  beforeEach(() => {
    keyObject = {
      name: 'test.com',
      port: 9999,
      key: '1234',
      selenium_version: '3.141.59',
      rproxyHost: 'localterminal.com',
      dialect: 'W3C',
    };

    params = {
      clientSessionID: '12345',
      hostname: 'local.com',
      rproxyHost: 'localterminal.com',
    };

    sinon.stub(requestLib, 'call', () => {});
    sinon.stub(HubLogger, 'miscLogger', () => {});
    sinon.stub(HubLogger, 'tempExceptionLogger', () => {});
    sinon.stub(bridge, 'sendResponse', () => {});
    sinon.stub(requestLib, 'appendBStackHostHeader', () => '');

    launchAppSpy = sinon.stub(SeleniumClient.prototype, 'launchApp', () => {
    });
  });

  afterEach(() => {
    requestLib.call.restore();
    HubLogger.miscLogger.restore();
    HubLogger.tempExceptionLogger.restore();
    bridge.sendResponse.restore();
    requestLib.appendBStackHostHeader.restore();
    launchAppSpy.restore();
  });

  it('should catch error while selenium client launches the app', async () => {
    launchAppSpy.restore();
    launchAppSpy = sinon.stub(SeleniumClient.prototype, 'launchApp', () => {
      throw new Error('unable to launch app');
    });
    await bridge.launchApp(keyObject, params);
    assert(HubLogger.tempExceptionLogger.calledOnce);
    assert(JSON.parse(params.data).status === 13);
  });

  it('should be successfully setting data', async () => {
    await bridge.launchApp(keyObject, params);
    assert(HubLogger.tempExceptionLogger.notCalled);
    assert(JSON.parse(params.data).status === 0);
  });

  it('should handle error in request', async () => {
    requestLib.call.restore();
    sinon.stub(requestLib, 'call', () => {
      throw new Error('Unable to request to terminal');
    });

    await bridge.launchApp(keyObject, params);
    assert(HubLogger.tempExceptionLogger.calledOnce);
    assert(JSON.parse(params.data).status === 13);
  });
});

describe('getHashBasicAuthiOS', () => {
  [
    {
      os_version: 'iOS 10',
      deviceName: 'iPhone 7 Plus - 10.3',
      hash: 'POST:basic-auth-ios'
    },
    {
      os_version: 'iOS 11',
      deviceName: 'iPhone 8 - 11.0',
      hash: 'POST:basic-auth-ios-gte11'
    },
    {
      os_version: 'iOS 12',
      deviceName: 'iPhone XS - 12.1',
      hash: 'POST:basic-auth-ios-gte11'
    },
    {
      os_version: 'iOS 13',
      deviceName: 'iPhone 11 - 13.2',
      hash: 'POST:basic-auth-ios-gte11'
    },
    {
      os_version: 'iOS 14',
      deviceName: 'iPhone XS - 14.0',
      hash: 'POST:basic-auth-ios-gte11'
    },
    {
      os_version: 'iOS 15',
      deviceName: 'iPhone XS - 15.0',
      hash: 'POST:basic-auth-ios-gte11'
    },
  ].forEach(function(row){
    it('should return hash for ' + row.os_version, () => {
      let keyObject = {deviceName: row.deviceName};
      const getHashBasicAuthiOS = rewiredBridge.__get__('getHashBasicAuthiOS');
      const result = getHashBasicAuthiOS(keyObject);
      assert.equal(result, row.hash);
    });
  });
});

describe('basicAuthPopupiOS', () => {
  let keyObject;

  beforeEach(() => {
    keyObject = {
      name: "Some IP address",
      wda_port: 8400,
      port: 8080,
      rproxyHost: "Some IP address",
      appium_version: "1.17.0",
      selenium_version: "3.2.1",
      dialect: "OSS",
      key: '1234',
      rails_session_id: 'abcdefg',
      secondary_state: 'basic-auth_required',
    };

    sinon.stub(WdaClient.prototype, 'attach').returns(true);
    sinon.stub(WdaClient.prototype, 'clickElement').returns("Click Element Response");
    sinon.stub(SeleniumClient.prototype, 'sendKeys').returns("Send Keys Response");
  });

  afterEach(() => {
    WdaClient.prototype.attach.restore();
    WdaClient.prototype.clickElement.restore();
    SeleniumClient.prototype.sendKeys.restore();
  });

  it('WDA Client should receive arguments for appium < 1.20', () => {
    const y = sinon.spy(WdaClient.prototype, 'findElement');
    ["1.17.0","1.18.0","1.19.1"].forEach(async (version,idx,arr)=>{
      keyObject.appium_version = version;
      await bridge.iOSBasicAuthHandler(keyObject,"abcd","1234",()=>{});
      try {
        assert(y.calledWith('accessibility id','User Name') === true);
      } catch {
        assert(y.calledWith('accessibility id','Username') === true);
      }
    });
    y.restore();
  });

  it('WDA Client should receive arguments for appium >= 1.20', () => {
    const y = sinon.spy(WdaClient.prototype, 'findElement');
    ["1.20.2","1.21.0"].forEach(async (version,idx,arr)=>{
      keyObject.appium_version = version;
      await bridge.iOSBasicAuthHandler(keyObject,"abcd","1234",()=>{});
      try {
        assert(y.calledWith('predicate string','value == "User Name"') === true);
      } catch {
        assert(y.calledWith('predicate string','value == "Username"') === true);
      }
    });
    y.restore();
  });
});
