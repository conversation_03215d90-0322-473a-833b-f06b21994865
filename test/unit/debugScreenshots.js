const assert = require("assert");
const { getCompleteFileName } = require("../../helpers/debugScreenshots");
const { getFileName } = require("../../helpers/debugScreenshots");

describe("#getCompleteFileName", () => {
  it("should get screenshot url for automate", () => {
    const bucket = "some-bucket";
    const sessionId = "test";
    const counter = 1;
    const isAppTesting = false;
    const keyObject = {
      rails_session_id: sessionId,
      s3bucket: bucket,
      appTesting: isAppTesting,
    };
    assert.equal(
      getCompleteFileName(keyObject, counter),
      "https://automate.browserstack.com/s3-debug/some-bucket/test/screenshot-1.jpeg"
    );
  });
  it("should get screenshot url for app-automate", () => {
    const bucket = "some-bucket";
    const sessionId = "test";
    const counter = 1;
    const isAppTesting = true;
    const keyObject = {
      rails_session_id: sessionId,
      s3bucket: bucket,
      appTesting: isAppTesting,
    };
    assert.equal(
      getCompleteFileName(keyObject, counter),
      "https://app-automate.browserstack.com/s3-debug/some-bucket/test/screenshot-1.jpeg"
    );
  });
});

describe('getFileName', () => {
  it('should return the correct file name with rails_session_id and fileCounter', () => {
    const obj = { appTesting: true, rails_session_id: '123' };
    const fileCounter = 1;
    const result = getFileName(obj, fileCounter);
    assert.equal(result, '/123/screenshot-1.jpeg');
  });

  it('should handle different rails_session_id and fileCounter', () => {
    const obj = { appTesting: false, rails_session_id: '456' };
    const fileCounter = 2;
    const result = getFileName(obj, fileCounter);
    assert.equal(result, '/456/screenshot-2.jpeg');
  });
});
