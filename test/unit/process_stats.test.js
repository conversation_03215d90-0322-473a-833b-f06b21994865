'use strict';

/* eslint-disable no-underscore-dangle */
/* eslint-disable no-unused-expressions */

const rewire = require('rewire');
const helper = require('../../helper');
const sinon = require('sinon');
const { expect } = require('chai');
const {
  describe, it, beforeEach, afterEach,
} = require('mocha');
const constants = require('../../constants');
const IOredisMock = require('ioredis-mock');

const ProcessStats = rewire('../../process_stats');

const genRandomHootHootData = samples => Array.from({ length: samples }).map((_, idx) => ({
  [idx]: 'some_random_value',
}));

describe('process stats for hoothoot', () => {
  beforeEach(() => {
    // This is done because the internal hoothoot module does the
    // console logging.
    sinon.stub(console, 'log', () => {});
  });

  afterEach(() => {
    // eslint-disable-next-line no-console
    console.log.restore();
  });

  it('should determine system stats correctly', async () => {
    const determineSystemStats = ProcessStats.__get__('determineSystemStats');
    const data = await determineSystemStats();
    expect(data).to.not.be.undefined;
    expect(data).to.include.all.keys(['cpu', 'memory', 'total_global', 'total_sessions']);
  });

  it('should handle error in system stats correctly', async () => {
    const originalProcessStat = ProcessStats.__get__('processStat');
    const randomErrorFn = () => { throw new Error('not able to fetch the process data'); };
    ProcessStats.__set__('processStat', randomErrorFn);
    const determineSystemStats = ProcessStats.__get__('determineSystemStats');
    const data = await determineSystemStats();
    expect(data).to.be.undefined;
    ProcessStats.__set__('processStat', originalProcessStat);
  });

  it('should clear the registry', () => {
    const sample = {};
    for (let idx = 0; idx < 10; ++idx) {
      sample[idx] = {
        key: 'some random value',
      };
    }
    constants.pushToHootHootRegistry = sample;
    const cleanRegistry = ProcessStats.__get__('cleanHootHootRegistry');
    const pushToHootHootSpy = sinon.spy(helper, 'pushStatsToHootHoot');
    cleanRegistry();
    sinon.assert.callCount(pushToHootHootSpy, 10);
    helper.pushStatsToHootHoot.restore();
  });

  it('should check the backlog lists', async () => {
    const ownerCallBack = ProcessStats.__get__('ownerCallBack');
    const regionCount = 2;
    const redisData = {};
    ['timeoutManager', 'timeoutManager_backup'].forEach((key) => {
      for (let idx = 0; idx < regionCount; idx += 1) {
        redisData[`${key}_${idx}`] = genRandomHootHootData(5);
      }
    });
    const redisMock = new IOredisMock({ data: redisData });
    ProcessStats.__set__('redisClient', redisMock);
    const pushStatsSpy = sinon.spy(helper, 'pushStatsToHootHoot');
    constants.timeoutManager.totalCountThisRegion = regionCount;
    await ownerCallBack();
    sinon.assert.callCount(pushStatsSpy, 5); // +1 due to v3
    helper.pushStatsToHootHoot.restore();
  });

  it('should send all data to hoothoot', async () => {
    const regionCount = 2;
    const redisData = {};
    constants.pushToHootHootRegistry = genRandomHootHootData(10);
    ['timeoutManager', 'timeoutManager_backup'].forEach((key) => {
      for (let idx = 0; idx < regionCount; idx += 1) {
        redisData[`${key}_${idx}`] = genRandomHootHootData(5);
      }
    });
    const redisMock = new IOredisMock({ data: redisData });
    ProcessStats.__set__('redisClient', redisMock);
    const pushStatsSpy = sinon.spy(helper, 'pushStatsToHootHoot');
    constants.timeoutManager.totalCountThisRegion = regionCount;
    sinon.stub(helper, 'checkOwnRedisMetrics', () => true);
    await ProcessStats.getMetricsForHootHoot();
    // This is done because hub.js is also setting some timers to
    // basically send the event loop interval as well.
    expect(pushStatsSpy.callCount >= 15).to.be.true;
    helper.pushStatsToHootHoot.restore();
    helper.checkOwnRedisMetrics.restore();
  });

  it('should only send systemStats to hoothoot if getV8HeapMetrics returns undefined', async () => {
    const regionCount = 2;
    const redisData = {};
    constants.pushToHootHootRegistry = genRandomHootHootData(10);
    ['timeoutManager', 'timeoutManager_backup'].forEach((key) => {
      for (let idx = 0; idx < regionCount; idx += 1) {
        redisData[`${key}_${idx}`] = genRandomHootHootData(5);
      }
    });
    const getV8HeapMetricsStub = sinon.stub().returns(undefined);
    const originalGetV8HeapMetrics = ProcessStats.__get__('getV8HeapMetrics');
    ProcessStats.__set__('getV8HeapMetrics', getV8HeapMetricsStub);

    const redisMock = new IOredisMock({ data: redisData });
    ProcessStats.__set__('redisClient', redisMock);
    const pushStatsSpy = sinon.spy(helper, 'pushStatsToHootHoot');
    constants.timeoutManager.totalCountThisRegion = regionCount;
    sinon.stub(helper, 'checkOwnRedisMetrics', () => true);
    await ProcessStats.getMetricsForHootHoot();
    // This is done because hub.js is also setting some timers to
    // basically send the event loop interval as well.
    expect(pushStatsSpy.callCount >= 15).to.be.true;
    helper.pushStatsToHootHoot.restore();
    helper.checkOwnRedisMetrics.restore();
    ProcessStats.__set__('getV8HeapMetrics', originalGetV8HeapMetrics);
  });

  it('should send alert when increased queue in timeout backlog', async () => {
    const regionCount = 2;
    const redisData = {};
    constants.pushToHootHootRegistry = genRandomHootHootData(10);
    ['timeoutManager', 'timeoutManager_backup'].forEach((key) => {
      for (let idx = 0; idx < regionCount; idx += 1) {
        redisData[`${key}_${idx}`] = genRandomHootHootData(10001);
      }
    });
    const redisMock = new IOredisMock({ data: redisData });
    ProcessStats.__set__('redisClient', redisMock);
    const pushStatsSpy = sinon.spy(helper, 'pushStatsToHootHoot');
    const pingZombieSpy = sinon.spy(helper, 'PingZombie');
    constants.timeoutManager.totalCountThisRegion = regionCount;
    sinon.stub(helper, 'checkOwnRedisMetrics', () => true);
    await ProcessStats.getMetricsForHootHoot();
    // This is done because hub.js is also setting some timers to
    // basically send the event loop interval as well.
    expect(pushStatsSpy.callCount >= 15).to.be.true;
    sinon.assert.callCount(pingZombieSpy, 2);
    helper.checkOwnRedisMetrics.restore();
    pushStatsSpy.restore();
    helper.PingZombie.restore();
  });
});
