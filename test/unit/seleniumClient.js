'use strict';

const sinon = require('sinon');
const SeleniumClient = require('../../seleniumClient');
const { expect } = require('chai');

describe('SeleniumClient', () => {
  let seleniumClient;

  beforeEach(() => {
    seleniumClient = new SeleniumClient({
      name: 'google.com',
      port: 9999,
      key: 'sessionId',
      selenium_version: '1.0.0',
      seleniumVersion: '1.0.0',
      rproxyHost: '***********',
    });
    sinon.spy(seleniumClient, 'validateSessionId');
  });

  afterEach(() => {
    seleniumClient.validateSessionId.restore();
  });

  context('Tests selenium client methods. ', () => {
    it('Send keys method should call makeRequest', async () => {
      sinon.stub(seleniumClient, 'makeRequest').returns(Promise.resolve({ status: 200 }));
      await seleniumClient.sendKeys(['test']);

      seleniumClient.validateSessionId.calledOnce.should.eql(true);
      seleniumClient.makeRequest.calledOnce.should.eql(true);
      seleniumClient.makeRequest.restore();
    });
  });

  describe('.getActiveElement', () => {
    it('should call make requests', async () => {
      sinon.stub(seleniumClient, 'makeRequest').returns(Promise.resolve({ status: 200, value: {} }));
      await seleniumClient.getActiveElement();
      sinon.assert.calledWith(seleniumClient.makeRequest, 'GET', '/wd/hub/session/sessionId/element/active');
      seleniumClient.makeRequest.restore();
    });

    it('should rethrow error', (done) => {
      sinon.stub(seleniumClient, 'makeRequest').throws('error');
      seleniumClient
        .getActiveElement()
        .catch((err) => {
          sinon.assert.calledWith(seleniumClient.makeRequest, 'GET', '/wd/hub/session/sessionId/element/active');
          seleniumClient.makeRequest.restore();
          done();
        })
    });
  });

  describe('.elementValue', () => {
    it('should call make requests', async () => {
      sinon.stub(seleniumClient, 'makeRequest').returns(Promise.resolve({ status: 200 }));
      await seleniumClient.elementValue('abcd', ['test1', 'test2']);
      sinon.assert.calledWith(
        seleniumClient.makeRequest,
        'POST',
        '/wd/hub/session/sessionId/element/abcd/value',
        {
          payload: {
            id: "abcd",
            text: "test1test2",
            value: ["t", "e", "s", "t", "1", "t", "e", "s", "t", "2"]
          }
        }
      );
      seleniumClient.makeRequest.restore();
    });

    it('should rethrow error', (done) => {
      sinon.stub(seleniumClient, 'makeRequest').throws('error');
      seleniumClient
        .elementValue('abcd', ['test1', 'test2'])
        .catch((err) => {
          seleniumClient.makeRequest.restore();
          done();
        })
    });
  });
});
describe('#checkInsecureWebsite' , () => {
  let seleniumClient;
  before(() => {
    seleniumClient = new SeleniumClient({
      name: 'google.com',
      port: 9999,
      key: 'sessionId',
      selenium_version: '1.0.0',
      seleniumVersion: '1.0.0',
      rproxyHost: '***********',
    });

    sinon.spy(seleniumClient, 'validateSessionId');
    sinon.stub(seleniumClient, 'makeRequest').returns(Promise.resolve({ status: 200 }));
    sinon.stub(seleniumClient, 'getTitle').returns("test-title");
  })
  after(() => {
    seleniumClient.makeRequest.restore();
    seleniumClient.validateSessionId.restore();
    seleniumClient.getTitle.restore();
  })
  it('should match the title', async () => {
    let error = null;
    try {
      await seleniumClient.checkInsecureWebsite('test-title');
    } catch (err) {
      error = err;
    }
    expect(error).to.equal(null);
  });

  it('should not match the title', async () => {
    let error = null;
    try {
      await seleniumClient.checkInsecureWebsite('test-title-diff');
    } catch (err) {
      error = err;
    }
    expect(error).to.not.equal(null);
  });
})

describe('#handleBiometricPopupAndroid' , () => {
  let seleniumClient;
  before(() => {
    seleniumClient = new SeleniumClient({
      name: 'google.com',
      port: 9999,
      key: 'sessionId',
      selenium_version: '1.0.0',
      seleniumVersion: '1.0.0',
      rproxyHost: '***********',
    });
  })
  it('should return popup_absent', async () => {
    sinon.stub(seleniumClient, 'makeRequest').returns(Promise.resolve({ status: 200, value: [] }));
    let error = null;
    try {
      await seleniumClient.handleBiometricPopupAndroid('pass');
    } catch (err) {
      error = err;
    }
    seleniumClient.makeRequest.restore();
  });

  it('should return popup_absent if biometricMatch is Fail', async () => {
    sinon.stub(seleniumClient, 'makeRequest').returns(Promise.resolve({ status: 200, value: [] }));
    let error = null;
    try {
      await seleniumClient.handleBiometricPopupAndroid('Fail');
    } catch (err) {
      error = err;
    }
    seleniumClient.makeRequest.restore();
  });

  it('should return popup_absent if biometricMatch is cancel', async () => {
    sinon.stub(seleniumClient, 'makeRequest').returns(Promise.resolve({ status: 200, value: [] }));
    let error = null;
    try {
      await seleniumClient.handleBiometricPopupAndroid('cancel');
    } catch (err) {
      error = err;
    }
    seleniumClient.makeRequest.restore();
  });
});

describe('.getTypes', () => {
  let seleniumClient;
  before(() => {
    seleniumClient = new SeleniumClient({
      name: 'google.com',
      port: 9999,
      key: 'sessionId',
      selenium_version: '1.0.0',
      seleniumVersion: '1.0.0',
      rproxyHost: '***********',
    });
  });

  it ('should call /se/log/types', async () => {
    sinon.stub(seleniumClient, 'makeRequest').returns(Promise.resolve({ status: 200, value: [] }));
    await seleniumClient.getTypes();
    sinon.assert.calledWith(seleniumClient.makeRequest, 'GET', '/wd/hub/session/sessionId/se/log/types');
    seleniumClient.makeRequest.restore();
  });

  it ('should rethrow error', (done) => {
    sinon.stub(seleniumClient, 'makeRequest').throws('error');
    seleniumClient
      .getTypes()
      .catch(() => {
        sinon.assert.calledWith(seleniumClient.makeRequest, 'GET', '/wd/hub/session/sessionId/se/log/types');
        seleniumClient.makeRequest.restore();
        done();
      })
  });
});

describe('.getLogTypesMobile', () => {
  let seleniumClient;
  before(() => {
    seleniumClient = new SeleniumClient({
      name: 'google.com',
      port: 9999,
      key: 'sessionId',
      selenium_version: '1.0.0',
      seleniumVersion: '1.0.0',
      rproxyHost: '***********',
    });
  });

  it ('should call /log/types', async () => {
    sinon.stub(seleniumClient, 'makeRequest').returns(Promise.resolve({ status: 200, value: [] }));
    await seleniumClient.getLogTypesMobile();
    sinon.assert.calledWith(seleniumClient.makeRequest, 'GET', '/wd/hub/session/sessionId/log/types');
    seleniumClient.makeRequest.restore();
  });

  it ('should rethrow error', (done) => {
    sinon.stub(seleniumClient, 'makeRequest').throws('error');
    seleniumClient
      .getLogTypesMobile()
      .catch(() => {
        sinon.assert.calledWith(seleniumClient.makeRequest, 'GET', '/wd/hub/session/sessionId/log/types');
        seleniumClient.makeRequest.restore();
        done();
      });
  });
});

describe('executeScriptAsync', () => {
  let seleniumClient;
  before(() => {
    seleniumClient = new SeleniumClient({
      name: 'google.com',
      port: 9999,
      key: 'sessionId',
      selenium_version: '1.0.0',
      seleniumVersion: '1.0.0',
      rproxyHost: '***********',
    });
  });

  it ('should call /execute_async for jsonwire', async () => {
    seleniumClient.isW3CDialect = false;
    sinon.stub(seleniumClient, 'makeRequest').returns(Promise.resolve({ status: 200, value: [] }));
    await seleniumClient.executeScriptAsync({ script: "event", args: [] }, { returnResp: true, parseRespose: false });
    sinon.assert.calledWith(seleniumClient.makeRequest, 'POST', '/wd/hub/session/sessionId/execute_async', { payload: { script: "event", args: [] }, parseRespose: false });
    seleniumClient.makeRequest.restore();
  });

  it ('should call /execute/async for w3c', async () => {
    seleniumClient.isW3CDialect = true;
    sinon.stub(seleniumClient, 'makeRequest').returns(Promise.resolve({ status: 200, value: [] }));
    await seleniumClient.executeScriptAsync({ script: "event", args: [] }, { returnResp: true, parseRespose: false });
    sinon.assert.calledWith(seleniumClient.makeRequest, 'POST', '/wd/hub/session/sessionId/execute/async', { payload: { script: "event", args: [] }, parseRespose: false });
    seleniumClient.makeRequest.restore();
  });

  it ('should call /execute/async for w3c and throw error', async () => {
    seleniumClient.isW3CDialect = true;
    sinon.stub(seleniumClient, 'makeRequest').returns(Promise.resolve({ status: 200, value: [] }));
    try {
      await seleniumClient.executeScriptAsync({ script: "event", args: [] }, { returnResp: false, parseRespose: false });
    } catch (err) {
      let error = err;
    }
    sinon.assert.calledWith(seleniumClient.makeRequest, 'POST', '/wd/hub/session/sessionId/execute/async', { payload: { script: "event", args: [] }, parseRespose: false });
    seleniumClient.makeRequest.restore();
  });

  it ('should call /execute_async for jsonwire and return response', async () => {
    seleniumClient.isW3CDialect = false;
    sinon.stub(seleniumClient, 'makeRequest').returns(Promise.resolve({ status: 0, value: [] }));
    let res = await seleniumClient.executeScriptAsync({ script: "event", args: [] }, { returnResp: false, parseRespose: false });
    sinon.assert.calledWith(seleniumClient.makeRequest, 'POST', '/wd/hub/session/sessionId/execute_async', { payload: { script: "event", args: [] }, parseRespose: false });
    seleniumClient.makeRequest.restore();
    expect(res).to.equal('executeScript: Successfully executed script')
  });

  it ('should call /execute/async for jsonwire and return response', async () => {
    seleniumClient.isW3CDialect = true;
    sinon.stub(seleniumClient, 'makeRequest').returns(Promise.resolve({ status: 0, value: null }));
    let res = await seleniumClient.executeScriptAsync({ script: "event", args: [] }, { returnResp: false, parseRespose: false });
    sinon.assert.calledWith(seleniumClient.makeRequest, 'POST', '/wd/hub/session/sessionId/execute/async', { payload: { script: "event", args: [] }, parseRespose: false });
    seleniumClient.makeRequest.restore();
    expect(res).to.equal('executeScript: Successfully executed script')
  });
});


describe('executeScript', () => {
  let seleniumClient;
  before(() => {
    seleniumClient = new SeleniumClient({
      name: 'google.com',
      port: 9999,
      key: 'sessionId',
      selenium_version: '1.0.0',
      seleniumVersion: '1.0.0',
      rproxyHost: '***********',
    });
  });

  it ('should call /execute for jsonwire', async () => {
    seleniumClient.isW3CDialect = false;
    sinon.stub(seleniumClient, 'makeRequest').returns(Promise.resolve({ status: 200, value: [] }));
    await seleniumClient.executeScript({ script: "event", args: [] }, { returnResp: true, parseRespose: false });
    sinon.assert.calledWith(seleniumClient.makeRequest, 'POST', '/wd/hub/session/sessionId/execute', { payload: { script: "event", args: [] }, parseRespose: false });
    seleniumClient.makeRequest.restore();
  });

  it ('should call /execute/sync for w3c', async () => {
    seleniumClient.isW3CDialect = true;
    sinon.stub(seleniumClient, 'makeRequest').returns(Promise.resolve({ status: 200, value: [] }));
    await seleniumClient.executeScript({ script: "event", args: [] }, { returnResp: true, parseRespose: false });
    sinon.assert.calledWith(seleniumClient.makeRequest, 'POST', '/wd/hub/session/sessionId/execute/sync', { payload: { script: "event", args: [] }, parseRespose: false });
    seleniumClient.makeRequest.restore();
  });

  it ('should call /execute/sync for w3c and throw error', async () => {
    seleniumClient.isW3CDialect = true;
    sinon.stub(seleniumClient, 'makeRequest').returns(Promise.resolve({ status: 200, value: [] }));
    try {
      await seleniumClient.executeScript({ script: "event", args: [] }, { returnResp: false, parseRespose: false });
    } catch (err) {
      let error = err;
    }
    sinon.assert.calledWith(seleniumClient.makeRequest, 'POST', '/wd/hub/session/sessionId/execute/sync', { payload: { script: "event", args: [] }, parseRespose: false });
    seleniumClient.makeRequest.restore();
  });

  it ('should call /execute for jsonwire and return response', async () => {
    seleniumClient.isW3CDialect = false;
    sinon.stub(seleniumClient, 'makeRequest').returns(Promise.resolve({ status: 0, value: [] }));
    let res = await seleniumClient.executeScript({ script: "event", args: [] }, { returnResp: false, parseRespose: false });
    sinon.assert.calledWith(seleniumClient.makeRequest, 'POST', '/wd/hub/session/sessionId/execute', { payload: { script: "event", args: [] }, parseRespose: false });
    seleniumClient.makeRequest.restore();
    expect(res).to.equal('executeScript: Successfully executed script')
  });

  it ('should call /execute/sync for jsonwire and return response', async () => {
    seleniumClient.isW3CDialect = true;
    sinon.stub(seleniumClient, 'makeRequest').returns(Promise.resolve({ status: 0, value: null }));
    let res = await seleniumClient.executeScript({ script: "event", args: [] }, { returnResp: false, parseRespose: false });
    sinon.assert.calledWith(seleniumClient.makeRequest, 'POST', '/wd/hub/session/sessionId/execute/sync', { payload: { script: "event", args: [] }, parseRespose: false });
    seleniumClient.makeRequest.restore();
    expect(res).to.equal('executeScript: Successfully executed script')
  });
});
