const mapHybridToOSS = require('../../utils/commandMapper/mapHybridToOSS');

describe('mapHybridToOSS function', () => {
  context('GET - Map selenium OSS commands to W3C', () => {
    const sessionPath = '/wd/hub/session/a1b2c3d4a1b2c3d4a1b2c3d4a1b2c3d4';
    it('converts the /se/log command', () => {
      const request = { method: 'POST', path: `${sessionPath}/se/log`, headers: {} };
      const mappedRequest = mapHybridToOSS.mapHybridRequestToOSS(request);
      mappedRequest.method.should.equal('POST');
      mappedRequest.path.should.equal(`${sessionPath}/log`);
    });

    it('converts the /fullscreen command to /maximize', () => {
      const request = { method: 'POST', path: `${sessionPath}/window/fullscreen`, headers: {} };
      const mappedRequest = mapHybridToOSS.mapHybridRequestToOSS(request);
      mappedRequest.method.should.equal('POST');
      mappedRequest.path.should.equal(`${sessionPath}/window/maximize`);
    });

    it('converts the /execute/sync command to /execute', () => {
      const request = { method: 'POST', path: `${sessionPath}/execute/sync`, headers: {} };
      const mappedRequest = mapHybridToOSS.mapHybridRequestToOSS(request);
      mappedRequest.method.should.equal('POST');
      mappedRequest.path.should.equal(`${sessionPath}/execute`);
    });
  });

  context('#mapOSSResponseToHybrid', () => {
    it('converts findElement response to be compatible with hybrid client', () => {
      const elementId = '5000';
      const response = { value: { 'ELEMENT': elementId } }
      const mappedResponse = mapHybridToOSS.mapOSSResponseToHybrid(response);
      mappedResponse.value['element-6066-11e4-a52e-4f735466cecf'].should.equal(elementId);
    });

    it("converts the findElements response to be compatible with hybrid client", () => {
      const elementId = '5000';
      const response = {
        value: [{ 'ELEMENT': elementId }]
      }
      const mappedResponse = mapHybridToOSS.mapOSSResponseToHybrid(response);
      mappedResponse.value[0]['element-6066-11e4-a52e-4f735466cecf'].should.equal(elementId);
    });

    it("converts the error to be compatible with hybrid client", () => {
      const response = {
        state: 'no such element',
        value: { 'data': 'some_data' },
        status: 7
      }
      const mappedResponse = mapHybridToOSS.mapOSSResponseToHybrid(response);
      mappedResponse.value['error'].should.equal('no such element');
      mappedResponse.value['message'].should.equal('no such element');
      mappedResponse.value['stacktrace'].should.equal('');
      mappedResponse.status.should.equal(7);
    });

    it('converts the timeout error to be compatible with hybrid client', () => {
      const errorMessage =
        'Cannot locate element on page with given search parameters';
      const error = 'no such element';
      const response = {
        state: 'error',
        value: { error, message: errorMessage },
        status: 7,
      };
      const mappedResponse = mapHybridToOSS.mapOSSResponseToHybrid(response);
      mappedResponse.value.error.should.equal(error);
      mappedResponse.value.message.should.equal(errorMessage);
      mappedResponse.value.stacktrace.should.equal('');
      mappedResponse.status.should.equal(7);
    });
  });
});
