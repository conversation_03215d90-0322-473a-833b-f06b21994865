'use strict';

const MonitoringHelper = require('../../../apps/monitoringService/monitoringHelper');
const hubHelper = require('../../../helper');
const constants = require('../../../constants');
const assert = require('assert');
const sinon = require('sinon');
const child_process = require('child_process');


describe('monitoring service tests', () => {
  const monitoringHelper = new MonitoringHelper();

  beforeEach((done) => {
    hubHelper.redisClient.flushall()
      .then(() => {
        done();
      });
  });

  afterEach((done) => {
    hubHelper.redisClient.flushall()
      .then(() => {
        done();
      });
  });

  it('should ensure monitoring service runs once per day', (done) => {
    monitoringHelper.ensureOncePerDay(constants.monitoring.ensureOncePerDayKey).then((value) => {
      assert(value === true)
      done();
    });
  });

  describe('.getPacketLoss', () => {
    it('should return packet loss output of the command', async () => {
      sinon.stub(child_process, 'exec').callsArgWith(1, undefined, '10', undefined);
      let packetLoss = await monitoringHelper.getPacketLoss();
      child_process.exec.restore();
      assert(packetLoss === 10);
    });

    it('should return 0 loss if command fails', async () => {
      sinon.stub(child_process, 'exec').callsArgWith(1, 'error', '10', '');
      let packetLoss = await monitoringHelper.getPacketLoss();
      child_process.exec.restore();
      assert(packetLoss === 0);
    });
  });
});
