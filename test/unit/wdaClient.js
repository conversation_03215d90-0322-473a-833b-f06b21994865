const wdaClient = require('../../wdaClient');
const sinon = require('sinon');
const expect = require('chai').expect;

describe('wdaClient Tests', () => {
  context("Click Element Executes successfully scenario", () => {
    const wda = new wdaClient("host", "post");

    beforeEach(async () => {
      sinon.stub(wda, "makeRequest").returns({
        sessionId: "RANDOM_SESSION_ID"
      });
      await wda.attach();
      wda.makeRequest.restore();
    });

    afterEach(() => {
      wda.makeRequest.restore();
    });

    it("Appium response which includes status key", async () => {
      const responseFromClickElement = {
        value: '',
        status: 0,
        sessionId: "RANDOM_SESSION_ID"
      }

      sinon.stub(wda, "makeRequest").returns(responseFromClickElement);
      const response = await wda.clickElement("RANDOM_ELEMENT_ID");
      response.should.eql(responseFromClickElement);
    });

    it("Appium response which doesn't include status key v1", async () => {
      const responseFromClickElement = {
        value: null,
        sessionId: "RANDOM_SESSION_ID"
      }

      sinon.stub(wda, "makeRequest").returns(responseFromClickElement);
      const response = await wda.clickElement("RANDOM_ELEMENT_ID");
      response.should.eql(responseFromClickElement);
    });

    it("Appium response which doesn't include status key v2", async () => {
      const responseFromClickElement = {
        value: '',
        sessionId: "RANDOM_SESSION_ID"
      }

      sinon.stub(wda, "makeRequest").returns(responseFromClickElement);
      const response = await wda.clickElement("RANDOM_ELEMENT_ID");
      response.should.eql(responseFromClickElement);
    });
  });

  context("Click Element Fail Scenario", () => {
    const wda = new wdaClient("host", "post");

    beforeEach(async () => {
      sinon.stub(wda, "makeRequest").returns({
        sessionId: "RANDOM_SESSION_ID"
      });
      await wda.attach();
      wda.makeRequest.restore();
    });

    afterEach(() => {
      wda.makeRequest.restore();
    });

    it("Appium response which includes status key", async () => {
      const responseFromClickElement = {
        value: {},
        status: 13,
        sessionId: "RANDOM_SESSION_ID"
      }

      sinon.stub(wda, "makeRequest").returns(responseFromClickElement);
      try {
        await wda.clickElement("RANDOM_ELEMENT_ID");
      } catch (err) {
        err.message.should.eql("Couldn't click Element : RANDOM_ELEMENT_ID. with response value : {}, and status : 13");
      }
    });

    it("Appium response which doesn't include status key", async () => {
      const responseFromClickElement = {
        value: {
          error: 'stale element reference',
          message: 'An element command failed because the referenced element is no longer attached to the DOM',
          traceback: ''
        },
        sessionId: "RANDOM_SESSION_ID"
      }

      sinon.stub(wda, "makeRequest").returns(responseFromClickElement);
      try {
        await wda.clickElement("RANDOM_ELEMENT_ID");
      } catch (err) {
        err.message.should.eql('Couldn\'t click Element : RANDOM_ELEMENT_ID. with response value : {"error":"stale element reference","message":"An element command failed because the referenced element is no longer attached to the DOM","traceback":""}, and status : undefined');
      }
    });
  });

  context("findElement scenario", () => {
    const wda = new wdaClient("host", "post");

    beforeEach(async () => {
      sinon.stub(wda, "makeRequest").returns({
        sessionId: "RANDOM_SESSION_ID"
      });
      await wda.attach();
      wda.makeRequest.restore();
    });

    afterEach(() => {
      wda.makeRequest.restore();
    });

    it("failed to find element", async () => {
      const responseFromFindElement = {
        value: {},
        status: 13,
        sessionId: "RANDOM_SESSION_ID"
      }

      sinon.stub(wda, "makeRequest").returns(responseFromFindElement);
      try {
        await wda.findElement("accessibility id", "random_id");
      } catch (err) {
        err.message.should.eql("Not Found");
      }
    });

    it("succeeded to find element", async () => {
      const responseFromFindElement = {
        value: { ELEMENT: "RANDOM_ELEMENT_ID" },
        status: 0,
        sessionId: "RANDOM_SESSION_ID"
      }

      sinon.stub(wda, "makeRequest").returns(responseFromFindElement);
      const response = await wda.findElement("accessibility id", "random_id");
      response.should.eql("RANDOM_ELEMENT_ID");
    });
  });

  context("fetch session id via status check using wda.attach", () => {
    it("should return the sessionID if a session has started", async () => {
      const wda = new wdaClient("host", "post");
      sinon.stub(wda, "makeRequest").returns({
        sessionId: "RANDOM_SESSION_ID"
      });

      const sessionId = await wda.attach();
      sessionId.should.eql("RANDOM_SESSION_ID");
      wda.makeRequest.restore();
    });

    it("should not return a sessionID if no session has started", async () => {
      const wda = new wdaClient("host", "post");
      sinon.stub(wda, "makeRequest").returns({
        status: 0
      });

      try {
        const sessionIdawait = await wda.attach();
      } catch (err) {
        err.message.should.eql("sessionId not found")
      }

      wda.makeRequest.restore();
    });
  });

  describe('#acceptAlert()', function() {
    it('should throw an error if sessionId is not valid', async function() {
      const wda = new wdaClient();
      wda.sessionId = null;

      try {
        await wda.acceptAlert();
      } catch (error) {
        expect(error.message).to.equal('Sesssion ID is not set');
      }
    });

    it('should throw an error if status is not 0', async function() {
      const wda = new wdaClient();
      wda.sessionId = '123';
      const makeRequestStub = sinon.stub(wda, 'makeRequest').returns(Promise.resolve({ status: 1 }));

      try {
        await wda.acceptAlert();
      } catch (error) {
        expect(error.message).to.equal('acceptAlert: Error occurred while accepting alert');
      }

      makeRequestStub.restore();
    });

    it('should throw an error if value is a hash and has an error property', async function() {
      const wda = new wdaClient();
      wda.sessionId = '123';
      const makeRequestStub = sinon.stub(wda, 'makeRequest').returns(Promise.resolve({ value: { error: 'Some error' } }));

      try {
        await wda.acceptAlert();
      } catch (error) {
        expect(error.message).to.equal('acceptAlert: Error occurred while accepting alert');
      }

      makeRequestStub.restore();
    });

    it('should return success message if status is 0 and value is not a hash or does not have an error property', async function() {
      const wda = new wdaClient();
      wda.sessionId = '123';
      const makeRequestStub = sinon.stub(wda, 'makeRequest').returns(Promise.resolve({ status: 0 }));

      const result = await wda.acceptAlert();

      expect(result).to.equal('acceptAlert: Successfully accepted alert');

      makeRequestStub.restore();
    });
  });
});
