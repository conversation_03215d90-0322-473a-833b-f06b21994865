'use strict';

const { describe, it } = require('mocha');
const chunkedResponse = require('../../chunkedResponse');
const { assert } = require('chai');
const sinon = require('sinon');

describe('chunkedResponse.js', () => {
  it('should trigger space job if noBlankPolling is false after 30 seconds', () => {
    const response = {
      on: sinon.spy(),
      write: sinon.stub(),
      writeHead: sinon.stub()
    };
    let timer = sinon.useFakeTimers();
    chunkedResponse(response, false);
    timer.tick(30000);   
    assert(response.writeHead.calledOnce);
    timer.restore();
  });

  it('should not trigger space job if noBlankPolling is true after 30 seconds', () => {
    const response = {
      on: sinon.spy(),
      write: sinon.stub(),
      writeHead: sinon.stub()
    };
    let timer = sinon.useFakeTimers();
    chunkedResponse(response, true);
    timer.tick(30000);   
    assert(response.writeHead.calledOnce === false);
    timer.restore();
  });
});
