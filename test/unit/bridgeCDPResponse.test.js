'use strict';

const { expect } = require('chai');
const sinon = require('sinon');
const bridge = require('../../bridge');
const hub = require('../../hub');
const helper = require('../../helper');
const ha = require('../../ha');
const pubsub = require('../../pubSub');

describe('Bridge Send Response and Send Error for CDP sessions', () => {
  let keyObject;
  let params;
  const mockSessionNotFound = {};

  beforeEach(() => {
    keyObject = {
      isPuppeteer: true,
      isPlaywright: false
    };
    params = {
      onResolve: sinon.stub()
    };
    mockSessionNotFound.stub = sinon.stub(hub, 'sessionNotFound');
  });

  afterEach(() => {
    mockSessionNotFound.stub.restore();
  });

  describe('sendResponse', () => {
    describe('In case of valid cdp session', () => {
      it('If onResolve exist in requestStateObj', () => {
        bridge.sendResponse(keyObject, params);
        expect(params.onResolve).to.not.be.undefined;
        expect(params.onResolve.calledOnce).to.be.true;
      });

      it('If onResolve does not exists behave normally', () => {
        delete params.onResolve;
        bridge.sendResponse(keyObject, params);
        expect(params.onResolve).to.be.undefined;
      });
    });

    describe('In case of invalid cdp session', () => {
      it('Should not try to check for onResolve function call', () => {
        keyObject.isPlaywright = false;
        keyObject.isPuppeteer = false;
        bridge.sendResponse(keyObject, params);
        expect(params.onResolve.notCalled).to.be.true;
      });
    });
  });

  describe('sendErrorResponse', () => {
    const mockRespondWithError = {};
    const mockStopSelenium = {};
    const mockTM = {};
    const mockPublish = {};
    const mockSetData = {};

    beforeEach(() => {
      params = {
        ...params,
        data: "{}",
        request: {
          url: 'https://localhost:8081'
        },
        response: {
          writeHead: sinon.stub()
        }
      }

      mockSetData.stub = sinon.stub(ha, 'setData');
      mockRespondWithError.stub = sinon.stub(helper, 'respondWithError');
      mockStopSelenium.stub = sinon.stub(helper, 'stopSeleniumClock');
      mockTM.stub = sinon.stub(helper, 'timeoutManagerUpdateTimeout');
      mockPublish.stub = sinon.stub(pubsub, 'publish');
    });

    afterEach(() => {
      mockPublish.stub.restore();
      mockSetData.stub.restore();
      mockTM.stub.restore();
      mockStopSelenium.stub.restore();
      mockRespondWithError.stub.restore();
    });

    describe('In case of valid cdp session', () => {
      it('If onResolve exist in requestStateObj', () => {
        bridge.sendErrorResponse(keyObject, params);
        expect(params.onResolve.calledOnce).to.be.true;
      });

      it('If onResolve does not exists behave normally', () => {
        delete params.onResolve;
        bridge.sendErrorResponse(keyObject, params);
        expect(params.onResolve).to.be.undefined;
      });
    });

    describe('In case of invalid cdp session', () => {
      it('Should not try to check for onResolve function call', () => {
        keyObject.isPlaywright = false;
        keyObject.isPuppeteer = false;
        bridge.sendErrorResponse(keyObject, params);
        expect(params.onResolve.notCalled).to.be.true;
      })
    });
  });
});
