'use strict';

const { assert } = require('chai');
const sinon = require('sinon');

const { mapUnsupportedRequestToW3C } = require("../../utils/commandMapper/mapUnsupportedToW3C");

describe('mapUnsupportedToW3C', () => {
  let request = {
    headers: {}
  };

  it('should call getElementInView mapped command', () => {
    request.path = '/wd/hub/session/abcde/element/xyz/location_in_view';
    request.method = 'GET';
    const mappedRequest = mapUnsupportedRequestToW3C(request);
    assert.deepEqual(mappedRequest, {
      body: '{"script":"arguments[0].scrollIntoView(true); return arguments[0].getBoundingClientRect()","args":[{"ELEMENT":"xyz","element-6066-11e4-a52e-4f735466cecf":"xyz"}]}',
      path: '/wd/hub/session/abcde/execute/sync',
      headers: { 'content-length': 162 },
      method: 'POST',
    });
  });
});
