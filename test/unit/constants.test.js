'use strict';

const expect = require('chai').expect;
const constants = require('../../constants');

describe('constants.js', () => {
  it('generateDefaultRetryDelayArray for tests', () => {
    expect(constants.generateDefaultRetryDelayArray()).to
      .deep.equal([0, 5000, 10000, 20000, 40000, 80000]);
  });

  it('generateDefaultRetryDelayArray for tests', () => {
    constants.isTestingEnv = true;
    expect(constants.DEFAULT_RETRY_DELAY).to.deep.equal([0, 0, 0, 0, 0]);
  });

  it('should return "production" when isProductionEnv is true', () => {
    constants.isProductionEnv = true;
    constants.isStagingEnv = false;
    expect(constants.getEnvNameForTerminal()).to.equal('production');
  });

  it('should return "staging" when isStagingEnv is true', () => {
    constants.isProductionEnv = false;
    constants.isStagingEnv = true;
    expect(constants.getEnvNameForTerminal()).to.equal('staging');
  });

  it('should return "development" when neither isProductionEnv nor isStagingEnv is true', () => {
    constants.isProductionEnv = false;
    constants.isStagingEnv = false;
    expect(constants.getEnvNameForTerminal()).to.equal('development');
  });
});
