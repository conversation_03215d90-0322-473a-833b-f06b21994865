const sinon = require('sinon');

const mainAcceptSSLHandler = require('../../../helpers/customSeleniumHandling/acceptSSLHandler/mainAcceptSSLHandler').runAcceptSSLHandler;
const iOSAcceptSSLHandler = require('../../../helpers/customSeleniumHandling/acceptSSLHandler/iOSAcceptSSLCertHandler');
const safariAcceptSSLHandler = require('../../../helpers/customSeleniumHandling/acceptSSLHandler/safariAcceptSSLHandler');
const customExecutorHelper = require('../../../helpers/customSeleniumHandling/customExecutorHelper');
const ERRORS_MAP = require('../../../constants').CUSTOM_EXECUTOR_ERRORS_ACCEPT_SSL;

const EXECUTOR_TYPE = 'acceptSsl';

describe("mainAcceptSSLHandler", () => {

  let keyObject = {};
  let requestStateObj = {
    req_data: JSON.stringify({
      script: 'browserstack_executor: {"action": "acceptSsl"}'
    }),
    clientSessionID: "some_random_id"
  };

  beforeEach(() => {
    keyObject = {};
    sinon.stub(customExecutorHelper, 'instrumentAndSendError');
    sinon.stub(customExecutorHelper, 'instrumentAndSendExecutorResponse');
  });

  afterEach(() => {
    customExecutorHelper.instrumentAndSendError.restore();
    customExecutorHelper.instrumentAndSendExecutorResponse.restore();
  });


  context("IOS executor", () => {

    context("it calls sendError()", () => {
      it("if os is ios and devicename is false (it is desktop)", () => {
        keyObject['os'] = 'ios';
        keyObject['deviceName'] = false;
        mainAcceptSSLHandler(keyObject, requestStateObj);
        sinon.assert.calledWith(customExecutorHelper.instrumentAndSendError, EXECUTOR_TYPE, keyObject, requestStateObj, ERRORS_MAP.unsupported_browser);
      });
      it("if ios version is less than 11", () => {
        keyObject['os'] = 'ios';
        keyObject['deviceName'] = "random device";
        keyObject['os_version'] = 10;
        mainAcceptSSLHandler(keyObject, requestStateObj);
        sinon.assert.calledWith(customExecutorHelper.instrumentAndSendError, EXECUTOR_TYPE, keyObject, requestStateObj, ERRORS_MAP.unsupported_on_ios10_or_below);
      });
    });

    it("calls iOSSLHandler on success case", () => {
      keyObject['os'] = 'ios';
      keyObject['deviceName'] = 'random-11';
      keyObject['os_version'] = 11;
      
      sinon.stub(iOSAcceptSSLHandler, 'sslHandler').callsArgWith(1, keyObject)
      mainAcceptSSLHandler(keyObject, requestStateObj);
      
      sinon.assert.calledWith(iOSAcceptSSLHandler.sslHandler,keyObject);
      sinon.assert.calledWith(customExecutorHelper.instrumentAndSendExecutorResponse, EXECUTOR_TYPE, keyObject, requestStateObj, false);
      
      iOSAcceptSSLHandler.sslHandler.restore();
    });
  });
  
  context("Safari executor", () => {
    it("calls safari executor", () => {
      keyObject['os'] = 'random';
      keyObject['browser'] = 'safari';

      sinon.stub(safariAcceptSSLHandler, 'sslHandler').callsArgWith(2, keyObject)
      mainAcceptSSLHandler(keyObject, requestStateObj);
      
      sinon.assert.calledWith(safariAcceptSSLHandler.sslHandler,keyObject);
      sinon.assert.calledWith(customExecutorHelper.instrumentAndSendExecutorResponse, EXECUTOR_TYPE, keyObject, requestStateObj, false);
      
      safariAcceptSSLHandler.sslHandler.restore();
     });
  });


  context("other executor", () => {
    it("throws error", () => {
      keyObject['os'] = 'random';
      mainAcceptSSLHandler(keyObject, requestStateObj);
      sinon.assert.calledWith(customExecutorHelper.instrumentAndSendError, EXECUTOR_TYPE, keyObject, requestStateObj, ERRORS_MAP.unsupported_browser)
    });
  });
});
