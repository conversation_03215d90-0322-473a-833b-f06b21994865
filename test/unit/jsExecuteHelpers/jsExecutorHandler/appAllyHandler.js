'use strict';

const sinon = require("sinon");
const appAllyHandler = require("../../../../helpers/customSeleniumHandling/jsExecutorHandler/appAllyHandler");
const customExecutorHelper = require("../../../../helpers/customSeleniumHandling/customExecutorHelper");
const requestlib = require("../../../../lib/request");
const constants = require("../../../../constants");
const errors = constants.APP_ALLY_CUSTOM_EXECUTOR_ERRORS;

describe("appAllyHandler ", () => {
  context("App Ally Screenshot error handling", () => {
    beforeEach(() => {
      sinon.stub(customExecutorHelper, "instrumentAndSendError");
    });

    afterEach(() => {
      customExecutorHelper.instrumentAndSendError.restore();
    });

    it("Error as argument not passed", async () => {
      let keyObject = {
        appTesting: true,
      };

      let requestStateObj = {
      req_data: JSON.stringify({
          script: 'browserstack_executor: {"action": "appAllyScreenshot", "arguments": {}}',
      }),
      clientSessionID: "random_session_id",
      };

      let parsedCommand = JSON.parse(
        JSON.parse(requestStateObj.req_data).script.split(
          "browserstack_executor:"
        )[1]
      );

      await appAllyHandler.appAllyScreenshotHandler(
        keyObject,
        requestStateObj,
        parsedCommand
      );
      sinon.assert.calledWith(
        customExecutorHelper.instrumentAndSendError,
        'app_ally_custom_executor',
        keyObject,
        requestStateObj,
        errors.invalid_arg_passed
      );
    });

    it("Error as argument not passed 2", async () => {
      let keyObject = {
        appTesting: true,
      };

      let requestStateObj = {
        req_data: JSON.stringify({
            script: 'browserstack_executor: {"action": "appAllyScreenshot", "arguments": {"thTestRunUuid": "thTestRunUuid"}}',
        }),
        clientSessionID: "random_session_id",
      };

      let parsedCommand = JSON.parse(
        JSON.parse(requestStateObj.req_data).script.split(
          "browserstack_executor:"
        )[1]
      );

      await appAllyHandler.appAllyScreenshotHandler(
        keyObject,
        requestStateObj,
        parsedCommand
      );
      sinon.assert.calledWith(
        customExecutorHelper.instrumentAndSendError,
        'app_ally_custom_executor',
        keyObject,
        requestStateObj,
        errors.invalid_arg_passed
      );
    });

    it("Error as argument not passed 3", async () => {
      let keyObject = {
        appTesting: true,
      };

      let requestStateObj = {
        req_data: JSON.stringify({
            script: 'browserstack_executor: {"action": "appAllyScreenshot", "arguments": {"thTestRunUuid": "thTestRunUuid", "thBuildUuid": "thBuildUuid"}}',
        }),
        clientSessionID: "random_session_id",
      };

      let parsedCommand = JSON.parse(
        JSON.parse(requestStateObj.req_data).script.split(
          "browserstack_executor:"
        )[1]
      );

      await appAllyHandler.appAllyScreenshotHandler(
        keyObject,
        requestStateObj,
        parsedCommand
      );
      sinon.assert.calledWith(
        customExecutorHelper.instrumentAndSendError,
        'app_ally_custom_executor',
        keyObject,
        requestStateObj,
        errors.invalid_arg_passed
      );
    });

    it("Error as argument not passed 4", async () => {
      let keyObject = {
        appTesting: true,
      };

      let requestStateObj = {
        req_data: JSON.stringify({
            script: 'browserstack_executor: {"action": "appAllyScreenshot", "arguments": {"thTestRunUuid": "thTestRunUuid", "thBuildUuid": "thBuildUuid", "method": "method"}}',
        }),
        clientSessionID: "random_session_id",
      };

      let parsedCommand = JSON.parse(
        JSON.parse(requestStateObj.req_data).script.split(
          "browserstack_executor:"
        )[1]
      );

      await appAllyHandler.appAllyScreenshotHandler(
        keyObject,
        requestStateObj,
        parsedCommand
      );
      sinon.assert.calledWith(
        customExecutorHelper.instrumentAndSendError,
        'app_ally_custom_executor',
        keyObject,
        requestStateObj,
        errors.invalid_arg_passed
      );
    });

    it("Error as argument not passed 5", async () => {
      let keyObject = {
        appTesting: true,
      };

      let requestStateObj = {
        req_data: JSON.stringify({
            script: 'browserstack_executor: {"action": "appAllyScreenshot", "arguments": {"thTestRunUuid": "thTestRunUuid", "thBuildUuid": "thBuildUuid", "method": "method", "authHeader": "authHeader"}}',
        }),
        clientSessionID: "random_session_id",
      };

      let parsedCommand = JSON.parse(
        JSON.parse(requestStateObj.req_data).script.split(
          "browserstack_executor:"
        )[1]
      );

      await appAllyHandler.appAllyScreenshotHandler(
        keyObject,
        requestStateObj,
        parsedCommand
      );
      sinon.assert.calledWith(
        customExecutorHelper.instrumentAndSendError,
        'app_ally_custom_executor',
        keyObject,
        requestStateObj,
        errors.invalid_arg_passed
      );
    });

    it("Error as argument not passed 6", async () => {
      let keyObject = {
        appTesting: true,
      };

      let requestStateObj = {
        req_data: JSON.stringify({
            script: 'browserstack_executor: {"action": "appAllyScreenshot", "arguments": {"thTestRunUuid": "thTestRunUuid", "thBuildUuid": "thBuildUuid", "method": "method", "authHeader": "authHeader", "scanTimestamp": "scanTimestamp"}}',
        }),
        clientSessionID: "random_session_id",
      };

      let parsedCommand = JSON.parse(
        JSON.parse(requestStateObj.req_data).script.split(
          "browserstack_executor:"
        )[1]
      );

      await appAllyHandler.appAllyScreenshotHandler(
        keyObject,
        requestStateObj,
        parsedCommand
      );
      sinon.assert.calledWith(
        customExecutorHelper.instrumentAndSendError,
        'app_ally_custom_executor',
        keyObject,
        requestStateObj,
        errors.invalid_arg_passed
      );
    });

    it("Error as argument not passed 7", async () => {
      let keyObject = {
        appTesting: false,
      };

      let requestStateObj = {
        req_data: JSON.stringify({
            script: 'browserstack_executor: {"action": "appAllyScreenshot", "arguments": {"thTestRunUuid": "thTestRunUuid", "thBuildUuid": "thBuildUuid", "method": "method", "authHeader": "authHeader", "scanTimestamp": "scanTimestamp", "thJwtToken": "thJwtToken"}}',
        }),
        clientSessionID: "random_session_id",
      };

      let parsedCommand = JSON.parse(
        JSON.parse(requestStateObj.req_data).script.split(
          "browserstack_executor:"
        )[1]
      );

      await appAllyHandler.appAllyScreenshotHandler(
        keyObject,
        requestStateObj,
        parsedCommand
      );
      sinon.assert.calledWith(
        customExecutorHelper.instrumentAndSendError,
        'app_ally_custom_executor',
        keyObject,
        requestStateObj,
        errors.invalid_combination_passed
      );
    });
  });

  context("App Ally Screenshot", () => {
    it("Call terminal request returns 200", async () => {
      let keyObject = {
        appTesting: true,
      };

      let requestStateObj = {
        req_data: JSON.stringify({
            script: 'browserstack_executor: {"action": "appAllyScreenshot", "arguments": {"thTestRunUuid": "thTestRunUuid", "thBuildUuid": "thBuildUuid", "method": "method", "authHeader": "authHeader", "scanTimestamp": "scanTimestamp", "thJwtToken": "thJwtToken"}}',
        }),
        clientSessionID: "random_session_id",
      };

      let parsedCommand = JSON.parse(
        JSON.parse(requestStateObj.req_data).script.split(
          "browserstack_executor:"
        )[1]
      );
      const result = {
        data: {},
        statusCode: 200,
      }
      sinon.stub(requestlib, "call").returns(result);
      sinon.stub(customExecutorHelper, "instrumentAndSendExecutorResponse");

      await appAllyHandler.appAllyScreenshotHandler(
        keyObject,
        requestStateObj,
        parsedCommand
      );
      sinon.assert.calledWith(
        customExecutorHelper.instrumentAndSendExecutorResponse,
        'app_ally_custom_executor',
        keyObject,
        requestStateObj,
        false
      );
      requestlib.call.restore();
      customExecutorHelper.instrumentAndSendExecutorResponse.restore();
    });

    it("Call terminal request returns non 200", async () => {
      let keyObject = {
        appTesting: true,
      };

      let requestStateObj = {
        req_data: JSON.stringify({
            script: 'browserstack_executor: {"action": "appAllyScreenshot", "arguments": {"thTestRunUuid": "thTestRunUuid", "thBuildUuid": "thBuildUuid", "method": "method", "authHeader": "authHeader", "scanTimestamp": "scanTimestamp", "thJwtToken": "thJwtToken"}}',
        }),
        clientSessionID: "random_session_id",
      };

      let parsedCommand = JSON.parse(
        JSON.parse(requestStateObj.req_data).script.split(
          "browserstack_executor:"
        )[1]
      );
      const result = {
        data: {},
        statusCode: 400,
      }
      sinon.stub(requestlib, "call").returns(result);
      sinon.stub(customExecutorHelper, "instrumentAndSendExecutorResponse");

      await appAllyHandler.appAllyScreenshotHandler(
        keyObject,
        requestStateObj,
        parsedCommand
      );
      sinon.assert.calledWith(
        customExecutorHelper.instrumentAndSendExecutorResponse,
        'app_ally_custom_executor',
        keyObject,
        requestStateObj,
        false
      );
      requestlib.call.restore();
      customExecutorHelper.instrumentAndSendExecutorResponse.restore();
    });
  });
});
