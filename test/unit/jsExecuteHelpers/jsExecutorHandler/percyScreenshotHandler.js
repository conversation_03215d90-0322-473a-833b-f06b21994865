'use strict';

const sinon = require('sinon');
const rewire = require('rewire');


const handlerRewire = rewire('../../../../helpers/customSeleniumHandling/jsExecutorHandler/percyScreenshotHandler');
const customExecutorHelper = require('../../../../helpers/customSeleniumHandling/customExecutorHelper');
const pubSub = require('../../../../pubSub');
const browserstack = require('../../../../browserstack');
const constants = require('../../../../constants');
const requestLib = require('../../../../lib/request');
const origHelper = require('../../../../helper');
const HubLogger = require('../../../../log');
const CapabilitiesValidator = require('../../../../helpers/customSeleniumHandling/jsExecutorHandler/percyCapabilityValidator');

const { assert, expect } = require('chai');

// eslint-disable-next-line max-lines-per-function
describe('percyScreenshotHandler', () => {
  const ogPercyScreenshotHandler = handlerRewire.__get__('percyScreenshotHandler');
  const ogPercyScreenshotBegin = handlerRewire.__get__('percyScreenshotBegin');
  const ogIsValidPercyUrl = handlerRewire.__get__('isValidPercyUrl');
  const ogPercyScreenshot = handlerRewire.__get__('percyScreenshot');
  const ogPercyScreenshotEnd = handlerRewire.__get__('percyScreenshotEnd');
  const ogPercyScreenshotSuccessResponse = handlerRewire.__get__('percyScreenshotSuccessResponse');
  const ogMarkAsPercy = handlerRewire.__get__('markAsPercy');
  const ogPercyScreenshotBeginUpdateKeyobject = handlerRewire.__get__('percyScreenshotBeginUpdateKeyobject');
  const ogSendDataToZombie = handlerRewire.__get__('sendDataToZombie');
  const ogMarkNumberOfTiles = handlerRewire.__get__('markNumberOfTiles');
  const executorType = handlerRewire.__get__('executorType');
  const errors = handlerRewire.__get__('errors');
  const ogSetupPercyOnAutomateTerminal = handlerRewire.__get__('setupPercyOnAutomateTerminal');
  const ogDisableAnimations = handlerRewire.__get__('disableAnimations');
  const ogZombieKind = handlerRewire.__get__('zombieKind');
  const ogGetDeviceSinatraPort = handlerRewire.__get__('getDeviceSinatraPort');
  const ogGetDeviceName = handlerRewire.__get__('getDeviceName');
  const ogGetTeamName = handlerRewire.__get__('getTeamName');
  const ogGetProjectId = handlerRewire.__get__('getProjectId');
  const ogGetMetadata = handlerRewire.__get__('getMetadata');
  const ogComparisonCaps = handlerRewire.__get__('comparisonCaps');

  const sessionId = 'random_session_id';
  const percyBuildId = '12345';
  let screenshotType = 'fullpage';
  const newscreenshotType = 'singlepage';
  const percyBuildUrl = 'https://percy.io/9560f98d/app-proj-temp/builds/25567736';
  const canaryPercyBuildUrl = 'https://canary.percy.io/9560f98d/app-proj-temp/builds/25567736';
  const enterprisePercyBuildUrl = 'https://percy-enterprise.browserstack.com/9560f98d/web/test-poa/builds/40030040';
  const percyScreenshotUrl = 'random_screenshot_url';
  const port = 5555;
  const name = 'random_screenshot_name';
  const status = 'success';
  const statusMessage = '';
  const scaleFactor = 1;
  const options = { "numOfTiles": 4, "deviceHeight": 2400 };
  const projectId = 'percy-dev';
  const APP_PERCY_TEAM = 'app-percy';
  const PERCY_PLATFORM_TEAM = 'percy-platform';
  const group_id = 16;

  const keyObject = {
    name: 'temp',
    device: '**************',
    rails_session_id: sessionId,
    rproxyHost: '***********',
    key: 'abcd-qwer',
    appTesting: true,
    markedAsPercy: false,
    user_id: 123,
    accesskey: 'random_access_key',
    build_hash: 'random_build_hash',
    deviceName: 'iPhone 12 - 14',
    os_version: '14',
    os: 'ios',
    selenium_version: '3.12.2',
    browser: 'Safari',
    browser_version: '92',
    port: 5555,
    platformDetails: { platformName: 'ios' },
    group_id: 16
  };
  const sessionInfo = {
    browser_name: keyObject.browser, browser_version: keyObject.browser_version, os_version: '10', device: 'win_10_safari_92',
  };

  const result = {
    data: 'dummy data'
  };

  let requestStateObj = null;

  let parsedCommand = null;

  const setRequestStateParams = (args = {}) => {
    const executorParams = {
      action: 'percyScreenshot',
    };
    if (args !== {}) {
      executorParams.arguments = args;
    }

    requestStateObj = {
      req_data: JSON.stringify({
        script: `browserstack_executor: ${JSON.stringify(executorParams)}`
      }),
      clientSessionID: sessionId,
    };

    parsedCommand = JSON.parse(
      JSON.parse(requestStateObj.req_data).script.split(
        'browserstack_executor:'
      )[1]
    );
  };

  let postBrowserStackStub;
  const stubPostBrowserstack = (resolveWith = {}) => {
    if (postBrowserStackStub) postBrowserStackStub.restore();
    postBrowserStackStub = sinon.stub(browserstack, 'postBrowserStack', (bsUrl, params, _1, _2, resolve) => {
      resolve(JSON.stringify(resolveWith));
    });
  };

  // helpers
  const expectErrorResponse = (reason = errors.invalid_arg_passed) => {
    sinon.assert.called(customExecutorHelper.instrumentAndSendError);
    sinon.assert.calledWith(
      customExecutorHelper.instrumentAndSendError,
      executorType,
      keyObject,
      requestStateObj,
      reason
    );
  };

  const percyScreenshotSuccessResponseStub = sinon.stub();
  const expectSuccessResponse = () => {
    sinon.assert.called(percyScreenshotSuccessResponseStub);
    sinon.assert.calledWith(
      percyScreenshotSuccessResponseStub,
      keyObject,
      requestStateObj
    );
  };

  const expectSuccessResponseWithData = (res) => {
    sinon.assert.called(percyScreenshotSuccessResponseStub);
    sinon.assert.calledWith(
      percyScreenshotSuccessResponseStub,
      keyObject,
      requestStateObj,
      'screenshot',
      res
    );
  };

  const markAsPercyStub = sinon.stub();
  const disableAnimationsStub = sinon.stub();
  const percyScreenshotBeginUpdateKeyobjectStub = sinon.stub();
  const sendDataToZombieStub = sinon.stub();
  const markNumberOfTilesStub = sinon.stub();

  // call it very first time to initialize variables to defaults
  before(() => {
    setRequestStateParams();
    stubPostBrowserstack();
    sinon.restore();
  });

  after(() => {
    postBrowserStackStub.restore();
    sinon.restore();
  });

  beforeEach(() => {
    sinon.stub(customExecutorHelper, 'instrumentAndSendError');
    sinon.stub(customExecutorHelper, 'instrumentAndSendExecutorResponse');
    handlerRewire.__set__('percyScreenshotSuccessResponse', percyScreenshotSuccessResponseStub);
    handlerRewire.__set__('markAsPercy', markAsPercyStub);
    handlerRewire.__set__('disableAnimations', disableAnimationsStub);
    handlerRewire.__set__('percyScreenshotBeginUpdateKeyobject', percyScreenshotBeginUpdateKeyobjectStub);
    handlerRewire.__set__('sendDataToZombie', sendDataToZombieStub);
    handlerRewire.__set__('markNumberOfTiles', markNumberOfTilesStub);
  });

  afterEach(() => {
    customExecutorHelper.instrumentAndSendError.restore();
    customExecutorHelper.instrumentAndSendExecutorResponse.restore();
    percyScreenshotSuccessResponseStub.reset();
    markAsPercyStub.reset();
    disableAnimationsStub.reset();
    percyScreenshotBeginUpdateKeyobjectStub.reset();
    sendDataToZombieStub.reset();
    markNumberOfTilesStub.reset();
  });

  context('percyScreenshotHandler', () => {
    const percyScreenshotBeginStub = sinon.stub();
    const percyScreenshotEndStub = sinon.stub();
    const percyScreenshotStub = sinon.stub();

    before(() => {
      handlerRewire.__set__('percyScreenshotBegin', percyScreenshotBeginStub);
      handlerRewire.__set__('percyScreenshotEnd', percyScreenshotEndStub);
      handlerRewire.__set__('percyScreenshot', percyScreenshotStub);
    });

    after(() => {
      handlerRewire.__set__('percyScreenshotBegin', ogPercyScreenshotBegin);
      handlerRewire.__set__('percyScreenshotEnd', ogPercyScreenshotEnd);
      handlerRewire.__set__('percyScreenshot', ogPercyScreenshot);
    });

    afterEach(() => {
      percyScreenshotBeginStub.reset();
      percyScreenshotEndStub.reset();
      percyScreenshotStub.reset();
    });

    context('with invalid state', () => {
      it('sends error if state is not passed', async () => {
        await ogPercyScreenshotHandler(
          keyObject,
          requestStateObj,
          parsedCommand
        );

        expectErrorResponse();
      });

      it('sends error if args are not passed in parsedCommand', async () => {
        await ogPercyScreenshotHandler(
          keyObject,
          requestStateObj,
          {}
        );

        expectErrorResponse();
      });

      it('sends error if state is not valid', async () => {
        setRequestStateParams({ state: 'unknown' });
        await ogPercyScreenshotHandler(
          keyObject,
          requestStateObj,
          parsedCommand
        );

        expectErrorResponse();
      });
    });

    context('with valid state', () => {
      it('calls percyScreenshotBegin when state is begin', async () => {
        setRequestStateParams({ state: 'begin' });
        await ogPercyScreenshotHandler(
          keyObject,
          requestStateObj,
          parsedCommand
        );

        sinon.assert.called(percyScreenshotBeginStub);
        sinon.assert.calledWith(
          percyScreenshotBeginStub,
          keyObject,
          requestStateObj,
          parsedCommand
        );
      });

      it('calls percyScreenshotEnd when state is end', async () => {
        setRequestStateParams({ state: 'end' });
        await ogPercyScreenshotHandler(
          keyObject,
          requestStateObj,
          parsedCommand
        );

        sinon.assert.called(percyScreenshotEndStub);
        sinon.assert.calledWith(
          percyScreenshotEndStub,
          keyObject,
          requestStateObj,
          parsedCommand
        );
      });

      it('calls percyScreenshot when state is screenshot', async () => {
        setRequestStateParams({ state: 'screenshot' });
        await ogPercyScreenshotHandler(
          keyObject,
          requestStateObj,
          parsedCommand
        );

        sinon.assert.called(percyScreenshotStub);
        sinon.assert.calledWith(
          percyScreenshotStub,
          keyObject,
          requestStateObj,
          parsedCommand
        );
      });
    });
  });

  context('percyScreenshotBegin', () => {
    afterEach(() => {
      setRequestStateParams();
    });

    context('with keyObject.markedAsPercy true', () => {
      before(() => {
        keyObject.markedAsPercy = true;
      });

      after(() => {
        keyObject.markedAsPercy = false;
      });

      it('sends percyScreenshotSuccessResponse in early exit if true', async () => {

        await ogPercyScreenshotBegin(
          keyObject,
          requestStateObj,
          parsedCommand
        );

        expectSuccessResponse();
        sinon.assert.called(percyScreenshotBeginUpdateKeyobjectStub);
        sinon.assert.notCalled(postBrowserStackStub);
      });
    });

    context('selenium version less than supported ', () => {
      before(() => {
        keyObject.selenium_version = '3.5.2';
        keyObject.appTesting = false;
      });

      after(() => {
        keyObject.selenium_version = '3.12.2';
        keyObject.appTesting = true;
      });

      it('send error with invalid selenium_version', async () => {
        await ogPercyScreenshotBegin(
          keyObject,
          requestStateObj,
          parsedCommand
        );
        expectErrorResponse({
          code: "invalid_combination_passed",
          message: "[PERCY_UNSUPPORTED_COMBINATION_PASSED] Selenium Version: 3.5.2 used is less than supported. Refer: https://www.browserstack.com/docs/percy/integrate/functional-and-visual#prerequisite"
        });
      });
    });

    context('browser version not present', () => {
      before(() => {
        delete keyObject['browser_version']
        keyObject.appTesting = false;
      });

      after(() => {
        keyObject['browser_version'] = '92';
        keyObject.appTesting = true;
      });

      it('send error with OS/Browser not supported', async () => {
        await ogPercyScreenshotBegin(
          keyObject,
          requestStateObj,
          parsedCommand
        );

        expectErrorResponse({
          code: "invalid_combination_passed",
          message: "[PERCY_UNSUPPORTED_COMBINATION_PASSED] OS/Browser Combination is not supported in Percy"
        });
      });
    });

    context('with invalid params', () => {
      it('sends error if percyBuildId is not passed', async () => {
        setRequestStateParams({ percyBuildUrl, name });

        await ogPercyScreenshotBegin(
          keyObject,
          requestStateObj,
          parsedCommand
        );

        expectErrorResponse();
      });

      it('sends error if percyBuildUrl is not passed', async () => {
        setRequestStateParams({ percyBuildId, name });

        await ogPercyScreenshotHandler(
          keyObject,
          requestStateObj,
          parsedCommand
        );

        expectErrorResponse();
      });

      it('sends error if invalid percyBuildUrl is passed', async () => {
        setRequestStateParams({ percyBuildId, name, percyBuildUrl: 'javascript:alert(document.domain)' });

        await ogPercyScreenshotHandler(
          keyObject,
          requestStateObj,
          parsedCommand
        );

        expectErrorResponse();
      });

      it('sends error if name is not passed', async () => {
        setRequestStateParams({ percyBuildId, percyBuildUrl });

        await ogPercyScreenshotHandler(
          keyObject,
          requestStateObj,
          parsedCommand
        );

        expectErrorResponse();
      });
    });

    context('with valid params', () => {
      let getProjectIdStub;

      beforeEach(() => {
        getProjectIdStub = sinon.stub();
        handlerRewire.__set__('getProjectId', getProjectIdStub);
        setRequestStateParams({ percyBuildId, percyBuildUrl, name, port });
        keyObject.os = 'android';
      });

      afterEach(() => {
        getProjectIdStub.reset();
        if (postBrowserStackStub) postBrowserStackStub.reset();
        keyObject.markedAsPercy = false;
        keyObject.percyBeginTime = -1;
      });

      context('with appTesting true', () => {
        it('calls markAsPercy in async and returns postive response', async () => {
          getProjectIdStub.returns(projectId);
          await ogPercyScreenshotBegin(
            keyObject,
            requestStateObj,
            parsedCommand
          );

          sinon.assert.called(percyScreenshotBeginUpdateKeyobjectStub);
          sinon.assert.called(getProjectIdStub);
          sinon.assert.called(markAsPercyStub);
          sinon.assert.calledWith(
            markAsPercyStub,
            keyObject,
            percyBuildId,
            percyBuildUrl
          );
          sinon.assert.called(disableAnimationsStub);

          expectSuccessResponse();
        });

        context('capability validator should not work be triggered with app percy', () => {
          before(() => {
            keyObject.selenium_version = '3.5.2';
          });

          after(() => {
            keyObject.selenium_version = '3.12.2';
          });

          it('should not send error with invalid selenium_version', async () => {
            getProjectIdStub.returns(projectId);
            const capabilityValidator = sinon.stub(CapabilitiesValidator.prototype, 'validateBrowserOSVersions')
            await ogPercyScreenshotBegin(
              keyObject,
              requestStateObj,
              parsedCommand
            );

            sinon.assert.notCalled(capabilityValidator);
            sinon.assert.called(percyScreenshotBeginUpdateKeyobjectStub);
            sinon.assert.called(getProjectIdStub);
            sinon.assert.called(markAsPercyStub);
            sinon.assert.calledWith(
              markAsPercyStub,
              keyObject,
              percyBuildId,
              percyBuildUrl
            );
            sinon.assert.called(disableAnimationsStub);

            expectSuccessResponse();
            capabilityValidator.restore()
          });
        });
      });

      context('with appTesting false -> automate', () => {
        let setupPercyOnAutomateTerminalStub;
        let getProjectIdStub;

        beforeEach(() => {
          keyObject.appTesting = false;
          getProjectIdStub = sinon.stub();
          handlerRewire.__set__('getProjectId', getProjectIdStub);
          setupPercyOnAutomateTerminalStub = sinon.stub();
          handlerRewire.__set__('setupPercyOnAutomateTerminal', setupPercyOnAutomateTerminalStub);
        });

        afterEach(() => {
          getProjectIdStub.reset();
          keyObject.appTesting = true;
          setupPercyOnAutomateTerminalStub.reset();
        });

        it('calls markAsPercy in async and returns positive response', async () => {
          getProjectIdStub.returns(projectId);
          setupPercyOnAutomateTerminalStub.returns(true);

          await ogPercyScreenshotBegin(
            keyObject,
            requestStateObj,
            parsedCommand
          );

          sinon.assert.called(getProjectIdStub);
          sinon.assert.called(setupPercyOnAutomateTerminalStub);
          sinon.assert.calledWith(
            setupPercyOnAutomateTerminalStub,
            keyObject,
            projectId
          );
          sinon.assert.called(percyScreenshotBeginUpdateKeyobjectStub);
          sinon.assert.called(markAsPercyStub);
          sinon.assert.calledWith(
            markAsPercyStub,
            keyObject,
            percyBuildId,
            percyBuildUrl
          );
          sinon.assert.called(disableAnimationsStub);

          expectSuccessResponse();
        });

        it('doesnot call markAsPercy in async and returns negative response', async () => {
          getProjectIdStub.returns(projectId);
          setupPercyOnAutomateTerminalStub.returns(false);

          await ogPercyScreenshotBegin(
            keyObject,
            requestStateObj,
            parsedCommand
          );

          sinon.assert.called(getProjectIdStub);
          sinon.assert.called(setupPercyOnAutomateTerminalStub);
          sinon.assert.calledWith(
            setupPercyOnAutomateTerminalStub,
            keyObject,
            projectId
          );

          assert(markAsPercyStub.notCalled, 'Expected markAsPercyStub to be called 0 times');
          assert(disableAnimationsStub.notCalled, 'Expected disableAnimationsStub to be called 0 times');
          expectErrorResponse(errors.unknown);
        });
      });
    });
  });

  context('percyScreenshotEnd', () => {
    context('with invalid params', () => {
      context('with undefined percyScreenshotUrl', () => {
        it('sends error', () => {
          setRequestStateParams({ name, status });

          ogPercyScreenshotEnd(
            keyObject,
            requestStateObj,
            parsedCommand
          );

          expectErrorResponse();
        });
      });

      context('with undefined name', () => {
        it('sends error', () => {
          setRequestStateParams({ percyScreenshotUrl, status });

          ogPercyScreenshotEnd(
            keyObject,
            requestStateObj,
            parsedCommand
          );

          expectErrorResponse();
        });
      });

      context('with undefined status', () => {
        it('sends error', () => {
          setRequestStateParams({ percyScreenshotUrl, name });

          ogPercyScreenshotEnd(
            keyObject,
            requestStateObj,
            parsedCommand
          );

          expectErrorResponse();
        });
      });

      context('with incorrect status', () => {
        it('sends error', () => {
          setRequestStateParams({ percyScreenshotUrl, status: 'invalid status', name });

          ogPercyScreenshotEnd(
            keyObject,
            requestStateObj,
            parsedCommand
          );

          expectErrorResponse();
        });
      });

      context('with non success status and no statusMessage', () => {
        it('sends error', () => {
          setRequestStateParams({ percyScreenshotUrl, status: 'failure', name });

          ogPercyScreenshotEnd(
            keyObject,
            requestStateObj,
            parsedCommand
          );

          expectErrorResponse();
        });
      });
    });

    context('with valid params', () => {
      context('with status == success', () => {
        beforeEach(() => {
          sinon.stub(HubLogger, 'miscLogger', () => {});
          keyObject.deviceName = 'IUIE123-900123';
          keyObject.percyNumberOfTiles = 1;
        });

        afterEach(() => {
          keyObject.percyNumberOfTiles = 0;
          keyObject.percyBeginTime = -1;
          HubLogger.miscLogger.restore();
        });

        context('with keyObject.percyBeginTime is not -1', () => {
          beforeEach(() => {
            keyObject.percyBeginTime = 1672511399000;
          });

          it('sends _1 kind to zombie', () => {
            setRequestStateParams({
              percyScreenshotUrl, name, status, statusMessage
            });

            ogPercyScreenshotEnd(
              keyObject,
              requestStateObj,
              parsedCommand
            );

            let expectedZombieData = {
              duration: sinon.match.number,
              automation_session_id: keyObject.rails_session_id,
              deviceName: ogGetDeviceName(keyObject),
              tiles: keyObject.percyNumberOfTiles,
              sync: false
            };

            let expectedDataToSend = {
              "session_id": keyObject.key,
              "kind": "app_percy_screenshot_time_1",
              "data": expectedZombieData,
              "os": keyObject.os.toLowerCase(),
              "machine": sinon.match.string,
              "os_version": keyObject.os_version,
              "team": APP_PERCY_TEAM
            };

            sinon.assert.called(sendDataToZombieStub);
            sinon.assert.calledWith(
              sendDataToZombieStub,
              expectedDataToSend
            );
            assert(HubLogger.miscLogger.notCalled, 'Expected hubLoggerStub to be called 0 times');

            expectSuccessResponse();
          });
        });

        context('with sync passed is not -1', () => {
          beforeEach(() => {
            keyObject.percyBeginTime = 1672511399000;
          });

          it('sends _1 kind to zombie', () => {
            setRequestStateParams({
              percyScreenshotUrl, name, status, statusMessage, sync: true
            });

            ogPercyScreenshotEnd(
              keyObject,
              requestStateObj,
              parsedCommand
            );

            let expectedZombieData = {
              duration: sinon.match.number,
              automation_session_id: keyObject.rails_session_id,
              deviceName: ogGetDeviceName(keyObject),
              tiles: keyObject.percyNumberOfTiles,
              sync: true
            };

            let expectedDataToSend = {
              "session_id": keyObject.key,
              "kind": "app_percy_screenshot_time_1",
              "data": expectedZombieData,
              "os": keyObject.os.toLowerCase(),
              "machine": sinon.match.string,
              "os_version": keyObject.os_version,
              "team": APP_PERCY_TEAM
            };

            sinon.assert.called(sendDataToZombieStub);
            sinon.assert.calledWith(
              sendDataToZombieStub,
              expectedDataToSend
            );
            assert(HubLogger.miscLogger.notCalled, 'Expected hubLoggerStub to be called 0 times');

            expectSuccessResponse();
          });
        });

        context('with keyObject.percyBeginTime -1', () => {
          beforeEach(() => {
            keyObject.percyBeginTime = -1;
          });

          it('sends _0 kind to zombie', () => {
            setRequestStateParams({
              percyScreenshotUrl, name, status, statusMessage
            });

            ogPercyScreenshotEnd(
              keyObject,
              requestStateObj,
              parsedCommand
            );

            let expectedZombieData = {
              duration: -1,
              automation_session_id: keyObject.rails_session_id,
              deviceName: ogGetDeviceName(keyObject),
              tiles: keyObject.percyNumberOfTiles,
              sync: false
            };

            let expectedDataToSend = {
              "session_id": keyObject.key,
              "kind": "app_percy_screenshot_time_0",
              "data": expectedZombieData,
              "os": keyObject.os.toLowerCase(),
              "machine": sinon.match.string,
              "os_version": keyObject.os_version,
              "team": APP_PERCY_TEAM
            };

            sinon.assert.called(sendDataToZombieStub);
            sinon.assert.calledWith(
              sendDataToZombieStub,
              expectedDataToSend
            );
            assert(HubLogger.miscLogger.notCalled, 'Expected hubLoggerStub to be called 0 times');

            expectSuccessResponse();
          });
        });
      });

      context('with status == failure', () => {
        it('sends success response', () => {
          // We do not expect percyScreenshotUrl in this case
          setRequestStateParams({
            name, status: 'failure', statusMessage: 'some message'
          });

          ogPercyScreenshotEnd(
            keyObject,
            requestStateObj,
            parsedCommand
          );

          expectSuccessResponse();
        });
      });
    });
  });

  context('percyScreenshot', () => {
    context('with appAutomate', () => {
      context('with invalid params', () => {
        context('with undefined scaleFactor', () => {
          it('sends error', () => {
            setRequestStateParams({ screenshotType, percyBuildId, options });

            ogPercyScreenshot(
              keyObject,
              requestStateObj,
              parsedCommand
            );

            expectErrorResponse();
          });
        });

        context('with undefined percyBuildId', () => {
          it('sends error', () => {
            setRequestStateParams({ screenshotType, scaleFactor, options });

            ogPercyScreenshot(
              keyObject,
              requestStateObj,
              parsedCommand
            );

            expectErrorResponse();
          });
        });

        context('with undefined screenshotType', () => {
          it('sends error', () => {
            setRequestStateParams({ percyBuildId, scaleFactor, options });

            ogPercyScreenshot(
              keyObject,
              requestStateObj,
              parsedCommand
            );

            expectErrorResponse();
          });
        });

        context('with undefined options', () => {
          it('sends error', () => {
            setRequestStateParams({ percyBuildId, scaleFactor, screenshotType });

            ogPercyScreenshot(
              keyObject,
              requestStateObj,
              parsedCommand
            );

            expectErrorResponse();
          });
        });
      });

      context('with valid params', () => {
        let requestLibCall;
        let requestAppendBstackHeader;
        let getProjectIdStub;

        beforeEach(() => {
          sinon.stub(HubLogger, 'miscLogger', () => {});
          getProjectIdStub = sinon.stub().returns('percy-prod');
          handlerRewire.__set__('getProjectId', getProjectIdStub);
        })
        afterEach(() => {
          if (requestLibCall) requestLibCall.restore();
          if (requestAppendBstackHeader) requestAppendBstackHeader.restore();
          HubLogger.miscLogger.restore();
          keyObject.markedAsPercy = false;
          getProjectIdStub.reset();
          screenshotType = 'fullpage';
        });

        it('sends success response with project id as default when not given', async () => {
          setRequestStateParams({
            percyBuildId, scaleFactor, screenshotType, options, port
          });

          const response = {
            statusCode: 200,
            data: JSON.stringify([
              {
                sha: 'dba003f59db4f1f9364b0edb4251a52ac56074caf99bf31520be97d2fc83cfc3-4',
                status_bar: null,
                nav_bar: null,
                header_height: 0,
                footer_height: 165,
                index: 0
              }
            ]),
          };

          requestLibCall = sinon.stub(requestLib, 'call').returns(Promise.resolve(response));
          await ogPercyScreenshot(
            keyObject,
            requestStateObj,
            parsedCommand
          );

          const expectedHeaders = {
            "accept": "application/json",
            "content-type": "application/json; charset=utf-8",
            "accept-encoding": "gzip;q=1.0,deflate;q=0.6,identity;q=0.3",
            "content-length": 256,
            "X-Source-Env-Type": "development",
            "BStack-Host": "temp"
          };

          requestAppendBstackHeader = sinon.stub(requestLib, 'appendBStackHostHeader').returns(expectedHeaders);

          const expectedBody = Buffer.from(JSON.stringify({
            appium_session_id: keyObject.key,
            automation_session_id: keyObject.rails_session_id,
            hub_session_id: keyObject.key,
            build_id: percyBuildId,
            project_id: 'percy-prod',
            screenshot_type: screenshotType,
            scale_factor: scaleFactor,
            port: port,
            options: options,
          }));

          const expectedTermOptions = {
            method: 'POST',
            body: expectedBody,
            hostname: keyObject.rproxyHost,
            port: 45671,
            timeout: 360 * 1000,
            path: `/app_percy/screenshot?device=${encodeURIComponent(keyObject.device)}`,
            headers: expectedHeaders,
          };

          const expectedDataToSend = {
            "session_id": keyObject.key,
            "kind": "app_percy_percy_screenshot_1",
            "data": sinon.match.object,
            "os": keyObject.os.toLowerCase(),
            "machine": sinon.match.string,
            "os_version": keyObject.os_version,
            "team": APP_PERCY_TEAM
          };

          sinon.assert.calledOnce(getProjectIdStub);
          sinon.assert.calledOnce(requestLibCall);
          sinon.assert.called(sendDataToZombieStub);
          sinon.assert.calledWith(requestLibCall, expectedTermOptions);
          sinon.assert.called(markNumberOfTilesStub);
          sinon.assert.calledWith(
            markNumberOfTilesStub,
            keyObject,
            1
          );
          sinon.assert.calledWith(
            sendDataToZombieStub,
            expectedDataToSend
          );
          assert(HubLogger.miscLogger.notCalled, 'Expected hubLoggerStub to be called 0 times');
          let res = {};
          res.data = response.data;
          expectSuccessResponseWithData(res);
        });

        it('sends success response with screenshotType is singlepage', async () => {
          screenshotType = 'singlepage';
          setRequestStateParams({
            percyBuildId, scaleFactor, screenshotType, options, projectId, port
          });

          const response = {
            statusCode: 200,
            data: JSON.stringify([
              {
                sha: 'dba003f59db4f1f9364b0edb4251a52ac56074caf99bf31520be97d2fc83cfc3-4',
                status_bar: null,
                nav_bar: null,
                header_height: 0,
                footer_height: 165,
                index: 0
              }
            ]),
          };

          requestLibCall = sinon.stub(requestLib, 'call').returns(Promise.resolve(response));
          await ogPercyScreenshot(
            keyObject,
            requestStateObj,
            parsedCommand
          );

          const expectedBody = Buffer.from(JSON.stringify({
            appium_session_id: keyObject.key,
            automation_session_id: keyObject.rails_session_id,
            hub_session_id: keyObject.key,
            build_id: percyBuildId,
            project_id: 'percy-prod',
            screenshot_type: screenshotType,
            scale_factor: scaleFactor,
            port: port,
            options: options,
          }));

          const expectedHeaders = {
            "accept": "application/json",
            "content-type": "application/json; charset=utf-8",
            "accept-encoding": "gzip;q=1.0,deflate;q=0.6,identity;q=0.3",
            "content-length": expectedBody.length,
            "X-Source-Env-Type": "development",
            "BStack-Host": "temp"
          };

          requestAppendBstackHeader = sinon.stub(requestLib, 'appendBStackHostHeader').returns(expectedHeaders);

          const expectedTermOptions = {
            method: 'POST',
            body: expectedBody,
            hostname: keyObject.rproxyHost,
            port: 45671,
            timeout: 360 * 1000,
            path: `/app_percy/screenshot?device=${encodeURIComponent(keyObject.device)}`,
            headers: expectedHeaders,
          };

          const expectedDataToSend = {
            "session_id": keyObject.key,
            "kind": "app_percy_percy_screenshot_1",
            "data": sinon.match.object,
            "os": keyObject.os.toLowerCase(),
            "machine": sinon.match.string,
            "os_version": keyObject.os_version,
            "team": APP_PERCY_TEAM
          };

          sinon.assert.calledOnce(getProjectIdStub);
          sinon.assert.calledOnce(requestLibCall);
          sinon.assert.called(sendDataToZombieStub);
          sinon.assert.calledWith(requestLibCall, expectedTermOptions);
          sinon.assert.called(markNumberOfTilesStub);
          sinon.assert.calledWith(
            markNumberOfTilesStub,
            keyObject,
            0
          );
          sinon.assert.calledWith(
            sendDataToZombieStub,
            expectedDataToSend
          );
          assert(HubLogger.miscLogger.notCalled, 'Expected hubLoggerStub to be called 0 times');
          let res = {};
          res.data = response.data;
          expectSuccessResponseWithData(res);
        });

        it('sends success response with project id as passed when given', async () => {
          setRequestStateParams({
            percyBuildId, scaleFactor, screenshotType, options, projectId, port
          });
          getProjectIdStub.returns(projectId);

          const response = {
            statusCode: 200,
            data: JSON.stringify([
              {
                sha: 'dba003f59db4f1f9364b0edb4251a52ac56074caf99bf31520be97d2fc83cfc3-4',
                status_bar: null,
                nav_bar: null,
                header_height: 0,
                footer_height: 165,
                index: 0
              }
            ])
          };

          requestLibCall = sinon.stub(requestLib, 'call').returns(Promise.resolve(response));
          await ogPercyScreenshot(
            keyObject,
            requestStateObj,
            parsedCommand
          );

          const expectedHeaders = {
            "accept": "application/json",
            "content-type": "application/json; charset=utf-8",
            "accept-encoding": "gzip;q=1.0,deflate;q=0.6,identity;q=0.3",
            "content-length": 255,
            "X-Source-Env-Type": "development",
            "BStack-Host": "temp"
          };

          requestAppendBstackHeader = sinon.stub(requestLib, 'appendBStackHostHeader').returns(expectedHeaders);

          const expectedBody = Buffer.from(JSON.stringify({
            appium_session_id: keyObject.key,
            automation_session_id: keyObject.rails_session_id,
            hub_session_id: keyObject.key,
            build_id: percyBuildId,
            project_id: projectId,
            screenshot_type: screenshotType,
            scale_factor: scaleFactor,
            port: port,
            options: options,
          }));

          const expectedTermOptions = {
            method: 'POST',
            body: expectedBody,
            hostname: keyObject.rproxyHost,
            port: 45671,
            timeout: 360 * 1000,
            path: `/app_percy/screenshot?device=${encodeURIComponent(keyObject.device)}`,
            headers: expectedHeaders,
          };

          const expectedDataToSend = {
            "session_id": keyObject.key,
            "kind": "app_percy_percy_screenshot_1",
            "data": sinon.match.object,
            "os": keyObject.os.toLowerCase(),
            "machine": sinon.match.string,
            "os_version": keyObject.os_version,
            "team": APP_PERCY_TEAM
          };

          sinon.assert.calledOnce(requestLibCall);
          sinon.assert.calledWith(requestLibCall, expectedTermOptions);
          sinon.assert.called(sendDataToZombieStub);
          sinon.assert.calledWith(
            sendDataToZombieStub,
            expectedDataToSend
          );
          sinon.assert.called(markNumberOfTilesStub);
          sinon.assert.calledWith(
            markNumberOfTilesStub,
            keyObject,
            1
          );
          assert(HubLogger.miscLogger.notCalled, 'Expected hubLoggerStub to be called 0 times');
          let res = {};
          res.data = response.data;
          expectSuccessResponseWithData(res);
        });

        it('should send error kind for non 200 status code', async() => {
          requestLibCall = sinon.stub(requestLib, 'call').returns(Promise.resolve({ statusCode: 500, data: {} }));
          await ogPercyScreenshot(
            keyObject,
            requestStateObj,
            parsedCommand
          );

          const expectedDataToSend = {
            "session_id": keyObject.key,
            "kind": "app_percy_percy_screenshot_0",
            "data": sinon.match.object,
            "os": keyObject.os.toLowerCase(),
            "machine": sinon.match.string,
            "os_version": keyObject.os_version,
            "team": APP_PERCY_TEAM
          };

          sinon.assert.calledOnce(requestLibCall);
          sinon.assert.called(sendDataToZombieStub);
          sinon.assert.calledWith(
            sendDataToZombieStub,
            expectedDataToSend
          );
          sinon.assert.notCalled(markNumberOfTilesStub);
          assert(HubLogger.miscLogger.calledOnce, 'Expected hubLoggerStub to be called once');

          expectErrorResponse(errors.unknown);
        });

        it('should send error log when there is error in parsing data', async() => {
          setRequestStateParams({
            percyBuildId, scaleFactor, screenshotType, options, port
          });

          const response = {
            statusCode: 200,
            data: [
              {
                sha: 'dba003f59db4f1f9364b0edb4251a52ac56074caf99bf31520be97d2fc83cfc3-4',
                status_bar: null,
                nav_bar: null,
                header_height: 0,
                footer_height: 165,
                index: 0
              }
            ],
          };

          requestLibCall = sinon.stub(requestLib, 'call').returns(Promise.resolve(response));
          await ogPercyScreenshot(
            keyObject,
            requestStateObj,
            parsedCommand
          );

          const expectedHeaders = {
            "accept": "application/json",
            "content-type": "application/json; charset=utf-8",
            "accept-encoding": "gzip;q=1.0,deflate;q=0.6,identity;q=0.3",
            "content-length": 256,
            "X-Source-Env-Type": "development",
            "BStack-Host": "temp"
          };

          requestAppendBstackHeader = sinon.stub(requestLib, 'appendBStackHostHeader').returns(expectedHeaders);

          const expectedBody = Buffer.from(JSON.stringify({
            appium_session_id: keyObject.key,
            automation_session_id: keyObject.rails_session_id,
            hub_session_id: keyObject.key,
            build_id: percyBuildId,
            project_id: 'percy-prod',
            screenshot_type: screenshotType,
            scale_factor: scaleFactor,
            port: port,
            options: options,
          }));

          const expectedTermOptions = {
            method: 'POST',
            body: expectedBody,
            hostname: keyObject.rproxyHost,
            port: 45671,
            timeout: 360 * 1000,
            path: `/app_percy/screenshot?device=${encodeURIComponent(keyObject.device)}`,
            headers: expectedHeaders,
          };

          const expectedDataToSend = {
            "session_id": keyObject.key,
            "kind": "app_percy_percy_screenshot_0",
            "data": sinon.match.object,
            "os": keyObject.os.toLowerCase(),
            "machine": sinon.match.string,
            "os_version": keyObject.os_version,
            "team": APP_PERCY_TEAM
          };

          sinon.assert.calledOnce(getProjectIdStub);
          sinon.assert.calledOnce(requestLibCall);
          sinon.assert.called(sendDataToZombieStub);
          sinon.assert.calledWith(requestLibCall, expectedTermOptions);
          sinon.assert.called(markNumberOfTilesStub);
          sinon.assert.calledWith(
            markNumberOfTilesStub,
            keyObject,
            -1
          );
          sinon.assert.calledWith(
            sendDataToZombieStub,
            expectedDataToSend
          );
          assert(HubLogger.miscLogger.calledOnce, 'Expected hubLoggerStub to be called once');
          let res = {};
          res.data = response.data;
          expectErrorResponse(errors.unknown);
        });
      });
    });

    context('with Automate', () => {
      let requestLibCall;
      let requestAppendBstackHeader;
      let getProjectIdStub;

      beforeEach(() => {
        keyObject.os = 'win';
        keyObject.os_version = '10';
        keyObject.appTesting = false;
        sinon.stub(HubLogger, 'miscLogger', () => {});
        getProjectIdStub = sinon.stub().returns('percy-prod');
        handlerRewire.__set__('getProjectId', getProjectIdStub);
      });

      afterEach(() => {
        keyObject.os = 'ios';
        keyObject.os_version = '14';
        keyObject.appTesting = true;
        if (requestLibCall) requestLibCall.restore();
        if (requestAppendBstackHeader) requestAppendBstackHeader.restore();
        HubLogger.miscLogger.restore();
        getProjectIdStub.reset();
      });

      context('with invalid params', () => {
        context('with undefined scaleFactor', () => {
          it('sends error', () => {
            setRequestStateParams({ screenshotType, percyBuildId, options });

            ogPercyScreenshot(
              keyObject,
              requestStateObj,
              parsedCommand
            );

            expectErrorResponse();
          });
        });

        context('with undefined percyBuildId', () => {
          it('sends error', () => {
            setRequestStateParams({ screenshotType, scaleFactor, options });

            ogPercyScreenshot(
              keyObject,
              requestStateObj,
              parsedCommand
            );

            expectErrorResponse();
          });
        });

        context('with undefined screenshotType', () => {
          it('sends error', () => {
            setRequestStateParams({ percyBuildId, scaleFactor, options });

            ogPercyScreenshot(
              keyObject,
              requestStateObj,
              parsedCommand
            );

            expectErrorResponse();
          });
        });

        context('with undefined options', () => {
          it('sends error', () => {
            setRequestStateParams({ percyBuildId, scaleFactor, screenshotType });

            ogPercyScreenshot(
              keyObject,
              requestStateObj,
              parsedCommand
            );

            expectErrorResponse();
          });
        });
      });

      context('with valid params', () => {
        it('sends success response with project id as default when not given', async () => {
          setRequestStateParams({
            percyBuildId, scaleFactor, screenshotType, options, port
          });
          const response = {
            statusCode: 200,
            data: JSON.stringify([
              {
                sha: 'dba003f59db4f1f9364b0edb4251a52ac56074caf99bf31520be97d2fc83cfc3-4',
                status_bar: null,
                nav_bar: null,
                header_height: 0,
                footer_height: 165,
                index: 0
              }
            ])
          };

          requestLibCall = sinon.stub(requestLib, 'call').returns(Promise.resolve(response));
          await ogPercyScreenshot(
            keyObject,
            requestStateObj,
            parsedCommand
          );

          const expectedHeaders = {
            "accept": "application/json",
            "content-type": "application/json; charset=utf-8",
            "accept-encoding": "gzip;q=1.0,deflate;q=0.6,identity;q=0.3",
            "content-length": 365,
            "X-Source-Env-Type": "development",
            "BStack-Host": "temp"
          };

          requestAppendBstackHeader = sinon.stub(requestLib, 'appendBStackHostHeader').returns(expectedHeaders);

          const expectedBody = Buffer.from(JSON.stringify({
            appium_session_id: keyObject.key,
            automation_session_id: keyObject.rails_session_id,
            hub_session_id: keyObject.key,
            build_id: percyBuildId,
            project_id: 'percy-prod',
            screenshot_type: screenshotType,
            scale_factor: scaleFactor,
            port: port,
            options: options,
            sessionInfo: sessionInfo,
          }));

          const expectedTermOptions = {
            method: 'POST',
            body: expectedBody,
            hostname: keyObject.rproxyHost,
            port: 4567,
            timeout: 360 * 1000,
            path: `/percy/screenshot?device=${encodeURIComponent(keyObject.device)}`,
            headers: expectedHeaders,
          };

          const expectedDataToSend = {
            "session_id": keyObject.key,
            "kind": "percy_platform_percy_screenshot_1",
            "data": sinon.match.object,
            "os": keyObject.os.toLowerCase(),
            "machine": sinon.match.string,
            "os_version": keyObject.os_version,
            "team": PERCY_PLATFORM_TEAM
          };

          sinon.assert.calledOnce(getProjectIdStub);
          sinon.assert.calledOnce(requestLibCall);
          sinon.assert.calledWith(requestLibCall, expectedTermOptions);
          sinon.assert.called(sendDataToZombieStub);
          sinon.assert.calledWith(
            sendDataToZombieStub,
            expectedDataToSend
          );
          sinon.assert.called(markNumberOfTilesStub);
          sinon.assert.calledWith(
            markNumberOfTilesStub,
            keyObject,
            1
          );
          assert(HubLogger.miscLogger.notCalled, 'Expected hubLoggerStub to be called 0 times');
          let res = {};
          res.data = response.data;
          expectSuccessResponseWithData(res);
        });

        it('sends success response with project id as passed when given', async () => {
          setRequestStateParams({
            percyBuildId, scaleFactor, screenshotType, options, projectId, port
          });
          getProjectIdStub.returns(projectId);

          const response = {
            statusCode: 200,
            data: JSON.stringify([
              {
                sha: 'dba003f59db4f1f9364b0edb4251a52ac56074caf99bf31520be97d2fc83cfc3-4',
                status_bar: null,
                nav_bar: null,
                header_height: 0,
                footer_height: 165,
                index: 0
              }
            ])
          };

          requestLibCall = sinon.stub(requestLib, 'call').returns(Promise.resolve(response));
          await ogPercyScreenshot(
            keyObject,
            requestStateObj,
            parsedCommand
          );

          const expectedHeaders = {
            "accept": "application/json",
            "content-type": "application/json; charset=utf-8",
            "accept-encoding": "gzip;q=1.0,deflate;q=0.6,identity;q=0.3",
            "content-length": 364,
            "X-Source-Env-Type": "development",
            "BStack-Host": "temp"
          };

          requestAppendBstackHeader = sinon.stub(requestLib, 'appendBStackHostHeader').returns(expectedHeaders);

          const expectedBody = Buffer.from(JSON.stringify({
            appium_session_id: keyObject.key,
            automation_session_id: keyObject.rails_session_id,
            hub_session_id: keyObject.key,
            build_id: percyBuildId,
            project_id: projectId,
            screenshot_type: screenshotType,
            scale_factor: scaleFactor,
            port: port,
            options: options,
            sessionInfo: sessionInfo,
          }));

          const expectedTermOptions = {
            method: 'POST',
            body: expectedBody,
            hostname: keyObject.rproxyHost,
            port: 4567,
            timeout: 360 * 1000,
            path: `/percy/screenshot?device=${encodeURIComponent(keyObject.device)}`,
            headers: expectedHeaders,
          };

          const expectedDataToSend = {
            "session_id": keyObject.key,
            "kind": "percy_platform_percy_screenshot_1",
            "data": sinon.match.object,
            "os": keyObject.os.toLowerCase(),
            "machine": sinon.match.string,
            "os_version": keyObject.os_version,
            "team": PERCY_PLATFORM_TEAM
          };

          sinon.assert.calledOnce(requestLibCall);
          sinon.assert.calledWith(requestLibCall, expectedTermOptions);
          sinon.assert.called(sendDataToZombieStub);
          sinon.assert.calledWith(
            sendDataToZombieStub,
            expectedDataToSend
          );
          sinon.assert.called(markNumberOfTilesStub);
          sinon.assert.calledWith(
            markNumberOfTilesStub,
            keyObject,
            1
          );
          assert(HubLogger.miscLogger.notCalled, 'Expected hubLoggerStub to be called 0 times');
          let res = {};
          res.data = response.data;
          expectSuccessResponseWithData(res);
        });

        it('passes framework data and framework correctly', async () => {
          const frameworkData = {
            'pageGuid': 1,
            'frameGuid': 2
          };
          const framework = 'playwright';
          setRequestStateParams({
            percyBuildId, scaleFactor, screenshotType, options, projectId, port,
            framework: framework, frameworkData: frameworkData
          });
          getProjectIdStub.returns(projectId);

          const response = {
            statusCode: 200,
            data: JSON.stringify([
              {
                sha: 'dba003f59db4f1f9364b0edb4251a52ac56074caf99bf31520be97d2fc83cfc3-4',
                status_bar: null,
                nav_bar: null,
                header_height: 0,
                footer_height: 165,
                index: 0
              }
            ])
          };

          requestLibCall = sinon.stub(requestLib, 'call').returns(Promise.resolve(response));
          await ogPercyScreenshot(
            keyObject,
            requestStateObj,
            parsedCommand
          );

          const expectedHeaders = {
            "accept": "application/json",
            "content-type": "application/json; charset=utf-8",
            "accept-encoding": "gzip;q=1.0,deflate;q=0.6,identity;q=0.3",
            "content-length": 435,
            "X-Source-Env-Type": "development",
            "BStack-Host": "temp"
          };

          requestAppendBstackHeader = sinon.stub(requestLib, 'appendBStackHostHeader').returns(expectedHeaders);

          const expectedBody = Buffer.from(JSON.stringify({
            appium_session_id: keyObject.key,
            automation_session_id: keyObject.rails_session_id,
            hub_session_id: keyObject.key,
            build_id: percyBuildId,
            project_id: projectId,
            screenshot_type: screenshotType,
            scale_factor: scaleFactor,
            port: port,
            framework_data: frameworkData,
            framework: framework,
            options: options,
            sessionInfo: sessionInfo,
          }));

          const expectedTermOptions = {
            method: 'POST',
            body: expectedBody,
            hostname: keyObject.rproxyHost,
            port: 4567,
            timeout: 360 * 1000,
            path: `/percy/screenshot?device=${encodeURIComponent(keyObject.device)}`,
            headers: expectedHeaders,
          };

          const expectedDataToSend = {
            "session_id": keyObject.key,
            "kind": "percy_platform_percy_screenshot_1",
            "data": sinon.match.object,
            "os": keyObject.os.toLowerCase(),
            "machine": sinon.match.string,
            "os_version": keyObject.os_version,
            "team": PERCY_PLATFORM_TEAM
          };

          sinon.assert.calledOnce(requestLibCall);
          sinon.assert.calledWith(requestLibCall, expectedTermOptions);
          sinon.assert.called(sendDataToZombieStub);
          sinon.assert.calledWith(
            sendDataToZombieStub,
            expectedDataToSend
          );
          sinon.assert.called(markNumberOfTilesStub);
          sinon.assert.calledWith(
            markNumberOfTilesStub,
            keyObject,
            1
          );
          assert(HubLogger.miscLogger.notCalled, 'Expected hubLoggerStub to be called 0 times');
          let res = {};
          res.data = response.data;
          expectSuccessResponseWithData(res);
        });

        it('should send error kind for non 200 status code', async() => {
          requestLibCall = sinon.stub(requestLib, 'call').returns(Promise.resolve({ statusCode: 500, data: {} }));
          await ogPercyScreenshot(
            keyObject,
            requestStateObj,
            parsedCommand
          );

          const expectedDataToSend = {
            "session_id": keyObject.key,
            "kind": "percy_platform_percy_screenshot_0",
            "data": sinon.match.object,
            "os": keyObject.os.toLowerCase(),
            "machine": sinon.match.string,
            "os_version": keyObject.os_version,
            "team": PERCY_PLATFORM_TEAM
          };

          sinon.assert.calledWith(percyScreenshotBeginUpdateKeyobjectStub,
            keyObject,
            -1);
          sinon.assert.calledOnce(requestLibCall);
          sinon.assert.called(sendDataToZombieStub);
          sinon.assert.calledWith(
            sendDataToZombieStub,
            expectedDataToSend
          );
          sinon.assert.notCalled(markNumberOfTilesStub);
          assert(HubLogger.miscLogger.calledOnce, 'Expected hubLoggerStub to be called once');
          expect(keyObject.percyBeginTime).to.equal(-1);
          expectErrorResponse(errors.unknown);
        });
      });
    });
  });

  context('percyScreenshotSuccessResponse', () => {
    let comparisonCapsStub;
    let getMetadataStub;

    beforeEach(() => {
      comparisonCapsStub = sinon.stub().returns({
        browserName: keyObject.browser,
        browserVersion: keyObject.browser_version,
        os: keyObject.os,
        os_version: keyObject.os_version,
        deviceOrientation: keyObject.deviceOrientation,
      });
      getMetadataStub = sinon.stub().returns({
        window_height: 733
      });
      handlerRewire.__set__('comparisonCaps', comparisonCapsStub);
      handlerRewire.__set__('getMetadata', getMetadataStub);
    });


    afterEach(() => {
      comparisonCapsStub.reset();
      getMetadataStub.reset();
      keyObject.appTesting = true;
      // Delete extra keys from requestState
      delete requestStateObj.hash;
      delete requestStateObj.data;
    });

    it('sends correct success response for begin', async () => {
      await ogPercyScreenshotSuccessResponse(
        keyObject,
        requestStateObj,
        'begin',
      );

      expect(requestStateObj.hash).to.eq('GET:value');
      expect(requestStateObj.data).to.eq(JSON.stringify({
        sessionId: requestStateObj.clientSessionID,
        status: 0,
        value: JSON.stringify({
          success: true,
          deviceName: keyObject.deviceName.split('-')[0].trim(),
          osVersion: keyObject.os_version,
          buildHash: keyObject.build_hash,
          sessionHash: keyObject.rails_session_id
        }),
      }));

      sinon.assert.called(customExecutorHelper.instrumentAndSendExecutorResponse);
      sinon.assert.calledWith(
        customExecutorHelper.instrumentAndSendExecutorResponse,
        executorType,
        keyObject,
        requestStateObj,
        false
      );
    });

    it('calls comparison caps and sends correct success response for begin', async () => {
      keyObject.appTesting = false;
      await ogPercyScreenshotSuccessResponse(
        keyObject,
        requestStateObj,
        'begin',
      );

      sinon.assert.called(comparisonCapsStub);
      sinon.assert.calledWith(comparisonCapsStub, keyObject);
      expect(requestStateObj.hash).to.eq('GET:value');
      expect(requestStateObj.data).to.eq(JSON.stringify({
        sessionId: requestStateObj.clientSessionID,
        status: 0,
        value: JSON.stringify({
          success: true,
          deviceName: keyObject.deviceName.split('-')[0].trim(),
          osVersion: keyObject.os_version,
          buildHash: keyObject.build_hash,
          sessionHash: keyObject.rails_session_id,
          capabilities: {
            browserName: keyObject.browser,
            browserVersion: keyObject.browser_version,
            os: keyObject.os,
            os_version: keyObject.os_version,
            deviceOrientation: keyObject.deviceOrientation,
          }
        }),
      }));

      sinon.assert.called(customExecutorHelper.instrumentAndSendExecutorResponse);
      sinon.assert.calledWith(
        customExecutorHelper.instrumentAndSendExecutorResponse,
        executorType,
        keyObject,
        requestStateObj,
        false
      );
    });

    it('sends correct success response for end', () => {
      ogPercyScreenshotSuccessResponse(
        keyObject,
        requestStateObj,
        'end',
      );

      expect(requestStateObj.hash).to.eq('GET:value');
      expect(requestStateObj.data).to.eq(JSON.stringify({
        sessionId: requestStateObj.clientSessionID,
        status: 0,
        value: JSON.stringify({
          success: true,
        }),
      }));

      sinon.assert.called(customExecutorHelper.instrumentAndSendExecutorResponse);
      sinon.assert.calledWith(
        customExecutorHelper.instrumentAndSendExecutorResponse,
        executorType,
        keyObject,
        requestStateObj,
        false
      );
    });

    it('sends correct success response for screenshot', async () => {
      await ogPercyScreenshotSuccessResponse(
        keyObject,
        requestStateObj,
        'screenshot',
        result
      );

      expect(requestStateObj.hash).to.eq('GET:value');
      expect(requestStateObj.data).to.eq(JSON.stringify({
        sessionId: requestStateObj.clientSessionID,
        status: 0,
        value: JSON.stringify({
          success: true,
          result: 'dummy data',
        }),
      }));

      sinon.assert.called(customExecutorHelper.instrumentAndSendExecutorResponse);
      sinon.assert.calledWith(
        customExecutorHelper.instrumentAndSendExecutorResponse,
        executorType,
        keyObject,
        requestStateObj,
        false
      );
    });

    it('sends correct success response for screenshot for android', async () => {
      keyObject.appTesting = false;
      keyObject.os = 'Android';
      await ogPercyScreenshotSuccessResponse(
        keyObject,
        requestStateObj,
        'screenshot',
        result
      );

      sinon.assert.called(getMetadataStub);
      sinon.assert.calledWith(getMetadataStub, keyObject);
      expect(requestStateObj.hash).to.eq('GET:value');
      expect(requestStateObj.data).to.eq(JSON.stringify({
        sessionId: requestStateObj.clientSessionID,
        status: 0,
        value: JSON.stringify({
          success: true,
          result: 'dummy data',
          metadata: {
            window_height: 733
          }
        }),
      }));

      sinon.assert.called(customExecutorHelper.instrumentAndSendExecutorResponse);
      sinon.assert.calledWith(
        customExecutorHelper.instrumentAndSendExecutorResponse,
        executorType,
        keyObject,
        requestStateObj,
        false
      );
    });

    it('sends correct success response screenshot for playwright android without metadata', async () => {
      keyObject.appTesting = false;
      keyObject.os = 'Android';
      await ogPercyScreenshotSuccessResponse(
        keyObject,
        requestStateObj,
        'screenshot',
        result,
        'playwright'
      );

      sinon.assert.notCalled(getMetadataStub);
      expect(requestStateObj.hash).to.eq('GET:value');
      expect(requestStateObj.data).to.eq(JSON.stringify({
        sessionId: requestStateObj.clientSessionID,
        status: 0,
        value: JSON.stringify({
          success: true,
          result: 'dummy data'
        }),
      }));

      sinon.assert.called(customExecutorHelper.instrumentAndSendExecutorResponse);
      sinon.assert.calledWith(
        customExecutorHelper.instrumentAndSendExecutorResponse,
        executorType,
        keyObject,
        requestStateObj,
        false
      );
    });
  });

  context('ogIsValidPercyUrl', () => {
    it('returns true for valid URL', () => {
      const isValid = ogIsValidPercyUrl(percyBuildUrl);
      expect(isValid).to.eq(true);
    });

    it('returns true for valid canary URL ', () => {
      const isValid = ogIsValidPercyUrl(canaryPercyBuildUrl);
      expect(isValid).to.eq(true);
    });

    it('return true for valid percy enterprise URL', () => {
      const isValid = ogIsValidPercyUrl(enterprisePercyBuildUrl)
      expect(isValid).to.eq(true)
    });

    it('returns false for valid URL with diffrent hostname', () => {
      const isValid = ogIsValidPercyUrl('https://www.browserstack.com/');
      expect(isValid).to.eq(false);
    });

    it('returns false for invalid URL', () => {
      const isValid = ogIsValidPercyUrl('browserstack');
      expect(isValid).to.eq(false);
    });
  });

  context('markAsPercy', () => {
    let pubSubPublishStub;

    before(() => {
      pubSubPublishStub = sinon.stub(pubSub, 'publish');
    });

    afterEach(() => {
      if (postBrowserStackStub) postBrowserStackStub.reset();
      pubSubPublishStub.reset();
      keyObject.markedAsPercy = false;
    });

    after(() => {
      pubSubPublishStub.restore();
    });

    context('with rails postive response', () => {
      beforeEach(() => {
        stubPostBrowserstack({ success: true });
      });

      it('calls rails, sets markedAsPercy (updates redis) and returns postive response', async () => {
        await ogMarkAsPercy(
          keyObject,
          percyBuildId,
          percyBuildUrl
        );

        sinon.assert.called(postBrowserStackStub);
        sinon.assert.calledWith(
          postBrowserStackStub,
          '&mark_as_percy=true',
          {
            isAppAutomate: true,
            automation_session_id: keyObject.rails_session_id,
            user_id: keyObject.user_id,
            percy_build_id: percyBuildId,
            percy_build_url: percyBuildUrl,
          },
          null,
          null,
          sinon.match.func
        );

        expect(keyObject.markedAsPercy).to.eq(true);

        sinon.assert.called(pubSubPublishStub);
        sinon.assert.calledWith(
          pubSubPublishStub,
          constants.updateKeyObject,
          {
            session: keyObject.rails_session_id,
            changed: {
              markedAsPercy: true,
            },
          }
        );
      });
    });

    context('with rails negative response', () => {
      beforeEach(() => {
        stubPostBrowserstack({ success: false });
      });

      it('calls rails, does not set markedAsPercy and sends error response', async () => {
        await ogMarkAsPercy(
          keyObject,
          percyBuildId,
          percyBuildUrl
        );

        sinon.assert.notCalled(pubSubPublishStub);
        expect(keyObject.markedAsPercy).to.eq(false);
      });
    });
  });

  context ('percyScreenshotBeginUpdateKeyobject', () => {
    let pubSubPublishStub;

    before(() => {
      var dateObject = Date.parse('01/01/2023');
      sinon.stub(Date, 'now').returns(dateObject);
      pubSubPublishStub = sinon.stub(pubSub, 'publish');
    });

    afterEach(() => {
      pubSubPublishStub.reset();
      keyObject.percyBeginTime = -1;
      Date.now.restore();
    });

    after(() => {
      pubSubPublishStub.restore();
    });

    context('when success', () => {
      it('should update the key to current time', async () => {
        const nbeginTime = Date.now();
        await ogPercyScreenshotBeginUpdateKeyobject(keyObject, nbeginTime);

        sinon.assert.called(pubSubPublishStub);
        sinon.assert.calledWith(
          pubSubPublishStub,
          constants.updateKeyObject,
          {
            session: keyObject.rails_session_id,
            changed: {
              percyBeginTime: nbeginTime,
            },
          }
        );
      });
    });
  });

  context ('markNumberOfTiles', () => {
    let pubSubPublishStub;

    before(() => {
      pubSubPublishStub = sinon.stub(pubSub, 'publish');
    });

    afterEach(() => {
      pubSubPublishStub.reset();
      keyObject.percyNumberOfTiles = 0;
    });

    after(() => {
      pubSubPublishStub.restore();
    });

    context('when success', () => {
      it('should update the key to current time', async () => {
        await ogMarkNumberOfTiles(keyObject, 1);

        sinon.assert.called(pubSubPublishStub);
        sinon.assert.calledWith(
          pubSubPublishStub,
          constants.updateKeyObject,
          {
            session: keyObject.rails_session_id,
            changed: {
              percyNumberOfTiles: 1,
            },
          }
        );
      });
    });
  });

  context ('sendDataToZombie', () => {
    let pingZombieStub;
    let dataToSend = {
      "session_id": keyObject.key,
      "kind": "some_kind",
      "data": sinon.match.object,
      "os": keyObject.os.toLowerCase(),
      "machine": 'abc',
      "os_version": keyObject.os_version,
      "team": PERCY_PLATFORM_TEAM
    };

    beforeEach(() => {
      sinon.stub(HubLogger, 'miscLogger', () => {});
    })
    afterEach(() => {
      if (pingZombieStub) origHelper.PingZombie.restore();
      HubLogger.miscLogger.restore();
    });

    context('when pingZombie is sucess', () => {
      it('should send data to zombie', async () => {
        pingZombieStub = sinon.stub(origHelper, 'PingZombie');
        origHelper.PingZombie = pingZombieStub;

        await ogSendDataToZombie(dataToSend);
        sinon.assert.calledWith(pingZombieStub, dataToSend);
        assert(HubLogger.miscLogger.notCalled, 'Expected hubLoggerStub to be called 0 times');
      });
    });

    context('when pingZombie throws error', () => {
      it('should log the error', async () => {
        pingZombieStub = sinon.stub(origHelper, 'PingZombie').throws(new Error('some error'));
        origHelper.PingZombie = pingZombieStub;

        await ogSendDataToZombie(dataToSend);

        sinon.assert.calledWith(pingZombieStub, dataToSend);
        expect(() => origHelper.PingZombie(dataToSend)).to.throw(Error, 'some error');
        assert(HubLogger.miscLogger.calledOnce, 'Expected hubLoggerStub to be called once');
      });
    });
  });

  context('setupPercyOnAutomateTerminal', () => {
    let requestLibCall;
    let requestAppendBstackHeader;

    beforeEach(() => {
      keyObject.os = 'win';
      keyObject.os_version = '10';
      keyObject.appTesting = false;
      keyObject.markedAsPercy = false;
      keyObject.group_id = 16;
      sinon.stub(HubLogger, 'miscLogger', () => {});
    });

    afterEach(() => {
      keyObject.os = 'ios';
      keyObject.os_version = '14';
      keyObject.appTesting = true;
      keyObject.markedAsPercy = false;
      keyObject.group_id = 16;
      if (requestLibCall) requestLibCall.restore();
      if (requestAppendBstackHeader) requestAppendBstackHeader.restore();
      HubLogger.miscLogger.restore();
    });

    it('should send success kind for 200 status code', async () => {
      const response = {
        statusCode: 200,
        data: []
      };

      requestLibCall = sinon.stub(requestLib, 'call').returns(Promise.resolve(response));
      const resp = await ogSetupPercyOnAutomateTerminal(
        keyObject,
        projectId
      );

      const expectedHeaders = {
        "accept": "application/json",
        "content-type": "application/json; charset=utf-8",
        "accept-encoding": "gzip;q=1.0,deflate;q=0.6,identity;q=0.3",
        "content-length": 143,
        "X-Source-Env-Type": "development",
        "BStack-Host": "temp"
      };

      requestAppendBstackHeader = sinon.stub(requestLib, 'appendBStackHostHeader').returns(expectedHeaders);

      const expectedBody = Buffer.from(JSON.stringify({
        certs: constants.PERCY_SCREENSHOT_UPLOADER_CERTS,
        project_id: projectId,
        automation_session_id: keyObject.rails_session_id,
        hub_session_id: keyObject.key,
        group_id: keyObject.group_id
      }));

      const expectedTermOptions = {
        method: 'POST',
        body: expectedBody,
        hostname: keyObject.rproxyHost,
        port: 4567,
        timeout: 240 * 1000,
        path: `/percy/setup_automate_session?device=${encodeURIComponent(keyObject.device)}`,
        headers: expectedHeaders,
      };

      const expectedDataToSend = {
        "session_id": keyObject.key,
        "kind": "percy_platform_setup_poa_terminal_1",
        "data": sinon.match.object,
        "os": keyObject.os.toLowerCase(),
        "machine": sinon.match.string,
        "os_version": keyObject.os_version,
        "team": PERCY_PLATFORM_TEAM,
        "group_id": keyObject.group_id,
      };

      sinon.assert.calledOnce(requestLibCall);
      sinon.assert.calledWith(requestLibCall, expectedTermOptions);
      sinon.assert.called(sendDataToZombieStub);
      sinon.assert.calledWith(
        sendDataToZombieStub,
        expectedDataToSend
      );
      assert(HubLogger.miscLogger.notCalled, 'Expected hubLoggerStub to be called 0 times');
      expect(resp).to.eq(true);

    });

    it('should include group_id as 16 in the body', async () => {
        const response = {
            statusCode: 200,
            data: []
        };

        // Stub the `requestLib.call` to return a mock response
        requestLibCall = sinon.stub(requestLib, 'call').returns(Promise.resolve(response));

        // Set `group_id` in the `keyObject`
        keyObject.group_id = 16;

        const resp = await ogSetupPercyOnAutomateTerminal(keyObject, projectId);

        const expectedBody = Buffer.from(JSON.stringify({
            certs: constants.PERCY_SCREENSHOT_UPLOADER_CERTS,
            project_id: projectId,
            automation_session_id: keyObject.rails_session_id,
            hub_session_id: keyObject.key,
            group_id: keyObject.group_id,
        }));

        const expectedTermOptions = {
            method: 'POST',
            body: expectedBody,
            hostname: keyObject.rproxyHost,
            port: 4567,
            timeout: 240 * 1000,
            path: `/percy/setup_automate_session?device=${encodeURIComponent(keyObject.device)}`,
            headers: sinon.match.any,
        };

        // Assert that `requestLib.call` was called with the correct options
        sinon.assert.calledOnce(requestLibCall);
        sinon.assert.calledWith(requestLibCall, expectedTermOptions);

        // Validate the response
        expect(resp).to.eq(true);
    });

    it('should send error kind for non 200 status code', async () => {
      const response = {
        statusCode: 500,
        data: []
      };

      requestLibCall = sinon.stub(requestLib, 'call').returns(Promise.resolve(response));
      const resp = await ogSetupPercyOnAutomateTerminal(
        keyObject,
        projectId
      );

      const expectedHeaders = {
        "accept": "application/json",
        "content-type": "application/json; charset=utf-8",
        "accept-encoding": "gzip;q=1.0,deflate;q=0.6,identity;q=0.3",
        "content-length": 143,
        "X-Source-Env-Type": "development",
        "BStack-Host": "temp"
      };

      requestAppendBstackHeader = sinon.stub(requestLib, 'appendBStackHostHeader').returns(expectedHeaders);

      const expectedBody = Buffer.from(JSON.stringify({
        certs: constants.PERCY_SCREENSHOT_UPLOADER_CERTS,
        project_id: projectId,
        automation_session_id: keyObject.rails_session_id,
        hub_session_id: keyObject.key,
        group_id: keyObject.group_id,
      }));

      const expectedTermOptions = {
        method: 'POST',
        body: expectedBody,
        hostname: keyObject.rproxyHost,
        port: 4567,
        timeout: 240 * 1000,
        path: `/percy/setup_automate_session?device=${encodeURIComponent(keyObject.device)}`,
        headers: expectedHeaders,
      };

      const expectedDataToSend = {
        "session_id": keyObject.key,
        "kind": "percy_platform_setup_poa_terminal_0",
        "data": sinon.match.object,
        "os": keyObject.os.toLowerCase(),
        "machine": sinon.match.string,
        "os_version": keyObject.os_version,
        "team": PERCY_PLATFORM_TEAM,
        "group_id": keyObject.group_id,
      };

      sinon.assert.calledOnce(requestLibCall);
      sinon.assert.calledWith(requestLibCall, expectedTermOptions);
      sinon.assert.called(sendDataToZombieStub);
      sinon.assert.calledWith(
        sendDataToZombieStub,
        expectedDataToSend
      );
      assert(HubLogger.miscLogger.calledOnce, 'Expected hubLoggerStub to be called once');
      expect(resp).to.eq(false);
    });
  });

  context('disableAnimations', () => {
    let requestLibCall;
    let requestAppendBstackHeader;

    beforeEach(() => {
      keyObject.os = 'android';
      keyObject.os_version = '14';
      keyObject.appTesting = true;
      keyObject.markedAsPercy = false;
      sinon.stub(HubLogger, 'miscLogger', () => {});
    });

    afterEach(() => {
      if (requestLibCall) requestLibCall.restore();
      if (requestAppendBstackHeader) requestAppendBstackHeader.restore();
      HubLogger.miscLogger.restore();
    });

    it('should send success kind for 200 status code', async () => {
      const response = {
        statusCode: 200,
        data: []
      };

      requestLibCall = sinon.stub(requestLib, 'call').returns(Promise.resolve(response));
      await ogDisableAnimations(keyObject);

      const expectedHeaders = {
        "accept": "application/json",
        "content-type": "application/json; charset=utf-8",
        "accept-encoding": "gzip;q=1.0,deflate;q=0.6,identity;q=0.3",
        "content-length": 33,
        "X-Source-Env-Type": "development",
        "BStack-Host": "temp"
      };

      requestAppendBstackHeader = sinon.stub(requestLib, 'appendBStackHostHeader').returns(expectedHeaders);

      const expectedBody = Buffer.from(JSON.stringify({
        appium_session_id: keyObject.key
      }));

      const expectedTermOptions = {
        method: 'POST',
        body: expectedBody,
        hostname: keyObject.rproxyHost,
        port: 45671,
        timeout: 240 * 1000,
        path: `/app_percy/disable_animation?device=${encodeURIComponent(keyObject.device)}`,
        headers: expectedHeaders,
      };

      const expectedDataToSend = {
        "session_id": keyObject.key,
        "kind": "app_percy_disable_animation_1",
        "data": sinon.match.object,
        "os": keyObject.os.toLowerCase(),
        "machine": sinon.match.string,
        "os_version": keyObject.os_version,
        "team": APP_PERCY_TEAM
      };

      sinon.assert.calledOnce(requestLibCall);
      sinon.assert.calledWith(requestLibCall, expectedTermOptions);
      sinon.assert.called(sendDataToZombieStub);
      sinon.assert.calledWith(
        sendDataToZombieStub,
        expectedDataToSend
      );
      assert(HubLogger.miscLogger.notCalled, 'Expected hubLoggerStub to be called 0 times');

    });

    it('should not call endpoint for iOS', async () => {
      keyObject.os = 'iOS';
      const response = {
        statusCode: 200,
        data: []
      };

      requestLibCall = sinon.stub(requestLib, 'call').returns(Promise.resolve(response));
      await ogDisableAnimations(keyObject);

      sinon.assert.notCalled(requestLibCall);
    });

    it('should send error kind for non 200 status code', async () => {
      const response = {
        statusCode: 500,
        data: []
      };

      requestLibCall = sinon.stub(requestLib, 'call').returns(Promise.resolve(response));
      await ogDisableAnimations(keyObject);

      const expectedHeaders = {
        "accept": "application/json",
        "content-type": "application/json; charset=utf-8",
        "accept-encoding": "gzip;q=1.0,deflate;q=0.6,identity;q=0.3",
        "content-length": 33,
        "X-Source-Env-Type": "development",
        "BStack-Host": "temp"
      };

      requestAppendBstackHeader = sinon.stub(requestLib, 'appendBStackHostHeader').returns(expectedHeaders);

      const expectedBody = Buffer.from(JSON.stringify({
        appium_session_id: keyObject.key
      }));

      const expectedTermOptions = {
        method: 'POST',
        body: expectedBody,
        hostname: keyObject.rproxyHost,
        port: 45671,
        timeout: 240 * 1000,
        path: `/app_percy/disable_animation?device=${encodeURIComponent(keyObject.device)}`,
        headers: expectedHeaders,
      };

      const expectedDataToSend = {
        "session_id": keyObject.key,
        "kind": "app_percy_disable_animation_0",
        "data": sinon.match.object,
        "os": keyObject.os.toLowerCase(),
        "machine": sinon.match.string,
        "os_version": keyObject.os_version,
        "team": APP_PERCY_TEAM
      };

      sinon.assert.calledOnce(requestLibCall);
      sinon.assert.calledWith(requestLibCall, expectedTermOptions);
      sinon.assert.called(sendDataToZombieStub);
      sinon.assert.calledWith(
        sendDataToZombieStub,
        expectedDataToSend
      );
      assert(HubLogger.miscLogger.calledOnce, 'Expected hubLoggerStub to be called once');
    });
  });

  context('utilFunctions', () => {
    let kind = 'sample_kind';

    context('with appTesting true -> appAutomate', () => {

      context('zombieKind', () => {
        it('should return app_percy prepended to kind', () => {
          const teamKind = ogZombieKind(keyObject, kind);
          expect(teamKind).to.eq('app_percy_sample_kind');
        });
      });

      context('getDeviceName', () => {
        before(() => {
          keyObject.deviceName = 'IUIE123-900123';
        });

        it('should return device name', () => {
          const deviceName = ogGetDeviceName(keyObject);
          expect(deviceName).to.eq('IUIE123');
        });
      });

      context('getDeviceSinatraPort', () => {
        it('should return 45671 port', () => {
          const port = ogGetDeviceSinatraPort(keyObject);
          expect(port).to.eq(45671);
        });
      });

      context('getTeamName', () => {
        it('should return app-percy', () => {
          const team = ogGetTeamName(keyObject);
          expect(team).to.eq('app-percy');
        });
      });
    });

    context('with appTesting false -> automate', () => {

      context('zombieKind', () => {
        beforeEach(() => {
          keyObject.appTesting = false;
        });

        afterEach(() => {
          keyObject.appTesting = true;
        });

        it('should return percy_platform prepended to kind', () => {
          const teamKind = ogZombieKind(keyObject, kind);
          expect(teamKind).to.eq('percy_platform_sample_kind');
        });
      });

      context('getDeviceName', () => {
        beforeEach(() => {
          keyObject.appTesting = false;
          delete keyObject.deviceName;
        });

        afterEach(() => {
          keyObject.appTesting = true;
        });

        it('should return device', () => {
          const deviceName = ogGetDeviceName(keyObject);
          expect(deviceName).to.eq(null);
        });

        it('should return desktop device name', () => {
          keyObject.os = 'Windows';
          const deviceName = ogGetDeviceName(keyObject);
          expect(deviceName).to.eq('windows_14_safari_92');
          keyObject.os = 'ios';
        });
      });

      context('getDeviceSinatraPort', () => {
        beforeEach(() => {
          keyObject.appTesting = false;
          keyObject.os = 'win';
        });

        afterEach(() => {
          keyObject.appTesting = true;
          keyObject.os = 'ios';
        });

        it('should return 4567 port', () => {
          const port = ogGetDeviceSinatraPort(keyObject);
          expect(port).to.eq(4567);
        });
      });

      context('getTeamName', () => {
        beforeEach(() => {
          keyObject.appTesting = false;
        });

        afterEach(() => {
          keyObject.appTesting = true;
        });

        it('should return percy-platform', () => {
          const team = ogGetTeamName(keyObject);
          expect(team).to.eq('percy-platform');
        });
      });
    });

    context('getProjectId', () => {
      it('should return percy-prod if called with none', () => {
        const rProjectId = ogGetProjectId();
        expect(rProjectId).to.eq('percy-prod');
      });

      it('should return percy-dev if called with percy-dev', () => {
        const rProjectId = ogGetProjectId('percy-dev');
        expect(rProjectId).to.eq('percy-dev');
      });
    });

    context('comparisonCaps', () => {
      it('should compute proper capabilities', async () => {
        const caps = await ogComparisonCaps(keyObject);

        const expectedCaps = {
          browserName: keyObject.browser,
          browserVersion: keyObject.browser_version,
          os: keyObject.os,
          os_version: keyObject.os_version,
          deviceOrientation: keyObject.deviceOrientation,
        };

        expect(caps).to.deep.eq(expectedCaps);
      });
    });

    context('getMetaData', () => {
      let requestLibCall;
      let requestAppendBstackHeader;
      const beforeOs = keyObject.os;

      beforeEach(() => {
        keyObject.appTesting = false;
        keyObject.os = 'android';
        sinon.stub(HubLogger, 'miscLogger', () => {});
      });

      afterEach(() => {
        keyObject.appTesting = true;
        keyObject.os = beforeOs;
        if (requestLibCall) requestLibCall.restore();
        if (requestAppendBstackHeader) requestAppendBstackHeader.restore();
        HubLogger.miscLogger.restore();
      });

      it('should give proper metadata', async () => {
        const response = {
          statusCode: 200,
          data: '{"window_height": 733}'
        };

        const body = Buffer.from(JSON.stringify({
          appium_session_id: keyObject.key,
          port: keyObject.port,
          device_id: keyObject.device,
        }));

        requestLibCall = sinon.stub(requestLib, 'call').returns(Promise.resolve(response));
        const resp = await ogGetMetadata(
          keyObject
        );

        const expectedHeaders = {
          accept: 'application/json',
          'content-type': 'application/json; charset=utf-8',
          'accept-encoding': 'gzip;q=1.0,deflate;q=0.6,identity;q=0.3',
          'content-length': body.length,
          "X-Source-Env-Type": "development",
          'BStack-Host': 'temp'
        };

        requestAppendBstackHeader = sinon.stub(requestLib, 'appendBStackHostHeader').returns(expectedHeaders);

        const expectedBody = Buffer.from(JSON.stringify({
          appium_session_id: keyObject.key,
          port: keyObject.port,
          device_id: keyObject.device,
        }));

        const expectedTermOptions = {
          method: 'POST',
          body: expectedBody,
          hostname: keyObject.rproxyHost,
          port: 45671,
          timeout: 240 * 1000,
          path: `/percy/get_metadata?device=${encodeURIComponent(keyObject.device)}`,
          headers: expectedHeaders,
        };

        sinon.assert.calledOnce(requestLibCall);
        sinon.assert.calledWith(requestLibCall, expectedTermOptions);
        assert(HubLogger.miscLogger.notCalled, 'Expected hubLoggerStub to be called 0 times');
        expect(resp.window_height).to.eq(733);
      });

      it('should raise exception but give empty result', async () => {
        const response = {
          statusCode: 500,
          data: '{}'
        };

        const body = Buffer.from(JSON.stringify({
          appium_session_id: keyObject.key,
          port,
          device_id: keyObject.device,
        }));

        requestLibCall = sinon.stub(requestLib, 'call').returns(Promise.resolve(response));
        const resp = await ogGetMetadata(
          keyObject
        );

        const expectedHeaders = {
          accept: 'application/json',
          'content-type': 'application/json; charset=utf-8',
          'accept-encoding': 'gzip;q=1.0,deflate;q=0.6,identity;q=0.3',
          'content-length': body.length,
          "X-Source-Env-Type": "development",
          'BStack-Host': 'temp'
        };

        requestAppendBstackHeader = sinon.stub(requestLib, 'appendBStackHostHeader').returns(expectedHeaders);

        const expectedBody = Buffer.from(JSON.stringify({
          appium_session_id: keyObject.key,
          port,
          device_id: keyObject.device,
        }));

        const expectedTermOptions = {
          method: 'POST',
          body: expectedBody,
          hostname: keyObject.rproxyHost,
          port: 45671,
          timeout: 240 * 1000,
          path: `/percy/get_metadata?device=${encodeURIComponent(keyObject.device)}`,
          headers: expectedHeaders,
        };

        sinon.assert.calledOnce(requestLibCall);
        sinon.assert.calledWith(requestLibCall, expectedTermOptions);
        assert(HubLogger.miscLogger.calledOnce, 'Expected hubLoggerStub to be called once');
        expect(resp).to.deep.eq({});
      });
    });
  });
});
