const { expect } = require("chai");
const sinon = require('sinon')
const PercyCapabilityValidator = require("../../../../helpers/customSeleniumHandling/jsExecutorHandler/percyCapabilityValidator");
const HubLogger = require('../../../../log');

describe("percyCapabilityValidator", () => {

  it('Platform Details are not provided', () => {
    const keyObject = {
      selenium_version: '3.141.2',
    }
    const validator = new PercyCapabilityValidator(keyObject)
    expect(() => validator.validateBrowserOSVersions()).to.throw(
      `Platform Details are not provided`
    );
  })

  it("Selenium Version not supported", async () => {
    const keyObject = {
      selenium_version: "3.1.2",
    };
    const validator = new PercyCapabilityValidator(keyObject);
    expect(() => validator.validateBrowserOSVersions()).to.throw(
      `Selenium Version: ${keyObject.selenium_version} used is less than supported. Refer: https://www.browserstack.com/docs/percy/integrate/functional-and-visual#prerequisite`
    );
  });

  it('Appium Version is not supported', async () => {
    const keyObject = {
      appium_version: "1.6.0"
    }
    const validator = new PercyCapabilityValidator(keyObject)
    expect(() => validator.validateBrowserOSVersions()).to.throw( 
      `Appium Version: ${keyObject.appium_version} used is less than supported`
    );
  });

  it("os, os_version, browser not present", async () => {
    const keyObject = {
      selenium_version: "3.12.2",
      platformDetails: { platformName: "OS X" },
    };
    const validator = new PercyCapabilityValidator(keyObject);
    expect(() => validator.validateBrowserOSVersions()).to.throw(
      `OS/Browser Combination is not supported in Percy`
    );
  });

  it("deviceName not present", () => {
    const keyObject = {
      selenium_version: "3.12.2",
      os: "Android",
      os_version: "Jelly Bean",
      browser: "chrome",
      browser_version: "90",
      platformDetails: { platformName: "Android" },
    };
    const validator = new PercyCapabilityValidator(keyObject);
    expect(() => validator.validateBrowserOSVersions()).to.throw(
      "Device capabilities are incorrect"
    );
  });

  it("Os version not supported", () => {
    const keyObject = {
      selenium_version: "3.12.2",
      os: "OS X",
      os_version: "Sierra",
      browser: "chrome",
      browser_version: "90",
      platformDetails: { platformName: "OS X" },
    };
    const validator = new PercyCapabilityValidator(keyObject);
    expect(() => validator.validateBrowserOSVersions()).to.throw(
      `OS version: ${keyObject.os_version} is not supported in Percy`
    );
  });

  it('Browser version is supported', () => {
    const keyObject = {
      selenium_version: "3.12.2",
      os: "OS X",
      os_version: "Sonoma",
      browser: "safari",
      browser_version: "90",
      platformDetails: { platformName: "OS X" },
    };

    const validator = new PercyCapabilityValidator(keyObject);
    expect(() => validator.validateBrowserOSVersions()).not.to.throw();
  })

  it("Browser version is less than min_version [CHROME]", () => {
    const keyObject = {
      selenium_version: "3.12.2",
      os: "OS X",
      os_version: "Big Sur",
      browser: "chrome",
      browser_version: "90",
      platformDetails: { platformName: "OS X" },
    };
    const validator = new PercyCapabilityValidator(keyObject);
    expect(() => validator.validateBrowserOSVersions()).to.throw(
      `Browser: ${keyObject.browser} ${keyObject.browser_version} is not supported in Percy on ${keyObject.os}. Refer: https://www.browserstack.com/docs/percy/integrate/functional-and-visual#limitations`
    );
  });

  it("Browser version is less than min_version [FIREFOX]", () => {
    const keyObject = {
      selenium_version: "3.12.2",
      os: "OS X",
      os_version: "Big Sur",
      browser: "Firefox",
      browser_version: "90",
      platformDetails: { platformName: "OS X" },
    };
    const validator = new PercyCapabilityValidator(keyObject);
    expect(() => validator.validateBrowserOSVersions()).to.throw(
      `Browser: ${keyObject.browser} ${keyObject.browser_version} is not supported in Percy on ${keyObject.os}. Refer: https://www.browserstack.com/docs/percy/integrate/functional-and-visual#limitations`
    );
  });

  it("Browser version is less than min_version [WINDOWS][FIREFOX]", () => {
    const keyObject = {
      selenium_version: "3.12.2",
      os: "Windows",
      os_version: "11",
      browser: "Firefox",
      browser_version: "90",
      platformDetails: { platformName: "Windows" }
    };
    const validator = new PercyCapabilityValidator(keyObject);
    expect(() => validator.validateBrowserOSVersions()).to.throw(
      `Browser: ${keyObject.browser} ${keyObject.browser_version} is not supported in Percy on ${keyObject.os}. Refer: https://www.browserstack.com/docs/percy/integrate/functional-and-visual#limitations`
    );
  });

  it("Browser not supported", () => {
    const keyObject = {
      selenium_version: "3.12.2",
      os: "Window",
      os_version: "11",
      browser: "internet explorer",
      browser_version: "90",
      platformDetails: { platformName: "Windows" },
    };
    const validator = new PercyCapabilityValidator(keyObject);
    expect(() => validator.validateBrowserOSVersions()).to.throw(
      `Browser: ${keyObject.browser} is not supported in Percy`
    );
  });

  it("Device not supported", () => {
    const keyObject = {
      deviceName: 'Android 12 - 14',
      selenium_version: "3.12.2",
      os: "Android",
      os_version: "Jelly Beans",
      browser: "chrome",
      browser_version: "92",
      platformDetails: { platformName: "Android" }
    };
    const validator = new PercyCapabilityValidator(keyObject)
    sinon.stub(validator, 'existsInArray').returns(true)
    expect(() => validator.validateBrowserOSVersions()).to.throw(
      'Android 12 is not supported in Percy'
    );
  })

  it('Device name not present in percy_excluded_browsers.json', () => {
    const keyObject = {
      deviceName: 'Android 12 - 14',
      selenium_version: "3.12.2",
      os: "Android",
      os_version: "Jelly Beans",
      browser: "chrome",
      browser_version: "92",
      platformDetails: { platformName: "Android" }
    };
    const validator = new PercyCapabilityValidator(keyObject)
    expect(() => validator.validateBrowserOSVersions()).not.to.throw()
  })

  it('Valid capabilities are present', () => {
    const keyObject = {
      name: 'abc',
      selenium_version: "3.12.2",
      os: "Windows",
      os_version: "11",
      browser: "Firefox",
      browser_version: "92",
      platformDetails: { platformName: "Windows" }
    }

    const validator = new PercyCapabilityValidator(keyObject)
    expect(() => validator.validateBrowserOSVersions()).not.to.throw()
  })

  it('Valid capabilities are present without browsers', () => {
    const keyObject = {
      deviceName: 'IOS 12 - 14',
      selenium_version: "3.12.2",
      os: "IOS",
      os_version: "Jelly Beans",
      browser: "chrome",
      browser_version: "92",
      platformDetails: { platformName: "MAC" }
    };
    const validator = new PercyCapabilityValidator(keyObject)
    expect(() => validator.validateBrowserOSVersions()).not.to.throw()
  })

  it('Valid capabilities are present no configuration for os present', () => {
    const keyObject = {
      deviceName: 'IOS 12 - 14',
      selenium_version: "3.12.2",
      os: "IOS",
      os_version: "Jelly Beans",
      browser: "chrome",
      browser_version: "92",
      platformDetails: { platformName: "linux" } // linux is not present so it should not throw error
    };
    const validator = new PercyCapabilityValidator(keyObject)
    expect(() => validator.validateBrowserOSVersions()).not.to.throw()
  })
});

describe('percyCapabilityValidator - methods Testing', () => {
  let keyObject;
  beforeEach(() => {
    keyObject = {
      name: 'Android 11',
      selenium_version: "3.12.2",
      deviceName: 'SGEB-mobile',
      os: "Android",
      os_version: "Jelly Beans",
      browser: "chrome",
      browser_version: "92",
      platformDetails: { platformName: "Android" }
    };
  })
  it('existsInArray', () => {
    const validator = new PercyCapabilityValidator(keyObject)
    expect(validator.existsInArray(['One', 'Two', 'Three'], 'Two')).to.be.true;
  })

  it('existsInArray return false', () => {
    const validator = new PercyCapabilityValidator(keyObject)
    expect(validator.existsInArray([], 'One')).to.be.false
  })

  it('getKeyIfPresent', () => {
    const validator = new PercyCapabilityValidator(keyObject)
    expect(validator.getKeyIfPresent('tHrEE', { 'One': 1, 'tWO': 2, 'ThrEE': 3 },)).to.be.equal(3)
  })

  it('getIfPresent - key not present', () => {
    const validator = new PercyCapabilityValidator(keyObject)
    expect(validator.getKeyIfPresent('TWO', { 'one': 1 })).to.be.false
  })

  it('getIfPresent - empty object', () => {
    const validator = new PercyCapabilityValidator(keyObject)
    expect(validator.getKeyIfPresent('One')).to.be.false
  })

  it('log', () => {
    const hubLoggerStub = sinon.stub(HubLogger, 'miscLogger');
    const validator = new PercyCapabilityValidator(keyObject);
    validator.log('Test warning', 1);

    expect(hubLoggerStub.calledOnce).to.be.true;
    expect(hubLoggerStub.firstCall.args[0]).to.equal(validator.TAG_ERROR); // Check tag
    expect(hubLoggerStub.firstCall.args[1]).to.equal('Test warning'); // Check message
    expect(hubLoggerStub.firstCall.args[2]).to.equal(validator.LL.WARN); // Check log level

    hubLoggerStub.restore();
  })
})
