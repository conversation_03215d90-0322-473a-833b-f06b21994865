'use strict';

const sinon = require("sinon");
const getSessionDetailsHandler = require("../../../../helpers/customSeleniumHandling/jsExecutorHandler/getSessionDetailsHandler");
const RestAPIHandler = require("../../../../helpers/customSeleniumHandling/restAPIHandler");
const customExecutorHelper = require("../../../../helpers/customSeleniumHandling/customExecutorHelper");

describe("getSessionDetailsHandler", () => {
  let keyObject = {
    rails_session_id: "random_session_id",
    appTesting: false,
  };

  let requestStateObj = {
    req_data: JSON.stringify({
      script: 'browserstack_executor: {"action": "getSessionDetails"}',
    }),
    clientSessionID: "random_session_id",
  };

  let parsedCommand = JSON.parse(
    JSON.parse(requestStateObj.req_data).script.split(
      "browserstack_executor:"
    )[1]
  );

  let data = '{"automation_session":{"rails_sesion_id":"random_session_id"}}';

  beforeEach(() => {
    sinon
      .stub(RestAPIHandler.prototype, "makeRequest")
      .returns({ statusCode: 200, data: data });
    sinon.stub(customExecutorHelper, "instrumentAndSendError");
    sinon.stub(customExecutorHelper, "instrumentAndSendExecutorResponse");
  });

  afterEach(() => {
    RestAPIHandler.prototype.makeRequest.restore();
    customExecutorHelper.instrumentAndSendError.restore();
    customExecutorHelper.instrumentAndSendExecutorResponse.restore();
  });

  context("Getting Session Details", () => {
    it("Executes Successfully", async () => {
      await getSessionDetailsHandler.runGetSessionDetailsHandler(
        keyObject,
        requestStateObj,
        parsedCommand
      );
      sinon.assert.called(RestAPIHandler.prototype.makeRequest);
      sinon.assert.calledWith(
        customExecutorHelper.instrumentAndSendExecutorResponse,
        parsedCommand.action,
        keyObject,
        requestStateObj,
        false
      );
    });
  });
});
