const sinon = require('sinon');
const iosHandler = require('../../../helpers/customSeleniumHandling/acceptSSLHandler/iOSAcceptSSLCertHandler');
const customExecutorHelper = require('../../../helpers/customSeleniumHandling/customExecutorHelper');
const supporting = require('../../../supporting');
const bridge = require('../../../bridge');
const requestLib = require('../../../lib/request');
const seleniumClient = require("../../../seleniumClient");
const helper = require('../../../helper');

const {
  SAFARI_INSECURE_PAGE_TITLE
} = require('../../../constants');

describe("iosAcceptSSLHandler", () => {

  let keyObject = {
    key: "random_session_id",
    rProxyHost: "random_host",
    name: "random_bstack_host",
    rails_session_id: "random_rails_session_id",
    os: "ios",
    terminalIp: "random_ip",
    deviceName: "random-11",
    wda_port: "random_port"
  }
  let requestStateObj = {
    req_data: JSON.stringify({
      script: 'browserstack_executor: {"action": "acceptSsl"}'
    }),
    clientSessionID: "some_random_id"
  }

  beforeEach(() => {
    sinon.stub(customExecutorHelper, 'sendError');
    seleniumHeaders = seleniumClient.DEFAULT_HEADERS;
    requestLib.appendBStackHostHeader(keyObject.name, seleniumHeaders);
  });
  let callbackFn;
  afterEach(() => {
    customExecutorHelper.sendError.restore();
  });
  it("should execute for IOS", () => {
    keyObject['os'] = 'ios';
    keyObject['deviceName'] = 'random-11';
    keyObject['os_version'] = 11;

    let reqlibResp = {
      "data": JSON.stringify({
        "value": SAFARI_INSECURE_PAGE_TITLE,
        "sessionId": "appium_session_id",
        "status": 0
      })
    };

    sinon.stub(supporting, 'waitForPageLoad');
    sinon.stub(requestLib, 'call').returns(reqlibResp);
    
    return iosHandler.sslHandler(keyObject, requestStateObj, callbackFn).then((res) => {
      res.should.eql(true);
      supporting.waitForPageLoad.restore();
      requestLib.call.restore();
    });
  });
});
