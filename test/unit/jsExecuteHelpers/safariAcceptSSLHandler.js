const sinon = require('sinon');
const safariHandler = require('../../../helpers/customSeleniumHandling/acceptSSLHandler/safariAcceptSSLHandler');
const customExecutorHelper = require('../../../helpers/customSeleniumHandling/customExecutorHelper');
const supporting = require('../../../supporting');
const bridge = require('../../../bridge');
const requestLib = require('../../../lib/request');
const seleniumClient = require("../../../seleniumClient");
const helper = require('../../../helper');

const {
  SAFARI_BUTTON_TEXT_VISIT_WEBSITE,
  SAFARI_INSECURE_PAGE_TITLE
} = require('../../../constants');
const { assert } = require('chai');

describe("safariAcceptSSLHandler", () => {

  let keyObject = {
    key: "random_session_id",
    browser: "safari",
    rails_session_id: "random_rails_session_id"
  }
  let requestStateObj = {
    req_data: JSON.stringify({
      script: 'browserstack_executor: {"action": "acceptSsl"}'
    }),
    clientSessionID: "some_random_id"
  }
  
  let successBrowToOsMapping = [
    {
      osDisplayName: "Sequoia",
      pageTitle: SAFARI_INSECURE_PAGE_TITLE,
      clickButtonText: SAFARI_BUTTON_TEXT_VISIT_WEBSITE
    },
    {
      osDisplayName: "Sonoma",
      pageTitle: SAFARI_INSECURE_PAGE_TITLE,
      clickButtonText: SAFARI_BUTTON_TEXT_VISIT_WEBSITE
    },
    {
      osDisplayName: "Ventura",
      pageTitle: SAFARI_INSECURE_PAGE_TITLE,
      clickButtonText: SAFARI_BUTTON_TEXT_VISIT_WEBSITE
    },
    {
      osDisplayName: "Monterey",
      pageTitle: SAFARI_INSECURE_PAGE_TITLE,
      clickButtonText: SAFARI_BUTTON_TEXT_VISIT_WEBSITE
    },
    {
      osDisplayName: "Big Sur",
      pageTitle: SAFARI_INSECURE_PAGE_TITLE,
      clickButtonText: SAFARI_BUTTON_TEXT_VISIT_WEBSITE
    },
    {
      osDisplayName: "Catalina",
      pageTitle: SAFARI_INSECURE_PAGE_TITLE,
      clickButtonText: SAFARI_BUTTON_TEXT_VISIT_WEBSITE
    },
    {
      osDisplayName: "Mojave",
      pageTitle: SAFARI_INSECURE_PAGE_TITLE,
      clickButtonText: SAFARI_BUTTON_TEXT_VISIT_WEBSITE
    },
    {
      osDisplayName: "High Sierra",
      pageTitle: SAFARI_INSECURE_PAGE_TITLE,
      clickButtonText: SAFARI_BUTTON_TEXT_VISIT_WEBSITE
    },
    {
      osDisplayName: "Sierra",
      pageTitle: SAFARI_INSECURE_PAGE_TITLE,
      clickButtonText: SAFARI_BUTTON_TEXT_VISIT_WEBSITE
    }
  ];
  let callbackFn, seleniumClientBaseReq, wdaClientBaseReq;
  
  beforeEach(() => {
    callbackFn = sinon.stub(bridge, 'sendResponse');
    let seleniumHeaders = seleniumClient.DEFAULT_HEADERS;
    requestLib.appendBStackHostHeader(keyObject.name, seleniumHeaders);
    seleniumClientBaseReq = {
      timeout: undefined,
      hostname: undefined,
      port: undefined,
      headers: seleniumHeaders
    };
    let wdaClientHeaders = {};
    requestLib.appendBStackHostHeader(keyObject.name, wdaClientHeaders);
    wdaClientBaseReq = {
      hostname: undefined,
      port: undefined,
      headers: wdaClientHeaders
    }
  });

  afterEach(() => {
    bridge.sendResponse.restore();
  });

  successBrowToOsMapping.forEach((osBrowMapppingObj) => {
    it(`should execute for safari ${osBrowMapppingObj.osDisplayName}`, () => {
      keyObject['os_version'] = osBrowMapppingObj.osDisplayName;

      let titleCheckReq = {
        ...seleniumClientBaseReq,
        method: 'GET',
        path: `/wd/hub/session/${keyObject.key}/title`,
        body: undefined
      };
      let titleCheckInsecurePageResponse = {
        "data": JSON.stringify({
          "value": osBrowMapppingObj.pageTitle
        })
      };
      let titleCheckSecurePageResponse = {
        "data": JSON.stringify({
          "value": "something random"
        })
      };
      let scriptLine = 'CertificateWarningController.visitInsecureWebsiteWithTemporaryBypass();';
      let payload = {
        script: scriptLine,
        args: []
      }
      let executeScriptReq = {
        ...seleniumClientBaseReq,
        method: 'POST',
        path: `/wd/hub/session/${keyObject.key}/execute`,
        body: Buffer.from(JSON.stringify(payload))
      };
      executeScriptReq.headers = {
        ...executeScriptReq.headers,
        'content-length': Buffer.from(JSON.stringify(payload)).length,
      }
      let executeScriptResp = {
        "data": JSON.stringify({
          "status": 0
        })
      };

      sinon.stub(supporting, 'waitForPageLoad').callsArg(0);

      let stubbedReqLib = sinon.stub(requestLib, 'call');
      stubbedReqLib.withArgs(titleCheckReq).onCall(0).returns(titleCheckInsecurePageResponse);
      stubbedReqLib.withArgs(titleCheckReq).onCall(1).returns(titleCheckSecurePageResponse);
      stubbedReqLib.withArgs(executeScriptReq).returns(executeScriptResp);

      return safariHandler.sslHandler(keyObject, requestStateObj, callbackFn).then((res) => {
        res.should.eql(true);
        supporting.waitForPageLoad.restore();
        requestLib.call.restore();
      });
    });
  });

  it('for W3C dialects, it uses updated selenium apis',() => {
    keyObject['os_version'] = 'Catalina';
    keyObject['dialect'] = 'W3C';

      let titleCheckReq = {
        ...seleniumClientBaseReq,
        method: 'GET',
        path: `/wd/hub/session/${keyObject.key}/title`,
        body: undefined
      };
      let titleCheckInsecurePageResponse = {
        "data": JSON.stringify({
          "value": SAFARI_INSECURE_PAGE_TITLE
        })
      };
      let titleCheckSecurePageResponse = {
        "data": JSON.stringify({
          "value": "something random"
        })
      };
      let scriptLine = 'CertificateWarningController.visitInsecureWebsiteWithTemporaryBypass();';
      let payload = {
        script: scriptLine,
        args: []
      }
      let executeScriptReq = {
        ...seleniumClientBaseReq,
        method: 'POST',
        path: `/wd/hub/session/${keyObject.key}/execute/sync`,
        body: Buffer.from(JSON.stringify(payload))
      };
      executeScriptReq.headers = {
        ...executeScriptReq.headers,
        'content-length': Buffer.from(JSON.stringify(payload)).length,
      }
      let executeScriptResp = {
        "data": JSON.stringify({
          "value": null
        })
      };
      sinon.stub(supporting, 'waitForPageLoad').callsArg(0);

      let stubbedReqLib = sinon.stub(requestLib, 'call');
      stubbedReqLib.withArgs(titleCheckReq).onCall(0).returns(titleCheckInsecurePageResponse);
      stubbedReqLib.withArgs(titleCheckReq).onCall(1).returns(titleCheckSecurePageResponse);
      stubbedReqLib.withArgs(executeScriptReq).returns(executeScriptResp);

      return safariHandler.sslHandler(keyObject, requestStateObj, callbackFn).then((res) => {
        res.should.eql(true);
        supporting.waitForPageLoad.restore();
        requestLib.call.restore();
      });
  });

  it('isPostBigSur should return true', () => {
    const res = safariHandler.isPostBigSur("macven");
    assert.equal(res, true);
  });

  it('isPostBigSur should return false', () => {
    const res = safariHandler.isPostBigSur("macml");
    assert.equal(res, false);
  });
});
