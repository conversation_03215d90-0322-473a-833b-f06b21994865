'use strict';

const sinon = require('sinon');
const customExecutorHelper = require('../../../../helpers/customSeleniumHandling/customExecutorHelper');
const supporting = require('../../../../supporting');
const bridge = require('../../../../bridge');
const iosBasicAuthHandler = require('../../../../helpers/customSeleniumHandling/basicAuthHandler/handlers/iosBasicAuthHandler');
const seleniumClient = require('../../../../seleniumClient');
const HubLogger = require('../../../../log');

describe('iosBasicAuthHandler', () => {
  let keyObject; 
  let requestStateObj; 
  let parsedCommand;
  let iosHandler;
  let bridgeOpenUrlBasicAuthHandlingStub; 
  let sendErrorStub; 
  let timer;

  const NEW_IOS_VERSIONS = ['11', '12', '13']; // seperate handling for ios10

  beforeEach(() => {
    keyObject = {
      rails_session_id: 'random_rails_session_id',
      os: 'ios',
    };

    requestStateObj = {
      req_data: JSON.stringify({
        script: 'browserstack_executor: {"action": "sendBasicAuth", "arguments": {"username": "guest", "password": "guest", "timeout": 5000}}',
      }),
      clientSessionID: 'random_session_id',
    };
    parsedCommand = {
      action: 'sendBasicAuth',
      arguments: { username: 'guest', password: 'guest', timeout: 5000 },
    };

    iosHandler = new iosBasicAuthHandler(keyObject, requestStateObj, parsedCommand);

    bridgeOpenUrlBasicAuthHandlingStub = sinon.stub(bridge, 'iOSBasicAuthHandler').callsArg(3);
    sendErrorStub = sinon.stub(customExecutorHelper, 'sendError');

    timer = sinon.useFakeTimers();
    sinon.stub(bridge, 'sendResponse');
    sinon.stub(HubLogger, 'miscLogger');
  });

  afterEach(() => {
    bridge.sendResponse.restore();
    bridgeOpenUrlBasicAuthHandlingStub.restore();
    sendErrorStub.restore();
    timer.restore();
    HubLogger.miscLogger.restore();
  });

  it('calls send error if dismiss auth is called', () => {
    iosHandler.executeDismissBasicAuth();
    sinon.assert.called(sendErrorStub);
  });

  NEW_IOS_VERSIONS.forEach((os_version) => {
    it(`successfully calls iosBasicAuthHandler of bridge for ios-${os_version}`, () => {
      keyObject.os_version = os_version;
      iosHandler.executeBasicAuth();
      timer.tick(5000);

      sinon.assert.called(bridgeOpenUrlBasicAuthHandlingStub);
    });
  });
  it('calls ios 10 or below function', () => {
    keyObject.os_version = '10.3';
    sinon.stub(iosBasicAuthHandler.prototype, 'ios10OrBelowBasicAuth').callsArg(0);
    iosHandler.executeBasicAuth();
    timer.tick(5000);
    sinon.assert.called(iosBasicAuthHandler.prototype.ios10OrBelowBasicAuth);
    iosBasicAuthHandler.prototype.ios10OrBelowBasicAuth.restore();
  });
  context('ios 10', () => {
    let checkPhishingWebsiteStub; let executeScriptStub; let waitForNextPageStub; let
      callbackCheck;
    const successCallback = () => {
      callbackCheck = 'successCallback';
    };
    const errorCallback = () => {
      callbackCheck = 'errorCallBack';
    };

    beforeEach(() => {
      checkPhishingWebsiteStub = sinon.stub(seleniumClient.prototype, 'checkInsecureWebsite');
      executeScriptStub = sinon.stub(seleniumClient.prototype, 'executeScript');
      waitForNextPageStub = sinon.stub(supporting, 'waitForPageLoad');
    });

    it('calls iosBasicAuthHandler of bridge in case its not phising website', () => {
      checkPhishingWebsiteStub.throws();
      return iosHandler.ios10OrBelowBasicAuth(errorCallback, successCallback, 'temp', 'temp').then(() => {
        sinon.assert.calledOnce(checkPhishingWebsiteStub);
        sinon.assert.called(bridgeOpenUrlBasicAuthHandlingStub);

        checkPhishingWebsiteStub.restore();
        executeScriptStub.restore();
        waitForNextPageStub.restore();
      });
    });

    it('successfully executes if opened website is showing phishing website error', () => {
      return iosHandler.ios10OrBelowBasicAuth(errorCallback, successCallback, 'temp', 'temp').then(() => {
        sinon.assert.calledOnce(checkPhishingWebsiteStub);
        sinon.assert.calledOnce(executeScriptStub);
        sinon.assert.calledOnce(waitForNextPageStub);
        callbackCheck.should.eql('successCallback');

        checkPhishingWebsiteStub.restore();
        executeScriptStub.restore();
        waitForNextPageStub.restore();
      });
    });
    it('calls error callback if any error occurs during handling  of phishing website', () => {
      executeScriptStub.throws();
      return iosHandler.ios10OrBelowBasicAuth(errorCallback, successCallback, 'temp', 'temp').then(() => {
        sinon.assert.calledOnce(checkPhishingWebsiteStub);
        sinon.assert.calledOnce(executeScriptStub);
        callbackCheck.should.eql('errorCallBack');

        checkPhishingWebsiteStub.restore();
        executeScriptStub.restore();
        waitForNextPageStub.restore();
      });
    });
  });
});
