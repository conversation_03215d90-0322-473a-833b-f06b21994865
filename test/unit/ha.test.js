'use strict';

const ha = require('../../ha');
const redisClient = require('./../../redisUtils').redisClient;
const { expect } = require('chai');
const { describe, beforeEach, it } = require('mocha');
const sinon = require('sinon');
const { assert } = require('chai');
const logger = require('../../logger').customLogger;
const constants = require('../../constants');
const helper = require('../../helper');

describe('ha', () => {
  beforeEach((done) => {
    constants.pushToHootHootRegistry = {};
    redisClient.flushall().then(() => {
      done();
    });
  });

  it('incrementCounter', () => {
    const key = 'abcdef';
    redisClient.set(key, 10);
    const callback = (_, res) => {
      assert(res === 11);
    };
    ha.incrementCounter(key, callback);
    redisClient.get(key).then((res) => {
      assert.equal(res, 11);
    });
  });

  it('trackNonZeroStatusError', () => {
    const key = 'key';
    redisClient.sadd(key, 'abcdef');
    const newHashedId = 'ghijkl';
    const callback = () => {};
    sinon.stub(logger, 'ERROR', () => {});
    ha.trackNonZeroStatusError(key, newHashedId, callback);
    redisClient.smembers(key).then((res) => {
      expect(res).to.have.members(['ghijkl', 'abcdef']);
    });

    logger.ERROR.restore();
  });

  it('should send error in the callback for incrementCounter', async () => {
    const erroObj = new Error('unable to increment key');
    const incrStub = sinon.stub(redisClient, 'incr');
    incrStub.throws(erroObj);
    const cb = sinon.spy();

    sinon.stub(logger, 'ERROR', () => {});
    await ha.incrementCounter('sampleKey', cb);
    assert(cb.calledOnce);
    sinon.assert.calledWith(cb, erroObj, undefined);
    incrStub.restore();
    logger.ERROR.restore();
  });

  it('should send errorObject in callback for trackNonZeroStatusError', async () => {
    const errObj = new Error('unable to add to track status');
    const trackStub = sinon.stub(redisClient, 'sadd');
    trackStub.throws(errObj);
    const cb = sinon.spy();
    sinon.stub(logger, 'ERROR', () => {});
    await ha.trackNonZeroStatusError('sampleKey', 'sampleHashedId', cb);
    assert(cb.calledOnce);
    sinon.assert.calledWith(cb, errObj, undefined);
    trackStub.restore();

    logger.ERROR.restore();
  });

  it('should delete the key from ha', async () => {
    const KEY = 'abcd';
    await ha.setData(KEY, { key: 'some random value' });
    const cb = (err, data) => {
      assert(data !== undefined);
      assert(err === undefined);
    };

    await ha.getData(KEY, cb);
    const shouldDeleteCb = (err, data) => {
      assert(err === undefined);
      assert(data === null);
    };
    await ha.deleteData(KEY, shouldDeleteCb);
  });

  it('should send error in errorCallback for deleteData', async () => {
    const KEY = 'abcd';
    const errObj = new Error('Unable to process command');
    const deleteSingletonKeyObjectStub = sinon.stub(redisClient, 'deleteSingletonKeyObject');
    deleteSingletonKeyObjectStub.throws(errObj);
    sinon.stub(logger, 'ERROR', () => {});
    const cb = (err, data) => {
      assert(err === errObj);
      assert(data === undefined);
    };

    await ha.deleteData(KEY, cb);

    assert(logger.ERROR.calledOnce);
    logger.ERROR.restore();
    deleteSingletonKeyObjectStub.restore();
  });

  it('should handle json parsing error when data is not proper JSON', async () => {
    const KEY = 'abcd';
    const nonJSONValue = 'randomString';
    await redisClient.set(`HA_${KEY}`, nonJSONValue);
    sinon.stub(logger, 'ERROR', () => {});
    const cb = (err, data) => {
      assert(err === undefined);
      assert(data === nonJSONValue);
    };

    await ha.getData(KEY, cb);

    assert(logger.ERROR.calledOnce);
    logger.ERROR.restore();
  });

  it('should send error in getData when redisClient misbehaves', async () => {
    const KEY = 'abcd';
    const errObj = new Error('Unable to process command');
    const getStub = sinon.stub(redisClient, 'get');
    getStub.throws(errObj);
    sinon.stub(logger, 'ERROR', () => {});
    const cb = (err, data) => {
      assert(err === errObj);
      assert(data === undefined);
    };

    await ha.getData(KEY, cb);

    assert(logger.ERROR.calledOnce);
    logger.ERROR.restore();
    getStub.restore();
  });

  it('handle not setKey when alreadStopped while setData', async () => {
    const KEY = 'abcd';
    const data = { key: 'some valid value' };
    sinon.stub(logger, 'INFO', () => {});
    await redisClient.set(`alreadyStopped_${KEY}`, true);
    const cb = (err, writeRes) => {
      assert(err === undefined);
      assert(writeRes === undefined);
    };

    await ha.setData(KEY, data, cb);
    assert(logger.INFO.calledOnce);

    logger.INFO.restore();
  });

  it('handle error in redisClient while setData', async () => {
    const KEY = 'abcd';
    const data = { key: 'some valid value' };
    const errObj = new Error('Unable to process command');
    sinon.stub(logger, 'ERROR', () => {});
    const writeSingletonKeyObjectStub = sinon.stub(redisClient, 'writeSingletonKeyObject');
    writeSingletonKeyObjectStub.throws(errObj);
    const cb = (err, writeRes) => {
      assert(err === errObj);
      assert(writeRes === undefined);
    };

    await ha.setData(KEY, data, cb);
    assert(logger.ERROR.calledOnce);
    writeSingletonKeyObjectStub.restore();
    logger.ERROR.restore();
  });

  it('Set data - instrument if value length is > threshold', async () => {
    const key = 'abcd';
    // generate string of length 8100
    const data = Array(8100).fill('a').join('');

    // Create a spy on logger.INFO
    const spy = sinon.spy(logger, 'INFO');

    // Call the function
    await ha.setData(key, data);

    // Print the call count
    console.log(`logger.INFO was called ${spy.callCount} time(s).`);

    // Assert that logger.INFO was called
    assert(spy.calledOnce);
    assert(constants.pushToHootHootRegistry.redisCommandExceededCount.setData_value === 1);
    assert(constants.pushToHootHootRegistry.redisCommandExceededCount.setData_key === undefined);

    // Restore the original method
    logger.INFO.restore();
  });

  it('Set data - instrument if value length is > threshold', async () => {
    const key = Array(100).fill('a').join('');
    const data = 'abxd';

    // Create a spy on logger.INFO
    const spy = sinon.spy(logger, 'INFO');

    // Call the function
    await ha.setData(key, data);

    // Assert that logger.INFO was called
    assert(spy.calledOnce);
    assert(constants.pushToHootHootRegistry.redisCommandExceededCount.setData_value === undefined);
    assert(constants.pushToHootHootRegistry.redisCommandExceededCount.setData_key === 1);

    // Restore the original method
    logger.INFO.restore();
  });


  it('instrumentRedisCommand', (done) => {
    helper.instrumentRedisCommand('getData', 10);
    assert(constants.pushToHootHootRegistry.redisCommandCount);
    assert(constants.pushToHootHootRegistry.redisCommandRate);
    assert(constants.pushToHootHootRegistry.redisCommandCount.getData);
    assert(constants.pushToHootHootRegistry.redisCommandRate.getData);
    done();
  });
});
