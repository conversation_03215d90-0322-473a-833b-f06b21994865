const mapToOSS = require('../../utils/commandMapper/mapToOSS');

describe("mapToOSS function", () => {
  context("Map Response to OSS", () => {
    it("converts the response to OSS with status code 0", () => {
      const response = {
        value: {
          'webdriver.remote.sessionid': 'random_session_id',
          'sessionId': 'random_session_id',
          'capabilities': {
            'randomCap': 'valOne',
            'randomCapTwo': 'valTwo'
          }
        }
      }
      let mappedResponse = mapToOSS.mapResponseToOSS(response);
      mappedResponse.status.should.equal(0);
    });

    it("converts the response to OSS with status code non 0 in case of error", () => {
      const response = {
        value: {
          'webdriver.remote.sessionid': 'random_session_id',
          'sessionId': 'random_session_id',
          'capabilities': {
            'randomCap': 'valOne',
            'randomCapTwo': 'valTwo',
            'error': 'unknown command'
          }
        }
      }
      let mappedResponse = mapToOSS.mapResponseToOSS(response);
      mappedResponse.status.should.equal(9);
    });

    it("converts the response to OSS with status code non 0 in case of error", () => {
      const response = {
        value: {
          'webdriver.remote.sessionid': 'random_session_id',
          'sessionId': 'random_session_id',
          'capabilities': {
            'randomCap': 'valOne',
            'randomCapTwo': 'valTwo',
            'error': 'unknown command'
          }
        }
      }
      let mappedResponse = mapToOSS.mapResponseToOSS(response);
      mappedResponse.status.should.equal(9);
    });

    it("converts the response to OSS with status code 12 in case of element not interactable", () => {
      const response = {
        value: {
          'webdriver.remote.sessionid': 'random_session_id',
          'sessionId': 'random_session_id',
          'error': 'element not interactable'
        }
      }
      let mappedResponse = mapToOSS.mapResponseToOSS(response);
      mappedResponse.status.should.equal(12);
    });

    it("converts the response to OSS with status 32 in case of invalid selector", () => {
      const response = {
        value: {
          'webdriver.remote.sessionid': 'random_session_id',
          'sessionId': 'random_session_id',
          'error': 'invalid selector'
        }
      }
      let mappedResponse = mapToOSS.mapResponseToOSS(response);
      mappedResponse.status.should.equal(32);
    });

    it("converts the response to OSS with status 7 in case of no such element", () => {
      const response = {
        value: {
          'webdriver.remote.sessionid': 'random_session_id',
          'sessionId': 'random_session_id',
          'error': 'no such element'
        }
      }
      let mappedResponse = mapToOSS.mapResponseToOSS(response);
      mappedResponse.status.should.equal(7);
    });

    it("converts the response to OSS with status 10 in case of stale element reference", () => {
      const response = {
        value: {
          'webdriver.remote.sessionid': 'random_session_id',
          'sessionId': 'random_session_id',
          'error': 'stale element reference'
        }
      }
      let mappedResponse = mapToOSS.mapResponseToOSS(response);
      mappedResponse.status.should.equal(10);
    });

    it("converts the response to OSS with status 13 in case of error", () => {
      const response = {
        value: {
          'webdriver.remote.sessionid': 'random_session_id',
          'sessionId': 'random_session_id',
          'error': 'no such error'
        }
      }
      let mappedResponse = mapToOSS.mapResponseToOSS(response);
      mappedResponse.status.should.equal(13);
    });


    it("converts the find element command response to OSS", () => {
      const elementId = '0.32141343';
      const response = { value: { 'element-6066-11e4-a52e-4f735466cecf': elementId } }
      let mappedResponse = mapToOSS.mapResponseToOSS(response);
      mappedResponse.value.ELEMENT.should.equal(elementId);
    });

    it("converts the find elements command response to OSS", () => {
      const elementId = '0.32141343';
      const response = {
        value: [{'element-6066-11e4-a52e-4f735466cecf': elementId}]
      }
      let mappedResponse = mapToOSS.mapResponseToOSS(response);
      mappedResponse.value[0].ELEMENT.should.equal(elementId);
    });

    it("converts the command response to OSS with status code 0 in case of no elements", () => {
      const response = {value:[]}
      let mappedResponse = mapToOSS.mapResponseToOSS(response);
      mappedResponse.status.should.equal(0);
    });

    it("converts the start session command response to OSS", () => {
      const capabilities = {
        browserName: 'chrome',
        browserVersion: '79.0',
      }
      const response = { value: { capabilities } }
      let mappedResponse = mapToOSS.mapResponseToOSS(response);
      mappedResponse.value.should.equal(capabilities);
    });
  });

  context("Map Request to OSS", () => {
    context('GET - Map selenium W3C commands to OSS', () => {
      const sessionPath = '/wd/hub/session/a1b2c3d4a1b2c3d4a1b2c3d4a1b2c3d4';  
      it('converts the /fullscreen command to /maximize', () => {
        const request = { method: 'POST', path: `${sessionPath}/window/fullscreen`, headers: {} };
        const mappedRequest = mapToOSS.mapRequestToOSS(request);
        mappedRequest.method.should.equal('POST');
        mappedRequest.path.should.equal(`${sessionPath}/window/maximize`);
      });
    });
  });
});
