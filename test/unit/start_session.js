'use strict';
const startSession = require('../../start_session')
const assert = require('assert');

describe('startSession', () => {
    context('checkDesiredCaps', () => {
      it('should return true when desiredCapabilities is empty JSON and capabilities is null', (done) => {
          let caps = {
            desiredCapabilities: {}
          };
          assert.equal(startSession.checkDesiredCaps(null, null, caps, {}), true);
          done();
      });
      it('should return true when caps is null', (done) => {
        assert.equal(startSession.checkDesiredCaps(null, null, null, {}), false);
        done();
      });
      it('should return true when desiredCapabilities is absent and alwaysMatch/firstMatch present in capabilities', (done) => {
        let capabilities = {
          "alwaysMatch": {
            "platform": "android",
            "bstack:options": {}
          },
          "firstMatch": [{}]
        };
        let caps = {
          capabilities
        };
        assert.equal(startSession.checkDesiredCaps(null, null, caps, {}), true);
        done();
      });
    });
    context('addW3CFlagIfSpecifiedBrowserstackOptions', () => {
        it('should have key useW3C inside bstack:options if capabilities has bstack:options but app_testing is false', (done) => {
            let capabilities = {
              "alwaysMatch": {
                "platform": "android",
                "bstack:options": {
                }
              },
              "firstMatch": [{}]
            };
        
            let desiredCapabilties = {
              "device": "iphone X"
            }
        
            let caps = {
              "capabilities": capabilities,
              "desiredCapabilities": desiredCapabilties
            }
            let app_testing = false;

            let expectedCapabilities = {
                "alwaysMatch": {
                  "platform": "android",
                  "bstack:options": {
                      "useW3C": 'true'
                  }
                },
                "firstMatch": [{}]
              };

            let expectedCaps = {
              "capabilities": expectedCapabilities,
              "desiredCapabilities": desiredCapabilties
            }

            assert.deepStrictEqual(startSession.addW3CFlagIfSpecifiedBrowserstackOptions(caps, app_testing), expectedCaps);
            done();
          });

          it('should not have key useW3C inside bstack:options if capabilities has bstack:options and app_testing is true', (done) => {
            let capabilities = {
              "alwaysMatch": {
                "platform": "android",
                "bstack:options": {
                }
              },
              "firstMatch": [{}]
            };
        
            let desiredCapabilties = {
              "app": "bs://random_url",
              "device": "iphone X"
            }
        
            let caps = {
              "capabilities": capabilities,
              "desiredCapabilities": desiredCapabilties
            }
            let app_testing = true;
        
            assert.deepStrictEqual(startSession.addW3CFlagIfSpecifiedBrowserstackOptions(caps, app_testing), caps);
            done();
          });
    });

});
