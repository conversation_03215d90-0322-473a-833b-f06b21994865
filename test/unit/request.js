const { assert, expect } = require('chai');

var requestlib = require('../../lib/request'),
  http = require('http'),
  Agent = http.Agent,
  nock = require('nock'),
  globalconstants = require('../../constants');
  should = require('chai').should(),
  sinon = require('sinon'),
  helper = require('../behaviour/helper'),
  origHelper = require('../../helper');

describe('Request Library', function() {
  describe('Calling an URL', function() {
    it('Fetches URL via the given options', function(done) {
      var scope = nock('http://example.com:8080')
                  .post('/test')
                  .query(true)
                  .reply(function(uri, body) {
                    body.should.equal('Hello');
                    return [200, 'World'];
                  });
      requestlib.call({
        host: 'example.com',
        port: 8080,
        path: '/test',
        method: 'POST',
        body: 'Hello'
      }).then((res) => {
        res.data.should.equal('World');
        scope.isDone().should.be.true;
        done();
      });
    });
    it('<PERSON>les error when converting dataBuffer to String in case of buffer greater than 512MB', function(done) {
      const largeBuffer = Buffer.alloc(512 * 1024 * 1024, 0xff); // buffer of 512 MB

      var scope = nock('http://example.com:8080')
        .post('/test')
        .query(true)
        .reply(200, largeBuffer);
  
      requestlib.call({
        host: 'example.com',
        port: 8080,
        path: '/test',
        method: 'POST',
        body: 'Hello'
      }).then((res) => {
        should.equal(res.data, undefined);
        scope.isDone().should.be.true;
        done();
      });
    });
    it('Deletes basic auth headers for non local and rails requests', function(done) {
      var scope = nock('http://example1.com:8080', { 
                  reqheaders: {
                    'x-one': 'val1',
                  }})
                  .post('/test')
                  .query(true)
                  .reply(function(uri, body) {
                    body.should.equal('Hello');
                    return [200, 'World'];
                  });
      requestlib.call({
        host: 'example1.com',
        port: 8080,
        path: '/test',
        method: 'POST',
        body: 'Hello',
        headers: { Authorization: 'Basic some_auth', 'x-one': 'val1', 'BStack-Host': 'some_host' }
      }).then((res) => {
        res.data.should.equal('World');
        scope.isDone().should.be.true;
        done();
      });
    });
    it('Does not delete basic auth headers local and rails requests', function(done) {
      var scope = nock('http://localhost:8080', { 
                  reqheaders: {
                    'x-one': 'val1',
                    'Authorization': 'Basic some_auth'
                  }})
                  .post('/test')
                  .query(true)
                  .reply(function(uri, body) {
                    body.should.equal('Hello');
                    return [200, 'World'];
                  });
      requestlib.call({
        host: 'localhost',
        port: 8080,
        path: '/test',
        method: 'POST',
        body: 'Hello',
        headers: { Authorization: 'Basic some_auth', 'x-one': 'val1' }
      }).then((res) => {
        res.data.should.equal('World');
        scope.isDone().should.be.true;
        done();
      });
    });
    it('Detects a request error', function(done) {
      var scope = nock('http://example.com:8080')
                  .post('/test')
                  .query(true)
                  .replyWithError('Boom!');
      requestlib.call({
        host: 'example.com',
        port: 8080,
        path: '/test',
        method: 'POST'
      }).catch((e) => {
        e.type.should.equal('RequestError');
        e.message.should.equal('Boom!');
        scope.isDone().should.be.true;
        done();
      });
    });
    // test will be fixed as a part of APS-4780
    xit('Detects timeout error', function(done) {
      var scope = nock('http://example.com:8080')
                  .post('/test')
                  .query(true)
                  .socketDelay(150000)
                  .reply(function(uri, body) {
                    body.should.equal('Hello');
                    return [200, 'World'];
                  });
      requestlib.call({
        host: 'example.com',
        port: 8080,
        path: '/test',
        method: 'POST',
        body: 'Hello',
      }).catch((e) => {
        e.type.should.equal('TimeoutError');
        scope.isDone().should.be.true;
        done();
      });
    });
    it('Can do retries', function(done) {
      var scope1 = nock('http://example.com:8080')
                  .post('/test')
                  .query(true)
                  .replyWithError('Boom');
      var scope2 = nock('http://example.com:8080')
                  .post('/test')
                  .query(true)
                  .reply(function(uri, body) {
                    body.should.equal('Hello');
                    return [200, 'World'];
                  });
      requestlib.call({
        host: 'example.com',
        port: 8080,
        path: '/test',
        method: 'POST',
        body: 'Hello'
      }, 1).then((res) => {
        res.data.should.equal('World');
        scope1.isDone().should.be.true;
        scope2.isDone().should.be.true;
        done();
      });
    });
    it('Can do delay between retries', function(done) {
      var scope1 = nock('http://example.com:8080')
                  .post('/test')
                  .query(true)
                  .replyWithError('Boom');
      var scope2 = nock('http://example.com:8080')
                  .post('/test')
                  .query(true)
                  .reply(function(uri, body) {
                    body.should.equal('Hello');
                    return [200, 'World'];
                  });
      var start = Date.now();
      requestlib.call({
        host: 'example.com',
        port: 8080,
        path: '/test',
        method: 'POST',
        body: 'Hello',
        delay: 100
      }, 1).then((res) => {
        var time = Date.now() - start;
        time.should.be.above(100);
        res.data.should.equal('World');
        scope1.isDone().should.be.true;
        scope2.isDone().should.be.true;
        done();
      });
    });
    it('Handles errors in retries as well', function(done) {
      var createScope = (msg) => nock('http://example.com:8080')
          .post('/test')
          .query(true)
          .replyWithError(msg);

      var scope1 = createScope('Boom!');
      var scope2 = createScope('Another boom!');
      var scope3 = createScope('Yet another boom!');
      var start = Date.now();
      requestlib.call({
        host: 'example.com',
        port: 8080,
        path: '/test',
        method: 'POST',
        body: 'Hello',
        delay: 100
      }, 2).catch((e) => {
        var time = Date.now() - start;
        time.should.be.above(200);
        scope1.isDone().should.be.true;
        scope2.isDone().should.be.true;
        scope3.isDone().should.be.true;
        e.type.should.equal('RequestError');
        e.message.should.equal('Yet another boom!');
        done();
      });
    });
    it('Raises error if given hub-to-term options do not have BStack-Host header', function (done) {
      requestlib.call({
        host: helper.RPROXY_HOST,
        port: 45671,
        path: '/selenium_command',
        method: 'GET',
        body: 'Hello'
      }).catch((err) => {
        err.message.should.equal(helper.constants.BSTACK_HOST_HEADER_MISSING_ERR);
        done();
      });
    });
    it('Successfully requests if given hub-to-term options has BStack-Host header', function (done) {
      var macScope = helper.buildMac();
      requestlib.call({
        host: helper.RPROXY_HOST,
        port: 45671,
        path: '/selenium_command',
        method: 'GET',
        headers: requestlib.appendBStackHostHeader(helper.TERMINAL_IP)
      }).then((res) => {
        res.statusCode.should.equal(200);
        macScope.isDone().should.be.true;
        done();
      });
    });
    it('Successfully records currJarTime time when recordJarTime flag is passed as true', function (done) {
      var scope = nock('http://localhost:8080', { 
      reqheaders: {
        'x-one': 'val1',
        'Authorization': 'Basic some_auth'
      }})
      .post('/test')
      .query(true)
      .reply(function(uri, body) {
        body.should.equal('Hello');
        return [200, 'World'];
      });
      requestlib.call({
        host: 'localhost',
        port: 8080,
        path: '/test',
        method: 'POST',
        body: 'Hello',
        headers: { Authorization: 'Basic some_auth', 'x-one': 'val1' },
        recordJarTime: true
      }).then((res) => {
        scope.isDone().should.be.true;
        JSON.stringify(res).should.include('currJarTime')
        done();
      });
    });
    it('mockPerformanceJarEndpoint is passed true and records currJarTime time when recordJarTime flag is passed as true', function (done) {
      globalconstants.mobileHubTerminalKeepAlive = 1;
      var scope = nock('http://localhost:8080')
        .get('/url')
        .query(true);
      requestlib.call({
        host: 'localhost',
        port: 8080,
        path: '/url', 
        method: 'GET',
        mockPerformanceJarEndpoint: true,
        recordJarTime: true
      }).then((res) => {
        JSON.stringify(res).should.include('currJarTime'); 
        done();
      });
    });
    it('Retries and returns original reponse aftr 2 retries if rproxy returns non jar response', function (done) {
      this.timeout(20000);
      globalconstants.RPROXY_RETRY_RESPONSE_TYPES = [400, 301]
      requestlib.call({
        host: 'rproxy-dc-use3b.browserstack.com',
        port: 80,
        path: '/test',
        method: 'GET',
        headers: requestlib.appendBStackHostHeader(helper.TERMINAL_IP)
      }).then((res) => {
        res.statusCode.should.equal(301);
        done();
      });
    });
  });

  describe('skipHeaderCheck for specific hostnames and paths', function () {
    it('returns true for skipHostnames', function (done) {
      const testHostname = 'https://alert-external.browserstack.com';
      const testPath = '/';
      requestlib.skipHeaderCheck(testHostname, testPath).should.be.true;
      done();
    });
    it('returns true for skipHostnames', function (done) {
      const testHostname = 'http://monitor.browserstack.com';
      const testPath = '/';
      requestlib.skipHeaderCheck(testHostname, testPath).should.be.true;
      done();
    });
    it('returns true for skipPaths', function (done) {
      const testHostname = helper.RPROXY_HOST;
      const testPath = '/selenium/authenticate?param=value';
      requestlib.skipHeaderCheck(testHostname, testPath).should.be.true;
      done();
    });
    it('returns false for valid hub to term hostnames and paths', function (done) {
      const testHostname = helper.RPROXY_HOST;
      const testPath = '/wd/hub/session';
      requestlib.skipHeaderCheck(testHostname, testPath).should.be.false;
      done();
    });
  });

  describe('Reading from a request', function() {
    it('Reads incoming HTTP request', function(done) {
      nock.enableNetConnect('localhost');
      var server = http.createServer((req, res) => {
        requestlib.readRequest(req)
        .then((data) => {
          data.should.equal('Hello abc');
          res.end('Gotcha');
        });
      });
      var port = Math.floor(Math.random() * 16000) + 2000;
      server.listen(port, 'localhost', () => {
        requestlib.call({
          method: 'POST',
          host: 'localhost',
          path: '/',
          port: port,
          body: 'Hello abc'
        }).then((res) => {
          res.data.should.equal('Gotcha');
          server.close();
          done();
        });
      });
    });
  });

  describe('Node.js bugs', function() {
    it('Idle sockets should not give ECONNRESET', function(done) {
      // This bug has been fixed in Node v5.4.1.
      // See https://github.com/nodejs/node/pull/4482
      // We have hit the issue on production causing global exceptions
      var agent = new Agent({
        keepAlive: true,
      });
      var port = Math.floor(Math.random() * 16000) + 2000;

      var requestParams = {
        method: 'GET',
        host: 'localhost',
        port: port,
        agent: agent,
        path: '/'
      };

      var socketKey = agent.getName(requestParams);

      function get() {
        return requestlib.call(requestParams);
      }

      var server = http.createServer(function(req, res) {
        res.end('hello world');
      });

      server.listen(port, () => {
        get()
        .then((res) => {
          res.statusCode.should.equal(200);
          process.nextTick(() => {
            var freeSockets = agent.freeSockets[socketKey];
            freeSockets.length.should.equal(1);
            var freeSocket = freeSockets[0];
            freeSocket.emit('error', new Error('ECONNRESET: test'));

            get()
            .then((res) => {
              Object.keys(agent.freeSockets).length.should.equal(1);
              agent.destroy();
              server.close();
              done();
            });
          });
        });
      });
    });
  });

  describe('isMobilePort method', function() {
    const mobilePorts = {
      'appiumSelPort': [8080, 8085, 8089, 38080, 38085, 38089],
      'wdaPort': [8400, 8405, 8409, 5037, 5038],
      'chromeDriverPort': [18080, 18085, 18089, 18100, 181200, 18319],
      'privoxyPort': [26080, 26085, 26089]
    };
    it('should return true for mobile ports', function(done) {
      Object.keys(mobilePorts).forEach((portType) => {
        mobilePorts[portType].forEach((port) => {
          requestlib.isMobilePort(port.toString()).should.be.true;
        });
      });
      done();
    });
  });

  describe('getKeepAliveAgent', () => {
    it('should return mobile agent for mobile port and rproxy hostname', () => {
      const hostname = 'rproxy.example.com';
      const portType = 'mobile';
      const agent = requestlib.getKeepAliveAgent(hostname, portType);
      assert.equal(agent, requestlib.httpKeepAliveAgentPrivateMobile)
    });
  
    it('should return mobile agent for mobile port and non-rproxy hostname', () => {
      const hostname = 'example.com';
      const portType = 'mobile';
      const agent = requestlib.getKeepAliveAgent(hostname, portType);
      assert.equal(agent, requestlib.httpKeepAliveAgentMobile)
    });
  
    it('should return desktop agent for desktop port and rproxy hostname', () => {
      const hostname = 'rproxy.example.com';
      const portType = 'desktop';
      const agent = requestlib.getKeepAliveAgent(hostname, portType);
      assert.equal(agent, requestlib.httpKeepAliveAgentPrivate)
    });
  
    it('should return desktop agent for desktop port and non-rproxy hostname', () => {
      const hostname = 'example.com';
      const portType = 'desktop';
      const agent = requestlib.getKeepAliveAgent(hostname, portType);
      assert.equal(agent, requestlib.httpKeepAliveAgent)
    });
  
    it('should return null for unsupported portType', () => {
      const hostname = 'example.com';
      const portType = 'invalid';
      const agent = requestlib.getKeepAliveAgent(hostname, portType);
      assert.equal(agent, null)
    });
  
    it('should return null for empty portType', () => {
      const hostname = 'example.com';
      const agent = requestlib.getKeepAliveAgent(hostname);
      assert.equal(agent, null)
    });
  });
});

  describe('mockPerformanceSessions', () => {
    it('should return dummy response data', () => {
      const options = {
        path: 'wd/hub/abc/title',
        method: 'GET'
      };
      const performance = requestlib.mockPerformanceSessions(options);
      const res = {
        "data": '{"value": "Testing performance"}'
      };
      assert.deepEqual(performance, res)
    });

    it('should return false for wrong endpoint', () => {
      const options = {
        path: 'wd/hub/abc/tile',
        method: 'GET'
      }
      const performance = requestlib.mockPerformanceSessions(options);
      assert.equal(performance, false)
    });

    it('should return false for error endpoint', () => {
      const options = {
        method: 'GET'
      }
      const performance = requestlib.mockPerformanceSessions(options);
      assert.equal(performance, false)
    });
  });
