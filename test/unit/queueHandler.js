const queueHandler = require('../../queueHandler');
const helper = require('../../helper');
const assert = require('assert');
const sinon = require('sinon');
const constants = require('../../constants');

describe('Queue handler', () => {
  describe('Redis methods', () => {
    it('test update queue method', (done) => {
      const x = sinon.spy(helper.redisClient, 'setex');
      queueHandler.updateQueue('randomuser', 1, 1, 2, 2, false);
      assert(x.callCount == 4);
      done();
    });
    it('incr queue should send request to rails when queue is empty', (done) => {
      const user = 'random_user';
      const p1 = helper.redisClient.setex([`testQueueutilized_${user}`, constants.sessionQueueResetTime, 0], function() {});
      const p2 = helper.redisClient.setex([`testQueuelimit_${user}`, constants.sessionQueueResetTime, 5], function() {});
      const p3 = helper.redisClient.setex([`testParallelutilized_${user}`, constants.sessionQueueResetTime, 0], function() {});
      const p4 = helper.redisClient.setex([`testParallellimit_${user}`, constants.sessionQueueResetTime, 10], function() {});
      Promise.all([p1, p2, p3, p4]).then( () => {
        queueHandler.incrQueue(user, false, () => {
          assert(true);
          done();
        }, () => {
          assert(false, 'Should not come here');
          done();
        });
      });
    });

    it('incr queue should send request to rails when queue is full, parallel is empty', (done) => {
      const user = 'random_user';
      const p1 = helper.redisClient.setex([`testQueueutilized_${user}`, constants.sessionQueueResetTime, 5], function() {});
      const p2 = helper.redisClient.setex([`testQueuelimit_${user}`, constants.sessionQueueResetTime, 5], function() {});
      const p3 = helper.redisClient.setex([`testParallelutilized_${user}`, constants.sessionQueueResetTime, 0], function() {});
      const p4 = helper.redisClient.setex([`testParallellimit_${user}`, constants.sessionQueueResetTime, 10], function() {});
      Promise.all([p1, p2, p3, p4]).then( () => {
        queueHandler.incrQueue(user, false, () => {
          assert(true);
          done();
        }, () => {
          assert(false, 'Should not come here');
          done();
        });
      });
    });

    it('incr queue should not send request to rails when queue is full, parallel is full', (done) => {
      const user = 'random_user';
      const p1 = helper.redisClient.setex([`testQueueutilized_${user}`, constants.sessionQueueResetTime, 6]);
      const p2 = helper.redisClient.setex([`testQueuelimit_${user}`, constants.sessionQueueResetTime, 6]);
      const p3 = helper.redisClient.setex([`testParallelutilized_${user}`, constants.sessionQueueResetTime, 10]);
      const p4 = helper.redisClient.setex([`testParallellimit_${user}`, constants.sessionQueueResetTime, 10]);
      Promise.all([p1, p2, p3, p4]).then( () => {
        queueHandler.incrQueue(user, false, () => {
          assert(false, 'Should not come here');
          done();
        }, () => {
          assert(true);
          done();
        });
      });
    });

    it('isQueueFull should call queueFullCb when queue is full, parallel is full', (done) => {
      const user = 'random_user';
      const p1 = helper.redisClient.setex([`testQueueutilized_${user}`, constants.sessionQueueResetTime, 6]);
      const p2 = helper.redisClient.setex([`testQueuelimit_${user}`, constants.sessionQueueResetTime, 6]);
      const p3 = helper.redisClient.setex([`testParallelutilized_${user}`, constants.sessionQueueResetTime, 10]);
      const p4 = helper.redisClient.setex([`testParallellimit_${user}`, constants.sessionQueueResetTime, 10]);
      Promise.all([p1, p2, p3, p4]).then(() => {
        queueHandler.isQueueFull(user, false, () => {
          assert(true);
          done();
        }, () => {
          assert(false, 'Should not come here');
          done();
        });
      });
    });
  });

  describe('miscellaneous queue handler methods', () => {
    it('getQueueProduct should return empty string in case of automate', () => {
      const queueProduct = queueHandler.getQueueProduct({});
      assert.equal(queueProduct, '');
    });
    it('getQueueProduct should return app automate queue in case of app automate', () => {
      const queueProduct = queueHandler.getQueueProduct({ isAppAutomate: true });
      assert.equal(queueProduct, 'app_automate::');
    });
    it('getQueueProduct should return functional testing queue in case of functional test', () => {
      const queueProduct = queueHandler.getQueueProduct({ product_package: constants.FUNCTIONAL_TESTING_PACKAGE_NAME });
      assert.equal(queueProduct, 'functional_testing::');
    });
  });
});


