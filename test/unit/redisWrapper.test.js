const { assert } = require("chai");
const { helper } = require("../../log");
const instrumentation = require("../../helpers/instrumentation");
const logger = require("../../logger").customLogger;

describe('Redis Wrapper', () => {
  it('should monitor redis keys and args length', (done) => {
    const longString = 'a'.repeat(1000);
    // stub logger.infp
    const loggerSpy = sinon.spy(logger, 'INFO');
    helper.redisClient.set('test', longString);
    setTimeout(() => {
      assert(loggerSpy.calledOnce);
      loggerSpy.restore();
      done();
    }, 1000);
  });

  it('should use logger.error when incrementHootHootStat fails', (done) => {
    const longString = 'a'.repeat(1000);
    const loggerSpy = sinon.spy(logger, 'ERROR');
    const incrementHootHootStatStub = sinon.stub(instrumentation, 'incrementHootHootStat');
    incrementHootHootStatStub.throws(new Error('Something went wrong'));
    helper.redisClient.set('test', longString);
    setTimeout(() => {
      assert(incrementHootHootStatStub.calledOnce);
      assert(loggerSpy.calledOnce);
      incrementHootHootStatStub.restore();
      loggerSpy.restore();
      done();
    }, 1000);
  });
});
