'use strict';

/* eslint-disable no-underscore-dangle */

const { describe, it } = require('mocha');
const redisClient = require('../../redisUtils');
const constants = require('../../constants');
const redis = require('ioredis');
const helper = require('../../helper');

const logger = require('../../logger').customLogger;
const sinon = require('sinon');
const { assert } = require('chai');
const fs = require('fs');
const IORedisMock = require('ioredis-mock');

describe('Redis Utils', () => {
  it('should log error when redis is not intialized', () => {
    sinon.stub(logger, 'ERROR', () => {});
    redisClient.redisClient.emit('error', 'Unable to connect to instance');
    assert(logger.ERROR.calledOnce === true);
    logger.ERROR.restore();
  });

  it('should send alert in case of wrong path generated', () => {
    const fakerAlertsFn = {
      sendAlerts: () => {},
    };
    const sendAlertSpy = sinon.spy(fakerAlertsFn, 'sendAlerts');
    const error = new Error('some fake error');
    sinon.stub(fs, 'readFileSync').throws(error);
    redisClient.alertManager = fakerAlertsFn;
    redisClient.initializeCustomLuaScripts();
    assert(process.exit.calledWith(1));
    assert(sendAlertSpy.called);
    fs.readFileSync.restore();
  });

  it('should be able to read the prod current path directory', () => {
    const redisMock = new IORedisMock();
    sinon.stub(fs, 'readFileSync', () => '');
    sinon.spy(redisMock, 'defineCommand');
    redisClient._redisClient = redisMock;
    redisClient.initializeCustomLuaScripts();
    assert(redisMock.defineCommand.callCount === 4);
    fs.readFileSync.restore();
  });

  it('should use the second client if multi cluster setup', () => {
    const redisMock = new IORedisMock();
    constants.multiRedisClusterSetup = true;
    redis.createClient.restore();
    sinon.stub(logger, 'ERROR', () => {});
    sinon.stub(redis, 'createClient').returns(redisMock);
    sinon.stub(helper, 'PingZombie');
    redisClient.setupClients();
    assert(redisClient._redisClientSecond === redisMock);
    assert(redisClient.redisClientSecond === redisMock);
    redisMock.emit('close', 'test');
    assert(helper.PingZombie);
    helper.PingZombie.restore();
    logger.ERROR.restore();
    constants.multiRedisClusterSetup = false;
  });
});
