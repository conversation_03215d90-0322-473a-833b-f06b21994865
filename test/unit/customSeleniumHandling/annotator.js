const { describe, it } = require('mocha');
const { expect } = require('chai');
const { stub } = require('sinon');
const { generateRequestObject, generateCDPRequestObject, TEXT_PAYLOAD } = require('../frameworkSpecHelpers');
const { annotate } = require('../../../helpers/customSeleniumHandling/annotator');
const customExecutorHelper = require('../../../helpers/customSeleniumHandling/customExecutorHelper');
const { BROWSERSTACK_EXECUTOR_PREFIX } = require('../../../constants');

describe('#annotator', () => {
  let instrumentAndSendExecutorResponseStub;
  let PAYLOAD;
  beforeEach(() => {
    PAYLOAD = { action: 'annotate', ...TEXT_PAYLOAD };
    instrumentAndSendExecutorResponseStub = stub(customExecutorHelper,'instrumentAndSendExecutorResponse');
  });

  afterEach(() => {
    instrumentAndSendExecutorResponseStub.restore();
  })
  
  it('should log data with default level as info and type Annotation', () => {
      const requestObject = generateRequestObject();
      annotate({}, requestObject, PAYLOAD);
      PAYLOAD.arguments.level = 'info';
      const logData = {
        script: `${BROWSERSTACK_EXECUTOR_PREFIX} ${JSON.stringify(PAYLOAD)}`,
        args: []
      };
      const data = { sessionId: requestObject.clientSessionID, status: 0, value: PAYLOAD.arguments.data };
      expect(requestObject.request.log_data).to.be.equal(JSON.stringify(logData));
      expect(requestObject.data).to.be.equal(JSON.stringify(data));
      expect(instrumentAndSendExecutorResponseStub.calledOnce).to.be.true;
  });

  it('should log data with level as debug and type Annotation', () => {
    const requestObject = generateRequestObject();
    PAYLOAD.arguments.level = 'debug'
    const logData = {
      script: `${BROWSERSTACK_EXECUTOR_PREFIX} ${JSON.stringify(PAYLOAD)}`,
      args: []
    };
    annotate({}, requestObject, PAYLOAD);
    const data = { sessionId: requestObject.clientSessionID, status: 0, value: PAYLOAD.arguments.data };
    expect(requestObject.request.log_data).to.be.equal(JSON.stringify(logData));
    expect(requestObject.data).to.be.equal(JSON.stringify(data));
    expect(instrumentAndSendExecutorResponseStub.calledOnce).to.be.true;
  });
  
  it('should log data and level as debug', () => {
    const requestObject = generateRequestObject();
    PAYLOAD.arguments.type = undefined
    PAYLOAD.arguments.level = 'debug'
    annotate({}, requestObject, PAYLOAD);
    const expectedPayload = PAYLOAD; 
    expectedPayload.arguments.type = 'Annotation';
    const logData = {
      script: `${BROWSERSTACK_EXECUTOR_PREFIX} ${JSON.stringify(expectedPayload)}`,
      args: []
    };
    const data = { sessionId: requestObject.clientSessionID, status: 0, value: PAYLOAD.arguments.data };
    expect(requestObject.request.log_data).to.be.equal(JSON.stringify(logData));
    expect(requestObject.data).to.be.equal(JSON.stringify(data));
    expect(instrumentAndSendExecutorResponseStub.calledOnce).to.be.true;
  });
  
  it('should log data and level as info', () => {
    const requestObject = generateRequestObject();
    PAYLOAD.arguments.level = undefined;
    PAYLOAD.arguments.type = undefined;
    annotate({}, requestObject, PAYLOAD);
    const data = PAYLOAD;
    data.arguments.type = 'Annotation';
    data.arguments.level = 'info';
    const logData = {
      script: `${BROWSERSTACK_EXECUTOR_PREFIX} ${JSON.stringify(data)}`,
      args: []
    };
    const responseData = { sessionId: requestObject.clientSessionID, status: 0, value: PAYLOAD.arguments.data };
    expect(requestObject.request.log_data).to.be.equal(JSON.stringify(logData));
    expect(requestObject.data).to.be.equal(JSON.stringify(responseData));
    expect(instrumentAndSendExecutorResponseStub.calledOnce).to.be.true;
  });

  context('Playwright and puppeteer specific tests', () => {
    it('should populate requestObject.request in case they\'re undefined', () => {
      const requestObject = generateCDPRequestObject();
      PAYLOAD.arguments.level = undefined;
      PAYLOAD.arguments.type = undefined;
      annotate({}, requestObject, PAYLOAD);
      const data = PAYLOAD;
      data.arguments.type = 'Annotation';
      data.arguments.level = 'info';
      const logData = {
        script: `${BROWSERSTACK_EXECUTOR_PREFIX} ${JSON.stringify(data)}`,
        args: []
      };
      const responseData = { sessionId: undefined, status: 0, value: PAYLOAD.arguments.data };
      expect(requestObject.request.log_data).to.be.equal(JSON.stringify(logData));
      expect(requestObject.data).to.be.equal(JSON.stringify(responseData));
      expect(instrumentAndSendExecutorResponseStub.calledOnce).to.be.true;
    });
  });
});
