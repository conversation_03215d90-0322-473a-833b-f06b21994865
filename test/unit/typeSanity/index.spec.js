var assert = require('assert');
var typeSanity = require('../../../typeSanity');

describe('Type sanity helpers', function () {
  describe('isUndefined helper', function () {
    it('should give true for undefined values', function () {
      assert.equal(typeSanity.isUndefined(undefined), true);
    });

    it('should give true for null values', function () {
      assert.equal(typeSanity.isUndefined(null), true);
    });

    it('should give true for empty values', function () {
      assert.equal(typeSanity.isUndefined(''), true);
    });
  });

  describe('isNotUndefined helper', function () {
    it('should give true for a number', function () {
      assert.equal(typeSanity.isNotUndefined(2), true);
    });

    it('should give false for undefined or null or empty values', function () {
      assert.equal(typeSanity.isNotUndefined(undefined), false);
      assert.equal(typeSanity.isNotUndefined(null), false);
      assert.equal(typeSanity.isNotUndefined(''), false);
    });

    it("should give true for a non empty string", function () {
      assert.equal(typeSanity.isNotUndefined("sample"), true);
    });

    it("should give true for an object or an array", function () {
      assert.equal(typeSanity.isNotUndefined({}), true);
      assert.equal(typeSanity.isNotUndefined([]), true);
    });
  });

  describe('isTrueString', function () {
    it('should give true for true string in any case', function () {
      assert.equal(typeSanity.isTrueString('true'), true);
      assert.equal(typeSanity.isTrueString('True'), true);
      assert.equal(typeSanity.isTrueString('TRUE'), true);
      assert.equal(typeSanity.isTrueString('tRue'), true);
      assert.equal(typeSanity.isTrueString(true), true);
    });

    it('should give false on false string or any undefined value', function () {
      assert.equal(typeSanity.isTrueString('false'), false);
      assert.equal(typeSanity.isTrueString(undefined), false);
      assert.equal(typeSanity.isTrueString(null), false);
      assert.equal(typeSanity.isTrueString('abcd'), false);
      assert.equal(typeSanity.isTrueString([]), false);
    });
  });

  describe('isFalseString', function () {
    it('should give true for false string in any case', function () {
      assert.equal(typeSanity.isFalseString('false'), true);
      assert.equal(typeSanity.isFalseString("FALSE"), true);
      assert.equal(typeSanity.isFalseString(false), true);
    });
  });

  describe('isString', function () {
    it('should give true for string type and non empty', function () {
      assert.equal(typeSanity.isString('abcd'), true);
    });

    it('should give false for non string values', function () {
      assert.equal(typeSanity.isString(undefined), false);
      assert.equal(typeSanity.isString(null), false);
      assert.equal(typeSanity.isString({}), false);
    });
  });

  describe('isFirefox', function () {
    it('should give true value for firefox browser', function () {
      assert.equal(typeSanity.isFirefox('mozilla firefox'), true);
      assert.equal(typeSanity.isFirefox('Mozilla Firefox'), true);
      assert.equal(typeSanity.isFirefox('MOZILLA FIREFOX'), true);
      assert.equal(typeSanity.isFirefox('firefox'), true);
    });

    it('should not give true for other browsers', function () {
      assert.equal(typeSanity.isFirefox('chrome'), false);
      assert.equal(typeSanity.isFirefox('microsoft edge'), false);
      assert.equal(typeSanity.isFirefox('opera'), false);
    });
  });

  describe('isChrome', function () {
    it('should give true value for chrome browser', function () {
      assert.equal(typeSanity.isChrome('chrome'), true);
      assert.equal(typeSanity.isChrome('google chrome'), true);
      assert.equal(typeSanity.isChrome('CHROME'), true);
      assert.equal(typeSanity.isChrome('chrome canary'), true);
    });

    it('should not give true for other browsers', function () {
      assert.equal(typeSanity.isChrome('firefox'), false);
      assert.equal(typeSanity.isChrome('microsoft edge'), false);
      assert.equal(typeSanity.isChrome('opera'), false);
    });
  });

  describe('isEdge', function () {
    it('should give true value for edge browser', function () {
      assert.equal(typeSanity.isEdge('edge'), true);
      assert.equal(typeSanity.isEdge('microsoft edge'), true);
      assert.equal(typeSanity.isEdge('MICROSOFT EDGE'), true);
      assert.equal(typeSanity.isEdge('EDGE'), true);
    });

    it('should not give true for other browsers', function () {
      assert.equal(typeSanity.isEdge('firefox'), false);
      assert.equal(typeSanity.isEdge('chrome'), false);
      assert.equal(typeSanity.isEdge('opera'), false);
    });
  });

  describe('isEdgeChromium', function () {
    it('should give true value for edge chromium browser', function () {
      assert.equal(typeSanity.isEdgeChromium('edge', 79), true);
      assert.equal(typeSanity.isEdgeChromium('microsoft edge', '79.0 beta'), true);
      assert.equal(typeSanity.isEdgeChromium('MICROSOFT EDGE', '79.0'), true);
      assert.equal(typeSanity.isEdgeChromium('EDGE', '79'), true);
    });

    it('should give true value for edge chromium browser with edge options', function () {
      assert.equal(typeSanity.isEdgeChromium('edge', 80, true), true);
      assert.equal(typeSanity.isEdgeChromium('microsoft edge', '80.0 beta', true), true);
      assert.equal(typeSanity.isEdgeChromium('MICROSOFT EDGE', '80.0', true), true);
      assert.equal(typeSanity.isEdgeChromium('EDGE', '80', true), true);
    });

    it('should not give true for other browsers', function () {
      assert.equal(typeSanity.isEdgeChromium('firefox', '72'), false);
      assert.equal(typeSanity.isEdgeChromium('chrome', '78'), false);
      assert.equal(typeSanity.isEdgeChromium('opera', '20'), false);
      assert.equal(typeSanity.isEdgeChromium('edge', 18), false);
    });

    it('should return false for edge chromium browsers when version is not number', function () {
      assert.equal(typeSanity.isEdgeChromium('microsoft edge', 'VERSION'), false);
      assert.equal(typeSanity.isEdgeChromium('MICROSOFT EDGE', 'version'), false);
      assert.equal(typeSanity.isEdgeChromium('EDGE', 'version'), false);
    });
  });

  describe('isEdgeButNonChromiumBased', function() {
    it('should give true value for edge but not chromium based browser', function () {
      assert.equal(typeSanity.isEdgeButNonChromiumBased('edge', 17), true);
      assert.equal(typeSanity.isEdgeButNonChromiumBased('microsoft edge', '17.0'), true);
      assert.equal(typeSanity.isEdgeButNonChromiumBased('MICROSOFT EDGE', '17.0'), true);
      assert.equal(typeSanity.isEdgeButNonChromiumBased('EDGE', '17'), true);
    });
  });

  describe('isSafari', function () {
    it('should give true value for safari browser', function () {
      assert.equal(typeSanity.isSafari('safari'), true);
      assert.equal(typeSanity.isSafari('applesafari'), true);
      assert.equal(typeSanity.isSafari('SAFARI'), true);
    });

    it('should not give true for other browsers', function () {
      assert.equal(typeSanity.isSafari('firefox'), false);
      assert.equal(typeSanity.isSafari('chrome'), false);
      assert.equal(typeSanity.isSafari('opera'), false);
    });
  });

  describe('isHash', function () {
    it('should give true for json object', function () {
      assert.equal(typeSanity.isHash({}), true);
      assert.equal(typeSanity.isHash({a: 1, b: 2}), true);
    });

    it('should give false for non object values', function () {
      assert.equal(typeSanity.isHash('2'), false);
      assert.equal(typeSanity.isHash([1, 2, 3]), false);
      assert.equal(typeSanity.isHash(null), false);
    });
  });

  describe('isMac', function () {
    it('should give true for mac string', function () {
      assert.equal(typeSanity.isMac('macmo'), true);
      assert.equal(typeSanity.isMac('MACMO'), true);
      assert.equal(typeSanity.isMac('maccat'), true);
    });

    it('should give false for other os string', function () {
      assert.equal(typeSanity.isMac('win11'), false);
      assert.equal(typeSanity.isMac('win10'), false);
      assert.equal(typeSanity.isMac('android pie'), false);
      assert.equal(typeSanity.isMac('ios 11'), false);
    });
  });

  describe('isAndroid', function () {
    it('should give true for android string', function () {
      assert.equal(typeSanity.isAndroid('android 10'), true);
      assert.equal(typeSanity.isAndroid('android 8'), true);
    });

    it('should give false for other os string', function () {
      assert.equal(typeSanity.isAndroid('winxp'), false);
      assert.equal(typeSanity.isAndroid('ios 11'), false);
    });
  });

  describe('isWindows', function () {
    it('should give true for win string', function () {
      assert.equal(typeSanity.isWindows('windows 7'), true);
      assert.equal(typeSanity.isWindows('windows 10'), true);
      assert.equal(typeSanity.isWindows('windows xp'), true);
    });

    it('should give false for other os string', function () {
      assert.equal(typeSanity.isWindows('android pie'), false);
      assert.equal(typeSanity.isWindows('ios 11'), false);
    });
  });

  describe('isFunction', function () {
    it('should give true for functions', function () {
      assert.equal(typeSanity.isFunction(() => {}), true);
      const fn = () => {};
      assert.equal(typeSanity.isFunction(fn), true);
      function fn2() {}
      assert.equal(typeSanity.isFunction(fn2), true);
    });

    it('should give false for non function vars', function () {
      assert.equal(typeSanity.isFunction('not a function'), false);
      assert.equal(typeSanity.isFunction(10), false);
      assert.equal(typeSanity.isFunction(null), false);
      assert.equal(typeSanity.isFunction(undefined), false);
    });
  });

  describe('isEmptyJson', function () {
    it('should give true for empty json', function () {
      assert.equal(typeSanity.isEmptyJson({}), true);
    });

    it('should give false for non empty json', function () {
      assert.equal(typeSanity.isEmptyJson({ random: 'test' }), false);
    });
  });

  describe('#getType', () => {
    it('should return string for string variables', () => {
      const strings = [
        '',
        'string',
        '{ object: JSON }',
        `interpolation ${0}`,
      ];
      strings.forEach(string => assert(typeSanity.getType(string), 'string'));
    });

    it('should return array for array variables', () => {
      const arrays = [
        [],
        ['strings', ['arrays'], { objects: 1 }],
      ];
      arrays.forEach(array => assert(typeSanity.getType(array), 'array'));
    });

    it('should return object for object variables', () => {
      const objects = [
        {},
        { hello: 'world' },
        { nested: { object: undefined } },
      ];
      objects.forEach(object => assert(typeSanity.getType(object), 'object'));
    });

    it('should return unknown for others', () => {
      const unknowns = [
        null,
        10,
        undefined,
        10.7,
      ];
      unknowns.forEach(unknown => assert(typeSanity.getType(unknown), 'unknown'));
    });
  });
});
