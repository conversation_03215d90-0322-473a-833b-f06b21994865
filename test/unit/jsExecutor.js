const jsExec = require('../../lib/customSeleniumHandler/jsExecutor');
const sinon = require('sinon');
const RestAPIHandler = require('../../helpers/customSeleniumHandling/restAPIHandler');
const bridge = require('../../bridge');
const requestLib = require('../../lib/request');
const HubLogger = require('../../log');
const helper = require('../../helper');
const constants = require('../../constants');
const mainAcceptSSLHandler = require('../../helpers/customSeleniumHandling/acceptSSLHandler/mainAcceptSSLHandler');
const iOSBasicAuthHandler = require('../../helpers/customSeleniumHandling/basicAuthHandler/handlers/iosBasicAuthHandler');
const customExecutorHelper = require('../../helpers/customSeleniumHandling/customExecutorHelper');
const getSessionDetailsHandler = require('../../helpers/customSeleniumHandling/jsExecutorHandler/getSessionDetailsHandler');
const percy = require('../../helpers/customSeleniumHandling/jsExecutorHandler/percyScreenshotHandler');
const appAlly = require('../../helpers/customSeleniumHandling/jsExecutorHandler/appAllyHandler');
const { expect, assert } = require('chai');
const wdaClient = require('../../wdaClient');
const rewire = require('rewire');
const rewiredjsExec = rewire('../../lib/customSeleniumHandler/jsExecutor');
const rewiredhelperExec = rewire('../../helpers/customSeleniumHandling/customAndroidSettingsHelper');
const Promise = require('bluebird');
const browserstack = require('../../browserstack');
const adbCommandExecutor = require('../../lib/customSeleniumHandler/adbCommandExecutor');
const LL = constants.LOG_LEVEL;

describe("jsExecutor", () => {
  context("#checkandExecuteIfBstackExecutor", () => {
    it("should return false with undefined data", () => {
      const mod = rewire('../../lib/customSeleniumHandler/jsExecutor');
      const checkandExecuteIfBstackExecutor = mod.__get__('checkandExecuteIfBstackExecutor');
      const parseAndRunBstackCommandStub = sinon.stub();
      const tempExceptionLoggerStub = sinon.stub();
      const sendErrorStub = sinon.stub();
      sendErrorStub.returns(true);

      const keyObject = {
        rails_session_id: "random_session_id",
        appTesting: false
      };
      const requestStateObj = {
        req_data: JSON.stringify({
          script: 'browserstack_executor: {"action": "setSessionName", "arguments": {"name": "new_name"}}'
        })
      };
      mod.__set__('parseAndRunBstackCommand', parseAndRunBstackCommandStub);
      mod.__set__('HubLogger', { tempExceptionLogger: tempExceptionLoggerStub });
      mod.__set__('sendError', sendErrorStub);
      // parseAndRunBstackCommandStub throws error if called
      parseAndRunBstackCommandStub.throws(new Error('parseAndRunBstackCommandStub should not be called'));
      checkandExecuteIfBstackExecutor(keyObject, requestStateObj)
      sinon.assert.calledOnce(tempExceptionLoggerStub);
    });
  });

  context("Setting Session Name", () => {
    it("Executes Successfully", () => {
      let keyObject = {
        rails_session_id: "random_session_id",
        appTesting: false
      }
      let requestStateObj = {
        req_data: JSON.stringify({
          script: 'browserstack_executor: {"action": "setSessionName", "arguments": {"name": "new_name"}}'
        })
      }
      sinon.stub(RestAPIHandler.prototype, "runUpdateSessionStateExecutor");
      const executed = jsExec.checkandExecuteIfBstackExecutor(keyObject, requestStateObj);
      executed.should.eql(true);
      sinon.assert.calledWith(RestAPIHandler.prototype.runUpdateSessionStateExecutor, keyObject, requestStateObj, JSON.parse(JSON.parse(requestStateObj.req_data).script.split('browserstack_executor:')[1]));
      RestAPIHandler.prototype.runUpdateSessionStateExecutor.restore();
    });
  });

  context("Setting Session Status", () => {
    let keyObject;

    beforeEach(() => {
      keyObject = {
        rails_session_id: "random_session_id",
        appTesting: false
      };
    });

    it("Executes Successfully", () => {
      let requestStateObj = {
        req_data: JSON.stringify({
          script: 'browserstack_executor: {"action": "setSessionStatus", "arguments": {"status": "passed", "reason": "some_reason"}}'
        })
      }

      sinon.stub(RestAPIHandler.prototype, "runUpdateSessionStateExecutor");
      const executed = jsExec.checkandExecuteIfBstackExecutor(keyObject, requestStateObj);
      executed.should.eql(true);
      sinon.assert.calledWith(RestAPIHandler.prototype.runUpdateSessionStateExecutor, keyObject, requestStateObj, JSON.parse(JSON.parse(requestStateObj.req_data).script.split('browserstack_executor:')[1]));
      RestAPIHandler.prototype.runUpdateSessionStateExecutor.restore();
    });

    it("Fails with Missing status status not passed", () => {
      let requestStateObj = {
        req_data: JSON.stringify({
          script: 'browserstack_executor: {"action": "setSessionStatus", "arguments": {"reason": "some_reason"}}'
        })
      };

      sinon.stub(bridge, "sendResponse");
      const executed = jsExec.checkandExecuteIfBstackExecutor(keyObject, requestStateObj);
      executed.should.eql(true);
      sinon.assert.calledWith(bridge.sendResponse, keyObject, {
        ...requestStateObj,
        hash: 'POST:value',
        data: JSON.stringify({
          sessionId: requestStateObj.clientSessionID,
          status: constants.SELENIUM_ERROR_CODE,
          value: 'Error executing browserstack_executor command. Missing status'
        })
      });
      bridge.sendResponse.restore();
    });
  });

  context("Saving File", () => {
    let keyObject;
    let requestStateObj;

    beforeEach(() => {
      keyObject = {
        rails_session_id: "random_session_id",
        appTesting: false
      };

      requestStateObj = {
        req_data: JSON.stringify({
          script: 'browserstack_executor: {"action": "saveFile"}'
        })
      };
    });

    it("Executes bridge.autoItSendKeys Successfully", () => {
      keyObject.browser = 'internet explorer';

      sinon.stub(bridge, "autoItSendKeys");
      const executed = jsExec.checkandExecuteIfBstackExecutor(keyObject, requestStateObj);
      executed.should.eql(true);
      bridge.autoItSendKeys.called.should.eql(true);
      bridge.autoItSendKeys.restore();
    });

    it("Fails if browser is not internet explorer", () => {
      keyObject.browser = 'safari';

      sinon.stub(bridge, "sendResponse");
      sinon.stub(customExecutorHelper, "sendError");
      const executed = jsExec.checkandExecuteIfBstackExecutor(keyObject, requestStateObj);
      executed.should.eql(true);
      sinon.assert.calledWith(bridge.sendResponse, keyObject, {
        ...requestStateObj,
        hash: 'POST:value',
        data: JSON.stringify({
          sessionId: requestStateObj.clientSessionID,
          status: constants.SELENIUM_ERROR_CODE,
          value: 'Error executing browserstack_executor command. This command is only supported on internet explorer'
        })
      });
      bridge.sendResponse.restore();
      customExecutorHelper.sendError.restore();
    });
  });

  context("Basic Auth Executor: Handle", () => {
    let keyObject;
    let requestStateObj;
    let windowsBrowsers = ["chrome", "firefox", "edge", "ie", "internet explorer"];
    let macBrowsers = ["chrome", "firefox", "edge"];
    let macUnsupportedBrowsers = ["safari"];

    beforeEach(() => {
      keyObject = {
        rails_session_id: "random_session_id",
        appTesting: false
      }

      requestStateObj = {
        req_data: JSON.stringify({
          script: 'browserstack_executor: {"action": "sendBasicAuth", "arguments": {"username": "guest", "password": "guest", "timeout": 5000}}'
        }),
        clientSessionID: "random_session_id",
        request: {}
      }
      sinon.stub(bridge, "sendResponse");
      sinon.stub(HubLogger, "miscLogger");
      sinon.stub(HubLogger, "exceptionLogger");
    });

    afterEach(() => {
      bridge.sendResponse.restore();
      HubLogger.miscLogger.restore();
      HubLogger.exceptionLogger.restore();
    });

    windowsBrowsers.forEach((browser) => {
      it(`Windows Executes Successfully for Browser: ${browser}`, () => {
        keyObject['os'] = 'win10';
        keyObject['browser'] = browser

        sinon.stub(bridge, "autoItSendKeys").callsArgWith(4, "");

        let timer = sinon.useFakeTimers();
        const executed = jsExec.checkandExecuteIfBstackExecutor(keyObject, requestStateObj);
        timer.tick(5000);
        executed.should.eql(true);
        sinon.assert.calledWith(bridge.sendResponse, keyObject, {
          ...requestStateObj,
          hash: 'POST:keys',
          data: JSON.stringify({
            sessionId: requestStateObj.clientSessionID,
            status: 0,
            value: null
          })
        });
        sinon.assert.calledWith(HubLogger.miscLogger, `[BasicAuthHandler] Successfully sent data for sendBasicAuth: random_session_id`, "", LL.INFO)
        assert(requestStateObj.request.log_data.includes("REDACTED"));
        bridge.autoItSendKeys.restore();
        timer.restore();
      });
    });

    macBrowsers.forEach((browser) => {
      it(`Mac Executes Successfully for Browser: ${browser}`, () => {
        keyObject['os'] = 'mac';
        keyObject['browser'] = browser;

        sinon.stub(bridge, 'basicAuthMac').returns({
          then: sinon.stub().callsArgWith(0, "").returns({
            catch: () => { }
          })
        });

        let timer = sinon.useFakeTimers();
        const executed = jsExec.checkandExecuteIfBstackExecutor(keyObject, requestStateObj);
        timer.tick(5000);
        executed.should.eql(true);
        sinon.assert.calledWith(bridge.sendResponse, keyObject, {
          ...requestStateObj,
          hash: 'POST:keys',
          data: JSON.stringify({
            sessionId: requestStateObj.clientSessionID,
            status: 0,
            value: null
          })
        });
        sinon.assert.calledWith(HubLogger.miscLogger, `[BasicAuthHandler] Successfully sent data for sendBasicAuth: random_session_id`, "", LL.INFO);
        bridge.basicAuthMac.restore();
        timer.restore();
      });
    });

    macUnsupportedBrowsers.forEach((browser) => {
      it(`Mac Fails for Browser: ${browser}`, () => {
        keyObject['os'] = 'mac';
        keyObject['browser'] = browser;

        const executed = jsExec.checkandExecuteIfBstackExecutor(keyObject, requestStateObj);
        executed.should.eql(true);
        sinon.assert.calledWith(bridge.sendResponse, keyObject, {
          ...requestStateObj,
          hash: 'POST:value',
          data: JSON.stringify({
            sessionId: requestStateObj.clientSessionID,
            status: constants.SELENIUM_ERROR_CODE,
            value: 'Error executing browserstack_executor command. Action sendBasicAuth is only supported on Google Chrome, Mozilla Firefox, Microsoft Edge & IE'
          })
        });
      });
    });

    it(`it successfully calls execute auth of iosBasiAuthHandler for ios`, () => {
      keyObject['os'] = 'ios';
      keyObject['os_version'] = 'random_version';

      let executeBasicAuthStub = sinon.stub(iOSBasicAuthHandler.prototype, 'executeBasicAuth');

      const executed = jsExec.checkandExecuteIfBstackExecutor(keyObject, requestStateObj);

      executed.should.eql(true);
      sinon.assert.called(executeBasicAuthStub);

      executeBasicAuthStub.restore();
    });

    it(`it successfully calls dismiss basic handler for ios`, () => {
      keyObject['os'] = 'ios';
      keyObject['os_version'] = 'random_version';
      requestStateObj = {
        req_data: JSON.stringify({
          script: 'browserstack_executor: {"action": "dismissBasicAuth"}'
        }),
        clientSessionID: "random_session_id"
      }

      let dismissBasicAuthStub = sinon.stub(iOSBasicAuthHandler.prototype, 'executeDismissBasicAuth');

      const executed = jsExec.checkandExecuteIfBstackExecutor(keyObject, requestStateObj);

      executed.should.eql(true);
      sinon.assert.called(dismissBasicAuthStub);

      dismissBasicAuthStub.restore();
    });

    it(`Fails for Android`, () => {
      keyObject['os'] = 'android';
      keyObject['browser'] = 'any';

      const executed = jsExec.checkandExecuteIfBstackExecutor(keyObject, requestStateObj);
      executed.should.eql(true);
      sinon.assert.calledWith(bridge.sendResponse, keyObject, {
        ...requestStateObj,
        hash: 'POST:value',
        data: JSON.stringify({
          sessionId: requestStateObj.clientSessionID,
          status: constants.SELENIUM_ERROR_CODE,
          value: 'Error executing browserstack_executor command. Action sendBasicAuth is only supported on Windows, Mac and iOS'
        })
      });
    });

    it(`Checks for 'arguments' field and raises error if not found`, () => {
      keyObject['os'] = 'mac';
      keyObject['browser'] = 'chrome';

      requestStateObj = {
        req_data: JSON.stringify({
          script: 'browserstack_executor: {"action": "sendBasicAuth"}'
        }),
        clientSessionID: "random_session_id"
      }

      const executed = jsExec.checkandExecuteIfBstackExecutor(keyObject, requestStateObj);
      executed.should.eql(true);
      sinon.assert.calledWith(bridge.sendResponse, keyObject, {
        ...requestStateObj,
        hash: 'POST:value',
        data: JSON.stringify({
          sessionId: requestStateObj.clientSessionID,
          status: constants.SELENIUM_ERROR_CODE,
          value: 'Error executing browserstack_executor command. Action sendBasicAuth requires arguments to be present'
        })
      });
    });
  });

  context("Basic Auth Executor: Dismiss", () => {
    let keyObject;
    let requestStateObj;
    let windowsBrowsers = ["chrome", "firefox", "edge", "ie", "internet explorer"];
    let macBrowsers = ["chrome", "firefox", "edge"];
    let macUnsupportedBrowsers = ["safari"];

    beforeEach(() => {
      keyObject = {
        rails_session_id: "random_session_id",
        appTesting: false
      }

      requestStateObj = {
        req_data: JSON.stringify({
          script: 'browserstack_executor: {"action": "dismissBasicAuth"}'
        }),
        clientSessionID: "random_session_id"
      }
      sinon.stub(bridge, "sendResponse");
      sinon.stub(HubLogger, "miscLogger");
      sinon.stub(HubLogger, "exceptionLogger");
    });

    afterEach(() => {
      bridge.sendResponse.restore();
      HubLogger.miscLogger.restore();
      HubLogger.exceptionLogger.restore();
    });

    windowsBrowsers.forEach((browser) => {
      it(`Windows Executes Successfully for Browser: ${browser}`, () => {
        keyObject['os'] = 'win10';
        keyObject['browser'] = browser;

        sinon.stub(bridge, "autoItSendKeys").callsArgWith(4, "");

        let timer = sinon.useFakeTimers();
        const executed = jsExec.checkandExecuteIfBstackExecutor(keyObject, requestStateObj);
        timer.tick(5000);
        executed.should.eql(true);
        sinon.assert.calledWith(bridge.sendResponse, keyObject, {
          ...requestStateObj,
          hash: 'POST:keys',
          data: JSON.stringify({
            sessionId: requestStateObj.clientSessionID,
            status: 0,
            value: null
          })
        });
        sinon.assert.calledWith(HubLogger.miscLogger, `[BasicAuthHandler] Successfully sent data for dismissBasicAuth: random_session_id`, "", LL.INFO)
        bridge.autoItSendKeys.restore();
        timer.restore();
      });
    });

    macBrowsers.forEach((browser) => {
      it(`Mac Executes Successfully for Browser: ${browser}`, () => {
        keyObject['os'] = 'mac';
        keyObject['browser'] = browser

        sinon.stub(bridge, 'basicAuthMac').returns({
          then: sinon.stub().callsArgWith(0, "").returns({
            catch: () => { }
          })
        });

        let timer = sinon.useFakeTimers();
        const executed = jsExec.checkandExecuteIfBstackExecutor(keyObject, requestStateObj);
        timer.tick(5000);
        executed.should.eql(true);
        sinon.assert.calledWith(bridge.sendResponse, keyObject, {
          ...requestStateObj,
          hash: 'POST:keys',
          data: JSON.stringify({
            sessionId: requestStateObj.clientSessionID,
            status: 0,
            value: null
          })
        });
        sinon.assert.calledWith(HubLogger.miscLogger, `[BasicAuthHandler] Successfully sent data for dismissBasicAuth: random_session_id`, "", LL.INFO);
        bridge.basicAuthMac.restore();
        timer.restore();
      });
    });

    macUnsupportedBrowsers.forEach((browser) => {
      it(`Mac Fails for Browser: ${browser}`, () => {
        keyObject['os'] = 'mac';
        keyObject['browser'] = browser;

        const executed = jsExec.checkandExecuteIfBstackExecutor(keyObject, requestStateObj);
        executed.should.eql(true);
        sinon.assert.calledWith(bridge.sendResponse, keyObject, {
          ...requestStateObj,
          hash: 'POST:value',
          data: JSON.stringify({
            sessionId: requestStateObj.clientSessionID,
            status: constants.SELENIUM_ERROR_CODE,
            value: 'Error executing browserstack_executor command. Action dismissBasicAuth is only supported on Google Chrome, Mozilla Firefox, Microsoft Edge & IE'
          })
        });
      });
    });

    it(`fails for any android version`, () => {
      keyObject['os'] = 'android';
      keyObject['browser'] = 'any';

      const executed = jsExec.checkandExecuteIfBstackExecutor(keyObject, requestStateObj);
      executed.should.eql(true);
      sinon.assert.calledWith(bridge.sendResponse, keyObject, {
        ...requestStateObj,
        hash: 'POST:value',
        data: JSON.stringify({
          sessionId: requestStateObj.clientSessionID,
          status: constants.SELENIUM_ERROR_CODE,
          value: 'Error executing browserstack_executor command. Action dismissBasicAuth is only supported on Windows, Mac and iOS'
        })
      });
    });
  });

  context("Errors", () => {
    let keyObject;

    beforeEach(() => {
      keyObject = {
        rails_session_id: "random_session_id",
        appTesting: false
      };
    });

    it("should return false with undefined data", () => {
      let requestStateObj = {
        req_data: undefined
      }
      const executed = jsExec.checkandExecuteIfBstackExecutor(keyObject, requestStateObj);
      executed.should.eql(false);
    });

    it("should return Invalid action for unsupported action", () => {
      requestStateObj = {
        req_data: JSON.stringify({
          script: 'browserstack_executor: {"action": "abcd"}'
        })
      };

      sinon.stub(bridge, "sendResponse");
      sinon.stub(customExecutorHelper, "sendError");
      const executed = jsExec.checkandExecuteIfBstackExecutor(keyObject, requestStateObj);
      executed.should.eql(true);
      sinon.assert.calledWith(bridge.sendResponse, keyObject, {
        ...requestStateObj,
        hash: 'POST:value',
        data: JSON.stringify({
          sessionId: requestStateObj.clientSessionID,
          status: constants.SELENIUM_ERROR_CODE,
          value: 'Error executing browserstack_executor command. Invalid action abcd'
        })
      });
      bridge.sendResponse.restore();
      customExecutorHelper.sendError.restore();
    });
  });

  context("Get File Properties Executor", () => {

    beforeEach(() => {
      sinon.stub(HubLogger, 'miscLogger').returns("nothing");
      sinon.stub(helper, 'sendToEDS').returns("sentSuccessfully");
      sinon.stub(bridge, 'sendResponse').returns("sentSuccessfully");
    });

    afterEach(() => {
      HubLogger.miscLogger.restore();
      helper.sendToEDS.restore();
      bridge.sendResponse.restore();
    });

    it("Executes successfully when provided with the filename", () => {
      let keyObject = {
        rails_session_id: "random_session_id",
        appTesting: false,
        browser: "Chrome",
        os: "win",
        name: "RANDOM_HOST",
        terminal_type: "desktop",
        rProxyHost: "RANDOM_HOST_AGAIN"
      }

      let requestStateObj = {
        req_data: JSON.stringify({
          script: 'browserstack_executor: {"action": "getFileProperties", "arguments": {"filename": "random_file.txt"}}'
        }),
        clientSessionID: "some_random_id"
      }

      let resFromTerminal = {
        data: JSON.stringify({
          data: {}
        }),
        statusCode: 200
      }

      sinon.stub(requestLib, 'call').returns({
        then: sinon.stub().callsArgWith(0, resFromTerminal).returns({
          catch: () => { }
        })
      });

      const executed = jsExec.checkandExecuteIfBstackExecutor(keyObject, requestStateObj);
      executed.should.eql(true);
      bridge.sendResponse.called.should.eql(true);
      sinon.assert.calledWith(bridge.sendResponse, keyObject, {
        ...requestStateObj,
        hash: 'POST:value',
        data: JSON.stringify({
          sessionId: requestStateObj.clientSessionID,
          status: 0,
          value: JSON.parse(resFromTerminal.data).data
        })
      });
      requestLib.call.restore();
    });

    it("Fails if the session is not of desktop", () => {
      let keyObject = {
        rails_session_id: "random_session_id",
        appTesting: false,
        browser: "safari",
        os: "ios",
        name: "RANDOM_HOST",
        terminal_type: "mobile",
        rProxyHost: "RANDOM_HOST_AGAIN"
      }

      let requestStateObj = {
        req_data: JSON.stringify({
          script: 'browserstack_executor: {"action": "getFileProperties", "arguments": {"filename": "random_file.txt"}}'
        }),
        clientSessionID: "some_random_id"
      }

      const executed = jsExec.checkandExecuteIfBstackExecutor(keyObject, requestStateObj);
      executed.should.eql(true);
      sinon.assert.calledWith(bridge.sendResponse, keyObject, {
        ...requestStateObj,
        hash: 'POST:value',
        data: JSON.stringify({
          sessionId: requestStateObj.clientSessionID,
          status: constants.SELENIUM_ERROR_CODE,
          value: "Error executing browserstack_executor command. getFileProperties is supported only for desktop browsers"
        })
      });
    });
  });

  context("Check file exists Executor", () => {

    beforeEach(() => {
      sinon.stub(HubLogger, 'miscLogger').returns("nothing");
      sinon.stub(helper, 'sendToEDS').returns("sentSuccessfully");
      sinon.stub(bridge, 'sendResponse').returns("sentSuccessfully");
    });

    afterEach(() => {
      HubLogger.miscLogger.restore();
      helper.sendToEDS.restore();
      bridge.sendResponse.restore();
    });
    let keyObject = {
      rails_session_id: "random_session_id",
      appTesting: false,
      browser: "Chrome",
      os: "win",
      name: "RANDOM_HOST",
      terminal_type: "desktop",
      rProxyHost: "RANDOM_HOST_AGAIN"
    }

    let requestStateObj = {
      req_data: JSON.stringify({
        script: 'browserstack_executor: {"action": "fileExists", "arguments": {"filename": "random_file.txt"}}'
      }),
      clientSessionID: "some_random_id"
    }

    it("Executes successfully when provided with the filename", () => {

      let resFromTerminal = {
        data: JSON.stringify({
          data: {}
        }),
        statusCode: 200
      }

      sinon.stub(requestLib, 'call').returns({
        then: sinon.stub().callsArgWith(0, resFromTerminal).returns({
          catch: () => { }
        })
      });

      const executed = jsExec.checkandExecuteIfBstackExecutor(keyObject, requestStateObj);
      executed.should.eql(true);
      bridge.sendResponse.called.should.eql(true);
      sinon.assert.calledWith(bridge.sendResponse, keyObject, {
        ...requestStateObj,
        hash: 'POST:value',
        data: JSON.stringify({
          sessionId: requestStateObj.clientSessionID,
          status: 0,
        })
      });
      requestLib.call.restore();
    });

    it("Sends file not present response if file is not present", () => {

      let resFromTerminal = {
        data: JSON.stringify({
          data: {
            message: 'File could not be found',
            success: false,
            data: {}
          }
        }),
        statusCode: 404
      }

      sinon.stub(requestLib, 'call').returns({
        then: sinon.stub().callsArgWith(0, resFromTerminal).returns({
          catch: () => { }
        })
      });

      const executed = jsExec.checkandExecuteIfBstackExecutor(keyObject, requestStateObj);
      executed.should.eql(true);
      bridge.sendResponse.called.should.eql(true);
      sinon.assert.calledWith(bridge.sendResponse, keyObject, {
        ...requestStateObj,
        hash: 'POST:value',
        data: JSON.stringify({
          sessionId: requestStateObj.clientSessionID,
          status: 0,
        })
      });
      requestLib.call.restore();
    });

    it("Fails if the session is not of desktop", () => {
      let keyObject = {
        rails_session_id: "random_session_id",
        appTesting: false,
        browser: "safari",
        os: "ios",
        name: "RANDOM_HOST",
        terminal_type: "mobile",
        rProxyHost: "RANDOM_HOST_AGAIN"
      }

      let requestStateObj = {
        req_data: JSON.stringify({
          script: 'browserstack_executor: {"action": "fileExists", "arguments": {"filename": "random_file.txt"}}'
        }),
        clientSessionID: "some_random_id"
      }

      const executed = jsExec.checkandExecuteIfBstackExecutor(keyObject, requestStateObj);
      executed.should.eql(true);
      sinon.assert.calledWith(bridge.sendResponse, keyObject, {
        ...requestStateObj,
        hash: 'POST:value',
        data: JSON.stringify({
          sessionId: requestStateObj.clientSessionID,
          status: constants.SELENIUM_ERROR_CODE,
          value: "Error executing browserstack_executor command. fileExists is supported only for desktop browsers"
        })
      });
    });

  });

  context("Upload file Executor", () => {

    beforeEach(() => {
      sinon.stub(HubLogger, 'miscLogger').returns("nothing");
      sinon.stub(helper, 'sendToEDS').returns("sentSuccessfully");
      sinon.stub(bridge, 'sendResponse').returns("sentSuccessfully");
    });

    afterEach(() => {
      HubLogger.miscLogger.restore();
      helper.sendToEDS.restore();
      bridge.sendResponse.restore();
    });

    let keyObject = {
      rails_session_id: "random_session_id",
      appTesting: false,
      browser: "Chrome",
      os: "win10",
      name: "RANDOM_HOST",
      terminal_type: "desktop",
      rProxyHost: "RANDOM_HOST_AGAIN"
    }

    let requestStateObj = {
      req_data: JSON.stringify({
        script: 'browserstack_executor: {"action": "uploadFile", "arguments": {"fileName": "random_file.txt"}}'
      }),
      clientSessionID: "some_random_id"
    }

    it("Executes successfully when provided with the filename", () => {

      let resFromTerminal = {
        data: JSON.stringify({
          data: {}
        }),
        statusCode: 200
      }

      sinon.stub(requestLib, 'call').returns({
        then: sinon.stub().callsArgWith(0, resFromTerminal).returns({
          catch: () => { }
        })
      });

      const executed = jsExec.checkandExecuteIfBstackExecutor(keyObject, requestStateObj);
      executed.should.eql(true);
      bridge.sendResponse.called.should.eql(true);
      sinon.assert.calledWith(bridge.sendResponse, keyObject, {
        ...requestStateObj,
        hash: 'POST:value',
        data: JSON.stringify({
          sessionId: requestStateObj.clientSessionID,
          status: 0,
        })
      });
      requestLib.call.restore();
    });

    it("Fails if the session is not of windows 8 and above", () => {
      let keyObjects = [
        {
          rails_session_id: "random_session_id",
          appTesting: false,
          browser: "safari",
          os: "ios",
          name: "RANDOM_HOST",
          terminal_type: "mobile",
          rProxyHost: "RANDOM_HOST_AGAIN"
        },
        {
          rails_session_id: "random_session_id",
          appTesting: false,
          browser: "chrome",
          os: "win7",
          name: "RANDOM_HOST",
          terminal_type: "mobile",
          rProxyHost: "RANDOM_HOST_AGAIN"
        }
      ]
      keyObjects.forEach((keyObject) => {
        let requestStateObj = {
          req_data: JSON.stringify({
            script: 'browserstack_executor: {"action": "fileExists", "arguments": {"filename": "random_file.txt"}}'
          }),
          clientSessionID: "some_random_id"
        }

        const executed = jsExec.checkandExecuteIfBstackExecutor(keyObject, requestStateObj);
        executed.should.eql(true);
        sinon.assert.calledWith(bridge.sendResponse, keyObject, {
          ...requestStateObj,
          hash: 'POST:value',
          data: JSON.stringify({
            sessionId: requestStateObj.clientSessionID,
            status: constants.SELENIUM_ERROR_CODE,
            value: "Error executing browserstack_executor command. fileExists is supported only for desktop browsers"
          })
        });
      });
    });

    it("Fails if non chromium", () => {
      let keyObjects = [
        {
          rails_session_id: "random_session_id",
          appTesting: false,
          browser: "firefox",
          os: "win11",
          name: "RANDOM_HOST",
          terminal_type: "mobile",
          rProxyHost: "RANDOM_HOST_AGAIN"
        },
        {
          rails_session_id: "random_session_id",
          appTesting: false,
          browser: "internet explorer",
          os: "win10",
          name: "RANDOM_HOST",
          terminal_type: "mobile",
          rProxyHost: "RANDOM_HOST_AGAIN"
        }
      ]
      keyObjects.forEach((keyObject) => {
        let requestStateObj = {
          req_data: JSON.stringify({
            script: 'browserstack_executor: {"action": "fileExists", "arguments": {"filename": "random_file.txt"}}'
          }),
          clientSessionID: "some_random_id"
        }

        const executed = jsExec.checkandExecuteIfBstackExecutor(keyObject, requestStateObj);
        executed.should.eql(true);
        sinon.assert.calledWith(bridge.sendResponse, keyObject, {
          ...requestStateObj,
          hash: 'POST:value',
          data: JSON.stringify({
            sessionId: requestStateObj.clientSessionID,
            status: constants.SELENIUM_ERROR_CODE,
            value: "Error executing browserstack_executor command. fileExists is supported only for desktop browsers"
          })
        });
      });
    });
  });

  context('#updateAndroidSettings', () => {
    const originalSendRequestToPlatform = rewiredhelperExec.__get__('sendRequestToPlatform');
    const originalInstrumentAndSendError = rewiredhelperExec.__get__('instrumentAndSendError');
    const originalInstrumentAndSendExecutorResponse = rewiredhelperExec.__get__('instrumentAndSendExecutorResponse');
    const updateAndroidDeviceSettings = rewiredjsExec.__get__('updateAndroidDeviceSettings');
    let keyObject,
      parsedCommand,
      requestStateObj,
      sendRequestToPlatform,
      instrumentAndSendError;

    beforeEach(() => {
      keyObject = {
        rails_session_id: 'random_session_id',
        appTesting: true,
        name: 'host',
        wda_port: 'port',
        os: 'android',
        rproxyHost: 'host',
        accessibleFeaturesList: [constants.DF_FEATURE_FENCE_CODES.CUSTOM_TIME_PLAN_CODE, constants.DF_FEATURE_FENCE_CODES.CUSTOM_DATE_PLAN_CODE],
      };

      parsedCommand = {
        action: 'updateAndroidDeviceSettings',
        arguments: {
          customDate: 'Mar 23 2019',
          customTime: '12:45'
        }
      };

      requestStateObj = {
        req_data: JSON.stringify({
          script: `browserstack_executor: ${JSON.stringify(parsedCommand)}`,
        }),
        clientSessionID: 'xxxxxxxxxxxxxx12345',
      };
      sendRequestToPlatform = sinon.stub();
      instrumentAndSendError = sinon.stub();

      // rewiredhelperExec.__set__('sendRequestToPlatform', sendRequestToPlatform);
      rewiredhelperExec.__set__('instrumentAndSendError', instrumentAndSendError);

    });

    afterEach(() => {
      rewiredhelperExec.__set__('sendRequestToPlatform', originalSendRequestToPlatform);
      rewiredhelperExec.__set__('instrumentAndSendError', originalInstrumentAndSendError);
    });

    it('should call instrumentAndSendExecutorResponse and set lock if all validations are successful', () => {
      parsedCommand.arguments['12HourTime'] = 'On';
      const instrumentAndSendExecutorResponse = sinon.stub();
      rewiredhelperExec.__set__('instrumentAndSendExecutorResponse', instrumentAndSendExecutorResponse);
      rewiredjsExec.__set__('sendUpdateSettingsRequestToPlatform', rewiredhelperExec.__get__('sendUpdateSettingsRequestToPlatform') );
      rewiredhelperExec.__set__('sendRequestToPlatform', (executorType, serverURL, reqObj, key, error, customTimeout, customOptions, responseHandler) => {
        responseHandler({
          statusCode: 200,
          data: JSON.stringify({
            date_time: {
              status: '200',
              message: 'sucess'
            }
          })
        });
      });
      updateAndroidDeviceSettings(keyObject, requestStateObj, parsedCommand);

      expect(instrumentAndSendExecutorResponse.calledOnce).to.be.true;
      expect(keyObject.isLockedForDeviceSettingsFlow).to.be.false;
    });

    it('should call instrumentAndSendExecutorResponse and set lock if all platform is giving failure with status 500 in date_time', () => {
      const instrumentAndSendError = sinon.stub();

      rewiredhelperExec.__set__('instrumentAndSendError', instrumentAndSendError);
      rewiredjsExec.__set__('sendUpdateSettingsRequestToPlatform', rewiredhelperExec.__get__('sendUpdateSettingsRequestToPlatform') );
      rewiredhelperExec.__set__('sendRequestToPlatform', (executorType, serverURL, reqObj, key, error, customTimeout, customOptions, responseHandler) => {
        responseHandler({
          statusCode: 200,
          data: JSON.stringify({
            date_time: {
              status: '500',
              message: 'sucess'
            }
          })
        });
      });
      updateAndroidDeviceSettings(keyObject, requestStateObj, parsedCommand);

      sinon.assert.calledWith(instrumentAndSendError, 'android_device_settings_executor', keyObject, requestStateObj, constants.ANDOID_DATE_TIME_CUSTOM_EXECUTOR_ERRORS.executor_internal_error);
      // expect(keyObject.isLockedForDeviceSettingsFlow).to.be.true;
    });

    it('For 12HourTime, should call instrumentAndSendExecutorResponse and set lock if all platform is giving failure with status 500', () => {
      parsedCommand.arguments = { '12HourTime': 'On' };
      const instrumentAndSendError = sinon.stub();

      rewiredhelperExec.__set__('instrumentAndSendError', instrumentAndSendError);
      rewiredjsExec.__set__('sendUpdateSettingsRequestToPlatform', rewiredhelperExec.__get__('sendUpdateSettingsRequestToPlatform') );
      rewiredhelperExec.__set__('sendRequestToPlatform', (executorType, serverURL, reqObj, key, error, customTimeout, customOptions, responseHandler) => {
        responseHandler({
          statusCode: 200,
          data: JSON.stringify({
            date_time: {
              status: '500',
              message: 'sucess'
            }
          })
        });
      });
      updateAndroidDeviceSettings(keyObject, requestStateObj, parsedCommand);

      sinon.assert.calledWith(instrumentAndSendError, 'android_device_settings_executor', keyObject, requestStateObj, constants.ANDROID_HOUR_FORMAT_CUSTOM_EXECUTOR_ERRORS.executor_internal_error);
      // expect(keyObject.isLockedForDeviceSettingsFlow).to.be.true;
    });

    it('should call instrumentAndSendExecutorResponse and set lock if all platform failure when code unavailablity or failure in api itself', () => {
      const instrumentAndSendError = sinon.stub();

      rewiredhelperExec.__set__('instrumentAndSendError', instrumentAndSendError);
      rewiredjsExec.__set__('sendUpdateSettingsRequestToPlatform', rewiredhelperExec.__get__('sendUpdateSettingsRequestToPlatform') );
      rewiredhelperExec.__set__('sendRequestToPlatform', (executorType, serverURL, reqObj, key, error, customTimeout, customOptions, responseHandler) => {
        responseHandler({
          statusCode: 500,
          data: JSON.stringify({
            date_time: {
              status: '500',
              message: 'sucess'
            }
          })
        });
      });
      updateAndroidDeviceSettings(keyObject, requestStateObj, parsedCommand);

      sinon.assert.calledWith(instrumentAndSendError, 'android_device_settings_executor', keyObject, requestStateObj, constants.ANDOID_DATE_TIME_CUSTOM_EXECUTOR_ERRORS.executor_internal_error);
      // expect(keyObject.isLockedForDeviceSettingsFlow).to.be.true;
    });

    it('For 12HourTime, should call instrumentAndSendExecutorResponse and set lock if all platform failure when code unavailablity or failure in api itself', () => {
      parsedCommand.arguments = { '12HourTime': 'On' };
      const instrumentAndSendError = sinon.stub();
      rewiredhelperExec.__set__('instrumentAndSendError', instrumentAndSendError);
      rewiredjsExec.__set__('sendUpdateSettingsRequestToPlatform', rewiredhelperExec.__get__('sendUpdateSettingsRequestToPlatform') );
      rewiredhelperExec.__set__('sendRequestToPlatform', (executorType, serverURL, reqObj, key, error, customTimeout, customOptions, responseHandler) => {
        responseHandler({
          statusCode: 500,
          data: JSON.stringify({
            date_time: {
              status: '500',
              message: 'sucess'
            }
          })
        });
      });
      updateAndroidDeviceSettings(keyObject, requestStateObj, parsedCommand);

      sinon.assert.calledWith(instrumentAndSendError, 'android_device_settings_executor', keyObject, requestStateObj, constants.ANDROID_HOUR_FORMAT_CUSTOM_EXECUTOR_ERRORS.executor_internal_error);
      // expect(keyObject.isLockedForDeviceSettingsFlow).to.be.true;
    });

    it('should call instrumentAndSendExecutorResponse and set lock if all platform is giving failure Json parse isssue case verify', () => {
      const instrumentAndSendError = sinon.stub();

      rewiredhelperExec.__set__('instrumentAndSendError', instrumentAndSendError);
      rewiredjsExec.__set__('sendUpdateSettingsRequestToPlatform', rewiredhelperExec.__get__('sendUpdateSettingsRequestToPlatform') );
      rewiredhelperExec.__set__('sendRequestToPlatform', (executorType, serverURL, reqObj, key, error, customTimeout, customOptions, responseHandler) => {
        responseHandler({
          statusCode: 200,
          data: {
            date_time: {
              status: '500',
              message: 'sucess'
            }
          }
        });
      });
      updateAndroidDeviceSettings(keyObject, requestStateObj, parsedCommand);

      // expect(instrumentAndSendError.calledOnce).to.be.true;
      sinon.assert.calledWith(instrumentAndSendError, 'android_device_settings_executor', keyObject, requestStateObj, constants.ANDOID_DATE_TIME_CUSTOM_EXECUTOR_ERRORS.executor_internal_error);
      // expect(keyObject.isLockedForDeviceSettingsFlow).to.be.true;
    });

    it('For 12HourTime, should call instrumentAndSendExecutorResponse and set lock if all platform is giving failure Json parse isssue case verify', () => {
      parsedCommand.arguments = { '12HourTime': 'On' };
      const instrumentAndSendError = sinon.stub();
      rewiredhelperExec.__set__('instrumentAndSendError', instrumentAndSendError);
      rewiredjsExec.__set__('sendUpdateSettingsRequestToPlatform', rewiredhelperExec.__get__('sendUpdateSettingsRequestToPlatform') );
      rewiredhelperExec.__set__('sendRequestToPlatform', (executorType, serverURL, reqObj, key, error, customTimeout, customOptions, responseHandler) => {
        responseHandler({
          statusCode: 200,
          data: {
            date_time: {
              status: '500',
              message: 'sucess'
            }
          }
        });
      });
      updateAndroidDeviceSettings(keyObject, requestStateObj, parsedCommand);

      // expect(instrumentAndSendError.calledOnce).to.be.true;
      sinon.assert.calledWith(instrumentAndSendError, 'android_device_settings_executor', keyObject, requestStateObj, constants.ANDROID_HOUR_FORMAT_CUSTOM_EXECUTOR_ERRORS.executor_internal_error);
      // expect(keyObject.isLockedForDeviceSettingsFlow).to.be.true;
    });

    it('should raise and error and not call sendRequestToPlatform if appTesting is false', () => {
      parsedCommand.arguments['12HourTime'] = 'On';
      keyObject.appTesting = false;
      const instrumentAndSendError = sinon.stub();
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);
      updateAndroidDeviceSettings(keyObject, requestStateObj, parsedCommand);
      sinon.assert.calledWith(instrumentAndSendError, 'android_device_settings_executor', keyObject, requestStateObj, constants.ANDOID_DATE_TIME_CUSTOM_EXECUTOR_ERRORS.incompatible_product);
      sinon.assert.notCalled(sendRequestToPlatform);
    });

    it('should raise an error and not call sendRequestToPlatform if os is not android for custom executor', () => {
      parsedCommand.arguments['12HourTime'] = 'On';
      keyObject.os = 'ios';
      const instrumentAndSendError = sinon.stub();
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);
      updateAndroidDeviceSettings(keyObject, requestStateObj, parsedCommand);
      sinon.assert.calledWith(instrumentAndSendError, 'android_device_settings_executor', keyObject, requestStateObj, constants.ANDOID_DATE_TIME_CUSTOM_EXECUTOR_ERRORS.incompatible_platform);
      sinon.assert.notCalled(sendRequestToPlatform);
    });

    it('should raise an error and not call sendRequestToPlatform if os is not android for custom executor', () => {
      parsedCommand.arguments['12HourTime'] = 'On';
      keyObject.accessibleFeaturesList = [];
      const instrumentAndSendError = sinon.stub();
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);
      updateAndroidDeviceSettings(keyObject, requestStateObj, parsedCommand);
      sinon.assert.calledWith(instrumentAndSendError, 'android_device_settings_executor', keyObject, requestStateObj, constants.ANDROID_HOUR_FORMAT_CUSTOM_EXECUTOR_ERRORS.feature_not_available_in_current_plan_for_aa);
      sinon.assert.notCalled(sendRequestToPlatform);
    });

    it('should raise an error and not call sendRequestToPlatform if os version is less than 6 for custom executor', () => {
      keyObject.os_version = '4.4';
      const instrumentAndSendError = sinon.stub();
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);
      updateAndroidDeviceSettings(keyObject, requestStateObj, parsedCommand);
      sinon.assert.calledWith(instrumentAndSendError, 'android_device_settings_executor', keyObject, requestStateObj, constants.ANDOID_DATE_TIME_CUSTOM_EXECUTOR_ERRORS.incompatible_os_version);
      sinon.assert.notCalled(sendRequestToPlatform);
    });

    it('For 12HourTime, should raise an error and not call sendRequestToPlatform if os version is less than 6 for custom executor', () => {
      parsedCommand.arguments = { '12HourTime': 'On' };
      keyObject.os_version = '4.4';
      const instrumentAndSendError = sinon.stub();
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);
      updateAndroidDeviceSettings(keyObject, requestStateObj, parsedCommand);
      sinon.assert.calledWith(instrumentAndSendError, 'android_device_settings_executor', keyObject, requestStateObj, constants.ANDROID_HOUR_FORMAT_CUSTOM_EXECUTOR_ERRORS.incompatible_os_version);
      sinon.assert.notCalled(sendRequestToPlatform);
    });


    it('should raise an error and not call sendRequestToPlatform if customTime has invalid argument for custom executor with wrong time', () => {
      const instrumentAndSendError = sinon.stub();
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);
      parsedCommand.arguments.customTime = '25:45';
      updateAndroidDeviceSettings(keyObject, requestStateObj, parsedCommand);
      sinon.assert.calledWith(instrumentAndSendError, 'android_device_settings_executor', keyObject, requestStateObj, constants.ANDOID_DATE_TIME_CUSTOM_EXECUTOR_ERRORS.invalid_value);
      sinon.assert.notCalled(sendRequestToPlatform);
    });

    it('should raise an error and not call sendRequestToPlatform if customTime has invalid argument for custom executor wrong date', () => {
      const instrumentAndSendError = sinon.stub();
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);
      parsedCommand.arguments.customDate = 'Mar 12 2019rfy';
      updateAndroidDeviceSettings(keyObject, requestStateObj, parsedCommand);
      sinon.assert.calledWith(instrumentAndSendError, 'android_device_settings_executor', keyObject, requestStateObj, constants.ANDOID_DATE_TIME_CUSTOM_EXECUTOR_ERRORS.invalid_value);
      sinon.assert.notCalled(sendRequestToPlatform);
    });

    it('should raise an error and not call sendRequestToPlatform if 12HourTime has invalid argument (case insenstive)', () => {
      parsedCommand.arguments = { '12HourTime': 'offff' };
      const instrumentAndSendError = sinon.stub();
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);
      parsedCommand.arguments['12HourTime'] = 'offff';
      updateAndroidDeviceSettings(keyObject, requestStateObj, parsedCommand);
      sinon.assert.calledWith(instrumentAndSendError, 'android_device_settings_executor', keyObject, requestStateObj, constants.ANDROID_HOUR_FORMAT_CUSTOM_EXECUTOR_ERRORS.invalid_value);
      sinon.assert.notCalled(sendRequestToPlatform);
    });


    it('should raise an error and not call sendRequestToPlatform if customTime has invalid argument for custom executor with wrong date', () => {
      parsedCommand.arguments.customDate = 'Feb 29 2022';
      const instrumentAndSendError = sinon.stub();
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);
      updateAndroidDeviceSettings(keyObject, requestStateObj, parsedCommand);
      sinon.assert.calledWith(instrumentAndSendError, 'android_device_settings_executor', keyObject, requestStateObj, constants.ANDOID_DATE_TIME_CUSTOM_EXECUTOR_ERRORS.invalid_value);
      sinon.assert.notCalled(sendRequestToPlatform);
    });

    it('should raise multiple commands error if multiple requests comes', () => {
      const instrumentAndSendError = sinon.stub();
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);
      parsedCommand.arguments.customTime = '25:45';
      keyObject.isLockedForDeviceSettingsFlow = true;
      updateAndroidDeviceSettings(keyObject, requestStateObj, parsedCommand);
      sinon.assert.calledWith(instrumentAndSendError, 'android_device_settings_executor', keyObject, requestStateObj, constants.UPDATE_ANDROID_SETTINGS_EXECUTOR_ERRORS.parallel_update_android_settings_command_error);
      sinon.assert.notCalled(sendRequestToPlatform);
    });

    it('should drop request if any invalid command is sent', () => {
      const instrumentAndSendError = sinon.stub();
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);
      parsedCommand.arguments = { command: '25:45' };
      updateAndroidDeviceSettings(keyObject, requestStateObj, parsedCommand);
      sinon.assert.notCalled(sendRequestToPlatform);
      sinon.assert.calledOnce(instrumentAndSendError);
      sinon.assert.calledWith(instrumentAndSendError, 'android_device_settings_executor', keyObject, requestStateObj, constants.UPDATE_ANDROID_SETTINGS_EXECUTOR_ERRORS.invalid_argument);
    });

    it('should raise multiple commands error if multiple requests comes', () => {
      parsedCommand = {
        action: 'updateIosDeviceSettings',
        arguments: {
          customDate: '12:45'
        }
      };
      const instrumentAndSendError = sinon.stub();
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);
      keyObject.isLockedForDeviceSettingsFlow = true;
      updateAndroidDeviceSettings(keyObject, requestStateObj, parsedCommand);
      sinon.assert.calledWith(instrumentAndSendError, 'android_device_settings_executor', keyObject, requestStateObj, constants.UPDATE_ANDROID_SETTINGS_EXECUTOR_ERRORS.parallel_update_android_settings_command_error);
      sinon.assert.notCalled(sendRequestToPlatform);
    });
  });

  context("Get File Content Executor", () => {

    beforeEach(() => {
      sinon.stub(HubLogger, 'miscLogger').returns("nothing");
      sinon.stub(helper, 'sendToEDS').returns("sentSuccessfully");
      sinon.stub(bridge, 'sendResponse').returns("sentSuccessfully");
    });

    afterEach(() => {
      HubLogger.miscLogger.restore();
      helper.sendToEDS.restore();
      bridge.sendResponse.restore();
    });

    it("Executes successfully when provided with the filename", () => {
      let keyObject = {
        rails_session_id: "random_session_id",
        appTesting: false,
        browser: "Chrome",
        os: "win",
        name: "RANDOM_HOST",
        terminal_type: "desktop",
        rProxyHost: "RANDOM_HOST_AGAIN"
      }

      let requestStateObj = {
        req_data: JSON.stringify({
          script: 'browserstack_executor: {"action": "getFileContent", "arguments": {"filename": "random_file.txt"}}'
        }),
        clientSessionID: "some_random_id"
      }

      let resFromTerminal = {
        buffer: Buffer.from("RANDOM CONTENT OF THE FILE"),
        data: {},
        statusCode: 200
      }

      sinon.stub(requestLib, 'call').returns({
        then: sinon.stub().callsArgWith(0, resFromTerminal).returns({
          catch: () => { }
        })
      });

      const executed = jsExec.checkandExecuteIfBstackExecutor(keyObject, requestStateObj);
      executed.should.eql(true);
      bridge.sendResponse.called.should.eql(true);
      sinon.assert.calledWith(bridge.sendResponse, keyObject, {
        ...requestStateObj,
        hash: 'POST:value',
        data: JSON.stringify({
          sessionId: requestStateObj.clientSessionID,
          status: 0,
          value: resFromTerminal.buffer.toString("base64")
        })
      });
      requestLib.call.restore();
    });

    it("Fails if the session is not of desktop", () => {
      let keyObject = {
        rails_session_id: "random_session_id",
        appTesting: false,
        browser: "safari",
        os: "ios",
        name: "RANDOM_HOST",
        terminal_type: "mobile",
        rProxyHost: "RANDOM_HOST_AGAIN"
      }

      let requestStateObj = {
        req_data: JSON.stringify({
          script: 'browserstack_executor: {"action": "getFileContent", "arguments": {"filename": "random_file.txt"}}'
        }),
        clientSessionID: "some_random_id"
      }

      const executed = jsExec.checkandExecuteIfBstackExecutor(keyObject, requestStateObj);
      executed.should.eql(true);
      sinon.assert.calledWith(bridge.sendResponse, keyObject, {
        ...requestStateObj,
        hash: 'POST:value',
        data: JSON.stringify({
          sessionId: requestStateObj.clientSessionID,
          status: constants.SELENIUM_ERROR_CODE,
          value: "Error executing browserstack_executor command. getFileContent is supported only for desktop browsers"
        })
      });
    });
  });


  context('runBstackCommand case statement with biometric', () => {
    let keyObject = {};
    let requestStateObj = {
      req_data: JSON.stringify({
        script: 'browserstack_executor: { "action": "biometric"}'
      }),
      clientSessionID: 'random_session_id'
    };
    const runBstackCommand = rewiredjsExec.__get__('checkandExecuteIfBstackExecutor');


    it('runBstackCommand case statement with biometric', () => {
      const original = rewiredjsExec.__get__('runBiometricPopupHandler');
      const runBiometricPopupHandler = sinon.stub();
      rewiredjsExec.__set__('runBiometricPopupHandler', runBiometricPopupHandler);
      runBstackCommand(keyObject, requestStateObj);
      expect(runBiometricPopupHandler.calledOnce).to.be.true;
      rewiredjsExec.__set__('runBiometricPopupHandler', original);
    });
  });

  context('runBstackCommand case stament injectAudio', () => {
    let keyObject = {};
    let requestStateObj = {
      req_data: JSON.stringify({
        script: 'browserstack_executor: { "action": "injectAudio"}'
      }),
      clientSessionID: 'random_session_id'
    };
    const runBstackCommand = rewiredjsExec.__get__('checkandExecuteIfBstackExecutor');


    it('runBstackCommand case stament injectAudio', () => {
      const original = rewiredjsExec.__get__('runCameraImageAndAudioInjectionHandler');
      const runCameraImageAndAudioInjectionHandler = sinon.stub();
      rewiredjsExec.__set__('runCameraImageAndAudioInjectionHandler', runCameraImageAndAudioInjectionHandler);
      runBstackCommand(keyObject, requestStateObj);
      expect(runCameraImageAndAudioInjectionHandler.calledOnce).to.be.true;
      rewiredjsExec.__set__('runBiometricPopupHandler', original);
    });
  });

  context('runBstackCommand case stament startAudio', () => {
    let keyObject = {};
    let requestStateObj = {
      req_data: JSON.stringify({
        script: 'browserstack_executor: { "action": "startAudio"}'
      }),
      clientSessionID: 'random_session_id'
    };
    const runBstackCommand = rewiredjsExec.__get__('checkandExecuteIfBstackExecutor');


    it('runBstackCommand case stament startAudio', () => {
      const original = rewiredjsExec.__get__('runControlAudioHandler');
      const runControlAudioHandler = sinon.stub();
      rewiredjsExec.__set__('runControlAudioHandler', runControlAudioHandler);
      runBstackCommand(keyObject, requestStateObj);
      expect(runControlAudioHandler.calledOnce).to.be.true;
      rewiredjsExec.__set__('runControlAudioHandler', original);
    });
  });

  context('runBstackCommand case stament stopAudio', () => {
    let keyObject = {};
    let requestStateObj = {
      req_data: JSON.stringify({
        script: 'browserstack_executor: { "action": "stopAudio"}'
      }),
      clientSessionID: 'random_session_id'
    };
    const runBstackCommand = rewiredjsExec.__get__('checkandExecuteIfBstackExecutor');


    it('runBstackCommand case stament stopAudio', () => {
      const original = rewiredjsExec.__get__('runControlAudioHandler');
      const runControlAudioHandler = sinon.stub();
      rewiredjsExec.__set__('runControlAudioHandler', runControlAudioHandler);
      runBstackCommand(keyObject, requestStateObj);
      expect(runControlAudioHandler.calledOnce).to.be.true;
      rewiredjsExec.__set__('runControlAudioHandler', original);
    });
  });


  context('runBstackCommand case stament applePay', () => {
    let keyObject = {};
    let requestStateObj = {
      req_data: JSON.stringify({
        script: 'browserstack_executor: { "action": "applePay"}'
      }),
      clientSessionID: 'random_session_id'
    };
    const runBstackCommand = rewiredjsExec.__get__('checkandExecuteIfBstackExecutor');


    it('runBstackCommand case stament applePay', () => {
      const original = rewiredjsExec.__get__('applePayCustomHandler');
      const applePayCustomHandler = sinon.stub();
      rewiredjsExec.__set__('applePayCustomHandler', applePayCustomHandler);
      runBstackCommand(keyObject, requestStateObj);
      expect(applePayCustomHandler.calledOnce).to.be.true;
      rewiredjsExec.__set__('applePayCustomHandler', original);
    });
  });

  context('runBstackCommand case stament customGesture', () => {
    let keyObject = {};
    let requestStateObj = {
      req_data: JSON.stringify({
        script: 'browserstack_executor: { "action": "customGesture"}'
      }),
      clientSessionID: 'random_session_id'
    }
    const runBstackCommand = rewiredjsExec.__get__('checkandExecuteIfBstackExecutor');


    it('runControlAudioHandler', () => {
      const original = rewiredjsExec.__get__('customGesturesHandler');
      const customGesturesHandler = sinon.stub();
      rewiredjsExec.__set__('customGesturesHandler', customGesturesHandler);
      runBstackCommand(keyObject, requestStateObj);
      expect(customGesturesHandler.calledOnce).to.be.true;
      rewiredjsExec.__set__('customGesturesHandler', original);
    });
  });


  context('runBstackCommand case stament updateAppSettings', () => {
    let keyObject = {};
    let requestStateObj = {
      req_data: JSON.stringify({
        script: 'browserstack_executor: { "action": "updateAppSettings"}'
      }),
      clientSessionID: 'random_session_id'
    };
    const runBstackCommand = rewiredjsExec.__get__('checkandExecuteIfBstackExecutor');


    it('runControlAudioHandler', () => {
      const original = rewiredjsExec.__get__('runUpdateAppSettingsHandler');
      const runUpdateAppSettingsHandler = sinon.stub();
      rewiredjsExec.__set__('runUpdateAppSettingsHandler', runUpdateAppSettingsHandler);
      runBstackCommand(keyObject, requestStateObj);
      expect(runUpdateAppSettingsHandler.calledOnce).to.be.true;
      rewiredjsExec.__set__('runUpdateAppSettingsHandler', original);
    });
  });

  context('runBstackCommand case stament annotate', () => {
    let keyObject = {};
    let requestStateObj = {
      req_data: JSON.stringify({
        script: 'browserstack_executor: { "action": "annotate"}'
      }),
      clientSessionID: 'random_session_id'
    };
    const runBstackCommand = rewiredjsExec.__get__('checkandExecuteIfBstackExecutor');


    it('runCameraImageAndAudioInjectionHandler', () => {
      const original = rewiredjsExec.__get__('annotate');
      const annotate = sinon.stub();
      rewiredjsExec.__set__('annotate', annotate);
      runBstackCommand(keyObject, requestStateObj);
      expect(annotate.calledOnce).to.be.true;
      rewiredjsExec.__set__('annotate', original);
    });
  });


  context('runBstackCommand case stament deviceInfo', () => {
    let keyObject = {};
    let requestStateObj = {
      req_data: JSON.stringify({
        script: 'browserstack_executor: { "action": "deviceInfo"}'
      }),
      clientSessionID: 'random_session_id'
    }
    const runBstackCommand = rewiredjsExec.__get__('checkandExecuteIfBstackExecutor');


    it('runCameraImageAndAudioInjectionHandler', () => {
      const original = rewiredjsExec.__get__('deviceInfoHandler');
      const deviceInfoHandler = sinon.stub();
      rewiredjsExec.__set__('deviceInfoHandler', deviceInfoHandler);
      runBstackCommand(keyObject, requestStateObj);
      expect(deviceInfoHandler.calledOnce).to.be.true;
      rewiredjsExec.__set__('deviceInfoHandler', original);
    });
  });

  context('runBstackCommand case stament cameraImageInjection', () => {
    let keyObject = {};
    let requestStateObj = {
      req_data: JSON.stringify({
        script: 'browserstack_executor: { "action": "cameraImageInjection"}'
      }),
      clientSessionID: 'random_session_id'
    }
    const runBstackCommand = rewiredjsExec.__get__('checkandExecuteIfBstackExecutor');


    it('runCameraImageAndAudioInjectionHandler', () => {
      const original = rewiredjsExec.__get__('runCameraImageAndAudioInjectionHandler');
      const runCameraImageAndAudioInjectionHandler = sinon.stub();
      rewiredjsExec.__set__('runCameraImageAndAudioInjectionHandler', runCameraImageAndAudioInjectionHandler);
      runBstackCommand(keyObject, requestStateObj);
      expect(runCameraImageAndAudioInjectionHandler.calledOnce).to.be.true;
      rewiredjsExec.__set__('runCameraImageAndAudioInjectionHandler', original);
    });
  });

  context('runBstackCommand case statement lighthouse', () => {
    let keyObject = {};
    let requestStateObj = {
      req_data: JSON.stringify({
        script: 'browserstack_executor: { "action": "lighthouse"}'
      }),
      clientSessionID: 'random_session_id'
    }
    const runBstackCommand = rewiredjsExec.__get__('checkandExecuteIfBstackExecutor');


    it('calls lighthouseHandler', () => {
      const original = rewiredjsExec.__get__('lighthouseHandler');
      const lighthouseHandlerStub = sinon.stub();
      rewiredjsExec.__set__('lighthouseHandler', lighthouseHandlerStub);
      runBstackCommand(keyObject, requestStateObj);
      expect(lighthouseHandlerStub.calledOnce).to.be.true;
      rewiredjsExec.__set__('lighthouseHandler', original);
    });
  });

  context('runBstackCommand case statement lighthouseAudit', () => {
    let keyObject = {};
    let requestStateObj = {
      req_data: JSON.stringify({
        script: 'browserstack_executor: { "action": "lighthouseAudit"}'
      }),
      clientSessionID: 'random_session_id'
    }
    const runBstackCommand = rewiredjsExec.__get__('checkandExecuteIfBstackExecutor');

    it('calls lighthouseExecutorHandler', () => {
      const original = rewiredjsExec.__get__('lighthouseExecutorHandler');
      const lighthouseExecutorHandlerStub = sinon.stub();
      rewiredjsExec.__set__('lighthouseExecutorHandler', lighthouseExecutorHandlerStub);
      runBstackCommand(keyObject, requestStateObj);
      expect(lighthouseExecutorHandlerStub.calledOnce).to.be.true;
      rewiredjsExec.__set__('lighthouseExecutorHandler', original);
    });
  });

  context("Accept SSL Certs", () => {
    let keyObject = {};
    let requestStateObj = {
      req_data: JSON.stringify({
        script: 'browserstack_executor: { "action": "acceptSsl"}'
      }),
      clientSessionID: "random_session_id"
    }
    it('should call accept ssl handler', () => {
      sinon.stub(mainAcceptSSLHandler, 'runAcceptSSLHandler');

      const executed = jsExec.checkandExecuteIfBstackExecutor(keyObject, requestStateObj);
      executed.should.eql(true);
      sinon.assert.called(mainAcceptSSLHandler.runAcceptSSLHandler)

      mainAcceptSSLHandler.runAcceptSSLHandler.restore();
    });
  });

  context('parseAndRunBstackCommand', () => {
    const commandJsonString = JSON.stringify({
      action: 'adbShell'
    });
    const keyObject = {};

    const requestStateObj = {
      req_data: JSON.stringify({
        script: 'browserstack_executor: { "action": "adbShell"}'
      }),
      clientSessionID: 'random_session_id'
    };
    const parseAndRunBstackCommand = rewiredjsExec.__get__('parseAndRunBstackCommand');


    it('parseAndRunBstackCommand success', () => {
      const original = rewiredjsExec.__get__('runBstackCommand');
      const runBstackCommand = sinon.stub();
      rewiredjsExec.__set__('runBstackCommand', runBstackCommand);
      parseAndRunBstackCommand(commandJsonString, keyObject, requestStateObj);
      expect(runBstackCommand.calledOnce).to.be.true;
      rewiredjsExec.__set__('runBstackCommand', original);
    });

    it('parseAndRunBstackCommand error', () => {
      const original = rewiredjsExec.__get__('sendError');
      const sendError = sinon.stub();
      rewiredjsExec.__set__('sendError', sendError);
      parseAndRunBstackCommand(keyObject, requestStateObj);
      expect(sendError.calledOnce).to.be.true;
      rewiredjsExec.__set__('sendError', original);
    });

  });

  context('adbShell', () => {
    const keyObject = {};
    const requestStateObj = {
      req_data: JSON.stringify({
        script: 'browserstack_executor: { "action": "adbShell"}'
      }),
      clientSessionID: 'random_session_id'
    };
    it('adbShell adbCommandExecutor', () => {
      sinon.stub(adbCommandExecutor, 'executeAdbCommand');

      const executed = jsExec.checkandExecuteIfBstackExecutor(keyObject, requestStateObj);
      executed.should.eql(true);
      sinon.assert.called(adbCommandExecutor.executeAdbCommand);

      adbCommandExecutor.executeAdbCommand.restore();
    });
  });

  context("biometric pop handle", () => {
    const keyObject = {
      rails_session_id: "random_session_id",
      appTesting: true,
      name: "host",
      wda_port: "port",
      rproxyHost: "host",
      enableBiometric: true
    };
    const parsedCommand = {
      action: 'biometric',
      arguments: {
        biometricMatch: 'pass',
      },
    };
    const requestStateObj = {
      req_data: JSON.stringify({
        script: `browserstack_executor: ${JSON.stringify(parsedCommand)}`
      }),
      clientSessionID: "random_session_id"
    };

    let origWdaClient;
    const rewiredBridge = rewiredjsExec.__get__('bridge');
    const runBiometricPopupHandler = rewiredjsExec.__get__('runBiometricPopupHandler');

    beforeEach(() => {
      origWdaClient = rewiredjsExec.__get__('WdaClient');
    });

    afterEach(() => {
      rewiredjsExec.__set__('WdaClient', origWdaClient);
    });

    it("Executes Successfully with correct keys for App-automate when user passes the prompt", async () => {
      sinon.stub(origWdaClient.prototype, "findElement", () => { });
      sinon.stub(origWdaClient.prototype, "attach", () => { });
      const originalSessionId = origWdaClient.prototype.sessionId;
      sinon.stub(origWdaClient.prototype, "clickElement", () => { });
      sinon.stub(rewiredBridge, "sendResponse");
      origWdaClient.prototype.sessionId = "random_session_id";
      let originalExecutorResponse = rewiredjsExec.__get__('instrumentAndSendExecutorResponse');
      let executorStub = sinon.stub();
      rewiredjsExec.__set__('instrumentAndSendExecutorResponse', executorStub);

      await runBiometricPopupHandler(keyObject, requestStateObj, parsedCommand);

      expect(executorStub.calledOnce).to.be.true;
      sinon.assert.calledWith(executorStub,
        'biometric_custom_executor',
        keyObject,
        requestStateObj,
      );

      rewiredBridge.sendResponse.restore();
      origWdaClient.prototype.attach.restore();
      origWdaClient.prototype.findElement.restore();
      origWdaClient.prototype.clickElement.restore();
      origWdaClient.prototype.sessionId = originalSessionId;
      rewiredjsExec.__set__('instrumentAndSendExecutorResponse', originalExecutorResponse);
    });

    it("Executes Successfully with correct keys for App-automate when user fails the prompt", async () => {
      sinon.stub(origWdaClient.prototype, "findElement", () => { });
      sinon.stub(origWdaClient.prototype, "attach", () => { });
      const originalSessionId = origWdaClient.prototype.sessionId;
      sinon.stub(origWdaClient.prototype, "clickElement", () => { });
      sinon.stub(rewiredBridge, "sendResponse");
      origWdaClient.prototype.sessionId = "random_session_id";
      let originalExecutorResponse = rewiredjsExec.__get__('instrumentAndSendExecutorResponse');
      let executorStub = sinon.stub();
      rewiredjsExec.__set__('instrumentAndSendExecutorResponse', executorStub);
      let promptCancelCommand = {
        action: 'biometric',
        arguments: {
          biometricMatch: 'fail',
        },
      };
      await runBiometricPopupHandler(keyObject, requestStateObj, promptCancelCommand);

      expect(executorStub.calledOnce).to.be.true;
      sinon.assert.calledWith(executorStub,
        'biometric_custom_executor',
        keyObject,
        requestStateObj,
      );

      rewiredBridge.sendResponse.restore();
      origWdaClient.prototype.attach.restore();
      origWdaClient.prototype.findElement.restore();
      origWdaClient.prototype.clickElement.restore();
      origWdaClient.prototype.sessionId = originalSessionId;
      rewiredjsExec.__set__('instrumentAndSendExecutorResponse', originalExecutorResponse);
    });

    it("Executes Successfully with correct keys for App-automate when user cancels the prompt", async () => {
      sinon.stub(origWdaClient.prototype, "findElement", () => { });
      sinon.stub(origWdaClient.prototype, "attach", () => { });
      const originalSessionId = origWdaClient.prototype.sessionId;
      sinon.stub(origWdaClient.prototype, "clickElement", () => { });
      sinon.stub(rewiredBridge, "sendResponse");
      origWdaClient.prototype.sessionId = "random_session_id";
      let originalExecutorResponse = rewiredjsExec.__get__('instrumentAndSendExecutorResponse');
      let executorStub = sinon.stub();
      rewiredjsExec.__set__('instrumentAndSendExecutorResponse', executorStub);
      let promptCancelCommand = {
        action: 'biometric',
        arguments: {
          biometricMatch: 'cancel',
        },
      };
      await runBiometricPopupHandler(keyObject, requestStateObj, promptCancelCommand);

      expect(executorStub.calledOnce).to.be.true;
      sinon.assert.calledWith(executorStub,
        'biometric_custom_executor',
        keyObject,
        requestStateObj,
      );

      rewiredBridge.sendResponse.restore();
      origWdaClient.prototype.attach.restore();
      origWdaClient.prototype.findElement.restore();
      origWdaClient.prototype.clickElement.restore();
      origWdaClient.prototype.sessionId = originalSessionId;
      rewiredjsExec.__set__('instrumentAndSendExecutorResponse', originalExecutorResponse);
    });

    it("Give generic error with correct action and argument for App-automate but unable to find element", async () => {
      sinon.stub(origWdaClient.prototype, "findElement", () => {
        throw new Error('unable to find element');
      });
      sinon.stub(origWdaClient.prototype, "attach", () => { });
      sinon.stub(rewiredBridge, "sendResponse");
      const originalSessionId = origWdaClient.prototype.sessionId;
      origWdaClient.prototype.sessionId = "random_session_id";
      let originalError = rewiredjsExec.__get__('instrumentAndSendError');
      let executorStub = sinon.stub();
      rewiredjsExec.__set__('instrumentAndSendError', executorStub);
      let errorObj = {
        code: 'popup_absent',
        message: "[BROWSERSTACK_COMMAND_EXECUTION_FAILED] Something went wrong while executing the command. Please retry and if the error persists, refer to our documentation or reach out to support."
      };

      await runBiometricPopupHandler(keyObject, requestStateObj, parsedCommand);

      expect(executorStub.calledOnce).to.be.true;
      sinon.assert.calledWith(executorStub,
        'biometric_custom_executor',
        keyObject,
        requestStateObj,
        errorObj
      );

      rewiredBridge.sendResponse.restore();
      origWdaClient.prototype.attach.restore();
      origWdaClient.prototype.findElement.restore();
      origWdaClient.prototype.sessionId = originalSessionId;
      rewiredjsExec.__set__('sendError', originalError);
    });

    it("Give error with correct action for Automate", async () => {
      let keyObject = {
        rails_session_id: "random_session_id",
        appTesting: false,
        name: "host",
        wda_port: "port",
        rproxyHost: "host"
      };
      let requestStateObj = {
        req_data: JSON.stringify({
          script: `browserstack_executor: ${JSON.stringify(parsedCommand)}`
        }),
        clientSessionID: "random_session_id"
      };
      let errorObj = {
        code: 'invalid_action_name',
        message: "[BROWSERSTACK_INVALID_ACTION_NAME] 'biometric' action is not supported on Automate. For details on supported actions, refer to our documentation."
      };
      sinon.stub(rewiredBridge, "sendResponse");
      const originalSessionId = origWdaClient.prototype.sessionId;
      origWdaClient.prototype.sessionId = "random_session_id";
      let originalError = rewiredjsExec.__get__('instrumentAndSendError');
      let executorStub = sinon.stub();
      rewiredjsExec.__set__('instrumentAndSendError', executorStub);

      await runBiometricPopupHandler(keyObject, requestStateObj, parsedCommand);

      expect(executorStub.calledOnce).to.be.true;
      sinon.assert.calledWith(executorStub,
        'biometric_custom_executor',
        keyObject,
        requestStateObj,
        errorObj
      );

      rewiredBridge.sendResponse.restore();
      origWdaClient.prototype.sessionId = originalSessionId;
      rewiredjsExec.__set__('sendError', originalError);
    });

    it("Give error if argument is incorrect", async () => {
      let keyObject = {
        rails_session_id: "random_session_id",
        appTesting: true,
        name: "host",
        wda_port: "port",
        rproxyHost: "host",
        enableBiometric: true
      };
      let invalidParsedCommand = {
        action: 'biometric',
        arguments: {
          biometricMatch: 'hii',
        },
      };
      let requestStateObj = {
        req_data: JSON.stringify({
          script: `browserstack_executor: ${JSON.stringify(invalidParsedCommand)}`
        }),
        clientSessionID: "random_session_id"
      };
      let errorObj = {
        code: 'invalid_argument_passed',
        message: "[BROWSERSTACK_INVALID_ARGUMENTS_PASSED] Arguments passed are invalid. For details on supported arguments, refer to our documentation or reach out to support."
      };
      sinon.stub(rewiredBridge, "sendResponse");
      const originalSessionId = origWdaClient.prototype.sessionId;
      origWdaClient.prototype.sessionId = "random_session_id";
      let originalError = rewiredjsExec.__get__('instrumentAndSendError');
      let executorStub = sinon.stub();
      rewiredjsExec.__set__('instrumentAndSendError', executorStub);

      await runBiometricPopupHandler(keyObject, requestStateObj, invalidParsedCommand);

      expect(executorStub.calledOnce).to.be.true;
      sinon.assert.calledWith(executorStub,
        "biometric_custom_executor",
        keyObject,
        requestStateObj,
        errorObj
      );

      rewiredBridge.sendResponse.restore();
      origWdaClient.prototype.sessionId = originalSessionId;
      rewiredjsExec.__set__('sendError', originalError);
    });

    it("Give error if enableBiometric capability is not set", async () => {
      let keyObject = {
        rails_session_id: "random_session_id",
        appTesting: true,
        name: "host",
        wda_port: "port",
        rproxyHost: "host",
        enableBiometric: false
      };

      let requestStateObj = {
        req_data: JSON.stringify({
          script: `browserstack_executor: ${JSON.stringify(parsedCommand)}`
        }),
        clientSessionID: "random_session_id"
      };
      let errorObj = {
        code: 'invalid_action_used',
        message: "[BROWSERSTACK_INVALID_ACTION_USED] 'To use biometric authentication in any session you have to send browserstack.enableBiometric capability as true in desired Appium capabilities. For details, refer to our documentation."
      };
      sinon.stub(rewiredBridge, "sendResponse");
      const originalSessionId = origWdaClient.prototype.sessionId;
      origWdaClient.prototype.sessionId = "random_session_id";
      let originalError = rewiredjsExec.__get__('instrumentAndSendError');
      let executorStub = sinon.stub();
      rewiredjsExec.__set__('instrumentAndSendError', executorStub);

      await runBiometricPopupHandler(keyObject, requestStateObj, parsedCommand);

      expect(executorStub.calledOnce).to.be.true;
      sinon.assert.calledWith(executorStub,
        "biometric_custom_executor",
        keyObject,
        requestStateObj,
        errorObj
      );

      rewiredBridge.sendResponse.restore();
      origWdaClient.prototype.sessionId = originalSessionId;
      rewiredjsExec.__set__('sendError', originalError);
    });

    it("Throws smart_tv_not_supported error if called for Smart TV Session", async () => {
      let keyObject = {
        rails_session_id: "random_session_id",
        appTesting: true,
        name: "host",
        wda_port: "port",
        rproxyHost: "host",
        enableBiometric: false,
        isSmartTV: true
      };

      let requestStateObj = {
        req_data: JSON.stringify({
          script: `browserstack_executor: ${JSON.stringify(parsedCommand)}`
        }),
        clientSessionID: "random_session_id"
      };
      let errorObj = {
        code: 'smart_tv_not_supported',
        message: "Error executing browserstack_executor command. [executorType] is not supported for Smart TV."
      };
      sinon.stub(rewiredBridge, "sendResponse");
      const originalSessionId = origWdaClient.prototype.sessionId;
      origWdaClient.prototype.sessionId = "random_session_id";
      let originalError = rewiredjsExec.__get__('instrumentAndSendError');
      let executorStub = sinon.stub();
      rewiredjsExec.__set__('instrumentAndSendError', executorStub);

      await runBiometricPopupHandler(keyObject, requestStateObj, parsedCommand);

      expect(executorStub.calledOnce).to.be.true;
      sinon.assert.calledWith(executorStub,
        "biometric_custom_executor",
        keyObject,
        requestStateObj,
        errorObj
      );

      rewiredBridge.sendResponse.restore();
      origWdaClient.prototype.sessionId = originalSessionId;
      rewiredjsExec.__set__('instrumentAndSendError', originalError);
    });
  });

  context('cameraImageInjectionHandler', () => {
    const keyObject = {
      rails_session_id: 'random_session_id',
      appTesting: true,
      name: 'host',
      wda_port: 'port',
      rproxyHost: 'host',
      enableCameraImageInjection: 'true',
      os: 'ios',
      bundlesForMultiAppImageInjection: ''
    };
    const parsedCommand = {
      action: 'cameraImageInjection',
      arguments: {
        imageUrl: 'media://12345',
      },
    };
    const requestStateObj = {
      req_data: JSON.stringify({
        script: `browserstack_executor: ${JSON.stringify(parsedCommand)}`
      }),
      clientSessionID: 'random_session_id'
    };

    const originalSendRequestToPlatform = rewiredjsExec.__get__('sendRequestToPlatform');
    const originalInstrumentAndSendError = rewiredjsExec.__get__('instrumentAndSendError');
    const runCameraImageInjectionHandler = rewiredjsExec.__get__('runCameraImageAndAudioInjectionHandler');

    it('Executes Successfully with correct keys for App-automate', async () => {
      const sendRequestToPlatform = sinon.stub();
      sinon.stub(browserstack, 'postBrowserStack').yields(JSON.stringify({ resolve_media: 'success', media_obj: JSON.stringify({ filename: 'abcd.jpg', s3_url: 'https://example.com' }) }));
      rewiredjsExec.__set__('sendRequestToPlatform', sendRequestToPlatform);

      await runCameraImageInjectionHandler(keyObject, requestStateObj, parsedCommand, 'camera');

      expect(sendRequestToPlatform.calledOnce).to.be.true;
      rewiredjsExec.__set__('sendRequestToPlatform', originalSendRequestToPlatform);
      browserstack.postBrowserStack.restore();
    });

    it('Executes Successfully with upper case extension for App-automate', async () => {
      const sendRequestToPlatform = sinon.stub();
      sinon.stub(browserstack, 'postBrowserStack').yields(JSON.stringify({ resolve_media: 'success', media_obj: JSON.stringify({ filename: 'abcd.JPG', s3_url: 'https://example.com' }) }));
      rewiredjsExec.__set__('sendRequestToPlatform', sendRequestToPlatform);

      await runCameraImageInjectionHandler(keyObject, requestStateObj, parsedCommand, 'camera');

      expect(sendRequestToPlatform.calledOnce).to.be.true;
      rewiredjsExec.__set__('sendRequestToPlatform', originalSendRequestToPlatform);
      browserstack.postBrowserStack.restore();
    });

    it('throws invalid action used if capability enableCameraImageInjection is not set', async () => {
      const keyObject = {
        rails_session_id: 'random_session_id',
        appTesting: true,
        name: 'host',
        wda_port: 'port',
        rproxyHost: 'host',
      };

      const instrumentAndSendError = sinon.stub();
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);

      await runCameraImageInjectionHandler(keyObject, requestStateObj, parsedCommand, 'camera');

      expect(instrumentAndSendError.calledOnce).to.be.true;
      sinon.assert.calledWith(instrumentAndSendError,
        'cameraImageInjection_custom_executor',
        keyObject,
        requestStateObj,
        constants.IMAGE_INJECTION_CUSTOM_EXECUTOR_ERRORS.invalid_action_used);

      rewiredjsExec.__set__('instrumentAndSendError', originalInstrumentAndSendError);
    });

    it('Throws smart_tv_not_supported error if called for Smart TV Session', async () => {
      const keyObject = {
        rails_session_id: 'random_session_id',
        appTesting: true,
        name: 'host',
        wda_port: 'port',
        rproxyHost: 'host',
        isSmartTV: true
      };

      const instrumentAndSendError = sinon.stub();
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);

      await runCameraImageInjectionHandler(keyObject, requestStateObj, parsedCommand, 'camera');

      expect(instrumentAndSendError.calledOnce).to.be.true;
      sinon.assert.calledWith(instrumentAndSendError,
        'cameraImageInjection_custom_executor',
        keyObject,
        requestStateObj,
        constants.JSE_GENERIC_ERRORS.smart_tv_not_supported);

      rewiredjsExec.__set__('instrumentAndSendError', originalInstrumentAndSendError);
    });

    it('throws invalid action name if automate session', async () => {
      const keyObject = {
        rails_session_id: 'random_session_id',
        name: 'host',
        wda_port: 'port',
        rproxyHost: 'host',
        enableCameraImageInjection: 'true',
        accessibleFeaturesList: "aa-ios-app-settings",
      };

      const instrumentAndSendError = sinon.stub();
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);

      await runCameraImageInjectionHandler(keyObject, requestStateObj, parsedCommand, 'camera');

      expect(instrumentAndSendError.calledOnce).to.be.true;
      sinon.assert.calledWith(instrumentAndSendError,
        'cameraImageInjection_custom_executor',
        keyObject,
        requestStateObj,
        constants.IMAGE_INJECTION_CUSTOM_EXECUTOR_ERRORS.invalid_action_name);
      rewiredjsExec.__set__('instrumentAndSendError', originalInstrumentAndSendError);
    });

    it('throws invalid arguments if arguments is not present', async () => {
      const parsedCommand = {
        action: 'cameraImageInjection'
      };

      const instrumentAndSendError = sinon.stub();
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);

      await runCameraImageInjectionHandler(keyObject, requestStateObj, parsedCommand, 'camera');

      expect(instrumentAndSendError.calledOnce).to.be.true;
      sinon.assert.calledWith(instrumentAndSendError,
        'cameraImageInjection_custom_executor',
        keyObject,
        requestStateObj,
        constants.IMAGE_INJECTION_CUSTOM_EXECUTOR_ERRORS.invalid_argument_passed);
      rewiredjsExec.__set__('instrumentAndSendError', originalInstrumentAndSendError);
    });

    it('throws invalid arguments if imageUrl tag is not present', async () => {
      const parsedCommand = {
        action: 'cameraImageInjection',
        arguments: {
        },
      };

      const instrumentAndSendError = sinon.stub();
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);

      await runCameraImageInjectionHandler(keyObject, requestStateObj, parsedCommand, 'camera');

      expect(instrumentAndSendError.calledOnce).to.be.true;
      sinon.assert.calledWith(instrumentAndSendError,
        'cameraImageInjection_custom_executor',
        keyObject,
        requestStateObj,
        constants.IMAGE_INJECTION_CUSTOM_EXECUTOR_ERRORS.invalid_argument_passed);
      rewiredjsExec.__set__('instrumentAndSendError', originalInstrumentAndSendError);
    });

    it('throws invalid arguments if imageUrl tag is not a string', async () => {
      const parsedCommand = {
        action: 'cameraImageInjection',
        arguments: {
          imageUrl: ['media://12345', 'media://45678']
        },
      };

      const instrumentAndSendError = sinon.stub();
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);

      await runCameraImageInjectionHandler(keyObject, requestStateObj, parsedCommand, 'camera');

      expect(instrumentAndSendError.calledOnce).to.be.true;
      sinon.assert.calledWith(instrumentAndSendError,
        'cameraImageInjection_custom_executor',
        keyObject,
        requestStateObj,
        constants.IMAGE_INJECTION_CUSTOM_EXECUTOR_ERRORS.invalid_argument_passed);
      rewiredjsExec.__set__('instrumentAndSendError', originalInstrumentAndSendError);
    });

    it('throws invalid image url if imageUrl does not start with media://', async () => {
      const parsedCommand = {
        action: 'cameraImageInjection',
        arguments: {
          imageUrl: '12345'
        },
      };

      const instrumentAndSendError = sinon.stub();
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);

      await runCameraImageInjectionHandler(keyObject, requestStateObj, parsedCommand, 'camera');

      expect(instrumentAndSendError.calledOnce).to.be.true;
      sinon.assert.calledWith(instrumentAndSendError,
        'cameraImageInjection_custom_executor',
        keyObject,
        requestStateObj,
        constants.IMAGE_INJECTION_CUSTOM_EXECUTOR_ERRORS.invalid_image_url);
      rewiredjsExec.__set__('instrumentAndSendError', originalInstrumentAndSendError);
    });

    it('throws invalid image url if hashed_id is not present in DB', async () => {
      const instrumentAndSendError = sinon.stub();
      sinon.stub(browserstack, 'postBrowserStack').yields(JSON.stringify({ resolve_media: 'success', media_obj: null }));
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);

      await runCameraImageInjectionHandler(keyObject, requestStateObj, parsedCommand, 'camera');

      expect(instrumentAndSendError.calledOnce).to.be.true;
      sinon.assert.calledWith(instrumentAndSendError,
        'cameraImageInjection_custom_executor',
        keyObject,
        requestStateObj,
        constants.IMAGE_INJECTION_CUSTOM_EXECUTOR_ERRORS.invalid_image_url);
      rewiredjsExec.__set__('instrumentAndSendError', originalInstrumentAndSendError);
      browserstack.postBrowserStack.restore();
    });

    it('throws invalid image type if filename extension is not jpeg or jpg or png', async () => {
      const instrumentAndSendError = sinon.stub();
      sinon.stub(browserstack, 'postBrowserStack').yields(JSON.stringify({ resolve_media: 'success', media_obj: JSON.stringify({ filename: 'abcd.gif', s3_url: 'https://example.com' }) }));
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);

      await runCameraImageInjectionHandler(keyObject, requestStateObj, parsedCommand, 'camera');

      expect(instrumentAndSendError.calledOnce).to.be.true;
      sinon.assert.calledWith(instrumentAndSendError,
        'cameraImageInjection_custom_executor',
        keyObject,
        requestStateObj,
        constants.IMAGE_INJECTION_CUSTOM_EXECUTOR_ERRORS.invalid_image_type);
      rewiredjsExec.__set__('instrumentAndSendError', originalInstrumentAndSendError);
      browserstack.postBrowserStack.restore();
    });

    it('throws unknown error if rails responds with 500', async () => {
      const instrumentAndSendError = sinon.stub();
      sinon.stub(browserstack, 'postBrowserStack').yields(JSON.stringify({ resolve_media: 'failed' }));
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);

      await runCameraImageInjectionHandler(keyObject, requestStateObj, parsedCommand, 'camera');

      expect(instrumentAndSendError.calledOnce).to.be.true;
      sinon.assert.calledWith(instrumentAndSendError,
        'cameraImageInjection_custom_executor',
        keyObject,
        requestStateObj,
        constants.IMAGE_INJECTION_CUSTOM_EXECUTOR_ERRORS.unknown);
      rewiredjsExec.__set__('instrumentAndSendError', originalInstrumentAndSendError);
      browserstack.postBrowserStack.restore();
    });
  });

  context('cameraVideoInjectionHandler', () => {
    const keyObject = {
      rails_session_id: 'random_session_id',
      appTesting: true,
      name: 'host',
      wda_port: 'port',
      rproxyHost: 'host',
      enableCameraVideoInjection: 'true',
      os: 'ios',
      bundlesForMultiAppImageInjection: ''
    };
    const parsedCommand = {
      action: 'cameraVideoInjection',
      arguments: {
        videoUrl: 'media://12345',
      },
    };
    const requestStateObj = {
      req_data: JSON.stringify({
        script: `browserstack_executor: ${JSON.stringify(parsedCommand)}`
      }),
      clientSessionID: 'random_session_id'
    };

    const originalSendRequestToPlatform = rewiredjsExec.__get__('sendRequestToPlatform');
    const originalInstrumentAndSendError = rewiredjsExec.__get__('instrumentAndSendError');
    const runCameraVideoInjectionHandler = rewiredjsExec.__get__('runCameraImageAndAudioInjectionHandler');

    it('Executes Successfully with correct keys for App-automate', async () => {
      const sendRequestToPlatform = sinon.stub();
      sinon.stub(browserstack, 'postBrowserStack').yields(JSON.stringify({ resolve_media: 'success', media_obj: JSON.stringify({ filename: 'abcd.mp4', s3_url: 'https://example.com' }) }));
      rewiredjsExec.__set__('sendRequestToPlatform', sendRequestToPlatform);

      await runCameraVideoInjectionHandler(keyObject, requestStateObj, parsedCommand, 'video');

      expect(sendRequestToPlatform.calledOnce).to.be.true;
      rewiredjsExec.__set__('sendRequestToPlatform', originalSendRequestToPlatform);
      browserstack.postBrowserStack.restore();
    });

    it('Executes Successfully with upper case extension for App-automate', async () => {
      const sendRequestToPlatform = sinon.stub();
      sinon.stub(browserstack, 'postBrowserStack').yields(JSON.stringify({ resolve_media: 'success', media_obj: JSON.stringify({ filename: 'abcd.mp4', s3_url: 'https://example.com' }) }));
      rewiredjsExec.__set__('sendRequestToPlatform', sendRequestToPlatform);

      await runCameraVideoInjectionHandler(keyObject, requestStateObj, parsedCommand, 'video');

      expect(sendRequestToPlatform.calledOnce).to.be.true;
      rewiredjsExec.__set__('sendRequestToPlatform', originalSendRequestToPlatform);
      browserstack.postBrowserStack.restore();
    });

    it('throws invalid action used if capability enableCameraVideoInjection is not set', async () => {
      const keyObject = {
        rails_session_id: 'random_session_id',
        appTesting: true,
        name: 'host',
        wda_port: 'port',
        rproxyHost: 'host',
      };

      const instrumentAndSendError = sinon.stub();
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);

      await runCameraVideoInjectionHandler(keyObject, requestStateObj, parsedCommand, 'video');

      expect(instrumentAndSendError.calledOnce).to.be.true;
      sinon.assert.calledWith(instrumentAndSendError,
        'injectVideo',
        keyObject,
        requestStateObj,
        constants.VIDEO_INJECTION_CUSTOM_EXECUTOR_ERRORS.invalid_action_used);

      rewiredjsExec.__set__('instrumentAndSendError', originalInstrumentAndSendError);
    });

    it('Throws smart_tv_not_supported error if called for Smart TV Session', async () => {
      const keyObject = {
        rails_session_id: 'random_session_id',
        appTesting: true,
        name: 'host',
        wda_port: 'port',
        rproxyHost: 'host',
        isSmartTV: true
      };

      const instrumentAndSendError = sinon.stub();
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);

      await runCameraVideoInjectionHandler(keyObject, requestStateObj, parsedCommand, 'video');

      expect(instrumentAndSendError.calledOnce).to.be.true;
      sinon.assert.calledWith(instrumentAndSendError,
        'injectVideo',
        keyObject,
        requestStateObj,
        constants.JSE_GENERIC_ERRORS.smart_tv_not_supported);

      rewiredjsExec.__set__('instrumentAndSendError', originalInstrumentAndSendError);
    });

    it('throws invalid action name if automate session', async () => {
      const keyObject = {
        rails_session_id: 'random_session_id',
        name: 'host',
        wda_port: 'port',
        rproxyHost: 'host',
        enableCameraVideoInjection: 'true',
        accessibleFeaturesList: "aa-ios-app-settings",
      };

      const instrumentAndSendError = sinon.stub();
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);

      await runCameraVideoInjectionHandler(keyObject, requestStateObj, parsedCommand, 'video');

      expect(instrumentAndSendError.calledOnce).to.be.true;
      sinon.assert.calledWith(instrumentAndSendError,
        'injectVideo',
        keyObject,
        requestStateObj,
        constants.VIDEO_INJECTION_CUSTOM_EXECUTOR_ERRORS.invalid_action_name);
      rewiredjsExec.__set__('instrumentAndSendError', originalInstrumentAndSendError);
    });

    it('throws invalid arguments if arguments is not present', async () => {
      const parsedCommand = {
        action: 'cameraVideoInjection'
      };

      const instrumentAndSendError = sinon.stub();
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);

      await runCameraVideoInjectionHandler(keyObject, requestStateObj, parsedCommand, 'video');

      expect(instrumentAndSendError.calledOnce).to.be.true;
      sinon.assert.calledWith(instrumentAndSendError,
        'injectVideo',
        keyObject,
        requestStateObj,
        constants.VIDEO_INJECTION_CUSTOM_EXECUTOR_ERRORS.invalid_argument_passed);
      rewiredjsExec.__set__('instrumentAndSendError', originalInstrumentAndSendError);
    });

    it('throws invalid arguments if videoUrl tag is not present', async () => {
      const parsedCommand = {
        action: 'cameraVideoInjection',
        arguments: {
        },
      };

      const instrumentAndSendError = sinon.stub();
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);

      await runCameraVideoInjectionHandler(keyObject, requestStateObj, parsedCommand, 'video');

      expect(instrumentAndSendError.calledOnce).to.be.true;
      sinon.assert.calledWith(instrumentAndSendError,
        'injectVideo',
        keyObject,
        requestStateObj,
        constants.VIDEO_INJECTION_CUSTOM_EXECUTOR_ERRORS.invalid_argument_passed);
      rewiredjsExec.__set__('instrumentAndSendError', originalInstrumentAndSendError);
    });

    it('throws invalid arguments if videoUrl tag is not a string', async () => {
      const parsedCommand = {
        action: 'cameraVideoInjection',
        arguments: {
          videoUrl: ['media://12345', 'media://45678']
        },
      };

      const instrumentAndSendError = sinon.stub();
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);

      await runCameraVideoInjectionHandler(keyObject, requestStateObj, parsedCommand, 'video');

      expect(instrumentAndSendError.calledOnce).to.be.true;
      sinon.assert.calledWith(instrumentAndSendError,
        'injectVideo',
        keyObject,
        requestStateObj,
        constants.VIDEO_INJECTION_CUSTOM_EXECUTOR_ERRORS.invalid_argument_passed);
      rewiredjsExec.__set__('instrumentAndSendError', originalInstrumentAndSendError);
    });

    it('throws invalid video url if videoUrl does not start with media://', async () => {
      const parsedCommand = {
        action: 'cameraVideoInjection',
        arguments: {
          videoUrl: '12345'
        },
      };

      const instrumentAndSendError = sinon.stub();
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);

      await runCameraVideoInjectionHandler(keyObject, requestStateObj, parsedCommand, 'video');

      expect(instrumentAndSendError.calledOnce).to.be.true;
      sinon.assert.calledWith(instrumentAndSendError,
        'injectVideo',
        keyObject,
        requestStateObj,
        constants.VIDEO_INJECTION_CUSTOM_EXECUTOR_ERRORS.invalid_media_url);
      rewiredjsExec.__set__('instrumentAndSendError', originalInstrumentAndSendError);
    });

    it('throws invalid video url if hashed_id is not present in DB', async () => {
      const instrumentAndSendError = sinon.stub();
      sinon.stub(browserstack, 'postBrowserStack').yields(JSON.stringify({ resolve_media: 'success', media_obj: null }));
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);

      await runCameraVideoInjectionHandler(keyObject, requestStateObj, parsedCommand, 'video');

      expect(instrumentAndSendError.calledOnce).to.be.true;
      sinon.assert.calledWith(instrumentAndSendError,
        'injectVideo',
        keyObject,
        requestStateObj,
        constants.VIDEO_INJECTION_CUSTOM_EXECUTOR_ERRORS.invalid_media_url);
      rewiredjsExec.__set__('instrumentAndSendError', originalInstrumentAndSendError);
      browserstack.postBrowserStack.restore();
    });

    it('throws invalid image type if filename extension is not mp4', async () => {
      const instrumentAndSendError = sinon.stub();
      sinon.stub(browserstack, 'postBrowserStack').yields(JSON.stringify({ resolve_media: 'success', media_obj: JSON.stringify({ filename: 'abcd.gif', s3_url: 'https://example.com' }) }));
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);

      await runCameraVideoInjectionHandler(keyObject, requestStateObj, parsedCommand, 'video');

      expect(instrumentAndSendError.calledOnce).to.be.true;
      sinon.assert.calledWith(instrumentAndSendError,
        'injectVideo',
        keyObject,
        requestStateObj,
        constants.VIDEO_INJECTION_CUSTOM_EXECUTOR_ERRORS.invalid_image_type);
      rewiredjsExec.__set__('instrumentAndSendError', originalInstrumentAndSendError);
      browserstack.postBrowserStack.restore();
    });

    it('throws unknown error if rails responds with 500', async () => {
      const instrumentAndSendError = sinon.stub();
      sinon.stub(browserstack, 'postBrowserStack').yields(JSON.stringify({ resolve_media: 'failed' }));
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);

      await runCameraVideoInjectionHandler(keyObject, requestStateObj, parsedCommand, 'video');

      expect(instrumentAndSendError.calledOnce).to.be.true;
      sinon.assert.calledWith(instrumentAndSendError,
        'injectVideo',
        keyObject,
        requestStateObj,
        constants.VIDEO_INJECTION_CUSTOM_EXECUTOR_ERRORS.unknown);
      rewiredjsExec.__set__('instrumentAndSendError', originalInstrumentAndSendError);
      browserstack.postBrowserStack.restore();
    });
  });


  context('audioInjection play audio', () => {
    const keyObject = {
      rails_session_id: 'random_session_id',
      appTesting: true,
      name: 'host',
      wda_port: 'port',
      rproxyHost: 'host',
      enableAudioInjection: 'true',
      os: 'android'
    };
    const parsedCommand = {
      action: 'startAudio',
    };
    const requestStateObj = {
      req_data: JSON.stringify({
        script: `browserstack_executor: ${JSON.stringify(parsedCommand)}`
      }),
      clientSessionID: 'random_session_id_1'
    };
    const originalSendRequestToPlatform = rewiredjsExec.__get__('sendRequestToPlatform');
    const originalInstrumentAndSendError = rewiredjsExec.__get__('instrumentAndSendError');
    const originalInstrumentAndSendExecutorResponse = rewiredjsExec.__get__('instrumentAndSendExecutorResponse');

    const runStartAudioHandler = rewiredjsExec.__get__('runControlAudioHandler');

    it('Executes Successfully with correct keys for App-automate', async () => {
      const sendRequestToPlatform = sinon.stub();
      rewiredjsExec.__set__('sendRequestToPlatform', sendRequestToPlatform);
      await runStartAudioHandler(keyObject, requestStateObj, 'start');

      expect(sendRequestToPlatform.calledOnce).to.be.true;
      rewiredjsExec.__set__('sendRequestToPlatform', originalSendRequestToPlatform);
    });

    it('Executes Successfully with correct keys for automate', async () => {
      const keyObjectWithoutAppTestFlag = {
        rails_session_id: 'random_session_id',
        name: 'host',
        wda_port: 'port',
        rproxyHost: 'host',
        enableAudioInjection: 'true',
        os: 'android'
      };
      const sendRequestToPlatform = sinon.stub();
      rewiredjsExec.__set__('sendRequestToPlatform', sendRequestToPlatform);
      await runStartAudioHandler(keyObjectWithoutAppTestFlag, requestStateObj, 'start');

      expect(sendRequestToPlatform.calledOnce).to.be.true;
      rewiredjsExec.__set__('sendRequestToPlatform', originalSendRequestToPlatform);
    });

    it('response handler callback function gives 200', async () => {
      const instrumentAndSendExecutorResponse = sinon.stub();

      rewiredjsExec.__set__('instrumentAndSendExecutorResponse', instrumentAndSendExecutorResponse);

      rewiredjsExec.__set__('sendRequestToPlatform', (executorType, serverURL, reqObj, key, error, customTimeout, customOptions, responseHandler) => {

        responseHandler({
          statusCode: 200
        });
      });
      await runStartAudioHandler(keyObject, requestStateObj, 'start');

      sinon.assert.calledWith(instrumentAndSendExecutorResponse,
        'startAudio',
        keyObject,
        requestStateObj);
      rewiredjsExec.__set__('sendRequestToPlatform', originalSendRequestToPlatform);
      rewiredjsExec.__set__('instrumentAndSendExecutorResponse', originalInstrumentAndSendExecutorResponse);
    });

    it('response handler callback function gives AL004', async () => {
      const instrumentAndSendError = sinon.stub();

      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);

      const data = {
        code: 'AL004',
      };
      rewiredjsExec.__set__('sendRequestToPlatform', (executorType, serverURL, reqObj, key, error, customTimeout, customOptions, responseHandler) => {
        responseHandler({
          statusCode: 400,
          data: JSON.stringify(data)
        });
      });
      await runStartAudioHandler(keyObject, requestStateObj, 'start');

      sinon.assert.calledWith(instrumentAndSendError, 'startAudio', keyObject, requestStateObj, constants.AUDIO_INJECTION_CUSTOM_EXECUTOR_ERRORS.audio_start_custom_failure);
      rewiredjsExec.__set__('sendRequestToPlatform', originalSendRequestToPlatform);
      rewiredjsExec.__set__('instrumentAndSendError', originalInstrumentAndSendError);
    });

    it('response handler callback function gives body parsing error', async () => {
      const instrumentAndSendError = sinon.stub();

      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);

      rewiredjsExec.__set__('sendRequestToPlatform', (executorType, serverURL, reqObj, key, error, customTimeout, customOptions, responseHandler) => {
        responseHandler({
          statusCode: 400,
          data: {
            code: 'AL004',
          }
        });
      });
      await runStartAudioHandler(keyObject, requestStateObj, 'start');

      sinon.assert.calledWith(instrumentAndSendError, 'startAudio', keyObject, requestStateObj, constants.AUDIO_INJECTION_CUSTOM_EXECUTOR_ERRORS.audio_start_failure);
      rewiredjsExec.__set__('sendRequestToPlatform', originalSendRequestToPlatform);
      rewiredjsExec.__set__('instrumentAndSendError', originalInstrumentAndSendError);
    });


    it('response handler empty body', async () => {
      const instrumentAndSendError = sinon.stub();

      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);

      rewiredjsExec.__set__('sendRequestToPlatform', (executorType, serverURL, reqObj, key, error, customTimeout, customOptions, responseHandler) => {
        responseHandler();
      });
      await runStartAudioHandler(keyObject, requestStateObj, 'start');

      sinon.assert.calledWith(instrumentAndSendError, 'startAudio', keyObject, requestStateObj, constants.AUDIO_INJECTION_CUSTOM_EXECUTOR_ERRORS.audio_start_failure);
      rewiredjsExec.__set__('sendRequestToPlatform', originalSendRequestToPlatform);
      rewiredjsExec.__set__('instrumentAndSendError', originalInstrumentAndSendError);
    });

    it('throws invalid action used if capability enableAudioInjection is not set', async () => {
      const keyObjectWithoutAIFlag = {
        rails_session_id: 'random_session_id',
        appTesting: true,
        name: 'host',
        wda_port: 'port',
        rproxyHost: 'host',
      };

      const instrumentAndSendError = sinon.stub();

      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);

      await runStartAudioHandler(keyObjectWithoutAIFlag, requestStateObj, 'start');

      expect(instrumentAndSendError.calledOnce).to.be.true;
      sinon.assert.calledWith(instrumentAndSendError,
        'startAudio',
        keyObjectWithoutAIFlag,
        requestStateObj,
        constants.AUDIO_INJECTION_CUSTOM_EXECUTOR_ERRORS.invalid_action_used);

      rewiredjsExec.__set__('instrumentAndSendError', originalInstrumentAndSendError);
      rewiredjsExec.__set__('sendRequestToPlatform', originalSendRequestToPlatform);

    });

    it('Throws smart_tv_not_supported error if called for Smart TV Session', async () => {
      const keyObjectWithoutAIFlag = {
        rails_session_id: 'random_session_id',
        appTesting: true,
        name: 'host',
        wda_port: 'port',
        rproxyHost: 'host',
        isSmartTV: true
      };

      const instrumentAndSendError = sinon.stub();

      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);

      await runStartAudioHandler(keyObjectWithoutAIFlag, requestStateObj, 'start');

      expect(instrumentAndSendError.calledOnce).to.be.true;
      sinon.assert.calledWith(instrumentAndSendError,
        'startAudio',
        keyObjectWithoutAIFlag,
        requestStateObj,
        constants.JSE_GENERIC_ERRORS.smart_tv_not_supported);

      rewiredjsExec.__set__('instrumentAndSendError', originalInstrumentAndSendError);
      rewiredjsExec.__set__('sendRequestToPlatform', originalSendRequestToPlatform);

    });

    it('throws platform not supported if os is not Android', async () => {
      const keyObjectWithoutOSFlag = {
        rails_session_id: 'random_session_id',
        appTesting: true,
        name: 'host',
        wda_port: 'port',
        rproxyHost: 'host',
        enableAudioInjection: 'true'
      };
      const instrumentAndSendError = sinon.stub();

      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);

      rewiredjsExec.__set__('sendRequestToPlatform', (executorType, serverURL, reqObj, key, error, customTimeout, customOptions, responseHandler) => {
        responseHandler();
      });
      await runStartAudioHandler(keyObjectWithoutOSFlag, requestStateObj, 'start');

      sinon.assert.calledWith(instrumentAndSendError, 'startAudio', keyObjectWithoutOSFlag, requestStateObj, constants.AUDIO_INJECTION_CUSTOM_EXECUTOR_ERRORS.platform_not_supported);
      rewiredjsExec.__set__('sendRequestToPlatform', originalSendRequestToPlatform);
      rewiredjsExec.__set__('instrumentAndSendError', originalInstrumentAndSendError);
    });

    it('throws unknown error if exception is raised', async () => {
      const instrumentAndSendError = sinon.stub();

      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);

      rewiredjsExec.__set__('sendRequestToPlatform', (executorType, serverURL, reqObj, key, error, customTimeout, customOptions, responseHandler) => {
        throw new Error('Something went wrong');
      });
      await runStartAudioHandler(keyObject, requestStateObj, 'start');

      sinon.assert.calledWith(instrumentAndSendError, 'startAudio', keyObject, requestStateObj, constants.AUDIO_INJECTION_CUSTOM_EXECUTOR_ERRORS.unknown);
      rewiredjsExec.__set__('sendRequestToPlatform', originalSendRequestToPlatform);
      rewiredjsExec.__set__('instrumentAndSendError', originalInstrumentAndSendError);
    });
  });


  context('audioInjection stop audio', () => {
    const keyObject = {
      rails_session_id: 'random_session_id',
      appTesting: true,
      name: 'host',
      wda_port: 'port',
      rproxyHost: 'host',
      enableAudioInjection: 'true',
      os: 'android'
    };
    const parsedCommand = {
      action: 'stopAudio',
    };
    const requestStateObj = {
      req_data: JSON.stringify({
        script: `browserstack_executor: ${JSON.stringify(parsedCommand)}`
      }),
      clientSessionID: 'random_session_id_1'
    };
    const originalSendRequestToPlatform = rewiredjsExec.__get__('sendRequestToPlatform');
    const originalInstrumentAndSendError = rewiredjsExec.__get__('instrumentAndSendError');
    const runStopAudioHandler = rewiredjsExec.__get__('runControlAudioHandler');
    it('Executes Successfully with correct keys for App-automate', async () => {
      const sendRequestToPlatform = sinon.stub();
      rewiredjsExec.__set__('sendRequestToPlatform', sendRequestToPlatform);
      await runStopAudioHandler(keyObject, requestStateObj, parsedCommand);

      expect(sendRequestToPlatform.calledOnce).to.be.true;
      rewiredjsExec.__set__('sendRequestToPlatform', originalSendRequestToPlatform);
    });
    it('Executes Successfully with correct keys for automate', async () => {
      const keyObjectWithoutAppTestFlag = {
        rails_session_id: 'random_session_id',
        name: 'host',
        wda_port: 'port',
        rproxyHost: 'host',
        enableAudioInjection: 'true',
        os: 'android'
      };
      const sendRequestToPlatform = sinon.stub();
      rewiredjsExec.__set__('sendRequestToPlatform', sendRequestToPlatform);
      await runStopAudioHandler(keyObjectWithoutAppTestFlag, requestStateObj, parsedCommand);

      expect(sendRequestToPlatform.calledOnce).to.be.true;
      rewiredjsExec.__set__('sendRequestToPlatform', originalSendRequestToPlatform);
    });

    it('response handler callback function gives AL005', async () => {

      const instrumentAndSendError = sinon.stub();

      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);

      const data = {
        code: 'AL005',
      };
      rewiredjsExec.__set__('sendRequestToPlatform', (executorType, serverURL, reqObj, key, error, customTimeout, customOptions, responseHandler) => {
        responseHandler({
          statusCode: 400,
          data: JSON.stringify(data)
        });
      });
      await runStopAudioHandler(keyObject, requestStateObj, 'stop');

      sinon.assert.calledWith(instrumentAndSendError, 'stopAudio', keyObject, requestStateObj, constants.AUDIO_INJECTION_CUSTOM_EXECUTOR_ERRORS.audio_stop_custom_failure);
      rewiredjsExec.__set__('sendRequestToPlatform', originalSendRequestToPlatform);
      rewiredjsExec.__set__('instrumentAndSendError', originalInstrumentAndSendError);
    });

    it('throws invalid action used if capability enableAudioInjection is not set', async () => {
      const keyObjectWithoutAIFlag = {
        rails_session_id: 'random_session_id',
        appTesting: true,
        name: 'host',
        wda_port: 'port',
        rproxyHost: 'host',
      };


      const instrumentAndSendError = sinon.stub();
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);

      await runStopAudioHandler(keyObjectWithoutAIFlag, requestStateObj, parsedCommand);

      expect(instrumentAndSendError.calledOnce).to.be.true;
      sinon.assert.calledWith(instrumentAndSendError,
        'stopAudio',
        keyObjectWithoutAIFlag,
        requestStateObj,
        constants.AUDIO_INJECTION_CUSTOM_EXECUTOR_ERRORS.invalid_action_used);

      rewiredjsExec.__set__('instrumentAndSendError', originalInstrumentAndSendError);
    });

    it('Throws smart_tv_not_supported error if called for tvos Session', async () => {
      const keyObjectWithoutAIFlag = {
        rails_session_id: 'random_session_id',
        appTesting: true,
        name: 'host',
        wda_port: 'port',
        rproxyHost: 'host',
        isSmartTV: true
      };


      const instrumentAndSendError = sinon.stub();
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);

      await runStopAudioHandler(keyObjectWithoutAIFlag, requestStateObj, parsedCommand);

      expect(instrumentAndSendError.calledOnce).to.be.true;
      sinon.assert.calledWith(instrumentAndSendError,
        'stopAudio',
        keyObjectWithoutAIFlag,
        requestStateObj,
        constants.JSE_GENERIC_ERRORS.smart_tv_not_supported);

      rewiredjsExec.__set__('instrumentAndSendError', originalInstrumentAndSendError);
    });

    it('throws platform not supported if os is not Android', async () => {
      const keyObjectWithoutOSFlag = {
        rails_session_id: 'random_session_id',
        appTesting: true,
        name: 'host',
        wda_port: 'port',
        rproxyHost: 'host',
        enableAudioInjection: true
      };

      const instrumentAndSendError = sinon.stub();
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);

      await runStopAudioHandler(keyObjectWithoutOSFlag, requestStateObj, parsedCommand);

      expect(instrumentAndSendError.calledOnce).to.be.true;
      sinon.assert.calledWith(instrumentAndSendError,
        'stopAudio',
        keyObjectWithoutOSFlag,
        requestStateObj,
        constants.AUDIO_INJECTION_CUSTOM_EXECUTOR_ERRORS.platform_not_supported);

      rewiredjsExec.__set__('instrumentAndSendError', originalInstrumentAndSendError);
    });

    it('throws unknown error if exception is raised', async () => {
      const instrumentAndSendError = sinon.stub();

      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);

      rewiredjsExec.__set__('sendRequestToPlatform', (executorType, serverURL, reqObj, key, error, customTimeout, customOptions, responseHandler) => {
        throw new Error('Something went wrong');
      });
      await runStopAudioHandler(keyObject, requestStateObj, 'stop');

      sinon.assert.calledWith(instrumentAndSendError, 'stopAudio', keyObject, requestStateObj, constants.AUDIO_INJECTION_CUSTOM_EXECUTOR_ERRORS.unknown);
      rewiredjsExec.__set__('sendRequestToPlatform', originalSendRequestToPlatform);
      rewiredjsExec.__set__('instrumentAndSendError', originalInstrumentAndSendError);
    });
  });

  context('audioInjectionHandler', () => {

    const keyObject = {
      rails_session_id: 'random_session_id',
      appTesting: true,
      name: 'host',
      wda_port: 'port',
      rproxyHost: 'host',
      enableAudioInjection: 'true',
      os: 'android',
      bundlesForMultiAppImageInjection: ''
    };
    const parsedCommand = {
      action: 'injectAudio',
      arguments: {
        audioUrl: 'media://12345',
      },
    };
    const requestStateObj = {
      req_data: JSON.stringify({
        script: `browserstack_executor: ${JSON.stringify(parsedCommand)}`
      }),
      clientSessionID: 'random_session_id_1'
    };

    const originalSendRequestToPlatform = rewiredjsExec.__get__('sendRequestToPlatform');
    const originalInstrumentAndSendError = rewiredjsExec.__get__('instrumentAndSendError');
    const runAudioInjectionHandler = rewiredjsExec.__get__('runCameraImageAndAudioInjectionHandler');

    // eslint-disable-next-line no-undef
    it('Executes Successfully with correct keys for App-automate', async () => {
      const sendRequestToPlatform = sinon.stub();
      sinon.stub(browserstack, 'postBrowserStack').yields(JSON.stringify({ resolve_media: 'success', media_obj: JSON.stringify({ filename: 'abcd.mp3', s3_url: 'https://example.com' }) }));
      rewiredjsExec.__set__('sendRequestToPlatform', sendRequestToPlatform);

      await runAudioInjectionHandler(keyObject, requestStateObj, parsedCommand, 'audio');

      expect(sendRequestToPlatform.calledOnce).to.be.true;
      rewiredjsExec.__set__('sendRequestToPlatform', originalSendRequestToPlatform);
      browserstack.postBrowserStack.restore();
    });

    it('Executes Successfully with upper case extension for App-automate', async () => {
      const sendRequestToPlatform = sinon.stub();
      sinon.stub(browserstack, 'postBrowserStack').yields(JSON.stringify({ resolve_media: 'success', media_obj: JSON.stringify({ filename: 'abcd.MP3', s3_url: 'https://example.com' }) }));
      rewiredjsExec.__set__('sendRequestToPlatform', sendRequestToPlatform);

      await runAudioInjectionHandler(keyObject, requestStateObj, parsedCommand, 'audio');

      expect(sendRequestToPlatform.calledOnce).to.be.true;
      rewiredjsExec.__set__('sendRequestToPlatform', originalSendRequestToPlatform);
      browserstack.postBrowserStack.restore();
    });

    it('Executes Successfully with correct keys for automate', async () => {
      const keyObjectWithoutAppTest = {
        rails_session_id: 'random_session_id',
        name: 'host',
        wda_port: 'port',
        rproxyHost: 'host',
        enableAudioInjection: 'true',
        os: 'android'
      };

      const sendRequestToPlatform = sinon.stub();
      sinon.stub(browserstack, 'postBrowserStack').yields(JSON.stringify({ resolve_media: 'success', media_obj: JSON.stringify({ filename: 'abcd.mp3', s3_url: 'https://example.com' }) }));
      rewiredjsExec.__set__('sendRequestToPlatform', sendRequestToPlatform);

      await runAudioInjectionHandler(keyObjectWithoutAppTest, requestStateObj, parsedCommand, 'audio');

      expect(sendRequestToPlatform.calledOnce).to.be.true;
      rewiredjsExec.__set__('sendRequestToPlatform', originalSendRequestToPlatform);
      browserstack.postBrowserStack.restore();
    });

    it('throws invalid action used if capability enableAudioInjection is not set', async () => {
      const keyObjectWithoutAIFlag = {
        rails_session_id: 'random_session_id',
        appTesting: true,
        name: 'host',
        wda_port: 'port',
        rproxyHost: 'host',
      };

      const instrumentAndSendError = sinon.stub();
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);

      await runAudioInjectionHandler(keyObjectWithoutAIFlag, requestStateObj, parsedCommand, 'audio');

      expect(instrumentAndSendError.calledOnce).to.be.true;
      sinon.assert.calledWith(instrumentAndSendError,
        'injectAudio',
        keyObjectWithoutAIFlag,
        requestStateObj,
        constants.AUDIO_INJECTION_CUSTOM_EXECUTOR_ERRORS.invalid_action_used);

      rewiredjsExec.__set__('instrumentAndSendError', originalInstrumentAndSendError);
    });

    it('throws platform not supported if os is not Android', async () => {
      const keyObjectWithoutOSFlag = {
        rails_session_id: 'random_session_id',
        appTesting: true,
        name: 'host',
        wda_port: 'port',
        rproxyHost: 'host',
        enableAudioInjection: 'true'
      };

      const instrumentAndSendError = sinon.stub();
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);

      await runAudioInjectionHandler(keyObjectWithoutOSFlag, requestStateObj, parsedCommand, 'audio');

      expect(instrumentAndSendError.calledOnce).to.be.true;
      sinon.assert.calledWith(instrumentAndSendError,
        'injectAudio',
        keyObjectWithoutOSFlag,
        requestStateObj,
        constants.AUDIO_INJECTION_CUSTOM_EXECUTOR_ERRORS.platform_not_supported);

      rewiredjsExec.__set__('instrumentAndSendError', originalInstrumentAndSendError);
    })

    it('throws invalid arguments if arguments is not present', async () => {
      const keyObject = {
        rails_session_id: 'random_session_id',
        appTesting: true,
        name: 'host',
        wda_port: 'port',
        rproxyHost: 'host',
        enableAudioInjection: 'true',
        os: 'android'
      };
      const parsedCommand = {
        action: 'audioInjection'
      };

      const instrumentAndSendError = sinon.stub();
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);

      await runAudioInjectionHandler(keyObject, requestStateObj, parsedCommand, 'audio');

      expect(instrumentAndSendError.calledOnce).to.be.true;
      sinon.assert.calledWith(instrumentAndSendError,
        'injectAudio',
        keyObject,
        requestStateObj,
        constants.AUDIO_INJECTION_CUSTOM_EXECUTOR_ERRORS.invalid_argument_passed);
      rewiredjsExec.__set__('instrumentAndSendError', originalInstrumentAndSendError);
    });

    it('throws invalid arguments if audioUrl tag is not present', async () => {
      const keyObject = {
        rails_session_id: 'random_session_id',
        appTesting: true,
        name: 'host',
        wda_port: 'port',
        rproxyHost: 'host',
        enableAudioInjection: 'true',
        os: 'android'
      };
      const parsedCommand = {
        action: 'audioInjection',
        arguments: {
        },
      };

      const instrumentAndSendError = sinon.stub();
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);

      await runAudioInjectionHandler(keyObject, requestStateObj, parsedCommand, 'audio');

      expect(instrumentAndSendError.calledOnce).to.be.true;
      sinon.assert.calledWith(instrumentAndSendError,
        'injectAudio',
        keyObject,
        requestStateObj,
        constants.AUDIO_INJECTION_CUSTOM_EXECUTOR_ERRORS.invalid_argument_passed);
      rewiredjsExec.__set__('instrumentAndSendError', originalInstrumentAndSendError);
    });

    it('throws invalid arguments if audioUrl tag is not a string', async () => {
      const parsedCommand = {
        action: 'audioInjection',
        arguments: {
          audioUrl: ['media://12345', 'media://45678']
        },
      };

      const instrumentAndSendError = sinon.stub();
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);

      await runAudioInjectionHandler(keyObject, requestStateObj, parsedCommand, 'audio');

      expect(instrumentAndSendError.calledOnce).to.be.true;
      sinon.assert.calledWith(instrumentAndSendError,
        'injectAudio',
        keyObject,
        requestStateObj,
        constants.AUDIO_INJECTION_CUSTOM_EXECUTOR_ERRORS.invalid_argument_passed);
      rewiredjsExec.__set__('instrumentAndSendError', originalInstrumentAndSendError);
    });

    it('throws invalid AudioUrl url if audioUrl does not start with media://', async () => {
      const parsedCommand = {
        action: 'audioInjection',
        arguments: {
          audioUrl: '12345'
        },
      };

      const instrumentAndSendError = sinon.stub();
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);

      await runAudioInjectionHandler(keyObject, requestStateObj, parsedCommand, 'audio');

      expect(instrumentAndSendError.calledOnce).to.be.true;
      sinon.assert.calledWith(instrumentAndSendError,
        'injectAudio',
        keyObject,
        requestStateObj,
        constants.AUDIO_INJECTION_CUSTOM_EXECUTOR_ERRORS.invalid_media_url);
      rewiredjsExec.__set__('instrumentAndSendError', originalInstrumentAndSendError);
    });

    it('throws invalid Audio url if hashed_id is not present in DB', async () => {
      const instrumentAndSendError = sinon.stub();
      sinon.stub(browserstack, 'postBrowserStack').yields(JSON.stringify({ resolve_media: 'success', media_obj: null }));
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);

      await runAudioInjectionHandler(keyObject, requestStateObj, parsedCommand, 'audio');

      expect(instrumentAndSendError.calledOnce).to.be.true;
      sinon.assert.calledWith(instrumentAndSendError,
        'injectAudio',
        keyObject,
        requestStateObj,
        constants.AUDIO_INJECTION_CUSTOM_EXECUTOR_ERRORS.invalid_media_url);
      rewiredjsExec.__set__('instrumentAndSendError', originalInstrumentAndSendError);
      browserstack.postBrowserStack.restore();
    });

    it('throws invalid audio type if filename extension is not mp3 or wav (in app automate)', async () => {
      const keyObject = {
        rails_session_id: 'random_session_id',
        appTesting: true,
        name: 'host',
        wda_port: 'port',
        rproxyHost: 'host',
        enableAudioInjection: 'true',
        os: 'android'
      };
      const instrumentAndSendError = sinon.stub();
      sinon.stub(browserstack, 'postBrowserStack').yields(JSON.stringify({ resolve_media: 'success', media_obj: JSON.stringify({ filename: 'abcd.gif', s3_url: 'https://example.com' }) }));
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);

      await runAudioInjectionHandler(keyObject, requestStateObj, parsedCommand, 'audio');

      expect(instrumentAndSendError.calledOnce).to.be.true;
      sinon.assert.calledWith(instrumentAndSendError,
        'injectAudio',
        keyObject,
        requestStateObj,
        constants.AUDIO_INJECTION_CUSTOM_EXECUTOR_ERRORS.invalid_media_type_aa);
      rewiredjsExec.__set__('instrumentAndSendError', originalInstrumentAndSendError);
      browserstack.postBrowserStack.restore();
    });

    it('throws invalid audio type if filename extension is not mp3 or wav (in automate)', async () => {
      const keyObject = {
        rails_session_id: 'random_session_id',
        name: 'host',
        wda_port: 'port',
        rproxyHost: 'host',
        enableAudioInjection: 'true',
        os: 'android'
      };
      const instrumentAndSendError = sinon.stub();
      sinon.stub(browserstack, 'postBrowserStack').yields(JSON.stringify({ resolve_media: 'success', media_obj: JSON.stringify({ filename: 'abcd.gif', s3_url: 'https://example.com' }) }));
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);

      await runAudioInjectionHandler(keyObject, requestStateObj, parsedCommand, 'audio');

      expect(instrumentAndSendError.calledOnce).to.be.true;
      sinon.assert.calledWith(instrumentAndSendError,
        'injectAudio',
        keyObject,
        requestStateObj,
        constants.AUDIO_INJECTION_CUSTOM_EXECUTOR_ERRORS.invalid_media_type_aut);
      rewiredjsExec.__set__('instrumentAndSendError', originalInstrumentAndSendError);
      browserstack.postBrowserStack.restore();
    });

    it('throws unknown error if exception is raised', async () => {
      const instrumentAndSendError = sinon.stub();
      sinon.stub(browserstack, 'postBrowserStack').throws(new Error('Something went wrong'));
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);

      await runAudioInjectionHandler(keyObject, requestStateObj, parsedCommand, 'audio');

      expect(instrumentAndSendError.calledOnce).to.be.true;
      sinon.assert.calledWith(instrumentAndSendError,
        'injectAudio',
        keyObject,
        requestStateObj,
        constants.AUDIO_INJECTION_CUSTOM_EXECUTOR_ERRORS.unknown);
      rewiredjsExec.__set__('instrumentAndSendError', originalInstrumentAndSendError);
      browserstack.postBrowserStack.restore();
    });

    it('throws unknown error if rails responds with 500', async () => {
      const keyObject = {
        rails_session_id: 'random_session_id',
        appTesting: true,
        name: 'host',
        wda_port: 'port',
        rproxyHost: 'host',
        enableAudioInjection: 'true',
        os: 'android'
      };
      const instrumentAndSendError = sinon.stub();
      sinon.stub(browserstack, 'postBrowserStack').yields(JSON.stringify({ resolve_media: 'failed' }));
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);

      await runAudioInjectionHandler(keyObject, requestStateObj, parsedCommand, 'audio');

      expect(instrumentAndSendError.calledOnce).to.be.true;
      sinon.assert.calledWith(instrumentAndSendError,
        'injectAudio',
        keyObject,
        requestStateObj,
        constants.AUDIO_INJECTION_CUSTOM_EXECUTOR_ERRORS.audio_injection_failure);
      rewiredjsExec.__set__('instrumentAndSendError', originalInstrumentAndSendError);
      browserstack.postBrowserStack.restore();
    });
  });

  context('validateCameraAndAudioInjection', () => {
    const originalInstrumentAndSendError = rewiredjsExec.__get__('instrumentAndSendError');
    const validateCameraAndAudioInjectionHandler = rewiredjsExec.__get__('validateCameraAndAudioInjection');
    let instrumentAndSendError;
    let keyObject;
    beforeEach(() => {
      instrumentAndSendError = sinon.stub();
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);
    })
    afterEach(() => {
      rewiredjsExec.__set__('instrumentAndSendError', originalInstrumentAndSendError);
    })

    let parsedCommandAudio = {
      action: 'injectAudio',
      arguments: {
        audioUrl: 'media://12345',
      },
    };
    let parsedCommandCamera = {
      action: 'cameraImageInjection',
      arguments: {
        audioUrl: 'media://12345',
      },
    }
    let keyObject_audio = {
      rails_session_id: 'random_session_id',
      name: 'host',
      wda_port: 'port',
      rproxyHost: 'host',
      enableAudioInjection: 'true',
      os: 'android'
    };
    let keyObject_camera = {
      rails_session_id: 'random_session_id',
      name: 'host',
      wda_port: 'port',
      rproxyHost: 'host',
      enableCameraImageInjection: 'true'
    };

    it('returns true for an audio injection session on app automate', async () => {
      keyObject = keyObject_audio
      keyObject["appTesting"] = true
      parsedCommand = parsedCommandAudio
      requestStateObj = {
        req_data: JSON.stringify({
          script: `browserstack_executor: ${JSON.stringify(parsedCommand)}`
        }),
        clientSessionID: 'random_session_id_1'
      };
      result = await validateCameraAndAudioInjectionHandler(keyObject, requestStateObj, parsedCommand, '', 'audio', constants.AUDIO_INJECTION_CUSTOM_EXECUTOR_ERRORS, 'audioUrl')
      expect(result).to.equal(true)
    })
    it('returns true for an audio injection session on automate', async () => {
      keyObject = keyObject_audio
      keyObject["appTesting"] = false
      parsedCommand = parsedCommandAudio
      requestStateObj = {
        req_data: JSON.stringify({
          script: `browserstack_executor: ${JSON.stringify(parsedCommand)}`
        }),
        clientSessionID: 'random_session_id_1'
      };
      result = await validateCameraAndAudioInjectionHandler(keyObject, requestStateObj, parsedCommand, '', 'audio', constants.AUDIO_INJECTION_CUSTOM_EXECUTOR_ERRORS, 'audioUrl')
      expect(result).to.equal(true)
    })
    it('returns true for an camera image injection session on app automate', async () => {
      keyObject = keyObject_camera
      keyObject["appTesting"] = true
      parsedCommand = parsedCommandCamera
      requestStateObj = {
        req_data: JSON.stringify({
          script: `browserstack_executor: ${JSON.stringify(parsedCommand)}`
        }),
        clientSessionID: 'random_session_id_1'
      };
      result = await validateCameraAndAudioInjectionHandler(keyObject, requestStateObj, parsedCommand, '', 'camera', constants.AUDIO_INJECTION_CUSTOM_EXECUTOR_ERRORS, 'audioUrl')
      expect(result).to.equal(true)
    })
    it('returns false for an camera image injection session on automate', async () => {
      keyObject = keyObject_camera
      keyObject["appTesting"] = false
      parsedCommand = parsedCommandCamera
      requestStateObj = {
        req_data: JSON.stringify({
          script: `browserstack_executor: ${JSON.stringify(parsedCommand)}`
        }),
        clientSessionID: 'random_session_id_1'
      };
      result = await validateCameraAndAudioInjectionHandler(keyObject, requestStateObj, parsedCommand, '', 'camera', constants.AUDIO_INJECTION_CUSTOM_EXECUTOR_ERRORS, 'audioUrl')
      expect(result).to.equal(false)
      expect(instrumentAndSendError.calledOnce).to.be.true;
    })
  })

  context("runUpdateAppSettingsHandler", () => {
    const keyObject = {
      rails_session_id: "random_session_id",
      appTesting: true,
      name: "host",
      wda_port: "port",
      rproxyHost: "host",
      accessibleFeaturesList: "aa-ios-app-settings",
      isAppSettingsBundlePresent: true
    };

    const parsedCommand = {
      action: 'updateAppSettings',
      arguments: {
        "OTP Auto-Fill": "OFF"
      },
    };

    const requestStateObj = {
      req_data: JSON.stringify({
        script: `browserstack_executor: ${JSON.stringify(parsedCommand)}`
      }),
      clientSessionID: "random_session_id"
    };

    const originalSendRequestToPlatform = rewiredjsExec.__get__('sendRequestToPlatform');
    const originalInstrumentAndSendError = rewiredjsExec.__get__('instrumentAndSendError');
    const runUpdateAppSettingsHandler = rewiredjsExec.__get__('runUpdateAppSettingsHandler');
    const originalInstrumentAndSendExecutorResponse = rewiredjsExec.__get__('instrumentAndSendExecutorResponse')
    it("Executes Successfully with correct keys for App-automate with platform error", async () => {
      const sendRequestToPlatform = sinon.stub();
      rewiredjsExec.__set__('sendRequestToPlatform', (executorType, serverURL, reqObj, key, error, customTimeout, customOptions, responseHandler, customCallback) => {
        responseHandler({
          statusCode: 200
        });
        customCallback({

        });
      });

      const instrumentAndSendError = sinon.stub();
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);
      await runUpdateAppSettingsHandler(keyObject, requestStateObj, parsedCommand);
      expect(instrumentAndSendError.calledOnce).to.be.true;
      rewiredjsExec.__set__('sendRequestToPlatform', originalSendRequestToPlatform);
    });

    it('response handler callback function gives 200', async () => {
      const instrumentAndSendExecutorResponse = sinon.stub();

      rewiredjsExec.__set__('instrumentAndSendExecutorResponse', instrumentAndSendExecutorResponse);

      rewiredjsExec.__set__('sendRequestToPlatform', (executorType, serverURL, reqObj, key, error, customTimeout, customOptions, responseHandler, customCallback) => {
        const data = {
          status: 'success',
        };
        customCallback({

        });
        responseHandler({
          statusCode: 200,
          data: JSON.stringify(data)

        });
      });
      await runUpdateAppSettingsHandler(keyObject, requestStateObj, parsedCommand);

      sinon.assert.calledWith(instrumentAndSendExecutorResponse,
        'app_setting_custom_executor',
        keyObject,
        requestStateObj);
      rewiredjsExec.__set__('sendRequestToPlatform', originalSendRequestToPlatform);
      rewiredjsExec.__set__('instrumentAndSendExecutorResponse', originalInstrumentAndSendExecutorResponse);
    });


    it('response handler callback function gives custom error', async () => {
      const instrumentAndSendError = sinon.stub();

      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);

      const data = {
        status: 'error',
        error: {
          kind: 'app_settings_invalid_value',
          meta_data: {
            automate: 'automation failed'

          }
        }
      };
      rewiredjsExec.__set__('sendRequestToPlatform', (executorType, serverURL, reqObj, key, error, customTimeout, customOptions, responseHandler) => {
        responseHandler({
          statusCode: 200,
          data: JSON.stringify(data)
        });
      });
      await runUpdateAppSettingsHandler(keyObject, requestStateObj, parsedCommand);

      sinon.assert.calledWith(instrumentAndSendError, 'app_setting_custom_executor', keyObject, requestStateObj, constants.SETTINGS_APP_CUSTOM_EXECUTOR_ERRORS.app_settings_invalid_value);
      rewiredjsExec.__set__('sendRequestToPlatform', originalSendRequestToPlatform);
      rewiredjsExec.__set__('instrumentAndSendError', originalInstrumentAndSendError);
    });

    it('response handler internal server error', async () => {
      const instrumentAndSendError = sinon.stub();

      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);

      rewiredjsExec.__set__('sendRequestToPlatform', (executorType, serverURL, reqObj, key, error, customTimeout, customOptions, responseHandler) => {
        responseHandler({

        });
      });
      await runUpdateAppSettingsHandler(keyObject, requestStateObj, parsedCommand);

      sinon.assert.calledWith(instrumentAndSendError, 'app_setting_custom_executor', keyObject, requestStateObj, constants.SETTINGS_APP_CUSTOM_EXECUTOR_ERRORS.internal_error);
      rewiredjsExec.__set__('sendRequestToPlatform', originalSendRequestToPlatform);
      rewiredjsExec.__set__('instrumentAndSendError', originalInstrumentAndSendError);
    });

    it('throw error for tvos', async () => {
      const instrumentAndSendError = sinon.stub();
      keyObject.os = 'tvos'

      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);

      rewiredjsExec.__set__('sendRequestToPlatform', (executorType, serverURL, reqObj, key, error, customTimeout, customOptions, responseHandler) => {
        responseHandler({

        });
      });
      await runUpdateAppSettingsHandler(keyObject, requestStateObj, parsedCommand);

      sinon.assert.calledWith(instrumentAndSendError, 'app_setting_custom_executor', keyObject, requestStateObj, constants.SETTINGS_APP_CUSTOM_EXECUTOR_ERRORS.invalid_os);
      rewiredjsExec.__set__('sendRequestToPlatform', originalSendRequestToPlatform);
      rewiredjsExec.__set__('instrumentAndSendError', originalInstrumentAndSendError);
      delete keyObject.os;
    });

    it('executes successfully if only Pemission Settings are passed even if isAppSettingsBundlePresent is not set', async () => {
      let parsedCommand = {
        action: 'updateAppSettings',
        arguments: {
          "Permission Settings": {
            "Location": {
              "ALLOW LOCATION ACCESS": "Always",
              "Precise Location": "ON"
            }
          }
        },
      };

      let keyObject = {
        rails_session_id: "random_session_id",
        appTesting: true,
        name: "host",
        wda_port: "port",
        rproxyHost: "host",
        accessibleFeaturesList: "aa-ios-app-settings",
      };

      const sendRequestToPlatform = sinon.stub();
      const instrumentAndSendError = sinon.stub();
      rewiredjsExec.__set__('sendRequestToPlatform', sendRequestToPlatform);
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);
      await runUpdateAppSettingsHandler(keyObject, requestStateObj, parsedCommand);

      expect(sendRequestToPlatform.calledOnce).to.be.true;
      rewiredjsExec.__set__('sendRequestToPlatform', originalSendRequestToPlatform);
      rewiredjsExec.__set__('instrumentAndSendError', originalInstrumentAndSendError);
    });

    it('executes successfully if only Pemission Settings are passed and isAppSettingsBundlePresent is set', async () => {
      let parsedCommand = {
        action: 'updateAppSettings',
        arguments: {
          "Permission Settings": {
            "Location": {
              "ALLOW LOCATION ACCESS": "Always",
              "Precise Location": "ON"
            }
          }
        },
      };

      const sendRequestToPlatform = sinon.stub();
      const instrumentAndSendError = sinon.stub();
      rewiredjsExec.__set__('sendRequestToPlatform', sendRequestToPlatform);
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);
      await runUpdateAppSettingsHandler(keyObject, requestStateObj, parsedCommand);

      expect(sendRequestToPlatform.calledOnce).to.be.true;
      rewiredjsExec.__set__('sendRequestToPlatform', originalSendRequestToPlatform);
      rewiredjsExec.__set__('instrumentAndSendError', originalInstrumentAndSendError);
    });

    it("throws invalid action used if capability isAppSettingsBundlePresent is not set", async () => {
      let keyObject = {
        rails_session_id: "random_session_id",
        appTesting: true,
        name: "host",
        wda_port: "port",
        rproxyHost: "host",
        accessibleFeaturesList: "aa-ios-app-settings",
      };

      const instrumentAndSendError = sinon.stub();
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);

      await runUpdateAppSettingsHandler(keyObject, requestStateObj, parsedCommand);

      expect(instrumentAndSendError.calledOnce).to.be.true;
      sinon.assert.calledWith(instrumentAndSendError,
        "app_setting_custom_executor",
        keyObject,
        requestStateObj,
        constants.SETTINGS_APP_CUSTOM_EXECUTOR_ERRORS.no_settings_bundle
      );

      rewiredjsExec.__set__('instrumentAndSendError', originalInstrumentAndSendError);
    });

    it("throws error if feature is not accisible", async () => {
      let keyObject = {
        rails_session_id: "random_session_id",
        appTesting: true,
        name: "host",
        wda_port: "port",
        rproxyHost: "host",
        accessibleFeaturesList: "",
      };

      const instrumentAndSendError = sinon.stub();
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);

      await runUpdateAppSettingsHandler(keyObject, requestStateObj, parsedCommand);

      expect(instrumentAndSendError.calledOnce).to.be.true;
      sinon.assert.calledWith(instrumentAndSendError,
        "app_setting_custom_executor",
        keyObject,
        requestStateObj,
        constants.SETTINGS_APP_CUSTOM_EXECUTOR_ERRORS.feature_not_available_in_current_plan_for_aa
      );

      rewiredjsExec.__set__('instrumentAndSendError', originalInstrumentAndSendError);
    });

    it("throws invalid action name if automate session", async () => {
      let keyObject = {
        rails_session_id: "random_session_id",
        name: "host",
        wda_port: "port",
        rproxyHost: "host",
        accessibleFeaturesList: "aa-ios-app-settings",
      };

      const instrumentAndSendError = sinon.stub();
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);

      await runUpdateAppSettingsHandler(keyObject, requestStateObj, parsedCommand);

      expect(instrumentAndSendError.calledOnce).to.be.true;
      sinon.assert.calledWith(instrumentAndSendError,
        "app_setting_custom_executor",
        keyObject,
        requestStateObj,
        constants.SETTINGS_APP_CUSTOM_EXECUTOR_ERRORS.invalid_action_name
      );
      rewiredjsExec.__set__('instrumentAndSendError', originalInstrumentAndSendError);
    });

    it("throws invalid arguments if arguments is not present", async () => {
      let parsedCommand = {
        action: 'updateAppSettings'
      };

      const instrumentAndSendError = sinon.stub();
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);

      await runUpdateAppSettingsHandler(keyObject, requestStateObj, parsedCommand);

      expect(instrumentAndSendError.calledOnce).to.be.true;
      sinon.assert.calledWith(instrumentAndSendError,
        "app_setting_custom_executor",
        keyObject,
        requestStateObj,
        constants.SETTINGS_APP_CUSTOM_EXECUTOR_ERRORS.invalid_argument_passed
      );
      rewiredjsExec.__set__('instrumentAndSendError', originalInstrumentAndSendError);
    });

    it("throws invalid arguments if appSettings tag is not a hash object", async () => {
      let parsedCommand = {
        action: 'updateAppSettings',
        arguments: "something invalid",
      };

      const instrumentAndSendError = sinon.stub();
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);

      await runUpdateAppSettingsHandler(keyObject, requestStateObj, parsedCommand);

      expect(instrumentAndSendError.calledOnce).to.be.true;
      sinon.assert.calledWith(instrumentAndSendError,
        "app_setting_custom_executor",
        keyObject,
        requestStateObj,
        constants.SETTINGS_APP_CUSTOM_EXECUTOR_ERRORS.invalid_argument_passed
      );
      rewiredjsExec.__set__('instrumentAndSendError', originalInstrumentAndSendError);
    });

    it("throws invalid arguments if appSettings tag is an empty hash object", async () => {
      let parsedCommand = {
        action: 'updateAppSettings',
        arguments: {},
      };

      const instrumentAndSendError = sinon.stub();
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);

      await runUpdateAppSettingsHandler(keyObject, requestStateObj, parsedCommand);

      expect(instrumentAndSendError.calledOnce).to.be.true;
      sinon.assert.calledWith(instrumentAndSendError,
        "app_setting_custom_executor",
        keyObject,
        requestStateObj,
        constants.SETTINGS_APP_CUSTOM_EXECUTOR_ERRORS.invalid_argument_passed
      );
      rewiredjsExec.__set__('instrumentAndSendError', originalInstrumentAndSendError);
    });

    it("throws max_num error in case number of keys in DSL exceeds maximum limit", async () => {
      let parsedCommand = {
        action: 'updateAppSettings',
        arguments: {
          "1": { "first_child": { "second_child": { "third_child": { "fourth_child": { "fifth_child": { "sixth_child": { "seventh_child": { "eighth_child": { "OTP Auto-Fill": "OFF" } } } } } } } } },
          "2": { "first_child": { "second_child": { "third_child": { "fourth_child": { "fifth_child": { "sixth_child": { "seventh_child": { "eighth_child": { "OTP Auto-Fill": "OFF" } } } } } } } } },
          "3": { "first_child": { "second_child": { "third_child": { "fourth_child": { "fifth_child": { "sixth_child": { "seventh_child": { "eighth_child": { "OTP Auto-Fill": "OFF" } } } } } } } } },
          "4": { "first_child": { "second_child": { "third_child": { "fourth_child": { "fifth_child": { "sixth_child": { "seventh_child": { "eighth_child": { "OTP Auto-Fill": "OFF" } } } } } } } } },
          "5": { "first_child": { "second_child": { "third_child": { "fourth_child": { "fifth_child": { "sixth_child": { "seventh_child": { "eighth_child": { "OTP Auto-Fill": "OFF" } } } } } } } } },
          "6": { "first_child": { "second_child": { "third_child": { "fourth_child": { "fifth_child": { "sixth_child": { "seventh_child": { "eighth_child": { "OTP Auto-Fill": "OFF" } } } } } } } } },
          "7": { "first_child": { "second_child": { "third_child": { "fourth_child": { "fifth_child": { "sixth_child": { "seventh_child": { "eighth_child": { "OTP Auto-Fill": "OFF" } } } } } } } } },
          "8": { "first_child": { "second_child": { "third_child": { "fourth_child": { "fifth_child": { "sixth_child": { "seventh_child": { "eighth_child": { "OTP Auto-Fill": "OFF" } } } } } } } } },
          "9": { "first_child": { "second_child": { "third_child": { "fourth_child": { "fifth_child": { "sixth_child": { "seventh_child": { "eighth_child": { "OTP Auto-Fill": "OFF" } } } } } } } } },
          "10": { "first_child": { "second_child": { "third_child": { "fourth_child": { "fifth_child": { "sixth_child": { "seventh_child": { "eighth_child": { "OTP Auto-Fill": "OFF" } } } } } } } } },
          "11": { "first_child": { "second_child": { "third_child": { "fourth_child": { "fifth_child": { "sixth_child": { "seventh_child": { "eighth_child": { "OTP Auto-Fill": "OFF" } } } } } } } } }
        },
      };

      const instrumentAndSendError = sinon.stub();
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);

      await runUpdateAppSettingsHandler(keyObject, requestStateObj, parsedCommand);

      expect(instrumentAndSendError.calledOnce).to.be.true;
      sinon.assert.calledWith(instrumentAndSendError,
        "app_setting_custom_executor",
        keyObject,
        requestStateObj,
        constants.SETTINGS_APP_CUSTOM_EXECUTOR_ERRORS.max_key
      );
      rewiredjsExec.__set__('instrumentAndSendError', originalInstrumentAndSendError);
    });

    it("throws error in case custom executor is used more than max limit in a session", async () => {
      const instrumentAndSendError = sinon.stub();
      const originalGetInstrumentationData = rewiredjsExec.__get__('instrumentAndSendError');
      const getInstrumentationData = (keyObject) => { return { custom_executor: { "app_setting_custom_executor": { count: constants.SETTINGS_APP_CUSTOM_EXECUTOR.max_count + 1 } } } };
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);
      rewiredjsExec.__set__('getInstrumentationData', getInstrumentationData);
      await runUpdateAppSettingsHandler(keyObject, requestStateObj, parsedCommand);

      expect(instrumentAndSendError.calledOnce).to.be.true;
      sinon.assert.calledWith(instrumentAndSendError,
        "app_setting_custom_executor",
        keyObject,
        requestStateObj,
        constants.SETTINGS_APP_CUSTOM_EXECUTOR_ERRORS.max_num_times
      );
      rewiredjsExec.__set__('instrumentAndSendError', originalInstrumentAndSendError);
      rewiredjsExec.__set__('getInstrumentationData', originalGetInstrumentationData);
    });
  });
  context("Getting Session Details", () => {
    it("should call getSessionDetailsHandler", () => {
      let keyObject = {
        rails_session_id: "random_session_id",
        appTesting: false,
      };
      let requestStateObj = {
        req_data: JSON.stringify({
          script:
            'browserstack_executor: {"action": "getSessionDetails"}',
        }),
      };
      sinon.stub(getSessionDetailsHandler, 'runGetSessionDetailsHandler');
      const executed = jsExec.checkandExecuteIfBstackExecutor(
        keyObject,
        requestStateObj
      );
      executed.should.eql(true);
      sinon.assert.called(getSessionDetailsHandler.runGetSessionDetailsHandler);
      getSessionDetailsHandler.runGetSessionDetailsHandler.restore();
    });
  });

  context('#isBStackExecutor', () => {
    it('should return true value on browserstack_executor request', () => {
      const request = {
        url: '/wd/hub/session/6c6d295afb399a1e3d1d79ea47ae7e827eaf8e2d/execute/sync',
        method: 'POST',
        log_data: JSON.stringify({
          script: 'browserstack_executor: {"action": "setSessionStatus", "arguments": {"status": "passed", "reason": "some_reason"}}',
        }),
      };
      const res = jsExec.isBStackExecutor(request);
      res.should.eql(true);
    });

    it('should return false value on non browserstack_executor request', () => {
      const request = {
        url: '/wd/hub/session/6c6d295afb399a1e3d1d79ea47ae7e827eaf8e2d/execute/sync',
        method: 'POST',
        log_data: '',
      };
      const res = jsExec.isBStackExecutor(request);
      res.should.eql(false);
    });

    it('should return false value on non browserstack_executor endpoint request', () => {
      const request = {
        url: '/wd/hub/session/xxxxxxxxxxxxxx12345',
        method: 'POST',
        log_data: JSON.stringify({
          script: 'browserstack_executor: {"action": "setSessionStatus", "arguments": {"status": "passed", "reason": "some_reason"}}',
        }),
      };
      const res = jsExec.isBStackExecutor(request);
      res.should.eql(false);
    });

    it('should return false value on non json request log_data', () => {
      const request = {
        url: '/wd/hub/session/xxxxxxxxxxxxxx12345',
        method: 'POST',
        log_data: 'a string',
      };
      const res = jsExec.isBStackExecutor(request);
      res.should.eql(false);
    });
  });

  context("browserstack_executor: percyScreenshot", () => {
    before(() => {
      sinon.stub(percy, 'percyScreenshotHandler');
    })

    afterEach(() => {
      percy.percyScreenshotHandler.reset();
    })

    after(() => {
      percy.percyScreenshotHandler.restore();
    });

    it("should call percyScreenshotHandler", () => {
      let keyObject = {
        rails_session_id: "random_session_id",
        appTesting: true,
      };

      let requestStateObj = {
        req_data: JSON.stringify({
          script:
            'browserstack_executor: {"action": "percyScreenshot"}',
        }),
      };

      const executed = jsExec.checkandExecuteIfBstackExecutor(
        keyObject,
        requestStateObj
      );

      executed.should.eql(true);
      sinon.assert.called(percy.percyScreenshotHandler);
      sinon.assert.calledWith(
        percy.percyScreenshotHandler,
        keyObject,
        requestStateObj,
        sinon.match.object
      );
    });
  });

  context("browserstack_executor: appAllyScan", () => {
    before(() => {
      sinon.stub(appAlly, 'appAllyScreenshotHandler');
    })

    afterEach(() => {
      appAlly.appAllyScreenshotHandler.reset();
    })

    after(() => {
      appAlly.appAllyScreenshotHandler.restore();
    });

    it("should call appAllyScreenshotHandler", () => {
      let keyObject = {
        rails_session_id: "random_session_id",
        appTesting: true,
      };

      let requestStateObj = {
        req_data: JSON.stringify({
          script:
            'browserstack_executor: {"action": "appAllyScan"}',
        }),
      };

      const executed = jsExec.checkandExecuteIfBstackExecutor(
        keyObject,
        requestStateObj
      );

      executed.should.eql(true);
      sinon.assert.called(appAlly.appAllyScreenshotHandler);
      sinon.assert.calledWith(
        appAlly.appAllyScreenshotHandler,
        keyObject,
        requestStateObj,
        sinon.match.object
      );
    });
  });

  context('#deviceInfoHandler', () => {
    const originalSendRequestToPlatform = rewiredjsExec.__get__('sendRequestToPlatform');
    const originalInstrumentAndSendError = rewiredjsExec.__get__('instrumentAndSendError');
    const originalinstrumentAndSendExecutorResponse = rewiredjsExec.__get__('instrumentAndSendExecutorResponse');
    const deviceInfoHandler = rewiredjsExec.__get__('deviceInfoHandler');

    let keyObject,
      parsedCommand,
      requestStateObj;

    beforeEach(() => {
      keyObject = {
        rails_session_id: 'random_session_id',
        appTesting: true,
        name: 'host',
        wda_port: 'port',
        os: 'android',
        rproxyHost: 'host',
        enableSim: true
      };

      parsedCommand = {
        action: 'deviceInfo',
        arguments: {
          deviceProperties: ["simOptions"]
        }
      };

      requestStateObj = {
        req_data: JSON.stringify({
          script: `browserstack_executor: ${JSON.stringify(parsedCommand)}`,
        }),
        clientSessionID: 'xxxxxxxxxxxxxx12345',
      };
    });

    it('should call instrumentAndSendExecutorResponse if all validations are successful', () => {
      keyObject['simPhoneNumber'] = "+919849822335"
      keyObject['simRegion'] = "Asia"
      const instrumentAndSendExecutorResponse = sinon.stub();
      rewiredjsExec.__set__('instrumentAndSendExecutorResponse', instrumentAndSendExecutorResponse);
      deviceInfoHandler(keyObject, requestStateObj, parsedCommand);
      expect(instrumentAndSendExecutorResponse.calledOnce).to.be.true;
      rewiredjsExec.__set__('instrumentAndSendExecutorResponse', originalinstrumentAndSendExecutorResponse);
    });

    it('should call instrumentAndSendExecutorResponse and set esim if all validations are successful and esim set', () => {
      keyObject['simPhoneNumber'] = "+919849822335"
      keyObject['simRegion'] = "Asia"
      keyObject["esim"] = true;
      const instrumentAndSendExecutorResponse = sinon.stub();
      rewiredjsExec.__set__('instrumentAndSendExecutorResponse', instrumentAndSendExecutorResponse);
      deviceInfoHandler(keyObject, requestStateObj, parsedCommand);
      expect(instrumentAndSendExecutorResponse.calledOnce).to.be.true;
      rewiredjsExec.__set__('instrumentAndSendExecutorResponse', originalinstrumentAndSendExecutorResponse);
    });

    it('should raise and error and not call instrumentAndSendExecutorResponse if enableSim is false', () => {
      keyObject.enableSim = false;
      const instrumentAndSendExecutorResponse = sinon.stub();
      const instrumentAndSendError = sinon.stub();
      rewiredjsExec.__set__('instrumentAndSendExecutorResponse', instrumentAndSendExecutorResponse);
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);
      deviceInfoHandler(keyObject, requestStateObj, parsedCommand);
      expect(instrumentAndSendError.calledOnce).to.be.true;
      expect(instrumentAndSendExecutorResponse.notCalled).to.be.true;
      rewiredjsExec.__set__('instrumentAndSendExecutorResponse', originalinstrumentAndSendExecutorResponse);
      rewiredjsExec.__set__('instrumentAndSendError', originalInstrumentAndSendError);
    });

    it('should raise and error and not call instrumentAndSendExecutorResponse if there are more than 1 argument(s) for custom executor', () => {
      parsedCommand.arguments.command = "dumpsys gfxinfo com.random.package framestats"
      const instrumentAndSendExecutorResponse = sinon.stub();
      const instrumentAndSendError = sinon.stub();
      rewiredjsExec.__set__('instrumentAndSendExecutorResponse', instrumentAndSendExecutorResponse);
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);
      deviceInfoHandler(keyObject, requestStateObj, parsedCommand);
      expect(instrumentAndSendError.calledOnce).to.be.true;
      expect(instrumentAndSendExecutorResponse.notCalled).to.be.true;
      rewiredjsExec.__set__('instrumentAndSendExecutorResponse', originalinstrumentAndSendExecutorResponse);
      rewiredjsExec.__set__('instrumentAndSendError', originalInstrumentAndSendError);
    });

    it('should raise and error and not call instrumentAndSendExecutorResponse if there are no arguments for custom executor', () => {
      parsedCommand.arguments = {}
      const instrumentAndSendExecutorResponse = sinon.stub();
      const instrumentAndSendError = sinon.stub();
      rewiredjsExec.__set__('instrumentAndSendExecutorResponse', instrumentAndSendExecutorResponse);
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);
      deviceInfoHandler(keyObject, requestStateObj, parsedCommand);
      expect(instrumentAndSendError.calledOnce).to.be.true;
      expect(instrumentAndSendExecutorResponse.notCalled).to.be.true;
      rewiredjsExec.__set__('instrumentAndSendExecutorResponse', originalinstrumentAndSendExecutorResponse);
      rewiredjsExec.__set__('instrumentAndSendError', originalInstrumentAndSendError);
    });

    it('should raise and error and not call instrumentAndSendExecutorResponse if deviceProperties argument is not present for custom executor', () => {
      parsedCommand.arguments = { command: "dumpsys gfxinfo com.random.package framestats" };
      const instrumentAndSendExecutorResponse = sinon.stub();
      const instrumentAndSendError = sinon.stub();
      rewiredjsExec.__set__('instrumentAndSendExecutorResponse', instrumentAndSendExecutorResponse);
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);
      deviceInfoHandler(keyObject, requestStateObj, parsedCommand);
      expect(instrumentAndSendError.calledOnce).to.be.true;
      expect(instrumentAndSendExecutorResponse.notCalled).to.be.true;
      rewiredjsExec.__set__('instrumentAndSendExecutorResponse', originalinstrumentAndSendExecutorResponse);
      rewiredjsExec.__set__('instrumentAndSendError', originalInstrumentAndSendError);
    });

    it('should raise and error and not call instrumentAndSendExecutorResponse if deviceProperties has more than one argument(s) for custom executor', () => {
      parsedCommand.arguments.deviceProperties.push("information");
      const instrumentAndSendExecutorResponse = sinon.stub();
      const instrumentAndSendError = sinon.stub();
      rewiredjsExec.__set__('instrumentAndSendExecutorResponse', instrumentAndSendExecutorResponse);
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);
      deviceInfoHandler(keyObject, requestStateObj, parsedCommand);
      expect(instrumentAndSendError.calledOnce).to.be.true;
      expect(instrumentAndSendExecutorResponse.notCalled).to.be.true;
      rewiredjsExec.__set__('instrumentAndSendExecutorResponse', originalinstrumentAndSendExecutorResponse);
      rewiredjsExec.__set__('instrumentAndSendError', originalInstrumentAndSendError);
    });

    it('should raise and error and not call instrumentAndSendExecutorResponse if deviceProperties has more than one argument(s) for custom executor', () => {
      parsedCommand.arguments.deviceProperties = ["information"];
      const instrumentAndSendExecutorResponse = sinon.stub();
      const instrumentAndSendError = sinon.stub();
      rewiredjsExec.__set__('instrumentAndSendExecutorResponse', instrumentAndSendExecutorResponse);
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);
      deviceInfoHandler(keyObject, requestStateObj, parsedCommand);
      expect(instrumentAndSendError.calledOnce).to.be.true;
      expect(instrumentAndSendExecutorResponse.notCalled).to.be.true;
      rewiredjsExec.__set__('instrumentAndSendExecutorResponse', originalinstrumentAndSendExecutorResponse);
      rewiredjsExec.__set__('instrumentAndSendError', originalInstrumentAndSendError);
    });

    it('should raise and error and not call instrumentAndSendExecutorResponse if simPhoneNumber is present and simRegion is not', () => {
      keyObject['simPhoneNumber'] = "+919849822335"
      const instrumentAndSendExecutorResponse = sinon.stub();
      const instrumentAndSendError = sinon.stub();
      rewiredjsExec.__set__('instrumentAndSendExecutorResponse', instrumentAndSendExecutorResponse);
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);
      deviceInfoHandler(keyObject, requestStateObj, parsedCommand);
      expect(instrumentAndSendError.calledOnce).to.be.true;
      expect(instrumentAndSendExecutorResponse.notCalled).to.be.true;
      rewiredjsExec.__set__('instrumentAndSendExecutorResponse', originalinstrumentAndSendExecutorResponse);
      rewiredjsExec.__set__('instrumentAndSendError', originalInstrumentAndSendError);
    });

    it('should raise and error and not call instrumentAndSendExecutorResponse if simPhoneNumber is not present and simRegion is present', () => {
      keyObject['simRegion'] = "+919849822335"
      const instrumentAndSendExecutorResponse = sinon.stub();
      const instrumentAndSendError = sinon.stub();
      rewiredjsExec.__set__('instrumentAndSendExecutorResponse', instrumentAndSendExecutorResponse);
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);
      deviceInfoHandler(keyObject, requestStateObj, parsedCommand);
      expect(instrumentAndSendError.calledOnce).to.be.true;
      expect(instrumentAndSendExecutorResponse.notCalled).to.be.true;
      rewiredjsExec.__set__('instrumentAndSendExecutorResponse', originalinstrumentAndSendExecutorResponse);
      rewiredjsExec.__set__('instrumentAndSendError', originalInstrumentAndSendError);
    });

  });

  context('#customGesturesHandler', () => {
    const originalSendRequestToPlatform = rewiredjsExec.__get__('sendRequestToPlatform');
    const originalInstrumentAndSendError = rewiredjsExec.__get__('instrumentAndSendError');
    const customGesturesHandler = rewiredjsExec.__get__('customGesturesHandler');

    let keyObject,
      parsedCommand,
      requestStateObj,
      sendRequestToPlatform,
      instrumentAndSendError;

    beforeEach(() => {
      keyObject = {
        rails_session_id: 'random_session_id',
        appTesting: true,
        name: 'host',
        wda_port: 'port',
        os: 'ios',
        os_version: '16.0',
        rproxyHost: 'host',
        accessibleFeaturesList: [constants.DF_FEATURE_FENCE_CODES.DEVICE_SHAKE_CODE],
      };

      parsedCommand = {
        action: 'customGestures',
        arguments: {
          deviceShake: "true"
        }
      };

      requestStateObj = {
        req_data: JSON.stringify({
          script: `browserstack_executor: ${JSON.stringify(parsedCommand)}`,
        }),
        clientSessionID: 'xxxxxxxxxxxxxx12345',
      };
      sendRequestToPlatform = sinon.stub();
      instrumentAndSendError = sinon.stub();

      rewiredjsExec.__set__('sendRequestToPlatform', sendRequestToPlatform);
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);
    });

    afterEach(() => {
      rewiredjsExec.__set__('sendRequestToPlatform', originalSendRequestToPlatform);
      rewiredjsExec.__set__('instrumentAndSendError', originalInstrumentAndSendError);
    });

    it('should call instrumentAndSendExecutorResponse and set lock if all validations are successful', () => {

      rewiredjsExec.__set__('sendRequestToPlatform', (executorType, serverURL, reqObj, key, error, customTimeout, customOptions, responseHandler) => {
        responseHandler({
          statusCode: 200
        });
      });
      const instrumentAndSendExecutorResponse = sinon.stub();
      rewiredjsExec.__set__('instrumentAndSendExecutorResponse', instrumentAndSendExecutorResponse);
      customGesturesHandler(keyObject, requestStateObj, parsedCommand);
      expect(instrumentAndSendExecutorResponse.calledOnce).to.be.true;
    });

    it('should call instrumentAndSendExecutorResponse and set lock if all validations are successful and platform failure', () => {

      rewiredjsExec.__set__('sendRequestToPlatform', (executorType, serverURL, reqObj, key, error, customTimeout, customOptions, responseHandler) => {
        responseHandler({
          statusCode: 500
        });
      });
      const instrumentAndSendError = sinon.stub();
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);
      customGesturesHandler(keyObject, requestStateObj, parsedCommand);
      expect(instrumentAndSendError.calledOnce).to.be.true;
    });

    it('should raise an error and not call sendRequestToPlatform if appTesting is false', () => {
      keyObject.appTesting = false;
      customGesturesHandler(keyObject, requestStateObj, parsedCommand);
      sinon.assert.calledWith(instrumentAndSendError, 'customGestures_custom_executor', keyObject, requestStateObj, constants.CUSTOM_GESTURES_CUSTOM_EXECUTOR_ERRORS.incompatible_product);
      sinon.assert.notCalled(sendRequestToPlatform);
    });

    it('should raise an error and not call sendRequestToPlatform if Smart TV session for custom executor', () => {
      keyObject['isSmartTV'] = true;
      customGesturesHandler(keyObject, requestStateObj, parsedCommand);
      sinon.assert.calledWith(instrumentAndSendError, 'customGestures_custom_executor', keyObject, requestStateObj, constants.JSE_GENERIC_ERRORS.smart_tv_not_supported);
      sinon.assert.notCalled(sendRequestToPlatform);
    });

    it('should raise an error and not call sendRequestToPlatform if os is not iOS for custom executor', () => {
      keyObject['os'] = 'android';
      customGesturesHandler(keyObject, requestStateObj, parsedCommand);
      sinon.assert.calledWith(instrumentAndSendError, 'customGestures_custom_executor', keyObject, requestStateObj, constants.CUSTOM_GESTURES_CUSTOM_EXECUTOR_ERRORS.incompatible_platform);
      sinon.assert.notCalled(sendRequestToPlatform);
    });

    it('should raise an error and not call sendRequestToPlatform if os version is less than 16 for custom executor', () => {
      keyObject['os_version'] = '15.4';
      customGesturesHandler(keyObject, requestStateObj, parsedCommand);
      sinon.assert.calledWith(instrumentAndSendError, 'customGestures_custom_executor', keyObject, requestStateObj, constants.CUSTOM_GESTURES_CUSTOM_EXECUTOR_ERRORS.incompatible_os_version);
      sinon.assert.notCalled(sendRequestToPlatform);
    });

    it('should raise an error and not call sendRequestToPlatform if there are no arguments for custom executor', () => {
      parsedCommand.arguments = {}
      customGesturesHandler(keyObject, requestStateObj, parsedCommand);
      sinon.assert.calledWith(instrumentAndSendError, 'customGestures_custom_executor', keyObject, requestStateObj, constants.CUSTOM_GESTURES_CUSTOM_EXECUTOR_ERRORS.incorrect_syntax);
      sinon.assert.notCalled(sendRequestToPlatform);
    });

    it('should raise an error and not call sendRequestToPlatform if deviceShake argument is not present for custom executor', () => {
      parsedCommand.arguments = { command: "dumpsys gfxinfo com.random.package framestats" };
      customGesturesHandler(keyObject, requestStateObj, parsedCommand);
      sinon.assert.calledWith(instrumentAndSendError, 'customGestures_custom_executor', keyObject, requestStateObj, constants.CUSTOM_GESTURES_CUSTOM_EXECUTOR_ERRORS.incorrect_syntax);
      sinon.assert.notCalled(sendRequestToPlatform);
    });

    it('should raise an error and not call sendRequestToPlatform if deviceShake has invalid argument(s) for custom executor', () => {
      parsedCommand.arguments.deviceShake = "anything other than true";
      customGesturesHandler(keyObject, requestStateObj, parsedCommand);
      sinon.assert.calledWith(instrumentAndSendError, 'customGestures_custom_executor', keyObject, requestStateObj, constants.CUSTOM_GESTURES_CUSTOM_EXECUTOR_ERRORS.incorrect_syntax);
      sinon.assert.notCalled(sendRequestToPlatform);
    });

    it('should raise an error and not call sendRequestToPlatform if deviceShake is not allowed in plan', () => {
      keyObject["accessibleFeaturesList"] = [];
      customGesturesHandler(keyObject, requestStateObj, parsedCommand);
      sinon.assert.calledWith(instrumentAndSendError, 'customGestures_custom_executor', keyObject, requestStateObj, constants.CUSTOM_GESTURES_CUSTOM_EXECUTOR_ERRORS.feature_not_available_in_current_plan_for_aa);
      sinon.assert.notCalled(sendRequestToPlatform);
    });

    it('should raise parallel custom gestures command error if multiple requests comes', () => {
      keyObject.isLockedForCustomGestureFlow = true;
      customGesturesHandler(keyObject, requestStateObj, parsedCommand);
      sinon.assert.calledWith(instrumentAndSendError, 'customGestures_custom_executor', keyObject, requestStateObj, constants.CUSTOM_GESTURES_CUSTOM_EXECUTOR_ERRORS.parallel_custom_gestures_command_error);
      sinon.assert.notCalled(sendRequestToPlatform);
    });

  });

  context('#updateIosDeviceSettings', () => {
    const originalSendRequestToPlatform = rewiredjsExec.__get__('sendRequestToPlatform');
    const originalInstrumentAndSendError = rewiredjsExec.__get__('instrumentAndSendError');
    const updateIosDeviceSettings = rewiredjsExec.__get__('updateIosDeviceSettings');
    let keyObject,
      parsedCommand,
      requestStateObj,
      sendRequestToPlatform,
      instrumentAndSendError;

    context('customTime', () => {
      beforeEach(() => {
        keyObject = {
          rails_session_id: 'random_session_id',
          appTesting: true,
          name: 'host',
          wda_port: 'port',
          os: 'ios',
          rproxyHost: 'host',
          accessibleFeaturesList: [constants.DF_FEATURE_FENCE_CODES.CUSTOM_TIME_PLAN_CODE],
        };

        parsedCommand = {
          action: 'updateIosDeviceSettings',
          arguments: {
            customTime: '12:45'
          }
        };

        requestStateObj = {
          req_data: JSON.stringify({
            script: `browserstack_executor: ${JSON.stringify(parsedCommand)}`,
          }),
          clientSessionID: 'xxxxxxxxxxxxxx12345',
        };
        sendRequestToPlatform = sinon.stub();
        instrumentAndSendError = sinon.stub();

        rewiredjsExec.__set__('sendRequestToPlatform', sendRequestToPlatform);
        rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);
      });

      afterEach(() => {
        rewiredjsExec.__set__('sendRequestToPlatform', originalSendRequestToPlatform);
        rewiredjsExec.__set__('instrumentAndSendError', originalInstrumentAndSendError);
      });

      it('should call instrumentAndSendExecutorResponse and set lock if all validations are successful', () => {
        rewiredjsExec.__set__('sendRequestToPlatform', (executorType, serverURL, reqObj, key, error, customTimeout, customOptions, responseHandler) => {
          responseHandler({
            statusCode: 200
          });
        });
        const instrumentAndSendExecutorResponse = sinon.stub();
        rewiredjsExec.__set__('instrumentAndSendExecutorResponse', instrumentAndSendExecutorResponse);
        updateIosDeviceSettings(keyObject, requestStateObj, parsedCommand);
        expect(instrumentAndSendExecutorResponse.calledOnce).to.be.true;
      });

      it('should call instrumentAndSendExecutorResponse and set lock if all validations are successful and platform error', () => {
        rewiredjsExec.__set__('sendRequestToPlatform', (executorType, serverURL, reqObj, key, error, customTimeout, customOptions, responseHandler) => {
          responseHandler({
            statusCode: 500
          });
        });
        const instrumentAndSendError = sinon.stub();
        rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);
        updateIosDeviceSettings(keyObject, requestStateObj, parsedCommand);
        expect(instrumentAndSendError.calledOnce).to.be.true;
      });

      it('should raise and error and not call sendRequestToPlatform if appTesting is false', () => {
        keyObject.appTesting = false;
        updateIosDeviceSettings(keyObject, requestStateObj, parsedCommand);
        sinon.assert.calledWith(instrumentAndSendError, 'ios_device_settings_executor_customTime', keyObject, requestStateObj, constants.IOS_SETTINGS_EXECUTOR_ERRORS.incompatible_product);
        sinon.assert.notCalled(sendRequestToPlatform);
      });

      it('should raise and error and not call sendRequestToPlatform if feature is not accessible to group', () => {
        keyObject.accessibleFeaturesList = [];
        updateIosDeviceSettings(keyObject, requestStateObj, parsedCommand);
        sinon.assert.calledWith(instrumentAndSendError, 'ios_device_settings_executor_customTime', keyObject, requestStateObj, constants.DATE_TIME_CUSTOM_EXECUTOR_ERRORS.feature_not_available_in_current_plan_for_aa);
        sinon.assert.notCalled(sendRequestToPlatform);
      });


      it('should raise an error and not call sendRequestToPlatform if os is not iOS for custom executor', () => {
        keyObject.os = 'android';
        updateIosDeviceSettings(keyObject, requestStateObj, parsedCommand);
        sinon.assert.calledWith(instrumentAndSendError, 'ios_device_settings_executor_customTime', keyObject, requestStateObj, constants.IOS_SETTINGS_EXECUTOR_ERRORS.incompatible_platform);
        sinon.assert.notCalled(sendRequestToPlatform);
      });

      it('should raise an error and not call sendRequestToPlatform if os is tvos for custom executor', () => {
        keyObject.isSmartTV = true;
        updateIosDeviceSettings(keyObject, requestStateObj, parsedCommand);
        sinon.assert.calledWith(instrumentAndSendError, 'ios_device_settings_executor_customTime', keyObject, requestStateObj, constants.JSE_GENERIC_ERRORS.smart_tv_not_supported);
        sinon.assert.notCalled(sendRequestToPlatform);
      });

      it('should raise an error and not call sendRequestToPlatform if os version is less than 13 for custom executor', () => {
        keyObject.os_version = '12.4';
        updateIosDeviceSettings(keyObject, requestStateObj, parsedCommand);
        sinon.assert.calledWith(instrumentAndSendError, 'ios_device_settings_executor_customTime', keyObject, requestStateObj, constants.DATE_TIME_CUSTOM_EXECUTOR_ERRORS.incompatible_os_version);
        sinon.assert.notCalled(sendRequestToPlatform);
      });

      it('should raise an error and not call sendRequestToPlatform if customTime has invalid argument for custom executor', () => {
        parsedCommand.arguments.customTime = '25:45';
        updateIosDeviceSettings(keyObject, requestStateObj, parsedCommand);
        sinon.assert.calledWith(instrumentAndSendError, 'ios_device_settings_executor_customTime', keyObject, requestStateObj, constants.DATE_TIME_CUSTOM_EXECUTOR_ERRORS.invalid_value);
        sinon.assert.notCalled(sendRequestToPlatform);
      });

      it('should raise multiple commands error if multiple requests comes', () => {
        parsedCommand.arguments.customTime = '25:45';
        keyObject.isLockedForDeviceSettingsFlow = true;
        updateIosDeviceSettings(keyObject, requestStateObj, parsedCommand);
        sinon.assert.calledWith(instrumentAndSendError, 'ios_device_settings_executor_customTime', keyObject, requestStateObj, constants.IOS_SETTINGS_EXECUTOR_ERRORS.parallel_update_ios_settings_command_error);
        sinon.assert.notCalled(sendRequestToPlatform);
      });

      it('should drop request if any invalid command is sent', () => {
        parsedCommand.arguments = { command: '25:45' };
        updateIosDeviceSettings(keyObject, requestStateObj, parsedCommand);
        sinon.assert.notCalled(sendRequestToPlatform);
        sinon.assert.calledOnce(instrumentAndSendError);
        sinon.assert.calledWith(instrumentAndSendError, 'ios_device_settings_executor', keyObject, requestStateObj, constants.IOS_SETTINGS_EXECUTOR_ERRORS.invalid_argument);
      });

      it('should raise multiple commands error if multiple requests comes', () => {
        parsedCommand = {
          action: 'updateIosDeviceSettings',
          arguments: {
            customArg: '12:45'
          }
        };
        keyObject.isLockedForDeviceSettingsFlow = true;
        updateIosDeviceSettings(keyObject, requestStateObj, parsedCommand);
        sinon.assert.calledWith(instrumentAndSendError, 'ios_device_settings_executor', keyObject, requestStateObj, constants.IOS_SETTINGS_EXECUTOR_ERRORS.parallel_update_ios_settings_command_error);
        sinon.assert.notCalled(sendRequestToPlatform);
      });
    });


    context('customDate', () => {
      beforeEach(() => {
        keyObject = {
          rails_session_id: 'random_session_id',
          appTesting: true,
          name: 'host',
          wda_port: 'port',
          os: 'ios',
          rproxyHost: 'host',
          aaDeviceDateAccessible: true,
          os_version: 16,
          accessibleFeaturesList: [constants.DF_FEATURE_FENCE_CODES.CUSTOM_DATE_PLAN_CODE]
        };

        parsedCommand = {
          action: 'updateIosDeviceSettings',
          arguments: {
            customDate: 'Nov 24 2023'
          }
        };

        requestStateObj = {
          req_data: JSON.stringify({
            script: `browserstack_executor: ${JSON.stringify(parsedCommand)}`,
          }),
          clientSessionID: 'xxxxxxxxxxxxxx12345',
        };
        sendRequestToPlatform = sinon.stub();
        instrumentAndSendError = sinon.stub();

        rewiredjsExec.__set__('sendRequestToPlatform', sendRequestToPlatform);
        rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);
      });

      afterEach(() => {
        rewiredjsExec.__set__('sendRequestToPlatform', originalSendRequestToPlatform);
        rewiredjsExec.__set__('instrumentAndSendError', originalInstrumentAndSendError);
      });

      it('should call instrumentAndSendExecutorResponse and set lock if all validations are successful', () => {
        rewiredjsExec.__set__('sendRequestToPlatform', (executorType, serverURL, reqObj, key, error, customTimeout, customOptions, responseHandler) => {
          responseHandler({
            statusCode: 200
          });
        });
        const instrumentAndSendExecutorResponse = sinon.stub();
        rewiredjsExec.__set__('instrumentAndSendExecutorResponse', instrumentAndSendExecutorResponse);
        updateIosDeviceSettings(keyObject, requestStateObj, parsedCommand);
        expect(instrumentAndSendExecutorResponse.calledOnce).to.be.true;
      });

      it('should call instrumentAndSendExecutorResponse and set lock if all validations are successful and platform error', () => {
        rewiredjsExec.__set__('sendRequestToPlatform', (executorType, serverURL, reqObj, key, error, customTimeout, customOptions, responseHandler) => {
          responseHandler({
            statusCode: 500
          });
        });
        const instrumentAndSendError = sinon.stub();
        rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);
        updateIosDeviceSettings(keyObject, requestStateObj, parsedCommand);
        expect(instrumentAndSendError.calledOnce).to.be.true;
      });

      it('should show custom error', () => {
        const errorCode = 'CD_0001'
        rewiredjsExec.__set__('sendRequestToPlatform', (executorType, serverURL, reqObj, key, error, customTimeout, customOptions, responseHandler) => {
          responseHandler({
            statusCode: 500,
            data: `{ "error_code": "${errorCode}"}`
          });
        });
        const instrumentAndSendError = sinon.stub();
        rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);
        updateIosDeviceSettings(keyObject, requestStateObj, parsedCommand);
        sinon.assert.calledWith(instrumentAndSendError, 'ios_device_settings_executor_customDate', keyObject, requestStateObj, constants.DF_EXECUTOR_ERROR_CODES[errorCode]);
      });

      it('should raise and error and not call sendRequestToPlatform if appTesting is false', () => {
        keyObject.appTesting = false;
        updateIosDeviceSettings(keyObject, requestStateObj, parsedCommand);
        sinon.assert.calledWith(instrumentAndSendError, 'ios_device_settings_executor_customDate', keyObject, requestStateObj, constants.DATE_TIME_CUSTOM_EXECUTOR_ERRORS.incompatible_product);
        sinon.assert.notCalled(sendRequestToPlatform);
      });

      it('should raise multiple commands error if multiple requests comes', () => {
        parsedCommand.arguments.customDate = '20 Dec 2023';
        keyObject.isLockedForDeviceSettingsFlow = true;
        updateIosDeviceSettings(keyObject, requestStateObj, parsedCommand);
        sinon.assert.calledWith(instrumentAndSendError, 'ios_device_settings_executor_customDate', keyObject, requestStateObj, constants.IOS_SETTINGS_EXECUTOR_ERRORS.parallel_update_ios_settings_command_error);
        sinon.assert.notCalled(sendRequestToPlatform);
      });

      it('should raise and error and not call sendRequestToPlatform if feature is not accessible to group', () => {
        keyObject.accessibleFeaturesList = [];
        updateIosDeviceSettings(keyObject, requestStateObj, parsedCommand);
        sinon.assert.calledWith(instrumentAndSendError, 'ios_device_settings_executor_customDate', keyObject, requestStateObj, constants.DATE_TIME_CUSTOM_EXECUTOR_ERRORS.feature_not_available_in_current_plan_for_aa);
        sinon.assert.notCalled(sendRequestToPlatform);
      });


      it('should raise an error and not call sendRequestToPlatform if os is not iOS for custom executor', () => {
        keyObject.os = 'android';
        updateIosDeviceSettings(keyObject, requestStateObj, parsedCommand);
        sinon.assert.calledWith(instrumentAndSendError, 'ios_device_settings_executor_customDate', keyObject, requestStateObj, constants.IOS_SETTINGS_EXECUTOR_ERRORS.incompatible_platform);
        sinon.assert.notCalled(sendRequestToPlatform);
      });

      it('should raise an error and not call sendRequestToPlatform if os is tvos for custom executor', () => {
        keyObject.isSmartTV = true;
        updateIosDeviceSettings(keyObject, requestStateObj, parsedCommand);
        sinon.assert.calledWith(instrumentAndSendError, 'ios_device_settings_executor_customDate', keyObject, requestStateObj, constants.JSE_GENERIC_ERRORS.smart_tv_not_supported);
        sinon.assert.notCalled(sendRequestToPlatform);
      });

      it('should raise an error and not call sendRequestToPlatform if os version is not 16 for custom executor', () => {
        keyObject.os_version = '15.0';
        updateIosDeviceSettings(keyObject, requestStateObj, parsedCommand);
        sinon.assert.calledWith(instrumentAndSendError, 'ios_device_settings_executor_customDate', keyObject, requestStateObj, constants.DATE_TIME_CUSTOM_EXECUTOR_ERRORS.incompatible_custom_date_os_version);
        sinon.assert.notCalled(sendRequestToPlatform);
      });

      it('should raise an error if aaDeviceDateAccessible is sent false by railsApp', () => {
        keyObject.aaDeviceDateAccessible = false;
        updateIosDeviceSettings(keyObject, requestStateObj, parsedCommand);
        sinon.assert.calledWith(instrumentAndSendError, 'ios_device_settings_executor_customDate', keyObject, requestStateObj, constants.DATE_TIME_CUSTOM_EXECUTOR_ERRORS.invalid_action_by_group);
        sinon.assert.notCalled(sendRequestToPlatform);
      });

      it('should raise an error and not call sendRequestToPlatform if customDate has invalid argument for custom executor', () => {
        parsedCommand.arguments.customDate = 'abcd';
        updateIosDeviceSettings(keyObject, requestStateObj, parsedCommand);
        sinon.assert.calledWith(instrumentAndSendError, 'ios_device_settings_executor_customDate', keyObject, requestStateObj, constants.DATE_TIME_CUSTOM_EXECUTOR_ERRORS.invalid_custom_date_value);
        sinon.assert.notCalled(sendRequestToPlatform);
      });

      it('should drop request if any invalid command is sent', () => {
        parsedCommand.arguments = { command: '25:45' };
        updateIosDeviceSettings(keyObject, requestStateObj, parsedCommand);
        sinon.assert.notCalled(sendRequestToPlatform);
        sinon.assert.calledOnce(instrumentAndSendError);
        sinon.assert.calledWith(instrumentAndSendError, 'ios_device_settings_executor', keyObject, requestStateObj, constants.IOS_SETTINGS_EXECUTOR_ERRORS.invalid_argument);
      });
    });

    context('LocationServices', () => {
      beforeEach(() => {
        keyObject = {
          rails_session_id: 'random_session_id',
          appTesting: true,
          name: 'host',
          wda_port: 'port',
          os: 'ios',
          rproxyHost: 'host',
          appDisplayName: 'sample_app',
          accessibleFeaturesList: [constants.DF_FEATURE_FENCE_CODES.CUSTOM_LOCATION_SERVICES_PLAN_CODE],
        };

        parsedCommand = {
          action: 'updateIosDeviceSettings',
          arguments: {
            LocationServices: 'OFF'
          }
        };

        requestStateObj = {
          req_data: JSON.stringify({
            script: `browserstack_executor: ${JSON.stringify(parsedCommand)}`,
          }),
          clientSessionID: 'xxxxxxxxxxxxxx12345',
        };
        sendRequestToPlatform = sinon.stub();
        instrumentAndSendError = sinon.stub();

        rewiredjsExec.__set__('sendRequestToPlatform', sendRequestToPlatform);
        rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);
      });

      afterEach(() => {
        rewiredjsExec.__set__('sendRequestToPlatform', originalSendRequestToPlatform);
        rewiredjsExec.__set__('instrumentAndSendError', originalInstrumentAndSendError);
      });

      it('should call instrumentAndSendExecutorResponse and set lock if all validations are successful', () => {
        rewiredjsExec.__set__('sendRequestToPlatform', (executorType, serverURL, reqObj, key, error, customTimeout, customOptions, responseHandler) => {
          responseHandler({
            statusCode: 200
          });
        });
        const instrumentAndSendExecutorResponse = sinon.stub();
        rewiredjsExec.__set__('instrumentAndSendExecutorResponse', instrumentAndSendExecutorResponse);
        updateIosDeviceSettings(keyObject, requestStateObj, parsedCommand);
        expect(instrumentAndSendExecutorResponse.calledOnce).to.be.true;
      });

      it('should call instrumentAndSendExecutorResponse and set lock if all validations are successful and platform error', () => {
        rewiredjsExec.__set__('sendRequestToPlatform', (executorType, serverURL, reqObj, key, error, customTimeout, customOptions, responseHandler) => {
          responseHandler({
            statusCode: 500
          });
        });
        const instrumentAndSendError = sinon.stub();
        rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);
        updateIosDeviceSettings(keyObject, requestStateObj, parsedCommand);
        expect(instrumentAndSendError.calledOnce).to.be.true;
      });

      it('should raise and error and not call sendRequestToPlatform if appTesting is false', () => {
        keyObject.appTesting = false;
        updateIosDeviceSettings(keyObject, requestStateObj, parsedCommand);
        sinon.assert.calledWith(instrumentAndSendError, 'ios_device_settings_executor_LocationServices', keyObject, requestStateObj, constants.IOS_SETTINGS_EXECUTOR_ERRORS.incompatible_product);
        sinon.assert.notCalled(sendRequestToPlatform);
      });

      it('should raise and error and not call sendRequestToPlatform if feature is not accessible to group', () => {
        keyObject.accessibleFeaturesList = [];
        updateIosDeviceSettings(keyObject, requestStateObj, parsedCommand);
        sinon.assert.calledWith(instrumentAndSendError, 'ios_device_settings_executor_LocationServices', keyObject, requestStateObj, constants.LOCATION_SERVICES_CUSTOM_EXECUTOR_ERRORS.feature_not_available_in_current_plan_for_aa);
        sinon.assert.notCalled(sendRequestToPlatform);
      });


      it('should raise an error and not call sendRequestToPlatform if os is not iOS for custom executor', () => {
        keyObject.os = 'android';
        updateIosDeviceSettings(keyObject, requestStateObj, parsedCommand);
        sinon.assert.calledWith(instrumentAndSendError, 'ios_device_settings_executor_LocationServices', keyObject, requestStateObj, constants.IOS_SETTINGS_EXECUTOR_ERRORS.incompatible_platform);
        sinon.assert.notCalled(sendRequestToPlatform);
      });

      it('should raise an error and not call sendRequestToPlatform if os is tvos for custom executor', () => {
        keyObject.isSmartTV = true;
        updateIosDeviceSettings(keyObject, requestStateObj, parsedCommand);
        sinon.assert.calledWith(instrumentAndSendError, 'ios_device_settings_executor_LocationServices', keyObject, requestStateObj, constants.JSE_GENERIC_ERRORS.smart_tv_not_supported);
        sinon.assert.notCalled(sendRequestToPlatform);
      });

      it('should raise an error and not call sendRequestToPlatform if os version is less than 15 for custom executor', () => {
        keyObject.os_version = '14.4';
        updateIosDeviceSettings(keyObject, requestStateObj, parsedCommand);
        sinon.assert.calledWith(instrumentAndSendError, 'ios_device_settings_executor_LocationServices', keyObject, requestStateObj, constants.LOCATION_SERVICES_CUSTOM_EXECUTOR_ERRORS.incompatible_os_version);
        sinon.assert.notCalled(sendRequestToPlatform);
      });

      it('should raise an error and not call sendRequestToPlatform if LocationServices has invalid argument for custom executor', () => {
        parsedCommand.arguments.LocationServices = 'Always';
        updateIosDeviceSettings(keyObject, requestStateObj, parsedCommand);
        sinon.assert.calledWith(instrumentAndSendError, 'ios_device_settings_executor_LocationServices', keyObject, requestStateObj, constants.LOCATION_SERVICES_CUSTOM_EXECUTOR_ERRORS.invalid_value);
        sinon.assert.notCalled(sendRequestToPlatform);
      });

      it('should raise multiple commands error if multiple requests comes', () => {
        parsedCommand.arguments.LocationServices = 'OFF';
        keyObject.isLockedForDeviceSettingsFlow = true;
        updateIosDeviceSettings(keyObject, requestStateObj, parsedCommand);
        sinon.assert.calledWith(instrumentAndSendError, 'ios_device_settings_executor_LocationServices', keyObject, requestStateObj, constants.IOS_SETTINGS_EXECUTOR_ERRORS.parallel_update_ios_settings_command_error);
        sinon.assert.notCalled(sendRequestToPlatform);
      });

      it('should drop request if any invalid command is sent', () => {
        parsedCommand.arguments = { PreciseLocation: 'ON' };
        updateIosDeviceSettings(keyObject, requestStateObj, parsedCommand);
        sinon.assert.notCalled(sendRequestToPlatform);
        sinon.assert.calledOnce(instrumentAndSendError);
        sinon.assert.calledWith(instrumentAndSendError, 'ios_device_settings_executor', keyObject, requestStateObj, constants.IOS_SETTINGS_EXECUTOR_ERRORS.invalid_argument);
      });

    });

    context('12HourTime', () => {
      beforeEach(() => {
        keyObject = {
          rails_session_id: 'random_session_id',
          appTesting: true,
          name: 'host',
          wda_port: 'port',
          os: 'ios',
          rproxyHost: 'host',
          appDisplayName: 'sample_app',
          accessibleFeaturesList: [constants.DF_FEATURE_FENCE_CODES.CUSTOM_TIME_PLAN_CODE],
        };

        parsedCommand = {
          action: 'updateIosDeviceSettings',
          arguments: {
            '12HourTime': 'On'
          }
        };

        requestStateObj = {
          req_data: JSON.stringify({
            script: `browserstack_executor: ${JSON.stringify(parsedCommand)}`,
          }),
          clientSessionID: 'xxxxxxxxxxxxxx12345',
        };
        sendRequestToPlatform = sinon.stub();
        instrumentAndSendError = sinon.stub();

        rewiredjsExec.__set__('sendRequestToPlatform', sendRequestToPlatform);
        rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);
      });

      afterEach(() => {
        rewiredjsExec.__set__('sendRequestToPlatform', originalSendRequestToPlatform);
        rewiredjsExec.__set__('instrumentAndSendError', originalInstrumentAndSendError);
      });

      it('should call instrumentAndSendExecutorResponse and set lock if all validations are successful', () => {
        rewiredjsExec.__set__('sendRequestToPlatform', (executorType, serverURL, reqObj, key, error, customTimeout, customOptions, responseHandler) => {
          responseHandler({
            statusCode: 200
          });
        });
        const instrumentAndSendExecutorResponse = sinon.stub();
        rewiredjsExec.__set__('instrumentAndSendExecutorResponse', instrumentAndSendExecutorResponse);
        updateIosDeviceSettings(keyObject, requestStateObj, parsedCommand);
        expect(instrumentAndSendExecutorResponse.calledOnce).to.be.true;
      });

      it('should call instrumentAndSendExecutorResponse and set lock if all validations are successful and platform error', () => {
        rewiredjsExec.__set__('sendRequestToPlatform', (executorType, serverURL, reqObj, key, error, customTimeout, customOptions, responseHandler) => {
          responseHandler({
            statusCode: 500
          });
        });
        const instrumentAndSendError = sinon.stub();
        rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);
        updateIosDeviceSettings(keyObject, requestStateObj, parsedCommand);
        expect(instrumentAndSendError.calledOnce).to.be.true;
      });

      it('should raise and error and not call sendRequestToPlatform if appTesting is false', () => {
        keyObject.appTesting = false;
        updateIosDeviceSettings(keyObject, requestStateObj, parsedCommand);
        sinon.assert.calledWith(instrumentAndSendError, 'ios_device_settings_executor_12HourTime', keyObject, requestStateObj, constants.IOS_SETTINGS_EXECUTOR_ERRORS.incompatible_product);
        sinon.assert.notCalled(sendRequestToPlatform);
      });

      it('should raise and error and not call sendRequestToPlatform if feature is not accessible to group', () => {
        keyObject.accessibleFeaturesList = [];
        updateIosDeviceSettings(keyObject, requestStateObj, parsedCommand);
        sinon.assert.calledWith(instrumentAndSendError, 'ios_device_settings_executor_12HourTime', keyObject, requestStateObj, constants.HOUR_FORMAT_CUSTOM_EXECUTOR_ERRORS.feature_not_available_in_current_plan_for_aa);
        sinon.assert.notCalled(sendRequestToPlatform);
      });

      it('should raise an error and not call sendRequestToPlatform if os is not iOS for custom executor', () => {
        keyObject.os = 'android';
        updateIosDeviceSettings(keyObject, requestStateObj, parsedCommand);
        sinon.assert.calledWith(instrumentAndSendError, 'ios_device_settings_executor_12HourTime', keyObject, requestStateObj, constants.IOS_SETTINGS_EXECUTOR_ERRORS.incompatible_platform);
        sinon.assert.notCalled(sendRequestToPlatform);
      });

      it('should raise an error and not call sendRequestToPlatform if os is tvos for custom executor', () => {
        keyObject.isSmartTV = true;
        updateIosDeviceSettings(keyObject, requestStateObj, parsedCommand);
        sinon.assert.calledWith(instrumentAndSendError, 'ios_device_settings_executor_12HourTime', keyObject, requestStateObj, constants.JSE_GENERIC_ERRORS.smart_tv_not_supported);
        sinon.assert.notCalled(sendRequestToPlatform);
      });

      it('should raise an error and not call sendRequestToPlatform if os version is less than 15 for custom executor', () => {
        keyObject.os_version = '14.4';
        updateIosDeviceSettings(keyObject, requestStateObj, parsedCommand);
        sinon.assert.calledWith(instrumentAndSendError, 'ios_device_settings_executor_12HourTime', keyObject, requestStateObj, constants.HOUR_FORMAT_CUSTOM_EXECUTOR_ERRORS.incompatible_os_version);
        sinon.assert.notCalled(sendRequestToPlatform);
      });

      it('should raise an error and not call sendRequestToPlatform if 12HourTime has invalid argument for custom executor', () => {
        parsedCommand.arguments['12HourTime'] = 'offff';
        updateIosDeviceSettings(keyObject, requestStateObj, parsedCommand);
        sinon.assert.calledWith(instrumentAndSendError, 'ios_device_settings_executor_12HourTime', keyObject, requestStateObj, constants.HOUR_FORMAT_CUSTOM_EXECUTOR_ERRORS.invalid_value);
        sinon.assert.notCalled(sendRequestToPlatform);
      });

      it('should raise multiple commands error if multiple requests comes', () => {
        parsedCommand.arguments['12HourTime'] = 'ON';
        keyObject.isLockedForDeviceSettingsFlow = true;
        updateIosDeviceSettings(keyObject, requestStateObj, parsedCommand);
        sinon.assert.calledWith(instrumentAndSendError, 'ios_device_settings_executor_12HourTime', keyObject, requestStateObj, constants.IOS_SETTINGS_EXECUTOR_ERRORS.parallel_update_ios_settings_command_error);
        sinon.assert.notCalled(sendRequestToPlatform);
      });

      it('should drop request if any invalid command is sent', () => {
        parsedCommand.arguments = { command: 'ON' };
        updateIosDeviceSettings(keyObject, requestStateObj, parsedCommand);
        sinon.assert.notCalled(sendRequestToPlatform);
        sinon.assert.calledOnce(instrumentAndSendError);
        sinon.assert.calledWith(instrumentAndSendError, 'ios_device_settings_executor', keyObject, requestStateObj, constants.IOS_SETTINGS_EXECUTOR_ERRORS.invalid_argument);
      });

    });

  });

  describe('biometricUserOptionHandler', () => {

    const originalSendRequestToPlatform = rewiredjsExec.__get__('sendRequestToPlatform');
    const originalInstrumentAndSendError = rewiredjsExec.__get__('instrumentAndSendError');
    const biometricUserOptionHandler = rewiredjsExec.__get__('biometricUserOptionHandler');

    let sendRequestStub;

    beforeEach(() => {
      keyObject = {
        rails_session_id: 'random_session_id',
        appTesting: false,
        name: 'host',
        wda_port: 'port',
        os: 'ios',
        os_version: '16.0',
        rproxyHost: 'host',
      };

      parsedCommand = {
        action: 'biometricUserOption',
        arguments: {
          userOption: "pass"
        }
      };

      requestStateObj = {
        req_data: JSON.stringify({
          script: `browserstack_executor: ${JSON.stringify(parsedCommand)}`,
        }),
        clientSessionID: 'xxxxxxxxxxxxxx12345',
      };
      sendRequestToPlatform = sinon.stub();
      instrumentAndSendError = sinon.stub();

      rewiredjsExec.__set__('sendRequestToPlatform', sendRequestToPlatform);
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);
    });

    afterEach(() => {
      rewiredjsExec.__set__('sendRequestToPlatform', originalSendRequestToPlatform);
      rewiredjsExec.__set__('instrumentAndSendError', originalInstrumentAndSendError);
    });

    it('should call instrumentAndSendExecutorResponse successfully', () => {
      keyObject['enableBiometric'] = true;
      keyObject['appTesting'] = true;
      rewiredjsExec.__set__('sendRequestToPlatform', (executorType, serverURL, reqObj, key, error, customTimeout, customOptions, responseHandler) => {
        responseHandler({
          statusCode: 200
        });
      });
      const instrumentAndSendExecutorResponse = sinon.stub();
      rewiredjsExec.__set__('instrumentAndSendExecutorResponse', instrumentAndSendExecutorResponse);
      biometricUserOptionHandler(keyObject, requestStateObj, parsedCommand);
      expect(instrumentAndSendExecutorResponse.calledOnce).to.be.true;
    });

    it('should raise error if os is android', () => {
      keyObject['os'] = 'android';
      keyObject['enableBiometric'] = true;
      keyObject['appTesting'] = true;
      biometricUserOptionHandler(keyObject, requestStateObj, parsedCommand);
      sinon.assert.calledWith(instrumentAndSendError, 'biometric_user_option_custom_executor', keyObject, requestStateObj, constants.BIOMETRIC_USER_OPTION_CUSTOM_EXECUTOR_ERRORS.incompatible_os_version);
      sinon.assert.notCalled(sendRequestToPlatform);
    });

    it('should raise error if enableBiometric not passed as true', () => {
      keyObject['enableBiometric'] = false;
      keyObject['appTesting'] = true;
      biometricUserOptionHandler(keyObject, requestStateObj, parsedCommand);
      sinon.assert.calledWith(instrumentAndSendError, 'biometric_user_option_custom_executor', keyObject, requestStateObj, constants.BIOMETRIC_USER_OPTION_CUSTOM_EXECUTOR_ERRORS.invalid_action_used);
      sinon.assert.notCalled(sendRequestToPlatform);
    });

    it('should raise error if Smart TV', () => {
      keyObject['isSmartTV'] = true;
      keyObject['appTesting'] = true;
      biometricUserOptionHandler(keyObject, requestStateObj, parsedCommand);
      sinon.assert.calledWith(instrumentAndSendError, 'biometric_user_option_custom_executor', keyObject, requestStateObj, constants.JSE_GENERIC_ERRORS.smart_tv_not_supported);
      sinon.assert.notCalled(sendRequestToPlatform);
    });

    it('should raise error if appTesting  is false', () => {
      keyObject['appTesting'] = false;
      biometricUserOptionHandler(keyObject, requestStateObj, parsedCommand);
      sinon.assert.calledWith(instrumentAndSendError, 'biometric_user_option_custom_executor', keyObject, requestStateObj, constants.BIOMETRIC_USER_OPTION_CUSTOM_EXECUTOR_ERRORS.invalid_action_name);
      sinon.assert.notCalled(sendRequestToPlatform);
    });

    it('should raise error if userOption is not pass/cancel/fail', () => {
      keyObject['appTesting'] = true;
      keyObject['enableBiometric'] = true;

      parsedCommand = {
        action: 'biometricUserOption',
        arguments: {
          userOption: "random"
        }
      };

      biometricUserOptionHandler(keyObject, requestStateObj, parsedCommand);
      sinon.assert.calledWith(instrumentAndSendError, 'biometric_user_option_custom_executor', keyObject, requestStateObj, constants.BIOMETRIC_USER_OPTION_CUSTOM_EXECUTOR_ERRORS.invalid_arg_passed);
      sinon.assert.notCalled(sendRequestToPlatform);
    });

    it('should call instrumentAndSendExecutorResponse failed', () => {
      keyObject['enableBiometric'] = true;
      keyObject['appTesting'] = true;
      rewiredjsExec.__set__('sendRequestToPlatform', (executorType, serverURL, reqObj, key, error, customTimeout, customOptions, responseHandler) => {
        responseHandler({
          statusCode: 500
        });
      });
      const instrumentAndSendError = sinon.stub();
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);
      biometricUserOptionHandler(keyObject, requestStateObj, parsedCommand);
      expect(instrumentAndSendError.calledOnce).to.be.true;
    });

    it('should call instrumentAndSendExecutorResponse thrown exeption', () => {
      keyObject['enableBiometric'] = true;
      keyObject['appTesting'] = true;
      rewiredjsExec.__set__('sendRequestToPlatform', (executorType, serverURL, reqObj, key, error, customTimeout, customOptions, responseHandler) => {
        throw new Error('Something went wrong');
      });
      const instrumentAndSendError = sinon.stub();
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);
      biometricUserOptionHandler(keyObject, requestStateObj, parsedCommand);
      expect(instrumentAndSendError.calledOnce).to.be.true;
    });
  });

  describe('customCertificateHandler', () => {
    const originalSendRequestToPlatform = rewiredjsExec.__get__('sendRequestToPlatform');
    const originalInstrumentAndSendError = rewiredjsExec.__get__('instrumentAndSendError');
  
    let sendRequestStub;
  
    beforeEach(() => {
      keyObject = {
        rails_session_id: 'random_session_id',
        appTesting: false,
        name: 'host',
        wda_port: 'port',
        os: 'ios',
        os_version: '16.0',
        rproxyHost: 'host',
      };
  
      parsedCommand = {
        action: 'customCertificate',
        arguments: {
          certificateId: "cert_id",
          password: "password"
        }
      };
  
      requestStateObj = {
        req_data: JSON.stringify({
          script: `browserstack_executor: ${JSON.stringify(parsedCommand)}`,
        }),
        clientSessionID: 'xxxxxxxxxxxxxx12345',
      };
      sendRequestToPlatform = sinon.stub();
      instrumentAndSendError = sinon.stub();
  
      rewiredjsExec.__set__('sendRequestToPlatform', sendRequestToPlatform);
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);
    });
  
    afterEach(() => {
      rewiredjsExec.__set__('sendRequestToPlatform', originalSendRequestToPlatform);
      rewiredjsExec.__set__('instrumentAndSendError', originalInstrumentAndSendError);
    });
  
    it('should call instrumentAndSendExecutorResponse successfully', () => {
      keyObject['customCertificate'] = {
        certificateId: "cert_id",
        password: "password"
      };
      keyObject['appTesting'] = true;
      rewiredjsExec.__set__('sendRequestToPlatform', (executorType, serverURL, reqObj, key, error, customTimeout, customOptions, responseHandler) => {
        responseHandler({
          statusCode: 200
        });
      });
      const instrumentAndSendExecutorResponse = sinon.stub();
      rewiredjsExec.__set__('instrumentAndSendExecutorResponse', instrumentAndSendExecutorResponse);
      expect(instrumentAndSendExecutorResponse.calledOnce).to.be.false;
    });
  
    it('should raise error if os is not iOS', () => {
      keyObject['os'] = 'android';
      keyObject['customCertificate'] = {
        certificateId: "cert_id",
        password: "password"
      };
      keyObject['appTesting'] = true;
      sinon.assert.notCalled(sendRequestToPlatform);
    });
  
    it('should raise error if customCertificate is missing', () => {
      keyObject['appTesting'] = true;
      sinon.assert.notCalled(sendRequestToPlatform);
    });

    it('should raise error if appTesting is false', () => {
      keyObject['customCertificate'] = {
        certificateId: "cert_id",
        password: "password"
      };
      keyObject['appTesting'] = false;
      sinon.assert.notCalled(sendRequestToPlatform);
    });
  
    it('should raise error if certificateId or password is invalid', () => {
      keyObject['customCertificate'] = {
        certificateId: "invalid_cert_id",
        password: "password"
      };
      keyObject['appTesting'] = true;
      sinon.assert.notCalled(sendRequestToPlatform);
    });
  
    it('should call instrumentAndSendExecutorResponse thrown exception', () => {
      const platformError = { message: 'browserstack_certificate_install_failure' };
      keyObject['customCertificate'] = {
        certificateId: "cert_id",
        password: "password"
      };
      keyObject['appTesting'] = true;
      rewiredjsExec.__set__('sendRequestToPlatform', (executorType, serverURL, reqObj, key, error, customTimeout, customOptions, responseHandler) => {
        throw platformError 
      });
      const instrumentAndSendError = sinon.stub();
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);
      expect(instrumentAndSendError.calledOnce).to.be.false;
    });

    it('should call instrumentAndSendExecutorResponse thrown exception', () => {
      const userError = { message: 'certificate_install_invalid_password' };
      keyObject['customCertificate'] = {
        certificateId: "cert_id",
        password: "invalid_password"
      };
      keyObject['appTesting'] = true;
      rewiredjsExec.__set__('sendRequestToPlatform', (executorType, serverURL, reqObj, key, error, customTimeout, customOptions, responseHandler) => {
        throw userError
      });
      const instrumentAndSendError = sinon.stub();
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);
      expect(instrumentAndSendError.calledOnce).to.be.false;
    });
  });

  context('#applePayCustomHandler', () => {
    const originalSendRequestToPlatform = rewiredjsExec.__get__('sendRequestToPlatform');
    const originalInstrumentAndSendError = rewiredjsExec.__get__('instrumentAndSendError');
    const applePayCustomHandler = rewiredjsExec.__get__('applePayCustomHandler');

    let keyObject,
      parsedCommand,
      requestStateObj,
      sendRequestToPlatform,
      instrumentAndSendError;

    beforeEach(() => {
      keyObject = {
        rails_session_id: 'random_session_id',
        appTesting: false,
        name: 'host',
        wda_port: 'port',
        os: 'ios',
        os_version: '16.0',
        rproxyHost: 'host',
      };

      parsedCommand = {
        action: 'applePay',
        arguments: {
          confirmPayment: "true"
        }
      };

      requestStateObj = {
        req_data: JSON.stringify({
          script: `browserstack_executor: ${JSON.stringify(parsedCommand)}`,
        }),
        clientSessionID: 'xxxxxxxxxxxxxx12345',
      };
      sendRequestToPlatform = sinon.stub();
      instrumentAndSendError = sinon.stub();

      rewiredjsExec.__set__('sendRequestToPlatform', sendRequestToPlatform);
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);
    });

    afterEach(() => {
      rewiredjsExec.__set__('sendRequestToPlatform', originalSendRequestToPlatform);
      rewiredjsExec.__set__('instrumentAndSendError', originalInstrumentAndSendError);
    });

    it('should call instrumentAndSendExecutorResponse and set lock if all validations are successful platform 200', () => {
      keyObject['enableApplePay'] = true;
      rewiredjsExec.__set__('sendRequestToPlatform', (executorType, serverURL, reqObj, key, error, customTimeout, customOptions, responseHandler) => {
        responseHandler({
          statusCode: 200
        });
      });
      const instrumentAndSendExecutorResponse = sinon.stub();
      rewiredjsExec.__set__('instrumentAndSendExecutorResponse', instrumentAndSendExecutorResponse);
      applePayCustomHandler(keyObject, requestStateObj, parsedCommand);
      expect(instrumentAndSendExecutorResponse.calledOnce).to.be.true;
    });

    it('should call instrumentAndSendExecutorResponse and set lock if all validations are successful platform returns 500', () => {
      keyObject['enableApplePay'] = true;
      rewiredjsExec.__set__('sendRequestToPlatform', (executorType, serverURL, reqObj, key, error, customTimeout, customOptions, responseHandler) => {
        responseHandler({
          statusCode: 500
        });
      });
      const instrumentAndSendError = sinon.stub();
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);
      applePayCustomHandler(keyObject, requestStateObj, parsedCommand);
      expect(instrumentAndSendError.calledOnce).to.be.true;
    });

    it('should raise an error when caps are not passed', () => {
      applePayCustomHandler(keyObject, requestStateObj, parsedCommand);
      sinon.assert.calledWith(instrumentAndSendError, 'applePay_custom_executor', keyObject, requestStateObj, constants.APPLE_PAY_CUSTOM_EXECUTOR_ERRORS.caps_not_passed);
      sinon.assert.notCalled(sendRequestToPlatform);
    });

    it('should raise an error and not call sendRequestToPlatform if executor does not pass confirmPayments true', () => {
      keyObject['enableApplePay'] = true;
      parsedCommand = {
        action: 'applePay',
        arguments: {
          confirmPayment: "false"
        }
      };
      applePayCustomHandler(keyObject, requestStateObj, parsedCommand);
      sinon.assert.calledWith(instrumentAndSendError, 'applePay_custom_executor', keyObject, requestStateObj, constants.APPLE_PAY_CUSTOM_EXECUTOR_ERRORS.invalid_value);
      sinon.assert.notCalled(sendRequestToPlatform);
    });

    it('should raise an error and not call sendRequestToPlatform if executor does not pass confirmPayment argument', () => {
      keyObject['enableApplePay'] = true;
      parsedCommand = {
        action: 'applePay',
        arguments: {
          confirm: "false"
        }
      };
      applePayCustomHandler(keyObject, requestStateObj, parsedCommand);
      sinon.assert.calledWith(instrumentAndSendError, 'applePay_custom_executor', keyObject, requestStateObj, constants.APPLE_PAY_CUSTOM_EXECUTOR_ERRORS.invalid_args);
      sinon.assert.notCalled(sendRequestToPlatform);
    });

    it('should raise an error and not call sendRequestToPlatform if there are no arguments for custom executor', () => {
      keyObject['enableApplePay'] = true;
      parsedCommand.arguments = {}
      applePayCustomHandler(keyObject, requestStateObj, parsedCommand);
      sinon.assert.calledWith(instrumentAndSendError, 'applePay_custom_executor', keyObject, requestStateObj, constants.APPLE_PAY_CUSTOM_EXECUTOR_ERRORS.invalid_args);
      sinon.assert.notCalled(sendRequestToPlatform);
    });

    it('should raise parallel apple pay command error if multiple requests comes', () => {
      keyObject['enableApplePay'] = true;
      keyObject.isLockedForApplePayFlow = true;
      applePayCustomHandler(keyObject, requestStateObj, parsedCommand);
      sinon.assert.calledWith(instrumentAndSendError, 'applePay_custom_executor', keyObject, requestStateObj, constants.APPLE_PAY_CUSTOM_EXECUTOR_ERRORS.parallel_apple_pay_command_error);
      sinon.assert.notCalled(sendRequestToPlatform);
    });

    it('should raise parallel apple pay command error if Smart TV', () => {
      keyObject['isSmartTV'] = true;
      keyObject.isLockedForApplePayFlow = true;
      applePayCustomHandler(keyObject, requestStateObj, parsedCommand);
      sinon.assert.calledWith(instrumentAndSendError, 'applePay_custom_executor', keyObject, requestStateObj, constants.JSE_GENERIC_ERRORS.smart_tv_not_supported);
      sinon.assert.notCalled(sendRequestToPlatform);
    });

  });

  context('#applePayDetails', () => {
    const originalSendRequestToPlatform = rewiredjsExec.__get__('sendRequestToPlatform');
    const originalInstrumentAndSendError = rewiredjsExec.__get__('instrumentAndSendError');
    const originalinstrumentAndSendExecutorResponse = rewiredjsExec.__get__('instrumentAndSendExecutorResponse');
    const originalValidateApplePayPrefillDetail = rewiredjsExec.__get__('validateApplePayPrefillDetail');
    const applePayPrefillDetailHandler = rewiredjsExec.__get__('applePayPrefillDetailHandler');

    let keyObject,
      parsedCommand,
      requestStateObj;

    beforeEach(() => {
      keyObject = {
        rails_session_id: 'random_session_id',
        appTesting: true,
        name: 'host',
        wda_port: 'port',
        os: 'android',
        rproxyHost: 'host',
        enableApplePay: true
      };

      parsedCommand = {
        action: 'applePayDetails',
        arguments: {
          billingDetails: {
            firstName: "First Name",
            lastName: "Last Name",
            state: "State",
            city: "City",
            street: "Street",
            zip: "ZIP",
            country: "Country"
          },
          shippingDetails: {
            firstName: "First Name",
            lastName: "Last Name",
            state: "State",
            city: "City",
            street: "Street",
            addressLine2: "Address Line 2",
            postCode: "Post Code",
            country: "Country"
          },
          contact: {
            email: "email",
            phone: "phone"
          }
        }
      };

      requestStateObj = {
        req_data: JSON.stringify({
          script: `browserstack_executor: ${JSON.stringify(parsedCommand)}`,
        }),
        clientSessionID: 'xxxxxxxxxxxxxx12345',
      };

      rewiredjsExec.__set__('sendRequestToPlatform', (executorType, serverURL, reqObj, key, error, customTimeout, customOptions, responseHandler) => {
        responseHandler({
          statusCode: 200
        });
      });
    });

    afterEach(() => {
      rewiredjsExec.__set__('sendRequestToPlatform', originalSendRequestToPlatform);
      rewiredjsExec.__set__('instrumentAndSendExecutorResponse', originalinstrumentAndSendExecutorResponse);
      rewiredjsExec.__set__('instrumentAndSendError', originalInstrumentAndSendError);
      rewiredjsExec.__set__('validateApplePayPrefillDetail', originalValidateApplePayPrefillDetail);
    })

    it('should call instrumentAndSendExecutorResponse if all validations are successful', () => {
      const instrumentAndSendExecutorResponse = sinon.stub();
      rewiredjsExec.__set__('instrumentAndSendExecutorResponse', instrumentAndSendExecutorResponse);
      applePayPrefillDetailHandler(keyObject, requestStateObj, parsedCommand);
      expect(instrumentAndSendExecutorResponse.calledOnce).to.be.true;
      rewiredjsExec.__set__('instrumentAndSendExecutorResponse', originalinstrumentAndSendExecutorResponse);
    });

    it('should raise an error and not call instrumentAndSendExecutorResponse if it is Smart TV session', () => {
      keyObject.isSmartTV = true;
      const instrumentAndSendExecutorResponse = sinon.stub();
      const instrumentAndSendError = sinon.stub();
      rewiredjsExec.__set__('instrumentAndSendExecutorResponse', instrumentAndSendExecutorResponse);
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);
      applePayPrefillDetailHandler(keyObject, requestStateObj, parsedCommand);
      expect(instrumentAndSendError.calledOnce).to.be.true;
      expect(instrumentAndSendExecutorResponse.notCalled).to.be.true;
      sinon.assert.calledWith(instrumentAndSendError, 'applePayDetails', keyObject, requestStateObj, constants.JSE_GENERIC_ERRORS.smart_tv_not_supported);
    });

    it('should raise an error and not call instrumentAndSendExecutorResponse if enableApplePay is false', () => {
      keyObject.enableApplePay = false;
      const instrumentAndSendExecutorResponse = sinon.stub();
      const instrumentAndSendError = sinon.stub();
      rewiredjsExec.__set__('instrumentAndSendExecutorResponse', instrumentAndSendExecutorResponse);
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);
      applePayPrefillDetailHandler(keyObject, requestStateObj, parsedCommand);
      expect(instrumentAndSendError.calledOnce).to.be.true;
      expect(instrumentAndSendExecutorResponse.notCalled).to.be.true;
      sinon.assert.calledWith(instrumentAndSendError, 'applePayDetails', keyObject, requestStateObj, constants.APPLE_PAY_PREFILL_DATA_CUSTOM_EXECUTOR_ERRORS.caps_not_passed);
    });

    it('should raise an error and not call instrumentAndSendExecutorResponse if parallel request comes', () => {
      keyObject.isLockedForApplePayPrefillDetailFlow = true;
      const instrumentAndSendExecutorResponse = sinon.stub();
      const instrumentAndSendError = sinon.stub();
      rewiredjsExec.__set__('instrumentAndSendExecutorResponse', instrumentAndSendExecutorResponse);
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);
      applePayPrefillDetailHandler(keyObject, requestStateObj, parsedCommand);
      expect(instrumentAndSendError.calledOnce).to.be.true;
      expect(instrumentAndSendExecutorResponse.notCalled).to.be.true;
      sinon.assert.calledWith(instrumentAndSendError, 'applePayDetails', keyObject, requestStateObj, constants.APPLE_PAY_PREFILL_DATA_CUSTOM_EXECUTOR_ERRORS.parallel_apple_pay_commands_error);
    });

    it('should raise an error and not call instrumentAndSendExecutorResponse if parallel request comes in race condition', () => {
      keyObject.isLockedForApplePayPrefillDetailFlow = true;
      const instrumentAndSendExecutorResponse = sinon.stub();
      const instrumentAndSendError = sinon.stub();
      const validateApplePayPrefillDetail = sinon.stub();
      rewiredjsExec.__set__('instrumentAndSendExecutorResponse', instrumentAndSendExecutorResponse);
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);
      rewiredjsExec.__set__('validateApplePayPrefillDetail', validateApplePayPrefillDetail);

      applePayPrefillDetailHandler(keyObject, requestStateObj, parsedCommand);
      expect(instrumentAndSendError.calledOnce).to.be.true;
      expect(instrumentAndSendExecutorResponse.notCalled).to.be.true;
      sinon.assert.calledWith(instrumentAndSendError, 'applePayDetails', keyObject, requestStateObj, constants.APPLE_PAY_PREFILL_DATA_CUSTOM_EXECUTOR_ERRORS.parallel_apple_pay_commands_error);
    });

    it('should raise an error and not call instrumentAndSendExecutorResponse if there are more than 1 argument(s) for custom executor', () => {
      parsedCommand.arguments.command = "dumpsys gfxinfo com.random.package framestats"
      const instrumentAndSendExecutorResponse = sinon.stub();
      const instrumentAndSendError = sinon.stub();
      rewiredjsExec.__set__('instrumentAndSendExecutorResponse', instrumentAndSendExecutorResponse);
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);
      applePayPrefillDetailHandler(keyObject, requestStateObj, parsedCommand);
      expect(instrumentAndSendError.calledOnce).to.be.true;
      expect(instrumentAndSendExecutorResponse.notCalled).to.be.true;
    });

    it('should raise an error and not call instrumentAndSendExecutorResponse if invalid argument is passed', () => {
      parsedCommand.arguments['randomArgument'] = "random string";
      const instrumentAndSendExecutorResponse = sinon.stub();
      const instrumentAndSendError = sinon.stub();
      rewiredjsExec.__set__('instrumentAndSendExecutorResponse', instrumentAndSendExecutorResponse);
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);
      applePayPrefillDetailHandler(keyObject, requestStateObj, parsedCommand);
      sinon.assert.calledWith(instrumentAndSendError, 'applePayDetails', keyObject, requestStateObj, constants.APPLE_PAY_PREFILL_DATA_CUSTOM_EXECUTOR_ERRORS.invalid_argument);
      expect(instrumentAndSendError.calledOnce).to.be.true;
      expect(instrumentAndSendExecutorResponse.notCalled).to.be.true;
    });

    it('should raise an error and not call instrumentAndSendExecutorResponse if billingDetails argument is not an object', () => {
      parsedCommand.arguments['billingDetails'] = "random string";
      const instrumentAndSendExecutorResponse = sinon.stub();
      const instrumentAndSendError = sinon.stub();
      rewiredjsExec.__set__('instrumentAndSendExecutorResponse', instrumentAndSendExecutorResponse);
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);
      applePayPrefillDetailHandler(keyObject, requestStateObj, parsedCommand);
      sinon.assert.calledWith(instrumentAndSendError, 'applePayDetails', keyObject, requestStateObj, constants.APPLE_PAY_PREFILL_DATA_CUSTOM_EXECUTOR_ERRORS.invalid_argument);
      expect(instrumentAndSendError.calledOnce).to.be.true;
      expect(instrumentAndSendExecutorResponse.notCalled).to.be.true;
    });

    it('should raise an error and not call instrumentAndSendExecutorResponse if any property is invalid in in billingDetails argument', () => {
      parsedCommand.arguments['billingDetails']['randomProperty'] = 'randomString';
      const instrumentAndSendExecutorResponse = sinon.stub();
      const instrumentAndSendError = sinon.stub();
      rewiredjsExec.__set__('instrumentAndSendExecutorResponse', instrumentAndSendExecutorResponse);
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);
      applePayPrefillDetailHandler(keyObject, requestStateObj, parsedCommand);
      sinon.assert.calledWith(instrumentAndSendError, 'applePayDetails', keyObject, requestStateObj, constants.APPLE_PAY_PREFILL_DATA_CUSTOM_EXECUTOR_ERRORS.invalid_argument);
      expect(instrumentAndSendError.calledOnce).to.be.true;
      expect(instrumentAndSendExecutorResponse.notCalled).to.be.true;
    });

    it('should raise an error and not call instrumentAndSendExecutorResponse if any property in billingDetails argument is not a string', () => {
      parsedCommand.arguments['billingDetails']['firstName'] = {};
      const instrumentAndSendExecutorResponse = sinon.stub();
      const instrumentAndSendError = sinon.stub();
      rewiredjsExec.__set__('instrumentAndSendExecutorResponse', instrumentAndSendExecutorResponse);
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);
      applePayPrefillDetailHandler(keyObject, requestStateObj, parsedCommand);
      sinon.assert.calledWith(instrumentAndSendError, 'applePayDetails', keyObject, requestStateObj, constants.APPLE_PAY_PREFILL_DATA_CUSTOM_EXECUTOR_ERRORS.invalid_argument);
      expect(instrumentAndSendError.calledOnce).to.be.true;
      expect(instrumentAndSendExecutorResponse.notCalled).to.be.true;
    });

    it('should raise an error and not call instrumentAndSendExecutorResponse if shippingDetails argument is not an object', () => {
      parsedCommand.arguments['shippingDetails'] = "random string";
      const instrumentAndSendExecutorResponse = sinon.stub();
      const instrumentAndSendError = sinon.stub();
      rewiredjsExec.__set__('instrumentAndSendExecutorResponse', instrumentAndSendExecutorResponse);
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);
      applePayPrefillDetailHandler(keyObject, requestStateObj, parsedCommand);
      sinon.assert.calledWith(instrumentAndSendError, 'applePayDetails', keyObject, requestStateObj, constants.APPLE_PAY_PREFILL_DATA_CUSTOM_EXECUTOR_ERRORS.invalid_argument);
      expect(instrumentAndSendError.calledOnce).to.be.true;
      expect(instrumentAndSendExecutorResponse.notCalled).to.be.true;
    });

    it('should raise an error and not call instrumentAndSendExecutorResponse if any property is invalid in in shippingDetails argument', () => {
      parsedCommand.arguments['shippingDetails']['randomProperty'] = 'randomString';
      const instrumentAndSendExecutorResponse = sinon.stub();
      const instrumentAndSendError = sinon.stub();
      rewiredjsExec.__set__('instrumentAndSendExecutorResponse', instrumentAndSendExecutorResponse);
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);
      applePayPrefillDetailHandler(keyObject, requestStateObj, parsedCommand);
      sinon.assert.calledWith(instrumentAndSendError, 'applePayDetails', keyObject, requestStateObj, constants.APPLE_PAY_PREFILL_DATA_CUSTOM_EXECUTOR_ERRORS.invalid_argument);
      expect(instrumentAndSendError.calledOnce).to.be.true;
      expect(instrumentAndSendExecutorResponse.notCalled).to.be.true;
    });

    it('should raise an error and not call instrumentAndSendExecutorResponse if any property in shippingDetails argument is not a string', () => {
      parsedCommand.arguments['shippingDetails']['firstName'] = {};
      const instrumentAndSendExecutorResponse = sinon.stub();
      const instrumentAndSendError = sinon.stub();
      rewiredjsExec.__set__('instrumentAndSendExecutorResponse', instrumentAndSendExecutorResponse);
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);
      applePayPrefillDetailHandler(keyObject, requestStateObj, parsedCommand);
      sinon.assert.calledWith(instrumentAndSendError, 'applePayDetails', keyObject, requestStateObj, constants.APPLE_PAY_PREFILL_DATA_CUSTOM_EXECUTOR_ERRORS.invalid_argument);
      expect(instrumentAndSendError.calledOnce).to.be.true;
      expect(instrumentAndSendExecutorResponse.notCalled).to.be.true;
    });

    it('should raise an error and not call instrumentAndSendExecutorResponse if shippingDetails argument is not an object', () => {
      parsedCommand.arguments['shippingDetails'] = "random string";
      const instrumentAndSendExecutorResponse = sinon.stub();
      const instrumentAndSendError = sinon.stub();
      rewiredjsExec.__set__('instrumentAndSendExecutorResponse', instrumentAndSendExecutorResponse);
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);
      applePayPrefillDetailHandler(keyObject, requestStateObj, parsedCommand);
      sinon.assert.calledWith(instrumentAndSendError, 'applePayDetails', keyObject, requestStateObj, constants.APPLE_PAY_PREFILL_DATA_CUSTOM_EXECUTOR_ERRORS.invalid_argument);
      expect(instrumentAndSendError.calledOnce).to.be.true;
      expect(instrumentAndSendExecutorResponse.notCalled).to.be.true;
    });

    it('should raise an error and not call instrumentAndSendExecutorResponse if any property is invalid in in shippingDetails argument', () => {
      parsedCommand.arguments['shippingDetails']['randomProperty'] = 'randomString';
      const instrumentAndSendExecutorResponse = sinon.stub();
      const instrumentAndSendError = sinon.stub();
      rewiredjsExec.__set__('instrumentAndSendExecutorResponse', instrumentAndSendExecutorResponse);
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);
      applePayPrefillDetailHandler(keyObject, requestStateObj, parsedCommand);
      sinon.assert.calledWith(instrumentAndSendError, 'applePayDetails', keyObject, requestStateObj, constants.APPLE_PAY_PREFILL_DATA_CUSTOM_EXECUTOR_ERRORS.invalid_argument);
      expect(instrumentAndSendError.calledOnce).to.be.true;
      expect(instrumentAndSendExecutorResponse.notCalled).to.be.true;
    });

    it('should raise an error and not call instrumentAndSendExecutorResponse if any property in shippingDetails argument is not a string', () => {
      parsedCommand.arguments['shippingDetails']['firstName'] = {};
      const instrumentAndSendExecutorResponse = sinon.stub();
      const instrumentAndSendError = sinon.stub();
      rewiredjsExec.__set__('instrumentAndSendExecutorResponse', instrumentAndSendExecutorResponse);
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);
      applePayPrefillDetailHandler(keyObject, requestStateObj, parsedCommand);
      sinon.assert.calledWith(instrumentAndSendError, 'applePayDetails', keyObject, requestStateObj, constants.APPLE_PAY_PREFILL_DATA_CUSTOM_EXECUTOR_ERRORS.invalid_argument);
      expect(instrumentAndSendError.calledOnce).to.be.true;
      expect(instrumentAndSendExecutorResponse.notCalled).to.be.true;
    });

    it('should raise an error and not call instrumentAndSendExecutorResponse if contact argument is not an object', () => {
      parsedCommand.arguments['contact'] = "random string";
      const instrumentAndSendExecutorResponse = sinon.stub();
      const instrumentAndSendError = sinon.stub();
      rewiredjsExec.__set__('instrumentAndSendExecutorResponse', instrumentAndSendExecutorResponse);
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);
      applePayPrefillDetailHandler(keyObject, requestStateObj, parsedCommand);
      sinon.assert.calledWith(instrumentAndSendError, 'applePayDetails', keyObject, requestStateObj, constants.APPLE_PAY_PREFILL_DATA_CUSTOM_EXECUTOR_ERRORS.invalid_argument);
      expect(instrumentAndSendError.calledOnce).to.be.true;
      expect(instrumentAndSendExecutorResponse.notCalled).to.be.true;
    });

    it('should raise an error and not call instrumentAndSendExecutorResponse if any property is invalid in in contact argument', () => {
      parsedCommand.arguments['contact']['randomProperty'] = 'randomString';
      const instrumentAndSendExecutorResponse = sinon.stub();
      const instrumentAndSendError = sinon.stub();
      rewiredjsExec.__set__('instrumentAndSendExecutorResponse', instrumentAndSendExecutorResponse);
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);
      applePayPrefillDetailHandler(keyObject, requestStateObj, parsedCommand);
      sinon.assert.calledWith(instrumentAndSendError, 'applePayDetails', keyObject, requestStateObj, constants.APPLE_PAY_PREFILL_DATA_CUSTOM_EXECUTOR_ERRORS.invalid_argument);
      expect(instrumentAndSendError.calledOnce).to.be.true;
      expect(instrumentAndSendExecutorResponse.notCalled).to.be.true;
    });

    it('should raise an error and not call instrumentAndSendExecutorResponse if any property in contact argument is not a string', () => {
      parsedCommand.arguments['contact']['email'] = {};
      const instrumentAndSendExecutorResponse = sinon.stub();
      const instrumentAndSendError = sinon.stub();
      rewiredjsExec.__set__('instrumentAndSendExecutorResponse', instrumentAndSendExecutorResponse);
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);
      applePayPrefillDetailHandler(keyObject, requestStateObj, parsedCommand);
      sinon.assert.calledWith(instrumentAndSendError, 'applePayDetails', keyObject, requestStateObj, constants.APPLE_PAY_PREFILL_DATA_CUSTOM_EXECUTOR_ERRORS.invalid_argument);
      expect(instrumentAndSendError.calledOnce).to.be.true;
      expect(instrumentAndSendExecutorResponse.notCalled).to.be.true;
    });

    it('should raise an error and not call instrumentAndSendExecutorResponse if request to platform fails', () => {
      rewiredjsExec.__set__('sendRequestToPlatform', (executorType, serverURL, reqObj, key, error, customTimeout, customOptions, responseHandler) => {
        responseHandler({
          statusCode: 500
        });
      });
      const instrumentAndSendExecutorResponse = sinon.stub();
      const instrumentAndSendError = sinon.stub();
      rewiredjsExec.__set__('instrumentAndSendExecutorResponse', instrumentAndSendExecutorResponse);
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);
      applePayPrefillDetailHandler(keyObject, requestStateObj, parsedCommand);
      expect(instrumentAndSendError.calledOnce).to.be.true;
      expect(instrumentAndSendExecutorResponse.notCalled).to.be.true;
      sinon.assert.calledWith(instrumentAndSendError, 'applePayDetails', keyObject, requestStateObj, constants.APPLE_PAY_PREFILL_DATA_CUSTOM_EXECUTOR_ERRORS.internal_error);
    });

    it('should raise an error and not call instrumentAndSendExecutorResponse if request to platform fails with response body containing error code as APPD_0001', () => {
      rewiredjsExec.__set__('sendRequestToPlatform', (executorType, serverURL, reqObj, key, error, customTimeout, customOptions, responseHandler) => {
        responseHandler({
          data: JSON.stringify({
            code: "APPD_0001"
          }),
          statusCode: 500
        });
      });
      const instrumentAndSendExecutorResponse = sinon.stub();
      const instrumentAndSendError = sinon.stub();
      rewiredjsExec.__set__('instrumentAndSendExecutorResponse', instrumentAndSendExecutorResponse);
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);
      applePayPrefillDetailHandler(keyObject, requestStateObj, parsedCommand);
      expect(instrumentAndSendError.calledOnce).to.be.true;
      expect(instrumentAndSendExecutorResponse.notCalled).to.be.true;
      sinon.assert.calledWith(instrumentAndSendError, 'applePayDetails', keyObject, requestStateObj, constants.APPLE_PAY_PREFILL_DATA_CUSTOM_EXECUTOR_ERRORS.missing_argument);
    });

    it('should raise an error and not call instrumentAndSendExecutorResponse if exception is raised', () => {
      rewiredjsExec.__set__('sendRequestToPlatform', (executorType, serverURL, reqObj, key, error, customTimeout, customOptions, responseHandler) => {
        throw new Error('Something went wrong');
      });
      const instrumentAndSendExecutorResponse = sinon.stub();
      const instrumentAndSendError = sinon.stub();
      rewiredjsExec.__set__('instrumentAndSendExecutorResponse', instrumentAndSendExecutorResponse);
      rewiredjsExec.__set__('instrumentAndSendError', instrumentAndSendError);
      applePayPrefillDetailHandler(keyObject, requestStateObj, parsedCommand);
      expect(instrumentAndSendError.calledOnce).to.be.true;
      expect(instrumentAndSendExecutorResponse.notCalled).to.be.true;
      sinon.assert.calledWith(instrumentAndSendError, 'applePayDetails', keyObject, requestStateObj, constants.APPLE_PAY_PREFILL_DATA_CUSTOM_EXECUTOR_ERRORS.internal_error);
    });
  });
});
