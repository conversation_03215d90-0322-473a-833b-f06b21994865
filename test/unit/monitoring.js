'use strict';

/* eslint-disable max-len */

const MonitoringService = require('../../apps/monitoringService/monitoringService');
const MonitoringHelper = require('../../apps/monitoringService/monitoringHelper');
const requestlib = require('../../lib/request');
const hubHelper = require('../../helper');
const constants = require('../../constants');
const { assert } = require('chai');
const sinon = require('sinon');
const winston = require('winston');

describe('monitoring service tests', () => {
  const monitoringService = new MonitoringService();
  const monitoringHelper = new MonitoringHelper();
  const trackTokenHash = constants.railsPipeline.redisAutomateTrackTokenHash;
  const appAutomateTrackTokenHash = constants.railsPipeline.redisAppAutomateTrackTokenHash;
  const logTag = 'automate';
  const appAutomateLogTag = 'appAutomate';
  const PIPELINE_STATE = constants.monitoring.pipelineState;
  let clock = new Date();

  const startForcedDowntime = () => {
    monitoringHelper.redisClient.set(trackTokenHash.downtimeToken, 'forcedZero');
  };
  const stubClock = () => {
    const now = new Date() - (20 * 60 * 1000);
    clock = sinon.useFakeTimers(now);
  };
  const appAutomateStartForcedDownTime = () => {
    monitoringHelper.redisClient.set(appAutomateTrackTokenHash.downtimeToken, 'forcedZero');
  };
  const startNon200Downtime = () => monitoringHelper.redisClient.set(trackTokenHash.downtimeToken, 'waitNon200');
  const stopDowntime = () => monitoringHelper.redisClient.del(trackTokenHash.downtimeToken);
  const appAutomateStopDowntime =
    () => monitoringHelper.redisClient.del(appAutomateTrackTokenHash.downtimeToken);
  const setResponseCounts = (response200Counts, responseNon200Counts) => Promise.all([
    monitoringHelper.redisClient.set(trackTokenHash.response200Token, response200Counts),
    monitoringHelper.redisClient.set(trackTokenHash.responseNon200Token, responseNon200Counts),
  ]);
  const setAppAutomateResponseCounts = (response200Counts, responseNon200Counts) => Promise.all([
    monitoringHelper.redisClient.set(appAutomateTrackTokenHash.response200Token, response200Counts),
    monitoringHelper.redisClient.set(appAutomateTrackTokenHash.responseNon200Token, responseNon200Counts),
  ]);
  const addToTrackSet = () => Promise.all([
    monitoringHelper.redisClient.sadd(trackTokenHash.queueTrackRequestsSetTag, 'test:123'),
    monitoringHelper.redisClient.sadd(appAutomateTrackTokenHash.queueTrackRequestsSetTag, 'test:123'),
  ]);
  const clearTrackSet = () => Promise.all([
    monitoringHelper.redisClient.del(trackTokenHash.queueTrackRequestsSetTag),
    monitoringHelper.redisClient.del(appAutomateTrackTokenHash.queueTrackRequestsSetTag),
  ]);
  const setNtaCounts = (
    desktopSoftNtaArr,
    desktopHardNtaArr,
    deviceSoftNtaArr,
    deviceHardNtaArr
  ) => {
    const tokenKeyHash = {
      ntaSoftDesktopTokenHash: desktopSoftNtaArr,
      ntaHardDesktopTokenHash: desktopHardNtaArr,
      ntaSoftMobileTokenHash: deviceSoftNtaArr,
      ntaHardMobileTokenHash: deviceHardNtaArr,
    };

    return Promise.all(Object.keys(tokenKeyHash).map(tokenKey => new Promise((resolve) => {
      monitoringHelper.redisClient.del(trackTokenHash[tokenKey]).then(() => {
        Promise.all(tokenKeyHash[tokenKey].map(eachNtaValue => monitoringHelper.redisClient.hincrby(trackTokenHash[tokenKey], `${eachNtaValue}:${tokenKey}`, eachNtaValue))).then(resolve);
      });
    })));
  };

  before(() => {
    sinon.stub(monitoringHelper, 'getPacketLoss').returns(10);
  });

  after(() => {
    monitoringHelper.getPacketLoss.restore();
  });

  describe('test behaviour of helper methods', () => {
    beforeEach((done) => {
      addToTrackSet().then(() => {
        monitoringHelper.redisClient.del(constants.monitoring.redisWatchKey).then(() => {
          done();
        });
      });
    });

    afterEach((done) => {
      clearTrackSet().then(() => {
        monitoringHelper.redisClient.del(constants.monitoring.redisWatchKey).then(() => {
          done();
        });
      });
    });

    ['automate', 'appAutomate'].forEach((product) => {
      it(`Should check the members of the trackset for ${product}`, (done) => {
        sinon.spy(monitoringHelper.redisClient, 'smembers');
        monitoringHelper.checkStaleRequestsInTrackSet(product).then(() => {
          assert(monitoringHelper.redisClient.smembers.calledOnce === true);
          monitoringHelper.redisClient.smembers.restore();
          done();
        });
      });
    });

    it('should initialize logger', (done) => {
      const x = sinon.stub(winston, 'configure');
      monitoringHelper.initializeLogger();
      assert(x.calledOnce === true);
      winston.configure.restore();
      done();
    });

    it('should initialize logger for production environment', (done) => {
      const x = sinon.stub(winston, 'configure');
      monitoringHelper.isProductionEnv = true;
      monitoringHelper.initializeLogger();
      assert(x.calledOnce === true);
      winston.configure.restore();
      done();
    });

    it('should send alert if redis fails', (done) => {
      const x = sinon.spy(monitoringHelper, 'sendAlerts');
      monitoringHelper.redisClient.del(constants.monitoring.verifyRedisAliveKey).then(() => {
        monitoringHelper.readFromRedis().then(() => {
          assert(x.calledWith('Timestamp key is older than 5 seconds.') === true);
          monitoringHelper.sendAlerts.restore();
          done();
        });
      });
    });
    [
      { product: 'automate', state: 'penalized' }, { product: 'automate', state: 'unrestored' },
      { product: 'appAutomate', state: 'penalized' }, { product: 'appAutomate', state: 'unrestored' },
    ].forEach((stateObj) => {
      it(`should check if pipeline is ${stateObj.state} and clear trackset for ${stateObj.product}`, (done) => {
        monitoringHelper.currentPipelineState[stateObj.product]
          = constants.monitoring.pipelineState[stateObj.state];
        const x = sinon.spy(monitoringHelper, 'clearTrackSet');
        monitoringHelper.checkPipelineStateAndClearTrackSet(stateObj.product).then(() => {
          assert(x.calledOnce === true);
          assert(monitoringHelper.clearTrackSet.calledWith(stateObj.product) === true);
          monitoringHelper.clearTrackSet.restore();
          done();
        });
      });
    });
    [
      { product: 'automate', threshold: '256' },
      { product: 'appAutomate', threshold: '256' },
    ].forEach((productSpecificThreshold) => {
      const { product, threshold } = productSpecificThreshold;
      it(`should not clear trackset for ${product} if pipeline state in memory is restored and maxRequests is ${threshold}`, (done) => {
        monitoringHelper.currentPipelineState[product]
          = constants.monitoring.pipelineState.restored;
        const y = sinon.stub(monitoringHelper.redisClient, 'get').returns(threshold);
        const x = sinon.spy(monitoringHelper, 'clearTrackSet');
        const z = sinon.spy(monitoringHelper, 'validatePipelineMemoryAndRedisState');
        monitoringHelper.checkPipelineStateAndClearTrackSet(product).then(() => {
          assert(x.notCalled === true);
          assert(y.called === true);
          assert(z.calledOnce === true);
          monitoringHelper.clearTrackSet.restore();
          monitoringHelper.redisClient.get.restore();
          monitoringHelper.validatePipelineMemoryAndRedisState.restore();
          done();
        });
      });
    });
    [
      { product: 'automate', threshold: '2' },
      { product: 'appAutomate', threshold: '2' },
    ].forEach((productSpecificThreshold) => {
      const { product, threshold } = productSpecificThreshold;
      it(`should clear trackset for ${product} if pipeline state in memory is restored and maxRequests is ${threshold}`, (done) => {
        monitoringHelper.currentPipelineState[product]
          = constants.monitoring.pipelineState.restored;
        const y = sinon.stub(monitoringHelper.redisClient, 'get').returns(threshold);
        const x = sinon.spy(monitoringHelper, 'clearTrackSet');
        const z = sinon.spy(monitoringHelper, 'validatePipelineMemoryAndRedisState');
        monitoringHelper.checkPipelineStateAndClearTrackSet(product).then(() => {
          assert(x.called === true);
          assert(y.called === true);
          assert(z.calledOnce === true);
          monitoringHelper.clearTrackSet.restore();
          monitoringHelper.redisClient.get.restore();
          monitoringHelper.validatePipelineMemoryAndRedisState.restore();
          done();
        });
      });
    });
    ['automate', 'appAutomate'].forEach((product) => {
      it(`should delete trackset for ${product}`, (done) => {
        const x = sinon.spy(monitoringHelper.redisClient, 'del');
        const tokenHash = product === 'automate' ? trackTokenHash : appAutomateTrackTokenHash;
        monitoringHelper.clearTrackSet(product).then(() => {
          assert(x.calledOnce === true);
          monitoringHelper.redisClient.scard(tokenHash.queueTrackRequestsSetTag).then((res) => {
            assert(res === 0);
            monitoringHelper.redisClient.del.restore();
            done();
          });
        });
      });
    });
    it('should catch error if trackset delete fails for automate', (done) => {
      const x = sinon.stub(monitoringHelper.redisClient, 'del').returns(Promise.reject());
      monitoringHelper.clearTrackSet(logTag).then(() => {
        assert(x.calledOnce === true);
        monitoringHelper.redisClient.scard(trackTokenHash.queueTrackRequestsSetTag).then((res) => {
          assert(res === 1);
          monitoringHelper.redisClient.del.restore();
          done();
        });
      });
    });
    it('should delete trackset for app automate', (done) => {
      const x = sinon.spy(monitoringHelper.redisClient, 'del');
      monitoringHelper.clearTrackSet(appAutomateLogTag).then(() => {
        assert(x.calledOnce === true);
        monitoringHelper.redisClient.scard(appAutomateTrackTokenHash.queueTrackRequestsSetTag)
          .then((res) => {
            assert(res === 0);
            monitoringHelper.redisClient.del.restore();
            done();
          });
      });
    });
    it('should send alert if redis get fails', (done) => {
      const x = sinon.spy(monitoringHelper, 'sendAlerts');
      const z = sinon.stub(monitoringHelper.redisClient, 'get').returns(Promise.reject());
      monitoringHelper.readFromRedis().then(() => {
        assert(x.calledOnce === true);
        assert(z.calledOnce === true);
        monitoringHelper.redisClient.get.restore();
        monitoringHelper.sendAlerts.restore();
        done();
      });
    });
    it('should send acks', (done) => {
      monitoringHelper.redisClient.hget([constants.monitoring.redisWatchKey, 'timestamp']).then((res) => {
        assert(res == null);
      }).then(() => {
        monitoringHelper.ackAlive().then(() => {
          monitoringHelper.redisClient.hget([constants.monitoring.redisWatchKey, 'timestamp']).then((res) => {
            assert(res != null);
            done();
          });
        });
      });
    });
    it('ackAlive should catch error if redis call fails', (done) => {
      const x = sinon.stub(monitoringHelper.redisClient, 'hset').returns(Promise.reject(new Error('Error')));
      monitoringHelper.ackAlive().then(() => {
        assert(x.calledOnce === true);
        monitoringHelper.redisClient.hset.restore();
        done();
      });
    });

    it('should write to redis', (done) => {
      monitoringHelper.writeToRedis().then(() => {
        monitoringHelper.redisClient.get(constants.monitoring.verifyRedisAliveKey).then((res) => {
          assert(res != null);
          done();
        });
      });
    });
    it('should write to redis should send alert if set fails', (done) => {
      const originalClient = monitoringHelper.redisClient;
      monitoringHelper.redisClient = {
        set: () => Promise.reject(),
      };
      const y = sinon.stub(monitoringHelper, 'sendAlerts');
      monitoringHelper.writeToRedis().then(() => {
        assert(y.calledOnce === true);
        monitoringHelper.sendAlerts.restore();
        monitoringHelper.redisClient = originalClient;
        done();
      });
    });
    it('should reset automate pipeline counts', (done) => {
      monitoringHelper.resetPipelineCounts('automate').then(() => {
        monitoringHelper.redisClient
          .get(constants.railsPipeline.redisAutomateTrackTokenHash.queueCountMaxTag)
          .then((res) => {
            assert.equal(res, '256');
            done();
          });
      });
    });
    it('should reset app automate pipeline counts', (done) => {
      monitoringHelper.resetPipelineCounts('appAutomate').then(() => {
        monitoringHelper.redisClient
          .get(constants.railsPipeline.redisAppAutomateTrackTokenHash.queueCountMaxTag)
          .then((res) => {
            assert.equal(res, '256');
            done();
          });
      });
    });
    it('should reset pipeline and update the current state', (done) => {
      const resetPipelineFunction = sinon.spy(monitoringHelper, 'resetPipelineCounts');
      monitoringHelper.currentPipelineState[logTag] = PIPELINE_STATE.penalized;
      monitoringHelper.resetPipelineAndRestoreCurrentState(logTag).then(() => {
        assert(resetPipelineFunction.calledOnce === true);
        assert(monitoringHelper.currentPipelineState[logTag] === PIPELINE_STATE.restored);
        assert(typeof monitoringHelper.pipelineStateChangeTimeStamp[logTag] === 'object');
        monitoringHelper.resetPipelineCounts.restore();
        done();
      });
    });
    it('should not reset pipeline in case resetPipelineCounts throws error', (done) => {
      const resetPipelineFunction = sinon.stub(monitoringHelper, 'resetPipelineCounts').returns(Promise.reject());
      const winstonError = sinon.spy(winston, 'error');
      monitoringHelper.currentPipelineState[logTag] = PIPELINE_STATE.penalized;
      monitoringHelper.resetPipelineAndRestoreCurrentState(logTag).then(() => {
        assert(resetPipelineFunction.calledOnce === true);
        assert(monitoringHelper.currentPipelineState[logTag] === PIPELINE_STATE.penalized);
        assert(typeof monitoringHelper.pipelineStateChangeTimeStamp[logTag] === 'object');
        assert(winstonError.calledOnce === true);
        winston.error.restore();
        monitoringHelper.resetPipelineCounts.restore();
        done();
      });
    });
    it('should ensure pipeline is restored when (memory and redis client) state is penalized', (done) => {
      const resetPipelineAndRestoreCurrentStateFunction = sinon.spy(monitoringHelper, 'resetPipelineAndRestoreCurrentState');
      monitoringHelper.currentPipelineState[logTag] = PIPELINE_STATE.penalized;
      const x = sinon.stub(monitoringHelper.redisClient, 'get').returns('2');
      const y = sinon.spy(monitoringHelper, 'validatePipelineMemoryAndRedisState');
      monitoringHelper.pipelineStateChangeTimeStamp[logTag] = new Date() - (20 * 60 * 1000);
      monitoringHelper.ensurePipelineRestored(logTag).then(() => {
        assert(resetPipelineAndRestoreCurrentStateFunction.calledOnce === true);
        assert(x.calledOnce === true);
        assert(y.calledOnce === true);
        monitoringHelper.resetPipelineAndRestoreCurrentState.restore();
        monitoringHelper.redisClient.get.restore();
        monitoringHelper.validatePipelineMemoryAndRedisState.restore();
        done();
      });
    });
    it('should ensure pipeline is not restored when (memory and redis client) state is restored ', (done) => {
      const resetPipelineAndRestoreCurrentStateFunction = sinon.spy(monitoringHelper, 'resetPipelineAndRestoreCurrentState');
      monitoringHelper.currentPipelineState[logTag] = PIPELINE_STATE.restored;
      const x = sinon.stub(monitoringHelper.redisClient, 'get').returns('256');
      const y = sinon.spy(monitoringHelper, 'validatePipelineMemoryAndRedisState');
      monitoringHelper.pipelineStateChangeTimeStamp[logTag] = new Date() - (20 * 60 * 1000);
      monitoringHelper.ensurePipelineRestored(logTag).then(() => {
        assert(resetPipelineAndRestoreCurrentStateFunction.calledOnce === false);
        assert(x.calledOnce === true);
        assert(y.calledOnce === true);
        monitoringHelper.resetPipelineAndRestoreCurrentState.restore();
        monitoringHelper.redisClient.get.restore();
        monitoringHelper.validatePipelineMemoryAndRedisState.restore();
        done();
      });
    });
    it('should ensure pipeline is restored when memory state is restored and redis state is penalized', (done) => {
      const resetPipelineAndRestoreCurrentStateFunction = sinon.spy(monitoringHelper, 'resetPipelineAndRestoreCurrentState');
      monitoringHelper.currentPipelineState[logTag] = PIPELINE_STATE.restored;
      const x = sinon.stub(monitoringHelper.redisClient, 'get').returns('2');
      const y = sinon.spy(monitoringHelper, 'validatePipelineMemoryAndRedisState');
      monitoringHelper.pipelineStateChangeTimeStamp[logTag] = new Date() - (20 * 60 * 1000);
      monitoringHelper.ensurePipelineRestored(logTag).then(() => {
        assert(resetPipelineAndRestoreCurrentStateFunction.calledOnce === true);
        assert(x.calledOnce === true);
        assert(y.calledOnce === true);
        monitoringHelper.resetPipelineAndRestoreCurrentState.restore();
        monitoringHelper.redisClient.get.restore();
        monitoringHelper.validatePipelineMemoryAndRedisState.restore();
        done();
      });
    });
    it('should trigger pipeline state change and set current pipeline to penalized', (done) => {
      monitoringHelper.currentPipelineState[logTag] = PIPELINE_STATE.restored;
      const x = sinon.spy(monitoringHelper, 'clearTrackSet');
      monitoringHelper.triggerPipelineStateChange(logTag, 64, 2).then(() => {
        assert(monitoringHelper.currentPipelineState[logTag] === PIPELINE_STATE.penalized);
        assert(typeof monitoringHelper.pipelineStateChangeTimeStamp[logTag] === 'object');
        assert(x.calledOnce === true);
        monitoringHelper.clearTrackSet.restore();
        done();
      });
    });
    it('should trigger pipeline state change and set current pipeline to restored', (done) => {
      monitoringHelper.currentPipelineState[logTag] = PIPELINE_STATE.penalized;
      monitoringHelper.triggerPipelineStateChange(logTag, 233, 256).then(() => {
        assert(monitoringHelper.currentPipelineState[logTag] === PIPELINE_STATE.restored);
        assert(typeof monitoringHelper.pipelineStateChangeTimeStamp[logTag] === 'object');
        done();
      });
    });
    it('should verify async master when redis results undefined', (done) => {
      const x = sinon.stub(monitoringHelper, 'stopFurtherProcessing').returns(Promise.resolve());
      const y = sinon.stub(monitoringHelper.redisClient, 'hget').returns(Promise.resolve(undefined));
      monitoringHelper.verifyMaster().then(() => {
        assert(x.calledOnce);
        assert(y.calledOnce);
        monitoringHelper.stopFurtherProcessing.restore();
        monitoringHelper.redisClient.hget.restore();
        done();
      });
    });
    it('should verify async master should not stop further processing with right res', (done) => {
      const x = sinon.stub(monitoringHelper, 'stopFurtherProcessing').returns(Promise.resolve());
      const y = sinon.stub(monitoringHelper.redisClient, 'hget').returns(Promise.resolve(process.pid.toString()));
      monitoringHelper.verifyMaster().then(() => {
        assert(x.calledOnce === false);
        assert(y.calledOnce === true);
        monitoringHelper.stopFurtherProcessing.restore();
        monitoringHelper.redisClient.hget.restore();
        done();
      });
    });
    it('async master should not throw error when redis resolves', (done) => {
      const x = sinon.stub(monitoringHelper, 'stopFurtherProcessing').returns(Promise.resolve());
      const y = sinon.stub(monitoringHelper.redisClient, 'hget').returns(Promise.reject());
      monitoringHelper.verifyMaster().then(() => {
        assert(x.calledOnce);
        assert(y.calledOnce);
        monitoringHelper.stopFurtherProcessing.restore();
        monitoringHelper.redisClient.hget.restore();
        done();
      });
    });
    it('async ensureOncePerDay should resolve to true if redis lock key is set', (done) => {
      const x = sinon.stub(monitoringHelper.redisClient, 'setnx').returns(Promise.resolve(1));
      monitoringHelper.ensureOncePerDay().then((result) => {
        assert(x.calledOnce);
        assert(result === true);
        monitoringHelper.redisClient.setnx.restore();
        done();
      });
    });
    it('async ensureOncePerDay should resolve to false if redis lock key is not set', (done) => {
      const x = sinon.stub(monitoringHelper.redisClient, 'setnx').returns(Promise.resolve(0));
      monitoringHelper.ensureOncePerDay().then((result) => {
        assert(x.calledOnce);
        assert(result === false);
        monitoringHelper.redisClient.setnx.restore();
        done();
      });
    });
    it('getGoHead should wait after receiving acknowledgement from master', (done) => {
      const errObj = { err: 'this is an error' };
      const x = sinon.stub(monitoringHelper.redisClient, 'becomeMaster').callsArgWith(1, errObj);
      const y = sinon.stub();
      const sandbox = sinon.sandbox.create();
      sandbox.stub(monitoringHelper, 'stop', true);
      sandbox.stub(constants.monitoring, 'pingForMasterInterval', 0);
      monitoringHelper.getGoAhead(y);
      assert(x.calledOnce);
      assert(y.calledOnce === false);
      sandbox.restore();
      monitoringHelper.redisClient.becomeMaster.restore();
      done();
    });
    it('getGoHead should execute callback after becoming master', (done) => {
      const res = 'I can become master';
      const x = sinon.stub(monitoringHelper.redisClient, 'becomeMaster').callsArgWith(1, undefined, res);
      const callback = sinon.spy();
      monitoringHelper.getGoAhead(callback);
      assert(x.calledOnce);
      assert(callback.calledOnce);
      monitoringHelper.redisClient.becomeMaster.restore();
      done();
    });
    it('processIntervalHook should set processIntervals', (done) => {
      monitoringHelper.processIntervalHook(1);
      assert(monitoringHelper.processIntervals, 1);
      done();
    });
    describe('rollbacks droppend sessions and sync those dropped sessions', () => {
      it('rollbacks dropped sessions', (done) => {
        const x = sinon.spy(monitoringHelper.redisClient, 'hincrby');
        monitoringHelper.rollbackDroppedSessions({ session1: 1, session2: 2 }).then(() => {
          assert(x.calledTwice === true);
          monitoringHelper.redisClient.hincrby.restore();
          done();
        });
      });
      it('syncs dropped sessions', (done) => {
        const y = sinon.spy(monitoringHelper, 'updateDroppedSessionOnRails');
        const x = sinon.spy(monitoringHelper.redisClient, 'multi');
        monitoringHelper.syncDroppedSessions().then(() => {
          assert(x.calledOnce === true);
          assert(y.calledOnce === true);
          monitoringHelper.updateDroppedSessionOnRails.restore();
          monitoringHelper.redisClient.multi.restore();
          done();
        });
      });
    });
    it('async updateDroppedSessionOnRails should not throw error when statusCode is 200', (done) => {
      const x = sinon.stub(requestlib, 'call').returns(Promise.resolve({ statusCode: 200 }));
      const y = sinon.stub(monitoringHelper, 'rollbackDroppedSessions').returns(Promise.resolve());
      monitoringHelper.updateDroppedSessionOnRails().then(() => {
        assert(x.calledOnce === true);
        assert(y.calledOnce === false);
        requestlib.call.restore();
        monitoringHelper.rollbackDroppedSessions.restore();
        done();
      });
    });
    it('async updateDroppedSessionOnRails should throw error when statusCode is not 200', (done) => {
      const x = sinon.stub(requestlib, 'call').returns(Promise.resolve({ statusCode: 201 }));
      const y = sinon.stub(monitoringHelper, 'rollbackDroppedSessions').returns(Promise.resolve());
      monitoringHelper.updateDroppedSessionOnRails().then(() => {
        assert(x.calledOnce === true);
        assert(y.calledOnce === true);
        requestlib.call.restore();
        monitoringHelper.rollbackDroppedSessions.restore();
        done();
      });
    });
    it('async syncDroppedSessions should not call updateDroppedSessionOnRails when function throws error', (done) => {
      const testResObj = { a: 1 };
      const x = sinon.stub(monitoringHelper.redisClient, 'multi').returns(Promise.resolve([[undefined, testResObj]]));
      const y = sinon.stub(monitoringHelper, 'updateDroppedSessionOnRails');
      monitoringHelper.syncDroppedSessions().then(() => {
        assert(x.calledOnce === true);
        assert(y.calledOnce === false);
        x.restore();
        y.restore();
        done();
      });
    });
  });

  describe('checks behaviour of pipeline after being stuck for more than 10 mins', () => {
    beforeEach((done) => {
      monitoringService.currentDowntimeState[logTag] =
        constants.monitoringDowntimeStates.runningState;
      setResponseCounts(0, 0)
        .then(setNtaCounts.bind(null, [], [], [], []))
        .then(monitoringHelper.redisClient.set(trackTokenHash.queueCountMaxTag, 256))
        .then(done.bind(null, null));
    });

    it('should trigger pipeline state change for manipulative action of division and set the current pipeline state to undefined', (done) => {
      setResponseCounts(0, 500).then(() => {
        stubClock();
        monitoringHelper.currentPipelineState[logTag] = PIPELINE_STATE.restored;
        monitoringHelper.triggerPipelineStateChange(logTag, 256, 64).then(() => {
          assert(monitoringHelper.currentPipelineState[logTag] === PIPELINE_STATE.unrestored);
          clock.restore();
          done();
        });
      });
    });

    it('should ensure pipeline is restored afer being stuck on 64 for 10 minutes', (done) => {
      const resetPipelineAndRestoreCurrentState = sinon.spy(monitoringHelper, 'resetPipelineAndRestoreCurrentState');
      const sendAlerts = sinon.spy(monitoringHelper, 'sendAlerts');
      const subject = `[${logTag}][Stuck after being in between restored and penalized state] Rails pipeline: Unable to restore completely in 10 minutes. Resetting pipeline counts.`;
      monitoringHelper.ensurePipelineRestored(logTag).then(() => {
        assert(resetPipelineAndRestoreCurrentState.calledOnce === true);
        assert(sendAlerts.calledWith(subject) === true);
        monitoringHelper.resetPipelineAndRestoreCurrentState.restore();
        monitoringHelper.sendAlerts.restore();
        done();
      });
    });

    it('should trigger pipeline state change for manipulative action of division and set the current pipeline state to penalized', (done) => {
      setResponseCounts(0, 500).then(() => {
        monitoringHelper.currentPipelineState[logTag] = PIPELINE_STATE.restored;
        stubClock();
        const x = sinon.spy(monitoringHelper, 'clearTrackSet');
        monitoringHelper.triggerPipelineStateChange(logTag, 4, 2).then(() => {
          assert(monitoringHelper.currentPipelineState[logTag] === PIPELINE_STATE.penalized);
          assert(x.calledOnce === true);
          monitoringHelper.clearTrackSet.restore();
          clock.restore();
          done();
        });
      });
    });

    it('should ensure pipeline is restored afer being stuck on 2 for 10 minutes', (done) => {
      const resetPipelineAndRestoreCurrentState = sinon.spy(monitoringHelper, 'resetPipelineAndRestoreCurrentState');
      const sendAlerts = sinon.spy(monitoringHelper, 'sendAlerts');
      const subject = `[${logTag}][Stuck after being in penalized state] Rails pipeline: Unable to restore completely in 10 minutes. Resetting pipeline counts.`;
      monitoringHelper.ensurePipelineRestored(logTag).then(() => {
        assert(resetPipelineAndRestoreCurrentState.calledOnce === true);
        assert(sendAlerts.calledWith(subject) === true);
        monitoringHelper.resetPipelineAndRestoreCurrentState.restore();
        monitoringHelper.sendAlerts.restore();
        done();
      });
    });

    it('should trigger pipeline state change for manipulative action of division and do not modify the current pipeline state', (done) => {
      setResponseCounts(0, 500).then(() => {
        monitoringHelper.currentPipelineState[logTag] = PIPELINE_STATE.unrestored;
        stubClock();
        monitoringHelper.triggerPipelineStateChange(logTag, 2, 3).then(() => {
          assert(monitoringHelper.currentPipelineState[logTag] === PIPELINE_STATE.unrestored);
          clock.restore();
          done();
        });
      });
    });

    it('should trigger pipeline state change for manipulative action of division and modify current pipeline state to restored', (done) => {
      setResponseCounts(0, 500).then(() => {
        monitoringHelper.currentPipelineState[logTag] = PIPELINE_STATE.unrestored;
        stubClock();
        monitoringHelper.triggerPipelineStateChange(logTag, 251, 256).then(() => {
          assert(monitoringHelper.currentPipelineState[logTag] === PIPELINE_STATE.restored);
          clock.restore();
          done();
        });
      });
    });
  });

  describe('checks whether down time has been enabled and checks max queue count tag for automate', () => {
    beforeEach((done) => {
      monitoringService.currentDowntimeState[logTag] =
        constants.monitoringDowntimeStates.runningState;
      setResponseCounts(0, 0)
        .then(setNtaCounts.bind(null, [], [], [], []))
        .then(startForcedDowntime)
        .then(done.bind(null, null));
    });

    afterEach((done) => {
      stopDowntime()
        .then(done.bind(null, null));
    });

    it('checks if forced downtime has been enabled for automate', (done) => {
      monitoringHelper.redisClient.get(trackTokenHash.downtimeToken).then((res) => {
        assert(res === 'forcedZero');
        done();
      });
    });

    it('checks max queue count tag for automate', (done) => {
      sinon.spy(monitoringService.monitoringHelper.redisClient, 'del');
      monitoringService.checkDowntimeEnabled(trackTokenHash, logTag).then(() => {
        monitoringHelper.redisClient.get(trackTokenHash.queueCountMaxTag).then((res) => {
          assert(monitoringService.monitoringHelper.redisClient.del
            .calledWith(trackTokenHash.queueTrackRequestsSetTag) === true);
          monitoringService.monitoringHelper.redisClient.del.restore();
          assert(res === '0');
          done();
        });
      });
    });
  });

  describe('checks whether down time has been enabled and checks max queue count tag for app automate', () => {
    beforeEach((done) => {
      monitoringService.currentDowntimeState[appAutomateLogTag] =
        constants.monitoringDowntimeStates.runningState;
      setResponseCounts(0, 0)
        .then(setNtaCounts.bind(null, [], [], [], []))
        .then(appAutomateStartForcedDownTime)
        .then(done.bind(null, null));
    });

    afterEach((done) => {
      appAutomateStopDowntime()
        .then(done.bind(null, null));
    });

    it('checks if forced downtime has been enabled for app automate', (done) => {
      monitoringHelper.redisClient.get(appAutomateTrackTokenHash.downtimeToken).then((res) => {
        assert(res === 'forcedZero');
        done();
      });
    });

    it('checks max queue count tag of app automate', (done) => {
      sinon.spy(monitoringService.monitoringHelper.redisClient, 'del');
      monitoringService.checkDowntimeEnabled(appAutomateTrackTokenHash, appAutomateLogTag)
        .then(() => {
          monitoringHelper.redisClient.get(appAutomateTrackTokenHash.queueCountMaxTag).then((res) => {
            assert(monitoringService.monitoringHelper.redisClient.del
              .calledWith(appAutomateTrackTokenHash.queueTrackRequestsSetTag) === true);
            assert(res === '0');
            monitoringService.monitoringHelper.redisClient.del.restore();
            done();
          });
        });
    });
  });

  describe('behaviour of rails hub pipeline on forced automate downtime', () => {
    beforeEach((done) => {
      monitoringService.currentDowntimeState[appAutomateLogTag] =
        constants.monitoringDowntimeStates.runningState;
      setResponseCounts(0, 0)
        .then(setNtaCounts.bind(null, [], [], [], []))
        .then(startForcedDowntime)
        .then(done.bind(null, null));
    });

    afterEach((done) => {
      monitoringService.currentState = 1;
      stopDowntime()
        .then(done.bind(null, null));
    });

    it('should check if down time is enabled for automate', (done) => {
      monitoringService.checkDowntimeEnabled(trackTokenHash, logTag).then((res) => {
        assert(res === constants.monitoringDowntimeStates.forcedDowntimeState);
        done();
      });
    });

    it('should check max queue count tag for automate', (done) => {
      monitoringHelper.redisClient.get(trackTokenHash.queueCountMaxTag)
        .then((res) => {
          assert(res === '0');
          done();
        });
    });

    it('should check down time is not enabled for app automate', (done) => {
      monitoringService.checkDowntimeEnabled(appAutomateTrackTokenHash, appAutomateLogTag)
        .then((res) => {
          assert(res !== constants.monitoringDowntimeStates.forcedDowntimeState);
          done();
        });
    });

    describe('should not call ensure pipeline restored for automate', () => {
      it('should set app automate redis key', (done) => {
        monitoringHelper.redisClient.set(appAutomateTrackTokenHash.queueCountMaxTag, '8');
        monitoringHelper.redisClient.get(appAutomateTrackTokenHash.queueCountMaxTag).then((res) => {
          assert(res === '8');
          done();
        });
      });

      it('should manipulate pipeline for app automate and set the state to penalized', (done) => {
        stubClock();
        const x = sinon.stub(monitoringService.redisClient, 'get');
        setAppAutomateResponseCounts(0, 1000).then(() => {
          monitoringService.manipulatePipeline(appAutomateTrackTokenHash, appAutomateLogTag)
            .then(() => {
              assert(x.calledTwice === true);
              x.restore();
              clock.restore();
              done();
            });
        });
      });
      it('should manipulate pipeline should catch errors', (done) => {
        const x = sinon.stub(monitoringService, 'getManipulativeAction').returns(Promise.reject());
        sinon.stub(monitoringService.redisClient, 'get').returns(10);
        monitoringService.manipulatePipeline(appAutomateTrackTokenHash, appAutomateLogTag)
          .then(() => {
            assert(x.calledOnce === true);
            monitoringService.getManipulativeAction.restore();
            monitoringService.redisClient.get.restore();
            done();
          });
      });
      it('should check if app automate pipeline has been penalized', (done) => {
        const x = sinon.spy(monitoringService.redisClient, 'smembers');
        monitoringHelper.redisClient.get(appAutomateTrackTokenHash.queueCountMaxTag).then((res) => {
          assert(res === '2');
          monitoringService.manipulatePipeline(appAutomateTrackTokenHash, 'appAutomate')
            .then(() => {
              assert(x.calledOnce === true);
              monitoringService.redisClient.smembers.restore();
              done();
            });
        });
      });

      it('should reset pipeline for app automate', (done) => {
        const checkDowntimeEnabled = sinon.spy(monitoringService, 'checkDowntimeEnabled');
        monitoringService.processPerMinute().then(() => {
          assert(checkDowntimeEnabled.calledTwice === true);
          monitoringHelper.redisClient.get(appAutomateTrackTokenHash.queueCountMaxTag)
            .then((res) => {
              assert(res === '256');
              done();
            });
          monitoringService.checkDowntimeEnabled.restore();
        });
      });

      it('should check max queue count tag for automate', (done) => {
        monitoringHelper.redisClient.get(trackTokenHash.queueCountMaxTag)
          .then((res) => {
            assert(res === '0');
            done();
          });
      });
    });
  });

  describe('behaviour of rails hub pipeline on forced app automate downtime', () => {
    beforeEach((done) => {
      monitoringService.currentDowntimeState[logTag] =
        constants.monitoringDowntimeStates.runningState;
      setResponseCounts(0, 0)
        .then(setNtaCounts.bind(null, [], [], [], []))
        .then(appAutomateStartForcedDownTime)
        .then(done.bind(null, null));
    });

    afterEach((done) => {
      monitoringService.currentState = 1;
      appAutomateStopDowntime()
        .then(done.bind(null, null));
    });

    it('should check if down time is enabled for app automate', (done) => {
      monitoringService.checkDowntimeEnabled(appAutomateTrackTokenHash, appAutomateLogTag)
        .then((res) => {
          assert(res === constants.monitoringDowntimeStates.forcedDowntimeState);
          done();
        });
    });

    it('should check max queue count tag for app automate', (done) => {
      monitoringHelper.redisClient.get(appAutomateTrackTokenHash.queueCountMaxTag)
        .then((res) => {
          assert(res === '0');
          done();
        });
    });

    it('should check down time is not enabled for automate', (done) => {
      monitoringService.checkDowntimeEnabled(trackTokenHash, logTag)
        .then((res) => {
          assert(res !== constants.monitoringDowntimeStates.forcedDowntimeState);
          done();
        });
    });

    describe('should not call ensure pipeline restored for app automate', () => {
      it('should set automate redis key', (done) => {
        monitoringHelper.redisClient.set(trackTokenHash.queueCountMaxTag, '8');
        monitoringHelper.redisClient.get(trackTokenHash.queueCountMaxTag).then((res) => {
          assert(res === '8');
          done();
        });
      });

      it('should manipulate pipeline for automate and set the state to penalized', (done) => {
        setResponseCounts(0, 1000).then(() => {
          stubClock();
          const x = sinon.spy(monitoringService.redisClient, 'get');
          monitoringService.manipulatePipeline(trackTokenHash, logTag)
            .then(() => {
              assert(x.calledTwice === true);
              clock.restore();
              x.restore();
              done();
            });
        });
      });

      it('should check if automate pipeline has been penalized', (done) => {
        const x = sinon.spy(monitoringService.redisClient, 'smembers');
        monitoringHelper.redisClient.get(trackTokenHash.queueCountMaxTag).then((res) => {
          assert(res === '2');
          monitoringService.manipulatePipeline(trackTokenHash, logTag)
            .then(() => {
              assert(x.calledOnce === true);
              monitoringService.redisClient.smembers.restore();
              done();
            });
        });
      });

      it('should reset pipeline for automate', (done) => {
        monitoringService.processPerMinute().then(() => {
          monitoringHelper.redisClient.get(trackTokenHash.queueCountMaxTag)
            .then((res) => {
              assert(res === '256');
              done();
            });
        });
      });

      it('should check max queue count tag for app automate', (done) => {
        monitoringHelper.redisClient.get(appAutomateTrackTokenHash.queueCountMaxTag)
          .then((res) => {
            assert(res === '0');
            done();
          });
      });
    });
  });

  describe('behaviour on firtNon200 downtime', () => {
    beforeEach((done) => {
      monitoringService.currentDowntimeState[logTag] =
        constants.monitoringDowntimeStates.runningState;
      // Reset all counts before each test
      setResponseCounts(0, 0)
        .then(setNtaCounts.bind(null, [], [], [], []))
        .then(startNon200Downtime)
        .then(monitoringHelper.redisClient.set(trackTokenHash.queueCountMaxTag, 256))
        .then(done.bind(null, null));
    });

    afterEach((done) => {
      monitoringService.currentState = 1;
      stopDowntime()
        .then(done.bind(null, null));
    });

    it('performs restore action on all 200', (done) => {
      setResponseCounts(500, 0).then(() => {
        monitoringService.getManipulativeAction(trackTokenHash, logTag).then((actionParams) => {
          actionParams.action.should.equal('multiply');
          actionParams.factor.should.equal(1.1);
          done();
        });
      });
    });

    it('performs restore action on all 200 new flow', (done) => {
      setResponseCounts(500, 0, 20).then(() => {
        monitoringService.getManipulativeAction(trackTokenHash, logTag, 20).then((actionParams) => {
          actionParams.action.should.equal('multiply');
          actionParams.factor.should.equal(1.5);
          done();
        });
      });
    });

    it('changes state to forcedDowntime on first non 200', (done) => {
      setResponseCounts(500, 1).then(() => {
        monitoringService.getManipulativeAction(trackTokenHash, logTag)
          .then(actionParams => new Promise((resolve) => {
            (actionParams.action === null).should.equal(true);
            monitoringService.currentDowntimeState[logTag].should
              .equal(constants.monitoringDowntimeStates.firstNon200State);
            monitoringHelper.redisClient.get(trackTokenHash.queueCountMaxTag)
              .then((maxRequests) => {
                maxRequests.should.equal('256');
                resolve();
              });
          }))
          .then(() => new Promise((resolve) => {
            monitoringService.getManipulativeAction(trackTokenHash, logTag)
              .then(resolve);
          }))
          .then(actionParams => new Promise((resolve) => {
            (actionParams.action === null).should.equal(true);
            monitoringService.currentDowntimeState[logTag].should
              .equal(constants.monitoringDowntimeStates.forcedDowntimeState);
            monitoringHelper.redisClient.get(trackTokenHash.queueCountMaxTag)
              .then((maxRequests) => {
                maxRequests.should.equal('0');
                resolve();
              });
          }))
          .then(done);
      });
    });
  });

  describe('behaviour on forced downtime', () => {
    beforeEach((done) => {
      monitoringService.currentDowntimeState[logTag] =
        constants.monitoringDowntimeStates.runningState;
      // Reset all counts before each test
      setResponseCounts(0, 0)
        .then(setNtaCounts.bind(null, [], [], [], []))
        .then(startForcedDowntime)
        .then(monitoringHelper.redisClient.set(trackTokenHash.queueCountMaxTag, 256))
        .then(done.bind(null, null));
    });

    afterEach((done) => {
      monitoringService.currentState = 1;
      stopDowntime()
        .then(done.bind(null, null));
    });

    it('does not perform any action on all 200', (done) => {
      setResponseCounts(500, 0).then(() => {
        monitoringService.getManipulativeAction(trackTokenHash, logTag).then((actionParams) => {
          (actionParams.action === null).should.equal(true);
          done();
        });
      });
    });

    it('sets max allowed key to 0', (done) => {
      setResponseCounts(500, 0).then(() => {
        monitoringService.getManipulativeAction(trackTokenHash, logTag).then(() => {
          monitoringHelper.redisClient.get(trackTokenHash.queueCountMaxTag)
            .then((maxRequests) => {
              maxRequests[0].should.equal('0');
              done();
            });
        });
      });
    });

    it('sets max allowed to min value on restoring downtime', (done) => {
      setResponseCounts(500, 0).then(() => {
        monitoringService.getManipulativeAction(trackTokenHash, logTag)
          .then(stopDowntime)
          .then(() => {
            setResponseCounts(500, 0).then(() => {
              monitoringService.getManipulativeAction(trackTokenHash, logTag)
                .then(() => {
                  monitoringHelper.redisClient.get(trackTokenHash.queueCountMaxTag)
                    .then((maxRequests) => {
                      maxRequests[0].should.equal('2');
                      done();
                    });
                });
            });
          });
      });
    });

    it('performs restoring action on restoring downtime', (done) => {
      setResponseCounts(500, 0).then(() => {
        monitoringService.getManipulativeAction(trackTokenHash, logTag)
          .then(stopDowntime)
          .then(() => {
            setResponseCounts(500, 0).then(() => {
              monitoringService.getManipulativeAction(trackTokenHash, logTag)
                .then((actionParams) => {
                  actionParams.action.should.equal('multiply');
                  actionParams.factor.should.equal(1.1);
                  done();
                });
            });
          });
      });
    });
  });

  describe('penalizes based on 200 and non 200 counts', () => {
    beforeEach((done) => {
      // Reset all counts before each test
      setResponseCounts(0, 0)
        .then(setNtaCounts.bind(null, [], [], [], []))
        .then(done.bind(null, null));
    });

    it('performs restore action on all 200', (done) => {
      setResponseCounts(500, 0).then(() => {
        monitoringService.getManipulativeAction(trackTokenHash, logTag).then((actionParams) => {
          actionParams.action.should.equal('multiply');
          actionParams.factor.should.equal(1.1);
          done();
        });
      });
    });

    it('performs penalize action on all non 200', (done) => {
      setResponseCounts(0, 500).then(() => {
        monitoringService.getManipulativeAction(trackTokenHash, logTag).then((actionParams) => {
          actionParams.action.should.equal('divide');
          actionParams.factor.should.equal(4);
          done();
        });
      });
    });

    it('performs penalize action on 1% non 200', (done) => {
      setResponseCounts(989, 11).then(() => {
        monitoringService.getManipulativeAction(trackTokenHash, logTag).then((actionParams) => {
          actionParams.action.should.equal('divide');
          actionParams.factor.should.equal(2);
          done();
        });
      });
    });

    it('performs no action on less than 1% -but nonzero non200', (done) => {
      setResponseCounts(995, 5).then(() => {
        monitoringService.getManipulativeAction(trackTokenHash, logTag).then((actionParams) => {
          (actionParams.action === null).should.equal(true);
          done();
        });
      });
    });

    it('performs less penalisation when 10% non200 but user specific errors', (done) => {
      const topUserErrorStub = sinon.stub(monitoringService, 'getTopUserErrorSum').returns(Promise.resolve({ topUserErrorSum: 110 }));
      setResponseCounts(900, 110).then(() => {
        monitoringService.getManipulativeAction(trackTokenHash, logTag).then((actionParams) => {
          actionParams.action.should.equal('divide');
          actionParams.factor.should.equal(2);
          topUserErrorStub.restore();
          done();
        });
      });
    });

    it('performs no penalisation when between 1% to 10% non200 but user specific errors', (done) => {
      const topUserErrorStub = sinon.stub(monitoringService, 'getTopUserErrorSum').returns(Promise.resolve({ topUserErrorSum: 20 }));
      setResponseCounts(900, 20).then(() => {
        monitoringService.getManipulativeAction(trackTokenHash, logTag).then((actionParams) => {
          (actionParams.action === null).should.equal(true);
          topUserErrorStub.restore();
          done();
        });
      });
    });
  });

  xdescribe('penalizes based on nta counts', () => {
    beforeEach((done) => {
      // Reset all counts before each test
      setResponseCounts(0, 0)
        .then(setNtaCounts.bind(null, [], [], [], []))
        .then(done.bind(null, null));
    });

    it('performs restore action on no ntas', (done) => {
      setResponseCounts(1, 0).then(setNtaCounts.bind(null, [], [], [], [])).then(() => {
        monitoringService.getManipulativeAction(trackTokenHash, logTag).then((actionParams) => {
          actionParams.action.should.equal('multiply');
          actionParams.factor.should.equal(1.1);
          done();
        });
      });
    });

    it('performs penalize action on all ntas', (done) => {
      setResponseCounts(100, 0).then(setNtaCounts.bind(null, [25], [25], [25], [25])).then(() => {
        monitoringService.getManipulativeAction(trackTokenHash, logTag).then((actionParams) => {
          actionParams.action.should.equal('divide');
          actionParams.factor.should.equal(4);
          done();
        });
      });
    });

    it('performs penalize action on 10% hard ntas', (done) => {
      setResponseCounts(100, 0).then(setNtaCounts.bind(null, [0], [5], [0], [5])).then(() => {
        monitoringService.getManipulativeAction(trackTokenHash, logTag).then((actionParams) => {
          actionParams.action.should.equal('divide');
          actionParams.factor.should.equal(4);
          done();
        });
      });
    });

    it('performs penalize action on 20% soft ntas', (done) => {
      setResponseCounts(100, 0).then(setNtaCounts.bind(null, [10], [0], [10], [0])).then(() => {
        monitoringService.getManipulativeAction(trackTokenHash, logTag).then((actionParams) => {
          actionParams.action.should.equal('divide');
          actionParams.factor.should.equal(4);
          done();
        });
      });
    });

    it('performs restore on ntas less than threshold', (done) => {
      setResponseCounts(100, 0).then(setNtaCounts.bind(null, [1], [0], [1], [0])).then(() => {
        monitoringService.getManipulativeAction(trackTokenHash, logTag).then((actionParams) => {
          actionParams.action.should.equal('multiply');
          actionParams.factor.should.equal(1.1);
          done();
        });
      });
    });
  });

  describe('test queueing stats sent to hoothoot', () => {
    it('should send count of queued sessions > 15 mins threshold(app-automate)', (done) => {
      const threshold = Date.now() - constants.monitoring.hoothootRequestQueueThreshold;
      monitoringHelper.redisClient.zadd([constants.monitoring.reqToTimeKeyAppAutomate, threshold + 10000, 'random_id_1']).then(() => {
        monitoringHelper.redisClient.zadd([constants.monitoring.reqToTimeKeyAppAutomate, threshold - 1000, 'random_id_2']).then(() => {
          monitoringHelper.redisClient.zadd([constants.monitoring.reqToTimeKeyAppAutomate, threshold - 2000, 'random_id_3']).then(() => {
            monitoringHelper.checkQueueCountHoothoot('app-automate').then((res) => {
              assert(res === 2);
              done();
            });
          });
        });
      });
    });

    it('should send count of queued sessions > 15 mins threshold(automate)', (done) => {
      const threshold = Date.now() - constants.monitoring.hoothootRequestQueueThreshold;
      monitoringHelper.redisClient.zadd([constants.monitoring.reqToTimeKeyAutomate, threshold + 10000, 'random_id_1']).then(() => {
        monitoringHelper.redisClient.zadd([constants.monitoring.reqToTimeKeyAutomate, threshold - 1000, 'random_id_2']).then(() => {
          monitoringHelper.redisClient.zadd([constants.monitoring.reqToTimeKeyAutomate, threshold - 2000, 'random_id_3']).then(() => {
            monitoringHelper.checkQueueCountHoothoot('automate').then((res) => {
              assert(res === 2);
              done();
            });
          });
        });
      });
    });

    ['automate', 'app-automate'].forEach((product) => {
      it(`should send pushover alert if pending sessions exceeded threshold for ${product}`, (done) => {
        const x = sinon.spy(monitoringHelper, 'sendAlerts');
        sinon.stub(hubHelper, 'hoothootPusher');
        monitoringHelper.checkPendingQueueStatsThreshold(null, 20, product).then(() => {
          assert(x.calledOnce === true);
          monitoringHelper.sendAlerts.restore();
          hubHelper.hoothootPusher.restore();
          done();
        });
      });
      it(`should not send pushover alert if pending sessions didn't exceed threshold for ${product}`, (done) => {
        const x = sinon.spy(monitoringHelper, 'sendAlerts');
        sinon.stub(hubHelper, 'hoothootPusher');
        monitoringHelper.checkPendingQueueStatsThreshold(null, 5, product).then(() => {
          assert(x.calledOnce === false);
          monitoringHelper.sendAlerts.restore();
          hubHelper.hoothootPusher.restore();
          done();
        });
      });
      it(`should reject if redis client fail while checking pending count for ${product}`, (done) => {
        const arr = [];
        const errObj = 'this is error';
        Promise.all(arr).then(() => {
          monitoringHelper.checkPendingQueueStatsThreshold(errObj, null, 'automate').then((err) => {
            assert.deepEqual(err, errObj);
            done();
          });
        });
      });
      it(`should push request id to hoothoot for ${product}`, (done) => {
        const requestIds = ['abcd', 'efgh', 'ijkl'];
        const x = sinon.spy(monitoringHelper, 'sendEventToAllPlatforms');
        monitoringHelper.pushRequestIdAsUniqueUser(null, requestIds, product).then(() => {
          assert(x.calledThrice === true);
          monitoringHelper.sendEventToAllPlatforms.restore();
          done();
        });
      });
      it(`should not push request id when err occurred for ${product}`, (done) => {
        const err = 'some error';
        const x = sinon.spy(monitoringHelper, 'sendEventToAllPlatforms');
        monitoringHelper.pushRequestIdAsUniqueUser(err, undefined, product).then((err1) => {
          assert(x.called === false);
          assert.equal(err, err1);
          monitoringHelper.sendEventToAllPlatforms.restore();
          done();
        });
      });
      it(`check queue count for ${product}`, (done) => {
        const x = sinon.spy(monitoringHelper, 'pushRequestIdAsUniqueUser');
        const y = sinon.spy(monitoringHelper, 'checkPendingQueueStatsThreshold');
        monitoringHelper.checkQueueCountHoothoot(product).then(() => {
          assert(x.calledOnce === true);
          assert(y.calledOnce === true);
          monitoringHelper.pushRequestIdAsUniqueUser.restore();
          monitoringHelper.checkPendingQueueStatsThreshold.restore();
          done();
        });
      });
      it(`checkQueueCount should not call methods in case of err for ${product}`, (done) => {
        const x = sinon.spy(monitoringHelper, 'pushRequestIdAsUniqueUser');
        const y = sinon.spy(monitoringHelper, 'checkPendingQueueStatsThreshold');
        // when in doubt regarding syntax for stubbing chain commands, just mock the whole class :p
        const mockRedis = {
          zrangebyscore() {
            return this;
          },
          zremrangebyscore() {
            return this;
          },
          exec(callback) {
            callback('some error', []);
          },
        };
        sinon.stub(monitoringHelper.redisClient, 'multi').returns(mockRedis);
        const z = sinon.spy(monitoringHelper.winston, 'error');
        monitoringHelper.checkQueueCountHoothoot(product).then(() => {
          assert(x.calledOnce === false);
          assert(y.calledOnce === false);
          assert(z.calledOnce === true);
          monitoringHelper.pushRequestIdAsUniqueUser.restore();
          monitoringHelper.redisClient.multi.restore();
          monitoringHelper.checkPendingQueueStatsThreshold.restore();
          monitoringHelper.winston.error.restore();
          done();
        });
      });
      it(`checkQueueCount should fail when redis results are malformed for ${product}`, (done) => {
        const x = sinon.spy(monitoringHelper, 'pushRequestIdAsUniqueUser');
        const y = sinon.spy(monitoringHelper, 'checkPendingQueueStatsThreshold');
        const mockRedis = {
          zrangebyscore() {
            return this;
          },
          zremrangebyscore() {
            return this;
          },
          exec(callback) {
            callback(null, [[]]);
          },
        };
        sinon.stub(monitoringHelper.redisClient, 'multi').returns(mockRedis);
        const z = sinon.spy(monitoringHelper.winston, 'error');
        monitoringHelper.checkQueueCountHoothoot(product).then(() => {
          assert(x.calledOnce === false);
          assert(y.calledOnce === false);
          assert(z.calledOnce === true);
          monitoringHelper.pushRequestIdAsUniqueUser.restore();
          monitoringHelper.redisClient.multi.restore();
          monitoringHelper.checkPendingQueueStatsThreshold.restore();
          monitoringHelper.winston.error.restore();
          done();
        });
      });
      it(`checkQueueCount should call methods when multi works fine for ${product}`, (done) => {
        const x = sinon.spy(monitoringHelper, 'pushRequestIdAsUniqueUser');
        const y = sinon.spy(monitoringHelper, 'checkPendingQueueStatsThreshold');
        const mockRedis = {
          zrangebyscore() {
            return this;
          },
          zremrangebyscore() {
            return this;
          },
          exec(callback) {
            callback(null, [[null, ['hello', 'world']], [null, 2]]);
          },
        };
        sinon.stub(monitoringHelper.redisClient, 'multi').returns(mockRedis);
        const z = sinon.spy(monitoringHelper.winston, 'error');
        const gamma = sinon.spy(monitoringHelper, 'listQueueKeysAndRemove');
        monitoringHelper.checkQueueCountHoothoot(product).then(() => {
          assert(x.calledOnce === true);
          assert(y.calledOnce === true);
          assert(z.calledOnce === false);
          assert(gamma.calledOnce === true);
          monitoringHelper.pushRequestIdAsUniqueUser.restore();
          monitoringHelper.listQueueKeysAndRemove.restore();
          monitoringHelper.redisClient.multi.restore();
          monitoringHelper.checkPendingQueueStatsThreshold.restore();
          monitoringHelper.winston.error.restore();
          done();
        });
      });
      it(`clearStaleKeysInQueue should not call methods in case of err for ${product}`, (done) => {
        // when in doubt regarding syntax for stubbing chain commands, just mock the whole class :p
        const x = sinon.spy(monitoringHelper, 'listQueueKeysAndRemove');
        const mockRedis = {
          zrangebyscore() {
            return this;
          },
          zremrangebyscore() {
            return this;
          },
          exec(callback) {
            callback('some error', []);
          },
        };
        sinon.stub(monitoringHelper.redisClient, 'multi').returns(mockRedis);
        const z = sinon.spy(monitoringHelper.winston, 'error');
        monitoringHelper.clearStaleKeysInQueue(product).then(() => {
          assert(z.calledOnce === true);
          assert(x.calledOnce === true);
          monitoringHelper.redisClient.multi.restore();
          monitoringHelper.listQueueKeysAndRemove.restore();
          monitoringHelper.winston.error.restore();
          done();
        });
      });
      it(`clearStaleKeysInQueue should call methods for ${product}`, (done) => {
        // when in doubt regarding syntax for stubbing chain commands, just mock the whole class :p
        const x = sinon.spy(monitoringHelper, 'listQueueKeysAndRemove');
        const mockRedis = {
          zrangebyscore() {
            return this;
          },
          zremrangebyscore() {
            return this;
          },
          exec(callback) {
            callback(null, [[null, ['hello', 'world']], [null, 1]]);
          },
        };
        sinon.stub(monitoringHelper.redisClient, 'multi').returns(mockRedis);
        const z = sinon.spy(monitoringHelper.winston, 'info');
        monitoringHelper.clearStaleKeysInQueue(product).then(() => {
          assert(z.calledOnce === true);
          assert(x.calledOnce === true);
          monitoringHelper.redisClient.multi.restore();
          monitoringHelper.listQueueKeysAndRemove.restore();
          monitoringHelper.winston.info.restore();
          done();
        });
      });
    });
  });

  describe('alert in case trackset is almost full', () => {
    ['automate', 'appAutomate'].forEach((product) => {
      it(`should send p2 alert if trackset is aobut to get full for ${product}`, (done) => {
        sinon.stub(monitoringHelper, 'validatePipelineMemoryAndRedisState').returns(false);
        sinon.stub(monitoringHelper.redisClient, 'scard').returns(Math.round(monitoringHelper.pipelineMaxValue[product] * 0.85));
        const x = sinon.spy(monitoringHelper, 'sendAlerts');
        monitoringHelper.checkTracksetSize(product).then(() => {
          assert(x.calledOnce === true);
          monitoringHelper.sendAlerts.restore();
          monitoringHelper.redisClient.scard.restore();
          monitoringHelper.validatePipelineMemoryAndRedisState.restore();
          done();
        });
      });

      it(`should send alert if trackset is almost full for ${product}`, (done) => {
        sinon.stub(monitoringHelper, 'validatePipelineMemoryAndRedisState').returns(false);
        sinon.stub(monitoringHelper.redisClient, 'scard').returns(monitoringHelper.pipelineMaxValue[product]);
        const x = sinon.spy(monitoringHelper, 'sendAlerts');
        monitoringHelper.checkTracksetSize(product).then(() => {
          assert(x.calledOnce === true);
          monitoringHelper.sendAlerts.restore();
          monitoringHelper.redisClient.scard.restore();
          monitoringHelper.validatePipelineMemoryAndRedisState.restore();
          done();
        });
      });

      it(`should not send alert if trackset is not almost full for ${product}`, (done) => {
        sinon.stub(monitoringHelper, 'validatePipelineMemoryAndRedisState').returns(false);
        sinon.stub(monitoringHelper.redisClient, 'scard').returns(monitoringHelper.pipelineMinValue[product]);
        const x = sinon.spy(monitoringHelper, 'sendAlerts');
        monitoringHelper.checkTracksetSize(product).then(() => {
          assert(x.calledOnce === false);
          monitoringHelper.sendAlerts.restore();
          monitoringHelper.redisClient.scard.restore();
          monitoringHelper.validatePipelineMemoryAndRedisState.restore();
          done();
        });
      });

      it(`should not send alert if trackset is not restored for ${product}`, (done) => {
        sinon.stub(monitoringHelper, 'validatePipelineMemoryAndRedisState').returns(true);
        const y = sinon.spy(monitoringHelper.redisClient, 'scard');
        const x = sinon.spy(monitoringHelper, 'sendAlerts');
        monitoringHelper.checkTracksetSize(product).then(() => {
          assert(x.calledOnce === false);
          assert(y.calledOnce === false);
          monitoringHelper.sendAlerts.restore();
          monitoringHelper.redisClient.scard.restore();
          monitoringHelper.validatePipelineMemoryAndRedisState.restore();
          done();
        });
      });
    });
  });

  describe('Behaviour of additional methods in monitoring service', () => {
    it('monitoringService run should call processIntervalHook', (done) => {
      const y = sinon.stub(monitoringService.monitoringHelper, 'processIntervalHook');
      monitoringService.run();
      assert(y.calledOnce === true);
      monitoringService.monitoringHelper.processIntervalHook.restore();
      done();
    });
    it('monitoringService processPerSecond should call manipulatePipeline', (done) => {
      const x = sinon.stub(monitoringService.monitoringHelper, 'ackAlive').returns(Promise.resolve());
      const y = sinon.stub(monitoringService.monitoringHelper, 'verifyMaster').returns(Promise.resolve());
      const z = sinon.stub(monitoringService, 'manipulatePipeline').returns(Promise.resolve());
      monitoringService.processPerSecond().then(() => {
        assert(x.calledOnce === true);
        assert(y.calledOnce === true);
        assert(z.calledTwice === true);
        monitoringService.monitoringHelper.ackAlive.restore();
        monitoringService.monitoringHelper.verifyMaster.restore();
        monitoringService.manipulatePipeline.restore();
        done();
      });
    });
    it('monitoringService processPer15Second should call syncDroppedSessions', (done) => {
      const x = sinon.stub(monitoringService.monitoringHelper, 'syncDroppedSessions').returns(Promise.resolve());
      monitoringService.processPer15Second().then(() => {
        assert(x.calledOnce === true);
        monitoringService.monitoringHelper.syncDroppedSessions.restore();
        done();
      });
    });
    it('monitoringService processPer30Second should call writeToRedis and readFromRedis and checkPipelineStateAndClearTrackSet', (done) => {
      const x = sinon.stub(monitoringService.monitoringHelper, 'writeToRedis').returns(Promise.resolve());
      const y = sinon.stub(monitoringService.monitoringHelper, 'readFromRedis').returns(Promise.resolve());
      const z = sinon.spy(monitoringService.monitoringHelper, 'checkPipelineStateAndClearTrackSet');
      monitoringService.processPer30Second().then(() => {
        assert(x.calledOnce === true);
        assert(y.calledOnce === true);
        assert(z.calledTwice === true);
        assert(monitoringService.monitoringHelper.checkPipelineStateAndClearTrackSet.calledWith('automate') === true);
        assert(monitoringService.monitoringHelper.checkPipelineStateAndClearTrackSet.calledWith('appAutomate') === true);
        monitoringService.monitoringHelper.writeToRedis.restore();
        monitoringService.monitoringHelper.readFromRedis.restore();
        monitoringService.monitoringHelper.checkPipelineStateAndClearTrackSet.restore();
        done();
      });
    });
    it('monitoringService processPerHour should call checkDowntimeEnabled', (done) => {
      const x = sinon.stub(monitoringService.monitoringHelper, 'ensureOncePerDay').returns(Promise.resolve(true));
      const y = sinon.stub(monitoringService, 'checkDowntimeEnabled').returns(Promise.resolve());
      monitoringService.processPerHour().then(() => {
        assert(x.calledOnce === true);
        assert(y.calledTwice === true);
        monitoringService.monitoringHelper.ensureOncePerDay.restore();
        monitoringService.checkDowntimeEnabled.restore();
        done();
      });
    });
    it('monitoringService processPerHour should not call checkDowntimeEnabled if ensure fails', (done) => {
      const x = sinon.stub(monitoringService.monitoringHelper, 'ensureOncePerDay').returns(Promise.reject());
      const y = sinon.stub(monitoringService, 'checkDowntimeEnabled').returns(Promise.resolve());
      monitoringService.processPerHour().then(() => {
        assert(x.calledOnce === true);
        assert(y.calledTwice === false);
        monitoringService.monitoringHelper.ensureOncePerDay.restore();
        monitoringService.checkDowntimeEnabled.restore();
        done();
      });
    });
    it('checks call per minute when automate downtime is enabled', (done) => {
      const w = sinon.spy(monitoringService.monitoringHelper, 'checkTracksetSize');
      const x = sinon.spy(monitoringService, 'checkDowntimeEnabled');
      const y = sinon.spy(monitoringService.monitoringHelper, 'ensurePipelineRestored');
      const z = sinon.spy(monitoringService.monitoringHelper, 'checkQueueCountHoothoot');
      startForcedDowntime();
      monitoringService.processPerMinute().then(() => {
        assert(w.calledTwice === true);
        assert(x.calledTwice === true);
        assert(y.calledWith('appAutomate') === true);
        assert(z.calledTwice === true);
        monitoringService.checkDowntimeEnabled.restore();
        monitoringService.monitoringHelper.ensurePipelineRestored.restore();
        monitoringService.monitoringHelper.checkQueueCountHoothoot.restore();
        monitoringService.monitoringHelper.checkTracksetSize.restore();
        stopDowntime();
        done();
      });
    });
    it('checks call per minute when app automate downtime is enabled', (done) => {
      const w = sinon.spy(monitoringService.monitoringHelper, 'checkTracksetSize');
      const x = sinon.spy(monitoringService, 'checkDowntimeEnabled');
      const y = sinon.spy(monitoringService.monitoringHelper, 'ensurePipelineRestored');
      const z = sinon.spy(monitoringService.monitoringHelper, 'checkQueueCountHoothoot');
      appAutomateStartForcedDownTime();
      monitoringService.processPerMinute().then(() => {
        assert(w.calledTwice === true);
        assert(x.calledTwice === true);
        assert(y.calledWith('automate') === true);
        assert(z.calledTwice === true);
        monitoringService.checkDowntimeEnabled.restore();
        monitoringService.monitoringHelper.ensurePipelineRestored.restore();
        monitoringService.monitoringHelper.checkQueueCountHoothoot.restore();
        monitoringService.monitoringHelper.checkTracksetSize.restore();
        appAutomateStopDowntime();
        done();
      });
    });
    it('checks hard reset of pipeline after 2 hours if downtime is not enabled', (done) => {
      const now = new Date() - (3 * 60 * 60 * 1000);
      clock = sinon.useFakeTimers(now);
      const x = sinon.spy(monitoringService, 'getManipulativeAction');
      const y = sinon.spy(monitoringService.monitoringHelper, 'resetPipelineAndRestoreCurrentState');
      const z = sinon.spy(monitoringService, 'checkDowntimeEnabled');
      monitoringService.monitoringHelper.lastResetTime = new Date().getTime();
      clock.restore();
      setResponseCounts(0, 500)
        .then(() => {
          monitoringHelper.redisClient.set(trackTokenHash.queueCountMaxTag, 256).then(() => {
            monitoringService.manipulatePipeline(trackTokenHash, logTag).then(() => {
              assert(x.calledOnce === true);
              assert(y.calledOnce === true);
              assert(z.calledOnce === true);
              monitoringService.getManipulativeAction.restore();
              monitoringService.monitoringHelper.resetPipelineAndRestoreCurrentState.restore();
              monitoringService.checkDowntimeEnabled.restore();
              clock.restore();
              done();
            });
          });
        });
    });
    ['automate', 'appAutomate'].forEach((product) => {
      it(`checks call per 15 minute when forced downtime is enabled for ${product}`, (done) => {
        const x = sinon.spy(monitoringService, 'checkDowntimeEnabled');
        const y = sinon.spy(monitoringService, 'checkDownTimeState');
        sinon.spy(monitoringService.monitoringHelper, 'sendAlerts');
        const subject = `${product} down time state forcedDowntime for region ${constants.region}`;
        const message = `forcedDowntime is enabled for ${product} region ${constants.region}`;
        const alertReceivers = product === 'automate' ? ['automate'] : ['automate', 'app-automate'];
        if (product === 'automate') {
          startForcedDowntime();
        } else {
          appAutomateStartForcedDownTime();
        }
        monitoringService.processPer15Minute().then(() => {
          assert(x.calledTwice === true);
          assert(y.calledTwice === true);
          assert(monitoringService.checkDowntimeEnabled.calledWith(constants.railsPipeline.redisAutomateTrackTokenHash, 'automate') === true);
          assert(monitoringService.checkDowntimeEnabled
            .calledWith(constants.railsPipeline.redisAppAutomateTrackTokenHash, 'appAutomate') === true);
          assert(monitoringService.monitoringHelper.sendAlerts
            .calledWith(subject, message, alertReceivers) === true);
          monitoringService.checkDowntimeEnabled.restore();
          monitoringService.monitoringHelper.sendAlerts.restore();
          monitoringService.checkDownTimeState.restore();
          if (product === 'automate') {
            stopDowntime().then(() => {
              done();
            });
          } else {
            appAutomateStopDowntime().then(() => {
              done();
            });
          }
        });
      });
    });
    it('checks call per 15 minute when forced downtime is disabled for automate,app-automate', (done) => {
      const x = sinon.spy(monitoringService, 'checkDowntimeEnabled');
      sinon.spy(monitoringService.monitoringHelper, 'sendAlerts');
      const y = sinon.spy(monitoringService, 'checkDownTimeState');
      monitoringService.processPer15Minute().then(() => {
        assert(x.calledTwice === true);
        assert(y.calledTwice === true);
        assert(monitoringService.checkDowntimeEnabled.calledWith(constants.railsPipeline.redisAutomateTrackTokenHash, 'automate') === true);
        assert(monitoringService.checkDowntimeEnabled.calledWith(constants.railsPipeline.redisAppAutomateTrackTokenHash, 'appAutomate') === true);
        assert(monitoringService.monitoringHelper.sendAlerts.calledWith(`appAutomate down time state forcedDowntime for region ${constants.region}`, `forcedDowntime is enabled for appAutomate region ${constants.region}`, ['app-automate', 'automate']) === false);
        assert(monitoringService.monitoringHelper.sendAlerts.calledWith(`automate down time state forcedDowntime for region ${constants.region}`, `forcedDowntime is enabled for automate region ${constants.region}`, ['automate']) === false);
        monitoringService.checkDowntimeEnabled.restore();
        monitoringService.checkDownTimeState.restore();
        monitoringService.monitoringHelper.sendAlerts.restore();
        done();
      });
    });
  });
  it('checks call per 5 minutes clearStaleKeysInQueue for automate,app-automate', (done) => {
    const x = sinon.spy(monitoringService.monitoringHelper, 'clearStaleKeysInQueue');
    monitoringService.processPer5Minute().then(() => {
      assert(x.calledTwice === true);
      assert(x.calledWith('automate') === true);
      assert(x.calledWith('app-automate') === true);
      monitoringService.monitoringHelper.clearStaleKeysInQueue.restore();
      done();
    });
  });
});
