'use strict';

const {
  describe, it, afterEach, beforeEach,
} = require('mocha');
const rewire = require('rewire');
const sinon = require('sinon');
const fs = require('fs-extra');
const { assert, expect } = require('chai');

let batchFilesProxy;

// if to use mocked file system or to test on real
const USE_MOCK_FS = true;
let logger;
let isUndefined;

describe('Kafka batch files', () => {
  beforeEach(() => {
    process.env.CONSUMER_TOPIC = '';
    process.env.CONSUMER_ID = '';
    process.env.COMMON_KAFKA_TOPIC_REGION = '';

    batchFilesProxy = rewire('../../apps/kafkaUploader/kafkaBatchFiles');
    logger = batchFilesProxy.__get__('logger');
    isUndefined = batchFilesProxy.__get__('isUndefined');

    const fakeReadDir = (path) => {
      const content = filesystem[path] && filesystem[path].isDirectory ? filesystem[path].content : null;
      if (content == null) {
        return Promise.reject('not a directory: ', path);
      }
      return Promise.resolve(content);
    };
    const fakeStat = (path) => {
      const file = filesystem[path] ? filesystem[path] : null;
      if (file == null) {
        return Promise.reject('file not found');
      }

      return Promise.resolve({
        isDirectory() {
          return file.isDirectory;
        }
      });
    };

    const fsMock = {
      stat: fakeStat,
      readdir: fakeReadDir
    };

    if (USE_MOCK_FS) {
      revertStat = batchFilesProxy.__set__('stat', fakeStat);
      revertReaddir = batchFilesProxy.__set__('readdir', fakeReadDir);
    }
  });

  afterEach(() => {
    if (typeof revertFsMock === 'function') {
      revertStat();
    }
    if (typeof revertReaddir === 'function') {
      revertReaddir();
    }
  });

  describe('Checking batchFiles', async () => {
    beforeEach(() => {
      sinon.spy(logger, 'info');
      sinon.spy(logger, 'error');
      sinon.spy(logger, 'warn');
    });

    afterEach(() => {
      logger.info.restore();
      logger.error.restore();
      logger.warn.restore();
    });

    it('should throw error for non-existing path', async () => {
      let error = null;
      try {
        await batchFilesProxy.batchFiles('/not/existing/path');
      } catch (err) {
        error = err;
      }
      expect(error).to.not.equal(null);
    });

    it('should not throw error for path being null', async () => {
      let error = null;
      let result;
      try {
        result = await batchFilesProxy.batchFiles(null, 3);
      } catch (err) {
        error = err;
      }
      expect(result.length).to.equal(0);
    });

    it('should not throw error for limit being 0', async () => {
      let error = null;
      let result;
      try {
        result = await batchFilesProxy.batchFiles(`${basepath}`, 0);
      } catch (err) {
        error = err;
      }
      expect(result.length).to.equal(0);
    });

    it('should not throw error for limit being 0 and path being null', async () => {
      let error = null;
      let result;
      try {
        result = await batchFilesProxy.batchFiles(null, 0);
      } catch (err) {
        error = err;
      }
      expect(result.length).to.equal(0);
    });

    it('should not throw error for existing path', async () => {
      const result = await batchFilesProxy.batchFiles(`${basepath}`, 3).catch((error) => {
        console.error('error: ', error);
      });
      expect(result.length).to.be.greaterThan(0);
    });

    it('should limit records collected', async () => {
      const result = await batchFilesProxy.batchFiles(`${basepath}`, 1);
      expect(result.length).to.equal(1);
    });

    it('should not return empty array for deep empty folders', async () => {
      const result = await batchFilesProxy.batchFiles(`${basepath}/deep_empty`, 4);
      expect(result.length).to.equal(1);
    });

    it('should throw if stat returns falsy (path does not exist)', async () => {
      const revertStat = batchFilesProxy.__set__('stat', () => Promise.resolve(null));
      // stat returns null, simulating a non-existent path

      let error = null;
      try {
        await batchFilesProxy.batchFiles('/non/existent/path', 2);
      } catch (err) {
        error = err;
      } finally {
        revertStat();
      }

      expect(error).to.not.equal(null);
      expect(error.message).to.include('Path does not exist: /non/existent/path');
    });
  });

  it('should log and throw if readdir fails in directory', async () => {
    // Override fakeReadDir to always reject
    const errorMsg = 'fail!';
    const errorReadDir = () => Promise.reject(new Error(errorMsg));
    revertReaddir = batchFilesProxy.__set__('readdir', errorReadDir);

    // Also, make sure stat returns a directory
    revertStat = batchFilesProxy.__set__('stat', () => Promise.resolve({
      isDirectory: () => true
    }));
  });
});

const basepath = '/tmp/checklogs';
let revertReaddir; let
  revertStat;

const filesystem = {
  '/tmp/checklogs': {
    isDirectory: true,
    content: ['01.txt', '02.txt', 'logs', 'deep_empty']
  },
  '/tmp/checklogs/01.txt': {
    isDirectory: false
  },
  '/tmp/checklogs/02.txt': {
    isDirectory: false
  },

  '/tmp/checklogs/logs': {
    isDirectory: true,
    content: ['day01']
  },
  '/tmp/checklogs/logs/day01': {
    isDirectory: true,
    content: [
      '01.txt', '02.txt', '03.txt', '04.txt', '05.txt', '06.txt', '07.txt', '08.txt', '09.txt', '10.txt'
    ]
  },

  '/tmp/checklogs/logs/day01/01.txt': {
    isDirectory: false
  },
  '/tmp/checklogs/logs/day01/02.txt': {
    isDirectory: false
  },
  '/tmp/checklogs/logs/day01/03.txt': {
    isDirectory: false
  },
  '/tmp/checklogs/logs/day01/04.txt': {
    isDirectory: false
  },
  '/tmp/checklogs/logs/day01/05.txt': {
    isDirectory: false
  },
  '/tmp/checklogs/logs/day01/06.txt': {
    isDirectory: false
  },
  '/tmp/checklogs/logs/day01/07.txt': {
    isDirectory: false
  },
  '/tmp/checklogs/logs/day01/08.txt': {
    isDirectory: false
  },
  '/tmp/checklogs/logs/day01/09.txt': {
    isDirectory: false
  },
  '/tmp/checklogs/logs/day01/10.txt': {
    isDirectory: false
  },

  '/tmp/checklogs/deep_empty': {
    isDirectory: true,
    content: ['01']
  },

  '/tmp/checklogs/deep_empty/01': {
    isDirectory: true,
    content: ['02']
  },

  '/tmp/checklogs/deep_empty/01/02': {
    isDirectory: true,
    content: ['03']
  },

  '/tmp/checklogs/deep_empty/01/02/03': {
    isDirectory: true,
    content: []
  }
};
