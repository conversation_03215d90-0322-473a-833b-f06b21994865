const helper = require('../helper');
const fs = require('fs');
const assert = require('assert');
const path = require('path');
const ConfigReader = require('../../../utils/configReader.js').ConfigReader;

describe('ConfigReader', function() {
  describe('constructor', function() {
    it('returns sets the location and config with ignoreEmpty false', function() {
      var testConfigFile = `${path.join(__dirname, "/../../..")}/conf.test.json`;
      var reader = new ConfigReader(testConfigFile, false);

      assert(reader.defaultConfigFileLocation === testConfigFile);
      var expectedObject = JSON.parse(fs.readFileSync(testConfigFile));
      assert.deepEqual(reader.config, expectedObject, "assertion_failed");
    });

    it('returns sets the location and config with ignoreEmpty true', function() {
      var testConfigFile = `${path.join(__dirname, "/../../..")}/conf.test.json`;
      var reader = new ConfigReader(testConfigFile, true);

      assert(reader.defaultConfigFileLocation === testConfigFile);
      var expectedObject = JSON.parse(fs.readFileSync(testConfigFile));
      assert.deepEqual(reader.config, expectedObject, "assertion_failed");
    });

    it('returns empty json if ignoreEmpty is true and a non existent file is passed', function() {
      var testConfigFile = `${path.join(__dirname, "/../../..")}/nonexistentfile.test.json`;
      var reader = new ConfigReader(testConfigFile, true);

      assert(reader.defaultConfigFileLocation === testConfigFile);
      assert.deepEqual(reader.config, {}, "assertion_failed");
    });

    it('should throw error is ignoreEmpty is false and a non existent file is passed', function() {
      var testConfigFile = `${path.join(__dirname, "/../../..")}/nonexistentfile.test.json`;

      assert.throws(() => {
        var reader = new ConfigReader(testConfigFile, false);
      }, /no such file or directory/);
    });
  });
});