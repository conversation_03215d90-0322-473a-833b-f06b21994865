const { expect, assert } = require('chai');

const isReqFromBrowserstack = require('../../../utils/verifyRequestHeaders');

describe('isReqFromBrowserstack', () => {

  it('returns true for allowed origins', () => {
    const headers = {
      origin: 'https://www.browserstack.com'  
    };
    
    const result = isReqFromBrowserstack(headers);

    expect(result).to.be.true;
  });

  it('returns false for non-allowed origins', () => {
    const headers = {
      origin: 'https://www.example.com'
    };

    const result = isReqFromBrowserstack(headers);

    expect(result).to.be.false;
  });

  it('returns false for malformed origins', () => {
    const headers = {
      origin: 'cjostfdfgpqd3mut2aog9chz6i3681rua-abc'
    };

    const result = isReqFromBrowserstack(headers);

    expect(result).to.be.false;
  });

  it('returns false for no origin', () => {
    const headers = {};

    const result = isReqFromBrowserstack(headers);

    expect(result).to.be.false; 
  });

});
