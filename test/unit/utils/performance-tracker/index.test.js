'use strict';

const performanceTracker = require('../../../../utils/performance-tracker');
const helper = require('./../../helper');
const constants = require('../../../../constants');
const hubHelper = require('../../../../helper');

describe('performance-tracker', () => {
  it('countSeleniumRequests when railsSessionId is present', (done) => {
    const keyObject = helper.getKeyObject();
    const railsSessionId = 'abcdef';
    constants.global_registry[railsSessionId] = keyObject;
    performanceTracker.countSeleniumRequests(railsSessionId);
    constants.global_registry[railsSessionId].should.include.keys('seleniumRequestsCount');
    done();
  });

  it('countSeleniumRequests when railsSession Id is not present', (done) => {
    const keyObject = helper.getKeyObject();
    const railsSessionId = undefined;
    constants.global_registry[railsSessionId] = keyObject;
    performanceTracker.countSeleniumRequests(railsSessionId);
    constants.global_registry[railsSessionId].should.not.include.keys('seleniumRequestsCount');
    done();
  });

  Object.keys(constants.APP_AUTOMATE_APPIUM_COMMANDS_TO_COUNT).forEach((key) => {
    it(`adds to appiumCommandUsage for the command ${key} in APP_AUTOMATE_APPIUM_COMMANDS_TO_COUNT`, (done) => {
      const keyObject = helper.getKeyObject();
      const railsSessionId = 'abcdef';
      keyObject.appTesting = true;
      constants.global_registry[railsSessionId] = keyObject;
      const command = key;

      const tag = constants.APP_AUTOMATE_APPIUM_COMMANDS_TO_COUNT[key];
      performanceTracker.countAppiumCommandUsage(railsSessionId, command);
      constants.global_registry[railsSessionId].should.include.keys('appiumCommandUsage');
      constants.global_registry[railsSessionId].appiumCommandUsage[tag].should.equal(1);

      hubHelper.removeFromGlobalRegistry({ rails_session_id: railsSessionId });
      done();
    });
  });

  it('don\'t countAppiumCommandUsage when railsSession Id is not present', (done) => {
    const keyObject = helper.getKeyObject();
    const railsSessionId = undefined;
    constants.global_registry[railsSessionId] = keyObject;

    performanceTracker.countAppiumCommandUsage(railsSessionId);

    constants.global_registry[railsSessionId].should.not.include.keys('appiumCommandUsage');

    hubHelper.removeFromGlobalRegistry({ rails_session_id: railsSessionId });
    done();
  });

  const commandRequestData = {
    push_file: { path: 'dummy_push_file_path', data: 'dummy_push_file_data' },
  };

  const commandTrackedData = {
    push_file: { count: 1, params: [{ count: 1, data: { path: 'dummy_push_file_path' } }] },
  };

  Object.keys(constants.APP_AUTOMATE_APPIUM_COMMANDS_TO_TRACK_DATA).forEach((key) => {
    it(`adds to appiumCommandUsage for the command ${key} in APP_AUTOMATE_APPIUM_COMMANDS_TO_TRACK_DATA`, (done) => {
      const keyObject = helper.getKeyObject();
      const railsSessionId = 'abcdef';
      keyObject.appTesting = true;
      constants.global_registry[railsSessionId] = keyObject;
      const command = key;

      const tag = constants.APP_AUTOMATE_APPIUM_COMMANDS_TO_TRACK_DATA[key].tag;
      performanceTracker.trackAppiumCommandData(railsSessionId, command, commandRequestData[tag]);
      constants.global_registry[railsSessionId].should.include.keys('appiumCommandUsage');
      constants.global_registry[railsSessionId]
        .appiumCommandUsage[tag]
        .should.eql(commandTrackedData[tag]);

      hubHelper.removeFromGlobalRegistry({ rails_session_id: railsSessionId });
      done();
    });
  });

  it('don\'t trackAppiumCommandData when railsSession Id is not present', (done) => {
    const keyObject = helper.getKeyObject();
    const railsSessionId = undefined;
    constants.global_registry[railsSessionId] = keyObject;

    performanceTracker.trackAppiumCommandData(railsSessionId);

    constants.global_registry[railsSessionId].should.not.include.keys('appiumCommandUsage');

    hubHelper.removeFromGlobalRegistry({ rails_session_id: railsSessionId });
    done();
  });

  it('should increment param count for duplicate data in trackAppiumCommandData', (done) => {
    const keyObject = helper.getKeyObject();
    const railsSessionId = 'abcdef-dup';
    keyObject.appTesting = true;
    constants.global_registry[railsSessionId] = keyObject;

    // Use a command from your APP_AUTOMATE_APPIUM_COMMANDS_TO_TRACK_DATA
    const command = Object.keys(constants.APP_AUTOMATE_APPIUM_COMMANDS_TO_TRACK_DATA)[0];
    const { tag, fields } = constants.APP_AUTOMATE_APPIUM_COMMANDS_TO_TRACK_DATA[command];

    // Prepare data with all required fields
    const data = {};
    fields.forEach((f) => { data[f] = 'val'; });

    // First call: adds new param
    performanceTracker.trackAppiumCommandData(railsSessionId, command, data);
    // Second call: should increment count for the same param (duplicate)
    performanceTracker.trackAppiumCommandData(railsSessionId, command, data);

    const usage = constants.global_registry[railsSessionId].appiumCommandUsage[tag];
    usage.count.should.equal(2);
    usage.params.should.have.length(1);
    usage.params[0].count.should.equal(2);

    hubHelper.removeFromGlobalRegistry({ rails_session_id: railsSessionId });
    done();
  });
});
