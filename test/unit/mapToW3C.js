const mapToW3C = require('../../utils/commandMapper/mapToW3C')
const {
  MOVETO_ACTION_PAYLOAD,
  CLICK_ACTION_PAYLOAD,
  BUTTON_DOWN_PAYLOAD,
  BUTTON_UP_PAYLOAD,
  DOUBLE_CLICK_PAYLOAD
} = require('../../utils/commandMapper/actionPayload');
const { assert } = require('chai');

describe('mapToW3C function', () => {
  context('GET - Map selenium OSS commands to W3C', () => {
    const sessionPath = '/wd/hub/session/a1b2c3d4a1b2c3d4a1b2c3d4a1b2c3d4';
    it('converts the displayed command', () => {
      const request = { method: 'GET', path: `${sessionPath}/element/some-random-id/displayed`, headers: {} };
      const mappedRequest = mapToW3C.mapRequestToW3C(request);
      mappedRequest.method.should.equal('POST');
      mappedRequest.path.should.equal(`${sessionPath}/execute/sync`);
      mappedRequest.headers['content-length'].should.equal(Buffer.byteLength(mappedRequest.body));
    });

    it('converts the get attribute command', () => {
      const request = { method: 'GET', path: `${sessionPath}/element/some-random-id/attribute/value`, headers: {} };
      const mappedRequest = mapToW3C.mapRequestToW3C(request);
      mappedRequest.method.should.equal('POST');
      mappedRequest.path.should.equal(`${sessionPath}/execute/sync`);
      mappedRequest.headers['content-length'].should.equal(Buffer.byteLength(mappedRequest.body));
    });

    it('converts the get window handle command', () => {
      const request = { method: 'GET', path: `${sessionPath}/window_handle` };
      const mappedRequest = mapToW3C.mapRequestToW3C(request);
      mappedRequest.method.should.equal('GET');
      mappedRequest.path.should.equal(`${sessionPath}/window`);
    });

    it('converts the get window handles command', () => {
      const request = { method: 'GET', path: `${sessionPath}/window_handles` };
      const mappedRequest = mapToW3C.mapRequestToW3C(request);
      mappedRequest.method.should.equal('GET');
      mappedRequest.path.should.equal(`${sessionPath}/window/handles`);
    });

    it('converts the get window size command', () => {
      const request = { method: 'GET', path: `${sessionPath}/window/some-random-id/size`, body: '{}', headers: {} };
      const mappedRequest = mapToW3C.mapRequestToW3C(request);
      mappedRequest.method.should.equal('GET');
      mappedRequest.path.should.equal(`${sessionPath}/window/rect`);
    });

    it('converts the get window position command', () => {
      const request = { method: 'GET', path: `${sessionPath}/window/some-random-id/position`, body: '{}', headers: {} };
      const mappedRequest = mapToW3C.mapRequestToW3C(request);
      mappedRequest.method.should.equal('GET');
      mappedRequest.path.should.equal(`${sessionPath}/window/rect`);
    });

    it('converts the get alert text command', () => {
      const request = { method: 'GET', path: `${sessionPath}/alert_text`, body: '{}', headers: {} };
      const mappedRequest = mapToW3C.mapRequestToW3C(request);
      mappedRequest.method.should.equal('GET');
      mappedRequest.path.should.equal(`${sessionPath}/alert/text`);
    });

    it('converts the get element location command', () => {
      const request = { method: 'GET', path: `${sessionPath}/element/some-random-id/location`, body: '{}', headers: {} };
      const mappedRequest = mapToW3C.mapRequestToW3C(request);
      mappedRequest.method.should.equal('GET');
      mappedRequest.path.should.equal(`${sessionPath}/element/some-random-id/rect`);
    });

    it('converts the get element location command', () => {
      const request = { method: 'GET', path: `${sessionPath}/element/some-random-id/size`, body: '{}', headers: {} };
      const mappedRequest = mapToW3C.mapRequestToW3C(request);
      mappedRequest.method.should.equal('GET');
      mappedRequest.path.should.equal(`${sessionPath}/element/some-random-id/rect`);
    });

    it('converts the get element location in view command', () => {
      const request = { method: 'GET', path: `${sessionPath}/element/some-random-id/location_in_view`, body: '{}', headers: {} };
      const mappedRequest = mapToW3C.mapRequestToW3C(request);
      mappedRequest.method.should.equal('POST');
      mappedRequest.path.should.equal(`${sessionPath}/execute/sync`);
      mappedRequest.headers['content-length'].should.equal(Buffer.byteLength(mappedRequest.body));
    });

  });

  context('POST - Map selenium OSS commands to W3C', () => {
    const sessionPath = '/wd/hub/session/a1b2c3d4a1b2c3d4a1b2c3d4a1b2c3d4';
    it('converts the find element(s) command', () => {
      const value = 'VALUE';
      const selectors = [['id', `[id=${value}]`], ['name', `[name=${value}]`], ['tag name', value], ['class name', `.${value}`]];
      for (const command of ['elements', 'elements']) {
        for (const [selector, expectedValue] of selectors) {
          const body = JSON.stringify({ using: selector, value });
          const request = { method: 'POST', path: `${sessionPath}/${command}`, headers: {}, body };
          const mappedRequest = mapToW3C.mapRequestToW3C(request);
          mappedRequest.path.should.equal(`${sessionPath}/${command}`);
          const parsedBody = JSON.parse(mappedRequest.body);
          parsedBody.using.should.equal('css selector');
          parsedBody.value.should.equal(expectedValue);
          mappedRequest.headers['content-length'].should.equal(Buffer.byteLength(mappedRequest.body));
        }
      }
    });

    it('converts the find element(s) command inside an element', () => {
      const value = 'VALUE';
      const selectors = [['id', `[id=${value}]`], ['name', `[name=${value}]`], ['tag name', value], ['class name', `.${value}`]];
      for (const command of ['elements', 'elements']) {
        for (const [selector, expectedValue] of selectors) {
          const body = JSON.stringify({ using: selector, value });
          const request = { method: 'POST', path: `${sessionPath}/element/some-element-id/${command}`, headers: {}, body };
          const mappedRequest = mapToW3C.mapRequestToW3C(request);
          mappedRequest.path.should.equal(`${sessionPath}/element/some-element-id/${command}`);
          const parsedBody = JSON.parse(mappedRequest.body);
          parsedBody.using.should.equal('css selector');
          parsedBody.value.should.equal(expectedValue);
          mappedRequest.headers['content-length'].should.equal(Buffer.byteLength(mappedRequest.body));
        }
      }
    });

    it('converts the set element value command', () => {
      const body = JSON.stringify({ value: ['S', 'A', 'M', 'P', 'L', 'E'] });
      const request = { method: 'POST', path: `${sessionPath}/element/some-element-id/value`, body, headers: {} };
      const mappedRequest = mapToW3C.mapRequestToW3C(request);
      const parsedBody = JSON.parse(mappedRequest.body);
      parsedBody.text.should.equal('SAMPLE');
      mappedRequest.headers['content-length'].should.equal(Buffer.byteLength(mappedRequest.body));
    });

    it('converts the execute script command', () => {
      const body = JSON.stringify({ script: 'return document.readyState', args: [] });
      const request = { method: 'POST', path: `${sessionPath}/execute`, body, headers: {} };
      const mappedRequest = mapToW3C.mapRequestToW3C(request);
      mappedRequest.path.should.equal(`${sessionPath}/execute/sync`);
    });

    it('converts the execute async script command', () => {
      const body = JSON.stringify({ script: 'return document.readyState', args: [] });
      const request = { method: 'POST', path: `${sessionPath}/execute_async`, body, headers: {} };
      const mappedRequest = mapToW3C.mapRequestToW3C(request);
      mappedRequest.path.should.equal(`${sessionPath}/execute/async`);
    });

    it('converts the execute async script command', () => {
      const request = { method: 'POST', path: `${sessionPath}/window/some-window-id/maximize`, body: '{"x": 1}', headers: {} };
      const mappedRequest = mapToW3C.mapRequestToW3C(request);
      mappedRequest.path.should.equal(`${sessionPath}/window/maximize`);
      mappedRequest.body.should.equal('{}');
      mappedRequest.headers['content-length'].should.equal(Buffer.byteLength(mappedRequest.body));
    });

    it('converts the async script timeouts command', () => {
      const body = JSON.stringify({ type: 'script', ms: 1000 });
      const request = { method: 'POST', path: `${sessionPath}/timeouts/async_script`, body, headers: {} };
      const mappedRequest = mapToW3C.mapRequestToW3C(request);
      mappedRequest.path.should.equal(`${sessionPath}/timeouts`);
      const parsedBody = JSON.parse(mappedRequest.body);
      parsedBody.script.should.equal(1000);
      mappedRequest.headers['content-length'].should.equal(Buffer.byteLength(mappedRequest.body));
    });

    it('converts the implicit timeouts command', () => {
      const body = JSON.stringify({ type: 'implicit', ms: 1000 });
      const request = { method: 'POST', path: `${sessionPath}/timeouts/implicit_wait`, body, headers: {} };
      const mappedRequest = mapToW3C.mapRequestToW3C(request);
      mappedRequest.path.should.equal(`${sessionPath}/timeouts`);
      const parsedBody = JSON.parse(mappedRequest.body);
      parsedBody.implicit.should.equal(1000);
      mappedRequest.headers['content-length'].should.equal(Buffer.byteLength(mappedRequest.body));
    });

    it('converts the page logs timeouts command', () => {
      const body = JSON.stringify({ type: 'page load', ms: 1000 });
      const request = { method: 'POST', path: `${sessionPath}/timeouts`, body, headers: {} };
      const mappedRequest = mapToW3C.mapRequestToW3C(request);
      mappedRequest.path.should.equal(`${sessionPath}/timeouts`);
      const parsedBody = JSON.parse(mappedRequest.body);
      parsedBody.pageLoad.should.equal(1000);
      mappedRequest.headers['content-length'].should.equal(Buffer.byteLength(mappedRequest.body));
    });

    it('converts the set window size command', () => {
      const body = JSON.stringify({ windowHandle: 'sample-window-id' });
      const request = { method: 'POST', path: `${sessionPath}/window/sample-window-id/size`, body, headers: {} };
      const mappedRequest = mapToW3C.mapRequestToW3C(request);
      mappedRequest.path.should.equal(`${sessionPath}/window/rect`);
      const parsedBody = JSON.parse(mappedRequest.body);
      (parsedBody.windowHandle === undefined).should.equal(true);
      mappedRequest.headers['content-length'].should.equal(Buffer.byteLength(mappedRequest.body));
    });

    it('converts the set window position command', () => {
      const body = JSON.stringify({ windowHandle: 'sample-window-id' });
      const request = { method: 'POST', path: `${sessionPath}/window/sample-window-id/position`, body, headers: {} };
      const mappedRequest = mapToW3C.mapRequestToW3C(request);
      mappedRequest.path.should.equal(`${sessionPath}/window/rect`);
      const parsedBody = JSON.parse(mappedRequest.body);
      (parsedBody.windowHandle === undefined).should.equal(true);
      mappedRequest.headers['content-length'].should.equal(Buffer.byteLength(mappedRequest.body));
    });

    it('converts the get active element command', () => {
      const request = { method: 'POST', path: `${sessionPath}/element/active`, body: '{}', headers: {} };
      const mappedRequest = mapToW3C.mapRequestToW3C(request);
      mappedRequest.path.should.equal(`${sessionPath}/element/active`);
      mappedRequest.method.should.equal('GET');
    });

    it('converts the accept alert command', () => {
      const request = { method: 'POST', path: `${sessionPath}/accept_alert`, body: '{}', headers: {} };
      const mappedRequest = mapToW3C.mapRequestToW3C(request);
      mappedRequest.path.should.equal(`${sessionPath}/alert/accept`);
      mappedRequest.body.should.equal('{}');
      mappedRequest.headers['content-length'].should.equal(Buffer.byteLength(mappedRequest.body));
    });

    it('converts the dismiss alert command', () => {
      const request = { method: 'POST', path: `${sessionPath}/dismiss_alert`, body: '{}', headers: {} };
      const mappedRequest = mapToW3C.mapRequestToW3C(request);
      mappedRequest.path.should.equal(`${sessionPath}/alert/dismiss`);
      mappedRequest.body.should.equal('{}');
      mappedRequest.headers['content-length'].should.equal(Buffer.byteLength(mappedRequest.body));
    });

    it('convert the click command', () => {
      const request = { method: 'POST', path: `${sessionPath}/click`, body: '{}', headers: {} };
      const mappedRequest = mapToW3C.mapRequestToW3C(request);
      mappedRequest.path.should.equal(`${sessionPath}/actions`);
      mappedRequest.body.should.equal(JSON.stringify(CLICK_ACTION_PAYLOAD));
      mappedRequest.headers['content-length'].should.equal(Buffer.byteLength(mappedRequest.body));
    })

    it('convert the moveto command', () => {
      const request = { method: 'POST', path: `${sessionPath}/moveto`, body: '{"xoffset": 100, "yoffset":100, "element":"random"}', headers: {} };
      const mappedRequest = mapToW3C.mapRequestToW3C(request);
      mappedRequest.path.should.equal(`${sessionPath}/actions`);
      const payload = Object.assign({}, MOVETO_ACTION_PAYLOAD);
      payload.actions[0].actions[0].x = 100;
      payload.actions[0].actions[0].y = 100;
      payload.actions[0].actions[0].origin['element-6066-11e4-a52e-4f735466cecf'] = "random";
      mappedRequest.body.should.equal(JSON.stringify(payload));
      mappedRequest.headers['content-length'].should.equal(Buffer.byteLength(mappedRequest.body));
    })

    it('convert the buttondown command', () => {
      const request = { method: 'POST', path: `${sessionPath}/buttondown`, body: '{}', headers: {} };
      const mappedRequest = mapToW3C.mapRequestToW3C(request);
      mappedRequest.path.should.equal(`${sessionPath}/actions`);
      mappedRequest.body.should.equal(JSON.stringify(BUTTON_DOWN_PAYLOAD));
      mappedRequest.headers['content-length'].should.equal(Buffer.byteLength(mappedRequest.body));
    })

    it('convert the buttonup command', () => {
      const request = { method: 'POST', path: `${sessionPath}/buttonup`, body: '{}', headers: {} };
      const mappedRequest = mapToW3C.mapRequestToW3C(request);
      mappedRequest.path.should.equal(`${sessionPath}/actions`);
      mappedRequest.body.should.equal(JSON.stringify(BUTTON_UP_PAYLOAD));
      mappedRequest.headers['content-length'].should.equal(Buffer.byteLength(mappedRequest.body));
    })

    it('convert the doubleclick command', () => {
      const request = { method: 'POST', path: `${sessionPath}/doubleclick`, body: '{}', headers: {} };
      const mappedRequest = mapToW3C.mapRequestToW3C(request);
      mappedRequest.path.should.equal(`${sessionPath}/actions`);
      mappedRequest.body.should.equal(JSON.stringify(DOUBLE_CLICK_PAYLOAD));
      mappedRequest.headers['content-length'].should.equal(Buffer.byteLength(mappedRequest.body));
    })

    it('convert the window focus command', () => {
      const request = { method: 'POST', path: `${sessionPath}/window`, body: '{ "name": "random"}', headers: {} };
      const mappedRequest = mapToW3C.mapRequestToW3C(request);
      mappedRequest.path.should.equal(`${sessionPath}/window`);
      mappedRequest.body.should.equal(JSON.stringify({name: "random", handle: "random"}));
      mappedRequest.headers['content-length'].should.equal(Buffer.byteLength(JSON.stringify({name: "random", handle: "random"})));
    })
  });

  it('removes the window handle from the request path', () => {
    const request = {
      method: 'POST',
      path: '/wd/hub/session/a1b2c3d4a1b2c3d4a1b2c3d4a1b2c3d4/window/1234/maximize',
      headers: {
        'content-length': 0
      }
    };
    const mappedRequest = mapToW3C.mapRequestToW3C(request);
    mappedRequest.path.slice(-15).should.equal('window/maximize');
  });
});

describe('executeScript in mapToW3C', () => {
  it('does not change sync execute', () => {
    const request = {
      method: 'POST',
      path : '/wd/hub/session/a1b2c3d4a1b2c3d4a1b2c3d4a1b2c3d4/execute'
    }
    const mappedRequest = mapToW3C.mapRequestToW3C(request);
    mappedRequest.path.match(/execute\//)[0].should.equal("execute/");
  });

  it('changes /execute_async to /execute/async', () => {
    const request = {
      method: 'POST',
      path: '/wd/hub/session/a1b2c3d4a1b2c3d4a1b2c3d4a1b2c3d4/execute_async'
    }
    const mappedRequest = mapToW3C.mapRequestToW3C(request);
    mappedRequest.path.match(/execute\/async/)[0].should.equal("execute/async");
  });
});

describe('submitForm', () => {
  it ('should convert path to /execute/sync and body and content length', () => {
    const request = { path: '/wd/hub/session/abcd/element/xyz/submit', headers: {} };
    assert.deepEqual(mapToW3C.submitForm(request), {
      path: '/wd/hub/session/abcd/execute/sync',
      body: '{\"script\":\"var e=arguments[0].ownerDocument.createEvent(\\\"Event\\\");e.initEvent(\\\"submit\\\", true, true);if(arguments[0].dispatchEvent(e)) {arguments[0].submit()}\",\"args\":[{\"ELEMENT\":\"xyz\",\"element-6066-11e4-a52e-4f735466cecf\":\"xyz\"}]}',
      headers: {
        'content-length': 233,
      },
    })
  });
});

describe('mapResponseToW3C', () => {
  it('should map response to W3C format', function() {
    const response = {
      value: {
        someKey: 'someValue'
      },
      sessionId: '12345'
    };

    const expectedResponse = {
      value: {
        someKey: 'someValue',
        sessionId: '12345',
        capabilities: {
          someKey: 'someValue',
          sessionId: '12345'
        }
      },
      sessionId: '12345'
    };

    assert.deepEqual(mapToW3C.mapResponseToW3C(response), expectedResponse);
  });
});

describe('initRequestBody', function() {
  it('should initialize request body and update content-length header', function() {
    const request = {
      body: 'initial body',
      headers: {
        'content-length': 10
      }
    };

    const expectedRequest = {
      body: '{}',
      headers: {
        'content-length': 2
      }
    };

    assert.deepEqual(mapToW3C.initRequestBody(request), expectedRequest);
  });
});
