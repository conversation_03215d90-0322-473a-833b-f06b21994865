'use strict';

const instrumentation = require('../../helpers/instrumentation');
const helper = require('../../helper');
const sinon = require('sinon');
const { expect } = require('chai');
const constants = require('../../constants');
const { Events } = require('browserstack-dwh');

describe('instrumentation', () => {
  describe('feature_usage methods', () => {
    beforeEach(() => {
      sinon.spy(helper, 'sendToEDS');
    });

    afterEach(() => {
      helper.sendToEDS.restore();
    });

    it('should send feature usage to eds', (cb) => {
      instrumentation.pushFeatureUsage('test', { redisTextLogs: true }, cb);
      helper.sendToEDS.called.should.eql(false);
    });

    it('should not end feature usage to eds', (cb) => {
      instrumentation.pushFeatureUsage('test', {}, cb);
    });
  });
  describe('pushProductStabilityReason', () => {
    beforeEach(() => {
      sinon.stub(helper, 'sendToEDS');
    });

    afterEach(() => {
      helper.sendToEDS.restore();
    });

    it('should send stability reason to eds', () => {
      instrumentation.pushProductStabilityReason('test', 'reason', true);
      helper.sendToEDS.called.should.eql(true);
    });

    it('should not send stability reason to eds', () => {
      instrumentation.pushProductStabilityReason('test', 'reason', false);
      helper.sendToEDS.called.should.eql(false);
    });
  });
  describe('pushFeatureUsage', () => {
    let checkSessionAppAutomateStub;
    let sendToEDSStub;

    beforeEach(() => {
      checkSessionAppAutomateStub = sinon.stub(helper, 'checkSessionAppAutomate');
      sendToEDSStub = sinon.stub(helper, 'sendToEDS');
    });

    afterEach(() => {
      helper.checkSessionAppAutomate.restore();
      helper.sendToEDS.restore();
    });

    it('should send APP_AUTOMATE_TEST_SESSIONS kind if isAppAutomate is true', (done) => {
      checkSessionAppAutomateStub.callsArgWith(1, true);
      instrumentation.pushFeatureUsage('sid', { foo: 1 }, () => {
        sinon.assert.calledOnce(sendToEDSStub);
        const arg = sendToEDSStub.firstCall.args[0];
        expect(arg.kind).to.equal(Events.APP_AUTOMATE_TEST_SESSIONS);
        done();
      });
    });

    it('should send AUTOMATE_TEST_SESSIONS kind if isAppAutomate and isPlaywrightSession are false', (done) => {
      checkSessionAppAutomateStub.callsArgWith(1, false);
      instrumentation.pushFeatureUsage('sid', { foo: 1 }, () => {
        sinon.assert.calledOnce(sendToEDSStub);
        const arg = sendToEDSStub.firstCall.args[0];
        expect(arg.kind).to.equal(Events.AUTOMATE_TEST_SESSIONS);
        done();
      });
    });

    it('should not call sendToEDS if featureUsage is empty', (done) => {
      checkSessionAppAutomateStub.callsArgWith(1, true);
      instrumentation.pushFeatureUsage('sid', {}, () => {
        sinon.assert.notCalled(sendToEDSStub);
        done();
      });
    });

    it('should not call sendToEDS if featureUsage is null', (done) => {
      checkSessionAppAutomateStub.callsArgWith(1, true);
      instrumentation.pushFeatureUsage('sid', null, () => {
        sinon.assert.notCalled(sendToEDSStub);
        done();
      });
    });
  });

  describe('incrementHootHootStat', () => {
    beforeEach(() => {
      // Reset the registry before each test
      constants.pushToHootHootRegistry = {};
    });

    it('should do nothing if kind is undefined', () => {
      instrumentation.incrementHootHootStat(undefined, 'genre');
      expect(constants.pushToHootHootRegistry).to.deep.equal({});
    });

    it('should do nothing if genre is undefined', () => {
      instrumentation.incrementHootHootStat('kind', undefined);
      expect(constants.pushToHootHootRegistry).to.deep.equal({});
    });

    it('should increment the stat if kind and genre are defined', () => {
      instrumentation.incrementHootHootStat('kind', 'genre');
      expect(constants.pushToHootHootRegistry.kind.genre).to.equal(1);
      instrumentation.incrementHootHootStat('kind', 'genre');
      expect(constants.pushToHootHootRegistry.kind.genre).to.equal(2);
    });
  });
});
