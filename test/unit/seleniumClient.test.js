'use strict';

const SeleniumClient = require('../../seleniumClient');
const { customLogger: logger } = require('../../logger');
const sinon = require('sinon');
const assert = require('assert');
const expect = require('chai').expect;

describe('SeleniumClient Unit Test', () => {
  let sel;
  before(() => {
    sinon.stub(logger, 'log').returns('true');
  });

  beforeEach(() => {
    sel = new SeleniumClient({
      name: 'host',
      port: '10101',
      key: 'sessionId',
      selenium_version: '3.141.59'
    });
  });

  after(() => {
    logger.log.restore();
  });

  context('resetApp', () => {
    it('successfully resets app', () => {
      sinon.stub(sel, 'makeRequest').returns(Promise.resolve({
        status: 0
      }));

      return sel.resetApp()
        .then((result) => {
          expect(result).to.eql('resetApp: Successfully resetApp');
          sel.makeRequest.restore();
        });
    });


    it('[w3c] successfully resets app while returning value as null', () => {
      sel = new SeleniumClient({
        name: 'host',
        port: '10101',
        key: 'sessionId',
        selenium_version: '3.141.59',
        dialect: "W3C"
      });

      sinon.stub(sel, 'makeRequest').returns(Promise.resolve({
        value: null
      }));

      return sel.resetApp()
        .then((err) => {
          expect(err).to.eql('resetApp: Successfully resetApp');
          sel.makeRequest.restore();
        });
    });

    it('if reset app fails for the appium session, status returned is non 0 and an error will be thrown', () => {
      sinon.stub(sel, 'makeRequest').returns(Promise.resolve({
        status: 13
      }));

      return sel.resetApp()
        .catch((err) => {
          expect(err).to.eql(new Error('resetApp: Error occurred while resetApp'));
          sel.makeRequest.restore();
        });
    });
  });

  context('getWindowSize', () => {
    it('gets window size', () => {
      sinon.stub(sel, 'makeRequest').returns(Promise.resolve({
        status: 0,
        value: '1234' // random size
      }));

      return sel.getWindowSize()
        .then((result) => {
          const { value } = result;
          expect(value).to.eql('1234');
          sel.makeRequest.restore();
        });
    });

    it('raises error if makeRequest fails / any other error', () => {
      sinon.stub(sel, 'makeRequest').throws("Some Random Error")

      return sel.getWindowSize()
        .catch((errString) => {
          expect(errString.name).to.eql("Some Random Error");
          sel.makeRequest.restore();
        });
    });
  });

  context('getTitle', () => {
    it('raises error if could not find page title', () => {
      sinon.stub(sel, 'makeRequest').returns(Promise.resolve({
        value: null
      }));

      return sel.getTitle()
        .catch((errString) => {
          expect(errString).to.eql(new Error("Error: Could not get page title"));
          sel.makeRequest.restore();
        });
    });
  });

  context('launchApp', () => {
    it('successfully launches app', () => {
      sinon.stub(sel, 'makeRequest').returns(Promise.resolve({
        status: 0,
      }));

      return sel.launchApp()
        .then((result) => {
          expect(result).to.eql('launchApp: Successfully launchApp');
          sel.makeRequest.restore();
        });
    });

    it('if launch app fails for the appium session, status returned is non 0 and an error will be thrown', () => {
      sinon.stub(sel, 'makeRequest').returns(Promise.resolve({
        status: 13,
      }));

      return sel.launchApp()
        .catch((err) => {
          expect(err).to.eql(new Error('launchApp: Error occurred while launchApp'));
          sel.makeRequest.restore();
        });
    });
  });

  context('getContexts', () => {
    it('successfully gets contexts', async () => {
      const contexts = { value: ['NATIVE_APP', 'WEBVIEW_1'] };
      sinon.stub(sel, 'makeRequest').returns(Promise.resolve(contexts));

      const result = await sel.getContexts();
      expect(result).to.eql(contexts);
      sel.makeRequest.restore();
    });

    it('throws an error if no contexts are found', async () => {
      sinon.stub(sel, 'makeRequest').returns(Promise.resolve(null));

      try {
        await sel.getContexts();
      } catch (err) {
        expect(err).to.be.an('error');
        expect(err.message).to.equal('Could not get contexts');
      }
      sel.makeRequest.restore();
    });
  });

  context('setContext', () => {
    it('successfully sets context', async () => {
      const makeRequestStub = sinon.stub(sel, 'makeRequest').returns(Promise.resolve());

      await sel.setContext('WEBVIEW_1');
      assert(makeRequestStub.calledOnce);
      assert(makeRequestStub.calledWith('POST', `/wd/hub/session/${sel.sessionId}/context`, { payload: { name: 'WEBVIEW_1' }, timeout: 100000 }));
      makeRequestStub.restore();
    });
  });

  describe('#findElement()', function() {
    let seleniumClient;
    before(() => {
      seleniumClient = new SeleniumClient({
        name: 'google.com',
        port: 9999,
        key: 'sessionId',
        selenium_version: '1.0.0',
        seleniumVersion: '1.0.0',
        rproxyHost: '***********',
      });
    });

    it('should throw an error if sessionId is not valid', async function() {
      seleniumClient.sessionId = null;

      try {
        await seleniumClient.findElement('id', 'test', { completeJarResponse : false, parseRespose : false });
      } catch (error) {
        expect(error.message).to.equal('No session Id');
      }
    });

    it('should throw an error if makeRequest throws an error', async function() {
      // const seleniumClient = new SeleniumClient();
      seleniumClient.sessionId = '123';
      const makeRequestStub = sinon.stub(seleniumClient, 'makeRequest').throws(new Error('Request failed'));

      try {
        await seleniumClient.findElement('id', 'test', { completeJarResponse : false, parseRespose : false });
      } catch (error) {
        expect(error.message).to.equal('Request failed');
      }

      makeRequestStub.restore();
    });

    it('should return the result of makeRequest if it does not throw an error', async function() {
      seleniumClient.sessionId = '123';
      const makeRequestStub = sinon.stub(seleniumClient, 'makeRequest').returns(Promise.resolve('Success'));

      const result = await seleniumClient.findElement('id', 'test', { completeJarResponse : false, parseRespose : false });

      expect(result).to.equal('Success');

      makeRequestStub.restore();
    });

    it('should return the result of makeRequest if it does not throw an error with default params', async function() {
      seleniumClient.sessionId = '123';
      const makeRequestStub = sinon.stub(seleniumClient, 'makeRequest').returns(Promise.resolve('Success'));

      const result = await seleniumClient.findElement('id', 'test', {});

      expect(result).to.equal('Success');

      makeRequestStub.restore();
    });
  });

  describe('#findShadowElement()', function() {
    let seleniumClient;
    before(() => {
      seleniumClient = new SeleniumClient({
        name: 'google.com',
        port: 9999,
        key: 'sessionId',
        selenium_version: '1.0.0',
        seleniumVersion: '1.0.0',
        rproxyHost: '***********',
      });
    });

    it('should throw an error if sessionId is not valid', async function() {
      seleniumClient.sessionId = null;

      try {
        await seleniumClient.findShadowElement('id', 'test', 'abcd', { completeJarResponse : false, parseRespose : false });
      } catch (error) {
        expect(error.message).to.equal('No session Id');
      }
    });

    it('should throw an error if makeRequest throws an error', async function() {
      // const seleniumClient = new SeleniumClient();
      seleniumClient.sessionId = '123';
      const makeRequestStub = sinon.stub(seleniumClient, 'makeRequest').throws(new Error('Request failed'));

      try {
        await seleniumClient.findShadowElement('id', 'test', 'abcd', { completeJarResponse : false, parseRespose : false });
      } catch (error) {
        expect(error.message).to.equal('Request failed');
      }

      makeRequestStub.restore();
    });

    it('should return the result of makeRequest if it does not throw an error', async function() {
      seleniumClient.sessionId = '123';
      const makeRequestStub = sinon.stub(seleniumClient, 'makeRequest').returns(Promise.resolve('Success'));

      const result = await seleniumClient.findShadowElement('id', 'test', 'abcd', { completeJarResponse : false, parseRespose : false });

      expect(result).to.equal('Success');

      makeRequestStub.restore();
    });

    it('should return the result of makeRequest if it does not throw an error with default params', async function() {
      seleniumClient.sessionId = '123';
      const makeRequestStub = sinon.stub(seleniumClient, 'makeRequest').returns(Promise.resolve('Success'));

      const result = await seleniumClient.findShadowElement('id', 'test', 'abcd', {});

      expect(result).to.equal('Success');

      makeRequestStub.restore();
    });
  });

});
