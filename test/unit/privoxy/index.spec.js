var redisClient = require('../../../redisUtils').redisClient;
var sinon = require('sinon');
var chai = require("chai");
var expect = chai.expect;
var {
  PRIVOXY_KEEP_ALIVE_TIMEOUTS,
  PRIVOXY_USER_KEEP_ALIVE_TIMEOUTS
} = require("../../../constants");
var helper = require("../../behaviour/helper");
var { configurePrivoxyTimeout } = require("../../../privoxy");

const userInfo = { userId: 2, groupId: 5 };

describe("Control privoxy keep alive timeout per user basis", function() {
  describe("configurePrivoxyTimeout", function() {
    beforeEach(function(done) {
      if (redisClient.hget.restore) {
        redisClient.hget.restore(); // restore the original method to prevent "already wrapped" error
      }
      done();
    });

    it("should not set privoxy parameter in case not defined", function() {
      const requestOptions = {};
      configurePrivoxyTimeout(requestOptions, {}).then(() => {
        expect(requestOptions).to.not.have.any.keys("privoxyKeepAliveTimeout");
      });
    });

    it("should give preference to groupId", function(done) {
      const requestOptions = {};
      const stub = sinon.stub(redisClient, 'hget');

      stub.withArgs(PRIVOXY_KEEP_ALIVE_TIMEOUTS, userInfo.groupId).returns("10");
      stub.withArgs(PRIVOXY_USER_KEEP_ALIVE_TIMEOUTS, userInfo.userId).returns("20");

      configurePrivoxyTimeout(requestOptions, userInfo).then(() => {
        expect(requestOptions).to.have.keys("privoxyKeepAliveTimeout");
        expect(requestOptions.privoxyKeepAliveTimeout).to.equal(10);
        stub.restore();
        done();
      });
    });


    it("should give value from userId incase groupId is not present", function(done) {
      const requestOptions = {};
      helper.hubHelper.redisClient.hset(
        PRIVOXY_USER_KEEP_ALIVE_TIMEOUTS,
        userInfo.userId,
        25
      );
      const hgetStub = sinon.stub(redisClient, 'hget');
      hgetStub.returns("25");
      configurePrivoxyTimeout(requestOptions, userInfo).then(() => {
        expect(requestOptions).to.have.keys("privoxyKeepAliveTimeout");
        expect(requestOptions.privoxyKeepAliveTimeout).to.equal(25);
        hgetStub.restore();
        done();
      });
    });

    it("should not set parameter in case of exception", function(done) {
      const requestOptions = {};
      helper.hubHelper.redisClient.hset(
        PRIVOXY_USER_KEEP_ALIVE_TIMEOUTS,
        userInfo.userId,
        25
      );
      const hgetStub = sinon.stub(redisClient, 'hget');
      hgetStub.throwsException("No property found with name");
      configurePrivoxyTimeout(requestOptions, userInfo).then(() => {
        expect(requestOptions).to.not.have.keys("privoxyKeepAliveTimeout");
        hgetStub.restore();
        done();
      });
    });

    it("should not set key in case of non numeric value", function(done) {
      const requestOptions = {};
      const hgetStub = sinon.stub(redisClient, 'hget');
      hgetStub.returns("abc");
      configurePrivoxyTimeout(requestOptions, userInfo).then(() => {
        expect(requestOptions).to.not.have.keys("privoxyKeepAliveTimeout");
        hgetStub.restore();
        done();
      });
    });
  });
});
