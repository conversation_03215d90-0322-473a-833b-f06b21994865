'use strict';

const requestTypeCorrection = require('../../../../helpers/customSeleniumHandling/requestTypeCorrection');
const { expect } = require('chai');
const frameworkConstants = require('../../../../helpers/customSeleniumHandling/frameworkConstants');
const { EXECUTE_URL, RANDOM_SESSION_ID } = require('../../frameworkSpecHelpers');

const generateRequestStateObject = {
  originalUrl: EXECUTE_URL,
};

const TEST_ARRAY = [
  ['type Text', frameworkConstants.TYPE_TEXT, 'POST:value', 'element/1234/value'],
  ['find element', frameworkConstants.FIND_ELEMENT, 'POST:element', 'element'],
  ['assert match', frameworkConstants.ASSERT_ELEMENT, 'POST:assertion', 'assertion'],
  ['open url', frameworkConstants.NAVIGATE_TO, 'POST:url', 'url'],
  ['press key', frameworkConstants.KEY_PRESS, 'POST:press', 'press'],
  ['click key', frameworkConstants.CLICK, 'POST:click', 'click'],
  ['set geolocation', frameworkConstants.SET_GEOLOCATION, 'POST:geolocation', 'set/geolocation'],
  ['get cookies', frameworkConstants.GET_COOKIES, 'GET:cookie', 'cookie'],
  ['move mouse', frameworkConstants.MOVE_MOUSE, 'POST:mouse', 'move/mouse'],
  ['handle touch', frameworkConstants.TOUCH, 'POST:touch', 'touch'],
  ['take screenshot', frameworkConstants.CAPTURE_SCREENSHOT, 'GET:screenshot', 'screenshot'],
  ['new context', frameworkConstants.NEW_CONTEXT, 'POST:newcontext', 'new/context'],
  ['generic', 'random-value', 'POST:execute', 'execute'],
  ['element focus', frameworkConstants.FOCUS, 'GET:focus', 'element/1234/focus'],
  ['mouse event moved', frameworkConstants.MOUSE_MOVED, 'POST:moved', 'mouse/moved'],
  ['mouse event pressed', frameworkConstants.MOUSE_PRESSED, 'POST:pressed', 'mouse/pressed'],
  ['mouse event released', frameworkConstants.MOUSE_RELEASED, 'POST:released', 'mouse/released'],
  ['evaluate expression', frameworkConstants.EVALUATE_EXPRESSION, 'POST:expression', 'element/1234/expression'],
];

describe('Request Type Correction', () => {
  TEST_ARRAY.forEach(([typeName, frameworkType, urlHash, suffixValue]) => {
    it(`should check for ${typeName} in requestStateObj`, () => {
      const requestStateObj = { ...generateRequestStateObject };
      requestTypeCorrection(requestStateObj, frameworkType);
      expect(requestStateObj.hash).to.be.eql(urlHash);
      expect(requestStateObj.originalUrl).to.be.eql(`/wd/hub/session/${RANDOM_SESSION_ID}/${suffixValue}`);
    });
  });
});
