'use strict';

const {
  genClickURL,
  genFindElementURL,
  genSendValueURL,
  genAssertMatchURL,
  genOpenURL,
  genSetFile,
  genNewContext,
  genMouseReleasedURL,
  genMousePressedURL,
  genMouseMovedURL,
  genEvaluateExpression,
  genFocusElement,
} = require('../../../../helpers/customSeleniumHandling/urlModifier');
const { expect } = require('chai');
const { RANDOM_SESSION_ID, EXECUTE_URL } = require('../../frameworkSpecHelpers');

const MODIFIERS = [
  [genSendValueURL, '/element/1234/value'],
  [genClickURL, '/click'],
  [genFindElementURL, '/element'],
  [genAssertMatchURL, '/assertion'],
  [genOpenURL, '/url'],
  [genSetFile, '/file'],
  [genNewContext, '/new/context'],
  [genMouseReleasedURL, '/mouse/released'],
  [genMousePressedURL, '/mouse/pressed'],
  [genMouseMovedURL, '/mouse/moved'],
  [genEvaluateExpression, '/element/1234/expression'],
  [genFocusElement, '/element/1234/focus'],
];

describe('Test URL modifier for framework logging', () => {
  MODIFIERS.forEach(([fn, urlVal]) => {
    it(`should generate send value for ${urlVal}`, () => {
      const actualVal = `/wd/hub/session/${RANDOM_SESSION_ID}${urlVal}`;
      expect(fn(EXECUTE_URL)).to.be.eql(actualVal);
    });
  });
});
