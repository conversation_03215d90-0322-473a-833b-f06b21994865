'use strict';

const kafka = require('../../clients/kafka/kafkaClient');
const constants = require('../../constants');
const stringHash = require('string-hash');
const HubLogger = require('./../../log');
const sinon = require('sinon');
const assert = require('assert');

const kafkaConfig = constants.kafkaConfig;

describe('kafkaClient.js', () => {
  it('should return getRegionBasedPartition when writePartition is passed', () => {
    const key = 'abcdef';
    const region = 'us-east-1';
    const startPartition = kafkaConfig.producer[region].startPartition;
    const endPartition = kafkaConfig.producer[region].endPartition;
    const range = (endPartition - startPartition) + 1;
    const writePartition = startPartition + (stringHash(key) % range);
    const messageKey = {
      writePartition,
      rails_session_id: key,
      originRegion: region,
    };
    assert.equal(kafka.getRegionBasedPartition(messageKey, 'raw_logs'), writePartition);
  });

  it('should return getRegionBasedPartition when writePartition is passed', () => {
    const key = 'abcdef';
    const region = 'us-east-1';
    const startPartition = kafkaConfig.producer[region].startPartitionExtended;
    const endPartition = kafkaConfig.producer[region].endPartitionExtended;
    const range = (endPartition - startPartition) + 1;
    const writePartition = startPartition + (stringHash(key) % range);
    const messageKey = {
      writePartition,
      rails_session_id: key,
      originRegion: region,
    };
    assert.equal(kafka.getRegionBasedPartition(messageKey, 'raw_extended_duration_logs'), writePartition);
  });

  it('initProducers', () => {
    const producers = kafka.initProducers('us-east-1');
    assert.equal(producers.client.options.kafkaHost, 'localhost:9092');
  });

  it('initKafkaOffset', () => {
    const kafkaOffset = kafka.initKafkaOffset();
    assert.equal(kafkaOffset.client.options.kafkaHost, 'localhost:9092');
  });

  it('sendMessage', () => {
    const messageKey = {
      writePartition: 12,
      rails_session_id: 'abcdef',
      originRegion: 'us-east-1',
    };
    const producer = kafka.initProducers('us-east-1');
    const topic = 'raw_logs';
    const message = '{"log": "data"}';
    const callback = () => {};
    const partitioner = () => {};
    const producerSend = sinon.stub(producer, 'send');
    kafka.sendMessage(producer, topic, messageKey, message, partitioner, callback);
    assert(producerSend.calledOnce === true);
    producer.send.restore();
  });

  it('tries to send message and gets error', (done) => {
    const messageKey = {
      writePartition: 12,
      rails_session_id: 'abcdef',
      originRegion: 'us-east-1',
    };
    const producer = kafka.initProducers('us-east-1');
    const errCallback = 'this is error';
    const topic = 'raw_logs';
    const message = '{"log": "data"}';
    const hubLoggerExceptionLogger = sinon.stub(HubLogger, 'exceptionLogger');
    const callback = () => {
      producer.send.restore();
      HubLogger.exceptionLogger.restore();
      assert(hubLoggerExceptionLogger.calledOnce === true);
      done();
    };
    const partitioner = () => (12);
    sinon.stub(producer, 'send').callsArgWith(1, (errCallback));
    kafka.sendMessage(producer, topic, messageKey, message, partitioner, callback);
  });

  it('tries to send messages and gets error', (done) => {
    const messageKey = {
      writePartition: 12,
      rails_session_id: 'abcdef',
      originRegion: 'us-east-1',
    };
    const producer = kafka.initProducers('us-east-1');
    const errCallback = 'this is error';
    const topic = 'raw_logs';
    const message = '{"log": "data"}';
    const hubLoggerExceptionLogger = sinon.stub(HubLogger, 'exceptionLogger');
    const callback = () => {
      producer.send.restore();
      HubLogger.exceptionLogger.restore();
      assert(hubLoggerExceptionLogger.calledOnce === true);
      done();
    };
    const partitioner = () => (12);
    sinon.stub(producer, 'send').callsArgWith(1, (errCallback));
    kafka.sendMessages(producer, topic, messageKey, [message], partitioner, callback);
  });
});
