'use strict';

const {
  describe, it, beforeEach, afterEach,
} = require('mocha');
const Mitm = require('mitm');
const debug = require('../../debug');
const HubLogger = require('../../log');
const { assert } = require('chai');
const sinon = require('sinon');
const { LOG_LEVEL: LL } = require('../../constants');

let mitm;
const HOST = '127.0.0.1';
const PORT = 3999;

describe('debug.js', () => {
  beforeEach(() => {
    mitm = Mitm();
  });

  afterEach(() => {
    mitm.disable();
  });

  it('should log timeout error on port', () => {
    sinon.spy(HubLogger, 'miscLogger');
    const portSock = debug.isPortOpen(HOST, PORT, () => {});
    portSock.emit('timeout');
    assert(HubLogger.miscLogger.calledOnce);
    sinon.assert.calledWith(HubLogger.miscLogger, 'Port Check', `Timeout checking port ${PORT} on host ${HOST} `, LL.DEBUG);
    HubLogger.miscLogger.restore();
  });

  it('should log error on port', () => {
    sinon.spy(HubLogger, 'miscLogger');
    const client = debug.isPortOpen(HOST, PORT, () => {});
    client.emit('error', { message: 'Error in upstream' });
    assert(HubLogger.miscLogger.calledOnce);
    sinon.assert.calledWith(HubLogger.miscLogger, 'Port Check', `Error checking port ${PORT} on host ${HOST}  Error : Error in upstream`, LL.DEBUG);
    HubLogger.miscLogger.restore();
  });
});
