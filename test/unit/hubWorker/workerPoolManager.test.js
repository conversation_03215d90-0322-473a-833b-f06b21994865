'use strict';

let WorkerPool;
const constants = require('../../../constants');
const sinon = require('sinon');
const { expect } = require('chai');
const rewire = require('rewire');

describe('WorkerPoolManager tests', () => {
  const beforeValue = constants.workerConf.kafkaPush.numberWorkers;

  after(() => {
    constants.workerConf.kafkaPush.numberWorkers = beforeValue;
  });

  describe('setupWorkers', () => {
    before(() => {
      constants.workerConf.kafkaPush.numberWorkers = 1;
      WorkerPool = rewire('../../../hubWorker/workerPoolManager');
      sinon.stub(WorkerPool, 'runTask');
    });

    after(() => {
      WorkerPool.runTask.restore();
    });
    it('should have setup workerFreeEvent and not call run if taskQueue is empty', () => {
      WorkerPool.tasksQueue.kafkaPush = [];
      WorkerPool.emit(WorkerPool.workerFreeEvent.kafkaPush);
      sinon.assert.notCalled(WorkerPool.runTask);
    });

    it('should have setup workerFreeEvent and call run if taskQueue has entry', () => {
      WorkerPool.tasksQueue.kafkaPush = [{ random: 'task' }];
      WorkerPool.emit(WorkerPool.workerFreeEvent.kafkaPush);
      sinon.assert.calledOnce(WorkerPool.runTask);
    });

    it('should not call addworker incase config has issues', () => {
      sinon.stub(WorkerPool.numThreads, 'hasOwnProperty').returns(false);
      sinon.stub(WorkerPool.workerFreeEvent, 'hasOwnProperty').returns(false);
      const confBackup = JSON.parse(JSON.stringify(WorkerPool.conf));
      WorkerPool.conf = {
        random: {
          numThreads: 1,
        },
      };
      WorkerPool.setupWorkers();
      WorkerPool.numThreads.hasOwnProperty.restore();
      WorkerPool.workerFreeEvent.hasOwnProperty.restore();
      WorkerPool.conf = confBackup;
    });
  });

  describe('addNewWorker', () => {
    before(() => {
      constants.workerConf.kafkaPush.numberWorkers = 1;
      WorkerPool = rewire('../../../hubWorker/workerPoolManager');
      sinon.stub(WorkerPool, 'emit');
      WorkerPool.tasksQueue.kafkaPush = [];
    });

    after(() => {
      WorkerPool.emit.restore();
    });

    it('should hove setup worker and call the callback in case of message', () => {
      sinon.stub(WorkerPool, 'addNewWorker');
      const worker = WorkerPool.workers.kafkaPush[0];
      const spy = sinon.spy();
      worker[WorkerPool.kTaskInfo] = spy;
      worker[WorkerPool.kTaskPerformance] = new Date();
      worker.emit('message', 'test');
      sinon.assert.calledOnce(WorkerPool.emit);
      sinon.assert.notCalled(WorkerPool.addNewWorker);
      sinon.assert.calledOnce(spy);
      WorkerPool.addNewWorker.restore();
    });

    it('should hove setup worker, callback error and steup new worker in case of error', () => {
      sinon.stub(WorkerPool, 'addNewWorker');
      const worker = WorkerPool.workers.kafkaPush[0];
      const spy = sinon.spy();
      worker[WorkerPool.kTaskInfo] = spy;
      worker[WorkerPool.kTaskPerformance] = new Date();
      worker.emit('error', 'test');
      sinon.assert.calledOnce(WorkerPool.emit);
      sinon.assert.calledOnce(WorkerPool.addNewWorker);
      sinon.assert.calledOnce(spy);
      WorkerPool.addNewWorker.restore();
      WorkerPool.setupWorkers();
    });

    it('should hove setup worker, callback WorkerPoolManager error and steup new worker in case of error', () => {
      sinon.stub(WorkerPool, 'addNewWorker');
      const worker = WorkerPool.workers.kafkaPush[0];
      worker[WorkerPool.kTaskPerformance] = new Date();
      worker.emit('error', 'test');
      sinon.assert.callCount(WorkerPool.emit, 3);
      sinon.assert.calledOnce(WorkerPool.addNewWorker);
      WorkerPool.addNewWorker.restore();
      WorkerPool.setupWorkers();
    });
  });

  describe('runTask', () => {
    before(() => {
      constants.workerConf.kafkaPush.numberWorkers = 1;
      WorkerPool = rewire('../../../hubWorker/workerPoolManager');
      WorkerPool.tasksQueue.kafkaPush = [];
    });

    it('should post message for single task', () => {
      // WorkerPool.tasksQueue.kafkaPush = [{random: 'task'}];
      const worker = WorkerPool.workers.kafkaPush[0];
      sinon.stub(worker, 'postMessage');
      const spy = sinon.spy();
      WorkerPool.runTask({ random: 'task' }, 'kafkaPush', spy);
      sinon.assert.calledOnce(worker.postMessage);
      worker.postMessage.restore();
      WorkerPool.freeWorkers.kafkaPush.push(worker);
    });

    it('should post message for array task', () => {
      const worker = WorkerPool.workers.kafkaPush[0];
      sinon.stub(worker, 'postMessage');
      const spy = sinon.spy();
      WorkerPool.runTask([{ random: 'task' }, { random: 'task2' }], 'kafkaPush', spy, 'array');
      sinon.assert.calledOnce(worker.postMessage);
      worker.postMessage.restore();
      WorkerPool.freeWorkers.kafkaPush.push(worker);
    });

    it('should enqueue message if no freeworker for single task', () => {
      const worker = WorkerPool.freeWorkers.kafkaPush.pop();
      const spy = sinon.spy();
      WorkerPool.runTask({ random: 'task' }, 'kafkaPush', spy);
      expect(WorkerPool.tasksQueue.kafkaPush[0]).to.deep.equal({ taskParams: { random: 'task' }, workerName: 'kafkaPush', callback: spy });
      WorkerPool.freeWorkers.kafkaPush.push(worker);
    });

    it('should enqueue message if no freeworker for array task', () => {
      WorkerPool.tasksQueue.kafkaPush = [];
      const worker = WorkerPool.freeWorkers.kafkaPush.pop();
      const spy = sinon.spy();
      WorkerPool.runTask([{ random: 'task' }], 'kafkaPush', spy, 'array');
      expect(WorkerPool.tasksQueue.kafkaPush).to.deep.equal[{ random: 'task' }];
      WorkerPool.freeWorkers.kafkaPush.push(worker);
    });
  });

  describe('close', () => {
    before(() => {
      constants.workerConf.kafkaPush.numberWorkers = 1;
      WorkerPool = rewire('../../../hubWorker/workerPoolManager');
      WorkerPool.tasksQueue.kafkaPush = [];
    });

    it('should terminate worker if closed', () => {
      const worker = WorkerPool.workers.kafkaPush[0];
      sinon.stub(worker, 'terminate');
      WorkerPool.close();
      sinon.assert.calledOnce(worker.terminate);
      worker.terminate.restore();
      WorkerPool.workers.kafkaPush.push(worker);
    });

    it('should terminate after sometime if task in queue', () => {
      WorkerPool.tasksQueue.kafkaPush = [{ random: 'task' }];
      const worker = WorkerPool.workers.kafkaPush[0];
      sinon.stub(worker, 'terminate');
      WorkerPool.close();
      WorkerPool.tasksQueue.kafkaPush = [];
      // sinon.assert.calledOnce(worker.terminate);
      worker.terminate.restore();
      WorkerPool.tasksQueue.kafkaPush = [];
      WorkerPool.workers.kafkaPush.push(worker);
    });

    it('should not call terminate worker if config has issues', () => {
      WorkerPool.tasksQueue.kafkaPush = [{ random: 'task' }];
      const worker = WorkerPool.workers.kafkaPush[0];
      sinon.stub(worker, 'terminate');
      sinon.stub(WorkerPool.tasksQueue, 'hasOwnProperty').returns(false);
      sinon.stub(WorkerPool.workers, 'hasOwnProperty').returns(false);
      WorkerPool.close();
      sinon.assert.notCalled(worker.terminate);
      worker.terminate.restore();
      WorkerPool.tasksQueue.hasOwnProperty.restore();
      WorkerPool.workers.hasOwnProperty.restore();
      WorkerPool.tasksQueue.kafkaPush = [];
      WorkerPool.workers.kafkaPush.push(worker);
    });
  });
});
