'use strict';

const AlertManager = require('../../alertManager');
const requestlib = require('../../lib/request');
const assert = require('assert');
const sinon = require('sinon');

const alertManager = new AlertManager();

describe('AlertManager', () => {
  let subject = '';
  let message = '';
  let alertReceivers = '';

  const stubClock = (time) => {
    const now = new Date() - (time);
    return sinon.useFakeTimers(now);
  };

  beforeEach(() => {
    subject = 'test subject';
    message = 'test message';
    alertReceivers = 'test';
    process.env.HUB_ENV = '';
  });

  afterEach(() => {
    process.env.HUB_ENV = 'testing';
  });

  it('should send alerts', (done) => {
    const x = sinon.stub(requestlib, 'call').returns(Promise.resolve({ statusCode: 200 }));
    const y = sinon.spy(alertManager.winston, 'info');
    alertManager.sendAlerts(subject, message, alertReceivers).then(() => {
      assert(x.calledOnce === true);
      assert(y.calledOnce === true);
      requestlib.call.restore();
      alertManager.winston.info.restore();
      done();
    });
  });

  it('should send alerts with shortenedSubject as true', (done) => {
    alertManager.lastAlertSent = (new Date()) - 40 * 60 * 1000;
    const x = sinon.stub(requestlib, 'call').returns(Promise.resolve({ statusCode: 200 }));
    const y = sinon.spy(alertManager.winston, 'info');
    alertManager.sendAlerts(subject, message, alertReceivers, 'critical', 5, false, 'P1', undefined, true).then(() => {
      assert(x.called === true);
      assert(y.called === true);
      requestlib.call.restore();
      alertManager.winston.info.restore();
      done();
    });
  });

  it('should send alerts with shortenedSubject as false', (done) => {
    alertManager.lastAlertSent = (new Date()) - 40 * 60 * 1000;
    const x = sinon.stub(requestlib, 'call').returns(Promise.resolve({ statusCode: 200 }));
    const y = sinon.spy(alertManager.winston, 'info');
    alertManager.sendAlerts(subject, message, alertReceivers, 'critical', 5, false, 'P1', undefined, false).then(() => {
      assert(x.called === true);
      assert(y.called === true);
      requestlib.call.restore();
      alertManager.winston.info.restore();
      done();
    });
  });

  it('should not send alert if last alert was sent before 30mins', (done) => {
    alertManager.lastAlertSent = new Date();
    const clock = stubClock(40 * 60 * 1000);
    const x = sinon.spy(alertManager.winston, 'error');
    const y = sinon.spy(requestlib, 'call');
    alertManager.sendAlerts(subject, message, alertReceivers).then(() => {
      assert(x.calledOnce === true);
      assert(y.called === false);
      clock.restore();
      alertManager.winston.error.restore();
      requestlib.call.restore();
      done();
    });
  });
});
