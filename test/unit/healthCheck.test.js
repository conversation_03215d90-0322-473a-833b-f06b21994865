'use strict';
const assert = require('assert');
const healthCheck = require('../../healthCheck.js');
const redisClient = require('../../redisUtils').redisClient;
const kafkaProducer = require('../../lib/kafka/kafkaProducer');
const expect = require('chai').expect;
const HubLogger = require('../../log');

describe('Testing health check api', () => { 
  it('returns 10% if 70 sessions are passed among 700', () => {
    for (let index = 1; index < 701; index++) {
      healthCheck.termialSessionList.addSession(index < 71, index);
    }
    assert.equal(healthCheck.termialSessionList.getTerminalSuccessRate(), 10, "Errors in calculating %");
  });

  it('Consider only last 700 session for terminal success rate', () => {
    for (let index = 0; index < 751; index++) {
      healthCheck.termialSessionList.addSession(true, index);
    }
    assert.equal(healthCheck.termialSessionList.size, 700, "Errors in removing past session list");
  });
});

describe('sendFinalResponse', () => {
  it('should send final respone', (done) => {
    const finalResponse = {
      checks: {
        "redis-connection": {status: 'fail'},
        "terminal-success-rate": {status: 'pass'},
      }
    };
    const callback = (status) => {
      done();
    }
    healthCheck.sendFinalResponse(finalResponse, callback)
  });
});

describe('#checkHubsHealth()', function() {
  it('should handle redis connection success', async function() {
    const callback = sinon.stub();
    const pingStub = sinon.stub(redisClient, 'ping').returns(Promise.resolve());
    const sendStub = sinon.stub(kafkaProducer.kafkaHealthCheckProducer, 'send').yields(null, {});

    await healthCheck.checkHubsHealth(callback);

    expect(callback.calledOnce).to.be.true;
    const response = callback.firstCall.args[0];
    expect(response.checks['redis-connection'].status).to.equal('pass');

    pingStub.restore();
    sendStub.restore();
  });

  it('should handle redis connection failure', async function() {
    const callback = sinon.stub();
    const pingStub = sinon.stub(redisClient, 'ping').returns(Promise.reject());
    const valueStub = sinon.stub(healthCheck.termialSessionList, 'getTerminalSuccessRate').returns(1);
    const sendStub = sinon.stub(kafkaProducer.kafkaHealthCheckProducer, 'send').yields(null, {});

    await healthCheck.checkHubsHealth(callback);

    expect(callback.calledOnce).to.be.true;
    const response = callback.firstCall.args[0];
    expect(response.checks['redis-connection'].status).to.equal('fail');

    pingStub.restore();
    sendStub.restore();
    valueStub.restore();
  });

  it('should handle kafka connection failure', async function() {
    const callback = sinon.stub();
    const pingStub = sinon.stub(redisClient, 'ping').returns();
    const sendStub = sinon.stub(kafkaProducer.kafkaHealthCheckProducer, 'send').yields(new Error('Kafka connection failed'), null);
    const miscLoggerStub = sinon.stub(HubLogger, 'miscLogger');

    await healthCheck.checkHubsHealth(callback);

    expect(callback.calledOnce).to.be.true;
    const response = callback.firstCall.args[0];
    expect(response.checks['kafka-connection'].status).to.equal('fail');
    sinon.assert.calledWith(miscLoggerStub, "kafkaHealthCheck", "Kafka connection failed", constants.LOG_LEVEL.WARN);

    pingStub.restore();
    sendStub.restore();
    miscLoggerStub.restore();
  });
});
