"use strict";

const rewire = require("rewire");
const { describe, it } = require("mocha");
const { expect, assert } = require("chai");
const sinon = require("sinon");
const lighthouseHandlerRewire = rewire("../../helpers/lighthouseHandler");
const customExecutorHelper = require("../../helpers/customSeleniumHandling/customExecutorHelper");
const requestlib = require("../../lib/request");
const HubLogger = require("../../log");

const {
  BROWSERSTACK_EXECUTOR_PREFIX,
  LH_ERROR_MESSAGES,
  LH_MIN_CHROME,
} = require("../../constants");
const LH_VALIDATED_MSG = "validated";

describe("#lighthouseHandler", () => {
  let keyObject = {
    rails_session_id: "random_session_id",
    os: "mac",
    rproxyHost: "terminal_ip",
    isPlaywright: true,
    browser: "chrome",
    browser_version: LH_MIN_CHROME.toString()
  };

  let requestStateObj = {
    req_data: JSON.stringify({
      script: `${BROWSERSTACK_EXECUTOR_PREFIX} {"action":"lighthouse","arguments":{"url":"https://www.google.com/"}}`,
    }),
    clientSessionID: "random_session_id",
  };

  let parsedCommand = JSON.parse(
    JSON.parse(requestStateObj.req_data).script.split(
      BROWSERSTACK_EXECUTOR_PREFIX
    )[1]
  );

  context("Lighthouse validation", () => {
    let lighthouseValidation;
    before(() => {
      lighthouseValidation = lighthouseHandlerRewire.__get__(
        "lighthouseValidation"
      );
    });
    it("should validate for correct OS/browser combination and framework is playwright", () => {
      const result = lighthouseValidation(keyObject);
      console.log(result);
      expect(result).to.be.equal(LH_VALIDATED_MSG);
    });

    it("should work only for playwright", () => {
      keyObject.isPlaywright = false;
      const result = lighthouseValidation(keyObject);
      expect(result).to.be.equal(LH_ERROR_MESSAGES.framework_not_supported);
      keyObject.isPlaywright = true;
    });

    it("should throw error for unsupported browser", () => {
      keyObject.browser = "firefox";
      const result = lighthouseValidation(keyObject);
      expect(result).to.be.equal(LH_ERROR_MESSAGES.browser_not_supported);
      keyObject.browser = "chrome";
    });

    it("should throw error for supported browser, unsupported version", () => {
      keyObject.browser_version = (LH_MIN_CHROME - 10).toString();
      const result = lighthouseValidation(keyObject);
      expect(result).to.be.equal(LH_ERROR_MESSAGES.chrome_version_not_supported);
    });
  });

  context("Lighthouse handler", () => {
    let lighthouseValidationOg = lighthouseHandlerRewire.__get__(
      "lighthouseValidation"
    );
    let lighthouseExecutorOg =
      lighthouseHandlerRewire.__get__("lighthouseExecutor");
    let lighthouseHandler =
      lighthouseHandlerRewire.__get__("lighthouseHandler");
    let lighthouseExecutorStub;

    beforeEach(() => {
      const lighthouseExecutorObject = {
        lighthouseExecutor:
          lighthouseHandlerRewire.__get__("lighthouseExecutor"),
      };
      lighthouseExecutorStub = sinon
        .stub(lighthouseExecutorObject, "lighthouseExecutor")
        .returns(LH_VALIDATED_MSG);
      lighthouseHandlerRewire.__set__(
        "lighthouseExecutor",
        lighthouseExecutorStub
      );
      sinon.stub(customExecutorHelper, "instrumentAndSendError");
      sinon.stub(customExecutorHelper, "instrumentAndSendExecutorResponse");
    });

    afterEach(() => {
      lighthouseHandlerRewire.__set__(
        "lighthouseValidation",
        lighthouseValidationOg
      );
      lighthouseHandlerRewire.__set__(
        "lighthouseExecutor",
        lighthouseExecutorOg
      );
      lighthouseExecutorStub.restore();
      customExecutorHelper.instrumentAndSendError.restore();
      customExecutorHelper.instrumentAndSendExecutorResponse.restore();
    });

    it("should call the executor if validation fails", () => {
      const lighthouseValidationObject = {
        lighthouseValidation: lighthouseHandlerRewire.__get__(
          "lighthouseValidation"
        ),
      };
      const lighthouseValidationStub = sinon
        .stub(lighthouseValidationObject, "lighthouseValidation")
        .returns(LH_VALIDATED_MSG);
      lighthouseHandlerRewire.__set__(
        "lighthouseValidation",
        lighthouseValidationStub
      );
      lighthouseHandler(keyObject, requestStateObj, parsedCommand);
      sinon.assert.called(lighthouseValidationStub);
      sinon.assert.called(lighthouseExecutorStub);
    });

    it("should not call the executor if validation fails", () => {
      const lighthouseValidationObject = {
        lighthouseValidation: lighthouseHandlerRewire.__get__(
          "lighthouseValidation"
        ),
      };
      const lighthouseValidationStub = sinon
        .stub(lighthouseValidationObject, "lighthouseValidation")
        .returns(LH_ERROR_MESSAGES.browser_not_supported);
      lighthouseHandlerRewire.__set__(
        "lighthouseValidation",
        lighthouseValidationStub
      );
      lighthouseHandler(keyObject, requestStateObj, parsedCommand);
      sinon.assert.called(lighthouseValidationStub);
      sinon.assert.called(
        customExecutorHelper.instrumentAndSendExecutorResponse
      );
      sinon.assert.notCalled(lighthouseExecutorStub);
    });
  });

  context("Lighthouse executor", () => {
    let lighthouseExecutor =
      lighthouseHandlerRewire.__get__("lighthouseExecutor");
    const successData = {
      statusCode: 200,
      data: '{"lhSuccess": "true", "data": "abc"}',
    };
    const failData = {
      statusCode: 500,
      data: '{"lhSuccess": "false", "data": "abc"}',
    };

    let requestLibCallStub;

    beforeEach(() => {
      requestLibCallStub = sinon.stub(requestlib, "call");
      sinon.stub(customExecutorHelper, "instrumentAndSendError");
      sinon.stub(customExecutorHelper, "instrumentAndSendExecutorResponse");
      sinon.stub(requestlib, "getEncodedURLParams");
      sinon.stub(requestlib, "appendBStackHostHeader");
      sinon.stub(HubLogger, "exceptionLogger");
    });

    afterEach(() => {
      requestLibCallStub.restore();
      customExecutorHelper.instrumentAndSendError.restore();
      customExecutorHelper.instrumentAndSendExecutorResponse.restore();
      requestlib.getEncodedURLParams.restore();
      requestlib.appendBStackHostHeader.restore();
      HubLogger.exceptionLogger.restore();
    });

    it("should send executor response if received data from terminal", () => {
      requestLibCallStub.returns(successData);

      lighthouseExecutor(keyObject, requestStateObj, parsedCommand);

      sinon.assert.called(requestlib.getEncodedURLParams);
      sinon.assert.called(requestlib.appendBStackHostHeader);
      sinon.assert.called(requestLibCallStub);
      sinon.assert.notCalled(HubLogger.exceptionLogger);
      sinon.assert.notCalled(customExecutorHelper.instrumentAndSendError);
    });

    it("should send executor response if received data from terminal with remote debugger port with win", () => {
      keyObject.isLaunchPersistentContext = true;
      keyObject.os = "win";
      requestLibCallStub.returns(successData);

      lighthouseExecutor(keyObject, requestStateObj, parsedCommand);

      sinon.assert.called(requestlib.getEncodedURLParams);
      sinon.assert.called(requestlib.appendBStackHostHeader);
      sinon.assert.called(requestLibCallStub);
      sinon.assert.notCalled(HubLogger.exceptionLogger);
      sinon.assert.notCalled(customExecutorHelper.instrumentAndSendError);
    });

    it("should throw executor if received exception from terminal", () => {
      requestLibCallStub.returns(failData);

      lighthouseExecutor(keyObject, requestStateObj, parsedCommand);

      sinon.assert.called(requestlib.getEncodedURLParams);
      sinon.assert.called(requestlib.appendBStackHostHeader);
      sinon.assert.called(requestLibCallStub);
      sinon.assert.notCalled(
        customExecutorHelper.instrumentAndSendExecutorResponse
      );
    });
  });
});
