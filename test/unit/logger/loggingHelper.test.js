const loggingHelper = require('../../../utils/logger/loggingHelper');
const HubLogger = require('../../../log');


const assert = require('assert');
const sinon = require('sinon');

describe('sendCombinedLogs', function() {
  it('should send combined logs', function() {
    const request = {
      log_data: 'initial log data',
      method: 'GET',
      url: '/wd/hub/test',
      log_data: { length: 3 * 1024 * 1024 }
    };
    const sessionKeyObj = {
      rails_session_id: '12345',
      realMobile: true,
      os: 'ios',
      maskCommands: []
    };
    const data = 'test data';
    const debugScreenshot = 'test screenshot';
    const featureLog = {};

    let sessionLog = sinon.stub(HubLogger, 'sessionLog');
    let miscLogger = sinon.stub(HubLogger, 'miscLogger');

    loggingHelper.sendCombinedLogs(request, sessionKeyObj, data, debugScreenshot, featureLog);

    assert(HubLogger.sessionLog.calledOnce);
    assert(HubLogger.miscLogger.calledOnce);
    sessionLog.restore();
    miscLogger.restore();
  });

  it('should send combined logs for ai', function() {
    const request = {
      log_data: 'initial log data',
      method: 'GET',
      url: '/wd/hub/test',
      log_data: { length: 3 * 1024 * 1024 }
    };
    const sessionKeyObj = {
      rails_session_id: '12345',
      realMobile: true,
      os: 'ios',
      maskCommands: []
    };
    const data = 'test data';
    const debugScreenshot = 'test screenshot';
    const featureLog = { logType: 'ai_session' };

    let sessionLog = sinon.stub(HubLogger, 'sessionLog');
    let miscLogger = sinon.stub(HubLogger, 'miscLogger');

    loggingHelper.sendCombinedLogs(request, sessionKeyObj, data, debugScreenshot, featureLog);

    assert(HubLogger.sessionLog.calledOnce);
    assert(HubLogger.miscLogger.calledOnce);
    sessionLog.restore();
    miscLogger.restore();
  });
});
