"use strict";

const jsExecutor = require('../../lib/customSeleniumHandler/jsExecutor');
const bridge = require("../../bridge");
const expect = require("chai").expect;
const customExecutorHelper = require("../../helpers/customSeleniumHandling/customExecutorHelper");
const helper = require("../../helper");
const sinon = require("sinon");
const {
  generateRequestObject,
  TEXT_PAYLOAD,
} = require("./frameworkSpecHelpers");

const annotator = require("../../helpers/customSeleniumHandling/annotator");

describe("annoted logger", function() {
  context("Annotate testcafe logger", function() {
    beforeEach(() => {
      sinon.stub(bridge, "sendResponse");
      sinon.stub(customExecutorHelper, "sendError");
      sinon.stub(helper,'sendToEDS');
    });

    afterEach(() => {
      bridge.sendResponse.restore();
      customExecutorHelper.sendError.restore();
      helper.sendToEDS.restore();
    });

    it("should not start annoted log", function() {
      const reqObj = {
        req_data: JSON.stringify({
          script: 'bs_action: {"action": "randomAction"}'
        })
      };

      const keyObj = {
        rails_session_id: "random_session_id",
        appTesting: false
      };

      const result = jsExecutor.checkandExecuteIfBstackExecutor(keyObj, reqObj);
      expect(result).to.be.false;
    });

    it("should start annoted log", function() {
      const reqObj = generateRequestObject();

      const keyObj = {
        rails_session_id: "random_session_id",
        appTesting: false
      };

      const result = jsExecutor.checkandExecuteIfBstackExecutor(keyObj, reqObj);
      expect(result).to.be.true;
      bridge.sendResponse.called.should.eql(true);
    });

    it("should send response in case of type-text event", function() {
      const reqObj = generateRequestObject();

      const keyObj = {
        rails_session_id: "random_session_id",
        appTesting: false
      };

      const data = TEXT_PAYLOAD;
      annotator.annotate(keyObj, reqObj, data);

      bridge.sendResponse.called.should.eql(true);
    });
  });
});
