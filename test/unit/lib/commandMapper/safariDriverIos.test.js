'use strict';

const { mapSafariDriverRequest, convertToJavascriptPayload, mapAllCookieCommand } = require('../../../../lib/commandMapper/safariDriverIos');
const constants = require('../../../../constants');
const expect = require('chai').expect;

describe('mapSafariDriverRequest function', () => {
  it('should map click to execute javasript click', () => {
    const options = {
      path: '/wd/hub/session/abcd/element/efgh/click',
      method: 'POST',
      body: '',
      headers: {},
    };
    const payload = convertToJavascriptPayload(constants.CLICK_SCRIPT_PAYLOAD, {
      ELEMENT: 'efgh',
      'element-6066-11e4-a52e-4f735466cecf': 'efgh',
    });
    const mappedOptions = {
      path: '/wd/hub/session/abcd/execute/sync',
      method: 'POST',
      body: JSON.stringify(payload),
      headers: {},
    };
    mappedOptions.headers['content-length'] = Buffer.byteLength(mappedOptions.body);
    const mapped = mapSafariDriverRequest(options);
    mapped.should.deep.equal(mappedOptions);
  });

  it('should delete all cookies to execute javasript delete Cookie', () => {
    const options = {
      path: '/wd/hub/session/abcd/cookie',
      method: 'DELETE',
      body: '',
      headers: {},
    };
    const payload = convertToJavascriptPayload(constants.DELETE_ALL_COOKIES_PAYLOAD);
    const mappedOptions = {
      path: '/wd/hub/session/abcd/execute/sync',
      method: 'POST',
      body: JSON.stringify(payload),
      headers: {},
    };
    mappedOptions.headers['content-length'] = Buffer.byteLength(mappedOptions.body);
    const mapped = mapSafariDriverRequest(options);
    mapped.should.deep.equal(mappedOptions);
  });

  it('should map find element by id to css selector', () => {
    const options = {
      path: '/wd/hub/session/abcd/element',
      method: 'POST',
      body: '{"using":"id","value":"q"}',
      headers: {},
    };
    const mappedOptions = {
      path: '/wd/hub/session/abcd/element',
      method: 'POST',
      body: '{"using":"css selector","value":"[id=q]"}',
      headers: {},
    };
    mappedOptions.headers['content-length'] = Buffer.byteLength(mappedOptions.body);
    const mapped = mapSafariDriverRequest(options);
    mapped.should.deep.equal(mappedOptions);
  });

  it('should map find element by tag name to css selector', () => {
    const options = {
      path: '/wd/hub/session/abcd/element/efgh/elements',
      method: 'POST',
      body: '{"using":"tag name","value":"q"}',
      headers: {},
    };
    const mappedOptions = {
      path: '/wd/hub/session/abcd/element/efgh/elements',
      method: 'POST',
      body: '{"using":"css selector","value":"q"}',
      headers: {},
    };
    mappedOptions.headers['content-length'] = Buffer.byteLength(mappedOptions.body);
    const mapped = mapSafariDriverRequest(options);
    mapped.should.deep.equal(mappedOptions);
  });

  it('should map find element by name to css selector', () => {
    const options = {
      path: '/wd/hub/session/abcd/element/efgh/elements',
      method: 'POST',
      body: '{"using":"name","value":"q"}',
      headers: {},
    };
    const mappedOptions = {
      path: '/wd/hub/session/abcd/element/efgh/elements',
      method: 'POST',
      body: '{"using":"css selector","value":"[name=q]"}',
      headers: {},
    };
    mappedOptions.headers['content-length'] = Buffer.byteLength(mappedOptions.body);
    const mapped = mapSafariDriverRequest(options);
    mapped.should.deep.equal(mappedOptions);
  });

  it('should map find element by class name to css selector', () => {
    const options = {
      path: '/wd/hub/session/abcd/element/efgh/elements',
      method: 'POST',
      body: '{"using":"class name","value":"q"}',
      headers: {},
    };
    const mappedOptions = {
      path: '/wd/hub/session/abcd/element/efgh/elements',
      method: 'POST',
      body: '{"using":"css selector","value":".q"}',
      headers: {},
    };
    mappedOptions.headers['content-length'] = Buffer.byteLength(mappedOptions.body);
    const mapped = mapSafariDriverRequest(options);
    mapped.should.deep.equal(mappedOptions);
  });

  it('should return empty response if path method is undefined', () => {
    const options = {
      path: undefined,
      method: undefined,
      body: '',
      headers: {
        'content-length': 0,
      },
    };
    const mappedOptions = {
      path: '',
      method: undefined,
      body: '',
      headers: {},
    };
    mappedOptions.headers['content-length'] = Buffer.byteLength(mappedOptions.body);
    const mapped = mapSafariDriverRequest(options);
    mapped.should.deep.equal(mappedOptions);
  });

  it('should delete all cookies to execute javasript delete Cookie', () => {
    const options = {
      path: '/wd/hub/session/abcd/cookie/efgh',
      method: 'DELETE',
      body: '',
      headers: {},
    };
    const payload = convertToJavascriptPayload(constants.DELETE_NAMED_COOKIES_PAYLOAD, 'efgh');
    const mappedOptions = {
      path: '/wd/hub/session/abcd/execute/sync',
      method: 'POST',
      body: JSON.stringify(payload),
      headers: {},
    };
    mappedOptions.headers['content-length'] = Buffer.byteLength(mappedOptions.body);
    const mapped = mapSafariDriverRequest(options);
    mapped.should.deep.equal(mappedOptions);
  });
});

describe('mapAllCookieCommand', () => {
  it('should correctly map the request object for deleting all cookies', () => {
    const request = {
      method: 'GET',
      path: '/api/cookie',
      body: '',
      headers: {},
    };

    const expectedMappedRequest = {
      method: 'POST',
      path: '/api/execute/sync',
      body: JSON.stringify(convertToJavascriptPayload(constants.DELETE_ALL_COOKIES_PAYLOAD)),
      headers: {
        'content-length': Buffer.byteLength(JSON.stringify(convertToJavascriptPayload(constants.DELETE_ALL_COOKIES_PAYLOAD))),
      },
    };
    const mappedRequest = mapAllCookieCommand(request);
    expect(mappedRequest).to.deep.equal(expectedMappedRequest);
  });
});
