'use strict';

const sinon = require('sinon');
const kafkaProducer = require('../../../../lib/kafka/kafkaProducer');
const HubLogger = require('../../../../log');
const constants = require('../../../../constants');
const helper = require('../../../../helper');
const instrumentation = require('../../../../helpers/instrumentation');
const rewire = require('rewire');

describe('kafkaProducer', () => {
  describe('uploadLogPartToKafkaV2', () => {
    const keyObject = {
      rails_session_id: 'keyObject.rails_session_id',
      request_count: 'keyObject.request_count',
      originRegion: 'keyObject.originRegion',
      appTesting: 'keyObject.appTesting',
      logs_aws_storage_class: 'keyObject.logs_aws_storage_class',
      writePartition: 'keyObject.writePartition',
    };
    const type = 'REQUEST_START';
    const logdata = 'random_data';
    const topic = 'performance_logs';
    it('should call runtask', () => {
      const spy = sinon.spy();
      kafkaProducer.setKafkaWorkerPool({ runTask: spy });
      kafkaProducer.uploadLogPartToKafkaV2(keyObject, type, logdata, topic);
      sinon.assert.calledOnce(spy);
    });

    it('shhould log error in case of runtask error', () => {
      sinon.stub(HubLogger, 'exceptionLogger');
      kafkaProducer.setKafkaWorkerPool({
        runTask(taskParams, worker, callback) {
          callback('error');
        },
      });
      kafkaProducer.uploadLogPartToKafkaV2(keyObject, type, logdata, topic);
      sinon.assert.calledOnce(HubLogger.exceptionLogger);
      HubLogger.exceptionLogger.restore();
    });
  });

  describe('uploadLogPartToKafkaV2 Calling', () => {
    let beforeValue;
    beforeEach(() => {
      beforeValue = constants.workerConf.kafkaPush.numberWorkers;
      constants.workerConf.kafkaPush.numberWorkers = 1;
    });

    afterEach(() => {
      constants.workerConf.kafkaPush.numberWorkers = beforeValue;
    });

    const keyObject = {
      rails_session_id: 'keyObject.rails_session_id',
      request_count: 'keyObject.request_count',
      originRegion: 'keyObject.originRegion',
      appTesting: 'keyObject.appTesting',
      logs_aws_storage_class: 'keyObject.logs_aws_storage_class',
      writePartition: 'keyObject.writePartition',
    };
    const type = 'STOP_SESSION';
    const logdata = 'random_data';
    const topic = 'performance_logs';
    it('should call v2 method in case kafkaWorkerFlags is enabled', () => {
      sinon.stub(kafkaProducer, 'uploadLogPartToKafkaV2');
      constants.kafkaWorkerFlags.enabled = true;
      kafkaProducer.uploadLogPartToKafka(keyObject, type, logdata, topic);
      sinon.assert.calledOnce(kafkaProducer.uploadLogPartToKafkaV2);
      kafkaProducer.uploadLogPartToKafkaV2.restore();
      constants.kafkaWorkerFlags.enabled = false;
    });
  });

  describe('uploadLogPartToKafkaV1 calling', () => {
    let beforeValue;
    let mod;
    let uploadLogPartToKafkaV1Mock;
    let orgUploadLogPartToKafkaV1;

    beforeEach(() => {
      beforeValue = constants.workerConf.kafkaPush.numberWorkers;
      constants.workerConf.kafkaPush.numberWorkers = 0;
      mod = rewire('../../../../lib/kafka/kafkaProducer');
      orgUploadLogPartToKafkaV1 = mod.__get__('uploadLogPartToKafkaV1');
      uploadLogPartToKafkaV1Mock = sinon.stub();
      mod.__set__('uploadLogPartToKafkaV1', uploadLogPartToKafkaV1Mock);
    });

    afterEach(() => {
      constants.workerConf.kafkaPush.numberWorkers = beforeValue;
    });

    const keyObject = {
      rails_session_id: 'keyObject.rails_session_id',
      request_count: 'keyObject.request_count',
      originRegion: 'keyObject.originRegion',
      appTesting: 'keyObject.appTesting',
      logs_aws_storage_class: 'keyObject.logs_aws_storage_class',
      writePartition: 'keyObject.writePartition',
    };
    const type = 'STOP_SESSION';
    const logdata = 'random_data';
    const topic = 'performance_logs';
    it('should call v1 as worker count is 0', () => {
      mod.uploadLogPartToKafka(keyObject, type, logdata, topic);
      sinon.assert.calledOnce(uploadLogPartToKafkaV1Mock);
    });
  });

  describe('uploadLogPartToKafkaV1 calling - 2', () => {
    let beforeValue;
    let beforeValue1;
    let beforeValue2;
    let mod;
    let uploadLogPartToKafkaV1Mock;
    let orgUploadLogPartToKafkaV1;

    beforeEach(() => {
      beforeValue = constants.workerConf.kafkaPush.numberWorkers;
      constants.workerConf.kafkaPush.numberWorkers = 0;
      beforeValue1 = constants.kafkaWorkerFlags.enabled;
      beforeValue2 = constants.kafkaWorkerFlags.partiallyEnabled;
      constants.kafkaWorkerFlags.enabled = false;
      constants.kafkaWorkerFlags.partiallyEnabled = false;
      mod = rewire('../../../../lib/kafka/kafkaProducer');
      orgUploadLogPartToKafkaV1 = mod.__get__('uploadLogPartToKafkaV1');
      uploadLogPartToKafkaV1Mock = sinon.stub();
      mod.__set__('uploadLogPartToKafkaV1', uploadLogPartToKafkaV1Mock);
    });

    afterEach(() => {
      constants.workerConf.kafkaPush.numberWorkers = beforeValue;
      constants.kafkaWorkerFlags.enabled = beforeValue1;
      constants.kafkaWorkerFlags.partiallyEnabled = beforeValue2;
    });

    const keyObject = {
      rails_session_id: 'keyObject.rails_session_id',
      request_count: 'keyObject.request_count',
      originRegion: 'keyObject.originRegion',
      appTesting: 'keyObject.appTesting',
      logs_aws_storage_class: 'keyObject.logs_aws_storage_class',
      writePartition: 'keyObject.writePartition',
    };
    const type = 'STOP_SESSION';
    const logdata = 'random_data';
    const topic = 'performance_logs';
    it('should call v1 as worker count is 0', () => {
      mod.uploadLogPartToKafka(keyObject, type, logdata, topic);
      sinon.assert.calledOnce(uploadLogPartToKafkaV1Mock);
    });
  });

  describe('kafkaPayloadLengthErrorInstrumentation', () => {
    const keyObject = {
      rails_session_id: 'keyObject.rails_session_id',
      request_count: 'keyObject.request_count',
      originRegion: 'keyObject.originRegion',
      appTesting: 'keyObject.appTesting',
      logs_aws_storage_class: 'keyObject.logs_aws_storage_class',
      writePartition: 'keyObject.writePartition',
    };
    const type = 'STOP_SESSION';
    const payLoadLength = 'random_data';
    const topic = 'performance_logs';
    it('should send instrumentation', () => {
      sinon.stub(helper, 'PingZombie');
      sinon.stub(instrumentation, 'pushFeatureUsage');
      kafkaProducer.kafkaPayloadLengthErrorInstrumentation(topic, keyObject, payLoadLength, type);
      helper.PingZombie.restore();
      instrumentation.pushFeatureUsage.restore();
    });
  });
});
