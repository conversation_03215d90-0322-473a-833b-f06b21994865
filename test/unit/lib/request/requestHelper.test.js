'use strict';

const requestHelper = require('../../../../lib/request/requestHelper');
const { assert } = require('chai');

describe('requestHelper tests', () => {
  describe('getEncodedURLParams', () => {
    it('params not a hash should return blank', () => {
      const params = 'random';
      const url = requestHelper.getEncodedURLParams(params);
      assert.equal(url, '');
    });

    it('params a hash should return url', () => {
      const params = {
        price: '$12',
      };
      const url = requestHelper.getEncodedURLParams(params);
      assert.equal(url, 'price=%2412');
    });
  });

  describe('appendBStackHostHeader', () => {
    it('should update existing header with bstack header', () => {
      const headers = {
        random: 'value',
      };
      const newHeader = requestHelper.appendBStackHostHeader('***********', headers);
      headers['BStack-Host'] = '***********';
      headers['X-Source-Env-Type'] = 'development';
      assert.deepEqual(newHeader, headers);
    });

    it('should return header with bstack header', () => {
      const headers = {
      };
      const newHeader = requestHelper.appendBStackHostHeader('***********');
      headers['BStack-Host'] = '***********';
      headers['X-Source-Env-Type'] = 'development';
      assert.deepEqual(newHeader, headers);
    });
  });
});
