'use strict';

/* eslint-disable no-global-assign */

const helper = require('./helper');
const cluster = require('../../cluster');
const sinon = require('sinon');
const rewire = require('rewire');
const fs = require('fs');

describe('Cluster', () => {
  describe('checkIfAlive', () => {
    let sandbox;
    let timeoutIds = [];
    let backupSetTimeout;
    let idx = 0;

    beforeEach(() => {
      sandbox = sinon.sandbox.create();
      backupSetTimeout = setTimeout;

      setTimeout = () => {
        const id = idx;
        idx += 1;

        timeoutIds.push(id);
        return id;
      };

      while (cluster.oldWorkers.pop());
      Object.keys(cluster.timeouts).forEach((key) => { delete cluster.timeouts[key]; });
    });

    afterEach(() => {
      sandbox.restore();
      setTimeout = backupSetTimeout;
      backupSetTimeout = undefined;
      idx = 0;
      timeoutIds = [];
      while (cluster.oldWorkers.pop());
      Object.keys(cluster.timeouts).forEach((key) => { delete cluster.timeouts[key]; });
    });

    it('does not queue further actions if worker is dead', () => {
      const worker = helper.getClusterWorker();
      sandbox.stub(worker, 'isDead').returns(true);

      cluster.checkIfAlive(worker);
      timeoutIds.length.should.be.equal(0);
    });

    it('does not queue further actions if worker is not dead and there are no older workers', () => {
      const worker = helper.getClusterWorker();
      sandbox.stub(worker, 'isDead').returns(false);

      cluster.checkIfAlive(worker);
      timeoutIds.length.should.be.equal(0);
    });

    it('queues further retry if worker is not dead and the worker isnt in listening state', () => {
      const worker = helper.getClusterWorker('something');
      sandbox.stub(worker, 'isDead').returns(false);

      const oldWorker = helper.getClusterWorker();
      const spy = sinon.spy(oldWorker, 'disconnect');
      cluster.oldWorkers.push(oldWorker);

      cluster.checkIfAlive(worker);
      timeoutIds.length.should.be.equal(1);
      spy.notCalled.should.be.equal(true);
    });

    it('queues forcekill if worker is not dead and the worker is in listening state', () => {
      const worker = helper.getClusterWorker('listening');
      sandbox.stub(worker, 'isDead').returns(false);

      const oldWorker = helper.getClusterWorker();
      const spy = sinon.spy(oldWorker, 'disconnect');
      cluster.oldWorkers.push(oldWorker);

      cluster.checkIfAlive(worker);
      timeoutIds.length.should.be.equal(1);
      Object.keys(cluster.timeouts).length.should.be.equal(1);
      cluster.oldWorkers.length.should.be.equal(0);
      spy.called.should.be.equal(true);
    });

    it('queues forcekill if worker is not dead and the worker is not in listening state if retries have exhausted', () => {
      const worker = helper.getClusterWorker('something');
      sandbox.stub(worker, 'isDead').returns(false);

      const oldWorker = helper.getClusterWorker();
      const spy = sinon.spy(oldWorker, 'disconnect');
      cluster.oldWorkers.push(oldWorker);

      cluster.checkIfAlive(worker, 10);
      timeoutIds.length.should.be.equal(1);
      Object.keys(cluster.timeouts).length.should.be.equal(1);
      cluster.oldWorkers.length.should.be.equal(0);
      spy.called.should.be.equal(true);
    });
  });

  describe('forceKill', () => {
    let sandbox;

    beforeEach(() => {
      sandbox = sinon.sandbox.create();
    });

    afterEach(() => {
      sandbox.restore();
    });

    it('doesnt kill worker if it is dead', () => {
      const worker = helper.getClusterWorker();
      sandbox.stub(worker, 'isDead').returns(true);
      const spy = sinon.spy(worker, 'kill');

      cluster.forceKill(worker);

      spy.called.should.be.equal(false);
    });

    it('kills worker if it is not dead', () => {
      const worker = helper.getClusterWorker();
      sandbox.stub(worker, 'isDead').returns(false);
      const spy = sinon.spy(worker, 'kill');

      cluster.forceKill(worker);

      spy.called.should.be.equal(true);
    });
  });

  describe('reload', () => {
    let sandbox;
    let timeoutIds = [];
    let backupSetTimeout;
    let idx = 0;
    let spy;

    beforeEach(() => {
      sandbox = sinon.sandbox.create();
      backupSetTimeout = setTimeout;

      setTimeout = () => {
        const id = idx;
        idx += 1;

        timeoutIds.push(id);
        return id;
      };

      spy = sinon.spy(cluster.util, 'sendAlerts');
    });

    afterEach(() => {
      sandbox.restore();
      setTimeout = backupSetTimeout;
      backupSetTimeout = undefined;
      idx = 0;
      timeoutIds = [];
      cluster.util.sendAlerts.restore();
    });

    it('send alert on deploy failure', () => {
      sandbox.stub(cluster.util, 'isSafeToStartProcess').returns(false);
      cluster.reload();
      spy.called.should.be.equal(true);
    });

    it('creates new workers on reload', () => {
      sandbox.stub(cluster.util, 'isSafeToStartProcess').returns(true);
      sandbox.stub(cluster.cluster, 'fork').returns(true);

      cluster.reload();
      spy.called.should.be.equal(false);
      timeoutIds.length.should.be.equal(3);
    });
  });

  describe('stop', () => {
    let sandbox;

    beforeEach(() => {
      sandbox = sinon.sandbox.create();
    });

    afterEach(() => {
      sandbox.restore();
    });

    it('discunnect the workers', () => {
      const worker = helper.getClusterWorker();
      const spy = sinon.spy(worker, 'disconnect');
      const originalWorkers = cluster.cluster.workers;
      cluster.cluster.workers = {
        1: worker,
      };

      cluster.stop();
      spy.called.should.be.equal(true);
      cluster.cluster.workers = originalWorkers;
    });
  });
});
