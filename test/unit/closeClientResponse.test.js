'use strict';

/* eslint-disable no-underscore-dangle */

const rewire = require('rewire');
const sinon = require('sinon');
const { expect } = require('chai');

const rewiredHub = rewire('../../clientRequest/responseChecks');

describe('Check response online', () => {
  const checkResponseOnline = rewiredHub.__get__('checkResponseOnline');
  let responseStub;
  const bsStub = {};
  const pingStatsStub = {};
  const addStopStub = {};
  const sessionRemoveStub = {};
  const publishStub = {};
  let keyObject = {};
  const stopURLStub = {};
  let onlineStub;

  beforeEach(() => {
    onlineStub = sinon.stub();
    responseStub = {
      clientOnline: () => onlineStub
    };
    keyObject = {
      rails_session_id: 'rails-1234',
      isPuppeteer: true,
      isPlaywright: false
    };
    bsStub.orig = rewiredHub.__get__('browserstack');
    bsStub.stub = sinon.stub(bsStub.orig, 'postBrowserStack');
    pingStatsStub.orig = rewiredHub.__get__('helper');
    pingStatsStub.stub = sinon.stub(pingStatsStub.orig, 'pingDataToStats');
    addStopStub.orig = rewiredHub.__get__('HubLogger');
    addStopStub.stub = sinon.stub(addStopStub.orig, 'addStopToRawLogs');
    sessionRemoveStub.orig = rewiredHub.__get__('helper');
    sessionRemoveStub.stub = sinon.stub(sessionRemoveStub.orig, 'sessionRemovedFromRegionHook');
    publishStub.orig = rewiredHub.__get__('pubSub');
    publishStub.stub = sinon.stub(publishStub.orig, 'publish');
    stopURLStub.orig = rewiredHub.__get__('helper');
    stopURLStub.stub = sinon.stub(stopURLStub.orig, 'buildRailsStopUrlParams');
  });

  afterEach(() => {
    bsStub.stub.restore();
    publishStub.stub.restore();
    sessionRemoveStub.stub.restore();
    addStopStub.stub.restore();
    stopURLStub.stub.restore();
    pingStatsStub.stub.restore();
  });

  it('Should close if cdp session and client not online', () => {
    responseStub = { clientOnline: () => false };
    stopURLStub.stub.returns({ stopUrl: '/some_url', stopPostParams: {} });
    bsStub.stub.yields(null);
    checkResponseOnline(keyObject, responseStub);
    expect(addStopStub.stub.calledOnce).to.be.true;
    expect(pingStatsStub.stub.calledOnce).to.be.true;
    expect(sessionRemoveStub.stub.calledOnce).to.be.true;
    expect(publishStub.stub.calledOnce).to.be.true;
  });

  it('Should not do anything for non-cdp sessions', () => {
    responseStub = { clientOnline: () => false };
    keyObject.isPuppeteer = false;
    stopURLStub.stub.returns({ stopUrl: '/some_url', stopPostParams: {} });
    bsStub.stub.yields(null);
    checkResponseOnline(keyObject, responseStub);
    expect(addStopStub.stub.calledOnce).to.be.false;
    expect(pingStatsStub.stub.calledOnce).to.be.false;
    expect(sessionRemoveStub.stub.calledOnce).to.be.false;
    expect(publishStub.stub.calledOnce).to.be.false;
  });
});
