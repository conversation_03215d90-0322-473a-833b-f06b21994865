'use strict';

const sinon = require('sinon');
const RestAPIHandler = require('../../helpers/customSeleniumHandling/restAPIHandler');
const requestLib = require('../../lib/request');
const customExecutorHelper = require('../../helpers/customSeleniumHandling/customExecutorHelper');
const Qig = require('../../helpers/qig');
const constants = require('../../constants');

const EXECUTOR_TYPE = 'any_rest_action';

describe('RestAPIHandler', () => {
  context('Validate constructors & header', () => {
    it('for app-automate', async () => {
      const restAPIHandler = new RestAPIHandler('randomSessionID', true);
      RestAPIHandler.DEFAULT_HEADERS.should.eql({
        accept: 'application/json',
        'content-type': 'application/json;',
        'user-agent': 'hub.browserstack.com', // User-agent is hardcoded here as this specific user-agent is being used on rails while using custom executors.
      });
      restAPIHandler.host.should.eql(constants.APP_AUTOMATE_BSTACK_API_HOST);
      restAPIHandler.product.should.eql('app-automate');
    });

    it('for automate', async () => {
      const restAPIHandler = new RestAPIHandler('randomSessionID', false);
      RestAPIHandler.DEFAULT_HEADERS.should.eql({
        accept: 'application/json',
        'content-type': 'application/json;',
        'user-agent': 'hub.browserstack.com', // User-agent is hardcoded here as this specific user-agent is being used on rails while using custom executors.
      });
      restAPIHandler.host.should.eql(constants.BSTACK_API_HOST);
      restAPIHandler.product.should.eql('automate');
    });
  });

  context('Validates makeRequest method. ', () => {
    it('Tests rest API call (irrespective of error or success)', async () => {
      const method = 'PUT';
      const path = '/automate/sessions.json';
      const body = '{"status":"failed","reason": "some random reason"}';
      const headers = RestAPIHandler.DEFAULT_HEADERS;
      const data = '{"automation_session":{"name":"fix sanity","duration":null,"os":"Windows","os_version":"10","browser_version":"72.0","browser":"chrome","device":null,"status":"passed","hashed_id":"0c4d8424e2908a90934ab72337c375a9b212315e","reason":"something went good","build_name":"custom executor","project_name":"Untitled Project"}}';
      const expectedResponse = `{"statusCode": 200,"data": ${data}}`;
      const restAPIHandler = new RestAPIHandler('randomSessionID', true);

      sinon.stub(requestLib, 'call').returns(expectedResponse);

      const actualResponse = await restAPIHandler.makeRequest(method, path, body, headers);

      const responseObject = JSON.parse(actualResponse);
      responseObject.statusCode.should.eql(200);
      requestLib.call.restore();
    });
  });

  context('Validates runUpdateSessionStateExecutor method', () => {
    beforeEach(() => {
      sinon.stub(customExecutorHelper, 'instrumentAndSendExecutorResponse');
      sinon.stub(customExecutorHelper, 'instrumentAndSendError');
      sinon.stub(Qig, 'markSessionStatusExecutor');
    });

    afterEach(() => {
      customExecutorHelper.instrumentAndSendExecutorResponse.restore();
      customExecutorHelper.instrumentAndSendError.restore();
      Qig.markSessionStatusExecutor.restore();
    });

    // TODO: make the unit tests robust by including non-empty `parsedCommands`
    // and asserting the args with which the API request is made.
    it('Tests successful updation of status for automate', async () => {
      const data = '{"automation_session":{"name":"fix sanity","duration":null,"os":"Windows","os_version":"10","browser_version":"72.0","browser":"chrome","device":null,"status":"passed","hashed_id":"0c4d8424e2908a90934ab72337c375a9b212315e","reason":"something went good","build_name":"custom executor","project_name":"Untitled Project"}}';
      const expectedResponse = { statusCode: 200, data };
      const restAPIHandler = new RestAPIHandler('randomSessionID', false);
      const keyObject = {};
      const requestStateObject = {
        clientSessionID: 12345,
      };
      const parsedCommand = { action: EXECUTOR_TYPE };
      sinon.stub(restAPIHandler, 'makeRequest').returns(expectedResponse);

      await restAPIHandler.runUpdateSessionStateExecutor(
        keyObject,
        requestStateObject,
        parsedCommand
      );

      restAPIHandler.makeRequest.called.should.eql(true);
      customExecutorHelper.instrumentAndSendExecutorResponse.called.should.eql(true);
      customExecutorHelper.instrumentAndSendError.called.should.eql(false);
      sinon.assert.calledWith(
        customExecutorHelper.instrumentAndSendExecutorResponse,
        EXECUTOR_TYPE,
        keyObject,
        requestStateObject,
        false
      );

      restAPIHandler.makeRequest.restore();
    });

    it('Tests successful updation of status for app automate', async () => {
      const data = '{"automation_session":{"name":"fix sanity","duration":null,"os":"Windows","os_version":"10","browser_version":"72.0","browser":"chrome","device":null,"status":"passed","hashed_id":"0c4d8424e2908a90934ab72337c375a9b212315e","reason":"something went good","build_name":"custom executor","project_name":"Untitled Project"}}';
      const expectedResponse = { statusCode: 200, data };
      const restAPIHandler = new RestAPIHandler('randomSessionID', true);
      const keyObject = {};
      const requestStateObject = {
        clientSessionID: 12345,
      };
      const parsedCommand = { action: EXECUTOR_TYPE };
      sinon.stub(restAPIHandler, 'makeRequest').returns(expectedResponse);

      await restAPIHandler.runUpdateSessionStateExecutor(
        keyObject,
        requestStateObject,
        parsedCommand
      );

      restAPIHandler.makeRequest.called.should.eql(true);
      customExecutorHelper.instrumentAndSendExecutorResponse.called.should.eql(true);
      customExecutorHelper.instrumentAndSendError.called.should.eql(false);
      sinon.assert.calledWith(
        customExecutorHelper.instrumentAndSendExecutorResponse,
        EXECUTOR_TYPE,
        keyObject,
        requestStateObject,
        false
      );

      restAPIHandler.makeRequest.restore();
    });

    it('Tests unsuccessful updation of status with JSON response for automate', async () => {
      const data = '{"reason": "unprocessible entity"}';
      const expectedResponse = { statusCode: 422, data };
      const restAPIHandler = new RestAPIHandler('randomSessionID', false);
      const keyObject = {};
      const requestStateObject = {
        clientSessionID: 12345,
      };
      const parsedCommand = { action: EXECUTOR_TYPE };

      sinon.stub(restAPIHandler, 'makeRequest').returns(expectedResponse);

      await restAPIHandler.runUpdateSessionStateExecutor(
        keyObject,
        requestStateObject,
        parsedCommand
      );

      restAPIHandler.makeRequest.called.should.eql(true);
      customExecutorHelper.instrumentAndSendExecutorResponse.called.should.eql(true);
      customExecutorHelper.instrumentAndSendError.called.should.eql(false);
      sinon.assert.calledWith(
        customExecutorHelper.instrumentAndSendExecutorResponse,
        EXECUTOR_TYPE,
        keyObject,
        requestStateObject,
        false
      );

      restAPIHandler.makeRequest.restore();
    });

    it('Tests unsuccessful updation of status with JSON response for app automate', async () => {
      const data = '{"reason": "unprocessible entity"}';
      const expectedResponse = { statusCode: 422, data };
      const restAPIHandler = new RestAPIHandler('randomSessionID', false);
      const keyObject = {};
      const requestStateObject = {
        clientSessionID: 12345,
      };
      const parsedCommand = { action: EXECUTOR_TYPE };

      sinon.stub(restAPIHandler, 'makeRequest').returns(expectedResponse);

      await restAPIHandler.runUpdateSessionStateExecutor(
        keyObject,
        requestStateObject,
        parsedCommand
      );

      restAPIHandler.makeRequest.called.should.eql(true);
      customExecutorHelper.instrumentAndSendExecutorResponse.called.should.eql(true);
      customExecutorHelper.instrumentAndSendError.called.should.eql(false);
      sinon.assert.calledWith(
        customExecutorHelper.instrumentAndSendExecutorResponse,
        EXECUTOR_TYPE,
        keyObject,
        requestStateObject,
        false
      );

      restAPIHandler.makeRequest.restore();
    });

    it('Tests unsuccessful updation of status with HTML response for automate', async () => {
      const data = '<html></html>';
      const expectedResponse = { statusCode: 400, data };
      const restAPIHandler = new RestAPIHandler('randomSessionID', false);
      const keyObject = {};
      const requestStateObject = {
        clientSessionID: 12345,
      };
      const parsedCommand = { action: EXECUTOR_TYPE };

      sinon.stub(restAPIHandler, 'makeRequest').returns(expectedResponse);

      await restAPIHandler.runUpdateSessionStateExecutor(
        keyObject,
        requestStateObject,
        parsedCommand
      );

      restAPIHandler.makeRequest.called.should.eql(true);
      customExecutorHelper.instrumentAndSendExecutorResponse.called.should.eql(false);
      customExecutorHelper.instrumentAndSendError.called.should.eql(true);
      sinon.assert.calledWith(
        customExecutorHelper.instrumentAndSendError,
        EXECUTOR_TYPE,
        keyObject,
        requestStateObject,
        constants.JSE_SESSION_STATUS.generic_error
      );
      restAPIHandler.makeRequest.restore();
    });

    it('Tests unsuccessful updation of status with HTML response for app automate', async () => {
      const data = '<html></html>';
      const expectedResponse = { statusCode: 400, data };
      const restAPIHandler = new RestAPIHandler('randomSessionID', true);
      const keyObject = {};
      const requestStateObject = {
        clientSessionID: 12345,
      };
      const parsedCommand = { action: EXECUTOR_TYPE };

      sinon.stub(restAPIHandler, 'makeRequest').returns(expectedResponse);

      await restAPIHandler.runUpdateSessionStateExecutor(
        keyObject,
        requestStateObject,
        parsedCommand
      );

      restAPIHandler.makeRequest.called.should.eql(true);
      customExecutorHelper.instrumentAndSendExecutorResponse.called.should.eql(false);
      customExecutorHelper.instrumentAndSendError.called.should.eql(true);
      sinon.assert.calledWith(
        customExecutorHelper.instrumentAndSendError,
        EXECUTOR_TYPE,
        keyObject,
        requestStateObject,
        constants.JSE_SESSION_STATUS.generic_error
      );

      restAPIHandler.makeRequest.restore();
    });

    // TODO: should be easily mergeable in above unit tests after the `parsedCommands`
    // is updated of the same.
    it('Also calls QIG method to mark the use of session-status-JS-executor in case of successful response', async () => {
      const data = '{"automation_session":{"name":"fix sanity","duration":null,"os":"Windows","os_version":"10","browser_version":"72.0","browser":"chrome","device":null,"status":"passed","hashed_id":"0c4d8424e2908a90934ab72337c375a9b212315e","reason":"something went good","build_name":"custom executor","project_name":"Untitled Project"}}';
      const expectedResponse = { statusCode: 200, data };
      const restAPIHandler = new RestAPIHandler('randomSessionID', false);
      const keyObject = {};
      const requestStateObject = {
        clientSessionID: 12345,
      };
      const parsedCommand = {
        action: EXECUTOR_TYPE,
        arguments: {
          status: 'passed',
          reason: 'abcd',
        },
      };
      sinon.stub(restAPIHandler, 'makeRequest').returns(expectedResponse);

      await restAPIHandler.runUpdateSessionStateExecutor(
        keyObject,
        requestStateObject,
        parsedCommand
      );

      restAPIHandler.makeRequest.called.should.eql(true);
      Qig.markSessionStatusExecutor.called.should.eql(true);
      customExecutorHelper.instrumentAndSendExecutorResponse.called.should.eql(true);
      customExecutorHelper.instrumentAndSendError.called.should.eql(false);
      sinon.assert.calledWith(
        customExecutorHelper.instrumentAndSendExecutorResponse,
        EXECUTOR_TYPE,
        keyObject,
        requestStateObject,
        false
      );

      restAPIHandler.makeRequest.restore();
    });

    it('Doesn\'t call the QIG method to mark the use of session-status-JS-executor if non-2xx response is received', async () => {
      const data = '{"reason": "unprocessible entity"}';
      const expectedResponse = { statusCode: 422, data };
      const restAPIHandler = new RestAPIHandler('randomSessionID', false);
      const keyObject = {};
      const requestStateObject = {
        clientSessionID: 12345,
      };
      const parsedCommand = {
        action: EXECUTOR_TYPE,
        arguments: {
          status: 'passed',
          reason: 'abcd',
        },
      };

      sinon.stub(restAPIHandler, 'makeRequest').returns(expectedResponse);

      await restAPIHandler.runUpdateSessionStateExecutor(
        keyObject,
        requestStateObject,
        parsedCommand
      );

      restAPIHandler.makeRequest.called.should.eql(true);
      Qig.markSessionStatusExecutor.called.should.eql(false);
      customExecutorHelper.instrumentAndSendExecutorResponse.called.should.eql(true);
      customExecutorHelper.instrumentAndSendError.called.should.eql(false);
      sinon.assert.calledWith(
        customExecutorHelper.instrumentAndSendExecutorResponse,
        EXECUTOR_TYPE,
        keyObject,
        requestStateObject,
        false
      );

      restAPIHandler.makeRequest.restore();
    });

    it('Doesn\'t call the QIG method to mark the use of session-status-JS-executor if the status is not updated', async () => {
      const data = '{"automation_session":{"name":"fix sanity","duration":null,"os":"Windows","os_version":"10","browser_version":"72.0","browser":"chrome","device":null,"status":"passed","hashed_id":"0c4d8424e2908a90934ab72337c375a9b212315e","reason":"something went good","build_name":"custom executor","project_name":"Untitled Project"}}';
      const expectedResponse = { statusCode: 200, data };
      const restAPIHandler = new RestAPIHandler('randomSessionID', false);
      const keyObject = {};
      const requestStateObject = {
        clientSessionID: 12345,
      };
      const parsedCommand = {
        action: EXECUTOR_TYPE,
        arguments: {
          name: 'fix sanity',
        },
      };
      sinon.stub(restAPIHandler, 'makeRequest').returns(expectedResponse);

      await restAPIHandler.runUpdateSessionStateExecutor(
        keyObject,
        requestStateObject,
        parsedCommand
      );

      restAPIHandler.makeRequest.called.should.eql(true);
      Qig.markSessionStatusExecutor.called.should.eql(false);
      customExecutorHelper.instrumentAndSendExecutorResponse.called.should.eql(true);
      customExecutorHelper.instrumentAndSendError.called.should.eql(false);
      sinon.assert.calledWith(
        customExecutorHelper.instrumentAndSendExecutorResponse,
        EXECUTOR_TYPE,
        keyObject,
        requestStateObject,
        false
      );

      restAPIHandler.makeRequest.restore();
    });
  });
});
