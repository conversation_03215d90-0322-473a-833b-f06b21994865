/* eslint-disable no-unused-expressions,no-underscore-dangle */

'use strict';

const sinon = require('sinon');
const helper = require('../../helper');
const bridge = require('../../bridge');
const constants = require('../../constants');
const rewire = require('rewire');
const { expect } = require('chai');
const HubLogger = require('../../log');
const requestLib = require('../../lib/request');

const rewiredCustomExecutorHelper = rewire('../../helpers/customSeleniumHandling/customExecutorHelper');

describe('customExecutorHelper', () => {
  const orignalSendError = rewiredCustomExecutorHelper.__get__('sendError');
  const orignalSendExecutorResponse = rewiredCustomExecutorHelper.__get__('sendExecutorResponse');
  const originalSetInstrumentationData = rewiredCustomExecutorHelper.__get__('setInstrumentationData');

  context('sending error to client', () => {
    beforeEach(() => {
      sinon.stub(helper, 'sendToEDS');
      sinon.stub(bridge, 'sendResponse');
    });

    afterEach(() => {
      helper.sendToEDS.restore();
      bridge.sendResponse.restore();
    });

    it('send error to client for automate', () => {
      const errorReason = 'Testing sendError';
      const keyObject = {
        appTesting: false,
      };
      const requestStateObj = {
        clientSessionID: 12345,
      };
      orignalSendError(keyObject, requestStateObj, errorReason);
      helper.sendToEDS.called.should.eql(true);
      bridge.sendResponse.called.should.eql(true);
    });

    it('send error to client for app automate', () => {
      const errorReason = 'Testing sendError';
      const keyObject = {
        appTesting: true,
      };
      const requestStateObj = {
        clientSessionID: 12345,
      };
      orignalSendError(keyObject, requestStateObj, errorReason);
      helper.sendToEDS.called.should.eql(true);
      bridge.sendResponse.called.should.eql(true);
    });
  });

  context('instrumentAndSendError', () => {
    it('should senderror and set instrumentation', () => {
      const errorObj = { message: 'error_message', code: 'error_code' };
      const keyObject = {
        appTesting: true,
      };
      const requestStateObj = {
        clientSessionID: 12345,
      };

      const sendErrorStub = sinon.stub();
      const setInstrumentationDataStub = sinon.stub();
      rewiredCustomExecutorHelper.__set__('sendError', sendErrorStub);
      rewiredCustomExecutorHelper.__set__('setInstrumentationData', setInstrumentationDataStub);

      rewiredCustomExecutorHelper.instrumentAndSendError('random', keyObject, requestStateObj, errorObj);
      expect(sendErrorStub.calledOnce).to.be.true;
      expect(setInstrumentationDataStub.calledOnce).to.be.true;
      rewiredCustomExecutorHelper.__set__('sendError', orignalSendError);
      rewiredCustomExecutorHelper.__set__('setInstrumentationData', originalSetInstrumentationData);
    });
  });

  context('instrumentAndSendExecutorResponse', () => {
    it('should send response and set instrumentation', () => {
      const keyObject = {
        appTesting: true,
      };
      const requestStateObj = {
        clientSessionID: 12345,
      };

      const sendExecutorResponseStub = sinon.stub();
      const setInstrumentationDataStub = sinon.stub();
      rewiredCustomExecutorHelper.__set__('sendExecutorResponse', sendExecutorResponseStub);
      rewiredCustomExecutorHelper.__set__('setInstrumentationData', setInstrumentationDataStub);

      rewiredCustomExecutorHelper.instrumentAndSendExecutorResponse('random', keyObject, requestStateObj);
      expect(sendExecutorResponseStub.calledOnce).to.be.true;
      expect(setInstrumentationDataStub.calledOnce).to.be.true;
      rewiredCustomExecutorHelper.__set__('sendExecutorResponse', orignalSendError);
      rewiredCustomExecutorHelper.__set__('setInstrumentationData', originalSetInstrumentationData);
    });
  });

  context('instrumentExecutorStatusAndSendResponse', () => {
    it('should set instrumentation data and send response on success executor status', () => {
      const keyObject = {
        appTesting: true,
      };
      const requestStateObj = {
        clientSessionID: 12345,
      };

      const sendExecutorResponseStub = sinon.stub();
      const setInstrumentationDataStub = sinon.stub();
      rewiredCustomExecutorHelper.__set__('sendExecutorResponse', sendExecutorResponseStub);
      rewiredCustomExecutorHelper.__set__('setInstrumentationData', setInstrumentationDataStub);

      rewiredCustomExecutorHelper.instrumentExecutorStatusAndSendResponse('success', '', 'random', keyObject, requestStateObj);
      expect(sendExecutorResponseStub.calledOnce).to.be.true;
      expect(setInstrumentationDataStub.calledOnce).to.be.true;
      rewiredCustomExecutorHelper.__set__('sendExecutorResponse', orignalSendError);
      rewiredCustomExecutorHelper.__set__('setInstrumentationData', originalSetInstrumentationData);
    });

    it('should set instrumentation data with error string and send response on error executor status', () => {
      const keyObject = {
        appTesting: true,
      };
      const requestStateObj = {
        clientSessionID: 12345,
      };
      const errorString = 'Some error occurred';

      const sendExecutorResponseStub = sinon.stub();
      const setInstrumentationDataStub = sinon.stub();
      rewiredCustomExecutorHelper.__set__('sendExecutorResponse', sendExecutorResponseStub);
      rewiredCustomExecutorHelper.__set__('setInstrumentationData', setInstrumentationDataStub);

      rewiredCustomExecutorHelper.instrumentExecutorStatusAndSendResponse('error', errorString, 'random', keyObject, requestStateObj);
      expect(sendExecutorResponseStub.calledOnce).to.be.true;
      expect(setInstrumentationDataStub.calledOnce).to.be.true;
      rewiredCustomExecutorHelper.__set__('sendExecutorResponse', orignalSendError);
      rewiredCustomExecutorHelper.__set__('setInstrumentationData', originalSetInstrumentationData);
    });

    it('should not set instrumentation data and send response on no executor status', () => {
      const keyObject = {
        appTesting: true,
      };
      const requestStateObj = {
        clientSessionID: 12345,
      };

      const sendExecutorResponseStub = sinon.stub();
      const setInstrumentationDataStub = sinon.stub();
      rewiredCustomExecutorHelper.__set__('sendExecutorResponse', sendExecutorResponseStub);
      rewiredCustomExecutorHelper.__set__('setInstrumentationData', setInstrumentationDataStub);

      rewiredCustomExecutorHelper.instrumentExecutorStatusAndSendResponse('', '', 'random', keyObject, requestStateObj);
      expect(sendExecutorResponseStub.calledOnce).to.be.true;
      expect(setInstrumentationDataStub.calledOnce).to.be.false;
      rewiredCustomExecutorHelper.__set__('sendExecutorResponse', orignalSendError);
      rewiredCustomExecutorHelper.__set__('setInstrumentationData', originalSetInstrumentationData);
    });
  });

  context('checkFileUploadExecutor', () => {
    it('should return true for file upload executor1', () => {
      const result = rewiredCustomExecutorHelper.checkFileUploadExecutor({ os: 'winxp' });
      expect(result).to.eq(constants.JSE_FILE_EXECUTORS_GENERIC.supports_only_windows);
    });

    it('should return true for file upload executor2', () => {
      const result = rewiredCustomExecutorHelper.checkFileUploadExecutor({ os: 'linux' });
      expect(result).to.eq(constants.JSE_FILE_EXECUTORS_GENERIC.supports_only_windows);
    });

    it('should return true for file upload executor3', () => {
      const result = rewiredCustomExecutorHelper.checkFileUploadExecutor({ os: 'win', browser: 'samsung' });
      expect(result).to.eq(constants.JSE_FILE_EXECUTORS_GENERIC.supports_only_chromium);
    });

    it('should return true for file upload executor4', () => {
      const result = rewiredCustomExecutorHelper.checkFileUploadExecutor({ os: 'win', browser: 'chrome' });
      expect(result).to.eq(undefined);
    });
  });

  context('sendRequestToPlatform', () => {
    let keyObject;
    let requestStateObj;
    let executorType;
    let serverUrl;
    let errorMessage;
    let customTimeout;

    beforeEach(() => {
      executorType = 'cameraImageInjection_custom_executor';
      serverUrl = `/inject_image?device_id=${encodeURIComponent('device')}&session_id=${encodeURIComponent('session_id')}&s3_media_url=https://image.com&media_id='media_id&format='jpg`;
      errorMessage = constants.IMAGE_INJECTION_CUSTOM_EXECUTOR_ERRORS.unknown;
      customTimeout = 9000;

      keyObject = {
        name: 'test.com',
        port: 9999,
        key: '1234',
        selenium_version: '3.141.59',
        rproxyHost: 'localterminal.com',
        dialect: 'W3C',
        idle_timeout: '90',
      };

      requestStateObj = {
        clientSessionID: '12345',
        hostname: 'local.com',
        rproxyHost: 'localterminal.com',
      };

      sinon.stub(requestLib, 'call', () => { });
      sinon.stub(HubLogger, 'miscLogger', () => { });
      sinon.stub(HubLogger, 'tempExceptionLogger', () => { });
      sinon.stub(requestLib, 'appendBStackHostHeader', () => JSON.parse('{"BStack-Host": "localterminal.com"}'));
    });

    afterEach(() => {
      requestLib.call.restore();
      HubLogger.miscLogger.restore();
      HubLogger.tempExceptionLogger.restore();
      requestLib.appendBStackHostHeader.restore();
    });

    it('platform responds with 200', async () => {
      requestLib.call.restore();

      const response = {
        statusCode: 200,
        data: { message: 'Success' },
      };

      const sendExecutorResponseStub = sinon.stub();
      const setInstrumentationDataStub = sinon.stub();
      rewiredCustomExecutorHelper.__set__('sendExecutorResponse', sendExecutorResponseStub);
      rewiredCustomExecutorHelper.__set__('setInstrumentationData', setInstrumentationDataStub);

      sinon.stub(requestLib, 'call', () => response);

      await rewiredCustomExecutorHelper.sendRequestToPlatform(
        executorType,
        serverUrl,
        requestStateObj,
        keyObject,
        errorMessage,
        customTimeout
      );

      expect(sendExecutorResponseStub.calledOnce).to.be.true;
      expect(setInstrumentationDataStub.calledOnce).to.be.true;
      rewiredCustomExecutorHelper.__set__('sendExecutorResponse', orignalSendExecutorResponse);
      rewiredCustomExecutorHelper.__set__('setInstrumentationData', originalSetInstrumentationData);
    });

    it('platform responds with 200 with responseHandler and callback', async () => {
      requestLib.call.restore();

      const customResponse = {
        statusCode: 200,
        data: { message: 'Success' },
      };

      let checkResponseHandler = false;
      let checkCallback = false;
      const responseHandler = (response) => {
        if (response.data.message === 'Success') checkResponseHandler = true;
      };
      const callback = () => {
        checkCallback = true;
      };

      sinon.stub(requestLib, 'call', () => customResponse);

      await rewiredCustomExecutorHelper.sendRequestToPlatform(
        executorType,
        serverUrl,
        requestStateObj,
        keyObject,
        errorMessage,
        customTimeout,
        {},
        responseHandler,
        callback
      );

      expect(checkResponseHandler).to.be.true;
      expect(checkCallback).to.be.true;
    });

    it('platform responds with non 200', async () => {
      requestLib.call.restore();

      const response = {
        statusCode: 500,
        data: { error: 'Something went wrong' },
      };

      const sendErrorStub = sinon.stub();
      const setInstrumentationDataStub = sinon.stub();
      rewiredCustomExecutorHelper.__set__('sendError', sendErrorStub);
      rewiredCustomExecutorHelper.__set__('setInstrumentationData', setInstrumentationDataStub);

      sinon.stub(requestLib, 'call', () => response);

      await rewiredCustomExecutorHelper.sendRequestToPlatform(
        executorType,
        serverUrl,
        requestStateObj,
        keyObject,
        errorMessage,
        customTimeout
      );

      expect(sendErrorStub.calledOnce).to.be.true;
      expect(setInstrumentationDataStub.calledOnce).to.be.true;
      rewiredCustomExecutorHelper.__set__('sendError', orignalSendError);
      rewiredCustomExecutorHelper.__set__('setInstrumentationData', originalSetInstrumentationData);
    });

    it('error while requesting to platform', async () => {
      requestLib.call.restore();

      const sendErrorStub = sinon.stub();
      const setInstrumentationDataStub = sinon.stub();
      rewiredCustomExecutorHelper.__set__('sendError', sendErrorStub);
      rewiredCustomExecutorHelper.__set__('setInstrumentationData', setInstrumentationDataStub);

      sinon.stub(requestLib, 'call', () => {
        throw new Error('Unable to request to terminal');
      });

      await rewiredCustomExecutorHelper.sendRequestToPlatform(
        executorType,
        serverUrl,
        requestStateObj,
        keyObject,
        errorMessage,
        customTimeout
      );

      expect(sendErrorStub.calledOnce).to.be.true;
      expect(setInstrumentationDataStub.calledOnce).to.be.true;
      rewiredCustomExecutorHelper.__set__('sendError', orignalSendError);
      rewiredCustomExecutorHelper.__set__('setInstrumentationData', originalSetInstrumentationData);
    });
  });
});
