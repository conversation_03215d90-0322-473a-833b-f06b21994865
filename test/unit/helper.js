'use strict';

const assert = require("assert");
const helper = require("../../helper");
const sinon = require('sinon');
const {
  beforeEach, afterEach, describe, it
} = require('mocha');
const requestlib = require('../../lib/request');
const { expect } = require("chai");
const { isUndefined, isNotUndefined } = require("../../typeSanity");
const constants = require("../../constants");
const errorMessages = require('../../errorMessages');
const HubLogger = require('../../log');
const pubSub = require('../../pubSub');
const { validateValuesOfObject } = require('../../helpers/objectOperations');
const SeleniumClient = require("../../seleniumClient");
const rewire = require("rewire");
const { REMOTE_DEBUGGER_PORT } = require("../../config/socketConstants");

var autPipelineReqMeta = {};
var user = {};
var appAutPipelineReqMeta = {};

const QUEUED_REQUEST_EXPONENTIAL_DELAY = constants.QUEUED_REQUEST_EXPONENTIAL_DELAY;

process.env["HUB_ENV"] = "testing";

exports.getClusterWorker = function(state) {
  return {
    isDead: function(){},
    state: state,
    disconnect: function(){},
    process: {
      pid: Math.floor(Math.random() * 100)
    },
    kill: function(){},
    send: function(){}
  };
}

exports.getKeyObject = (opts = {}) => ({
  delayRegSession: false,
  rails_session_id: 'abcd-efgh-ijkl-mnop-qrst-uvwx-yz',
  name: 'hostname',
  rproxyHost: 'rproxyHost',
  key: 'zy-xwvu-tsrq-ponm-lkji-hgfe-dcba',
  browser: 'internet explorer',
  browser_version: '10',
  debug_url: '{"key":"value"}',
  ieSpecialKeyPress: [],
  sessionId: 'anksnldjweoifnwoieoifnwihf',
  appTesting: false,
  ...opts,
});

exports.getAppAutomateKeyObject = function() {
  return {
    delayRegSession: false,
    rails_session_id: 'abcd-efgh-ijkl-mnop-qrst-uvwx-yz',
    name: 'hostname',
    rproxyHost: 'rproxyHost',
    key: 'zy-xwvu-tsrq-ponm-lkji-hgfe-dcba',
    browser: 'internet explorer',
    browser_version: '10',
    debug_url: '{"key":"value"}',
    ieSpecialKeyPress: [],
    appTesting: true
  };
};

exports.getRequestsObject = function() {
  return {
    method: 'POST',
    url: '/session/abcd-efgh-ijkl-mnop-qrst-uvwx-yz/active',
    checkedActive: false,
    log_data: 'log_data',
    headers: {'x-forwarded-for': '127.0.0.1'}
  };
};

exports.getParamsObject = function() {
  var keyObject = this.getKeyObject();

  return {
    request: this.getRequestsObject(),
    response: {
      writeHead: function() {},
      write: function() {},
      end: function() {}
    },
    data: '{"key":"value","status":1}',
    index_counter: 1,
    callbacks: 2,
    hostname: '3',
    originalUrl: '4',
    browserstackTunnel: '5',
    rproxyHost: '6',
    remoteSessionID: keyObject.key,
    clientSessionID: keyObject.rails_session_id,
    non_pipe_url: 'non pipe url',
    requestData: '{"url":"value","sessionId":"'+keyObject.rails_session_id+'","value":"url","ms":5000}'
  };
};

exports.getEdgeFileObject = function() {
  var keyObject = this.getKeyObject();

  return {
    request: this.getRequestsObject(),
    response: {
      writeHead: function() {},
      write: function() {},
      end: function() {}
    },
    keyObject,
    data: '{"key":"value","status":1}',
    index_counter: 1,
    callbacks: 2,
    hostname: '3',
    originalUrl: '4',
    browserstackTunnel: '5',
    rproxyHost: '6',
    remoteSessionID: keyObject.key,
    clientSessionID: keyObject.rails_session_id,
    non_pipe_url: 'non pipe url',
    requestData: `{"text":"hello","value":["h", "e", "l", "l", "o"]}`
  }
}

describe("hostResolverRules scenarios", () => {
  context("hostResolverHonored helper method checks", () => {
    it("should return true if the session is android, non local and `--host-resolver-rules` arg is passed", () => {
      const platform = "ANDROID";
      const chromeOptions = {
        "args": ["--host-resolver-rules=MAP xyz.com *************"]
      }
      const browserstackTunnel = undefined;
      assert.equal(helper.hostResolverHonored(platform, chromeOptions, browserstackTunnel), true);
    });

    it("should return false if the session is not of android", () => {
      const platform = "WINDOWS";
      const chromeOptions = {
        "args": ["--host-resolver-rules=MAP xyz.com *************"]
      }
      const browserstackTunnel = undefined;
      assert.equal(helper.hostResolverHonored(platform, chromeOptions, browserstackTunnel), false);
    });

    it("should return false if local is on", () => {
      const platform = "ANDROID";
      const chromeOptions = {
        "args": ["--host-resolver-rules=MAP xyz.com *************"]
      }
      const browserstackTunnel = true;
      assert.equal(helper.hostResolverHonored(platform, chromeOptions, browserstackTunnel), false);
    });

    it("should return false if the arg is not passed", () => {
      const platform = "ANDROID";
      const chromeOptions = {
        "args": ["ignore-certificate-errors"]
      }
      const browserstackTunnel = undefined;
      assert.equal(helper.hostResolverHonored(platform, chromeOptions, browserstackTunnel), false);
    });

    it("should return false if `args` is not an Array", () => {
      const platform = "ANDROID";
      const chromeOptions = {
        "args": "--host-resolver-rules=MAP xyz.com *************"
      }
      const browserstackTunnel = undefined;
      assert.equal(helper.hostResolverHonored(platform, chromeOptions, browserstackTunnel), false);
    });

    context("should return false if `args` array contains only non-string value(s)", () => {
      const argValues = [1234, null, undefined, 0x456, 0, -1];
      argValues.forEach((arg) => {
        it(`for value: ${arg}`, () => {
          const platform = "ANDROID";
          const chromeOptions = {
            "args": [arg]
          }
          const browserstackTunnel = undefined;
          assert.equal(helper.hostResolverHonored(platform, chromeOptions, browserstackTunnel), false);
        });
      });
    });
  });
});

describe('eliminateRproxyForWebsocketFrameworks', () => {
  let originalConstants;
  beforeEach(() => {
    // Save original constants to restore after tests
    originalConstants = {
      eliminateRproxyPrivateHubSubRegions: constants.eliminateRproxyPrivateHubSubRegions,
      PRIVATE_HUB_REGIONS: constants.PRIVATE_HUB_REGIONS,
      PRIVATE_HUB_REGIONS_TERMINAL_MAPPING: constants.PRIVATE_HUB_REGIONS_TERMINAL_MAPPING,
      region: constants.region
    };

    // Default test values
    constants.eliminateRproxyPrivateHubSubRegions = true;
    constants.PRIVATE_HUB_REGIONS = new Set(['test-region']);
    constants.PRIVATE_HUB_REGIONS_TERMINAL_MAPPING = { 'test-region': 'test-sub-region' };
    constants.region = 'test-region';
  });

  afterEach(() => {
    // Restore original constants
    constants.eliminateRproxyPrivateHubSubRegions = originalConstants.eliminateRproxyPrivateHubSubRegions;
    constants.PRIVATE_HUB_REGIONS = originalConstants.PRIVATE_HUB_REGIONS;
    constants.PRIVATE_HUB_REGIONS_TERMINAL_MAPPING = originalConstants.PRIVATE_HUB_REGIONS_TERMINAL_MAPPING;
    constants.region = originalConstants.region;
  });

  it('should modify the WebSocket URL when all conditions are met', () => {
    const wsURL = 'wss://rproxy.example.com/socket';
    const terminalIP = '************';
    const keyObject = { terminalSubRegion: 'test-sub-region' };

    const result = helper.eliminateRproxyForWebsocketFrameworks(wsURL, terminalIP, keyObject);

    // URL should be changed, with hostname set to terminalIP and port set to REMOTE_DEBUGGER_PORT
    expect(result).to.include(terminalIP);
    expect(result).to.include(`:${REMOTE_DEBUGGER_PORT}`);
    expect(result).to.not.include('rproxy.example.com');
  });

  it('should not modify the WebSocket URL when feature flag is disabled', () => {
    // Feature flag disabled
    constants.eliminateRproxyPrivateHubSubRegions = false;

    const wsURL = 'wss://rproxy.example.com/socket';
    const terminalIP = '************';
    const keyObject = { terminalSubRegion: 'test-sub-region' };

    const result = helper.eliminateRproxyForWebsocketFrameworks(wsURL, terminalIP, keyObject);

    // URL should remain unchanged
    expect(result).to.equal(wsURL);
  });

  it('should not modify the WebSocket URL when region is not in PRIVATE_HUB_REGIONS', () => {
    constants.region = 'different-region';

    const wsURL = 'wss://rproxy.example.com/socket';
    const terminalIP = '************';
    const keyObject = { terminalSubRegion: 'test-sub-region' };

    const result = helper.eliminateRproxyForWebsocketFrameworks(wsURL, terminalIP, keyObject);

    // URL should remain unchanged
    expect(result).to.equal(wsURL);
  });

  it('should not modify the WebSocket URL when terminal sub-region does not match', () => {
    const wsURL = 'wss://rproxy.example.com/socket';
    const terminalIP = '************';
    const keyObject = { terminalSubRegion: 'different-sub-region' };

    const result = helper.eliminateRproxyForWebsocketFrameworks(wsURL, terminalIP, keyObject);

    // URL should remain unchanged
    expect(result).to.equal(wsURL);
  });

  it('should properly format the new URL with correct protocol, hostname and port', () => {
    const wsURL = 'wss://rproxy.example.com:8080/socket/path?query=param';
    const terminalIP = '************';
    const keyObject = { terminalSubRegion: 'test-sub-region' };

    const result = helper.eliminateRproxyForWebsocketFrameworks(wsURL, terminalIP, keyObject);

    // URL should be changed but preserve protocol and path
    expect(result).to.include('wss://');
    expect(result).to.include(terminalIP);
    expect(result).to.include(`:${REMOTE_DEBUGGER_PORT}`);
    expect(result).to.include('/socket/path?query=param');
  });
});

describe('helper.getWebContextName', () => {
  let getWebContextName;
  let seleniumClientStub;
  let getContextsStub;
  const helperRewire = rewire('../../helper');

  before(() => {
    getWebContextName = helperRewire.__get__('getWebContextName');
  });

  beforeEach(() => {
    seleniumClientStub = sinon.stub(SeleniumClient.prototype, 'constructor').returnsThis();
    getContextsStub = sinon.stub(SeleniumClient.prototype, 'getContexts');
  });

  afterEach(() => {
    seleniumClientStub.restore();
    getContextsStub.restore();
  });

  it('should return a webview context if one exists', async () => {
    getContextsStub.returns({ value: ['NATIVE_APP', 'WEBVIEW_1'] });
    const result = await getWebContextName({});
    expect(result).to.equal('WEBVIEW_1');
  });

  it('should throw an error if no webview context exists', async () => {
    getContextsStub.returns({ value: ['NATIVE_APP'] });
    try {
      await getWebContextName({});
    } catch (err) {
      expect(err).to.be.an('error');
      expect(err.message).to.equal('Did not find webview');
    }
  });
});

describe('helper.addToGetOptionsforAdditionalJsonCapsOnPWIOS', () => {
  // Test for orientation handling - landscape
  it('should add landscape orientation to capabilities', () => {
    const options = {
      orientation: 'landscape'
    };
    helper.addToGetOptionsforAdditionalJsonCapsOnPWIOS(options);
    expect(options).to.have.property('ADDITIONAL_JSON_CAP_PW_ON_IOS');
    const parsedCaps = JSON.parse(options.ADDITIONAL_JSON_CAP_PW_ON_IOS);
    expect(parsedCaps).to.have.property('orientation', 'LANDSCAPE');
  });

  // Test for orientation not being landscape
  it('should not add orientation if not landscape', () => {
    const options = {
      orientation: 'portrait'
    };
    helper.addToGetOptionsforAdditionalJsonCapsOnPWIOS(options);
    expect(options).to.not.have.property('ADDITIONAL_JSON_CAP_PW_ON_IOS');
  });

  // Test for autoAcceptAlerts handling
  it('should add autoAcceptAlerts and safariAllowPopups when autoAcceptAlerts is true', () => {
    const options = {
      autoAcceptAlerts: true
    };
    helper.addToGetOptionsforAdditionalJsonCapsOnPWIOS(options);
    expect(options).to.have.property('ADDITIONAL_JSON_CAP_PW_ON_IOS');
    const parsedCaps = JSON.parse(options.ADDITIONAL_JSON_CAP_PW_ON_IOS);
    expect(parsedCaps).to.have.property('autoAcceptAlerts', true);
    expect(parsedCaps).to.have.property('safariAllowPopups', true);
  });

  // Test for autoAcceptAlerts being false
  it('should not add alert capabilities when autoAcceptAlerts is false', () => {
    const options = {
      autoAcceptAlerts: false
    };
    helper.addToGetOptionsforAdditionalJsonCapsOnPWIOS(options);
    expect(options).to.not.have.property('ADDITIONAL_JSON_CAP_PW_ON_IOS');
  });

  // Test for combining multiple capabilities
  it('should add multiple capabilities when multiple options are set', () => {
    const options = {
      orientation: 'landscape',
      autoAcceptAlerts: true
    };
    helper.addToGetOptionsforAdditionalJsonCapsOnPWIOS(options);
    expect(options).to.have.property('ADDITIONAL_JSON_CAP_PW_ON_IOS');
    const parsedCaps = JSON.parse(options.ADDITIONAL_JSON_CAP_PW_ON_IOS);
    expect(parsedCaps).to.have.property('orientation', 'LANDSCAPE');
    expect(parsedCaps).to.have.property('autoAcceptAlerts', true);
    expect(parsedCaps).to.have.property('safariAllowPopups', true);
  });

  // Test for existing capabilities
  it('should preserve existing capabilities in ADDITIONAL_JSON_CAP_PW_ON_IOS', () => {
    const options = {
      orientation: 'landscape',
      ADDITIONAL_JSON_CAP_PW_ON_IOS: JSON.stringify({
        'appium:deviceName': 'iPhone X'
      })
    };
    helper.addToGetOptionsforAdditionalJsonCapsOnPWIOS(options);
    expect(options).to.have.property('ADDITIONAL_JSON_CAP_PW_ON_IOS');
    const parsedCaps = JSON.parse(options.ADDITIONAL_JSON_CAP_PW_ON_IOS);
    expect(parsedCaps).to.have.property('orientation', 'LANDSCAPE');
    expect(parsedCaps).to.have.property('appium:deviceName', 'iPhone X');
  });

  // Test for handling invalid JSON in existing capabilities
  it('should handle invalid JSON in ADDITIONAL_JSON_CAP_PW_ON_IOS', () => {
    const options = {
      orientation: 'landscape',
      ADDITIONAL_JSON_CAP_PW_ON_IOS: '{invalid-json}'
    };
    helper.addToGetOptionsforAdditionalJsonCapsOnPWIOS(options);
    expect(options).to.have.property('ADDITIONAL_JSON_CAP_PW_ON_IOS');
    const parsedCaps = JSON.parse(options.ADDITIONAL_JSON_CAP_PW_ON_IOS);
    expect(parsedCaps).to.have.property('orientation', 'LANDSCAPE');
  });

  // Test for no options
  it('should handle empty options object', () => {
    const options = {};
    helper.addToGetOptionsforAdditionalJsonCapsOnPWIOS(options);
    expect(options).to.not.have.property('ADDITIONAL_JSON_CAP_PW_ON_IOS');
  });

  // Test for undefined properties
  it('should handle undefined properties correctly', () => {
    const options = {
      orientation: undefined,
      autoAcceptAlerts: undefined
    };
    helper.addToGetOptionsforAdditionalJsonCapsOnPWIOS(options);
    expect(options).to.not.have.property('ADDITIONAL_JSON_CAP_PW_ON_IOS');
  });

  // Test for case sensitivity in orientation
  it('should handle case insensitive orientation value', () => {
    const options = {
      orientation: 'landscape'
    };
    helper.addToGetOptionsforAdditionalJsonCapsOnPWIOS(options);
    expect(options).to.have.property('ADDITIONAL_JSON_CAP_PW_ON_IOS');
    const parsedCaps = JSON.parse(options.ADDITIONAL_JSON_CAP_PW_ON_IOS);
    expect(parsedCaps).to.have.property('orientation', 'LANDSCAPE');
  });

  // Test for overriding existing values
  it('should override existing capability values if duplicated', () => {
    const options = {
      orientation: 'landscape',
      ADDITIONAL_JSON_CAP_PW_ON_IOS: JSON.stringify({
        'appium:orientation': 'PORTRAIT'
      })
    };
    helper.addToGetOptionsforAdditionalJsonCapsOnPWIOS(options);
    expect(options).to.have.property('ADDITIONAL_JSON_CAP_PW_ON_IOS');
    const parsedCaps = JSON.parse(options.ADDITIONAL_JSON_CAP_PW_ON_IOS);
    expect(parsedCaps).to.have.property('orientation', 'LANDSCAPE');
  });

  // Mock isNotUndefined function if needed
  // This assumes isNotUndefined is a function that checks if value is not undefined
  // If that's not how it works in your codebase, adjust accordingly
  describe('with mocked isNotUndefined', () => {
    let originalIsNotUndefined;

    beforeEach(() => {
      // Save the original function to restore later
      if (typeof global.isNotUndefined === 'function') {
        originalIsNotUndefined = global.isNotUndefined;
      }

      // Mock the function
      global.isNotUndefined = (val) => val !== undefined;
    });

    afterEach(() => {
      // Restore the original function
      if (originalIsNotUndefined) {
        global.isNotUndefined = originalIsNotUndefined;
      } else {
        delete global.isNotUndefined;
      }
    });

    it('should use isNotUndefined correctly', () => {
      const options = {
        orientation: 'landscape',
        autoAcceptAlerts: true
      };
      helper.addToGetOptionsforAdditionalJsonCapsOnPWIOS(options);
      expect(options).to.have.property('ADDITIONAL_JSON_CAP_PW_ON_IOS');
      const parsedCaps = JSON.parse(options.ADDITIONAL_JSON_CAP_PW_ON_IOS);
      expect(parsedCaps).to.have.property('orientation', 'LANDSCAPE');
      expect(parsedCaps).to.have.property('autoAcceptAlerts', true);
    });
  });
});

describe('helper.setContextIOSChrome', () => {
  let PingZombieStub = null;
  let exceptionLoggerStub = null;
  let setContextStub = null;
  let getContextsStub, getWebContextName;
  const helperRewire = rewire('../../helper');

  before(() => {
    getWebContextName = helperRewire.__get__('getWebContextName');
  });

  beforeEach(() => {
    PingZombieStub = sinon.stub(helper, 'PingZombie');
    setContextStub = sinon.stub(SeleniumClient.prototype, 'setContext').returns(true);
    getContextsStub = sinon.stub(SeleniumClient.prototype, 'getContexts');
  });

  afterEach(() => {
    PingZombieStub.restore();
    getContextsStub.restore();
    setContextStub.restore();
  });

  it('should call callback if retries > 10', async () => {
    const hostParams = {};
    const request = {};
    const retries = 11;
    const callback = sinon.stub().returns(true);

    await helper.setContextIOSChrome(hostParams, request, retries, callback);
    assert.strictEqual(callback.calledOnce, true);
  });

  // SUCCESS CASE
  it('should call setContext if retries < 10', async () => {
    const hostParams = {};
    const request = {};
    const retries = 5;
    const callback = sinon.stub().returns(true);

    getContextsStub.returns({ value: ['NATIVE_APP', 'WEBVIEW_1'] });

    await helper.setContextIOSChrome(hostParams, request, retries, callback);
    assert.strictEqual(getContextsStub.calledOnce, true);
  });

  it('should retry if getWebContextName throws error', async () => {
    const hostParams = {};
    const request = {};
    let retries = 9;
    const callback = sinon.stub().returns(true);

    // getContextsStub return error
    getContextsStub.returns(Error('error'));

    await helper.setContextIOSChrome(hostParams, request, retries, callback);
    assert.strictEqual(getContextsStub.callCount, 2);
  }).timeout(60000);
});

describe('helper.setContextSamsungMobile', () => {
  let PingZombieStub = null;
  let requestsStub = null;
  let sleepStub = null;

  beforeEach(() => {
    requestsStub = sinon.stub(requestlib, 'call');
    PingZombieStub = sinon.stub(helper, 'PingZombie');
    sleepStub = sinon.stub(helper, 'sleep').returns(true);
  });

  afterEach(() => {
    requestsStub.restore();
    PingZombieStub.restore();
    sleepStub.restore();
  });

  it('should call callback if retries > 10', async () => {
    const hostParams = {};
    const request = {
      headers: {}
    };
    const retries = 11;
    const callback = sinon.stub().returns(true);

    await helper.setContextSamsungMobile(hostParams, request, retries, callback);
    assert.strictEqual(callback.calledOnce, true);
  });

  it('should call setContext if retries < 10', async () => {
    const hostParams = {};
    const request = {
      headers: {}
    };
    const retries = 9;
    const callback = sinon.stub().returns(true);

    requestsStub.returns({ statusCode: 200});

    await helper.setContextSamsungMobile(hostParams, request, retries, callback);
    assert.strictEqual(callback.calledOnce, true);
  });

  it('should retry if request throws error', async () => {
    const hostParams = {};
    const request = {
      headers: {}
    };
    const retries = 9;
    const callback = sinon.stub().returns(true);

    // request return error
    requestsStub.returns(Error('error'));

    await helper.setContextSamsungMobile(hostParams, request, retries, callback);
    assert.strictEqual(requestsStub.callCount, 2);
  }).timeout(60000);

  it('should retry if request throws non 200', async () => {
    const hostParams = {};
    const request = {
      headers: {}
    };
    const retries = 9;
    const callback = sinon.stub().returns(true);

    // request return 404
    requestsStub.returns({ statusCode: 404});

    await helper.setContextSamsungMobile(hostParams, request, retries, callback);
    assert.strictEqual(requestsStub.callCount, 2);
  }).timeout(60000);
});

describe('helper.flakySamsungTabChrome', () => {
  let PingZombieStub = null;
  let requestsStub = null;
  let sleepStub = null;

  beforeEach(() => {
    requestsStub = sinon.stub(requestlib, 'call');
    PingZombieStub = sinon.stub(helper, 'PingZombie');
    sleepStub = sinon.stub(helper, 'sleep').returns(true);
  });

  afterEach(() => {
    requestsStub.restore();
    PingZombieStub.restore();
    sleepStub.restore();
  });

  it('should call flakySamsungTabChrome', async () => {
    const hostParams = {};
    const request = {
      headers: {}
    };
    const callback = sinon.stub().returns(true);

    requestsStub.returns({ statusCode: 200});

    await helper.flakySamsungTabChrome(hostParams, request, callback);
    assert.strictEqual(callback.calledOnce, true);
  });

  it('should log error if any request fails', async () => {
    const hostParams = {};
    const request = {
      headers: {}
    };
    const callback = sinon.stub().returns(true);

    requestsStub.returns(Error('error'));

    sinon.stub(HubLogger, 'newCGLogger');

    await helper.flakySamsungTabChrome(hostParams, request, callback);
    assert(HubLogger.newCGLogger.called.should.be.true);
    HubLogger.newCGLogger.restore();
  }).timeout(60000);
});

describe('helper.sendSeleniumRequest', () => {
  let requestStub = null;

  beforeEach(() => {
    requestStub = sinon.stub(requestlib, 'call');
  });

  afterEach(() => {
    requestStub.restore();
  });

  it('should send a request with proper options and return data', async () => {
    const method = 'POST';
    const path = '/example';
    const hostParams = {
      rproxyHost: 'example.com',
      port: 443,
      name: 'testHost'
    };
    const headers = { 'Content-Type': 'application/json' };
    const body = { key: 'value' };

    requestStub.returns({ statusCode: 200, data: {}});

    const result = await helper.sendSeleniumRequest(method, path, hostParams, headers, body);

    const expectedOptions = {
      method: 'POST',
      path: '/example',
      headers: {
        ...headers,
        "content-length": JSON.stringify(JSON.stringify(body).length),
      },
      hostname: 'example.com',
      port: 443,
      timeout: constants.NODE_DIED_IN,
      body: JSON.stringify(body),
    };

    sinon.assert.calledOnce(requestStub);
    sinon.assert.calledWith(requestStub, sinon.match(expectedOptions));
    sinon.assert.calledOnce(requestStub);
  });

  it('should call updateHeadersForSelenium4Jars and appendBStackHostHeader', async () => {
    const updateHeadersStub = sinon.stub(helper, 'updateHeadersForSelenium4Jars').returns({ updated: true });
    const appendHostHeaderStub = sinon.stub(requestlib, 'appendBStackHostHeader');

    const method = 'POST';
    const path = '/example';
    const hostParams = {
      rproxyHost: 'example.com',
      port: 443,
      name: 'testHost'
    };
    const headers = { 'Content-Type': 'application/json' };
    const body = { key: 'value' };

    requestStub.returns({ statusCode: 200, data: {}});

    await helper.sendSeleniumRequest(method, path, hostParams, headers, body);

    sinon.assert.calledOnce(updateHeadersStub);
    sinon.assert.calledOnce(appendHostHeaderStub);

    updateHeadersStub.restore();
    appendHostHeaderStub.restore();
  });
});

describe('helper.installHealingExtensionFirefox', () => {
  let PingZombieStub = null;
  let requestsStub = null;
  let sleepStub = null;

  beforeEach(() => {
    requestsStub = sinon.stub(requestlib, 'call');
    PingZombieStub = sinon.stub(helper, 'PingZombie');
    sleepStub = sinon.stub(helper, 'sleep').returns(true);
  });

  afterEach(() => {
    requestsStub.restore();
    PingZombieStub.restore();
    sleepStub.restore();
  });

  it('should call callback if retries > 5', async () => {
    const hostParams = {};
    const request = {
      headers: {}
    };
    const retries = 6;
    const activateBrowser = () => {};
    const setPageLoadOnFetchDataCallbackStub = sinon.stub();

    await helper.installHealingExtensionFirefox(hostParams, request, retries, setPageLoadOnFetchDataCallbackStub, activateBrowser);
    setPageLoadOnFetchDataCallbackStub.calledWith(activateBrowser);
  });

  it('should call callback if retries < 5', async () => {
    const hostParams = {};
    const request = {
      headers: {}
    };
    const retries = 4;
    const activateBrowser = () => {};
    const setPageLoadOnFetchDataCallbackStub = sinon.stub();

    requestsStub.returns({ statusCode: 200});

    await helper.installHealingExtensionFirefox(hostParams, request, retries, setPageLoadOnFetchDataCallbackStub, activateBrowser);
    setPageLoadOnFetchDataCallbackStub.calledWith(activateBrowser);
  });

  it('should call callback if retries < 5, even when no headers', async () => {
    const hostParams = {};
    const request = {};
    const retries = 4;
    const activateBrowser = () => {};
    const setPageLoadOnFetchDataCallbackStub = sinon.stub();

    requestsStub.returns({ statusCode: 200});

    await helper.installHealingExtensionFirefox(hostParams, request, retries, setPageLoadOnFetchDataCallbackStub, activateBrowser);
    setPageLoadOnFetchDataCallbackStub.calledWith(activateBrowser);
  });

  it('should retry if request throws error', async () => {
    const hostParams = {};
    const request = {
      headers: {}
    };
    const retries = 4;
    const activateBrowser = () => {};
    const setPageLoadOnFetchDataCallbackStub = sinon.stub();

    // request return error
    requestsStub.returns(Error('error'));

    await helper.installHealingExtensionFirefox(hostParams, request, retries, setPageLoadOnFetchDataCallbackStub, activateBrowser);
    assert.strictEqual(requestsStub.callCount, 2);
  }).timeout(60000);

  it('should retry if request throws non 200', async () => {
    const hostParams = {};
    const request = {
      headers: {}
    };
    const retries = 4;
    const activateBrowser = () => {};
    const setPageLoadOnFetchDataCallbackStub = sinon.stub();

    // request return 404
    requestsStub.returns({ statusCode: 404});

    await helper.installHealingExtensionFirefox(hostParams, request, retries, setPageLoadOnFetchDataCallbackStub, activateBrowser);
    assert.strictEqual(requestsStub.callCount, 2);
  }).timeout(60000);
});


describe('shouldSendInstrumentationThroughKafka', function() {
  it('should return false if key is not in constants or value is N', function() {
    assert.equal(helper.shouldSendInstrumentationThroughKafka('nonexistent_key', 1), false);
    assert.equal(helper.shouldSendInstrumentationThroughKafka('deviceOrientation', 1), false);
  });

  it('should return true if request_count is greater than 2', function() {
    assert.equal(helper.shouldSendInstrumentationThroughKafka('request_count', 3), true);
  });

  it('should return false if request_count is less than or equal to 2', function() {
    assert.equal(helper.shouldSendInstrumentationThroughKafka('request_count', 2), false);
    assert.equal(helper.shouldSendInstrumentationThroughKafka('request_count', 1), false);
  });

  it('should return true for keys with value Y or M', function() {
    assert.equal(helper.shouldSendInstrumentationThroughKafka('exceptionClass', 1), true);
    assert.equal(helper.shouldSendInstrumentationThroughKafka('toggle', 1), true);
  });
});

describe('validateValuesOfObject', () => {
  it('should replace undefined values with the default string', () => {
    const inputObject = { a: 'valueA', b: undefined, c: 'valueC' };
    const expectedObject = { a: 'valueA', b: 'NOT AVAILABLE', c: 'valueC' };

    const result = validateValuesOfObject(inputObject);

    expect(result).to.deep.equal(expectedObject);
  });

  it('should replace null values with the default string', () => {
    const inputObject = { x: null, y: 'valueY', z: null };
    const expectedObject = { x: 'NOT AVAILABLE', y: 'valueY', z: 'NOT AVAILABLE' };

    const result = validateValuesOfObject(inputObject);

    expect(result).to.deep.equal(expectedObject);
  });

  it('should not modify values that are already defined', () => {
    const inputObject = { p: 'valueP', q: 'valueQ' };
    const expectedObject = { p: 'valueP', q: 'valueQ' };

    const result = validateValuesOfObject(inputObject);

    expect(result).to.deep.equal(expectedObject);
  });

  it('should replace values with the custom string if provided', () => {
    const inputObject = { one: undefined, two: null, three: 'valueThree' };
    const customString = 'CUSTOM_STRING';
    const expectedObject = { one: customString, two: customString, three: 'valueThree' };

    const result = validateValuesOfObject(inputObject, customString);

    expect(result).to.deep.equal(expectedObject);
  });

  it('should handle an empty object', () => {
    const inputObject = {};
    const expectedObject = {};

    const result = validateValuesOfObject(inputObject);

    expect(result).to.deep.equal(expectedObject);
  });
});

describe("isVersionEqualOrGreater", () => {
  it("should return true if the version is greater than the threshold", () => {
    assert.equal(helper.isVersionEqualOrGreater("1.2.3", "1.2.4"), true);
  });

  it("should return true if the version is equal to the threshold", () => {
    assert.equal(helper.isVersionEqualOrGreater("1.2.3", "1.2.3"), true);
  });

  it("should return false if the version is less than the threshold", () => {
    assert.equal(helper.isVersionEqualOrGreater("1.2.3", "1.2.2"), false);
  });
});

describe("validSDKVersionForDynamicHubAllocation", () => {
  it("should return true if the version is greater than the threshold", () => {
    assert.equal(helper.validSDKVersionForDynamicHubAllocation("browserstack-local-javaagent/2.2.3"), true);
  });

  it("should return true if the version is equal to the threshold", () => {
    assert.equal(helper.validSDKVersionForDynamicHubAllocation("browserstack-local-javaagent/2.0.0"), true);
  });

  it("should return false if the version is less than the threshold", () => {
    assert.equal(helper.validSDKVersionForDynamicHubAllocation("browserstack-local-javaagent/1.2.1"), false);
  });
});

describe('validateAIHealingDetails', () => {
  it('should return true for a valid healingDetails object', () => {
    const validHealingDetails = {
      total_healing_enabled_request: 10,
      total_healing_request: 5,
      pre_check_failure_count: 1,
      script_exec_error_count: 2,
      healing_failure_count: 1,
      healing_success_count: 1,
      total_healing_duration: 1000,
    };

    const result = helper.validateAIHealingDetails(validHealingDetails);
    assert.equal(result, true);
  });

  it('should return false if healingDetails is missing required keys', () => {
    const invalidHealingDetails = {
      total_healing_enabled_request: 5,
      total_healing_request: 10,
      // Missing other required keys
    };

    const result = helper.validateAIHealingDetails(invalidHealingDetails);
    assert.equal(result, false);
  });

  it('should return false if healingDetails has invalid types', () => {
    const invalidHealingDetails = {
      total_healing_enabled_request: '5', // Should be a number
      total_healing_request: 10,
      script_exec_error_count: 2,
      pre_check_failure_count: 1,
      healing_failure_count: 3,
      healing_success_count: 7,
      total_healing_duration: 1000,
    };

    const result = helper.validateAIHealingDetails(invalidHealingDetails);
    assert.equal(result, false);
  });

  it('should return false if healingDetails is null', () => {
    const result = helper.validateAIHealingDetails(null);
    assert.equal(result, false);
  });

  it('should return false if healingDetails is not an object', () => {
    const result = helper.validateAIHealingDetails('invalid');
    assert.equal(result, false);
  });

  it('should return false if healingDetails is an empty object', () => {
    const result = helper.validateAIHealingDetails({});
    assert.equal(result, false);
  });

  it('should return false if healingDetails object contains incorrect keys', () => {
    const invalidHealingDetails = {
      abc: 5,
      def: 10,
    };
    const result = helper.validateAIHealingDetails(invalidHealingDetails);
    assert.equal(result, false);
  });
});

describe("validate selenium versions check", () => {
  it("should return true if the version is greater than the threshold", () => {
    assert.equal(helper.validateCdpSeleniumJar("4.1.1"), true);
    assert.equal(helper.validateBidiSeleniumJar("4.21.0"), true);
    assert.equal(helper.validateDeleteBrowserVersion("4.1.1"), true);
    assert.equal(helper.validateDeleteChromeOptionsEdgeChromium("4.1.1"), true);
  });

  it("should return true if the version is equal to the threshold", () => {
    assert.equal(helper.validateCdpSeleniumJar("4.0.0"), true);
    assert.equal(helper.validateBidiSeleniumJar("4.20.0"), true);
    assert.equal(helper.validateDeleteBrowserVersion("4.0.0"), true);
    assert.equal(helper.validateDeleteChromeOptionsEdgeChromium("4.0.0"), true);
  });

  it("should return false if the version is less than the threshold", () => {
    assert.equal(helper.validateCdpSeleniumJar("3.141.59"), false);
    assert.equal(helper.validateBidiSeleniumJar("4.19.0"), false);
    assert.equal(helper.validateDeleteBrowserVersion("3.141.59"), false);
    assert.equal(helper.validateDeleteChromeOptionsEdgeChromium("3.141.59"), false);
  });

  it("should return false if error", () => {
    const x = sinon.stub(helper, 'isVersionEqualOrGreater').throws(new Error('Something went wrong'));
    assert.equal(helper.validateCdpSeleniumJar("3.141.59"), false);
    assert.equal(helper.validateBidiSeleniumJar("3.141.59"), false);
    assert.equal(helper.validateDeleteBrowserVersion("3.141.59"), false);
    assert.equal(helper.validateDeleteChromeOptionsEdgeChromium("3.141.59"), false);
    x.restore();
  });
});

describe("stringTemplating", () => {
  it("String characters should be replaced with expected ones", () => {
    const arr = ["a", "b", "c"];
    var expectedResString = "Characters are a  b  c ";
    var resString = helper.stringTemplating("Characters are <0>  <1>  <2> ", arr);
    assert.deepEqual(resString, expectedResString);
  });
});

describe("helper getParsedObjectOrEmpty scenario", () => {
  it("should pass for a stringified JSON", () => {
    const jsonString = JSON.stringify({"randomKey": "randomValue", "another": ["array"]});
    assert.deepEqual(helper.getParsedObjectOrEmpty(jsonString), JSON.parse(jsonString));
  });

  it("should fail for an invalid stringified JSON", () => {
    const jsonString = '{"randomKey":"randomValue,"another":["array"]}';
    assert.deepEqual(helper.getParsedObjectOrEmpty(jsonString), {});
  });

  it("should fail for a plain string", () => {
    const jsonString = "random string";
    assert.deepEqual(helper.getParsedObjectOrEmpty(jsonString), {});
  });
});

describe("helper nestedKeyValue scenario", () => {
  it("should pass for a valid key value requested", () => {
    const json = {"random": { "another_random": {"key": "value"}}};
    assert.equal(helper.nestedKeyValue(json, ["random", "another_random", "key"]), "value");
  });

  it("should fail for an invalid key value requested", () => {
    const json = {"random": { "another_random": {"key": "value"}}};
    assert.equal(helper.nestedKeyValue(json, ["random", "another_random", "wrong_key"]), undefined);
  });

  it("should fail for a stringified JSON", () => {
    const json = {"random": { "another_random": {"key": "value"}}};
    assert.equal(helper.nestedKeyValue(JSON.stringify(json), ["random", "another_random", "key"]), undefined);
  });
});


describe("check keyObject debugSession flag", () => {
  it("should be undefined if debugSession key doesnt exist", () => {
    const keyObject = this.getKeyObject();
    assert.equal(keyObject.debugSession, undefined);
  });
});

describe("get s3LogFormat", () => {
  it("should return s3 log formatted string", () => {
     const date = helper.getDate();
     assert.equal(helper.s3LogFormat("REQUEST", date, "Random Data"), date.toString()+" REQUEST Random Data");
  });
});

describe('removeFromGlobalRegistry', () => {
  const railsSessionId = 'abcdef';
  let keyObject = null
  let pingZombieStub = null;

  beforeEach(() => {
    keyObject = this.getKeyObject();
    keyObject.hubHostName = 'hub-cloud.browserstack.com';
    pingZombieStub = sinon.stub(helper, 'PingZombie');
  });

  afterEach(() => {
    helper.PingZombie.restore();
  })

  // it should send userToNginxTime to Zombie for automation_session_stats
  it('it should send userToNginxTime to Zombie for automation_session_stats', () => {
    keyObject.appTesting = false;
    keyObject.userToNginxTime = 100;
    constants.global_registry[railsSessionId] = keyObject;

    helper.removeFromGlobalRegistry({ rails_session_id: railsSessionId });

    pingZombieStub.called.should.equal(true);
  });

  it('it should send userToNginxTime to Zombie for automation_session_stats for appTesting', () => {
    keyObject.appTesting = true;
    keyObject.userToNginxTime = 100;
    constants.global_registry[railsSessionId] = keyObject;

    helper.removeFromGlobalRegistry({ rails_session_id: railsSessionId });

    pingZombieStub.called.should.equal(true);
  });

  it('it should call pushToCLS if appiumCommandUsage is set', () => {
    const clsStub = sinon.stub(helper, 'pushToCLS');
    keyObject.appTesting = true;
    keyObject.appiumCommandUsage = { page_source: 1 };
    constants.global_registry[railsSessionId] = keyObject;

    helper.removeFromGlobalRegistry({ rails_session_id: railsSessionId });

    clsStub.called.should.equal(true);
    clsStub.calledWith('command_usage_tracking_appium', {
      ...keyObject.appiumCommandUsage,
      session_id: railsSessionId,
    }, true)
      .should
      .equal(true);
    clsStub.restore();
  });

  it('it should send server_port to Zombie for automation_session_stats', () => {
    keyObject.appTesting = false;
    keyObject.server_port = 4444;
    constants.global_registry[railsSessionId] = keyObject;
    helper.removeFromGlobalRegistry({rails_session_id: railsSessionId});
    assert.equal(pingZombieStub.getCall(0).args[0].tertiary_params.server_port, keyObject.server_port);
  });

  it('it should send hub_hostname to Zombie for automation_session_stats', () => {
    keyObject.appTesting = false;
    constants.global_registry[railsSessionId] = keyObject;
    helper.removeFromGlobalRegistry({rails_session_id: railsSessionId});
    assert.equal(pingZombieStub.getCall(0).args[0].tertiary_params.hub_hostname, keyObject.hubHostName);
  });

  it('it should send hub_hostname to Zombie for app_automation_session_stats', () => {
    keyObject.appTesting = true;
    constants.global_registry[railsSessionId] = keyObject;
    helper.removeFromGlobalRegistry({rails_session_id: railsSessionId});
    assert.equal(pingZombieStub.getCall(0).args[0].tertiary_params.hub_hostname, keyObject.hubHostName);
  });

});

describe('#generateQueuedRequestRetryDelay', () => {
  const exponentialRetryDelaySize = QUEUED_REQUEST_EXPONENTIAL_DELAY.DEFAULT_RETRY_DELAY.length;

  for (let i = 1; i <= exponentialRetryDelaySize; i++) {
    it(`should generate exponential retry of ${QUEUED_REQUEST_EXPONENTIAL_DELAY.DEFAULT_RETRY_DELAY[i-1]} for attempt ${i}`, () => {
      const requestDelay = helper.generateQueuedRequestRetryDelay(i);
      assert(requestDelay === QUEUED_REQUEST_EXPONENTIAL_DELAY.DEFAULT_RETRY_DELAY[i-1]);
    });
  }

  it(`should generate exponential retry of ${QUEUED_REQUEST_EXPONENTIAL_DELAY.HARD_RETRY_REQUEST_DELAY} for attempt ${exponentialRetryDelaySize + 2} which is greater than ${exponentialRetryDelaySize}`, () => {
    const requestDelay = helper.generateQueuedRequestRetryDelay(exponentialRetryDelaySize + 2);
    assert(requestDelay === QUEUED_REQUEST_EXPONENTIAL_DELAY.HARD_RETRY_REQUEST_DELAY);
  });


  it(`should generate exponential retry of ${QUEUED_REQUEST_EXPONENTIAL_DELAY.HARD_RETRY_REQUEST_DELAY} for attempt ${exponentialRetryDelaySize + 10} which is greater than ${exponentialRetryDelaySize}`, () => {
    const requestDelay = helper.generateQueuedRequestRetryDelay(exponentialRetryDelaySize + 10);
    assert(requestDelay === QUEUED_REQUEST_EXPONENTIAL_DELAY.HARD_RETRY_REQUEST_DELAY);
  });
});

describe('#generateRailsCustomTimeout', () => {
  describe('for reserved terminals', () => {
    const customRetries = constants.RESERVED_TERMINAL_QUEUEING.RESERVED_TERMINAL_CUSTOM_RETRIES;
    const customTime = constants.RESERVED_TERMINAL_QUEUEING.RESERVED_TERMINAL_MAX_WAIT_TIME / customRetries;
    describe('for app automate sessions', () => {
      describe('for mjsonwp session', () => {
        it(`should generate custom time as ${customTime} if reserveDevice cap and no of retries less than ${customRetries}`, () => {
          for (let i = 1; i <= customRetries; i++) {
            var generatedCustomTimeout = helper.generateRailsCustomTimeout({
              post_params: {
                isAppAutomate: true,
                desiredCapabilities: "%7B%22browserstack.reserveDevice%22%3Atrue%7D"
              }
            }, i);
            assert(generatedCustomTimeout == customTime)
          }
        })
        it(`should generate custom time as 0 if no reserveDevice cap and no of retries less than ${customRetries}`, () => {
          for (let i = 1; i <= customRetries; i++) {
            var generatedCustomTimeout = helper.generateRailsCustomTimeout({
              post_params: {
                isAppAutomate: true,
                desiredCapabilities: ""
              }
            }, i);
            assert(generatedCustomTimeout == 0)
          }
        })
        it(`should generate custom time as 0 if no of retries greater than ${customRetries}`, () => {
          for (let i = customRetries + 1; i <= customRetries + 3; i++) {
            var generatedCustomTimeout = helper.generateRailsCustomTimeout({
              post_params: {
                isAppAutomate: true,
                desiredCapabilities: "%7B%22browserstack.reserveDevice%22%3Atrue%7D"
              }
            }, i)
            assert(generatedCustomTimeout == 0)
          }
        })
      });
      describe('for w3c session', () => {
        it(`should generate custom time as ${customTime} if reserveDevice cap and no of retries less than ${customRetries}`, () => {
          for (let i = 1; i <= customRetries; i++) {
            var generatedCustomTimeout = helper.generateRailsCustomTimeout({
              post_params: {
                isAppAutomate: true,
                desiredCapabilities: "%7B%22bstack%3Aoptions%22%3A%7B%22reserveDevice%22%3Atrue%7D%7D"
              }
            }, i)
            assert(generatedCustomTimeout == customTime)
          }
        })
        it(`should generate custom time as 0 if no reserveDevice cap and no of retries less than ${customRetries}`, () => {
          for (let i = 1; i <= customRetries; i++) {
            var generatedCustomTimeout = helper.generateRailsCustomTimeout({
              post_params: {
                isAppAutomate: true,
                desiredCapabilities: "%7B%22bstack%3Aoptions%22%3A%7B%7D%7D"
              }
            }, i)
            assert(generatedCustomTimeout == 0)
          }
        })
        it(`should generate custom time as 0 if no of retries greater than ${customRetries}`, () => {
          for (let i = customRetries + 1; i <= customRetries + 3; i++) {
            var generatedCustomTimeout = helper.generateRailsCustomTimeout({
              post_params: {
                isAppAutomate: true,
                desiredCapabilities: "%7B%22bstack%3Aoptions%22%3A%7B%22reserveDevice%22%3Atrue%7D%7D"
              }
            }, i)
            assert(generatedCustomTimeout == 0)
          }
        })
      });
    });
    describe('for automate sessions', () => {
      it(`should generate custom time as 0`, () => {
        for (let i = 1; i <= customRetries + 5; i++) {
          var generatedCustomTimeout = helper.generateRailsCustomTimeout({
            post_params: {
              isAppAutomate: false,
              desiredCapabilities: "%7B%22bstack%3Aoptions%22%3A%7B%22reserveDevice%22%3Atrue%7D%7D"
            }
          }, i)
          assert(generatedCustomTimeout == 0)
        }
      })
    });
  });

  it('should return default retry delay for retries greater than exponentialRetryDelaySize', () => {
    const options = {
      post_params: {
        isAppAutomate: false
      },
      reason: 'nta'
    };
    const retries = 5;
    const result = helper.generateRailsCustomTimeout(options, retries);
    assert.equal(result, constants.NTA_RETRY_EXPONENTIAL_DELAY.NTA_RETRY_DEFAULT_DELAY);
  });

  it('should return specific retry delay for retries within exponentialRetryDelaySize', () => {
    const options = {
      post_params: {
        isAppAutomate: false
      },
      reason: 'nta'
    };
    const retries = 2;
    const result = helper.generateRailsCustomTimeout(options, retries);
    assert.equal(result, constants.NTA_RETRY_EXPONENTIAL_DELAY.DEFAULT_NTA_RETRY_DELAY[1]);
  });

  it('should return 0 if retries is null or undefined', () => {
    const options = {
      post_params: {
        isAppAutomate: false
      },
      reason: 'nta'
    };
    const retries = null;
    const result = helper.generateRailsCustomTimeout(options, retries);

    assert.equal(result, 0);
  });

  it('should not modify timeout for non-nta reasons', () => {
    const options = {
      post_params: {
        isAppAutomate: false
      },
      reason: 'other'
    };
    const retries = 2;
    const result = helper.generateRailsCustomTimeout(options, retries);
    assert.equal(result, 0);
  });

  it('should not modify timeout for app automate sessions', () => {
    const options = {
      post_params: {
        isAppAutomate: true
      },
      reason: 'nta'
    };
    const retries = 2;
    const result = helper.generateRailsCustomTimeout(options, retries);
    assert.equal(result, 0);
  });
});

describe('checks isExceptionLog', () => {
  it('should return true for exception logs', (done) => {
    const isExceptionLog = helper.isExceptionLog('EXCEPTION');
    assert(isExceptionLog === true);
    done();
  });
  ['REQUEST', 'RESPONSE', 'kafkaClient'].forEach((category) => {
    it(`should return false for ${category} logs`, (done) => {
      const isExceptionLog = helper.isExceptionLog(category);
      assert(isExceptionLog === false);
      done();
    });
  });
});

describe('#skipJarDeleteExperiment', () => {
  it(`should return regex match for Windows when experiment is enabled`, (done) => {
    constants.ENABLE_EXPERIMENT_SKIP_JAR_DELETE = true;
    assert(isNotUndefined(helper.skipJarDeleteExperiment('Windows')));
    done();
  });

  it(`should return false when experiment is disabled`, (done) => {
    constants.ENABLE_EXPERIMENT_SKIP_JAR_DELETE = false;
    assert(helper.skipJarDeleteExperiment('os') === false);
    done();
  });
});

describe('instrumentRedisCommand', () => {
  beforeEach(() => {
    constants.pushToHootHootRegistry.redisCommandCount = {};
    constants.pushToHootHootRegistry.redisCommandRate = {};
  });

  it('should add to hoot hoot registry', () => {
    helper.instrumentRedisCommand('test', 100);
    assert(constants.pushToHootHootRegistry.redisCommandCount);
    assert(constants.pushToHootHootRegistry.redisCommandRate);
    assert(constants.pushToHootHootRegistry.redisCommandCount.test === 1);
    assert(constants.pushToHootHootRegistry.redisCommandRate.test === 100);
  });
});

describe('isNonEmptyHash', () => {
  it('should return true if input is a non empty hash', (done) => {
    assert(helper.isNonEmptyHash({"a": "b"}) === true);
    done();
  });

  it('should return false if input is aa empty hash', (done) => {
    assert(helper.isNonEmptyHash({}) === false);
    done();
  });

  it('should return false if input is undefined', (done) => {
    assert(helper.isNonEmptyHash(undefined) === false);
    done();
  });

  it('should return false if input is not an hash', (done) => {
    assert(helper.isNonEmptyHash(5) === false);
    done();
   });
});

describe('originalOrParsedObj scenarios', () => {
  let hash;
  beforeEach(() => {
    hash = {
      keyOne: 'valueOne',
      keyTwo: 'valueTwo'
    }
  });

  it("returns the original hash if the input is of type Object, i.e. {'key': 'value'...}", () => {
    expect(helper.originalOrParsedObj(hash)).to.eql(hash);
  });

  it("returns a parsed hash if the input is a stringified object, i.e. '{\"key\": \"value\"...}'", () => {
    const stringifiedHash = JSON.stringify(hash);
    expect(helper.originalOrParsedObj(stringifiedHash)).to.eql(hash);
  });

  it("returns a parsed hash if the input is a Bufferred Object, i.e. Buffer.from('{\"key\": \"value\"...}')", () => {
    const bufferredObject = Buffer.from(JSON.stringify(hash));
    expect(helper.originalOrParsedObj(bufferredObject)).to.eql(hash);
  });

  context("returns an empty hash in case the input is not a valid hash even after parsing", () => {
    const invalidInput = {
      "random string": "random string",
      "null": null,
      "undefined": undefined,
      "empty string": "",
      "number": 1234,
      "boolean": true,
      "buffer": Buffer.from("just a string"),
      "invalid stringified hash": '{"key": \"value\'}',
      "array": [1, 2, 3, 4]
    }

    Object.entries(invalidInput).forEach(([invalidInputType, invalidInputValue]) => {
      it(`with ${invalidInputType} as input`, () => {
        expect(helper.originalOrParsedObj(invalidInputValue)).to.eql({});
      });
    });
  });
});

describe('nestedKeyValueGeneric scenarios', () => {
  let hash;

  beforeEach(() => {
    hash = {
      keyOne: {
        nestedKeyOne: "nestedValueOne"
      },
      keyTwo: JSON.stringify({
        nestedStringifiedKey: "nestedStringifiedValue"
      })
    }
  });

  context('without default value and test function', () => {
    it("returns the nested value if found from an Object", () => {
      expect(helper.nestedKeyValueGeneric(hash, ['keyOne', 'nestedKeyOne'])).to.eql(hash.keyOne.nestedKeyOne);
    });

    it("returns the nested value if found from an Object which has stringified values", () => {
      expect(helper.nestedKeyValueGeneric(hash, ['keyTwo', 'nestedStringifiedKey'])).to.eql(JSON.parse(hash.keyTwo).nestedStringifiedKey);
    });

    it("returns undefined if value is not found from an Object", () => {
      expect(helper.nestedKeyValueGeneric(hash, ['keyOne', 'nestedKeyOne', 'nestedKeyTwo'])).to.be.undefined;
    });

    it("returns undefined if value is not found from an Object which has stringified values", () => {
      expect(helper.nestedKeyValueGeneric(hash, ['keyTwo', 'nestedStringifiedKey', 'nestedStringifiedKeyTwo'])).to.be.undefined;
    });

    it("returns the nested value from a Stringified Object", () => {
      expect(helper.nestedKeyValueGeneric(JSON.stringify(hash), ['keyOne', 'nestedKeyOne'])).to.eql(hash.keyOne.nestedKeyOne);
    });

    it("returns the nested value from a Stringified Object which has stringified values already", () => {
      expect(helper.nestedKeyValueGeneric(JSON.stringify(hash), ['keyTwo', 'nestedStringifiedKey'])).to.eql(JSON.parse(hash.keyTwo).nestedStringifiedKey);
    });
  });

  context("with default value and test function", () => {
    it("returns the default value if the value doesn't pass the test function", () => {
      expect(helper.nestedKeyValueGeneric(hash, ['keyOne', 'nestedKeyTwo'], 'DEFAULT_VAL', isNotUndefined)).to.eql('DEFAULT_VAL');
    });

    it("returns the result as it is if it passes the test function", () => {
      expect(helper.nestedKeyValueGeneric(hash, ['keyOne', 'nestedKeyOne'], 'DEFAULT_VAL', isNotUndefined)).to.eql(hash.keyOne.nestedKeyOne);
    });

    it("returns the result as it is without evaluating it with the test function if only default value is passed", () => {
      expect(helper.nestedKeyValueGeneric(hash, ['keyOne', 'nestedKeyTwo'], 'DEFAULT_VAL')).to.be.undefined;
    });

    it("returns the result as it is without evaluating it with the test function if only test function is passed", () => {
      expect(helper.nestedKeyValueGeneric(hash, ['keyOne', 'nestedKeyTwo'], undefined, isNotUndefined)).to.be.undefined;
    });
  });

  context("with default value and test value (i.e. to test the result with the expected value and not a function)", () => {
    it("returns the default value if the value doesn't equate to the test value", () => {
      expect(helper.nestedKeyValueGeneric(hash, ['keyOne', 'nestedKeyTwo'], 'DEFAULT_VAL', "EXPECTED_VAL")).to.eql('DEFAULT_VAL');
    });

    it("returns the result as it is if it equals the test value", () => {
      expect(helper.nestedKeyValueGeneric(hash, ['keyOne', 'nestedKeyOne'], 'DEFAULT_VAL', 'nestedValueOne')).to.eql(hash.keyOne.nestedKeyOne);
    });

    it("returns the result as it is without equating it with test value if only default value is passed", () => {
      expect(helper.nestedKeyValueGeneric(hash, ['keyOne', 'nestedKeyTwo'], 'DEFAULT_VAL')).to.be.undefined;
    });

    it("returns the result as it is without equating it with test value if only test value is passed", () => {
      expect(helper.nestedKeyValueGeneric(hash, ['keyOne', 'nestedKeyTwo'], undefined, "EXPECTED_VAL")).to.be.undefined;
    });
  });

  context("returns undefined if the input is not a valid hash", () => {
    const invalidInput = {
      "random string": "random string",
      "null": null,
      "undefined": undefined,
      "empty string": "",
      "number": 1234,
      "boolean": true,
      "buffer": Buffer.from("just a string"),
      "invalid stringified hash": '{"key": \"value\'}',
      "array": [1, 2, 3, 4]
    }

    Object.entries(invalidInput).forEach(([invalidInputType, invalidInputValue]) => {
      it(`with ${invalidInputType} as input`, () => {
        expect(helper.nestedKeyValueGeneric(invalidInputValue, ['someKey'])).to.eql(undefined);
      });
    });
  });
});

describe('mapDurationMap create nested map for session_time_components', () => {

  it('should create map with single attempts for normal session', () => {
    let durationMap = { 'request_id' : '9ef07fd5-9db6-4b39-a33b-fcba06e17120',
    'received-request': 6332,
    'session-creation': 6331,
    'caps-parsing': 1,
    'is-queue-full': 0,
    'increase-queue': 1,
    'post-browserstack': 5776,
    'rails-defender': 9,
    'selauth-request': 538,
    'check-rails-response-code': 2,
    'fire-command': 2065,
    'patch-start-session': 0,
    'start-command': 3153,
    'set-page-load-time': 545,
    'activate-browser': 0 };
    let nestedDurationMap = { request_id: '9ef07fd5-9db6-4b39-a33b-fcba06e17120',
    'received-request': 6332,
    'session-creation': 6331,
    'caps-parsing': 1,
    'is-queue-full': 0,
    'increase-queue': 1,
    'post-browserstack': 5776,
    'rails-defender': { 'attempts': 1, 'total': 9, '0_rails-defender': 9 },
    'selauth-request': { 'attempts': 1, 'total': 538, '0_selauth-request': 538 },
    'check-rails-response-code': { 'attempts': 1, 'total': 2, '0_check-rails-response-code': 2 },
    'fire-command': { 'attempts': 1, 'total': 2065, '0_fire-command': 2065 },
    'patch-start-session': { 'attempts': 1, 'total': 0, '0_patch-start-session': 0 },
    'start-command': { 'attempts': 1, 'total': 3153, '0_start-command': 3153 },
    'set-page-load-time': 545,
    'activate-browser': 0 }
    expect(helper.mapDurationMap(durationMap)).to.eql(nestedDurationMap);
  })

  it('should create map with multiple attempts for queued session', () => {
    let durationMap = { 'request_id': '38b1f0f2-4107-4fe7-b444-45bebdd4bb80',
    'received-request': 82602,
    'session-creation': 82599,
    'caps-parsing': 1,
    'is-queue-full': 1,
    'increase-queue': 1,
    'post-browserstack': 82028,
    'rails-defender': 45,
    'selauth-request': 1391,
    'check-rails-response-code': 4,
    'time-in-queue': 67346,
    '1_rails-defender': 18,
    '1_selauth-request': 383,
    '1_check-rails-response-code': 2,
    '2_rails-defender': 15,
    '2_selauth-request': 1121,
    '2_check-rails-response-code': 10,
    'fire-command': 10114,
    'patch-start-session': 1,
    'start-command': 3108,
    'set-page-load-time': 553,
    'activate-browser': 1 }
    let nestedDurationMap = { 'request_id': '38b1f0f2-4107-4fe7-b444-45bebdd4bb80',
    'received-request': 82602,
    'session-creation': 82599,
    'caps-parsing': 1,
    'is-queue-full': 1,
    'increase-queue': 1,
    'post-browserstack': 82028,
    'rails-defender': { 'attempts': 3, 'total': 78, '0_rails-defender': 45, '1_rails-defender': 18, '2_rails-defender': 15 },
    'selauth-request': { 'attempts': 3, 'total': 2895, '0_selauth-request': 1391, '1_selauth-request': 383, '2_selauth-request': 1121 },
    'check-rails-response-code': { 'attempts': 3, 'total': 16, '0_check-rails-response-code': 4, '1_check-rails-response-code': 2, '2_check-rails-response-code': 10 },
    'time-in-queue': { 'attempts': 1, 'total': 67346, '0_time-in-queue': 67346 },
    'fire-command': { 'attempts': 1, 'total': 10114, '0_fire-command': 10114 },
    'patch-start-session': { 'attempts': 1, 'total': 1, '0_patch-start-session': 1 },
    'start-command': { 'attempts': 1, 'total': 3108, '0_start-command': 3108 },
    'set-page-load-time': 553,
    'activate-browser': 1 }
    expect(helper.mapDurationMap(durationMap)).to.eql(nestedDurationMap);
  })
});

describe('#timeoutManagerUpdateTimeout', () => {
  it('should call zadd', () => {
    const zaddStub = sinon.stub(helper.redisClient, 'zadd');
    helper.timeoutManagerUpdateTimeout('abcd', {});
    assert(zaddStub.calledOnce);
    zaddStub.restore();
  });

  it('should not call zadd for detox session', () => {
    const zaddStub = sinon.stub(helper.redisClient, 'zadd');
    helper.timeoutManagerUpdateTimeout('abcd', {isDetox: true});
    assert(!zaddStub.called);
    zaddStub.restore();
  });
});

describe('#timeoutManagerClearTimeout', () => {
  it('should call zrem', () => {
    const zremStub = sinon.stub(helper.redisClient, 'zrem');
    helper.timeoutManagerClearTimeout('abcd', {});
    assert(zremStub.calledOnce);
    zremStub.restore();
  });

  it('should not call zadd for detox session', () => {
    const zremStub = sinon.stub(helper.redisClient, 'zrem');
    helper.timeoutManagerUpdateTimeout('abcd', {isDetox: true});
    assert(!zremStub.called);
    zremStub.restore();
  });
});

describe('#getFireCMDErrorMessage function', () => {
  var keyObject, opts, errorString;

  beforeEach(() => {
    keyObject = this.getKeyObject();
    opts = { "terminal_type": "realMobile" }
    errorString = "Could not start Mobile Browser.";
  });

  it('should return flutter error message when bsCapsObject is present', () => {
    keyObject.bsCaps = { "automationName":  "flutter" }
    helper.getFireCMDErrorMessage(opts, keyObject).should.equal(errorString + errorMessages.APP_AUTOMATE_AUTOMATION_ERROR_MESSAGES['flutter']);
  });
  it('should return not flutter error message when bsCapsObject is not present', () => {
    helper.getFireCMDErrorMessage(opts, keyObject).should.equal(errorString);
  });
});

describe('#getReverseOrientation function', () => {
  it('should return LANDSCAPE orientation if current orientation is PORTRAIT', () => {
    helper.getReverseOrientation('PORTRAIT').should.equal('LANDSCAPE');
  });
  it('should return PORTRAIT orientation if current orientation is LANDSCAPE', () => {
    helper.getReverseOrientation('LANDSCAPE').should.equal('PORTRAIT');
  });
});

describe('#getOsVersion function', () => {
  it('should return 14.6 if deviceName is iPhone 12-14.6', () => {
    helper.getOsVersion('iPhone 12-14.6').should.equal(14.6);
  });
  it('should return 14.5.1 if deviceName is iPhone 12-14.5.1', () => {
    helper.getOsVersion('iPhone 12-14.5.1').should.equal(14.5);
  });
  it('should return undefined if deviceName is iPhone 12', () => {
    expect(helper.getOsVersion('iPhone 12')).to.be.undefined;
  });
});

describe('#isMobileCommand function', () => {
  it('should return true if mobile: getAppStrings', () => {
    helper.isMobileCommand('/session/b738879113ed11486e9e68f5c98e989d3ef7d698/execute/sync', JSON.stringify({ script: 'mobile: getAppStrings', args: [{ language: 'fr', stringFile: 'some_file' }] }), 'mobile: getAppStrings').should.equal(true);
  });
  it('should return false if mobile: doesNotMatch', () => {
    helper.isMobileCommand('/session/b738879113ed11486e9e68f5c98e989d3ef7d698/execute/sync', JSON.stringify({ script: 'mobile: getAppStrings', args: [{ language: 'fr', stringFile: 'some_file' }] }), 'mobile: doesNotMatch').should.equal(false);
  });
  it('should return false if not execute command', () => {
    helper.isMobileCommand('/session/b738879113ed11486e9e68f5c98e989d3ef7d698', JSON.stringify({ script: 'mobile: getAppStrings', args: [{ language: 'fr', stringFile: 'some_file' }] }), 'mobile: doesNotMatch').should.equal(false);
  });
});

describe('#getMetaDataforCommand function', () => {
  it('should return strategy for find element command ', () => {
    helper.getMetaDataforCommand('POST', 'element', JSON.stringify({using: "xpath"})).should.equal("xpath");
  });
  it('should return strategy for find elements command ', () => {
    helper.getMetaDataforCommand('POST', 'elements', JSON.stringify({using: "xpath"})).should.equal("xpath");
  });
  it('should return empty strategy for non find element command ', () => {
    helper.getMetaDataforCommand('POST', 'reset', JSON.stringify({using: "xpath"})).should.equal("");
  });
  it('should return empty strategy if exception encountered ', () => {
    helper.getMetaDataforCommand('POST', 'element', {using: "xpath"}).should.equal("");
  });
  it('should return context name for set context command ', () => {
    helper.getMetaDataforCommand('POST', 'context', JSON.stringify({name: "random"})).should.equal("random");
  });
  it('should return context name for execute sync command ', () => {
    helper.getMetaDataforCommand('POST', 'execute_sync', JSON.stringify({script: "mobile:setAppearance"})).should.equal("mobile:setAppearance");
  });
});

describe('#startUpFailureCallbackHandler', () => {
  it('should call addStopToRawLogs method if sessionId is passed in options object', (done) => {
    const device = true;
    const appTesting = false;
    const options = {
      realMobile: true,
      sessionId: 'abcd',
    };

    sinon.stub(HubLogger, 'addStopToRawLogs');
    helper.startUpFailureCallbackHandler(options, device, appTesting, null);
    assert(HubLogger.addStopToRawLogs.called.should.be.true);
    HubLogger.addStopToRawLogs.restore();
    done();
  });
});

describe('#escapeCharactersForJs', () => {
  it('should escape single quotes', (done) => {
    assert(helper.escapeCharactersForJs("'") == '\\\'');
    done();
  });

  it('should escape backward slash', (done) => {
    assert(helper.escapeCharactersForJs('\\') == '\\\\');
    done();
  });
});

describe('#sessionRemovedFromRegionHook function', () => {
  let keyObject;
  beforeEach(() => {
    keyObject = exports.getKeyObject();
    constants.global_registry[keyObject.rails_session_id] = keyObject;
    sinon.stub(pubSub, 'publish');
    sinon.stub(helper, 'timeoutManagerClearTimeout');
    sinon.stub(helper, 'removeFromGlobalRegistry');
  });

  afterEach(() => {
    delete constants.global_registry[keyObject.rails_session_id];
    pubSub.publish.restore();
    helper.timeoutManagerClearTimeout.restore();
    helper.removeFromGlobalRegistry.restore();
  });

  it('should not publish if timeout param = null', () => {
    helper.sessionRemovedFromRegionHook(keyObject);
    assert(pubSub.publish.called.should.be.false);
  });
  it('should publish if timeout param = true', () => {
    helper.sessionRemovedFromRegionHook(keyObject,true);
    assert(pubSub.publish.called.should.be.true);
  });
  it('should publish with proper params timeout param = true', () => {
    helper.sessionRemovedFromRegionHook(keyObject,true);
    assert(pubSub.publish.calledWith(constants.sessionTimeout,keyObject.rails_session_id,keyObject));
    helper.sessionRemovedFromRegionHook(keyObject,true,'random key');
    assert(pubSub.publish.calledWith(constants.sessionTimeout,'random key',keyObject));
  });
  it('should publish if timeout param = false', () => {
    helper.sessionRemovedFromRegionHook(keyObject,false);
    assert(pubSub.publish.called.should.be.true);
  });
  it('should publish with proper params timeout param = false', () => {
    helper.sessionRemovedFromRegionHook(keyObject,false);
    assert(pubSub.publish.calledWith(constants.sessionDeletionChannel,keyObject.rails_session_id));
    helper.sessionRemovedFromRegionHook(keyObject,false,'random key');
    assert(pubSub.publish.calledWith(constants.sessionDeletionChannel,'random key'));
  });
});

describe('#pubSubKafkaKeysAndCodes', () => {
  const keyData = {
    'automate_ai_find_element_count': 'random',
    'deviceOrientation': false,
    'lastResponseTime': 'random',
    'lastRequestTime': 'random',
    'outsideBrowserstackTime': 'random',
    'customExecutorInstrumentation': {},
    'seleniumRequestsCount': 'random',
    'nonZeroStatusesCount': [],
    'nonZeroIncrementCounters': [],
    'exceptionEncountered': false,
    'exceptionClass': 'random',
    'exceptionRequest': 'random',
    'exceptionMessage': 'random',
    'insideHubTime': 'random',
    'userToNginxTime': 'random',
    'hubProcessingTime': 'random',
    'jarTime': 'random',
    'nginxToHubTime': 'random',
    'sleepTime': 'random',
    'sleepTimeNow': 'random',
    'numSleep': 'random',
    'request_count': 'random',
    'timestamp': 'random',
    'lastRequest': 'random',
    'instable': 'random',
    'safariPrivoxyTimeout': true,
    'updateOutsideBrowserstackTime': {
      'lastResponseTime': 'random',
      'lastRequestTime': 'random'
    }
  };
  const codeData = {
    '_1': false,
    '_2': 'random',
    '_3': 'random',
    '_4': 'random',
    '_5': {},
    '_6': 'random',
    '_7': [],
    '_8': [],
    '_9': false,
    '_a': 'random',
    '_b': 'random',
    '_c': 'random',
    '_d': 'random',
    '_e': 'random',
    '_z': 'random',
    '_f': 'random',
    '_g': 'random',
    '_h': 'random',
    '_i': 'random',
    '_j': 'random',
    '_k': 'random',
    '_l': 'random',
    '_m': 'random',
    '_n': true,
    '_o': {
      '_2': 'random',
      '_3': 'random'
    },
    '_p': 'random',
    '_B': "random",
  };
  const redactKeyData = {
    "automate_ai_find_element_count": "random",
    'deviceOrientation': false,
    'lastResponseTime': 'random',
    'lastRequestTime': 'random',
    'outsideBrowserstackTime': 'random',
    'customExecutorInstrumentation': {},
    'seleniumRequestsCount': 'random',
    'nonZeroStatusesCount': [],
    'nonZeroIncrementCounters': [],
    'exceptionEncountered': false,
    'exceptionClass': 'random',
    'exceptionRequest': 'random',
    'exceptionMessage': 'random',
    'insideHubTime': 'random',
    'userToNginxTime': 'random',
    'hubProcessingTime': 'random',
    'jarTime': 'random',
    'nginxToHubTime': 'random',
    'sleepTime': 'random',
    'sleepTimeNow': 'random',
    'numSleep': 'random',
    'request_count': 'random',
    'timestamp': 'random',
    'lastRequest': 'random',
    'instable': 'random',
    'video_aws_keys': 'somerandomkey',
    'video_aws_secret': 'somerandomsecret',
    's3key': 'somekey',
    's3secret': 'somesecret',
    'safariPrivoxyTimeout': true,
    'updateOutsideBrowserstackTime': {
      'lastResponseTime': 'random',
      'lastRequestTime': 'random'
    }
  };

  const redactedData = {
    '_1': false,
    '_2': 'random',
    '_3': 'random',
    '_4': 'random',
    '_5': {},
    '_6': 'random',
    '_7': [],
    '_8': [],
    '_9': false,
    '_a': 'random',
    '_b': 'random',
    '_c': 'random',
    '_d': 'random',
    '_e': 'random',
    '_z': 'random',
    '_f': 'random',
    '_g': 'random',
    '_h': 'random',
    '_i': 'random',
    '_j': 'random',
    '_k': 'random',
    '_l': 'random',
    '_m': 'random',
    'video_aws_keys': '[REDACTED]',
    'video_aws_secret': '[REDACTED]',
    's3key': '[REDACTED]',
    's3secret': '[REDACTED]',
    '_n': true,
    '_o': {
      '_2': 'random',
      '_3': 'random'
    },
    '_p': 'random',
    '_B': "random",
  };

  it('should convert keys to codes', (done) => {
    assert.deepEqual(helper.convertPubSubKafkaKeysToCodes(keyData), codeData);
    done();
  });

  it('should convert codes to keys for intermediate and stop request', (done) => {
    const tmpCodeData = {
      uniqueId: 'uid',
      data: codeData
    };
    helper.convertPubSubKafkaCodesToKeys(tmpCodeData);
    assert.deepEqual(tmpCodeData, {
      uniqueId: 'uid',
      data: keyData
    });
    done();
  });

  it('should convert codes to keys for start request', (done) => {
    const tmpCodeData = {
      uniqueId: 'uid',
      initKeyObject: codeData
    };
    helper.convertPubSubKafkaCodesToKeys(tmpCodeData);
    assert.deepEqual(tmpCodeData, {
      uniqueId: 'uid',
      initKeyObject: keyData
    });
    done();
  });

  it('should convert keys to codes', (done) => {
    assert.deepEqual(helper.convertPubSubKafkaKeysToCodes(redactKeyData), redactedData);
    done();
  });

});

describe('#isHubOrInstrumentationService', () => {
  it('should return service name depending on env topic variable', (done) => {
    const currTopic = process.env.CONSUMER_TOPIC;
    process.env.CONSUMER_TOPIC = 'instrumentation_logs';
    assert(helper.isHubOrInstrumentationService(process.env.CONSUMER_TOPIC) === 'Instrumentation Service');
    process.env.CONSUMER_TOPIC = 'random_logs';
    assert(helper.isHubOrInstrumentationService(process.env.CONSUMER_TOPIC) === '');
    delete process.env.CONSUMER_TOPIC;
    assert(helper.isHubOrInstrumentationService(process.env.CONSUMER_TOPIC) === 'SeleniumHub');
    process.env.CONSUMER_TOPIC = currTopic;
    done();
  });
});

describe('#sendDataToInstrumentationService', () => {
  beforeEach(() => {
    sinon.stub(helper, 'convertPubSubKafkaKeysToCodes');
    sinon.stub(HubLogger, 'uploadLogPartToKafka');
  });
  afterEach(() => {
    helper.convertPubSubKafkaKeysToCodes.restore();
    HubLogger.uploadLogPartToKafka.restore();
  });

  it('should send data to kafka topic if type = start request', (done) => {
    helper.sendDataToInstrumentationService({},'REQUEST_START',{});
    assert(helper.convertPubSubKafkaKeysToCodes.called.should.be.true);
    assert(HubLogger.uploadLogPartToKafka.called.should.be.true);
    done();
  });

  it('should send data to kafka topic if type = stop/timeout request', (done) => {
    helper.sendDataToInstrumentationService({},'STOP_SESSION',{});
    setTimeout(()=>{
      assert(helper.convertPubSubKafkaKeysToCodes.called.should.be.true);
      assert(HubLogger.uploadLogPartToKafka.calledTwice.should.be.true);
      done();
    },150);
  });

  it('should send data to kafka topic if type = atomic request', (done) => {
    helper.sendDataToInstrumentationService({},null,{});
    assert(helper.convertPubSubKafkaKeysToCodes.called.should.be.true);
    assert(HubLogger.uploadLogPartToKafka.called.should.be.true);
    done();
  });
});

describe('#setClientConnectionSocketsCount', () => {
  var keyObject;

  beforeEach(() => {
    keyObject = this.getKeyObject();
    keyObject.clientConnectionSocketsCount = 0;
    constants.global_registry[keyObject.rails_session_id] = keyObject;
  });

  it('it sets x-connection-requests for 1 valid request and not for any more succeeding requests', function(done) {
    var validInputOutput = [["1",0], ["1",0], ["2", 2], ["100", 2], ["999", 2]];
    validInputOutput.forEach((arg) => {
      helper.setClientConnectionSocketsCount({is_app_automate_session: false, headers: {"x-connection-requests": arg[0]}}, keyObject, keyObject.rails_session_id);
      assert(constants.global_registry[keyObject.rails_session_id].clientConnectionSocketsCount=== arg[1])
    });
    done();
  });

  it('Do not set if request is for app-automate session', function(done) {
    helper.setClientConnectionSocketsCount({is_app_automate_session: true, headers: {"x-connection-requests": "999"}}, keyObject, keyObject.rails_session_id)
    assert(constants.global_registry[keyObject.rails_session_id].clientConnectionSocketsCount=== 0)
    done();
  });

  it('Do not set if request header does not contain `x-connection-requests`', function(done) {
    helper.setClientConnectionSocketsCount({is_app_automate_session: false, headers: {}}, keyObject, keyObject.rails_session_id)
    assert(constants.global_registry[keyObject.rails_session_id].clientConnectionSocketsCount=== 0)
    done();
  });

  it('Do not set if key or keyObject passed is null', function(done) {
    helper.setClientConnectionSocketsCount({is_app_automate_session: false, headers: {"x-connection-requests": "3"}},null, keyObject.rails_session_id)
    helper.setClientConnectionSocketsCount({is_app_automate_session: false, headers: {"x-connection-requests": "123"}},keyObject, null)
    assert(constants.global_registry[keyObject.rails_session_id].clientConnectionSocketsCount=== 0)
    done();
  });
});

describe('#unregisterDroppedRailsRequestFromTrackSet', () => {
  after((done) => {
    helper.redisClient.flushall()
      .then(() => {
        done();
      });
  });

  beforeEach((done) => {

    user = {
      username: 'user::name',
      password: 'user::pass',
      response: {
        statusCode: 200,
      },
    };

    autPipelineReqMeta = {
      identifier: 'pipe::autoident',
      sessionId: helper.SESSION_ID,
      username: 'user::name',
      password: 'user::pass',
      response: {
        statusCode: 200,
      },
      isStartRequest: true,
      isStopRequest: false,
      isAppAutomateSession: false,
      railsPipelineExtraData: {
        isQueued: false,
        isMobile: false,
      },
    };

    appAutPipelineReqMeta = {
      identifier: 'pipe::appautoident',
      sessionId: helper.SESSION_ID,
      username: 'user::name',
      password: 'user::pass',
      response: {
        statusCode: 200,
      },
      isStartRequest: true,
      isStopRequest: false,
      isAppAutomateSession: true,
      railsPipelineExtraData: {
        isQueued: false,
        isMobile: false,
      },
    };

    helper.redisClient.flushall()
      .then(() => {
        done();
      });
  });

  it('should release request from trackset for Automate', (done) => {
    helper.redisClient
      .sadd(
        constants.railsPipeline.redisAutomateTrackTokenHash.queueTrackRequestsSetTag,
        autPipelineReqMeta.identifier
      )
      .then(async (res) => {
        if(res == 1) {
          await helper.unregisterDroppedRailsRequestFromTrackSet(autPipelineReqMeta.identifier, autPipelineReqMeta.isAppAutomateSession)
          helper.redisClient
            .sismember(
            constants.railsPipeline.redisAutomateTrackTokenHash.queueTrackRequestsSetTag,
            autPipelineReqMeta.identifier
            ).then((res) => {
              res.should.equal(0);
              done();
            });
          } else {
            done();
          }
      });
    });

    it('should release request from trackset for App. Automate', (done) => {
      helper.redisClient
        .sadd(
          constants.railsPipeline.redisAppAutomateTrackTokenHash.queueTrackRequestsSetTag,
          appAutPipelineReqMeta.identifier
        )
        .then(async(res) => {
          if(res == 1) {
            await helper.unregisterDroppedRailsRequestFromTrackSet(appAutPipelineReqMeta.identifier, appAutPipelineReqMeta.isAppAutomateSession)
            helper.redisClient
              .sismember(
              constants.railsPipeline.redisAppAutomateTrackTokenHash.queueTrackRequestsSetTag,
              appAutPipelineReqMeta.identifier
              ).then((res) => {
                res.should.equal(0);
                done();
              });
            } else {
              done();
            }
          });
      });

  it('should not release request from automate trackset if tracklog for App. Automate', (done) => {
    helper.redisClient
      .sadd(
        constants.railsPipeline.redisAutomateTrackTokenHash.queueTrackRequestsSetTag,
        autPipelineReqMeta.identifier
      )
      .then(async(res) => {
        if(res == 1) {
          await helper.unregisterDroppedRailsRequestFromTrackSet(appAutPipelineReqMeta.identifier, appAutPipelineReqMeta.isAppAutomateSession)
          helper.redisClient
            .sismember(
            constants.railsPipeline.redisAutomateTrackTokenHash.queueTrackRequestsSetTag,
            autPipelineReqMeta.identifier
            ).then((res) => {
              res.should.equal(1);
              done();
            });
          } else {
            done();
          }
        });
    });

  it('should not release request from App. automate trackset if tracklog for Automate', (done) => {
    helper.redisClient
      .sadd(
        constants.railsPipeline.redisAppAutomateTrackTokenHash.queueTrackRequestsSetTag,
        appAutPipelineReqMeta.identifier
      )
      .then(async(res) => {
        if(res == 1) {
          await helper.unregisterDroppedRailsRequestFromTrackSet(autPipelineReqMeta.identifier, autPipelineReqMeta.isAppAutomateSession)
          helper.redisClient
            .sismember(
            constants.railsPipeline.redisAppAutomateTrackTokenHash.queueTrackRequestsSetTag,
            appAutPipelineReqMeta.identifier
            ).then((res) => {
              res.should.equal(1);
              done();
            });
          } else {
            done();
          }
        });
    });
});

describe('#isPrivateDomainOrIP', function() {
  it('returns true if url is private', function() {
    assert(helper.isPrivateDomainOrIP("127.0.0.1"));
    assert(helper.isPrivateDomainOrIP("bs-local.com"));
    assert(helper.isPrivateDomainOrIP("***********"));
  })

  it('returns false if url is not private', function() {
    assert(!helper.isPrivateDomainOrIP("***********"));
    assert(!helper.isPrivateDomainOrIP(undefined));
    assert(!helper.isPrivateDomainOrIP(null));
    assert(!helper.isPrivateDomainOrIP(''));
  })
})

describe('#getRequestObjAndUrlHostname', function() {
  let keyObject;
  beforeEach(() => {
    keyObject = exports.getKeyObject();
  });
  it('returns parsed requestObject and url hostname', function() {
    const requestData = "{\"url\": \"http://localhost:45691/check\"}";
    const { requestDataUrlHostname, requestDataObj } = helper.getRequestObjAndUrlHostname(requestData, keyObject);
    assert.equal(requestDataUrlHostname, "localhost");
    assert.equal(requestDataObj.url, "http://localhost:45691/check");
  })

  it('returns null hostname if invalid url', function() {
    const requestData = "{\"url\": \"12345\"}";
    const { requestDataUrlHostname } = helper.getRequestObjAndUrlHostname(requestData, keyObject);
    assert.equal(requestDataUrlHostname, null);
  })

  it('returns empty string if JSON parse fails on requestData', function() {
    const requestData = "{\"url\": 'wrong.url.com'}";
    const { requestDataUrlHostname } = helper.getRequestObjAndUrlHostname(requestData, keyObject);
    assert.equal(requestDataUrlHostname, '');
  })

})

describe('#isAppleOs', function() {
  it('returns true when os is ios', function() {
    assert(helper.isAppleOs('iOS'));
  });

  it('returns true when os is tvos', function() {
    assert(helper.isAppleOs('tvOS'));
  });

  it('returns false when os is android', function() {
    assert(!helper.isAppleOs('android'));
  });

  it('returns false when os is undefined', function() {
    assert(!helper.isAppleOs(undefined));
  });

  it('returns false when os is null', function() {
    assert(!helper.isAppleOs(null));
  });

  it('ignores case', function() {
    assert(helper.isAppleOs('TVOS'));
    assert(helper.isAppleOs('tvos'));
    assert(helper.isAppleOs('tvOS'));
    assert(helper.isAppleOs('IOS'));
    assert(helper.isAppleOs('ios'));
    assert(helper.isAppleOs('iOS'));
    assert(!helper.isAppleOs('android'));
    assert(!helper.isAppleOs('Android'));
  });
})

describe("helper setNestedValue scenario", () => {
  it("should set nested value", () => {
    const original = { test: "test" }
    const path = "a.b.c", value = "test_val";
    const expected = { test: "test", a: { b: { c: "test_val" }} }
    helper.setNestedValue(original, path, value);
    assert.deepEqual(original, expected);
  });
});

describe('stopSeleniumClockAI', function() {
  let sessionId = 'abcd-efgh-ijkl-mnop-qrst-uvwx-yz'

  beforeEach(function() {
    constants.execution_time_registry = {
      'abcd-efgh-ijkl-mnop-qrst-uvwx-yz' : {
        "requestMethod": 'GET',
         "seleniumCommandIndex": 1,
         "aiStartTime" : 1605591259450,
         "tcgStopTime": 1605591260450,
      }
    };
    constants.global_registry = {
      'abcd-efgh-ijkl-mnop-qrst-uvwx-yz' : {}
    };
  });

  afterEach(function() {
    sessionId = 'abcd-efgh-ijkl-mnop-qrst-uvwx-yz'
    delete constants.execution_time_registry[sessionId];
    delete constants.global_registry[sessionId];
    HubLogger.uploadLogPartToKafka.restore();
  });

  it('should upload performance log part to kafka if the session data exists in execution_time_registry', function() {
    const uploadLogPartToKafka = sinon.stub(HubLogger, 'uploadLogPartToKafka');

    origHelper.stopSeleniumClockAI(sessionId, `/wd/hub/session/${sessionId}/element`, 'GET', false, 1, 0, 1605591260450);
    assert(uploadLogPartToKafka.calledOnce);
  });

  it('should upload performance log part to kafka if the session data exists in execution_time_registry with W3C', function() {
    const uploadLogPartToKafka = sinon.stub(HubLogger, 'uploadLogPartToKafka');

    origHelper.stopSeleniumClockAI(sessionId, `/wd/hub/session/${sessionId}/element`, 'GET', true, 1, 0, 1605591260450);
    assert(uploadLogPartToKafka.calledOnce);
  });

  it('should log and return when seleniumCommandIndex exceeds MAX_SELENIUM_COMMANDS_INSTRUMENTATION', function() {
    const uploadLogPartToKafka = sinon.stub(HubLogger, 'uploadLogPartToKafka');
    constants.execution_time_registry[sessionId]['seleniumCommandIndex'] = constants.MAX_SELENIUM_COMMANDS_INSTRUMENTATION;

    const miscLoggerStub = sinon.stub(HubLogger, 'miscLogger');

    origHelper.stopSeleniumClockAI(sessionId, `/wd/hub/session/${sessionId}/element`, 'GET', false, 1, 0, 1605591260450);

    assert(miscLoggerStub.calledOnce);
    assert(miscLoggerStub.calledWith('stopSeleniumClockAI', `Commands for session ${sessionId} exceeded ${constants.MAX_SELENIUM_COMMANDS_INSTRUMENTATION}, ignoring kafka push`));
    assert(uploadLogPartToKafka.notCalled); // Ensure kafka push is ignored

    miscLoggerStub.restore();
  });

  it('should log an exception if an error is thrown', function() {
    const uploadLogPartToKafka = sinon.stub(HubLogger, 'uploadLogPartToKafka');
    const tempExceptionLoggerStub = sinon.stub(HubLogger, 'tempExceptionLogger');

    // Force an error by causing extractSeleniumCommandFromUrl to throw
    const extractSeleniumCommandFromUrlStub = sinon.stub(origHelper, 'extractSeleniumCommandFromUrl').throws(new Error('Test error'));

    origHelper.stopSeleniumClockAI(sessionId, `/wd/hub/session/${sessionId}/element`, 'GET', false, 1, 0, 1605591260450);

    assert(tempExceptionLoggerStub.calledOnce);
    assert(tempExceptionLoggerStub.calledWith('stopSeleniumClockAI', 'Test error'));

    extractSeleniumCommandFromUrlStub.restore();
    tempExceptionLoggerStub.restore();
  });
});

describe('getGlobalExceptionGenre', () => {
  it('should return socket_error for socket hang up', () => {
    assert.strictEqual(helper.getGlobalExceptionGenre('socket hang up'), 'socket_error');
  });
  it('should return address_in_use for EADDRINUSE', () => {
    assert.strictEqual(helper.getGlobalExceptionGenre('EADDRINUSE'), 'address_in_use');
  });
  it('should return econn_reset for ECONNRESET', () => {
    assert.strictEqual(helper.getGlobalExceptionGenre('ECONNRESET'), 'econn_reset');
  });
  it('should return epipe for EPIPE', () => {
    assert.strictEqual(helper.getGlobalExceptionGenre('EPIPE'), 'epipe');
  });
  it('should return etimedout for ETIMEDOUT', () => {
    assert.strictEqual(helper.getGlobalExceptionGenre('ETIMEDOUT'), 'etimedout');
  });
  it('should return network_tls for TLS error', () => {
    assert.strictEqual(helper.getGlobalExceptionGenre('Client network socket disconnected before secure TLS connection was established'), 'network_tls');
  });
  it('should return default for unknown error', () => {
    assert.strictEqual(helper.getGlobalExceptionGenre('something else'), 'default');
  });
});

describe('a11yIsURL', () => {
  it('should return false for undefined', () => {
    assert.strictEqual(helper.a11yIsURL(undefined), false);
  });

  it('should return false for null', () => {
    assert.strictEqual(helper.a11yIsURL(null), false);
  });

  it('should return false for short strings', () => {
    assert.strictEqual(helper.a11yIsURL('htt'), false);
    assert.strictEqual(helper.a11yIsURL('abc'), false);
  });

  it('should return false for non-http strings', () => {
    assert.strictEqual(helper.a11yIsURL('ftp://example.com'), false);
    assert.strictEqual(helper.a11yIsURL('file://test.txt'), false);
  });

  it('should return true for http URLs', () => {
    assert.strictEqual(helper.a11yIsURL('http://example.com'), true);
    assert.strictEqual(helper.a11yIsURL('https://example.com'), true);
  });
});

describe('a11yIsPresignedURL', () => {
  it('should return true for URL with query', () => {
    assert.strictEqual(helper.a11yIsPresignedURL('http://example.com?sig=123'), true);
  });
  it('should return false for URL without query', () => {
    assert.strictEqual(helper.a11yIsPresignedURL('http://example.com'), false);
  });
});
