const redactor = require('../../helpers/redactor');

describe("Testing Redactor", () => {
  context("Returns Redacted Request/Response", () => {
    it("Returns Redacted Value while fetching elements'/attributes' values", () => {
      let request = {
        url: "/wd/hub/session/6c6d295afb399a1e3d1d79ea47ae7e827eaf8e2d/element/0/attribute/value",
        method: "GET"
      }
      let response = {
        string: '{"state":"success","sessionId":"6c6d295afb399a1e3d1d79ea47ae7e827eaf8e2d","hCode":336070154,"value":"Google zoeken","class":"org.openqa.selenium.remote.Response","status":0}'
      }
      let { response: redactedResponse } = redactor.redact(request, response, ["getvalues"], {});
      redactedResponse.string.should.eql('{"state":"success","sessionId":"6c6d295afb399a1e3d1d79ea47ae7e827eaf8e2d","hCode":336070154,"value":"[REDACTED VALUE]","class":"org.openqa.selenium.remote.Response","status":0}')
    });
  });

  it("Returns Redacted request while setting values", () => {
    let request = {
      url: "/wd/hub/session/6c6d295afb399a1e3d1d79ea47ae7e827eaf8e2d/element/0/value",
      method: "POST",
      log_data: '{"text":"TO_BE_REDACTED_TEXT","value":"TO_BE_REDACTED_VALUE"}'
    }
    let response = {
      string: 'NOT REQUIRED AT THE MOMENT'
    }
    let { request: redactedRequest } = redactor.redact(request, response, ["setvalues"], {});
    redactedRequest.log_data.should.eql('{"text":"REDACTED","value":["R","E","D","A","C","T","E","D"]}');
  });
  // TO-DO Check for other commands as well (AASI-480) 
  it("Returns Redacted request while setting values for url's containing /appium/", () => {
    let request = {
      url: "/wd/hub/session/6c6d295afb399a1e3d1d79ea47ae7e827eaf8e2d/appium/element/0/value",
      method: "POST",
      log_data: '{"text":"TO_BE_REDACTED_TEXT","value":"TO_BE_REDACTED_VALUE"}'
    }
    let response = {
      string: 'NOT REQUIRED AT THE MOMENT'
    }
    let { request: redactedRequest } = redactor.redact(request, response, ["setvalues"], {});
    redactedRequest.log_data.should.eql('{"text":"REDACTED","value":["R","E","D","A","C","T","E","D"]}');
  });

  it("Returns redacted value while fetching cookies", () => {
    let request = {
      url: "/wd/hub/session/6c6d295afb399a1e3d1d79ea47ae7e827eaf8e2d/cookie",
      method: "GET"
    }
    let response = {
      string: '{"state":"success","sessionId":"6c6d295afb399a1e3d1d79ea47ae7e827eaf8e2d","hCode":336070154,"value":[{"name":"COOKIE_NAME","value":"COOKIE_VALUE","class":"org.openqa.selenium.Cookie"}],"class":"org.openqa.selenium.remote.Response","status":0}'
    }
    let { response: redactedResponse } = redactor.redact(request, response, ["getcookies"], {});
    redactedResponse.string.should.eql('{"state":"success","sessionId":"6c6d295afb399a1e3d1d79ea47ae7e827eaf8e2d","hCode":336070154,"value":[{"name":"COOKIE_NAME","value":"[REDACTED VALUE]","class":"org.openqa.selenium.Cookie"}],"class":"org.openqa.selenium.remote.Response","status":0}')
  });

  it("Returns redacted value while setting cookies", () => {
    let request = {
      url: "/wd/hub/session/6c6d295afb399a1e3d1d79ea47ae7e827eaf8e2d/cookie",
      method: "POST",
      log_data: '{"cookie":{"name":"foo","value":"bar"}}'
    }
    let response = {
      string: 'NOT REQUIRED AT THE MOMENT'
    }
    let { request: redactedRequest } = redactor.redact(request, response, ["setcookies"], {});
    redactedRequest.log_data.should.eql('{"cookie":{"name":"foo","value":"[REDACTED VALUE]"}}');
  });

  context("#redactSensitiveDataFromSessionInfo", () => {
    it('should redact keychainPassword and keychainPath if present in data values capabilities', (done) => {
      const redactedData = redactor.redactSensitiveDataFromSessionInfo(JSON.stringify({ value: { capabilities: { keychainPassword: 'random_key', keychainPath: 'some_path' } } }));

      JSON.parse(redactedData).value.capabilities.keychainPassword.should.eql('[REDACTED VALUE]');
      JSON.parse(redactedData).value.capabilities.keychainPath.should.eql('[REDACTED VALUE]');

      done();
    });

    it('should redact keychainPassword and keychainPath if present in data values', (done) => {  
      const redactedData = redactor.redactSensitiveDataFromSessionInfo(JSON.stringify({ value: { keychainPassword: 'random_key', keychainPath: 'some_path' } } ));  

      JSON.parse(redactedData).value.keychainPassword.should.eql('[REDACTED VALUE]');  
      JSON.parse(redactedData).value.keychainPath.should.eql('[REDACTED VALUE]');  

      done();  
    });

    it('should not change data if keys to be redacted are absent in it', (done) => {
      const actualData = JSON.stringify({ value: { capabilities: { some_key1: 'random_key', some_key2: 'some_path', some_key: 'some_value' } } });

      const redactedData = redactor.redactSensitiveDataFromSessionInfo(actualData);

      actualData.should.eql(redactedData);

      done();
    });

    it('should not raise error and return same data if data is not an stringified object', (done) => {
      const actualData = 'someData';

      const redactedData = redactor.redactSensitiveDataFromSessionInfo(actualData);

      actualData.should.eql(redactedData);

      done();
    });

    it('should not raise error and return same data if the capabilities object not found in it', (done) => {
      const actualData = JSON.stringify({ value: { some_key1: 'random_key', some_key2: 'some_path', some_key: 'some_value' } });
      const redactedData = redactor.redactSensitiveDataFromSessionInfo(actualData);

      actualData.should.eql(redactedData);

      done();
    });
  });

  context("#deletePlatformVersion", () => {
    it('should delete platformVersion if present in data values capabilities for w3c', (done) => {
      const expectedData = JSON.stringify({ value: { capabilities: { platformName: 'iOS' } } });
      const deletedData = redactor.deletePlatformVersion(JSON.stringify({ value: { capabilities: { platformVersion: '12', platformName: 'iOS' } } }));

      deletedData.should.eql(expectedData);

      done();
    });

    it('should not delete platformVersion if present in data values capabilities if platformName is android for w3c', (done) => {
      const expectedData = JSON.stringify({ value: { capabilities: { platformVersion: '12', platformName: 'android' } } });
      const deletedData = redactor.deletePlatformVersion(JSON.stringify({ value: { capabilities: { platformVersion: '12', platformName: 'android' } } }));

      deletedData.should.eql(expectedData);

      done();
    });

    it('should not delete platformVersion if present in data values capabilities if platformName is android for JSONWP', (done) => {
      const expectedData = JSON.stringify({ value: { platformVersion: '12', platformName: 'android' }  });
      const deletedData = redactor.deletePlatformVersion(JSON.stringify({ value: { platformVersion: '12', platformName: 'android' } }));

      deletedData.should.eql(expectedData);

      done();
    });

    it('should delete platformVersion if present in data values capabilities if platformName is ios for JSONWP', (done) => {
      const expectedData = JSON.stringify({ value: { platformName: 'iOS' }  });
      const deletedData = redactor.deletePlatformVersion(JSON.stringify({ value: { platformVersion: '12', platformName: 'iOS' } }));

      deletedData.should.eql(expectedData);

      done();
    });
  });

  context("Redacting execute script commands for w3c atoms", () => {
    it("should return redacted value on running getAttribute atom", () => {
      let request = {
        url: "/wd/hub/session/6c6d295afb399a1e3d1d79ea47ae7e827eaf8e2d/execute/sync",
        method: "POST"
      }
      let response = {
        string: '{"value": "password"}'
      }
      let { response: redactedResponse } = redactor.redact(request, response, ["getvalues"], {});
      redactedResponse.string.should.eql(response.string.replace("password", "[REDACTED VALUE]"))
    });

    it("should return non redacted value on running isDisplayed atom", () => {
      let request = {
        url: "/wd/hub/session/6c6d295afb399a1e3d1d79ea47ae7e827eaf8e2d/execute/sync",
        method: "POST"
      }
      let response = {
        string: '{"value": "true"}'
      }
      let { response: redactedResponse } = redactor.redact(request, response, ["getvalues"], {});
      redactedResponse.string.should.eql(response.string);
    });

    it("should return non redacted value on running readyState command", () => {
      let request = {
        url: "/wd/hub/session/6c6d295afb399a1e3d1d79ea47ae7e827eaf8e2d/execute/sync",
        method: "POST"
      }
      let response = {
        string: '{"value": "complete"}'
      }
      let { response: redactedResponse } = redactor.redact(request, response, ["getvalues"], {});
      redactedResponse.string.should.eql(response.string)
    });

    it("should return non redacted value if getvalues isnt inside maskable commands", () => {
      let request = {
        url: "/wd/hub/session/6c6d295afb399a1e3d1d79ea47ae7e827eaf8e2d/execute/sync",
        method: "POST"
      }
      let response = {
        string: '{"value": "true"}'
      }
      let { response: redactedResponse } = redactor.redact(request, response, ["getcookies"], {});
      redactedResponse.string.should.eql(response.string)
    });

    it("should return non redacted value if browserstack executor commands", () => {
      let request = {
        url: "/wd/hub/session/6c6d295afb399a1e3d1d79ea47ae7e827eaf8e2d/execute/sync",
        method: "POST"
      }
      let response = {
        string: '{"status": "passed"}'
      }
      let { response: redactedResponse } = redactor.redact(request, response, ["getvalues"], {isBStackExecutor: true});
      redactedResponse.string.should.eql(response.string)
    });
  });

});
