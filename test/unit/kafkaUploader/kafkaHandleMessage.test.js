'use strict';

/* eslint-disable no-underscore-dangle */
/* eslint-disable no-unused-expressions */
/* eslint-disable global-require */

const {
  describe, it, afterEach, beforeEach,
} = require('mocha');
const rewire = require('rewire');
const { assert } = require('chai');
const sinon = require('sinon');
const fs = require('fs');
const { kafkaConfig: { LOG_DATA_MESSAGE_TYPE, raw_logs_topic } } = require('../../../constants');

let kafkaUploaderProxy;
let kafkaHelperProxy;
let logger;

describe('Kafka Handle Message', () => {
  beforeEach(() => {
    process.env.CONSUMER_TOPIC = raw_logs_topic;
    process.env.CONSUMER_ID = '';
    process.env.COMMON_KAFKA_TOPIC_REGION = '';

    kafkaUploaderProxy = rewire('../../../apps/kafkaUploader/kafkaUploader');
    kafkaUploaderProxy.__set__('kafkaHelper', kafkaHelperProxy);

    logger = kafkaUploaderProxy.__get__('logger');
  });

  afterEach(() => {
    delete process.env.CONSUMER_TOPIC;
    delete process.env.CONSUMER_ID;
    delete process.env.COMMON_KAFKA_TOPIC_REGION;
  });

  describe('handle', () => {
    let handle;
    let reAssembleChunk;
    let kafkaS3Pipeline;

    let handleSpies;
    let helperSpy;
    let kafkaMessage;

    beforeEach(() => {
      reAssembleChunk = kafkaUploaderProxy.__get__('reAssembleChunk');
      kafkaS3Pipeline = kafkaUploaderProxy.__get__('kafkaS3Pipeline');
      handleSpies = {
        reAssembleSpy: sinon.spy(),
        getWriteSpy: sinon.spy(),
        kafkaS3PipelineSpy: sinon.spy(kafkaS3Pipeline, ['push']),
      };
      helperSpy = {
        getWriteFdForMessage: handleSpies.getWriteSpy,
      };

      kafkaMessage = {
        value: {
          type: '',
          requestId: 'request_123',
          rails_session_id: '123',
        },
      };

      kafkaUploaderProxy.__set__('reAssembleChunk', handleSpies.reAssembleSpy);
      kafkaUploaderProxy.__set__('kafkaHelper', helperSpy);

      sinon.stub(fs, 'writeSync', () => {});
      sinon.spy(logger, 'info');
      sinon.spy(logger, 'error');

      handle = kafkaUploaderProxy.__get__('handle');
    });

    afterEach(() => {
      kafkaUploaderProxy.__set__('kafkaS3Pipeline', kafkaS3Pipeline);
      kafkaUploaderProxy.__set__('reAssembleChunk', reAssembleChunk);
      kafkaUploaderProxy.__set__('kafkaHelper', kafkaHelperProxy);

      handleSpies.kafkaS3PipelineSpy.restore();
      logger.info.restore();
      logger.error.restore();
      fs.writeSync.restore();
    });

    it('should call reassemblechunk when chunk end message is received', () => {
      kafkaMessage.value.type = LOG_DATA_MESSAGE_TYPE.CHUNK_END;
      handle(kafkaMessage);
      assert(handleSpies.reAssembleSpy.calledOnce);
    });

    it('should write file descriptor when chunk start message is received', () => {
      kafkaMessage.value.type = LOG_DATA_MESSAGE_TYPE.CHUNK_START;
      handle(kafkaMessage);
      assert(handleSpies.getWriteSpy.calledOnce);
    });

    [
      LOG_DATA_MESSAGE_TYPE.START,
      LOG_DATA_MESSAGE_TYPE.CHUNKED_MESSAGE,
      LOG_DATA_MESSAGE_TYPE.ATOMIC_MESSAGE,
    ].forEach((keyVal) => {
      it(`should call sychronous write to file descriptor when ${keyVal} is received`, () => {
        kafkaMessage.value.type = keyVal;
        handle(kafkaMessage);
        assert(fs.writeSync.calledOnce);
        assert(handleSpies.getWriteSpy.calledOnce);
      });
    });

    it('should push to pipeline when stop is received', () => {
      kafkaMessage.value.type = LOG_DATA_MESSAGE_TYPE.STOP;
      handle(kafkaMessage);
      assert(handleSpies.kafkaS3PipelineSpy.calledOnce, 'kafkaS3Pipeline.push was not called');
      assert(logger.info.calledOnce);
    });

    it('should push to pipeline when stop is received', () => {
      fs.writeSync.restore();
      sinon.stub(fs, 'writeSync').throws(new Error('Test writeSync error'));
      kafkaMessage.value.type = LOG_DATA_MESSAGE_TYPE.ATOMIC_MESSAGE;
      handle(kafkaMessage);
      assert(logger.error.calledOnce);
    });

    it('should just log for any other type message', () => {
      kafkaMessage.value.type = 'some_random_topic';
      handle(kafkaMessage);
      assert(logger.info.calledOnce);
      Object.values(handleSpies).forEach(el => assert(el.notCalled));
    });
  });

  describe('handleMessageStop', () => {
    let handleMessageStop;
    let kafkaS3Pipeline;
    let edsPipeline;

    let handleSpies;
    let helperSpy;
    let kafkaMessage;

    beforeEach(() => {
      kafkaS3Pipeline = kafkaUploaderProxy.__get__('kafkaS3Pipeline');
      edsPipeline = kafkaUploaderProxy.__get__('edsPipeline');
      handleSpies = {
        kafkaS3PipelineSpy: sinon.spy(kafkaS3Pipeline, ['push']),
        edsPipelineSpy: sinon.spy(edsPipeline, ['push']),
      };

      kafkaMessage = {
        value: {
          type: '',
          requestId: 'request_123',
          rails_session_id: '123',
        },
      };
      kafkaUploaderProxy.__set__('kafkaHelper', helperSpy);
      sinon.spy(logger, 'error');
      handleMessageStop = kafkaUploaderProxy.__get__('handleMessageStop');
    });

    afterEach(() => {
      kafkaUploaderProxy.__set__('kafkaS3Pipeline', kafkaS3Pipeline);
      kafkaUploaderProxy.__set__('edsPipeline', edsPipeline);

      handleSpies.kafkaS3PipelineSpy.restore();
      handleSpies.edsPipelineSpy.restore();
      logger.error.restore();
    });

    it('should call push on s3Pipeline if topic is raw_logs', () => {
      handleMessageStop('raw_logs', kafkaMessage);
      assert(handleSpies.kafkaS3PipelineSpy.calledOnce);
      assert(handleSpies.edsPipelineSpy.notCalled);
    });

    it('should call push on s3Pipeline if topic is random_raw_logs', () => {
      handleMessageStop('random_raw_logs', kafkaMessage);
      assert(handleSpies.kafkaS3PipelineSpy.calledOnce);
      assert(handleSpies.edsPipelineSpy.notCalled);
    });

    it('should call push on s3Pipeline if topic is console_logs', () => {
      handleMessageStop('console_logs', kafkaMessage);
      assert(handleSpies.kafkaS3PipelineSpy.calledOnce);
      assert(handleSpies.edsPipelineSpy.notCalled);
    });

    it('should call push on s3Pipeline if topic is random_console_logs', () => {
      handleMessageStop('random_console_logs', kafkaMessage);
      assert(handleSpies.kafkaS3PipelineSpy.calledOnce);
      assert(handleSpies.edsPipelineSpy.notCalled);
    });

    it('should call push on edsPipeline if topic is performance_logs', () => {
      handleMessageStop('performance_logs', kafkaMessage);
      assert(handleSpies.edsPipelineSpy.calledOnce);
      assert(handleSpies.kafkaS3PipelineSpy.notCalled);
    });

    it('should call push on edsPipeline if topic is random_performance_logs', () => {
      handleMessageStop('random_performance_logs', kafkaMessage);
      assert(handleSpies.edsPipelineSpy.calledOnce);
      assert(handleSpies.kafkaS3PipelineSpy.notCalled);
    });

    it('should log error if invalid topic is passed', () => {
      handleMessageStop('invalid_topic', kafkaMessage);
      assert(logger.error.calledOnce);
      assert(handleSpies.edsPipelineSpy.notCalled);
      assert(handleSpies.kafkaS3PipelineSpy.notCalled);
    });
  });
});
