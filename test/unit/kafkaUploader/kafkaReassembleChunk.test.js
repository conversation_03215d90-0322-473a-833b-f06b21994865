'use strict';

/* eslint-disable no-underscore-dangle */
/* eslint-disable no-unused-expressions */
/* eslint-disable global-require */

const {
  describe, beforeEach, it, afterEach,
} = require('mocha');
const rewire = require('rewire');
const { assert } = require('chai');
const sinon = require('sinon');
const fs = require('fs');

let kafkaUploaderProxy;
let logger;

describe('kafka reassemble chunk', () => {
  beforeEach(() => {
    process.env.CONSUMER_TOPIC = '';
    process.env.CONSUMER_ID = '';
    process.env.COMMON_KAFKA_TOPIC_REGION = '';

    kafkaUploaderProxy = rewire('../../../apps/kafkaUploader/kafkaUploader');

    logger = kafkaUploaderProxy.__get__('logger');
  });

  afterEach(() => {
    delete process.env.CONSUMER_TOPIC;
    delete process.env.CONSUMER_ID;
    delete process.env.COMMON_KAFKA_TOPIC_REGION;
  });

  describe('reAssembleChunk', () => {
    let reAssembleChunk;
    let kafkaMessage;
    let fdCache;
    let fdCacheStub;
    let topic;

    let fsMethodsStub;

    beforeEach(() => {
      reAssembleChunk = kafkaUploaderProxy.__get__('reAssembleChunk');
      kafkaMessage = {
        value: {
          rails_session_id: '1234',
          requestId: 'request_1234',
          type: 5,
        },
      };
      fdCache = kafkaUploaderProxy.__get__('fdCache');

      fdCacheStub = {
        get: sinon.stub(),
        ttl: sinon.stub(),
        set: sinon.stub(),
        del: sinon.stub(),
      };

      kafkaUploaderProxy.__set__('fdCache', fdCacheStub);

      const statsSpy = { size: 10 };

      fsMethodsStub = {
        closeSync: sinon.stub(fs, 'closeSync', () => {}),
        readSync: sinon.stub(fs, 'readSync', () => {}),
        writeSync: sinon.stub(fs, 'writeSync', () => {}),
        unlink: sinon.stub(fs, 'unlink', () => {}),
        statSync: sinon.stub(fs, 'statSync', () => statsSpy),
        openSync: sinon.stub(fs, 'openSync', () => {}),
      };

      topic = kafkaUploaderProxy.__get__('topic');

      kafkaUploaderProxy.__set__('topic', 'testing_raw_logs');
      process.env.COMMON_KAFKA_TOPIC_REGION = 'us-east-1';
    });

    afterEach(() => {
      delete process.env.CONSUMER_TOPIC;
      delete process.env.COMMON_KAFKA_TOPIC_REGION;

      Object.values(fsMethodsStub).forEach(value => value.restore());

      kafkaUploaderProxy.__set__('fdCache', fdCache);
      kafkaUploaderProxy.__set__('topic', topic);
    });

    it('should unlink the chunkedFile descriptor', () => {
      sinon.spy(logger, 'error');

      const chunkedFileFd = {};
      const outFd = {};

      fdCacheStub.get
        .onCall(0).returns(chunkedFileFd)
        .onCall(1).returns(outFd);

      reAssembleChunk(kafkaMessage);

      assert(logger.error.notCalled);
      assert(fs.closeSync.calledTwice);
      assert(fs.openSync.calledOnce);
      assert(fs.readSync.calledOnce);
      assert(fs.unlink.calledOnce);

      assert(fdCacheStub.get.calledTwice);

      logger.error.restore();
    });

    it('should log error when unlink cannot be done', () => {
      sinon.spy(logger, 'error');

      fsMethodsStub.unlink.restore();

      const errObj = new Error('unable to unlink');
      fsMethodsStub.unlink = sinon.stub(fs, 'unlink').yields(errObj);

      const chunkedFileFd = {};
      const outFd = {};

      fdCacheStub.get
        .onCall(0).returns(chunkedFileFd)
        .onCall(1).returns(outFd);

      reAssembleChunk(kafkaMessage);

      assert(logger.error.calledOnce);
      assert(fs.closeSync.calledTwice);
      assert(fs.openSync.calledOnce);
      assert(fs.readSync.calledOnce);
      assert(fs.unlink.calledOnce);

      assert(fdCacheStub.get.calledTwice);

      logger.error.restore();
    });

    it('should handle stats error logging', () => {
      sinon.spy(logger, 'error');

      fsMethodsStub.statSync.restore();

      const errObj = new Error('unable to unlink');
      fsMethodsStub.statSync = sinon.stub(fs, 'statSync', () => { throw errObj; });

      const chunkedFileFd = {};
      const outFd = {};

      fdCacheStub.get
        .onCall(0).returns(chunkedFileFd)
        .onCall(1).returns(outFd);

      reAssembleChunk(kafkaMessage);

      assert(logger.error.calledOnce);
      assert(fs.closeSync.calledOnce);
      assert(fs.openSync.notCalled);
      assert(fs.readSync.notCalled);
      assert(fs.unlink.notCalled);

      assert(fdCacheStub.get.calledTwice);

      logger.error.restore();
    });
  });
});
