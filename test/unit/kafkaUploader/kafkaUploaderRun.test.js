'use strict';

/* eslint-disable no-underscore-dangle */
/* eslint-disable no-unused-expressions */
/* eslint-disable global-require */

const {
  describe, it, afterEach, beforeEach,
} = require('mocha');
const rewire = require('rewire');
const { assert, expect } = require('chai');
const sinon = require('sinon');
const { Chitragupta } = require('chitragupta');
const helper = require('../../../helper');

let kafkaUploaderRunProxy;
let logger;
let chitraguptaStub;

describe('Kafka uploader run', () => {
  beforeEach(() => {
    process.env.CONSUMER_TOPIC = 'abcdef';
    process.env.CONSUMER_ID = 'abcd';
    chitraguptaStub = sinon.stub(Chitragupta, 'setupProcessLogger', () => { });

    kafkaUploaderRunProxy = rewire('../../../apps/kafkaUploader/kafkaUploaderRun');
    logger = kafkaUploaderRunProxy.__get__('logger');
  });

  afterEach(() => {
    delete process.env.CONSUMER_TOPIC;
    delete process.env.CONSUMER_ID;
    chitraguptaStub.restore();
  });


  describe('#stop', () => {
    let stop;
    const intervals = {
      firstInterval: setInterval(() => {}, 10000),
    };

    beforeEach(() => {
      stop = kafkaUploaderRunProxy.__get__('stop');
      kafkaUploaderRunProxy.__set__('intervals', intervals);
      sinon.spy(logger, 'info');
      sinon.spy(global, 'clearInterval');
    });

    afterEach(() => {
      logger.info.restore();
      global.clearInterval.restore();
    });

    it('should clear intervals', () => {
      stop();

      assert(logger.info.calledOnce);
      expect(clearInterval).to.have.been.calledOnce;
    });
  });

  describe('#acknowledgeAsAlive', () => {
    let acknowledgeAsAlive;
    let hmsetStub;
    beforeEach(() => {
      acknowledgeAsAlive = kafkaUploaderRunProxy.__get__('acknowledgeAsAlive');
      hmsetStub = sinon.stub(helper.redisClient, 'hset');
    });

    afterEach(() => {
      hmsetStub.restore();
    });

    it('should call redis', () => {
      acknowledgeAsAlive();

      expect(helper.redisClient).to.have.been.calledOnce;
    });
  });

  describe('#exit', () => {
    let exit;
    let giveUpAsMaster;
    const intervals = {};

    beforeEach(() => {
      giveUpAsMaster = kafkaUploaderRunProxy.__get__('giveUpAsMaster');
      exit = kafkaUploaderRunProxy.__get__('exit');
      kafkaUploaderRunProxy.__set__('intervals', intervals);
    });

    afterEach(() => {
      kafkaUploaderRunProxy.__set__('giveUpAsMaster', giveUpAsMaster);
    });

    it('should handle exit', async () => {
      const exitHelperObject = {
        giveUpAsMaster: kafkaUploaderRunProxy.__get__('giveUpAsMaster'),
      };
      const giveUpAsMasterSpy = sinon.spy(exitHelperObject, 'giveUpAsMaster');
      kafkaUploaderRunProxy.__set__('giveUpAsMaster', giveUpAsMasterSpy);

      await exit();

      assert(giveUpAsMasterSpy.calledOnce);
      assert(process.exit.calledWith(0));

      giveUpAsMasterSpy.restore(0);
    });
  });

  describe('#onBecomeMaster', () => {
    let onBecomeMaster;
    let stillMaster;
    let kafkaUploader;

    beforeEach(() => {
      kafkaUploaderRunProxy.__set__('wasIaSlaveEarlier', true);
      onBecomeMaster = kafkaUploaderRunProxy.__get__('onBecomeMaster');
      stillMaster = kafkaUploaderRunProxy.__get__('stillMaster');
      kafkaUploader = kafkaUploaderRunProxy.__get__('kafkaUploader');
    });

    afterEach(() => {
      kafkaUploaderRunProxy.__set__('wasIaSlaveEarlier', false);
      kafkaUploaderRunProxy.__set__('stillMaster', stillMaster);
      kafkaUploaderRunProxy.__set__('kafkaUploader', kafkaUploader);
    });

    it('should start kafka uploader', () => {
      const kafkaUploaderObject = {
        startKafkaUploader: () => true,
      };
      const kafkaUploaderSpy = sinon.spy(kafkaUploaderObject, 'startKafkaUploader');
      kafkaUploaderRunProxy.__set__('kafkaUploader', kafkaUploaderObject);

      onBecomeMaster();

      assert(kafkaUploaderSpy.calledOnce);

      kafkaUploaderSpy.restore();
    });
  });

  describe('stillMaster', () => {
    let stillMaster;

    beforeEach(() => {
      stillMaster = kafkaUploaderRunProxy.__get__('stillMaster');
    });

    afterEach(() => {
      kafkaUploaderRunProxy.__set__('stillMaster', stillMaster);
    });

    it('should return true if pid is same', async () => {
      const originalPid = process.pid;
      Object.defineProperty(process, 'pid', {
        value: 2,
      });
      const redisStub = sinon.stub(helper.redisClient, 'hget').returns(Promise.resolve('2'));
      expect(await stillMaster()).to.equal(true);
      redisStub.restore();
      Object.defineProperty(process, 'pid', {
        value: originalPid,
      });
    });

    it('should return false if pid is not same', async () => {
      const redisStub = sinon.stub(helper.redisClient, 'hget').returns(Promise.resolve('2'));
      expect(await stillMaster()).to.equal(false);
      redisStub.restore();
    });

    it('should return false if redis issue', async () => {
      const redisStub = sinon.stub(helper.redisClient, 'hget').returns(Promise.reject(new Error('error')));
      expect(await stillMaster()).to.equal(false);
      redisStub.restore();
    });
  });

  describe('acknowledgeAsAlive', () => {
    let acknowledgeAsAlive;

    beforeEach(() => {
      acknowledgeAsAlive = kafkaUploaderRunProxy.__get__('acknowledgeAsAlive');
    });

    afterEach(() => {
      kafkaUploaderRunProxy.__set__('acknowledgeAsAlive', acknowledgeAsAlive);
    });

    it('set redis key', async () => {
      const redisStub = sinon.stub(helper.redisClient, 'hget').returns(Promise.resolve('2'));
      await acknowledgeAsAlive();
      expect(redisStub).to.have.been.calledOnce;
      redisStub.restore();
    });
  });

  describe('tryToBecomeMaster', () => {
    let tryToBecomeMaster;

    beforeEach(() => {
      tryToBecomeMaster = kafkaUploaderRunProxy.__get__('tryToBecomeMaster');
    });

    afterEach(() => {
      kafkaUploaderRunProxy.__set__('tryToBecomeMaster', tryToBecomeMaster);
    });

    it('can become master', async () => {
      const redisStub = sinon.stub(helper.redisClient, 'becomeMaster').yields(null, 'I can become master. Current master pid');
      await tryToBecomeMaster();
      expect(redisStub).to.have.been.calledOnce;
      redisStub.restore();
    });

    it('can\'t become master', async () => {
      const redisStub = sinon.stub(helper.redisClient, 'becomeMaster').yields('error', '');
      await tryToBecomeMaster();
      expect(redisStub).to.have.been.calledOnce;
      redisStub.restore();
    });
  });


  describe('giveUpAsMaster', () => {
    let giveUpAsMaster;

    beforeEach(() => {
      giveUpAsMaster = kafkaUploaderRunProxy.__get__('giveUpAsMaster');
    });

    afterEach(() => {
      kafkaUploaderRunProxy.__set__('giveUpAsMaster', giveUpAsMaster);
    });

    it('should return true in case able to unset', async () => {
      const redisStub = sinon.stub(helper.redisClient, 'unsetMaster').yields(null, true);
      expect(await giveUpAsMaster()).to.equal(true);
      expect(redisStub).to.have.been.calledOnce;
      redisStub.restore();
    });

    it('should return false in case able to unset and log error', async () => {
      const redisStub = sinon.stub(helper.redisClient, 'unsetMaster').yields('there was an error', false);
      expect(await giveUpAsMaster()).to.equal(false);
      expect(redisStub).to.have.been.calledOnce;
      redisStub.restore();
    });
  });

  describe('becomeMasterCheck', () => {
    let becomeMasterCheck;

    beforeEach(() => {
      becomeMasterCheck = kafkaUploaderRunProxy.__get__('becomeMasterCheck');
    });

    afterEach(() => {
      kafkaUploaderRunProxy.__set__('becomeMasterCheck', becomeMasterCheck);
    });

    it('can become master', async () => {
      const masterCheckHelperObject = {
        tryToBecomeMaster: kafkaUploaderRunProxy.__get__('tryToBecomeMaster'),
        onBecomeMaster: kafkaUploaderRunProxy.__get__('onBecomeMaster'),
      };
      const tryToBecomeMasterSpy = sinon.stub(masterCheckHelperObject, 'tryToBecomeMaster').returns(true);
      const onBecomeMasterSpy = sinon.stub(masterCheckHelperObject, 'onBecomeMaster');
      kafkaUploaderRunProxy.__set__('tryToBecomeMaster', tryToBecomeMasterSpy);
      kafkaUploaderRunProxy.__set__('onBecomeMaster', onBecomeMasterSpy);
      await becomeMasterCheck();
      tryToBecomeMasterSpy.restore();
      onBecomeMasterSpy.restore();
      expect(onBecomeMasterSpy).to.have.been.calledOnce;
    });

    it('can\'t become master', async () => {
      const masterCheckHelperObject = {
        tryToBecomeMaster: kafkaUploaderRunProxy.__get__('tryToBecomeMaster'),
        onBecomeMaster: kafkaUploaderRunProxy.__get__('onBecomeMaster'),
      };
      const tryToBecomeMasterSpy = sinon.stub(masterCheckHelperObject, 'tryToBecomeMaster').returns(false);
      const onBecomeMasterSpy = sinon.stub(masterCheckHelperObject, 'onBecomeMaster');
      kafkaUploaderRunProxy.__set__('tryToBecomeMaster', tryToBecomeMasterSpy);
      kafkaUploaderRunProxy.__set__('onBecomeMaster', onBecomeMasterSpy);
      await becomeMasterCheck();
      tryToBecomeMasterSpy.restore();
      onBecomeMasterSpy.restore();
      expect(onBecomeMasterSpy).not.to.have.been.calledOnce;
    });
  });

  describe('start', () => {
    let start;

    beforeEach(() => {
      start = kafkaUploaderRunProxy.__get__('start');
    });

    afterEach(() => {
      kafkaUploaderRunProxy.__set__('start', start);
    });

    it('should start becomemaster check', async () => {
      const preStartChecks = {
        capableToStartUploader: sinon.stub().returns(true),
      };
      const masterCheckHelperObject = {
        becomeMasterCheck: kafkaUploaderRunProxy.__get__('becomeMasterCheck'),
      };
      const origcapableToStartUploader = kafkaUploaderRunProxy.__get__('preStartChecks');
      const becomeMasterCheckSpy = sinon.stub(masterCheckHelperObject, 'becomeMasterCheck');
      kafkaUploaderRunProxy.__set__('becomeMasterCheck', becomeMasterCheckSpy);
      kafkaUploaderRunProxy.__set__('preStartChecks', preStartChecks);
      await start();
      becomeMasterCheckSpy.restore();
      kafkaUploaderRunProxy.__set__('preStartChecks', origcapableToStartUploader);
      expect(becomeMasterCheckSpy).to.have.been.calledOnce;
      expect(preStartChecks.capableToStartUploader).to.have.been.calledOnce;
    });
  });
});
