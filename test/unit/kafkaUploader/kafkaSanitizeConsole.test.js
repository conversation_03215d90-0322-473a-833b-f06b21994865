'use strict';

/* eslint-disable no-underscore-dangle */
/* eslint-disable no-unused-expressions */
/* eslint-disable global-require */

const {
  describe, beforeEach, it, afterEach,
} = require('mocha');
const rewire = require('rewire');
const { assert } = require('chai');
const sinon = require('sinon');
const fs = require('fs');

let kafkaS3pipeline;

describe('kafka sanitize console logs', () => {
  beforeEach(() => {
    process.env.CONSUMER_TOPIC = '';
    process.env.CONSUMER_ID = '';
    process.env.COMMON_KAFKA_TOPIC_REGION = '';

    kafkaS3pipeline = rewire('../../../apps/kafkaUploader/kafkaS3pipeline');
  });

  afterEach(() => {
    delete process.env.CONSUMER_TOPIC;
    delete process.env.CONSUMER_ID;
    delete process.env.COMMON_KAFKA_TOPIC_REGION;
  });

  describe('sanitizeConsoleLogs', () => {
    let sanitizeConsoleLogs;
    let filename;
    let sessionId;

    beforeEach(() => {
      sanitizeConsoleLogs = kafkaS3pipeline.__get__('sanitizeConsoleLogs');

      filename = 'abcd_logs.txt';
      sessionId = '1234';
    });

    it('should resolve when there are no errors', async () => {
      sinon.stub(fs, 'stat').yields(null, { size: 12 });
      sinon.stub(fs, 'writeFile', () => {});

      await sanitizeConsoleLogs(filename, sessionId);

      assert(fs.writeFile.notCalled);

      fs.stat.restore();
      fs.writeFile.restore();
    });

    it('should reject when error in stats', async () => {
      sinon.stub(fs, 'stat').yields('some error in stat', null);

      try {
        await sanitizeConsoleLogs(filename, sessionId);
      } catch (err) {
        assert(err === 'some error in stat');
      }

      fs.stat.restore();
    });

    it('should write 0 byte file size', async () => {
      sinon.stub(fs, 'stat').yields(null, { size: 0 });
      sinon.stub(fs, 'writeFile').yields(Promise.resolve());

      await sanitizeConsoleLogs(filename, sessionId);

      assert(fs.writeFile.calledOnce);

      fs.stat.restore();
      fs.writeFile.restore();
    });
  });
});
