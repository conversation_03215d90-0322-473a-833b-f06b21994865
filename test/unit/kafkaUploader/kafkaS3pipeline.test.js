'use strict';

/* eslint-disable no-underscore-dangle */
/* eslint-disable no-unused-expressions */
/* eslint-disable global-require */

const {
  describe, beforeEach, afterEach, it,
} = require('mocha');
const rewire = require('rewire');
const { assert } = require('chai');
const sinon = require('sinon');
const fsExtra = require('fs-extra');

let kafkaS3pipelineProxy;
let logger;

describe('kafka s3 pipeline', () => {
  beforeEach(() => {
    process.env.CONSUMER_TOPIC = '';
    process.env.CONSUMER_ID = '';
    process.env.COMMON_KAFKA_TOPIC_REGION = '';

    kafkaS3pipelineProxy = rewire('../../../apps/kafkaUploader/kafkaS3pipeline');

    logger = kafkaS3pipelineProxy.__get__('logger');
  });

  afterEach(() => {
    delete process.env.CONSUMER_TOPIC;
    delete process.env.CONSUMER_ID;
    delete process.env.COMMON_KAFKA_TOPIC_REGION;
  });


  describe('saveErrorUploadLogs', () => {
    let saveErrorUploadLogs;
    let sourceDir;
    let targetDir;

    beforeEach(() => {
      saveErrorUploadLogs = kafkaS3pipelineProxy.__get__('saveErrorUploadLogs');
      sourceDir = 'sourceDir/';
      targetDir = 'targetDir/';
    });

    it('should work when directory is present is present', async () => {
      sinon.stub(fsExtra, 'move').returns(Promise.resolve());
      sinon.spy(logger, 'error');

      await saveErrorUploadLogs(sourceDir, targetDir);

      assert(fsExtra.move.calledOnce);
      assert(logger.error.notCalled);

      fsExtra.move.restore();
      logger.error.restore();
    });

    it('should log error when directory not able to delete', async () => {
      sinon.stub(fsExtra, 'move').returns(Promise.reject());
      sinon.spy(logger, 'error');

      await saveErrorUploadLogs(sourceDir, targetDir);

      assert(fsExtra.move.calledOnce);
      assert(logger.error.calledOnce);

      fsExtra.move.restore();
      logger.error.restore();
    });
  });
});
