'use strict';

/* eslint-disable no-underscore-dangle */
/* eslint-disable no-unused-expressions */
/* eslint-disable global-require */

const {
  describe, it, afterEach, beforeEach,
} = require('mocha');
const rewire = require('rewire');
const { assert, expect } = require('chai');
const sinon = require('sinon');
const fs = require('fs');
const fsExtra = require('fs-extra');
const AWS = require('aws-sdk');

let kafkaS3pipelineProxy;
let logger;
let clock;

describe('Kafka uploadToS3', () => {
  beforeEach(() => {
    process.env.CONSUMER_TOPIC = '';
    process.env.CONSUMER_ID = '';
    process.env.COMMON_KAFKA_TOPIC_REGION = '';

    clock = sinon.useFakeTimers();
    kafkaS3pipelineProxy = rewire('../../../apps/kafkaUploader/kafkaS3pipeline');
    logger = kafkaS3pipelineProxy.__get__('logger');
  });

  afterEach(() => {
    clock.restore();

    delete process.env.CONSUMER_TOPIC;
    delete process.env.CONSUMER_ID;
    delete process.env.COMMON_KAFKA_TOPIC_REGION;
  });

  describe('reUploadLogs', () => {
    let reUploadLogs;
    let sessionId1;
    let sessionId2;
    let dirname1;
    let dirname2;
    let filename1;
    let filename2;
    let filename3;
    let filename4;

    let batchFiles;
    let kafkaHelper;

    let batchFilesStub;

    beforeEach(() => {
      reUploadLogs = kafkaS3pipelineProxy.__get__('reUploadLogs');

      sessionId1 = '1234';
      sessionId2 = '5678';
      dirname1 = `/a/b/c/${sessionId1}`;
      dirname2 = `/a/b/c/${sessionId2}`;
      filename1 = `${dirname1}/${sessionId1}-logs-v2.txt`;
      filename2 = `${dirname2}/${sessionId2}-console-v2.txt`;
      filename3 = `${dirname2}/${sessionId2}-exception-v2.txt`;
      filename4 = `${dirname2}/${sessionId2}-performance-v2.txt`;

      sinon.spy(logger, 'info');
      sinon.spy(logger, 'error');

      batchFiles = kafkaS3pipelineProxy.__get__('batchFiles');

      batchFilesStub = sinon.stub();
      kafkaS3pipelineProxy.__set__('batchFiles', batchFilesStub);

      kafkaHelper = kafkaS3pipelineProxy.__get__('kafkaHelper');
    });

    afterEach(() => {
      kafkaS3pipelineProxy.__set__('batchFiles', batchFiles);
      kafkaS3pipelineProxy.__set__('kafkaHelper', kafkaHelper);

      logger.info.restore();
      logger.error.restore();
    });

    it('should not upload if logs path is invalid', async () => {
      batchFilesStub.withArgs('/invalid/path').throws(new Error('ENOENT: no such file or directory'));

      await reUploadLogs('/invalid/path');

      assert(batchFilesStub.withArgs('/invalid/path').calledOnce);

      assert(logger.error.calledOnce);
    });

    it('should not upload if logs path is empty', async () => {
      batchFilesStub.withArgs('/empty/path').returns([]);

      await reUploadLogs('/empty/path');

      assert(batchFilesStub.withArgs('/empty/path').calledOnce);
    });

    it('should remove log file on successful upload', async () => {
      batchFilesStub.withArgs('/logs/path').returns([dirname1, dirname2]);
      batchFilesStub.withArgs(dirname1).returns([filename1]);
      batchFilesStub.withArgs(dirname2).returns([filename2]);

      const sendToS3Stub = sinon.stub();
      sendToS3Stub.yields(null, { data: 'some' });

      const s3UploadStub = sinon.stub(AWS.S3.prototype, 'upload', () => ({
        send: sendToS3Stub,
      }));

      const fsExtraRemoveSyncStub = sinon.stub(fsExtra, 'removeSync');
      const fsRmDirSyncStub = sinon.stub(fs, 'rmdirSync');

      await reUploadLogs('/logs/path');

      assert(batchFilesStub.withArgs('/logs/path').calledOnce);
      assert(batchFilesStub.withArgs(dirname1).calledOnce);
      assert(batchFilesStub.withArgs(dirname2).calledOnce);

      expect(logger.info.callCount).to.equal(7);
      assert(logger.error.notCalled);
      assert(fsExtraRemoveSyncStub.calledTwice);
      assert(fsRmDirSyncStub.calledTwice);

      s3UploadStub.restore();
      fsExtra.removeSync.restore();
      fs.rmdirSync.restore();
    });

    it('should log error on upload error', async () => {
      batchFilesStub.withArgs('/logs/path').returns([dirname1]);
      batchFilesStub.withArgs(dirname1).returns([filename1]);

      const sendToS3Stub = sinon.stub();
      sendToS3Stub.yields(new Error('Upload error'), null);

      const s3UploadStub = sinon.stub(AWS.S3.prototype, 'upload', () => ({
        send: sendToS3Stub,
      }));

      await reUploadLogs('/logs/path');

      assert(batchFilesStub.withArgs('/logs/path').calledOnce);
      assert(batchFilesStub.withArgs(dirname1).calledOnce);

      assert(logger.info.calledTwice);
      assert(logger.error.calledOnce);

      s3UploadStub.restore();
    });

    it('should abort the upload if timeout is reached', async () => {
      batchFilesStub.withArgs('/logs/path').returns([dirname1, dirname2]);
      batchFilesStub.withArgs(dirname1).returns([filename3]);
      batchFilesStub.withArgs(dirname2).returns([filename4]);

      const sendToS3Stub = sinon.stub();
      const abortStub = sinon.spy();

      const s3UploadStub = sinon.stub(AWS.S3.prototype, 'upload', () => ({
        send: sendToS3Stub,
        abort: abortStub,
      }));

      await reUploadLogs('/logs/path');

      assert(batchFilesStub.withArgs('/logs/path').calledOnce);
      assert(batchFilesStub.withArgs(dirname1).calledOnce);
      assert(batchFilesStub.withArgs(dirname2).calledOnce);

      assert(logger.info.calledThrice);

      clock.tick(60000);

      assert(logger.error.calledTwice);

      s3UploadStub.restore();
    });
  });
});
