'use strict';

/* eslint-disable no-underscore-dangle */
/* eslint-disable no-unused-expressions */
/* eslint-disable global-require */

const {
  describe, it, afterEach, beforeEach,
} = require('mocha');
const rewire = require('rewire');
const { assert } = require('chai');
const sinon = require('sinon');
const fs = require('fs');
const fsExtra = require('fs-extra');
const util = require('util');

const origKafkaHelper = require('../../../apps/kafkaUploader/kafkaHelper');

let kafkaS3pipelineProxy;
let logger;
let helperSpy;
let hoothootStats;

describe('kafka handle stop', () => {
  beforeEach(() => {
    process.env.CONSUMER_TOPIC = '';
    process.env.CONSUMER_ID = '';
    process.env.COMMON_KAFKA_TOPIC_REGION = '';

    kafkaS3pipelineProxy = rewire('../../../apps/kafkaUploader/kafkaS3pipeline');

    logger = kafkaS3pipelineProxy.__get__('logger');

    hoothootStats = {
      uploadError: 0,
      uploadSuccessfull: {},
      uploadTime: {},
      uploadSize: {},
    };
  });

  afterEach(() => {
    delete process.env.CONSUMER_TOPIC;
    delete process.env.CONSUMER_ID;
    delete process.env.COMMON_KAFKA_TOPIC_REGION;
  });

  describe('handleStop', () => {
    let handleStop;
    let fsStubs;
    let fdCache;
    let uploadToS3;
    let uploadToS3Async;
    let saveErrorUploadLogs;
    let topic;
    let kafkaMessage;
    let uploadSpy;
    let uploadSpyAsync;
    let saveErrorSpy;
    let sanitizeConsoleLogsSpy;

    beforeEach(() => {
      handleStop = kafkaS3pipelineProxy.__get__('handleStop');
      topic = kafkaS3pipelineProxy.__get__('topic');

      fsStubs = {
        closeSync: sinon.stub(fs, 'closeSync', () => {}),
        writeFileSync: sinon.stub(fs, 'writeFileSync', () => {}),
        stat: sinon.stub(fs, 'stat'),
        writeFile: sinon.stub(fs, 'writeFile', () => {}),
        existsSync: sinon.stub(fs, 'existsSync', () => {}),
        remove: sinon.stub(fsExtra, 'remove', () => {}),
      };

      kafkaMessage = {
        value: {
          type: 5,
          requestId: 'request_123',
          rails_session_id: '123',
          message: 'This is the kafka message',
          appTesting: false,
        },
      };

      fdCache = {
        del: sinon.stub(),
        get: sinon.stub(),
        ttl: sinon.stub(),
        set: sinon.stub(),
      };
      uploadToS3 = kafkaS3pipelineProxy.__get__('uploadToS3');
      uploadToS3Async = kafkaS3pipelineProxy.__get__('uploadToS3Async');
      saveErrorUploadLogs = kafkaS3pipelineProxy.__get__('saveErrorUploadLogs');

      helperSpy = {
        fsWriteFileAsync: sinon.spy(),
        fsMkdirAsync: sinon.spy(),
      };
      Object.assign(origKafkaHelper, helperSpy);
      kafkaS3pipelineProxy.__set__('kafkaHelper', origKafkaHelper);

      uploadSpy = sinon.stub();
      uploadSpyAsync = util.promisify(uploadSpy);
      saveErrorSpy = sinon.stub();
      sanitizeConsoleLogsSpy = sinon.spy();

      kafkaS3pipelineProxy.__set__('topic', 'testing_raw_logs');
      kafkaS3pipelineProxy.__set__('uploadToS3', uploadSpy);
      kafkaS3pipelineProxy.__set__('uploadToS3Async', uploadSpyAsync);

      kafkaS3pipelineProxy.__set__('saveErrorUploadLogs', saveErrorSpy);
      kafkaS3pipelineProxy.__set__('sanitizeConsoleLogs', sanitizeConsoleLogsSpy);


      sinon.spy(logger, 'info');
      sinon.spy(logger, 'error');
    });

    afterEach(() => {
      kafkaS3pipelineProxy.__set__('topic', topic);
      Object.values(fsStubs).forEach(el => el.restore());

      kafkaS3pipelineProxy.__set__('uploadToS3', uploadToS3);
      kafkaS3pipelineProxy.__set__('uploadToS3Async', uploadToS3Async);
      kafkaS3pipelineProxy.__set__('saveErrorUploadLogs', saveErrorUploadLogs);

      logger.info.restore();
      logger.error.restore();
    });

    it('should upload logs to s3', async () => {
      const stopFd = {};

      fdCache.get.onCall(0).returns(stopFd);
      fsStubs.existsSync.restore();

      fsStubs.existsSync = sinon.stub(fs, 'existsSync', () => true);
      fsStubs.stat.yields(null, { size: 12 });

      uploadSpy.yields(null);

      const options = {
        kafkaMessage,
        fdCache,
        hoothootStats,
      };
      await handleStop(options);

      assert(helperSpy.fsWriteFileAsync.calledOnce);
      assert(logger.error.notCalled);
      assert(uploadSpy.calledOnce);
    });

    it('should call sanitize console logs if topic is for it', async () => {
      const stopFd = {};

      fdCache.get.onCall(0).returns(stopFd);
      fsStubs.existsSync.restore();

      fsStubs.existsSync = sinon.stub(fs, 'existsSync', () => true);
      fsStubs.stat.yields(null, { size: 12 });

      uploadSpy.yields(null);
      kafkaS3pipelineProxy.__set__('sanitizeConsoleLogs', sanitizeConsoleLogsSpy);

      kafkaS3pipelineProxy.__set__('topic', 'console_logs');

      const options = {
        kafkaMessage,
        fdCache,
        hoothootStats,
      };
      await handleStop(options);

      assert(sanitizeConsoleLogsSpy.calledOnce);
      assert(helperSpy.fsWriteFileAsync.calledOnce);
      assert(logger.error.notCalled);
      assert(uploadSpy.calledOnce);
    });

    it('should still upload after sanitizeConsoleLogs crashes', async () => {
      const stopFd = {};

      fdCache.get.onCall(0).returns(stopFd);
      fsStubs.existsSync.restore();

      fsStubs.existsSync = sinon.stub(fs, 'existsSync', () => true);
      fsStubs.stat.yields(null, { size: 12 });

      uploadSpy.yields(null);

      function errFn() { throw new Error('err'); }
      sanitizeConsoleLogsSpy = sinon.spy(errFn);

      kafkaS3pipelineProxy.__set__('sanitizeConsoleLogs', sanitizeConsoleLogsSpy);

      kafkaS3pipelineProxy.__set__('topic', 'console_logs');

      const options = {
        kafkaMessage,
        fdCache,
        hoothootStats,
      };
      await handleStop(options);

      assert(sanitizeConsoleLogsSpy.calledOnce);
      assert(helperSpy.fsWriteFileAsync.calledOnce);
      assert(logger.error.notCalled);
      assert(uploadSpy.calledOnce);
    });

    it('should log error in uploadToS3 and add to errorUploadsLogs', async () => {
      const stopFd = {};

      fdCache.get.onCall(0).returns(stopFd);
      fsStubs.existsSync.restore();

      fsStubs.existsSync = sinon.stub(fs, 'existsSync', () => true);
      fsStubs.stat.yields(null, { size: 12 });

      uploadSpy.yields('Error while uploading');

      const options = {
        kafkaMessage,
        fdCache,
        hoothootStats,
      };
      await handleStop(options);

      assert(helperSpy.fsWriteFileAsync.calledOnce);
      assert(logger.error.calledOnce);
      assert(uploadSpy.calledOnce);
    });
  });
});
