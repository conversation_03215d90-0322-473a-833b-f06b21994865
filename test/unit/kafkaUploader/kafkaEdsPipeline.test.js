'use strict';

/* eslint-disable no-underscore-dangle */
/* eslint-disable no-unused-expressions */
/* eslint-disable global-require */

const {
  describe, beforeEach, afterEach, it,
} = require('mocha');
const rewire = require('rewire');
const { assert } = require('chai');
const sinon = require('sinon');
const fsExtra = require('fs-extra');
const fs = require('fs');
const Readable = require('stream').Readable;

const origKafkaHelper = require('../../../apps/kafkaUploader/kafkaHelper');

let kafkaEdsPipelineProxy;
let logger;
let helperSpy;
let hoothootStats;
let fsStubs;
let bufferedStub;

describe('kafkaEdsPipeline', () => {
  let originalFSStats;

  beforeEach(() => {
    process.env.CONSUMER_TOPIC = '';
    process.env.CONSUMER_ID = '';
    process.env.COMMON_KAFKA_TOPIC_REGION = '';

    kafkaEdsPipelineProxy = rewire('../../../apps/kafkaUploader/kafkaEdsPipeline');
    logger = kafkaEdsPipelineProxy.__get__('logger');

    fsStubs = {
      closeSync: sinon.stub(fs, 'closeSync', () => {}),
      close: sinon.stub(fs, 'close', () => {}),
      writeFileSync: sinon.stub(fs, 'writeFileSync', () => {}),
      writeFile: sinon.stub(fs, 'writeFile', () => {}),
      existsSync: sinon.stub(fs, 'existsSync', () => {}),
      remove: sinon.stub(fsExtra, 'remove', () => {}),
      stat: sinon.stub(fs, 'stat'),
    };

    originalFSStats = kafkaEdsPipelineProxy.__get__('fsStatAsync');
    kafkaEdsPipelineProxy.__set__('fsStatAsync', fsStubs.stat);

    bufferedStub = sinon.stub(fs, 'createReadStream');
    hoothootStats = {
      uploadError: 0,
      uploadSuccessfull: {},
      uploadTime: {},
      uploadSize: {},
    };
  });
  afterEach(() => {
    delete process.env.CONSUMER_TOPIC;
    delete process.env.CONSUMER_ID;
    delete process.env.COMMON_KAFKA_TOPIC_REGION;

    kafkaEdsPipelineProxy.__set__('fsStatAsync', originalFSStats);
    Object.values(fsStubs).forEach(el => el.restore());
    bufferedStub.restore();
  });

  describe('#getPerformanceStatsJson', () => {
    let getPerformanceStatsJson;
    beforeEach(() => {
      getPerformanceStatsJson = kafkaEdsPipelineProxy.__get__('getPerformanceStatsJson');
    });

    it('reads line from bufferedStream and generates json when the file exists', () => {
      const smallBufferedStream = new Readable();
      smallBufferedStream.push('node-auto-6:POST:url:30:0');
      smallBufferedStream.push(null);
      bufferedStub.returns(smallBufferedStream);
      const expectedJson = { 'node-auto-6': { 'POST:url': { d: ['30'], s: ['0'] } } };
      getPerformanceStatsJson('/', false).then((data) => {
        assert.deepEqual(data, expectedJson);
      });
    });

    it('reads multiple lines from bufferedStream and generates json when the file exists', () => {
      const smallBufferedStream = new Readable();
      smallBufferedStream.push('node-auto-6:POST:url:30:0\n');
      smallBufferedStream.push('node-auto-7:DELETE::40:0\n');
      smallBufferedStream.push(null);
      bufferedStub.returns(smallBufferedStream);
      fsStubs.stat.yields(null, '');
      const expectedJson = { 'node-auto-6': { 'POST:url': { d: ['30'], s: ['0'] } }, 'node-auto-7': { 'DELETE:': { d: ['40'], s: ['0'] } } };
      getPerformanceStatsJson('/', false).then((data) => {
        assert.deepEqual(data, expectedJson);
      });
    });

    it('reads multiple lines from bufferedStream and generates json when the file exists for app_automate', () => {
      const smallBufferedStream = new Readable();
      smallBufferedStream.push('node-auto-6:POST:url:30:0:\n');
      smallBufferedStream.push('node-auto-7:DELETE::40:0:\n');
      smallBufferedStream.push('node-auto-6:POST:element:40:7:xpath\n');
      smallBufferedStream.push('node-auto-6:POST:element:20:0:id\n');
      smallBufferedStream.push(null);
      bufferedStub.returns(smallBufferedStream);
      fsStubs.stat.yields(null, '');
      const expectedJson = { 'node-auto-6': { 'POST:url': { d: ['30'], s: ['0'], m: [''] }, 'POST:element': { d: ['40', '20'], s: ['7', '0'], m: ['xpath', 'id'] } }, 'node-auto-7': { 'DELETE:': { d: ['40'], s: ['0'], m: [''] } } };
      getPerformanceStatsJson('/', true).then((data) => {
        assert.deepEqual(data, expectedJson);
      });
    });

    it('reads multiple lines from bufferedStream and generates json when the file exists for automate', () => {
      const smallBufferedStream = new Readable();
      smallBufferedStream.push('node-auto-6:POST:url:30:0\n');
      smallBufferedStream.push('node-auto-7:DELETE::40:0\n');
      smallBufferedStream.push('node-auto-6:POST:element:40:7\n');
      smallBufferedStream.push('node-auto-6:POST:element:20:0\n');
      smallBufferedStream.push('node-auto-6:POST:element_ai:20:0:1\n');
      smallBufferedStream.push(null);
      bufferedStub.returns(smallBufferedStream);
      fsStubs.stat.yields(null, '');
      const expectedJson = {
        'node-auto-6': {
          'POST:url': { d: ['30'], s: ['0'] },
          'POST:element': { d: ['40', '20'], s: ['7', '0'] },
          'POST:element_ai': {
            d: ['20'], s: ['0'], ar: ['1'],
          },
        },
        'node-auto-7': { 'DELETE:': { d: ['40'], s: ['0'] } },
      };
      getPerformanceStatsJson('/', false).then((data) => {
        assert.deepEqual(data, expectedJson);
      });
    });

    it('throws error if file not available', () => {
      fsStubs.stat.yields(new Error('File Not Found'), '');
      getPerformanceStatsJson('/', false).then(() => {}).catch((error) => {
        assert(error);
      });
    });
  });

  describe('#handlePerformanceStats', () => {
    let handlePerformanceStats;
    let fdCache;
    let topic;
    let kafkaMessage;

    beforeEach(() => {
      handlePerformanceStats = kafkaEdsPipelineProxy.__get__('handlePerformanceStats');
      topic = kafkaEdsPipelineProxy.__get__('topic');

      kafkaMessage = {
        value: {
          type: 5,
          requestId: 'request_123',
          rails_session_id: '123',
          message: '',
          appTesting: false,
        },
      };

      fdCache = {
        del: sinon.stub(),
        get: sinon.stub(),
        ttl: sinon.stub(),
        set: sinon.stub(),
      };

      helperSpy = {
        fsWriteFileAsync: sinon.spy(),
        fsMkdirAsync: sinon.spy(),
      };
      Object.assign(origKafkaHelper, helperSpy);
      kafkaEdsPipelineProxy.__set__('kafkaHelper', origKafkaHelper);
      kafkaEdsPipelineProxy.__set__('topic', 'testing_raw_logs');

      sinon.spy(logger, 'info');
      sinon.spy(logger, 'error');
    });

    afterEach(() => {
      kafkaEdsPipelineProxy.__set__('topic', topic);

      logger.info.restore();
      logger.error.restore();
    });

    it('should generate stats from file for that session', async () => {
      const smallBufferedStream = new Readable();
      smallBufferedStream.push('node-auto-6:POST:url:30');
      smallBufferedStream.push(null);
      bufferedStub.returns(smallBufferedStream);
      fsStubs.stat.returns(Promise.resolve(''));

      const stopFd = 1;

      fdCache.get.onCall(0).returns(stopFd);
      fsStubs.existsSync.restore();

      fsStubs.existsSync = sinon.stub(fs, 'existsSync', () => true);

      const options = {
        kafkaMessage,
        fdCache,
        hoothootStats,
      };
      await handlePerformanceStats(options);

      assert(logger.error.notCalled);
      assert(logger.info.called);
      assert(fdCache.del.called);
      assert(fsStubs.close.called);
    });

    it('should generate stats from file if json file threshold exceeds the max JSON_PACKET_THRESHOLD for app automate', async () => {
      const smallBufferedStream = new Readable();

      const times = 5;
      for (let i = 0; i < times; i++) {
        smallBufferedStream.push(`node-auto-6:POST:url-${i}-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text:3000000000000000:1000000000000000000000000:random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text\n`);
        smallBufferedStream.push(`node-auto-6:POST:element-${i}-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text:30000000000000:700000000000000000000:xpath-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text\n`);
      }
      smallBufferedStream.push(null);
      bufferedStub.returns(smallBufferedStream);
      fsStubs.stat.returns(Promise.resolve(''));

      const stopFd = 1;

      fdCache.get.onCall(0).returns(stopFd);
      fsStubs.existsSync.restore();

      fsStubs.existsSync = sinon.stub(fs, 'existsSync', () => true);
      kafkaMessage.value.appTesting = true;

      const options = {
        kafkaMessage,
        fdCache,
        hoothootStats,
      };
      await handlePerformanceStats(options);

      assert(logger.error.notCalled);
      assert(logger.info.called);
      assert(fdCache.del.called);
      assert(fsStubs.close.called);
    });

    it('should generate stats from file if json file threshold exceeds the max JSON_PACKET_THRESHOLD for automate', async () => {
      const smallBufferedStream = new Readable();

      const times = 5;
      for (let i = 0; i < times; i++) {
        smallBufferedStream.push(`node-auto-6:POST:url-${i}--random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text:3000000000000000:1000000000000000000000000:ra\n`);
        smallBufferedStream.push(`node-auto-6:POST:element-${i}--random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text-random-text:30000000000000:700000000000000000000:xpath\n`);
      }
      smallBufferedStream.push(null);
      bufferedStub.returns(smallBufferedStream);
      fsStubs.stat.returns(Promise.resolve(''));

      const stopFd = 1;

      fdCache.get.onCall(0).returns(stopFd);
      fsStubs.existsSync.restore();

      fsStubs.existsSync = sinon.stub(fs, 'existsSync', () => true);
      kafkaMessage.value.appTesting = false;

      const options = {
        kafkaMessage,
        fdCache,
        hoothootStats,
      };
      await handlePerformanceStats(options);

      assert(logger.error.notCalled);
      assert(logger.info.called);
      assert(fdCache.del.called);
      assert(fsStubs.close.called);
    });

    it('should generate stats from file for that session app automate', async () => {
      const smallBufferedStream = new Readable();
      smallBufferedStream.push('node-auto-6:POST:url:30:0:\n');
      smallBufferedStream.push('node-auto-6:POST:elemet:30:7:xpath\n');
      smallBufferedStream.push(null);
      bufferedStub.returns(smallBufferedStream);
      fsStubs.stat.returns(Promise.resolve(''));

      const stopFd = 1;

      fdCache.get.onCall(0).returns(stopFd);
      fsStubs.existsSync.restore();

      fsStubs.existsSync = sinon.stub(fs, 'existsSync', () => true);
      kafkaMessage.value.appTesting = true;

      const options = {
        kafkaMessage,
        fdCache,
        hoothootStats,
      };
      await handlePerformanceStats(options);

      assert(logger.error.notCalled);
      assert(logger.info.called);
      assert(fdCache.del.called);
      assert(fsStubs.close.called);
    });

    it('should generate stats from file for that session', async () => {
      fsStubs.stat.yields(new Error('file not found'), '');

      const stopFd = 1;

      fdCache.get.onCall(0).returns(stopFd);
      fsStubs.existsSync.restore();

      fsStubs.existsSync = sinon.stub(fs, 'existsSync', () => true);

      const options = {
        kafkaMessage,
        fdCache,
        hoothootStats,
      };
      await handlePerformanceStats(options);
      assert(logger.error.called);
      assert(fdCache.del.called);
      assert(fsStubs.close.called);
    });

    it('should generate stats from file for that session for app automate', async () => {
      fsStubs.stat.yields(new Error('file not found'), '');

      const stopFd = 1;

      fdCache.get.onCall(0).returns(stopFd);
      fsStubs.existsSync.restore();

      fsStubs.existsSync = sinon.stub(fs, 'existsSync', () => true);
      kafkaMessage.value.appTesting = true;

      const options = {
        kafkaMessage,
        fdCache,
        hoothootStats,
      };
      await handlePerformanceStats(options);
      assert(logger.error.called);
      assert(fdCache.del.called);
      assert(fsStubs.close.called);
    });
  });
});
