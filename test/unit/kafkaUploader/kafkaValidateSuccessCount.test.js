'use strict';

/* eslint-disable no-underscore-dangle */

const sinon = require('sinon');
const { assert } = require('chai');
const { describe } = require('mocha');
const rewire = require('rewire');
const helper = require('../../../helper');
const constant = require('../../../constants');

describe('getUploadSuccessfull count', () => {
  let gatherUploadSuccessfulCount;
  let validateUploadSuccessfulCount;
  let hoothootStateObj;
  let kafkaUploaderRewire;
  let defaultUploadSuccessfullCount;
  let logger;

  beforeEach(() => {
    process.env.CONSUMER_TOPIC = '';
    process.env.CONSUMER_ID = '';
    process.env.COMMON_KAFKA_TOPIC_REGION = '';

    kafkaUploaderRewire = rewire('../../../apps/kafkaUploader/kafkaUploader');
    gatherUploadSuccessfulCount =
      kafkaUploaderRewire.__get__('gatherUploadSuccessfulCount');
    validateUploadSuccessfulCount =
      kafkaUploaderRewire.__get__('validateUploadSuccessfulCount');
    hoothootStateObj = kafkaUploaderRewire.__get__('hoothootStats');
    defaultUploadSuccessfullCount =
      kafkaUploaderRewire.__get__('uploadSuccessfullCount');
    logger = kafkaUploaderRewire.__get__('logger');
  });

  afterEach(() => {
    delete process.env.CONSUMER_TOPIC;
    delete process.env.CONSUMER_ID;
    delete process.env.COMMON_KAFKA_TOPIC_REGION;

    kafkaUploaderRewire.__set__('hoothootStats', hoothootStateObj);
    kafkaUploaderRewire.__set__('uploadSuccessfullCount',
      defaultUploadSuccessfullCount);
  });

  describe('gatherUploadSuccessfulCount', () => {
    let specTempState;

    beforeEach(() => {
      specTempState = {
        uploadSuccessfull: {
          txt: 10,
          log: 15,
        },
      };

      kafkaUploaderRewire.__set__('hoothootStats', specTempState);
    });

    it('should update the sum values of successfull count values when uploadSuccessfull filetype not present', () => {
      gatherUploadSuccessfulCount();

      const resultingUploadSuccessCount =
        kafkaUploaderRewire.__get__('uploadSuccessfullCount');
      Object.entries(specTempState.uploadSuccessfull).forEach(([keyName, value]) => {
        assert(resultingUploadSuccessCount[keyName] === value);
      });
    });

    it('should increment the sum if uploadSuccessfull count if present', () => {
      const specUploadCount = {};
      Object.keys(specTempState.uploadSuccessfull).forEach((keyName) => {
        specUploadCount[keyName] = 10;
      });

      kafkaUploaderRewire.__set__('uploadSuccessfullCount', specUploadCount);

      gatherUploadSuccessfulCount();
      const resultingUploadingSuccessfullCount =
        kafkaUploaderRewire.__get__('uploadSuccessfullCount');

      Object.entries(specTempState.uploadSuccessfull).forEach(([keyName, value]) => {
        assert(resultingUploadingSuccessfullCount[keyName] === (value + 10));
      });
    });
  });

  describe('validateUploadSuccessfulCount', () => {
    let defaultUploadCount;

    beforeEach(() => {
      defaultUploadCount = kafkaUploaderRewire.__get__('uploadSuccessfullCount');
      sinon.stub(logger, 'warn', () => {});
      sinon.stub(helper, 'sendAlerts', () => {});
    });

    afterEach(() => {
      kafkaUploaderRewire.__set__('uploadSuccessfullCount', defaultUploadCount);
      logger.warn.restore();
      helper.sendAlerts.restore();
    });

    it('should not send stuck mail when non-zero successfull uploads', () => {
      const specUploadCountState = {
        txt: 10,
        log: 15,
      };
      kafkaUploaderRewire.__set__('uploadSuccessfullCount', specUploadCountState);

      validateUploadSuccessfulCount();

      assert(logger.warn.notCalled);
      assert(helper.sendAlerts.notCalled);
    });

    it('should send the alert when zero successfull count upload', () => {
      validateUploadSuccessfulCount();

      const [message] = logger.warn.getCall(0).args;
      const serverName = constant.SERVER_NAME;
      const expectedWarnMessage = `No successfull uploads have been made in last 1800 seconds by ${serverName}`;

      assert(logger.warn.calledOnce);
      assert(helper.sendAlerts.calledOnce);
      assert(message === expectedWarnMessage);
    });
  });
});
