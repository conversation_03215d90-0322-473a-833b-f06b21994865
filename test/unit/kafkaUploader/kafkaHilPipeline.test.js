'use strict';

/* eslint-disable no-underscore-dangle */
/* eslint-disable no-unused-expressions */
/* eslint-disable global-require */

const {
  describe, beforeEach, afterEach, it,
} = require('mocha');
const rewire = require('rewire');
const { assert, expect } = require('chai');
const sinon = require('sinon');
const fsExtra = require('fs-extra');
const fs = require('fs');
const Readable = require('stream').Readable;
const NodeCache = require('node-cache');

const origKafkaHelper = require('../../../apps/kafkaUploader/kafkaHelper');
const helper = require('../../../helper');
const instrumentation = require('../../../helpers/instrumentation');
const constants = require('../../../constants');

let fsStubs;
let bufferedStub;
let existsSyncStub;
let hilPipelineStubs;

describe('kafkaHilPipeline', () => {
  let kafkaHilPipelineProxy;
  let sessionStopHandler; let handleStartRequest; let handleCommand; let handleStopRequest; let handleOutsideBSTime; let syncKeyForDelete; let handleSpies; let syncAcrossNodes; let setSleepStats; let setOutsideBSTime; let cleanupForSession; let pushInstrumentation; let setNonZeroStatusesCount; let
    setNonZeroIncrementCounters;
  beforeEach(() => {
    process.env.CONSUMER_TOPIC = 'instrumentation_logs';
    process.env.CONSUMER_ID = '1';
    process.env.COMMON_KAFKA_TOPIC_REGION = '';

    kafkaHilPipelineProxy = rewire('../../../apps/kafkaUploader/hubInstrumentationConsumerHandlers.js');

    fsStubs = {
      closeSync: sinon.stub(fs, 'closeSync', () => {}),
      close: sinon.stub(fs, 'close', () => {}),
      writeFileSync: sinon.stub(fs, 'writeFileSync', () => {}),
      writeFile: sinon.stub(fs, 'writeFile', () => {}),
      writeSync: sinon.stub(fs, 'writeSync', () => {}),
      remove: sinon.stub(fsExtra, 'remove', () => {}),
      stat: sinon.stub(fs, 'stat'),
    };
    hilPipelineStubs = {
      getPathForMessage: sinon.stub(origKafkaHelper, 'getPathForMessage', () => 'filename'),
      getWriteFdForMessage: sinon.stub(origKafkaHelper, 'getWriteFdForMessage', () => {}),
    };

    bufferedStub = sinon.stub(fs, 'createReadStream');
    existsSyncStub = sinon.stub(fs, 'existsSync');

    sessionStopHandler = kafkaHilPipelineProxy.__get__('sessionStopHandler');
    handleStartRequest = kafkaHilPipelineProxy.__get__('handleStartRequest');
    handleCommand = kafkaHilPipelineProxy.__get__('handleCommand');
    handleStopRequest = kafkaHilPipelineProxy.__get__('handleStopRequest');
    handleOutsideBSTime = kafkaHilPipelineProxy.__get__('handleOutsideBSTime');
    syncKeyForDelete = kafkaHilPipelineProxy.__get__('syncKeyForDelete');
    syncAcrossNodes = kafkaHilPipelineProxy.__get__('syncAcrossNodes');
    setSleepStats = kafkaHilPipelineProxy.__get__('setSleepStats');
    setOutsideBSTime = kafkaHilPipelineProxy.__get__('setOutsideBSTime');
    cleanupForSession = kafkaHilPipelineProxy.__get__('cleanupForSession');
    pushInstrumentation = kafkaHilPipelineProxy.__get__('pushInstrumentation');
    setNonZeroStatusesCount = kafkaHilPipelineProxy.__get__('setNonZeroStatusesCount');
    setNonZeroIncrementCounters = kafkaHilPipelineProxy.__get__('setNonZeroIncrementCounters');
  });
  afterEach(() => {
    delete process.env.CONSUMER_TOPIC;
    delete process.env.CONSUMER_ID;
    delete process.env.COMMON_KAFKA_TOPIC_REGION;

    Object.values(fsStubs).forEach(el => el.restore());
    Object.values(hilPipelineStubs).forEach(el => el.restore());
    bufferedStub.restore();
    existsSyncStub.restore();
  });

  describe('addNestedObjects', () => {
    let addNestedObjects;
    beforeEach(() => {
      addNestedObjects = kafkaHilPipelineProxy.__get__('addNestedObjects');
    });

    afterEach(() => {
      kafkaHilPipelineProxy.__set__('addNestedObjects', () => { });
    });

    it('should add nested objects - replacing nested obj if not in first', () => {
      const current = {
        a: {
          b: {
            c: 1,
          },
        },
      };
      const toAdd = {
        a: {
          d: 1,
        },
      };
      const result = {
        a: {
          b: {
            c: 1,
          },
          d: 1,
        },
      };
      addNestedObjects(current, toAdd);
      assert.deepEqual(current, result);
    });

    it('should add nested objects - add value of non nested-obj keys', () => {
      const current = {
        a: {
          b: {
            c: 4,
            d: {
              e: 1,
            },
          },
        },
      };
      const toAdd = {
        a: {
          b: {
            c: 3,
            d: {
              e: 7,
            },
          },
        },
      };
      const result = {
        a: {
          b: {
            c: 7,
            d: {
              e: 8,
            },
          },
        },
      };
      addNestedObjects(current, toAdd);
      assert.deepEqual(current, result);
    });
  });

  describe('#sessionStopHandler', () => {
    beforeEach(() => {
      handleSpies = {
        handleStartRequestSpy: sinon.spy(),
        handleCommandSpy: sinon.spy(),
        handleStopRequestSpy: sinon.spy(),
      };

      kafkaHilPipelineProxy.__set__('handleStartRequest', handleSpies.handleStartRequestSpy);
      kafkaHilPipelineProxy.__set__('handleCommand', handleSpies.handleCommandSpy);
      kafkaHilPipelineProxy.__set__('handleStopRequest', handleSpies.handleStopRequestSpy);
      sinon.stub(helper, 'convertPubSubKafkaCodesToKeys');
      existsSyncStub.returns(true);
    });
    afterEach(() => {
      kafkaHilPipelineProxy.__set__('handleStartRequest', handleStartRequest);
      kafkaHilPipelineProxy.__set__('handleCommand', handleCommand);
      kafkaHilPipelineProxy.__set__('handleStopRequest', handleStopRequest);
      helper.convertPubSubKafkaCodesToKeys.restore();
    });

    it('reads logs from session log file stream and calls all handlers if the file exists', () => {
      const smallBufferedStream = new Readable();
      smallBufferedStream.push(`${JSON.stringify({ uniqueId: 'random', initKeyObject: {} })}\r\n`);
      smallBufferedStream.push(`${JSON.stringify({ uniqueId: 'random', data: {} })}\r\n`);
      smallBufferedStream.push(`${JSON.stringify({ uniqueId: 'random', data: {}, isTimeout: false })}\r\n`);
      smallBufferedStream.push(null);
      bufferedStub.returns(smallBufferedStream);

      Promise.resolve(sessionStopHandler({ kafkaMessage: { value: { rails_session_id: 'random' } }, fdCache: new NodeCache({ stdTTL: 60, checkperiod: 10 }) }))
        .then(() => {
          assert(helper.convertPubSubKafkaCodesToKeys.called.should.be.true);
          assert(handleSpies.handleStartRequestSpy.called);
          assert(handleSpies.handleCommandSpy.called);
          assert(handleSpies.handleStopRequestSpy.called);
        });
    });

    it('reads logs from session log file stream and does not call stop if the file exists', () => {
      const smallBufferedStream = new Readable();
      smallBufferedStream.push(`${JSON.stringify({ uniqueId: 'random', initKeyObject: {} })}\r\n`);
      smallBufferedStream.push(`${JSON.stringify({ uniqueId: 'random', data: {} })}\r\n`);
      smallBufferedStream.push(null);
      bufferedStub.returns(smallBufferedStream);

      Promise.resolve(sessionStopHandler({ kafkaMessage: { value: { rails_session_id: 'random' } }, fdCache: new NodeCache({ stdTTL: 60, checkperiod: 10 }) }))
        .then(() => {
          assert(helper.convertPubSubKafkaCodesToKeys.called.should.be.true);
          assert(handleSpies.handleStartRequestSpy.called);
          assert(handleSpies.handleCommandSpy.called);
          assert(handleSpies.handleStopRequestSpy.notCalled);
        });
    });

    it('reads logs from session log file stream and does not call handle command if the file exists', () => {
      const smallBufferedStream = new Readable();
      smallBufferedStream.push(`${JSON.stringify({ uniqueId: 'random', initKeyObject: {} })}\r\n`);
      smallBufferedStream.push(`${JSON.stringify({ uniqueId: 'random', data: {}, isTimeout: false })}\r\n`);
      smallBufferedStream.push(null);
      bufferedStub.returns(smallBufferedStream);

      Promise.resolve(sessionStopHandler({ kafkaMessage: { value: { rails_session_id: 'random' } }, fdCache: new NodeCache({ stdTTL: 60, checkperiod: 10 }) }))
        .then(() => {
          assert(helper.convertPubSubKafkaCodesToKeys.called.should.be.true);
          assert(handleSpies.handleStartRequestSpy.called);
          assert(handleSpies.handleCommandSpy.notCalled);
          assert(handleSpies.handleStopRequestSpy.called);
        });
    });
  });

  describe('#handleStartRequest', () => {
    let messageObj;
    beforeEach(() => {
      messageObj = {
        uniqueId: 'uniqueId',
        initKeyObject: {
          seleniumRequestsCount: 0,
          lastRequestTime: 'xyz',
          rproxyHost: 'xyz',
        },
      };
    });
    afterEach(() => {
      messageObj = {};
      if (constants.global_registry.rails_session_id) delete constants.global_registry.rails_session_id;
    });

    it('should take sessionId and start log as arguments and create keyObject in constants file', () => {
      Promise.resolve(handleStartRequest('rails_session_id', messageObj))
        .then(() => {
          assert(constants.global_registry.rails_session_id);
        });
    });
  });

  describe('#handleCommand', () => {
    let messageObj;
    beforeEach(() => {
      messageObj = {
        uniqueId: 'uniqueId',
        data: {
          seleniumRequestsCount: 10,
          safariPrivoxyTimeout: false,
          sleepTimeNow: 'xyz',
          lastResponseTime: 'xyz',
        },
      };
      constants.global_registry.rails_session_id = {
        seleniumRequestsCount: {
          someOtherRandomId: 0,
        },
        lastRequestTime: [],
        lastResponseTime: [],
        sleepTimeNow: [],
      };
      handleSpies = {
        handleOutsideBSTimeSpy: sinon.spy(handleOutsideBSTime),
        syncKeyForDeleteSpy: sinon.spy(),
      };
    });
    afterEach(() => {
      messageObj = {};
      if (constants.global_registry.rails_session_id) delete constants.global_registry.rails_session_id;
    });

    it('should take sessionId and command log as arguments and call handleOutsideBSTime', () => {
      kafkaHilPipelineProxy.__set__('handleOutsideBSTime', handleSpies.handleOutsideBSTimeSpy);
      kafkaHilPipelineProxy.__set__('syncKeyForDelete', handleSpies.syncKeyForDeleteSpy);
      Promise.resolve(handleCommand('rails_session_id', messageObj, false))
        .then(() => {
          assert(handleSpies.handleOutsideBSTimeSpy.called);
          assert(handleSpies.syncKeyForDeleteSpy.called);
          kafkaHilPipelineProxy.__set__('handleOutsideBSTime', handleOutsideBSTime);
          kafkaHilPipelineProxy.__set__('syncKeyForDelete', syncKeyForDelete);
        });
    });

    it('should take sessionId and command log as arguments and change keyObject data', () => {
      Promise.resolve(handleCommand('rails_session_id', messageObj, false))
        .then(() => {
          assert.equal(constants.global_registry.rails_session_id.seleniumRequestsCount.uniqueId, 10);
          assert.deepEqual(constants.global_registry.rails_session_id.lastResponseTime, ['xyz']);
          assert.deepEqual(constants.global_registry.rails_session_id.sleepTimeNow, ['xyz']);
        });
    });
  });

  describe('#handleStopRequest', () => {
    beforeEach(() => {
      constants.global_registry.rails_session_id = {
        seleniumRequestsCount: {
          someOtherRandomId: 0,
        },
        outsideBrowserstackTime: 'xyz',
        sleepTimeNow: [],
        nonZeroStatusesCount: 'xyz',
        nonZeroIncrementCounters: 'xyz',
      };
      handleSpies = {
        handleCommandSpy: sinon.spy(),
        syncAcrossNodesSpy: sinon.spy(),
        setSleepStatsSpy: sinon.spy(),
        setOutsideBSTimeSpy: sinon.spy(),
        cleanupForSessionSpy: sinon.spy(),
        pushInstrumentationSpy: sinon.spy(),
        setNonZeroStatusesCountSpy: sinon.spy(),
        setNonZeroIncrementCountersSpy: sinon.spy(),
      };
      kafkaHilPipelineProxy.__set__('handleCommand', handleSpies.handleCommandSpy);
      kafkaHilPipelineProxy.__set__('syncAcrossNodes', handleSpies.syncAcrossNodesSpy);
      kafkaHilPipelineProxy.__set__('setSleepStats', handleSpies.setSleepStatsSpy);
      kafkaHilPipelineProxy.__set__('setOutsideBSTime', handleSpies.setOutsideBSTimeSpy);
      kafkaHilPipelineProxy.__set__('cleanupForSession', handleSpies.cleanupForSessionSpy);
      kafkaHilPipelineProxy.__set__('pushInstrumentation', handleSpies.pushInstrumentationSpy);
      kafkaHilPipelineProxy.__set__('setNonZeroStatusesCount', handleSpies.setNonZeroStatusesCountSpy);
      kafkaHilPipelineProxy.__set__('setNonZeroIncrementCounters', handleSpies.setNonZeroIncrementCountersSpy);
      sinon.stub(helper, 'PingZombie');
      sinon.stub(helper, 'pingDataToStats');
      sinon.stub(helper, 'sendNonZeroStatusCountToZombie');
      sinon.stub(instrumentation, 'pushFeatureUsage');
    });
    afterEach(() => {
      if (constants.global_registry.rails_session_id) delete constants.global_registry.rails_session_id;
      kafkaHilPipelineProxy.__set__('handleCommand', handleCommand);
      kafkaHilPipelineProxy.__set__('syncAcrossNodes', syncAcrossNodes);
      kafkaHilPipelineProxy.__set__('setSleepStats', setSleepStats);
      kafkaHilPipelineProxy.__set__('setOutsideBSTime', setOutsideBSTime);
      kafkaHilPipelineProxy.__set__('cleanupForSession', cleanupForSession);
      kafkaHilPipelineProxy.__set__('pushInstrumentation', pushInstrumentation);
      kafkaHilPipelineProxy.__set__('setNonZeroStatusesCount', setNonZeroStatusesCount);
      kafkaHilPipelineProxy.__set__('setNonZeroIncrementCounters', setNonZeroIncrementCounters);
      helper.PingZombie.restore();
      helper.pingDataToStats.restore();
      helper.sendNonZeroStatusCountToZombie.restore();
      instrumentation.pushFeatureUsage.restore();
    });

    it('should call handleCommand if a timeout log', () => {
      handleStopRequest('rails_session_id', {}, true, {
        uploadError: 0,
        uploadSuccessfull: {},
        uploadTime: {},
        uploadSize: {},
      });
      assert(handleSpies.handleCommandSpy.called);
    });

    it('should call handleCommand if not a timeout log', () => {
      handleStopRequest('rails_session_id', {}, false, {
        uploadError: 0,
        uploadSuccessfull: {},
        uploadTime: {},
        uploadSize: {},
      });
      assert(handleSpies.handleCommandSpy.called);
    });

    it('should call relevant data handlers for stop log', () => {
      handleStopRequest('rails_session_id', {}, false, {
        uploadError: 0,
        uploadSuccessfull: {},
        uploadTime: {},
        uploadSize: {},
      });
      assert(handleSpies.syncAcrossNodesSpy.called);
      assert(handleSpies.setSleepStatsSpy.called);
      assert(handleSpies.setOutsideBSTimeSpy.called);
    });

    it('should forward handling to other handlers for specific keys for stop log', () => {
      kafkaHilPipelineProxy.__set__('syncAcrossNodes', syncAcrossNodes);
      handleStopRequest('rails_session_id', {}, false, {
        uploadError: 0,
        uploadSuccessfull: {},
        uploadTime: {},
        uploadSize: {},
      });
      assert(handleSpies.setNonZeroStatusesCountSpy.called);
      assert(handleSpies.setNonZeroIncrementCountersSpy.called);
    });

    it('should call relevant post processing handlers for stop log', () => {
      handleStopRequest('rails_session_id', {}, false, {
        uploadError: 0,
        uploadSuccessfull: {},
        uploadTime: {},
        uploadSize: {},
      });
      assert(handleSpies.cleanupForSessionSpy.called);
      assert(handleSpies.pushInstrumentationSpy.called);
    });

    it('should not call pingZombie to send instrumentation data if flag = 2', () => {
      constants.instrumentationMechanismFlag = 2;
      pushInstrumentation('rails_session_id');
      assert(helper.PingZombie.called.should.be.false);
    });

    it('should not call pingZombie to send instrumentation data if flag = 1', () => {
      constants.instrumentationMechanismFlag = 1;
      pushInstrumentation('rails_session_id');
      assert(helper.PingZombie.called.should.be.false);
      constants.instrumentationMechanismFlag = 2;
    });

    it('should call pingZombie to send instrumentation data if flag = 0', () => {
      constants.instrumentationMechanismFlag = 0;
      pushInstrumentation('rails_session_id');
      assert(helper.PingZombie.called.should.be.true);
      constants.instrumentationMechanismFlag = 2;
    });

    it('should not call pushFeatureUsage to send instrumentation data if flag = 2', () => {
      constants.instrumentationMechanismFlag = 2;
      pushInstrumentation('rails_session_id');
      assert(instrumentation.pushFeatureUsage.called.should.be.false);
    });

    it('should call pushFeatureUsage to send instrumentation data if flag = 1', () => {
      constants.instrumentationMechanismFlag = 1;
      pushInstrumentation('rails_session_id');
      assert(instrumentation.pushFeatureUsage.called.should.be.true);
      constants.instrumentationMechanismFlag = 2;
    });

    it('should call pushFeatureUsage to send instrumentation data if flag = 0', () => {
      constants.instrumentationMechanismFlag = 0;
      pushInstrumentation('rails_session_id');
      assert(instrumentation.pushFeatureUsage.called.should.be.true);
      constants.instrumentationMechanismFlag = 2;
    });

    it('should call pingDataToStats to send instrumentation data if flag = 0', () => {
      constants.instrumentationMechanismFlag = 0;
      pushInstrumentation('rails_session_id');
      assert(helper.pingDataToStats.called.should.be.true);
      constants.instrumentationMechanismFlag = 2;
    });

    it('should call pingDataToStats to send instrumentation data if flag = 1', () => {
      constants.instrumentationMechanismFlag = 1;
      pushInstrumentation('rails_session_id');
      assert(helper.pingDataToStats.called.should.be.true);
      constants.instrumentationMechanismFlag = 2;
    });

    it('should call pingDataToStats to send instrumentation data if flag = 2', () => {
      constants.instrumentationMechanismFlag = 2;
      pushInstrumentation('rails_session_id');
      assert(helper.pingDataToStats.called.should.be.true);
    });

    // it should call pingZombie to send instrumentation data with userToNginxTime if flag = 0
    // it should call pingZombie to send instrumentation data with userToNginxTime if flag = 1

    it('should call pingZombie to send instrumentation data with userToNginxTime if flag = 0', () => {
      constants.instrumentationMechanismFlag = 0;
      constants.global_registry.rails_session_id.appTesting = false;
      constants.global_registry.rails_session_id.terminal_type = 'desktop';
      constants.global_registry.rails_session_id.userToNginxTime = 100;
      constants.global_registry.rails_session_id.socketMessages = 100;
      pushInstrumentation('rails_session_id');
      assert(instrumentation.pushFeatureUsage.called.should.be.true);
      constants.instrumentationMechanismFlag = 2;
    });

    it('should call pingZombie to send instrumentation data with userToNginxTime if flag = 0 for appTesting', () => {
      constants.instrumentationMechanismFlag = 0;
      constants.global_registry.rails_session_id.appTesting = true;
      constants.global_registry.rails_session_id.terminal_type = 'mobile';
      constants.global_registry.rails_session_id.userToNginxTime = 100;
      constants.global_registry.rails_session_id.socketMessages = 100;
      pushInstrumentation('rails_session_id');
      assert(instrumentation.pushFeatureUsage.called.should.be.true);
      constants.instrumentationMechanismFlag = 2;
    });

    it('should call pingZombie to send instrumentation data with server_port if flag = 0', () => {
      constants.instrumentationMechanismFlag = 0;
      constants.global_registry.rails_session_id.appTesting = true;
      constants.global_registry.rails_session_id.terminal_type = 'mobile';
      constants.global_registry.rails_session_id.server_port = 4444;
      constants.global_registry.rails_session_id.socketMessages = 100;
      pushInstrumentation('rails_session_id');
      assert.equal(helper.PingZombie.getCall(0).args[0].tertiary_params.server_port, constants.global_registry.rails_session_id.server_port);
      constants.instrumentationMechanismFlag = 2;
    });

    it('should set hubProcessingTime if greater then zero', () => {
      constants.instrumentationMechanismFlag = 0;
      constants.global_registry.rails_session_id.appTesting = false;
      constants.global_registry.rails_session_id.terminal_type = 'desktop';
      constants.global_registry.rails_session_id.hubProcessingTime = 100;
      constants.global_registry.rails_session_id.nginxToHubTime = 100;
      constants.global_registry.rails_session_id.jarTime = 100;
      constants.global_registry.rails_session_id.automate_ai_duration = 100;
      constants.global_registry.rails_session_id.automate_ai_success = 100;
      constants.global_registry.rails_session_id.automate_ai_retry_count = 100;
      constants.global_registry.rails_session_id.automate_ai_find_element_count = 100;
      constants.global_registry.rails_session_id.automate_tcg_duration = 100;
      pushInstrumentation('rails_session_id');
      assert(instrumentation.pushFeatureUsage.called.should.be.true);
      constants.instrumentationMechanismFlag = 2;
    });

    it('should set hubProcessingTime if greater then zero for appTesting', () => {
      constants.instrumentationMechanismFlag = 0;
      constants.global_registry.rails_session_id.appTesting = true;
      constants.global_registry.rails_session_id.terminal_type = 'mobile';
      constants.global_registry.rails_session_id.hubProcessingTime = 100;
      constants.global_registry.rails_session_id.nginxToHubTime = 100;
      constants.global_registry.rails_session_id.jarTime = 100;
      constants.global_registry.rails_session_id.automate_ai_duration = 100;
      constants.global_registry.rails_session_id.automate_ai_success = 100;
      constants.global_registry.rails_session_id.automate_ai_retry_count = 100;
      constants.global_registry.rails_session_id.automate_ai_find_element_count = 100;
      constants.global_registry.rails_session_id.automate_tcg_duration = 100;
      pushInstrumentation('rails_session_id');
      assert(instrumentation.pushFeatureUsage.called.should.be.true);
      constants.instrumentationMechanismFlag = 2;
    });

    it('should call pingZombie via pingDataToStats to send instrumentation data if service = SeleniumHub', () => {
      delete process.env.CONSUMER_TOPIC;
      delete process.env.CONSUMER_ID;
      delete process.env.COMMON_KAFKA_TOPIC_REGION;
      helper.pingDataToStats.restore();
      constants.global_registry.rails_session_id.nonZeroStatusesCount = {
        7: 1,
      };
      helper.pingDataToStats(constants.global_registry.rails_session_id);
      assert(helper.PingZombie.called.should.be.true);
      assert(helper.sendNonZeroStatusCountToZombie.called.should.be.true);
      sinon.stub(helper, 'pingDataToStats');
      process.env.CONSUMER_TOPIC = 'instrumentation_logs';
      process.env.CONSUMER_ID = '1';
      process.env.COMMON_KAFKA_TOPIC_REGION = '';
      constants.global_registry.rails_session_id.nonZeroStatusesCount = 'xyz';
    });

    it('should call/not call pingZombie via pingDataToStats to send instrumentation data depending on flag if service = instrumentation service', () => {
      helper.pingDataToStats.restore();

      constants.instrumentationMechanismFlag = 1;
      constants.global_registry.rails_session_id.nonZeroStatusesCount = {
        7: 1,
      };
      helper.pingDataToStats(constants.global_registry.rails_session_id);
      assert(helper.PingZombie.called.should.be.false);
      assert(helper.sendNonZeroStatusCountToZombie.called.should.be.true);

      constants.instrumentationMechanismFlag = 2;
      constants.global_registry.rails_session_id.nonZeroStatusesCount = {
        7: 1,
      };
      helper.pingDataToStats(constants.global_registry.rails_session_id);
      assert(helper.PingZombie.called.should.be.false);
      assert(helper.sendNonZeroStatusCountToZombie.called.should.be.true);

      constants.instrumentationMechanismFlag = 0;
      constants.global_registry.rails_session_id.nonZeroStatusesCount = {
        7: 1,
      };
      helper.pingDataToStats(constants.global_registry.rails_session_id);
      assert(helper.PingZombie.called.should.be.true);
      assert(helper.sendNonZeroStatusCountToZombie.called.should.be.true);

      sinon.stub(helper, 'pingDataToStats');
      constants.global_registry.rails_session_id.nonZeroStatusesCount = 'xyz';
      constants.instrumentationMechanismFlag = 2;
    });
  });

  describe('setNonZero', () => {
    let setNonZeroStatusesCoun;
    let setNonZeroIncrementCounter;
    let setCustomExecutorInstrumentatio;

    beforeEach(() => {
      setNonZeroStatusesCoun = kafkaHilPipelineProxy.__get__('setNonZeroStatusesCount');
      setNonZeroIncrementCounter = kafkaHilPipelineProxy.__get__('setNonZeroIncrementCounters');
      setCustomExecutorInstrumentatio = kafkaHilPipelineProxy.__get__('setCustomExecutorInstrumentation');
    });

    afterEach(() => {
      kafkaHilPipelineProxy.__set__('setNonZeroStatusesCount', setNonZeroStatusesCoun);
      kafkaHilPipelineProxy.__set__('setNonZeroIncrementCounters', setNonZeroIncrementCounter);
      kafkaHilPipelineProxy.__set__('setCustomExecutorInstrumentation', setCustomExecutorInstrumentatio);
    });

    it('set non zero status key', () => {
      constants.global_registry.abc = {
        nonZeroStatusesCount: {
          1: { 1: 1, 2: 3 },
          2: { 2: 1, 3: 3 },
        },
      };
      setNonZeroStatusesCoun('abc');
      expect(constants.global_registry.abc.nonZeroStatusesCount).to.deep.equal({ 1: 1, 2: 4, 3: 3 });
      delete constants.global_registry.abc;
    });

    it('set non zero incremental counters', () => {
      constants.global_registry.abc = {
        nonZeroIncrementCounters: {
          1: ['a', 'b', 'c'],
          2: ['z', 'x', 'c'],
        },
      };
      setNonZeroIncrementCounter('abc');
      expect(constants.global_registry.abc.nonZeroIncrementCounters).to.deep.equal(['a', 'b', 'c', 'z', 'x']);
      delete constants.global_registry.abc;
    });

    it('set custom executor instrumentation', () => {
      constants.global_registry.abc = {
        customExecutorInstrumentation: {
          1: { 1: 1, 2: 3 },
          2: { 2: 1, 3: 3 },
        },
      };
      setCustomExecutorInstrumentatio('abc');
      expect(constants.global_registry.abc.customExecutorInstrumentation).to.deep.equal({ 1: 1, 2: 4, 3: 3 });
    });
  });

  describe('setOutsideBSTime', () => {
    let setOutsideBSTim;

    beforeEach(() => {
      setOutsideBSTim = kafkaHilPipelineProxy.__get__('setOutsideBSTime');
    });

    afterEach(() => {
      kafkaHilPipelineProxy.__set__('setOutsideBSTime', setOutsideBSTim);
    });

    it('set outside bs time', () => {
      constants.global_registry.abc = {};
      setOutsideBSTim('abc', [4, 5, 6], [3, 4, 5]);
      expect(constants.global_registry.abc.outsideBrowserstackTime).to.equal(3);
      delete constants.global_registry.abc;
    });

    it('set outside bs time and log if data not equal', () => {
      constants.global_registry.abc = {};
      setOutsideBSTim('abc', [4, 5, 6], [3, 4]);
      expect(constants.global_registry.abc.outsideBrowserstackTime).to.equal(2);
      delete constants.global_registry.abc;
    });
  });

  describe('cleanupForSession', () => {
    let cleanupForSessio;

    beforeEach(() => {
      cleanupForSessio = kafkaHilPipelineProxy.__get__('cleanupForSession');
    });

    afterEach(() => {
      kafkaHilPipelineProxy.__set__('cleanupForSession', cleanupForSessio);
    });

    it('set cleanupForSession if keys exist', () => {
      constants.global_registry.abc = {};
      constants.global_registry.abc.lastRequestTime = [1, 2, 3, 4];
      constants.global_registry.abc.lastResponseTime = [2, 3, 4, 5];
      cleanupForSessio('abc');
      expect(constants.global_registry.abc.lastRequestTime).to.equal(4);
      expect(constants.global_registry.abc.lastResponseTime).to.equal(5);
      delete constants.global_registry.abc;
    });

    it('don\'t set cleanupForSession if keys not present', () => {
      constants.global_registry.abc = {};
      cleanupForSessio('abc');
      expect(constants.global_registry.abc).to.not.include({ lastRequestTime: 4 });
      expect(constants.global_registry.abc).to.not.include({ lastResponseTime: 5 });
      delete constants.global_registry.abc;
    });
  });

  describe('syncAcrossNodes', () => {
    let syncAcrossNode;

    beforeEach(() => {
      syncAcrossNode = kafkaHilPipelineProxy.__get__('syncAcrossNodes');
    });

    afterEach(() => {
      kafkaHilPipelineProxy.__set__('syncAcrossNodes', syncAcrossNode);
    });

    it('should return setCustomExecutorInstrumentation', () => {
      const setCustomExecutorInstrumentation = kafkaHilPipelineProxy.__get__('setCustomExecutorInstrumentation');
      const x = sinon.stub();
      kafkaHilPipelineProxy.__set__('setCustomExecutorInstrumentation', x);
      syncAcrossNode('abc', 'customExecutorInstrumentation');
      assert(x.calledOnce === true);
      kafkaHilPipelineProxy.__set__('setCustomExecutorInstrumentation', setCustomExecutorInstrumentation);
    });

    it('should set request_count', () => {
      constants.global_registry.abc = {};
      constants.global_registry.abc.request_count = 10;
      syncAcrossNode('abc', 'request_count', false, { request_count: 10 });
      expect(constants.global_registry.abc.request_count).to.equal(10);
      delete constants.global_registry.abc;
    });

    it('should set value as 0 for an unknown key', () => {
      constants.global_registry.abc = {};
      constants.global_registry.abc.xyz = 10;
      syncAcrossNode('abc', 'xyz', false, {});
      expect(constants.global_registry.abc.xyz).to.equal(0);
      delete constants.global_registry.abc;
    });
  });
});
