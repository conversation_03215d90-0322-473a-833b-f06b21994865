'use strict';

/* eslint-disable no-underscore-dangle */
/* eslint-disable no-unused-expressions */
/* eslint-disable global-require */

const {
  describe, beforeEach, afterEach, it,
} = require('mocha');
const { assert } = require('chai');
const sinon = require('sinon');
const rewire = require('rewire');
const fsExtra = require('fs-extra');
const fs = require('fs');
const constants = require('../../../constants');

let hubInstrumentationConsumerHandlers;
let logger;
let fsStubs;
let bufferedStub;

describe('HubInstrumentationConsumerHandler', () => {
  beforeEach(() => {
    process.env.CONSUMER_TOPIC = '';
    process.env.CONSUMER_ID = '';
    process.env.COMMON_KAFKA_TOPIC_REGION = '';

    hubInstrumentationConsumerHandlers = rewire('../../../apps/kafkaUploader/hubInstrumentationConsumerHandlers');
    logger = hubInstrumentationConsumerHandlers.__get__('logger');

    fsStubs = {
      closeSync: sinon.stub(fs, 'closeSync', () => {}),
      close: sinon.stub(fs, 'close', () => {}),
      writeFileSync: sinon.stub(fs, 'writeFileSync', () => {}),
      writeFile: sinon.stub(fs, 'writeFile', () => {}),
      existsSync: sinon.stub(fs, 'existsSync', () => {}),
      remove: sinon.stub(fsExtra, 'remove', () => {}),
      stat: sinon.stub(fs, 'stat'),
    };

    bufferedStub = sinon.stub(fs, 'createReadStream');
  });

  afterEach(() => {
    delete process.env.CONSUMER_TOPIC;
    delete process.env.CONSUMER_ID;
    delete process.env.COMMON_KAFKA_TOPIC_REGION;

    Object.values(fsStubs).forEach(el => el.restore());
    bufferedStub.restore();
  });

  describe('setSleepStats', () => {
    let setSleepStats;

    beforeEach(() => {
      setSleepStats = hubInstrumentationConsumerHandlers.__get__('setSleepStats');
      sinon.spy(logger, 'info');
      sinon.spy(logger, 'error');
    });

    afterEach(() => {
      delete process.env.CONSUMER_TOPIC;
      delete process.env.CONSUMER_ID;
      delete process.env.COMMON_KAFKA_TOPIC_REGION;

      logger.info.restore();
      logger.error.restore();
      sinon.restore();
    });

    it('should calculate sleep stats correctly', () => {
      const rails_session_id = 'abcd-efgh-ijkl-mnop-qrst-uvwx-yz';
      constants.global_registry = {
        'abcd-efgh-ijkl-mnop-qrst-uvwx-yz': {
          lastResponseTime: [10000, 5000, 2000],
          sleepTimeNow: [8000, 300],
        },
      };

      setSleepStats(rails_session_id);
      console.log(constants.global_registry[rails_session_id]);

      assert.equal(constants.global_registry[rails_session_id].sleepTime, 1);
      assert.equal(constants.global_registry[rails_session_id].numSleep, 2);
      assert.isUndefined(constants.global_registry[rails_session_id].sleepTimeNow);
    });

    it('should log an error if no responses are recorded', () => {
      const rails_session_id = 'session2';
      constants.global_registry[rails_session_id] = {
        lastResponseTime: [],
        sleepTimeNow: [8000, 3000],
      };

      setSleepStats(rails_session_id);

      assert(logger.error.calledWith('[EXCEPTION] : [SLEEP STATS ERROR] : session2 : [NO RESPONSES RECORDED]'));
    });

    it('should log an error if no sleep timestamps are recorded', () => {
      const rails_session_id = 'session3';
      constants.global_registry[rails_session_id] = {
        lastResponseTime: [10000, 5000, 2000],
        sleepTimeNow: [],
      };

      setSleepStats(rails_session_id);

      assert(logger.error.calledWith('[EXCEPTION] : [SLEEP STATS ERROR] : session3 : [NO SLEEP TIMESTAMPS RECORDED]'));
    });

    it('should log an error if the number of responses and sleep timestamps do not match', () => {
      const rails_session_id = 'session4';
      constants.global_registry[rails_session_id] = {
        lastResponseTime: [10000, 5000],
        sleepTimeNow: [8000, 3000],
      };

      setSleepStats(rails_session_id);

      assert(logger.error.calledWith('[EXCEPTION] : [SLEEP STATS ERROR] : session4 : [SLEEP RESPONSES NUM MISMATCH]'));
    });

    it('should handle an empty global registry for a session id', () => {
      const rails_session_id = 'session5';
      constants.global_registry[rails_session_id] = {
        lastResponseTime: [],
        sleepTimeNow: [],
      };

      setSleepStats(rails_session_id);

      assert(logger.error.calledOnce);
    });
  });

  describe('syncKeyForDelete', () => {
    let syncKeyForDelete;

    beforeEach(() => {
      syncKeyForDelete = hubInstrumentationConsumerHandlers.__get__('syncKeyForDelete');
    });
    it('should return true if isDeleteRequest is false', () => {
      const result = syncKeyForDelete('someKey', false, false);
      assert.strictEqual(result, true);
    });

    it('should return false if isDeleteRequest is true and isTimeout is true', () => {
      const result = syncKeyForDelete('someKey', true, true);
      assert.strictEqual(result, false);
    });

    it('should return true if isDeleteRequest is true, isTimeout is false, and key is inside the list', () => {
      const result1 = syncKeyForDelete('insideHubTime', true, false);
      const result2 = syncKeyForDelete('nonZeroStatusesCount', true, false);
      const result3 = syncKeyForDelete('nonZeroIncrementCounters', true, false);
      assert.strictEqual(result1, true);
      assert.strictEqual(result2, true);
      assert.strictEqual(result3, true);
    });

    it('should return false if isDeleteRequest is true, isTimeout is false, and key is not inside the list', () => {
      const result = syncKeyForDelete('someOtherKey', true, false);
      assert.strictEqual(result, false);
    });
  });
});
