'use strict';

/* eslint-disable no-underscore-dangle */
/* eslint-disable no-unused-expressions */
/* eslint-disable global-require */

const {
  describe, it, afterEach, beforeEach,
} = require('mocha');
const rewire = require('rewire');
const { assert, expect } = require('chai');
const sinon = require('sinon');
const helper = require('../../../helper');
const { Events } = require('browserstack-dwh');

let kafkaS3pipelineProxy;

describe('Kafka sendToEDS', () => {
  beforeEach(() => {
    process.env.CONSUMER_TOPIC = '';
    process.env.CONSUMER_ID = '';
    process.env.COMMON_KAFKA_TOPIC_REGION = '';

    kafkaS3pipelineProxy = rewire('../../../apps/kafkaUploader/kafkaS3pipeline');
  });

  afterEach(() => {
    delete process.env.CONSUMER_TOPIC;
    delete process.env.CONSUMER_ID;
    delete process.env.COMMON_KAFKA_TOPIC_REGION;
  });

  describe('sendDataToEds', () => {
    let sendDataToEds;
    let topic;
    let sessionId;
    let isAppAutomate;
    let fileSize;

    beforeEach(() => {
      sendDataToEds = kafkaS3pipelineProxy.__get__('sendDataToEds');
      topic = kafkaS3pipelineProxy.__get__('topic');

      kafkaS3pipelineProxy.__set__('topic', 'testing_raw_logs');

      sessionId = '1234';
      isAppAutomate = false;
      fileSize = 12;
    });

    afterEach(() => {
      kafkaS3pipelineProxy.__set__('topic', topic);
    });

    it('should be successfully able to sendToEds', () => {
      sinon.stub(helper, 'sendToEDS', () => {});

      sendDataToEds(sessionId, isAppAutomate, false, fileSize, null);

      const expectedArg = {
        kind: Events.AUTOMATE_TEST_SESSIONS,
        hashed_id: '1234',
        feature_usage: {
          kafkaTestingRawLogs: {
            success: false, size: 12, storage_class: 'STANDARD', compressed_size: -1,
          },
        },
      };

      assert(helper.sendToEDS.calledOnce);

      expect(helper.sendToEDS.args[0][0]).to.eql(expectedArg);

      helper.sendToEDS.restore();
    });

    it('should be successfully able to sendToEds for appAutomate event', () => {
      sinon.stub(helper, 'sendToEDS', () => {});

      isAppAutomate = true;

      sendDataToEds(sessionId, isAppAutomate, true, fileSize, null);

      const expectedArg = {
        kind: Events.APP_AUTOMATE_TEST_SESSIONS,
        hashed_id: '1234',
        feature_usage: {
          kafkaTestingRawLogs: {
            success: true, size: 12, storage_class: 'STANDARD', compressed_size: -1,
          },
        },
      };

      assert(helper.sendToEDS.calledOnce);

      expect(helper.sendToEDS.args[0][0]).to.eql(expectedArg);

      helper.sendToEDS.restore();
    });
    it('should also send error message in payload', () => {
      sinon.stub(helper, 'sendToEDS', () => {});

      const errObj = { message: 'some error occurred' };
      sendDataToEds(sessionId, isAppAutomate, isAppAutomate, fileSize, errObj);

      const expectedArg = {
        kind: 'automate_test_sessions',
        hashed_id: '1234',
        feature_usage: {
          kafkaTestingRawLogs: {
            success: false, storage_class: 'STANDARD', size: 12, exception: 'some error occurred', compressed_size: -1,
          },
        },
      };

      assert(helper.sendToEDS.calledOnce);

      expect(helper.sendToEDS.args[0][0]).to.eql(expectedArg);

      helper.sendToEDS.restore();
    });
  });
});
