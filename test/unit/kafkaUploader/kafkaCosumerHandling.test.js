'use strict';

/* eslint-disable no-underscore-dangle */
/* eslint-disable global-require */
/* eslint-disable no-unused-expressions */

const {
  describe, it, beforeEach, afterEach,
} = require('mocha');
const rewire = require('rewire');
const sinon = require('sinon');
const fs = require('fs');
const { assert, expect } = require('chai');
const constants = require('../../../constants');
const kafka = require('../../../clients/kafka/kafkaClient');
const helper = require('../../../helper');

let kafkaUploaderProxy;
let logger;

describe('Consumer pause and resume handling', () => {
  beforeEach(() => {
    process.env.CONSUMER_TOPIC = '';
    process.env.CONSUMER_ID = '';
    process.env.COMMON_KAFKA_TOPIC_REGION = '';

    kafkaUploaderProxy = rewire('../../../apps/kafkaUploader/kafkaUploader');

    logger = kafkaUploaderProxy.__get__('logger');
  });

  afterEach(() => {
    delete process.env.CONSUMER_TOPIC;
    delete process.env.CONSUMER_ID;
    delete process.env.COMMON_KAFKA_TOPIC_REGION;
  });

  describe('pause consumers', () => {
    let clock;
    let curDate;
    let pauseConsumers;

    beforeEach(() => {
      curDate = new Date();
      clock = sinon.useFakeTimers(curDate.getTime());
      pauseConsumers = kafkaUploaderProxy.__get__('pauseConsumers');
    });

    afterEach(() => {
      clock.restore();
      kafkaUploaderProxy.__set__('consumerPaused', null);
    });

    it('consumerPaused should be nil when consumer paused', () => {
      sinon.spy(logger, 'warn');
      kafkaUploaderProxy.__set__('consumerPaused', Date.now());
      pauseConsumers();

      assert(logger.warn.notCalled);

      logger.warn.restore();
    });

    it('should pause running consumers', () => {
      const consumerCount = 5;
      const proxy = sinon.spy();

      const consumers = {};
      for (let idx = 0; idx < consumerCount; ++idx) {
        consumers[idx] = { pause: proxy };
      }

      kafkaUploaderProxy.__set__('consumers', consumers);
      sinon.spy(logger, 'warn');
      pauseConsumers();

      assert(proxy.callCount === 5);

      logger.warn.restore();
    });
  });

  describe('resume consumers', () => {
    let resumeConsumers;

    beforeEach(() => {
      resumeConsumers = kafkaUploaderProxy.__get__('resumeConsumers');
    });

    afterEach(() => {
      kafkaUploaderProxy.__set__('consumers', {});
    });

    it('should resume consumers', () => {
      const consumers = {};
      const proxy = sinon.spy();
      const consumerCount = 5;

      for (let idx = 0; idx < consumerCount; ++idx) {
        consumers[idx] = { resume: proxy };
      }

      kafkaUploaderProxy.__set__('consumers', consumers);
      sinon.spy(logger, 'warn');

      resumeConsumers();

      assert(proxy.callCount === consumerCount);
      assert(logger.warn.callCount === consumerCount);
      expect(kafkaUploaderProxy.__get__('consumerPaused')).to.be.null;

      logger.warn.restore();
    });
  });

  describe('fetch offsets from brokers and resume', () => {
    let fetchOffsetsFromBrokerAndResume;
    let originalRegion;

    beforeEach(() => {
      fetchOffsetsFromBrokerAndResume = kafkaUploaderProxy.__get__('fetchOffsetsFromBrokerAndResume');
      originalRegion = constants.region;
      constants.region = 'us-east-1';
    });

    afterEach(() => {
      constants.region = originalRegion;
      kafkaUploaderProxy.__set__('consumers', {});
    });

    it('should resume consumers and commit the current offset', async () => {
      kafkaUploaderProxy.__set__('topic', 'raw_logs');
      kafkaUploaderProxy.__set__('consumerId', '1');
      const originalKafkaConfig = constants.kafkaConfig;
      const partitionsArr = [0, 1, 2, 3];
      constants.kafkaConfig = {
        consumer: {
          raw_logs: {
            'us-east-1': {
              1: {
                partitions: partitionsArr,
              },
            },
          },
        },
      };

      kafkaUploaderProxy.__set__('kafkaConfig', constants.kafkaConfig);

      const sampleData = { raw_logs: {} };
      partitionsArr.forEach((partition) => {
        sampleData.raw_logs[partition] = [partition];
      });

      const offsetSpy = sinon.spy();
      const sampleConsumer = { raw_logs_1: { setOffset: offsetSpy } };

      kafkaUploaderProxy.__set__('consumers', sampleConsumer);

      const fetchSpy = sinon.stub().yields(null, sampleData);
      sinon.stub(kafka, 'initKafkaOffset', () => ({
        fetch: fetchSpy,
      }));

      const resumeConsumerSpy = sinon.spy();
      const commitOffsetsSpy = sinon.spy();

      const origResumeConsumers = kafkaUploaderProxy.__get__('resumeConsumers');
      const origCommitOffsets = kafkaUploaderProxy.__get__('commitOffsets');

      kafkaUploaderProxy.__set__('resumeConsumers', resumeConsumerSpy);
      kafkaUploaderProxy.__set__('commitOffsets', commitOffsetsSpy);

      await fetchOffsetsFromBrokerAndResume();

      assert(fetchSpy.calledOnce);
      assert(offsetSpy.callCount === partitionsArr.length);
      assert(resumeConsumerSpy.calledOnce);
      assert(commitOffsetsSpy.calledOnce);

      constants.kafkaConfig = originalKafkaConfig;
      kafkaUploaderProxy.__set__('kafkaConfig', constants.kafkaConfig);
      kafkaUploaderProxy.__set__('resumeConsumers', origResumeConsumers);
      kafkaUploaderProxy.__set__('commitOffsets', origCommitOffsets);

      kafka.initKafkaOffset.restore();
    });

    it('should increment offset fetch count error', async () => {
      kafkaUploaderProxy.__set__('topic', 'raw_logs');
      kafkaUploaderProxy.__set__('consumerId', '1');
      const originalKafkaConfig = constants.kafkaConfig;
      const partitionsArr = [0, 1, 2, 3];
      constants.kafkaConfig = {
        consumer: {
          raw_logs: {
            'us-east-1': {
              1: {
                partitions: partitionsArr,
              },
            },
          },
        },
      };

      kafkaUploaderProxy.__set__('kafkaConfig', constants.kafkaConfig);

      const sampleData = { raw_logs: {} };
      partitionsArr.forEach((partition) => {
        sampleData.raw_logs[partition] = [partition];
      });

      const errObj = new Error('unexpected happened');
      const fetchSpy = sinon.stub().yields(errObj, null);
      sinon.stub(kafka, 'initKafkaOffset', () => ({
        fetch: fetchSpy,
      }));

      await fetchOffsetsFromBrokerAndResume();

      assert(fetchSpy.calledOnce);
      assert(kafkaUploaderProxy.__get__('lastOffsetFetchError') === errObj);

      constants.kafkaConfig = originalKafkaConfig;
      kafkaUploaderProxy.__set__('kafkaConfig', constants.kafkaConfig);

      kafka.initKafkaOffset.restore();
    });
  });

  describe('alertIfTooLongPause', () => {
    let alertIfTooLongPause;
    let consumerPaused;
    let onMessageCallbackPaused;

    beforeEach(() => {
      alertIfTooLongPause = kafkaUploaderProxy.__get__('alertIfTooLongPause');
      consumerPaused = kafkaUploaderProxy.__get__('consumerPaused');
      onMessageCallbackPaused = kafkaUploaderProxy.__get__('onMessageCallbackPaused');
      sinon.stub(helper, 'sendAlerts', () => {});
      constants.isProductionEnv = true;
    });

    afterEach(() => {
      kafkaUploaderProxy.__set__('consumerPaused', consumerPaused);
      kafkaUploaderProxy.__set__('onMessageCallbackPaused', onMessageCallbackPaused);
      helper.sendAlerts.restore();
      constants.isProductionEnv = false;
    });

    it('should send alert when timeout is greater than 10 minutes', () => {
      const dateNow = Date.now() - (11 * 60 * 1000);
      kafkaUploaderProxy.__set__('consumerPaused', dateNow);

      alertIfTooLongPause();

      assert(helper.sendAlerts.calledOnce);
    });

    it('should send alert when timeout is greater than 10 minutes', () => {
      const dateNow = Date.now() - (11 * 60 * 1000);
      kafkaUploaderProxy.__set__('onMessageCallbackPaused', dateNow);

      const utimesSyncStub = sinon.stub(fs, 'utimesSync', () => {});
      alertIfTooLongPause();

      //  stub fs.utimesSync(watchPath, new Date(), new Date());
      assert(helper.sendAlerts.calledTwice);
      assert(fs.utimesSync.calledOnce);
      utimesSyncStub.restore();
    });

    it('should send alert when timeout is greater than 10 minutes and touch tmp is failed', () => {
      const dateNow = Date.now() - (11 * 60 * 1000);
      kafkaUploaderProxy.__set__('onMessageCallbackPaused', dateNow);

      const utimesSyncStub = sinon.stub(fs, 'utimesSync').throws(new Error('error'));
      alertIfTooLongPause();

      //  stub fs.utimesSync(watchPath, new Date(), new Date());
      assert(helper.sendAlerts.calledTwice);
      assert(fs.utimesSync.calledOnce);
      utimesSyncStub.restore();
    });

    it('should not send alert when non production env', () => {
      constants.isProductionEnv = false;
      const dateNow = Date.now() - (11 * 60 * 1000);
      kafkaUploaderProxy.__set__('onMessageCallbackPaused', dateNow);

      alertIfTooLongPause();

      //  stub fs.utimesSync(watchPath, new Date(), new Date());
      assert(helper.sendAlerts.notCalled);
    });

    it('should return when timeout is not that large', () => {
      const dateNow = Date.now() - (2 * 60 * 1000);
      kafkaUploaderProxy.__set__('consumerPaused', dateNow);

      alertIfTooLongPause();

      assert(helper.sendAlerts.notCalled);
    });
  });

  it('getTopicPartitionOffsets', (cb) => {
    const setSuffix = '';
    const options = {};
    const getTopicPartitionOffsets = kafkaUploaderProxy.__get__('getTopicPartitionOffsets');
    kafkaUploaderProxy.__set__('topic', 'raw_logs');
    kafkaUploaderProxy.__set__('consumerId', '1');
    const originalKafkaConfig = constants.kafkaConfig;
    const partitionsArr = [0, 1, 2, 3];
    constants.kafkaConfig = {
      consumer: {
        raw_logs: {
          'us-east-1': {
            1: {
              partitions: partitionsArr,
            },
          },
        },
      },
    };
    kafkaUploaderProxy.__set__('kafkaConfig', constants.kafkaConfig);

    getTopicPartitionOffsets(setSuffix, options, () => {
      constants.kafkaConfig = originalKafkaConfig;
      kafkaUploaderProxy.__set__('kafkaConfig', constants.kafkaConfig);
      cb();
    });
  });
});
