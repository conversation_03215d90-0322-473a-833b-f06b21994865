'use strict';

/* eslint-disable no-underscore-dangle */
/* eslint-disable no-unused-expressions */
/* eslint-disable global-require */

const {
  describe, it, afterEach, beforeEach,
} = require('mocha');
const rewire = require('rewire');
const { assert } = require('chai');
const sinon = require('sinon');
const fs = require('fs');
const AWS = require('aws-sdk');

let kafkaS3pipelineProxy;
let logger;
let clock;
let hoothootStats;

describe('Kafka uploadToS3', () => {
  beforeEach(() => {
    process.env.CONSUMER_TOPIC = '';
    process.env.CONSUMER_ID = '';
    process.env.COMMON_KAFKA_TOPIC_REGION = '';

    clock = sinon.useFakeTimers();
    kafkaS3pipelineProxy = rewire('../../../apps/kafkaUploader/kafkaS3pipeline');
    logger = kafkaS3pipelineProxy.__get__('logger');
    hoothootStats = {
      uploadError: 0,
      uploadSuccessfull: {},
      uploadTime: {},
      uploadSize: {},
    };
  });

  afterEach(() => {
    clock.restore();

    delete process.env.CONSUMER_TOPIC;
    delete process.env.CONSUMER_ID;
    delete process.env.COMMON_KAFKA_TOPIC_REGION;
  });

  describe('UploadToS3', () => {
    let uploadToS3;
    let sendDataToEds;
    let filename;
    let stats;
    let sessionId;
    let suffix;
    let isAppAutomate;

    let simpleCallback;
    let sendToEDSSpy;
    let streamSpy;

    beforeEach(() => {
      uploadToS3 = kafkaS3pipelineProxy.__get__('uploadToS3');

      filename = 'sample-logs.txt';
      stats = { size: 12 };
      sessionId = '1234';
      suffix = '-log.txt';
      isAppAutomate = false;
      simpleCallback = sinon.spy();

      sinon.spy(logger, 'info');
      sinon.spy(logger, 'error');
      sinon.spy(logger, 'warn');

      sendDataToEds = kafkaS3pipelineProxy.__get__('sendDataToEds');

      sendToEDSSpy = sinon.spy();
      kafkaS3pipelineProxy.__set__('sendDataToEds', sendToEDSSpy);

      streamSpy = {
        on: sinon.spy(),
        pipe: sinon.spy(),
      };
      sinon.stub(fs, 'createReadStream', () => streamSpy);
    });

    afterEach(() => {
      kafkaS3pipelineProxy.__set__('sendDataToEds', sendDataToEds);

      logger.info.restore();
      logger.error.restore();
      logger.warn.restore();

      fs.createReadStream.restore();
    });

    it('should update hoothoot entries on successfull upload', () => {
      const sendToS3Spy = sinon.stub();
      sendToS3Spy.yields(null, { data: 'some' });

      const s3UploadStub = sinon.stub(AWS.S3.prototype, 'upload', () => ({
        send: sendToS3Spy,
      }));

      uploadToS3(
        filename,
        stats,
        sessionId,
        suffix,
        isAppAutomate,
        hoothootStats,
        'STANDARD_IA',
        false,
        false,
        simpleCallback
      );

      assert(simpleCallback.calledOnce);
      assert(sendToEDSSpy.calledOnce);
      assert(logger.error.notCalled);
      assert(logger.info.calledTwice);

      s3UploadStub.restore();
    });

    it('should not upload file size of 0 bytes', () => {
      stats = { size: 0 };

      uploadToS3(
        filename,
        stats,
        sessionId,
        suffix,
        isAppAutomate,
        hoothootStats,
        'STANDARD_IA',
        false,
        false,
        simpleCallback
      );

      assert(simpleCallback.calledOnce);
      assert(sendToEDSSpy.calledOnce);
      assert(logger.error.notCalled);
      assert(logger.warn.calledOnce);
    });

    it('should abort the upload if timeout is reached', () => {
      const sendToS3Spy = sinon.stub();
      const abortStub = sinon.spy();

      const s3UploadStub = sinon.stub(AWS.S3.prototype, 'upload', () => ({
        send: sendToS3Spy,
        abort: abortStub,
      }));

      uploadToS3(
        filename,
        stats,
        sessionId,
        suffix,
        isAppAutomate,
        hoothootStats,
        'STANDARD_IA',
        false,
        true,
        simpleCallback
      );

      assert(simpleCallback.notCalled);

      clock.tick(60000);

      assert(logger.error.calledOnce);

      s3UploadStub.restore();
    });

    it('should update hoothoot entries on successfull upload for zipping enabled', () => {
      const sendToS3Spy = sinon.stub();
      sendToS3Spy.yields(null, { data: 'some' });

      const s3UploadStub = sinon.stub(AWS.S3.prototype, 'upload', () => ({
        send: sendToS3Spy,
      }));

      uploadToS3(
        filename,
        stats,
        sessionId,
        suffix,
        isAppAutomate,
        hoothootStats,
        'STANDARD_IA',
        true,
        true,
        simpleCallback
      );

      assert(simpleCallback.calledOnce);
      assert(sendToEDSSpy.calledOnce);
      assert(logger.info.calledThrice);
      assert(logger.error.notCalled);

      s3UploadStub.restore();
    });
  });
});
