'use strict';

/* eslint-disable no-underscore-dangle */
/* eslint-disable no-unused-expressions */
/* eslint-disable global-require */

const {
  describe, beforeEach, afterEach, it,
} = require('mocha');
const rewire = require('rewire');
const { assert, expect } = require('chai');
const sinon = require('sinon');

let kafkaUploaderProxy;
let kafkaHelperProxy;
let logger;

describe('core consumer logic', () => {
  beforeEach(() => {
    process.env.CONSUMER_TOPIC = '';
    process.env.CONSUMER_ID = '';
    process.env.COMMON_KAFKA_TOPIC_REGION = '';

    kafkaUploaderProxy = rewire('../../../apps/kafkaUploader/kafkaUploader');
    kafkaHelperProxy = rewire('../../../apps/kafkaUploader/kafkaHelper');

    logger = kafkaUploaderProxy.__get__('logger');
  });

  afterEach(() => {
    delete process.env.CONSUMER_TOPIC;
    delete process.env.CONSUMER_ID;
    delete process.env.COMMON_KAFKA_TOPIC_REGION;
  });

  xdescribe('logKafkaError', () => {
    let logKafkaError;
    let loggerSpy;

    beforeEach(() => {
      logKafkaError = kafkaUploaderProxy.__get__('logKafkaError');
      loggerSpy = sinon.spy(logger, 'error');
    });

    afterEach(() => {
      loggerSpy.restore();
    });

    it('should log with the correct output', () => {
      const errObj = new Error('Unable to process');
      logKafkaError(errObj);
      assert(loggerSpy.calledOnce);
    });
  });

  describe('offSet out of range callback', () => {
    let onOffsetOutOfRangeCallback;
    let pauseConsumers;
    let fetchOffsetsFromBrokerAndResume;
    let offsetOutOfRangeCount;

    beforeEach(() => {
      onOffsetOutOfRangeCallback = kafkaUploaderProxy.__get__('onOffsetOutOfRangeCallback');
      pauseConsumers = kafkaUploaderProxy.__get__('pauseConsumers');
      fetchOffsetsFromBrokerAndResume = kafkaUploaderProxy.__get__('fetchOffsetsFromBrokerAndResume');
      offsetOutOfRangeCount = kafkaUploaderProxy.__get__('offsetOutOfRangeCount');
    });

    afterEach(() => {
      kafkaUploaderProxy.__set__('pauseConsumers', pauseConsumers);
      kafkaUploaderProxy.__set__('fetchOffsetsFromBrokerAndResume', fetchOffsetsFromBrokerAndResume);
      kafkaUploaderProxy.__set__('offsetOutOfRangeCount', offsetOutOfRangeCount);
    });

    it('should increment the offset out of range counter', () => {
      kafkaUploaderProxy.__set__('offsetOutOfRangeCount', 1);
      onOffsetOutOfRangeCallback();

      assert(kafkaUploaderProxy.__get__('offsetOutOfRangeCount') === 2);
    });

    it('should call consumers and resume', () => {
      kafkaUploaderProxy.__set__('offsetOutOfRangeCount', 0);
      const pauseSpy = sinon.spy();
      const resumeSpy = sinon.spy();
      kafkaUploaderProxy.__set__('pauseConsumers', pauseSpy);
      kafkaUploaderProxy.__set__('fetchOffsetsFromBrokerAndResume', resumeSpy);

      onOffsetOutOfRangeCallback();

      assert(kafkaUploaderProxy.__get__('offsetOutOfRangeCount') === 1);
      assert(pauseSpy.calledOnce);
      assert(resumeSpy.calledOnce);
    });
  });

  describe('get logs dir path', () => {
    let topic;

    beforeEach(() => {
      topic = kafkaHelperProxy.__get__('topic');
    });

    afterEach(() => {
      kafkaHelperProxy.__set__('topic', topic);
    });

    it('should resolve save dir for session', () => {
      const sessionId = '1234';
      kafkaHelperProxy.__set__('topic', 'random_topic');
      const data = kafkaHelperProxy.getLogsDirPath(sessionId);
      assert(/1234_random_topic/.test(data), 'topic dir does not match');
    });
  });

  describe('getReadPathForMessage', () => {
    let getReadPathForMessage;
    let message;
    let temphelper;
    let getLogsDirPath;

    beforeEach(() => {
      getReadPathForMessage = kafkaUploaderProxy.__get__('getReadPathForMessage');
      message = {
        value: {
          rails_session_id: '1234',
          requestId: 'request_1234',
        },
      };

      temphelper = kafkaUploaderProxy.__get__('kafkaHelper');
      getLogsDirPath = temphelper.getLogsDirPath;
      temphelper.getLogsDirPath = _ => '1234_';
    });

    afterEach(() => {
      temphelper = kafkaUploaderProxy.__get__('kafkaHelper');
      temphelper.getLogsDirPath = getLogsDirPath;
    });

    it('should generate path when correct object passed', () => {
      const readPath = getReadPathForMessage(message);

      expect(readPath).to.have.string('1234_/1234-request_1234');
    });

    it('should handle when rails_session_id and requestId not present', () => {
      const readPath = getReadPathForMessage({ value: {} });

      expect(readPath).to.have.string('_/-');
    });
  });
});
