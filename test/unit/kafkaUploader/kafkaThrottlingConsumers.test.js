'use strict';

const rewire = require('rewire');
const chai = require('chai');
const sinon = require('sinon');

const { assert, expect } = require('chai');

const kafka = require('../../../clients/kafka/kafkaClient');
const helper = require('../../../helper');

let kafkaUploaderProxy;
let logger;

describe('throttling consumer logic', () => {
  beforeEach(() => {
    process.env.CONSUMER_TOPIC = '';
    process.env.CONSUMER_ID = '';
    process.env.COMMON_KAFKA_TOPIC_REGION = '';

    kafkaUploaderProxy = rewire('../../../apps/kafkaUploader/kafkaUploader');

    logger = kafkaUploaderProxy.__get__('logger');
  });

  afterEach(() => {
    delete process.env.CONSUMER_TOPIC;
    delete process.env.CONSUMER_ID;
    delete process.env.COMMON_KAFKA_TOPIC_REGION;
  });

  describe('throttlingConsumers', () => {
    let throttlingConsumers;
    let resumeConsumers;
    let resumeConsumersStub;
    let pauseConsumers;
    let pauseConsumersStub;
    let kafkaS3Pipeline;

    beforeEach(() => {
      throttlingConsumers = kafkaUploaderProxy.__get__('throttlingConsumers');
      kafkaS3Pipeline = kafkaUploaderProxy.__get__('kafkaS3Pipeline');

      sinon.spy(logger, 'info');

      resumeConsumers = kafkaUploaderProxy.__get__('resumeConsumers');
      resumeConsumersStub = sinon.stub();
      kafkaUploaderProxy.__set__('resumeConsumers', resumeConsumersStub);

      pauseConsumers = kafkaUploaderProxy.__get__('pauseConsumers');
      pauseConsumersStub = sinon.stub();
      kafkaUploaderProxy.__set__('pauseConsumers', pauseConsumersStub);
    });

    afterEach(() => {
      kafkaUploaderProxy.__set__('resumeConsumers', resumeConsumers);
      kafkaUploaderProxy.__set__('pauseConsumers', pauseConsumers);

      logger.info.restore();
      kafkaS3Pipeline.running.restore();
    });

    it('should call resumeConsumers', async () => {
      const fakeTopic = 'raw_logs';

      sinon.stub(kafkaS3Pipeline, 'running').returns(20);
      kafkaUploaderProxy.__set__('continueState', null);
      kafkaUploaderProxy.__set__('consumerPaused', true);
      kafkaUploaderProxy.__set__('topic', fakeTopic);
      await throttlingConsumers();
      assert(logger.info.calledOnce);
      assert(resumeConsumersStub.calledOnce);
      assert(pauseConsumersStub.notCalled);
    });

    it('should call pauseConsumers', async () => {
      const fakeTopic = 'raw_logs';

      sinon.stub(kafkaS3Pipeline, 'running').returns(100);
      kafkaUploaderProxy.__set__('continueState', null);
      kafkaUploaderProxy.__set__('consumerPaused', null);
      kafkaUploaderProxy.__set__('topic', fakeTopic);
      await throttlingConsumers();
      assert(logger.info.calledOnce);
      assert(pauseConsumersStub.calledOnce);
      assert(resumeConsumersStub.notCalled);
    });

    it('should not call resumeConsumers and pauseConsumers', async () => {
      const fakeTopic = 'raw_logs';

      sinon.stub(kafkaS3Pipeline, 'running').returns(20);
      kafkaUploaderProxy.__set__('continueState', null);
      kafkaUploaderProxy.__set__('consumerPaused', null);
      kafkaUploaderProxy.__set__('topic', fakeTopic);
      await throttlingConsumers();
      assert(logger.info.notCalled);
      assert(resumeConsumersStub.notCalled);
      assert(pauseConsumersStub.notCalled);
    });

    it('should not call resumeConsumers and pauseConsumers when touch tmp is done', async () => {
      const fakeTopic = 'raw_logs';

      sinon.stub(kafkaS3Pipeline, 'running').returns(0);
      kafkaUploaderProxy.__set__('continueState', '1');
      kafkaUploaderProxy.__set__('consumerPaused', null);
      kafkaUploaderProxy.__set__('topic', fakeTopic);
      await throttlingConsumers();
      assert(logger.info.calledOnce);
      assert(resumeConsumersStub.notCalled);
      assert(pauseConsumersStub.notCalled);
    });
  });

  describe('#setEventFunctions', () => {
    let setEventFunctions;

    beforeEach(() => {
      setEventFunctions = kafkaUploaderProxy.__get__('setEventFunctions');
    });

    afterEach(() => {
      kafkaUploaderProxy.__set__('setEventFunctions', setEventFunctions);
    });

    it('should set listeners on consumer', () => {
      const consumer = {
        on: sinon.spy(),
      };

      setEventFunctions(consumer);

      sinon.assert.calledWith(consumer.on, 'message', sinon.match.func);
      sinon.assert.calledWith(consumer.on, 'offsetOutOfRange', sinon.match.func);
      sinon.assert.calledWith(consumer.on, 'error', sinon.match.func);
    });
  });

  describe('initKafkaUploader', () => {
    let initKafkaUploader;

    beforeEach(() => {
      initKafkaUploader = kafkaUploaderProxy.__get__('initKafkaUploader');
    });

    afterEach(() => {
      kafkaUploaderProxy.__set__('initKafkaUploader', initKafkaUploader);
    });

    it('should set event functions', async () => {
      const initKafkaUploaderHelperObject = {
        initCaches: kafkaUploaderProxy.__get__('initCaches'),
        getTopicPartitionOffsets: kafkaUploaderProxy.__get__('getTopicPartitionOffsets'),
        setEventFunctions: kafkaUploaderProxy.__get__('setEventFunctions'),
      };
      const initCachesStub = sinon.stub(initKafkaUploaderHelperObject, 'initCaches');
      const getTopicPartitionOffsetsStub = sinon.stub(initKafkaUploaderHelperObject, 'getTopicPartitionOffsets').yields(2);
      const setEventFunctionsStub = sinon.stub(initKafkaUploaderHelperObject, 'setEventFunctions');
      kafkaUploaderProxy.__set__('initCaches', initCachesStub);
      kafkaUploaderProxy.__set__('getTopicPartitionOffsets', getTopicPartitionOffsetsStub);
      kafkaUploaderProxy.__set__('setEventFunctions', setEventFunctionsStub);
      const kafkaStub = sinon.stub(kafka, 'initConsumer');
      await initKafkaUploader();
      expect(getTopicPartitionOffsetsStub).to.have.been.calledOnce;
      initCachesStub.restore();
      getTopicPartitionOffsetsStub.restore();
      setEventFunctionsStub.restore();
      kafkaStub.restore();
    });

    it('should set event functions for readback', async () => {
      const initKafkaUploaderHelperObject = {
        initCaches: kafkaUploaderProxy.__get__('initCaches'),
        getTopicPartitionOffsets: kafkaUploaderProxy.__get__('getTopicPartitionOffsets'),
        setEventFunctions: kafkaUploaderProxy.__get__('setEventFunctions'),
        getHourIdentifier: kafkaUploaderProxy.__get__('getHourIdentifier'),
      };
      const initCachesStub = sinon.stub(initKafkaUploaderHelperObject, 'initCaches');
      const getTopicPartitionOffsetsStub = sinon.stub(initKafkaUploaderHelperObject, 'getTopicPartitionOffsets').yields(2);
      const setEventFunctionsStub = sinon.stub(initKafkaUploaderHelperObject, 'setEventFunctions');
      const getHourIdentifierStub = sinon.stub(initKafkaUploaderHelperObject, 'getHourIdentifier');
      kafkaUploaderProxy.__set__('initCaches', initCachesStub);
      kafkaUploaderProxy.__set__('getTopicPartitionOffsets', getTopicPartitionOffsetsStub);
      kafkaUploaderProxy.__set__('setEventFunctions', setEventFunctionsStub);
      kafkaUploaderProxy.__set__('getHourIdentifier', getHourIdentifierStub);
      const kafkaStub = sinon.stub(kafka, 'initConsumer');
      await initKafkaUploader(2);
      expect(getTopicPartitionOffsetsStub).to.have.been.calledOnce;
      initCachesStub.restore();
      getTopicPartitionOffsetsStub.restore();
      setEventFunctionsStub.restore();
      getHourIdentifierStub.restore();
      kafkaStub.restore();
    });
  });

  describe('checkAccumulatedErrors', () => {
    let checkAccumulatedErrors;

    beforeEach(() => {
      checkAccumulatedErrors = kafkaUploaderProxy.__get__('checkAccumulatedErrors');
    });

    afterEach(() => {
      kafkaUploaderProxy.__set__('checkAccumulatedErrors', checkAccumulatedErrors);
    });

    it('send error in case error count greater then zero', () => {
      kafkaUploaderProxy.__set__('offsetOutOfRangeCount', 1);
      kafkaUploaderProxy.__set__('offsetFetchErrorCount', 1);
      const sendAlerts = sinon.stub(helper, 'sendAlerts');
      checkAccumulatedErrors();
      sendAlerts.restore();
      expect(sendAlerts).to.have.been.calledTwice;
    });

    it('don\'t send error in case error count zero', () => {
      kafkaUploaderProxy.__set__('offsetOutOfRangeCount', 0);
      kafkaUploaderProxy.__set__('offsetFetchErrorCount', 0);
      const sendAlerts = sinon.stub(helper, 'sendAlerts');
      checkAccumulatedErrors();
      sendAlerts.restore();
      expect(sendAlerts).not.to.have.been.calledOnce;
    });
  });
});
