'use strict';

const {
  describe, beforeEach, afterEach, it,
} = require('mocha');
const rewire = require('rewire');
const { assert, expect } = require('chai');
const sinon = require('sinon');
const path = require('path');
const constants = require('../../../constants');
const fs = require('fs');

const kafkaConfig = constants.kafkaConfig;

let kafkaHelperProxy;

describe('kafka Helper', () => {
  beforeEach(() => {
    kafkaHelperProxy = rewire('../../../apps/kafkaUploader/kafkaHelper');
  });

  describe('generate kafka topic v2 according to environment', () => {
    it('returns testing prefix for testing environments', (done) => {
      const kafkaTopicMaps = kafkaHelperProxy.generateFileSuffixForKafkaTopic();
      const testingMap = {
        testing_raw_logs: '-logs-v2.txt',
        testing_raw_extended_duration_logs: '-logs-v2.txt',
        testing_console_logs: '-console-logs-v2.txt',
        testing_performance_logs: '-performance-logs-v2.txt',
        testing_instrumentation_logs: '-instrumentation-logs-v2.txt',
      };
      assert(JSON.stringify(kafkaTopicMaps) === JSON.stringify(testingMap));
      done();
    });
  });

  describe('getPathForMessage', () => {
    const mesageObj = {};
    let topic;

    beforeEach(() => {
      mesageObj.value = {
        rails_session_id: '1234',
        requestId: 'req_1234',
      };
      topic = kafkaHelperProxy.__get__('topic');

      kafkaHelperProxy.__set__('topic', 'testing_raw_logs');
    });

    afterEach(() => {
      kafkaHelperProxy.__set__('topic', topic);
    });

    Object.keys(kafkaConfig.LOG_DATA_MESSAGE_TYPE).forEach((keyType) => {
      it(`should give appropriate filename for ${keyType}`, () => {
        const localMessageObj = { ...mesageObj };
        const valueType = kafkaConfig.LOG_DATA_MESSAGE_TYPE[keyType];
        localMessageObj.value.type = valueType;
        const prefix = kafkaHelperProxy.getLogsDirPath(mesageObj.value.rails_session_id);
        const relativePath = valueType >= 4 ? '1234-req_1234' : '1234-logs-v2.txt';
        const assertValue = path.join(prefix, relativePath);
        const data = kafkaHelperProxy.getPathForMessage(localMessageObj);
        assert(data === assertValue, `appropriate filename for ${keyType} does not match`);
      });
    });

    it('should give undefined filename for some random key', () => {
      const localMessageObj = { ...mesageObj };
      const valueType = 123;
      localMessageObj.value.type = valueType;
      const data = kafkaHelperProxy.getPathForMessage(localMessageObj);
      expect(data).to.be.undefined;
    });
  });

  describe('getWriteFdForPath', () => {
    let dirPath;
    let filePath;
    let fdCacheStub;

    beforeEach(() => {
      dirPath = 'sample_dir_path';
      filePath = 'sample_file_path';

      fdCacheStub = {
        get: sinon.stub(),
        ttl: sinon.stub(),
        set: sinon.stub(),
      };
    });

    it('should return the file descriptor', () => {
      fdCacheStub.get.returns(null);

      const fdResult = {};
      sinon.stub(fs, 'openSync').returns(fdResult);
      sinon.stub(fs, 'existsSync').returns(true);
      sinon.spy(fs, 'mkdirSync');

      const fd = kafkaHelperProxy.getWriteFdForPath(dirPath, filePath, fdCacheStub);

      assert(fd === fdResult);
      assert(fs.mkdirSync.notCalled);
      assert(fdCacheStub.ttl.notCalled);

      fs.existsSync.restore();
      fs.openSync.restore();
      fs.mkdirSync.restore();
    });

    it('should create the directory if not present', () => {
      fdCacheStub.get.returns(null);

      const fdResult = {};
      sinon.stub(fs, 'openSync').returns(fdResult);
      sinon.stub(fs, 'existsSync').returns(false);
      sinon.stub(fs, 'mkdirSync', () => { });

      const fd = kafkaHelperProxy.getWriteFdForPath(dirPath, filePath, fdCacheStub);

      assert(fd === fdResult);
      assert(fs.mkdirSync.calledOnce);
      assert(fdCacheStub.ttl.notCalled);

      fs.existsSync.restore();
      fs.openSync.restore();
      fs.mkdirSync.restore();
    });

    it('should return from the cache if present', () => {
      const fdResult = {};
      fdCacheStub.get.returns(fdResult);

      const fd = kafkaHelperProxy.getWriteFdForPath(dirPath, filePath, fdCacheStub);

      assert(fd === fdResult);
      assert(fdCacheStub.ttl.calledOnce);
    });
  });

  describe('getFailedLogsDirPath', () => {
    let getFailedLogsDirPath;

    beforeEach(() => {
      getFailedLogsDirPath = kafkaHelperProxy.__get__('getFailedLogsDirPath');
    });

    it('should generate correct failed logs path for sessionId when passed', () => {
      const pathName = getFailedLogsDirPath('1234');

      expect(pathName).to.have.string('logs/1234');
    });

    it('should generate correct failed logs path when sessionId is not passed', () => {
      const pathName = getFailedLogsDirPath();

      expect(pathName).to.have.string('logs');
    });
  });
});
