'use strict';

const {
  describe, beforeEach, afterEach, it,
} = require('mocha');
const childProcess = require('child_process');
const { assert, expect } = require('chai');
const constants = require('../../../constants');
const sinon = require('sinon');

const preStartChecks = require('../../../apps/kafkaUploader/preStartChecks');
const helper = require('../../../helper');


describe('safeSendAlerts', async () => {
  it('calls helper.sendAlerts with given params', async () => {
    const sendAlerts = sinon.stub(helper, 'sendAlerts').returns(Promise.resolve('abcd'));
    await preStartChecks.safeSendAlerts('subject1', 'message1', ['abc', 'def']);
    sinon.assert.calledOnce(sendAlerts);
    sinon.assert.calledWith(sendAlerts, 'subject1', 'message1', ['abc', 'def']);
    sendAlerts.restore();
  });

  it('does not raise error if helper.sendAlerts throws error', async () => {
    const sendAlerts = sinon.stub(helper, 'sendAlerts').returns(Promise.reject(new Error('xyz')));
    let e;
    try {
      await preStartChecks.safeSendAlerts('subject1', 'message1', ['abc', 'def']);
    } catch (error) {
      e = error;
    }
    expect(e).to.eql(undefined);
    sinon.assert.calledOnce(sendAlerts);
    sinon.assert.calledWith(sendAlerts, 'subject1', 'message1', ['abc', 'def']);
    sendAlerts.restore();
  });
});

describe('checkDiskUsage', async () => {
  let safeSendAlertsStub;

  beforeEach(() => {
    safeSendAlertsStub = sinon.stub(preStartChecks, 'safeSendAlerts');
  });

  afterEach(() => {
    safeSendAlertsStub.restore();
  });

  it('returns object with key capableToBecome = true if disk is less than threshold', async () => {
    const execStub = sinon.stub(childProcess, 'exec').yields(null, { stdout: `${constants.UPLOADER_DISK_USAGE_THRESHOLD - 1}` });
    assert.deepEqual(await preStartChecks.checkDiskUsage(), {
      capableToBecome: true,
      message: `Disk usage: ${constants.UPLOADER_DISK_USAGE_THRESHOLD - 1}, threshold: ${constants.UPLOADER_DISK_USAGE_THRESHOLD}`,
    });
    sinon.assert.calledOnce(execStub);
    sinon.assert.calledWith(execStub, 'df -P /ebs/kafka/saving | grep /ebs | awk \'{ print $5 }\' | sed \'s/%//g\' | tr -d \'\n\'', { timeout: 5000 });
    sinon.assert.notCalled(safeSendAlertsStub);
    execStub.restore();
  });

  it('returns object with key capableToBecome = true if disk is grater or equal to threshold', async () => {
    const execStub = sinon.stub(childProcess, 'exec').yields(null, { stdout: `${constants.UPLOADER_DISK_USAGE_THRESHOLD + 1}` });
    assert.deepEqual(await preStartChecks.checkDiskUsage(), {
      capableToBecome: false,
      message: `Disk usage: ${constants.UPLOADER_DISK_USAGE_THRESHOLD + 1}, threshold: ${constants.UPLOADER_DISK_USAGE_THRESHOLD}`,
    });
    sinon.assert.calledOnce(execStub);
    sinon.assert.calledWith(execStub, 'df -P /ebs/kafka/saving | grep /ebs | awk \'{ print $5 }\' | sed \'s/%//g\' | tr -d \'\n\'', { timeout: 5000 });
    sinon.assert.notCalled(safeSendAlertsStub);
    execStub.restore();
  });

  it('returns object with key capableToBecome = true if system command parsing error occurs', async () => {
    const execStub = sinon.stub(childProcess, 'exec').yields(null, { stdout: 'i am not a number' });
    assert.deepEqual(await preStartChecks.checkDiskUsage(), {
      capableToBecome: true,
      message: 'Calculated disk usage is not a number',
    });
    sinon.assert.calledOnce(execStub);
    sinon.assert.calledWith(execStub, 'df -P /ebs/kafka/saving | grep /ebs | awk \'{ print $5 }\' | sed \'s/%//g\' | tr -d \'\n\'', { timeout: 5000 });
    sinon.assert.calledOnce(safeSendAlertsStub);
    sinon.assert.calledWith(safeSendAlertsStub, sinon.match('BecomeMasterCheck Exception: Disk check command error'), 'Calculated disk usage is not a number');
    execStub.restore();
  });

  it('returns object with key capableToBecome = true if system command returns stderr', async () => {
    const execStub = sinon.stub(childProcess, 'exec').yields(null, { stdout: null, stderr: 'some err' });
    assert.deepEqual(await preStartChecks.checkDiskUsage(), {
      capableToBecome: true,
      message: 'Calculated disk usage is not a number',
    });
    sinon.assert.calledOnce(execStub);
    sinon.assert.calledWith(execStub, 'df -P /ebs/kafka/saving | grep /ebs | awk \'{ print $5 }\' | sed \'s/%//g\' | tr -d \'\n\'', { timeout: 5000 });
    sinon.assert.calledOnce(safeSendAlertsStub);
    sinon.assert.calledWith(safeSendAlertsStub, sinon.match('BecomeMasterCheck Exception: Disk check command error'), 'Calculated disk usage is not a number');
    execStub.restore();
  });

  it('raises error if system command raises error or exits', async () => {
    const execStub = sinon.stub(childProcess, 'exec').throws(new Error('some error'));
    let err;
    try {
      await preStartChecks.checkDiskUsage();
    } catch (e) {
      err = e;
    }
    expect(err).not.to.eql(undefined);
    expect(err.message).to.eql('some error');
    sinon.assert.calledOnce(execStub);
    sinon.assert.calledWith(execStub, 'df -P /ebs/kafka/saving | grep /ebs | awk \'{ print $5 }\' | sed \'s/%//g\' | tr -d \'\n\'', { timeout: 5000 });
    execStub.restore();
  });

  it('raises error if system command returns error', async () => {
    const execStub = sinon.stub(childProcess, 'exec').yields(new Error('some error1'));
    let err;
    try {
      await preStartChecks.checkDiskUsage();
    } catch (e) {
      err = e;
    }
    expect(err).not.to.eql(undefined);
    expect(err.message).to.eql('some error1');
    sinon.assert.calledOnce(execStub);
    sinon.assert.calledWith(execStub, 'df -P /ebs/kafka/saving | grep /ebs | awk \'{ print $5 }\' | sed \'s/%//g\' | tr -d \'\n\'', { timeout: 5000 });
    execStub.restore();
  });
});

describe('capableToStartUploader', async () => {
  let safeSendAlertsStub;

  beforeEach(() => {
    safeSendAlertsStub = sinon.stub(preStartChecks, 'safeSendAlerts');
  });

  afterEach(() => {
    safeSendAlertsStub.restore();
  });

  it('returns true if disk is less than threshold', async () => {
    const execStub = sinon.stub(childProcess, 'exec').yields(null, { stdout: `${constants.UPLOADER_DISK_USAGE_THRESHOLD - 1}` });
    expect(await preStartChecks.capableToStartUploader('host', 'tag')).to.eql(true);
    sinon.assert.calledOnce(execStub);
    sinon.assert.calledWith(execStub, 'df -P /ebs/kafka/saving | grep /ebs | awk \'{ print $5 }\' | sed \'s/%//g\' | tr -d \'\n\'', { timeout: 5000 });
    sinon.assert.notCalled(safeSendAlertsStub);
    execStub.restore();
  });

  it('returns false if disk is grater or equal to threshold', async () => {
    const execStub = sinon.stub(childProcess, 'exec').yields(null, { stdout: `${constants.UPLOADER_DISK_USAGE_THRESHOLD + 1}` });
    expect(await preStartChecks.capableToStartUploader('host', 'tag')).to.eql(false);
    sinon.assert.calledOnce(execStub);
    sinon.assert.calledWith(execStub, 'df -P /ebs/kafka/saving | grep /ebs | awk \'{ print $5 }\' | sed \'s/%//g\' | tr -d \'\n\'', { timeout: 5000 });
    sinon.assert.calledOnce(safeSendAlertsStub);
    sinon.assert.calledWith(safeSendAlertsStub, sinon.match('BecomeMasterCheck : Cannot become master'), sinon.match.any);
    execStub.restore();
  });

  it('returns true if system command parsing error occurs', async () => {
    const execStub = sinon.stub(childProcess, 'exec').yields(null, { stdout: 'i am not a number' });
    expect(await preStartChecks.capableToStartUploader('host', 'tag')).to.eql(true);
    sinon.assert.calledOnce(execStub);
    sinon.assert.calledWith(execStub, 'df -P /ebs/kafka/saving | grep /ebs | awk \'{ print $5 }\' | sed \'s/%//g\' | tr -d \'\n\'', { timeout: 5000 });
    sinon.assert.calledOnce(safeSendAlertsStub);
    sinon.assert.calledWith(safeSendAlertsStub, sinon.match('BecomeMasterCheck Exception: Disk check command error'), 'Calculated disk usage is not a number');
    execStub.restore();
  });

  it('returns true if system command raises error', async () => {
    const execStub = sinon.stub(childProcess, 'exec').throws(new Error('some error'));
    expect(await preStartChecks.capableToStartUploader('host', 'tag')).to.eql(true);
    sinon.assert.calledOnce(execStub);
    sinon.assert.calledWith(execStub, 'df -P /ebs/kafka/saving | grep /ebs | awk \'{ print $5 }\' | sed \'s/%//g\' | tr -d \'\n\'', { timeout: 5000 });
    sinon.assert.calledOnce(safeSendAlertsStub);
    sinon.assert.calledWith(safeSendAlertsStub, sinon.match('BecomeMasterCheck EXCEPTION:'), sinon.match('some error'));
    execStub.restore();
  });
});
