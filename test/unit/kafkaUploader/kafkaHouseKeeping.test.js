'use strict';

/* eslint-disable no-underscore-dangle */
/* eslint-disable global-require */

const {
  describe, it, beforeEach, afterEach,
} = require('mocha');
const sinon = require('sinon');
const { assert } = require('chai');
const rewire = require('rewire');
const helper = require('../../../helper');
const constants = require('../../../constants');

let kafkaUploaderProxy;
let logger;

describe('Kafka Housekeeping functions', () => {
  let kafkaS3pipeline;

  beforeEach(() => {
    process.env.CONSUMER_TOPIC = '';
    process.env.CONSUMER_ID = '';
    process.env.COMMON_KAFKA_TOPIC_REGION = '';

    // This is done because we have the initialization of usage of some env
    // variable while making the module tree at the time of _require_.
    kafkaUploaderProxy = rewire('../../../apps/kafkaUploader/kafkaUploader');

    logger = kafkaUploaderProxy.__get__('logger');

    kafkaS3pipeline = kafkaUploaderProxy.__get__('kafkaS3Pipeline');
    kafkaS3pipeline.pause();
  });

  afterEach(() => {
    delete process.env.CONSUMER_TOPIC;
    delete process.env.CONSUMER_ID;
    delete process.env.COMMON_KAFKA_TOPIC_REGION;

    kafkaS3pipeline.kill();
  });

  describe('uploadsInProgress', () => {
    beforeEach(() => {
      for (let idx = 0; idx < 10; idx += 1) {
        const filename = `filename_${idx}`;
        kafkaS3pipeline.push(filename);
      }
    });

    afterEach(() => {
      kafkaS3pipeline.kill();
    });

    it('should print current uploads in queue', () => {
      const result = kafkaS3pipeline.length();
      assert(result === 10);
    });

    it('should print current uploads in processing', () => {
      const result = kafkaS3pipeline.running();

      assert(result === 0);
    });
  });

  describe('checkHighUploads', () => {
    let checkHighUploads;

    beforeEach(() => {
      checkHighUploads = kafkaUploaderProxy.__get__('checkHighUploads');

      for (let idx = 0; idx < 1001; idx += 1) {
        const filename = `filename_${idx}`;
        kafkaS3pipeline.push(filename);
      }

      sinon.stub(helper, 'sendAlerts', () => {});
    });

    afterEach(() => {
      helper.sendAlerts.restore();
    });

    it('should send alert when high upload count in production mode', () => {
      constants.isProductionEnv = true;
      checkHighUploads();

      assert(helper.sendAlerts.calledOnce);

      constants.isProductionEnv = false;
    });

    it('should not send alert when high upload count in non-production mode', () => {
      constants.isProductionEnv = false;
      checkHighUploads();

      assert(helper.sendAlerts.notCalled);
    });
  });

  describe('exitFunction', () => {
    let exitFunction;
    let commitOffsetSpy;
    let uploadSpy;
    let clock;

    beforeEach(() => {
      exitFunction = kafkaUploaderProxy.__get__('exitFunction');
      commitOffsetSpy = sinon.stub();
      uploadSpy = sinon.stub();
      sinon.spy(logger, 'warn');

      kafkaUploaderProxy.__set__('commitOffsets', commitOffsetSpy);

      clock = sinon.useFakeTimers();
    });

    afterEach(() => {
      clock.restore();
      logger.warn.restore();
    });

    it('should exit the process as the upload count is zero', async () => {
      uploadSpy.onCall(0).returns(0);
      await exitFunction();

      assert(process.exit.calledOnce);
      assert(logger.warn.calledTwice);
    });
  });
});
