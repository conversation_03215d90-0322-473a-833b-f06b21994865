'use strict';

/* eslint-disable no-underscore-dangle */
/* eslint-disable global-require */
/* eslint-disable no-unused-expressions */

const {
  describe, it, beforeEach, afterEach,
} = require('mocha');
const rewire = require('rewire');
const sinon = require('sinon');
const { assert } = require('chai');
const fs = require('fs');

let kafkaUploaderProxy;

describe('#onMessageCallback', () => {
  beforeEach(() => {
    process.env.CONSUMER_TOPIC = '';
    process.env.CONSUMER_ID = '';
    process.env.COMMON_KAFKA_TOPIC_REGION = '';

    kafkaUploaderProxy = rewire('../../../apps/kafkaUploader/kafkaUploader');
  });

  afterEach(() => {
    delete process.env.CONSUMER_TOPIC;
    delete process.env.CONSUMER_ID;
    delete process.env.COMMON_KAFKA_TOPIC_REGION;
  });

  describe('process message', () => {
    let handleStub;
    let onMessageCallback;
    let initCaches;
    let processedMessages;

    beforeEach(() => {
      onMessageCallback = kafkaUploaderProxy.__get__('onMessageCallback');
      initCaches = kafkaUploaderProxy.__get__('initCaches');
      handleStub = sinon.stub();
      sinon.stub(fs, 'writeSync', () => { });

      kafkaUploaderProxy.__set__('handle', handleStub);
    });

    afterEach(() => {
      fs.writeSync.restore();
    });

    it('should register message when handled correctly', () => {
      const message = {
        value: JSON.stringify({ rails_session_id: 'XYZ', requestId: 0, type: 3 }),
        partition: 0,
        offset: 0,
      };
      handleStub.returns(true);
      initCaches();
      onMessageCallback(message);
      processedMessages = kafkaUploaderProxy.__get__('processedMessages');
      assert(processedMessages.keys().length === 1);
    });

    it('should not register message when handled correctly', () => {
      const message = {
        value: JSON.stringify({ rails_session_id: 'XYZ', requestId: 0, type: 3 }),
        partition: 0,
        offset: 0,
      };
      handleStub.returns(false);
      initCaches();
      onMessageCallback(message);
      processedMessages = kafkaUploaderProxy.__get__('processedMessages');
      assert(processedMessages.keys().length === 0);
    });
  });
});
