'use strict';

/* eslint-disable no-underscore-dangle */
/* eslint-disable global-require */

const {
  describe, it, beforeEach, afterEach,
} = require('mocha');
const sinon = require('sinon');
const rewire = require('rewire');
const { assert } = require('chai');
const redisClient = require('../../../redisUtils').redisClient;
const fs = require('fs');

let kafkaUploader;
let kafkaUploaderProxy;
let kafkaHelper;
let logger;

describe('Offset management helpers', () => {
  let helperSpy;

  beforeEach(() => {
    process.env.CONSUMER_TOPIC = '';
    process.env.CONSUMER_ID = '';
    process.env.COMMON_KAFKA_TOPIC_REGION = '';

    // This is done because we have the initialization of usage of some env
    // variable while making the module tree at the time of _require_.
    kafkaUploader = require('../../../apps/kafkaUploader/kafkaUploader');
    kafkaUploaderProxy = rewire('../../../apps/kafkaUploader/kafkaUploader');

    helperSpy = {
      fsWriteFileAsync: sinon.spy(),
      fsMkdirAsync: sinon.spy(),
    };

    kafkaUploaderProxy.__set__('kafkaHelper', helperSpy);
    logger = kafkaUploaderProxy.__get__('logger');
  });

  afterEach(() => {
    delete process.env.CONSUMER_TOPIC;
    delete process.env.CONSUMER_ID;
    delete process.env.COMMON_KAFKA_TOPIC_REGION;

    kafkaUploaderProxy.__set__('kafkaHelper', kafkaHelper);
  });

  describe('getHourIdentifier', () => {
    let clock;
    let hourIdentifier;

    beforeEach(() => {
      const date = new Date(1970, 0, 1, 1);
      clock = sinon.useFakeTimers(date.getTime());
      sinon.stub(kafkaUploader, 'startKafkaUploader', () => {});
      hourIdentifier = kafkaUploaderProxy.__get__('getHourIdentifier');
    });

    afterEach(() => {
      clock.restore();
      kafkaUploader.startKafkaUploader.restore();
    });

    it('should provide time at the start of epoch', () => {
      assert(hourIdentifier() === '1970_1_1_1');
    });

    it('should provide time at the start of epoch after tick', () => {
      clock.tick(24 * 60 * 60 * 1000);
      assert(hourIdentifier(24) === '1970_1_1_1');
    });

    it('should provide time at the next tick', () => {
      clock.tick(24 * 60 * 60 * 1000);
      assert(hourIdentifier() === '1970_1_2_1');
    });
  });

  describe('save offsets to redis', () => {
    let writeOffsetsToSet;
    let sampleParitionOffsetsCommitted;

    beforeEach(() => {
      writeOffsetsToSet = kafkaUploaderProxy.__get__('writeOffsetsToSet');
      sampleParitionOffsetsCommitted = {
        offset_0: {},
        offset_1: {},
      };
    });

    afterEach(() => {
      kafkaUploaderProxy.__set__('partitionOffsetCommitted', {});
    });

    it('should call the appropriate redis methods', async () => {
      kafkaUploaderProxy.__set__('partitionOffsetCommitted', sampleParitionOffsetsCommitted);
      const hmsetStub = sinon.stub(redisClient, 'hmset');
      const expireStub = sinon.stub(redisClient, 'expire');
      await writeOffsetsToSet();

      assert(hmsetStub.calledOnce);
      assert(expireStub.calledOnce);

      hmsetStub.restore();
      expireStub.restore();
    });

    it('should log error when error in setting hash key', async () => {
      kafkaUploaderProxy.__set__('partitionOffsetCommitted', sampleParitionOffsetsCommitted);
      const errObj = new Error('Unable to process command');
      const hmsetStub = sinon.stub(redisClient, 'hmset');
      hmsetStub.throws(errObj);
      const expireStub = sinon.stub(redisClient, 'expire');
      sinon.spy(logger, 'error');

      await writeOffsetsToSet();

      assert(hmsetStub.calledOnce);
      assert(expireStub.notCalled);
      assert(logger.error.calledOnce);

      hmsetStub.restore();
      expireStub.restore();
      logger.error.restore();
    });

    it('should log error when setting expire in redis', async () => {
      kafkaUploaderProxy.__set__('partitionOffsetCommitted', sampleParitionOffsetsCommitted);
      const errObj = new Error('Unable to process command');
      const hmsetStub = sinon.stub(redisClient, 'hmset');
      const expireStub = sinon.stub(redisClient, 'expire');
      expireStub.throws(errObj);
      sinon.spy(logger, 'error');

      await writeOffsetsToSet();

      assert(hmsetStub.calledOnce);
      assert(expireStub.calledOnce);
      assert(logger.error.calledOnce);

      hmsetStub.restore();
      expireStub.restore();
      logger.error.restore();
    });

    it('should early return when offsetCommitted is empty', async () => {
      kafkaUploaderProxy.__set__('partitionOffsetCommitted', {});
      const hmsetStub = sinon.stub(redisClient, 'hmset');
      const expireStub = sinon.stub(redisClient, 'expire');
      sinon.spy(logger, 'info');
      sinon.spy(logger, 'error');

      await writeOffsetsToSet();

      assert(logger.info.calledOnce);
      assert(logger.error.notCalled);
      assert(hmsetStub.notCalled);
      assert(expireStub.notCalled);

      hmsetStub.restore();
      expireStub.restore();
      logger.info.restore();
      logger.error.restore();
    });
  });

  describe('save offsets to file', () => {
    let writeOffsetsToFile;

    beforeEach(() => {
      // kafkaUploaderProxy.__set__('kafkaHelper', null);
      writeOffsetsToFile = kafkaUploaderProxy.__get__('writeOffsetsToFile');
      // fsWriteFileAsync = kafkaHelperProxy.__get__('fsWriteFileAsync');
      // fsMkdirAsync = kafkaHelperProxy.__get__('fsMkdirAsync');

      kafkaUploaderProxy.__set__('partitionOffsetCommitted', {});
    });

    it('should create the directory if not present and write to the file', async () => {
      sinon.stub(fs, 'existsSync', () => false);

      await writeOffsetsToFile();

      assert(helperSpy.fsMkdirAsync.calledOnce, 'fsMkdirAsync not called');
      assert(helperSpy.fsWriteFileAsync.calledOnce, 'fsWriteFileAsync not called');

      fs.existsSync.restore();
    });

    it('should not create directory if already exists', async () => {
      sinon.stub(fs, 'existsSync', () => true);

      await writeOffsetsToFile();

      assert(helperSpy.fsMkdirAsync.notCalled, 'fsMkdirAsync called');
      assert(helperSpy.fsWriteFileAsync.calledOnce, 'fsWriteFileAsync not called');

      fs.existsSync.restore();
    });

    it('should handle error when writing file', async () => {
      sinon.stub(fs, 'existsSync', () => false);
      sinon.spy(logger, 'error');

      function processErr() { throw new Error('Unable to process command'); }
      helperSpy.fsWriteFileAsync = sinon.spy(processErr);

      await writeOffsetsToFile();

      assert(helperSpy.fsMkdirAsync.calledOnce, 'fsMkdirAsync not called');
      assert(helperSpy.fsWriteFileAsync.calledOnce, 'fsWriteFileAsync not called');
      assert(logger.error.calledOnce);

      fs.existsSync.restore();
      logger.error.restore();
    });
  });

  describe('commiting offsets', () => {
    let commitOffsets;
    let writeOffsetsToFile;
    let writeOffsetsToSet;

    beforeEach(() => {
      commitOffsets = kafkaUploaderProxy.__get__('commitOffsets');
      writeOffsetsToSet = kafkaUploaderProxy.__get__('writeOffsetsToSet');
      writeOffsetsToFile = kafkaUploaderProxy.__get__('writeOffsetsToFile');
    });

    it('should call the functions in order', async () => {
      const writeToSetSpy = sinon.spy();
      const writeToFileSpy = sinon.spy();

      kafkaUploaderProxy.__set__('writeOffsetsToFile', writeToFileSpy);
      kafkaUploaderProxy.__set__('writeOffsetsToSet', writeToSetSpy);

      await commitOffsets();

      assert(writeToSetSpy.calledTwice);
      assert(writeToFileSpy.calledOnce);

      kafkaUploaderProxy.__set__('writeOffsetsToSet', writeOffsetsToSet);
      kafkaUploaderProxy.__set__('writeOffsetsToFile', writeOffsetsToFile);
    });
  });
});
