/* eslint-disable max-lines-per-function */

'use strict';

const rewire = require('rewire');
const HubLogger = require('../../../../log');
const requestlib = require('../../../../lib/request');
const testHelper = require('../../helper');
const pubSub = require('../../../../pubSub');
const sinon = require('sinon');
const { assert, expect } = require('chai');
const constants = require('../../../../constants');

const rewiredBrowserActions = rewire('../../../../services/selenium/browserActions');

describe('browserActions SeleniumInterface tests', () => {
  const sessionKeyObj = testHelper.getKeyObject();
  sessionKeyObj.port = 1234;
  const script = 'some script';

  describe('generateCheckPageLoadOptions', () => {
    const generateCheckPageLoadOptions = rewiredBrowserActions.__get__('generateCheckPageLoadOptions');

    it('returns params for checkingPageLoad call', () => {
      const expectedOptions = {
        agent: sinon.match.any,
        body: JSON.stringify({
          script,
          args: [],
        }),
        headers: {
          'BStack-Host': sessionKeyObj.name,
          accept: 'application/json',
          'accept-encoding': 'gzip;q=1.0,deflate;q=0.6,identity;q=0.3',
          'content-length': 34,
          'content-type': 'application/json; charset=utf-8',
        },
        hostname: sessionKeyObj.rproxyHost,
        method: 'POST',
        path: '/wd/hub/session/zy-xwvu-tsrq-ponm-lkji-hgfe-dcba/execute',
        port: 1234,
        timeout: 10000,
      };
      const res = generateCheckPageLoadOptions(sessionKeyObj, script);
      sinon.assert.match(res, expectedOptions);
    });
  });

  describe('checkIssuesWithTitle', () => {
    const checkIssuesWithTitle = rewiredBrowserActions.__get__('checkIssuesWithTitle');
    let miscLogger;
    let publish;

    beforeEach(() => {
      miscLogger = sinon.stub(HubLogger, 'miscLogger');
      publish = sinon.stub(pubSub, 'publish');
    });

    afterEach(() => {
      miscLogger.restore();
      publish.restore();
    });

    describe('when data.value is array, checkTitleReadyState is truthy and title is among issue', () => {
      it('logs, publishes to pubSub and updates sessionKeyObj.udpKeys.local_5xx to 1', () => {
        const data = {
          value: ['a', 'b', 'Internal Server Error'],
        };
        const sessionKeyObj1 = testHelper.getKeyObject();
        sessionKeyObj1.udpKeys = { key1: 'val1' };

        checkIssuesWithTitle(data, true, sessionKeyObj1);
        sinon.assert.calledOnce(miscLogger);
        sinon.assert.calledWith(miscLogger, 'checkPageLoadOnTerminal', `SessionId: ${sessionKeyObj.rails_session_id} Got Title Internal Server Error`, sinon.match.any);
        sinon.assert.calledOnce(publish);
        sinon.assert.calledWith(publish, constants.updateKeyObject, {
          session: sessionKeyObj1.rails_session_id,
          changed: {
            udpKeys: sessionKeyObj1.udpKeys,
          },
        });
        expect(sessionKeyObj1.udpKeys.local_5xx).to.eql(1);
      });
    });

    describe('no issues with title cases - returns without logging or publishing', () => {
      it('when data or data.value is falsy or data.value is not array', () => {
        const data = undefined;
        const data1 = {
          value: undefined,
        };
        const data2 = {
          value: 1234,
        };
        sessionKeyObj.udpKeys = {};
        checkIssuesWithTitle(data, true, sessionKeyObj);
        checkIssuesWithTitle(data1, true, sessionKeyObj);
        checkIssuesWithTitle(data2, true, sessionKeyObj);
        sinon.assert.notCalled(miscLogger);
        sinon.assert.notCalled(publish);
        expect(sessionKeyObj.udpKeys.local_5xx).to.eql(undefined);
      });

      it('when checkTitleAndReadyState is falsy', () => {
        const data = {
          value: ['a', 'b', 'c'],
        };
        checkIssuesWithTitle(data, false, sessionKeyObj);
        sinon.assert.notCalled(miscLogger);
        sinon.assert.notCalled(publish);
        expect(sessionKeyObj.udpKeys.local_5xx).to.eql(undefined);
      });

      it('when title is not issue', () => {
        const data = {
          value: ['a', 'b', 'c'],
        };
        checkIssuesWithTitle(data, true, sessionKeyObj);
        sinon.assert.notCalled(miscLogger);
        sinon.assert.notCalled(publish);
        expect(sessionKeyObj.udpKeys.local_5xx).to.eql(undefined);
      });
    });
  });

  describe('checkPageLoad', () => {
    let stubgenerateCheckPageLoadOptions;
    let miscLogger;
    let publish;
    let generateCheckPageLoadOptionsObj;
    before(() => {
      generateCheckPageLoadOptionsObj = { generateCheckPageLoadOptions: rewiredBrowserActions.__get__('generateCheckPageLoadOptions') };
      stubgenerateCheckPageLoadOptions = sinon.stub(generateCheckPageLoadOptionsObj, 'generateCheckPageLoadOptions').returns({ key: 'val1' });
      rewiredBrowserActions.__set__('generateCheckPageLoadOptions', stubgenerateCheckPageLoadOptions);
    });
    after(() => {
      stubgenerateCheckPageLoadOptions.restore();
    });
    beforeEach(() => {
      miscLogger = sinon.stub(HubLogger, 'miscLogger');
      publish = sinon.stub(pubSub, 'publish');
    });

    afterEach(() => {
      miscLogger.restore();
      publish.restore();
    });

    it('returns obj with success true if terminal call to check page load returns value as complete or interactive', async () => {
      const parsedData = {
        value: ['complete', 'some title'],
      };
      const resp = {
        data: JSON.stringify(parsedData),
        statusCode: 200,
      };
      const requestLibCall = sinon.stub(requestlib, 'call').returns(Promise.resolve(resp));

      const res = await rewiredBrowserActions.checkPageLoad(sessionKeyObj, true, true);
      assert.deepEqual(res, {
        data: { value: 'complete' },
        res: resp,
        success: true,
      });
      sinon.assert.called(stubgenerateCheckPageLoadOptions);
      sinon.assert.calledWith(stubgenerateCheckPageLoadOptions, sessionKeyObj, 'return [document.readyState, document.title]');
      sinon.assert.calledOnce(requestLibCall);
      sinon.assert.calledWith(requestLibCall, { key: 'val1' });
      sinon.assert.calledThrice(miscLogger);
      sinon.assert.calledWith(miscLogger, 'checkPageLoadOnTerminal', `sessionId: ${sessionKeyObj.rails_session_id} status: success ${JSON.stringify('complete').substring(0, 20)}`, sinon.match.any);
      sinon.assert.neverCalledWith(miscLogger, 'checkPageLoadOnTerminal', `sessionId: ${sessionKeyObj.rails_session_id} status: error ${JSON.stringify((undefined) || '')}`, sinon.match.any);
      requestLibCall.restore();
    });

    it('returns obj with success true if terminal call to check page load returns value not as complete, interactive, or loading', async () => {
      const parsedData = {
        value: ['not loading', 'some title'],
      };
      const resp = {
        data: JSON.stringify(parsedData),
        statusCode: 200,
      };
      const requestLibCall = sinon.stub(requestlib, 'call').returns(Promise.resolve(resp));

      const res = await rewiredBrowserActions.checkPageLoad(sessionKeyObj, true, true);
      assert.deepEqual(res, {
        data: { value: 'not loading' },
        res: resp,
        success: true,
      });
      sinon.assert.called(stubgenerateCheckPageLoadOptions);
      sinon.assert.calledWith(stubgenerateCheckPageLoadOptions, sessionKeyObj, 'return [document.readyState, document.title]');
      sinon.assert.calledOnce(requestLibCall);
      sinon.assert.calledWith(requestLibCall, { key: 'val1' });
      sinon.assert.calledWith(miscLogger, 'checkPageLoadOnTerminal', `sessionId: ${sessionKeyObj.rails_session_id} status: error ${JSON.stringify((undefined) || '')}`, sinon.match.any);

      requestLibCall.restore();
    });

    it('returns obj with success false if terminal call to check page load returns value loading', async () => {
      const parsedData = {
        value: ['loading', 'some title'],
      };
      const resp = {
        data: JSON.stringify(parsedData),
        statusCode: 200,
      };
      const requestLibCall = sinon.stub(requestlib, 'call').returns(Promise.resolve(resp));

      const res = await rewiredBrowserActions.checkPageLoad(sessionKeyObj, true, true);
      assert.deepEqual(res, {
        data: { value: 'loading' },
        res: resp,
        success: false,
      });
      sinon.assert.called(stubgenerateCheckPageLoadOptions);
      sinon.assert.calledWith(stubgenerateCheckPageLoadOptions, sessionKeyObj, 'return [document.readyState, document.title]');
      sinon.assert.calledOnce(requestLibCall);
      sinon.assert.calledWith(requestLibCall, { key: 'val1' });
      sinon.assert.calledWith(miscLogger, 'checkPageLoadOnTerminal', `sessionId: ${sessionKeyObj.rails_session_id} status: error ${JSON.stringify((undefined) || '')}`, sinon.match.any);

      requestLibCall.restore();
    });

    it('throws error if terminal call to check page load raises error', async () => {
      const requestLibCall = sinon.stub(requestlib, 'call').returns(Promise.reject(new Error('some error')));
      let err;
      try {
        await rewiredBrowserActions.checkPageLoad(sessionKeyObj, true, true);
      } catch (e) {
        err = e;
      }
      expect(err).to.eql(new Error('some error'));
      sinon.assert.called(stubgenerateCheckPageLoadOptions);
      sinon.assert.calledWith(stubgenerateCheckPageLoadOptions, sessionKeyObj, 'return [document.readyState, document.title]');
      sinon.assert.calledOnce(requestLibCall);
      sinon.assert.calledWith(requestLibCall, { key: 'val1' });

      requestLibCall.restore();
    });
  });

  describe('waitForPageLoad', () => {
    let checkPageLoadObj;
    let stubcheckPageLoad;

    before(() => {
      checkPageLoadObj = { checkPageLoad: rewiredBrowserActions.__get__('checkPageLoad') };
      stubcheckPageLoad = sinon.stub(checkPageLoadObj, 'checkPageLoad').returns(Promise.resolve({ success: true, res: 'some res' }));
      rewiredBrowserActions.__set__('checkPageLoad', stubcheckPageLoad);
    });
    after(() => {
      stubcheckPageLoad.restore();
    });

    it('resolves with success false if maxWait < 0', async () => {
      const res = await rewiredBrowserActions.waitForPageLoad(sessionKeyObj, -1);
      expect(res.success).to.eql(false);
    });

    it('resolves with checkPageLoad response if maxWait > 0', async () => {
      const res = await rewiredBrowserActions.waitForPageLoad(sessionKeyObj, 1);
      assert.deepEqual(res, { success: true, res: 'some res' });
    });
  });
});
