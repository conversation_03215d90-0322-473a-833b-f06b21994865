'use strict';

const selenium = require('../../../../services/selenium');
const testHelper = require('../../helper');
const HubLogger = require('../../../../log');
const requestlib = require('../../../../lib/request');
const sinon = require('sinon');
const { assert } = require('chai');

describe('SeleniumInterface tests', () => {
  describe('getOpenUrl', () => {
    const keyObject = testHelper.getKeyObject();

    it('should make request to terminal', async () => {
      const requestLibCall = sinon.stub(requestlib, 'call').returns(Promise.resolve('some_data'));
      const res = await selenium.getOpenUrl(keyObject);

      sinon.assert.calledOnce(requestLibCall);
      sinon.assert.calledWith(
        requestLibCall,
        sinon.match({
          hostname: keyObject.rproxyHost,
          port: keyObject.port,
          path: `/wd/hub/session/${keyObject.key}/url`,
          headers: { 'BStack-Host': 'hostname' },
        })
      );
      sinon.assert.pass(res === 'some_data');
      requestLibCall.restore();
    });

    it('should handle request failure', async () => {
      /* eslint-disable prefer-promise-reject-errors */
      const requestLibCall = sinon
        .stub(requestlib, 'call')
        .returns(Promise.reject({ type: 'a', error: 'some_data' }));
      const exceptionLogger = sinon.stub(HubLogger, 'exceptionLogger');
      const res = await selenium.getOpenUrl(keyObject);

      sinon.assert.calledOnce(requestLibCall);
      sinon.assert.calledWith(
        requestLibCall,
        sinon.match({
          hostname: keyObject.rproxyHost,
          port: keyObject.port,
          path: `/wd/hub/session/${keyObject.key}/url`,
          headers: { 'BStack-Host': 'hostname' },
        })
      );
      sinon.assert.calledOnce(exceptionLogger);
      sinon.assert.calledWith(exceptionLogger, 'GetURL Error', 'a while getURL some_data');
      sinon.assert.pass(res === undefined);
      exceptionLogger.restore();
      requestLibCall.restore();
    });
  });

  describe('getIOSLocation', () => {
    const sessionKeyObj = testHelper.getKeyObject();

    it('should return command not supported without calling terminal', async () => {
      const res = selenium.getIOSLocation(sessionKeyObj);
      const message =
        'Get geolocation command (https://appium.io/docs/en/commands/session/geolocation/get-geolocation/) is not currently supported on BrowserStack. Please reach out to the support team for any queries';
      assert.deepEqual(res, {
        sessionId: sessionKeyObj.rails_session_id,
        status: 13,
        value: { message },
      });
    });
  });

  describe('realIOSFileUpload', () => {
    const sessionKeyObj = testHelper.getKeyObject();

    it('should return status code 422 without calling terminal as file upload is not supported in real iOS devices', async () => {
      const res = selenium.realIOSFileUpload(sessionKeyObj);
      assert.deepEqual(res, {
        statusCode: 422,
        headers: {},
        data: JSON.stringify({
          state: 'real iOS File upload',
          sessionId: sessionKeyObj.key,
          value: { message: 'File upload is not supported on real iOS devices' },
          class: 'org.openqa.selenium.remote.Response',
          status: 13,
        }),
      });
    });
  });

  describe('chromeMacMaximize', () => {
    it('should return status 0 and success without calling terminal', async () => {
      const res = selenium.chromeMacMaximize();
      assert.deepEqual(res, {
        state: 'success',
        hCode: 1528908777,
        value: null,
        class: 'org.openqa.selenium.remote.Response',
        status: 0,
      });
    });
  });
  describe('iOSMaximize', () => {
    it('should return status 0 and success without calling terminal', async () => {
      const res = selenium.iOSMaximize();
      assert.deepEqual(res, {
        state: 'success',
        hCode: 1528908777,
        value: {
          height: 664, width: 390, x: 0, y: 0,
        },
        class: 'org.openqa.selenium.remote.Response',
        status: 0,
      });
    });
  });
});
