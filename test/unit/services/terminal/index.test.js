'use strict';

const terminal = require('../../../../services/terminal');
const testHelper = require('../../helper');
const requestlib = require('../../../../lib/request');
const sinon = require('sinon');
const { assert } = require('chai');

describe('TerminalInterface tests', () => {
  describe('getAutoItSendKeysParams', () => {
    const keyObject = testHelper.getKeyObject();
    const sendKeysWaitInterval = 5;
    const options = { timeout: 20 };
    const text = 'some_text *^#$';

    it('returns params for send keys command call', async () => {
      const val = terminal.getAutoItSendKeysParams(keyObject, sendKeysWaitInterval, options, text);
      sinon.assert.match(val, {
        method: 'GET',
        timeout: 20,
        hostname: 'rproxyHost',
        port: 4567,
        path: '/sendkeys?sleep=5&text=some_text%20*%5E%23%24',
        headers: { 'BStack-Host': 'hostname', 'X-Source-Env-Type': 'development' },
      });
    });
  });

  describe('autoItSendKeys', () => {
    const keyObject = testHelper.getKeyObject();
    const params = { clientSessionID: 'session_id1' };
    const options = { timeout: 20 };
    const text = 'some_text *^#$';

    it('should make sendKeys call to terminal and return true is succeeds', async () => {
      keyObject.autoitSendKeys = '5';
      const requestLibCall = sinon.stub(requestlib, 'call').returns(Promise.resolve({ data: 'some_data' }));
      const res = await terminal.autoItSendKeys(keyObject, params, options, text);
      sinon.assert.calledOnce(requestLibCall);
      sinon.assert.calledWith(requestLibCall, {
        headers: { 'BStack-Host': 'hostname', 'X-Source-Env-Type': 'development' },
        hostname: 'rproxyHost',
        method: 'GET',
        path: '/sendkeys?sleep=800&text=some_text%20*%5E%23%24',
        port: 4567,
        recordJarTime: true,
        timeout: 20,
      });
      assert.equal(res, true);
      requestLibCall.restore();
    });

    it('should raise error if sendKeys call to terminal error', async () => {
      keyObject.autoitSendKeys = '5';
      const requestLibCall = sinon.stub(requestlib, 'call').returns(Promise.reject(new Error('err1')));
      let res;
      try {
        await terminal.autoItSendKeys(keyObject, params, options, text);
      } catch (err) {
        res = err;
      }
      sinon.assert.calledOnce(requestLibCall);
      sinon.assert.calledWith(requestLibCall, {
        headers: { 'BStack-Host': 'hostname', 'X-Source-Env-Type': 'development' },
        hostname: 'rproxyHost',
        method: 'GET',
        path: '/sendkeys?sleep=800&text=some_text%20*%5E%23%24',
        port: 4567,
        recordJarTime: true,
        timeout: 20,
      });
      assert.notEqual(res, undefined);
      sinon.assert.match(res.message, 'err1');
      requestLibCall.restore();
    });

    it('should not make sendKeys call to terminal and return true if text is empty', async () => {
      keyObject.autoitSendKeys = '5';
      const requestLibCall = sinon.stub(requestlib, 'call');
      const res = await terminal.autoItSendKeys(keyObject, params, options, '');
      sinon.assert.notCalled(requestLibCall);
      sinon.assert.match(res, true);
      requestLibCall.restore();
    });
  });

  describe('basicAuthEdge', () => {
    const sessionKeyObject = testHelper.getKeyObject();
    const edgeUsername = 'some_user&*6@';
    const edgePassword = 'some_password^$^@#';

    it('should make request to terminal', async () => {
      const requestLibCall = sinon.stub(requestlib, 'call').returns(Promise.resolve('some_data'));
      const res = await terminal.basicAuthEdge(sessionKeyObject, edgeUsername, edgePassword);
      sinon.assert.calledOnce(requestLibCall);
      sinon.assert.calledWith(requestLibCall, sinon.match({
        method: 'GET',
        hostname: sessionKeyObject.rproxyHost,
        port: 4567,
        path: `/basic_auth_edge?username=some_user%26*6%40&password=some_password%5E%24%5E%40%23&browser_version=${encodeURIComponent(sessionKeyObject.browser_version)}`,
        headers: { 'BStack-Host': sessionKeyObject.name, 'X-Source-Env-Type': 'development' },
        recordJarTime: true,
      }));
      sinon.assert.match(res, 'some_data');
      requestLibCall.restore();
    });

    it('should raise error if call to terminal fails', async () => {
      const requestLibCall = sinon.stub(requestlib, 'call').returns(Promise.reject(new Error('some_error')));
      let errorMessage;
      try {
        await terminal.basicAuthEdge(sessionKeyObject, edgeUsername, edgePassword);
      } catch (e) {
        errorMessage = e.message;
      }
      sinon.assert.match(errorMessage, 'some_error');
      sinon.assert.calledOnce(requestLibCall);
      requestLibCall.restore();
    });
  });

  describe('cancelSafariPopup', () => {
    const keyObject = testHelper.getKeyObject();

    it('should make request to terminal', async () => {
      const requestLibCall = sinon.stub(requestlib, 'call').returns(Promise.resolve('some_data'));
      const res = await terminal.cancelSafariPopup(keyObject);

      sinon.assert.calledOnce(requestLibCall);
      sinon.assert.calledWith(requestLibCall, sinon.match({
        method: 'GET',
        hostname: keyObject.rproxyHost,
        port: 45671,
        path: '/safari_popup_cancel',
        headers: { 'BStack-Host': 'hostname', 'X-Source-Env-Type': 'development' },
        recordJarTime: true,
      }));
      sinon.assert.match(res, 'some_data');
      requestLibCall.restore();
    });

    it('should throw error on request failure', async () => {
      /* eslint-disable prefer-promise-reject-errors */
      const requestLibCall = sinon.stub(requestlib, 'call').returns(Promise.reject({ type: 'a', error: 'some_data' }));
      let res;
      try {
        res = await terminal.cancelSafariPopup(keyObject);
      } catch (e) {
        sinon.assert.match(e.error, 'some_data');
      }

      sinon.assert.calledOnce(requestLibCall);
      sinon.assert.calledWith(requestLibCall, sinon.match({
        method: 'GET',
        hostname: keyObject.rproxyHost,
        port: 45671,
        path: '/safari_popup_cancel',
        headers: { 'BStack-Host': 'hostname', 'X-Source-Env-Type': 'development' },
        recordJarTime: true,
      }));
      sinon.assert.match(res, undefined);
      requestLibCall.restore();
    });
  });

  describe('getIOSAppString', () => {
    const sessionKeyObj = testHelper.getKeyObject();
    sessionKeyObj.device = 'iPhone 6';
    sessionKeyObj.idle_timeout = 10;
    const requestStateObj = { req_data: JSON.stringify({ language: 'fr', stringFile: 'some_file' }) };
    const url = 'session/53dda0e36279a9d71d0fc0795c07c4d7bd1c914f/appium/app/strings';

    it('should make request to terminal', async () => {
      const requestLibCall = sinon.stub(requestlib, 'call').returns(Promise.resolve('some_data'));
      const res = await terminal.getIOSAppString(url, sessionKeyObj, requestStateObj);
      sinon.assert.calledOnce(requestLibCall);
      sinon.assert.calledWith(requestLibCall, sinon.match({
        hostname: sessionKeyObj.rproxyHost,
        port: 45671,
        path: `/app_strings?device=${encodeURIComponent(sessionKeyObj.device)}&automate_session_id=${encodeURIComponent(sessionKeyObj.rails_session_id)}&language=fr&string_file=some_file`,
        headers: sinon.match({ 'BStack-Host': sessionKeyObj.name, 'X-Source-Env-Type': 'development' }),
        timeout: 10000,
      }));
      sinon.assert.match(res, 'some_data');
      requestLibCall.restore();
    });

    it('should raise error if call to terminal fails', async () => {
      const requestLibCall = sinon.stub(requestlib, 'call').returns(Promise.reject(new Error('some_error')));
      let errorMessage;
      try {
        await terminal.getIOSAppString(url, sessionKeyObj, requestStateObj);
      } catch (e) {
        errorMessage = e.message;
      }
      sinon.assert.match(errorMessage, 'some_error');
      sinon.assert.calledOnce(requestLibCall);
      requestLibCall.restore();
    });

    it('should make request to terminal for mobile: extension as well', async () => {
      const requestLibCall = sinon.stub(requestlib, 'call').returns(Promise.resolve('some_data'));
      const urlMobile = '/session/b738879113ed11486e9e68f5c98e989d3ef7d698/execute/sync';
      const requestStateObjMobile = { req_data: JSON.stringify({ script: 'mobile: getAppStrings', args: [{ language: 'fr', stringFile: 'some_file' }] }) };
      const res = await terminal.getIOSAppString(urlMobile, sessionKeyObj, requestStateObjMobile);
      sinon.assert.calledOnce(requestLibCall);
      sinon.assert.calledWith(requestLibCall, sinon.match({
        hostname: sessionKeyObj.rproxyHost,
        port: 45671,
        path: `/app_strings?device=${encodeURIComponent(sessionKeyObj.device)}&automate_session_id=${encodeURIComponent(sessionKeyObj.rails_session_id)}&language=fr&string_file=some_file`,
        headers: sinon.match({ 'BStack-Host': sessionKeyObj.name, 'X-Source-Env-Type': 'development' }),
        timeout: 10000,
      }));
      sinon.assert.match(res, 'some_data');
      requestLibCall.restore();
    });
  });
});
