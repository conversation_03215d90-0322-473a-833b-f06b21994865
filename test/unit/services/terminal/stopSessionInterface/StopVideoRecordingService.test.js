'use strict';

const { StopVideoRecordingService } = require('../../../../../services/terminal/stopSessionInterface/StopVideoRecordingService');
const helper = require('../../../../../helper');
const HubLogger = require('../../../../../log');
const requestlib = require('../../../../../lib/request');
const { assert } = require('chai');
const sinon = require('sinon');

describe('StopVideoRecordingService tests', () => {
  describe('getStopVideoParams', () => {
    it('should create videoparams', () => {
      const stopSessionService = new StopVideoRecordingService({
        tunnel: true, captureCrash: true, isPlaywright: true, webDriverLogs: true,
      }, 'stopByBS');
      const videoparams = stopSessionService.getStopVideoParams();
      const params = {
        video: undefined,
        video_aws_keys: undefined,
        video_aws_secret: undefined,
        video_aws_bucket: undefined,
        video_aws_region: undefined,
        video_aws_storage_class: undefined,
        logs_new_bucketing: undefined,
        logs_aws_keys: undefined,
        logs_aws_secret: undefined,
        logs_aws_bucket: undefined,
        logs_aws_region: undefined,
        logs_aws_storage_class: undefined,
        video_file: undefined,
        upload_fg: false,
        logHost: 'zombiestaging.browserstack.com',
        edsHost: 'localhost',
        edsPort: 8553,
        edsKey: '123456',
        selenium_version: undefined,
        session_id: undefined,
        uploadWebDriverLogs: true,
        webdriver_logs_key: 'selenium-logs.txt',
        modifyLogsForAi: 'false',
        local: true,
        capture_crash: true,
        genre: 'playwright',
      };
      assert.deepEqual(videoparams, params);
    });

    it('should create videoparams', () => {
      const stopSessionService = new StopVideoRecordingService({}, 'stopByBS');
      const videoparams = stopSessionService.getStopVideoParams();
      const params = {
        video: undefined,
        video_aws_keys: undefined,
        video_aws_secret: undefined,
        video_aws_bucket: undefined,
        video_aws_region: undefined,
        video_aws_storage_class: undefined,
        logs_aws_storage_class: undefined,
        logs_new_bucketing: undefined,
        logs_aws_keys: undefined,
        logs_aws_secret: undefined,
        logs_aws_bucket: undefined,
        logs_aws_region: undefined,
        video_file: undefined,
        upload_fg: false,
        logHost: 'zombiestaging.browserstack.com',
        edsHost: 'localhost',
        edsPort: 8553,
        edsKey: '123456',
        selenium_version: undefined,
        session_id: undefined,
        uploadWebDriverLogs: undefined,
        webdriver_logs_key: 'selenium-logs.txt',
        modifyLogsForAi: 'false',
      };
      assert.deepEqual(videoparams, params);
    });
  });

  describe('getAttributesWhenBSF', () => {
    it('should extract session keyobject for start-error', () => {
      const sessionKeyObj = {
        browserstackParams: {
          'browserstack.video.aws.key': 'abcd',
          'browserstack.video.aws.secret': 'efgh',
          'browserstack.video.aws.s3bucket': 'abcd',
          'browserstack.video.aws.region': 'region',
          'browserstack.video.aws.storageclass': 'STANDARD',
          'browserstack.logs.aws.key': 'key',
          'browserstack.logs.aws.secret': 'secret',
          'browserstack.logs.aws.s3bucket': 's3bucket',
          'browserstack.logs.aws.storageclass': 'STANDARD',
          'browserstack.logs.aws.region': 'region',
          'browserstack.selenium_version': '4.0.0',
        },
        bsCaps: {
          new_bucketing: 'true',
          orig_os: 'win',
        },
        post_params: {
          u: 'random',
        },
        rails_session_id: 'abdf',
        host_name: '***********',
        user_id: '111',
        rproxyHost: '***********',
      };
      const stopSessionService = new StopVideoRecordingService(sessionKeyObj, 'start-error');
      const params = {
        video: false,
        video_aws_keys: 'abcd',
        video_aws_secret: 'efgh',
        video_aws_bucket: 'abcd',
        video_aws_region: 'region',
        video_file: 'video',
        video_aws_storage_class: 'STANDARD',
        logs_aws_storage_class: 'STANDARD',
        logs_new_bucketing: true,
        logs_aws_keys: 'key',
        logs_aws_secret: 'secret',
        logs_aws_bucket: 's3bucket',
        logs_aws_region: 'region',
        selenium_version: '4.0.0',
        captureCrash: true,
        tunnel: false,
        os: 'win',
        user: 'random',
        sessionId: 'abdf',
        rails_session_id: 'abdf',
        webDriverLogs: false,
        name: '***********',
        user_id: '111',
        rproxyHost: '***********',
      };
      assert.deepEqual(stopSessionService.sessionKeyObj, params);
    });
  });

  describe('handleStop', () => {
    it('return if no session keyobject present', async () => {
      const pushToCLS = sinon.stub(helper, 'pushToCLS');
      const stopSessionService = new StopVideoRecordingService(undefined, 'stopByBS');
      await stopSessionService.handleStop();
      sinon.assert.notCalled(pushToCLS);
      pushToCLS.restore();
    });

    it('should make terminal request and handle response if terminal has proper response', async () => {
      const pushToCLS = sinon.stub(helper, 'pushToCLS');
      const getStopVideoParams = sinon.stub(StopVideoRecordingService.prototype, 'getStopVideoParams');
      const getEncodedURLParams = sinon.stub(requestlib, 'getEncodedURLParams');
      const terminalRequest = sinon.stub(StopVideoRecordingService.prototype, 'terminalRequest').returns({ data: 'random' });
      const handleStopVideoResponse = sinon.stub(StopVideoRecordingService.prototype, 'handleStopVideoResponse');
      const stopSessionService = new StopVideoRecordingService({
        tunnel: true, captureCrash: true, isPlaywright: true, webDriverLogs: true,
      }, 'stopByBS');
      await stopSessionService.handleStop();
      sinon.assert.calledOnce(pushToCLS);
      sinon.assert.calledOnce(getStopVideoParams);
      sinon.assert.calledOnce(getEncodedURLParams);
      sinon.assert.calledOnce(terminalRequest);
      sinon.assert.calledOnce(handleStopVideoResponse);
      pushToCLS.restore();
      getStopVideoParams.restore();
      getEncodedURLParams.restore();
      terminalRequest.restore();
      handleStopVideoResponse.restore();
    });

    it('should make terminal request and don\'t handle response if terminal response not proper', async () => {
      const pushToCLS = sinon.stub(helper, 'pushToCLS');
      const getStopVideoParams = sinon.stub(StopVideoRecordingService.prototype, 'getStopVideoParams');
      const getEncodedURLParams = sinon.stub(requestlib, 'getEncodedURLParams');
      const terminalRequest = sinon.stub(StopVideoRecordingService.prototype, 'terminalRequest').returns({});
      const handleStopVideoResponse = sinon.stub(StopVideoRecordingService.prototype, 'handleStopVideoResponse');
      const stopSessionService = new StopVideoRecordingService({
        tunnel: true, captureCrash: true, isPlaywright: true, webDriverLogs: true,
      }, 'stopByBS');
      await stopSessionService.handleStop();
      sinon.assert.calledOnce(pushToCLS);
      sinon.assert.calledOnce(getStopVideoParams);
      sinon.assert.calledOnce(getEncodedURLParams);
      sinon.assert.calledOnce(terminalRequest);
      sinon.assert.notCalled(handleStopVideoResponse);
      pushToCLS.restore();
      getStopVideoParams.restore();
      getEncodedURLParams.restore();
      terminalRequest.restore();
      handleStopVideoResponse.restore();
    });
  });

  describe('handleStopVideoResponse', () => {
    let exceptionLogger; let miscLogger; let
      seleniumStats;

    beforeEach(() => {
      exceptionLogger = sinon.stub(HubLogger, 'exceptionLogger');
      miscLogger = sinon.stub(HubLogger, 'miscLogger');
      seleniumStats = sinon.stub(HubLogger, 'seleniumStats');
    });

    afterEach(() => {
      exceptionLogger.restore();
      seleniumStats.restore();
      miscLogger.restore();
    });

    it('parsing error should log excption and send stats', async () => {
      const stopSessionService = new StopVideoRecordingService({
        tunnel: true, captureCrash: true, isPlaywright: true, webDriverLogs: true,
      }, 'stopByBS');
      await stopSessionService.handleStopVideoResponse('/stop_video_rec', { data: 'parse error' });
      sinon.assert.calledOnce(exceptionLogger);
      sinon.assert.calledOnce(seleniumStats);
    });

    it('terminal response has error', async () => {
      const stopSessionService = new StopVideoRecordingService({
        tunnel: true, captureCrash: true, isPlaywright: true, webDriverLogs: true,
      }, 'stopByBS');
      await stopSessionService.handleStopVideoResponse('/stop_video_rec', { data: JSON.stringify({ error: 'response error' }) });
      sinon.assert.calledOnce(exceptionLogger);
      sinon.assert.calledOnce(seleniumStats);
    });

    it('process terminal response and large video upload time', async () => {
      const stopSessionService = new StopVideoRecordingService({
        tunnel: true, captureCrash: true, isPlaywright: true, webDriverLogs: true,
      }, 'stopByBS');
      const tres = {
        file_size: 0.5,
        time: 15,
        active_window: 'facebook',
      };
      await stopSessionService.handleStopVideoResponse('/stop_video_rec', { data: JSON.stringify(tres) });
      sinon.assert.notCalled(exceptionLogger);
      sinon.assert.calledOnce(seleniumStats);
      sinon.assert.calledOnce(miscLogger);
    });

    it('process terminal response and very large video upload time', async () => {
      const stopSessionService = new StopVideoRecordingService({
        tunnel: true, captureCrash: true, isPlaywright: true, webDriverLogs: true,
      }, 'stopByBS');
      const tres = {
        file_size: 1.5,
        time: 25,
        active_window: 'facebook',
      };
      await stopSessionService.handleStopVideoResponse('/stop_video_rec', { data: JSON.stringify(tres) });
      sinon.assert.notCalled(exceptionLogger);
      sinon.assert.calledOnce(seleniumStats);
      sinon.assert.calledOnce(miscLogger);
    });

    it('process terminal response and large video upload time - 2', async () => {
      const stopSessionService = new StopVideoRecordingService({
        tunnel: true, captureCrash: true, isPlaywright: true, webDriverLogs: true,
      }, 'stopByBS');
      const tres = {
        file_size: 1.5,
        time: 10,
        active_window: 'facebook',
      };
      await stopSessionService.handleStopVideoResponse('/stop_video_rec', { data: JSON.stringify(tres) });
      sinon.assert.notCalled(exceptionLogger);
      sinon.assert.calledOnce(seleniumStats);
      sinon.assert.calledOnce(miscLogger);
    });

    it('process terminal response without large file sze', async () => {
      const stopSessionService = new StopVideoRecordingService({
        tunnel: true, captureCrash: true, isPlaywright: true, webDriverLogs: true,
      }, 'stopByBS');
      const tres = {
        file_size: 1.5,
        time: 1,
      };
      await stopSessionService.handleStopVideoResponse('/stop_video_rec', { data: JSON.stringify(tres) });
      sinon.assert.notCalled(exceptionLogger);
      sinon.assert.notCalled(seleniumStats);
      sinon.assert.calledOnce(miscLogger);
    });
  });

  describe('terminalRequest', () => {
    let exceptionLogger; let miscLogger; let seleniumStats; let
      pushToCLS;

    beforeEach(() => {
      exceptionLogger = sinon.stub(HubLogger, 'exceptionLogger');
      miscLogger = sinon.stub(HubLogger, 'miscLogger');
      seleniumStats = sinon.stub(HubLogger, 'seleniumStats');
      pushToCLS = sinon.stub(helper, 'pushToCLS');
    });

    afterEach(() => {
      exceptionLogger.restore();
      seleniumStats.restore();
      miscLogger.restore();
      pushToCLS.restore();
    });

    it('return in case url us empty', async () => {
      const stopSessionService = new StopVideoRecordingService({
        os: 'win', unnel: true, captureCrash: true, isPlaywright: true, webDriverLogs: true,
      }, 'stopByBS');
      const terminalResponse = await stopSessionService.terminalRequest('');
      assert.equal(terminalResponse, undefined);
      sinon.assert.notCalled(exceptionLogger);
      sinon.assert.notCalled(miscLogger);
      sinon.assert.notCalled(seleniumStats);
      sinon.assert.notCalled(pushToCLS);
    });

    it('log error in case of request error', async () => {
      const requestlibCall = sinon.stub(requestlib, 'call').returns(Promise.reject(new Error('Error')));
      const stopSessionService = new StopVideoRecordingService({
        os: 'win', unnel: true, captureCrash: true, isPlaywright: true, webDriverLogs: true,
      }, 'stopByBS');
      const terminalResponse = await stopSessionService.terminalRequest('random');
      assert.equal(terminalResponse, undefined);
      sinon.assert.calledOnce(exceptionLogger);
      sinon.assert.calledOnce(miscLogger);
      sinon.assert.calledOnce(seleniumStats);
      sinon.assert.calledOnce(pushToCLS);
      requestlibCall.restore();
    });

    it('return terminal response in case of windows', async () => {
      const requestlibCall = sinon.stub(requestlib, 'call').returns(Promise.resolve({ data: 'response' }));
      const stopSessionService = new StopVideoRecordingService({
        os: 'win', unnel: true, captureCrash: true, isPlaywright: true, webDriverLogs: true,
      }, 'stopByBS');
      const terminalResponse = await stopSessionService.terminalRequest('random');
      assert.deepEqual(terminalResponse, { data: 'response' });
      sinon.assert.notCalled(exceptionLogger);
      sinon.assert.calledOnce(miscLogger);
      sinon.assert.notCalled(seleniumStats);
      sinon.assert.calledTwice(pushToCLS);
      requestlibCall.restore();
    });

    it('return terminal response in case of osx', async () => {
      const requestlibCall = sinon.stub(requestlib, 'call').returns(Promise.resolve({ data: 'response' }));
      const stopSessionService = new StopVideoRecordingService({
        os: 'osx', unnel: true, captureCrash: true, isPlaywright: true, webDriverLogs: true,
      }, 'stopByBS');
      const terminalResponse = await stopSessionService.terminalRequest('random');
      assert.deepEqual(terminalResponse, { data: 'response' });
      sinon.assert.notCalled(exceptionLogger);
      sinon.assert.calledOnce(miscLogger);
      sinon.assert.notCalled(seleniumStats);
      sinon.assert.calledTwice(pushToCLS);
      requestlibCall.restore();
    });
  });
});
