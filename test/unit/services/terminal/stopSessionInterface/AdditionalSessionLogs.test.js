'use strict';

const { AdditionalSessionLogs } = require('../../../../../services/terminal/stopSessionInterface/AdditionalSessionLogs');
const helper = require('../../../../../helper');
const HubLogger = require('../../../../../log');
const constants = require('../../../../../constants');
const requestlib = require('../../../../../lib/request');
const sinon = require('sinon');
const { assert } = require('chai');


describe('AdditionalSessionLogs tests', () => {
  describe('handleStop', () => {
    it('should call retreive console logs in sync and network logs in async', async () => {
      const sessionKeyObj = {
        rails_session_id: 'abcd',
        consoleLogsEnabled: true,
        os: 'mac',
      };
      const retrieveAndUploadConsoleLogs = sinon.stub(AdditionalSessionLogs.prototype, 'retrieveAndUploadConsoleLogs').returns(Promise.resolve());
      const generateNetworkLogs = sinon.stub(AdditionalSessionLogs.prototype, 'generateNetworkLogs').returns(Promise.resolve());
      const sessionLogs = new AdditionalSessionLogs(sessionKeyObj);
      await sessionLogs.handleStop();
      sinon.assert.calledOnce(retrieveAndUploadConsoleLogs);
      sinon.assert.calledOnce(generateNetworkLogs);
      retrieveAndUploadConsoleLogs.restore();
      generateNetworkLogs.restore();
    });

    it('should call retreive console logs in sync and network logs in async for automate ios session', async () => {
      const sessionKeyObj = {
        rails_session_id: 'abcd',
        consoleLogsEnabled: true,
        os: 'ios',
        appTesting: false,
      };
      const retrieveAndUploadConsoleLogs = sinon.stub(AdditionalSessionLogs.prototype, 'retrieveAndUploadConsoleLogs').returns(Promise.resolve());
      const generateNetworkLogs = sinon.stub(AdditionalSessionLogs.prototype, 'generateNetworkLogs').returns(Promise.resolve());
      const sessionLogs = new AdditionalSessionLogs(sessionKeyObj);
      await sessionLogs.handleStop();
      sinon.assert.calledOnce(retrieveAndUploadConsoleLogs);
      sinon.assert.calledOnce(generateNetworkLogs);
      retrieveAndUploadConsoleLogs.restore();
      generateNetworkLogs.restore();
    });

    it('should call retreive console logs and network logs in async', async () => {
      const sessionKeyObj = {
        rails_session_id: 'abcd',
        consoleLogsEnabled: true,
        os: 'win',
      };
      const retrieveAndUploadConsoleLogs = sinon.stub(AdditionalSessionLogs.prototype, 'retrieveAndUploadConsoleLogs').returns(Promise.resolve());
      const generateNetworkLogs = sinon.stub(AdditionalSessionLogs.prototype, 'generateNetworkLogs').returns(Promise.resolve());
      const sessionLogs = new AdditionalSessionLogs(sessionKeyObj);
      await sessionLogs.handleStop();
      sinon.assert.calledOnce(retrieveAndUploadConsoleLogs);
      sinon.assert.calledOnce(generateNetworkLogs);
      retrieveAndUploadConsoleLogs.restore();
      generateNetworkLogs.restore();
    });

    it('should not call retreive console logs and call network logs in async', async () => {
      const sessionKeyObj = {
        rails_session_id: 'abcd',
        consoleLogsEnabled: false,
        os: 'win',
      };
      const retrieveAndUploadConsoleLogs = sinon.stub(AdditionalSessionLogs.prototype, 'retrieveAndUploadConsoleLogs').returns(Promise.resolve());
      const generateNetworkLogs = sinon.stub(AdditionalSessionLogs.prototype, 'generateNetworkLogs').returns(Promise.resolve());
      const sessionLogs = new AdditionalSessionLogs(sessionKeyObj);
      await sessionLogs.handleStop();
      sinon.assert.notCalled(retrieveAndUploadConsoleLogs);
      sinon.assert.calledOnce(generateNetworkLogs);
      retrieveAndUploadConsoleLogs.restore();
      generateNetworkLogs.restore();
    });
  });

  describe('retrieveAndUploadConsoleLogs', () => {
    it('should get console logs and send them to kafka if status is 0', async () => {
      const sessionKeyObj = {
        rails_session_id: 'abcd',
        consoleLogsEnabled: true,
        os: 'mac',
      };
      const retrieveConsoleLogs = sinon.stub(AdditionalSessionLogs.prototype, 'retrieveConsoleLogs').returns({ status: 0, value: 'ads' });
      const appendConsoleLogs = sinon.stub(AdditionalSessionLogs.prototype, 'appendConsoleLogs');
      const sessionLogs = new AdditionalSessionLogs(sessionKeyObj);
      await sessionLogs.retrieveAndUploadConsoleLogs();
      sinon.assert.calledOnce(appendConsoleLogs);
      retrieveConsoleLogs.restore();
      appendConsoleLogs.restore();
    });

    it('should get console logs and send them to kafka if value is array', async () => {
      const sessionKeyObj = {
        rails_session_id: 'abcd',
        consoleLogsEnabled: true,
        os: 'mac',
      };
      const retrieveConsoleLogs = sinon.stub(AdditionalSessionLogs.prototype, 'retrieveConsoleLogs').returns({ status: 30, value: [] });
      const appendConsoleLogs = sinon.stub(AdditionalSessionLogs.prototype, 'appendConsoleLogs');
      const sessionLogs = new AdditionalSessionLogs(sessionKeyObj);
      await sessionLogs.retrieveAndUploadConsoleLogs();
      sinon.assert.calledOnce(appendConsoleLogs);
      retrieveConsoleLogs.restore();
      appendConsoleLogs.restore();
    });

    it('should get console logs and not send them to kafka if value not array and status non 0', async () => {
      const sessionKeyObj = {
        rails_session_id: 'abcd',
        consoleLogsEnabled: true,
        os: 'mac',
      };
      const retrieveConsoleLogs = sinon.stub(AdditionalSessionLogs.prototype, 'retrieveConsoleLogs').returns({ status: 30, value: 'asdsd' });
      const appendConsoleLogs = sinon.stub(AdditionalSessionLogs.prototype, 'appendConsoleLogs');
      const sessionLogs = new AdditionalSessionLogs(sessionKeyObj);
      await sessionLogs.retrieveAndUploadConsoleLogs();
      sinon.assert.notCalled(appendConsoleLogs);
      retrieveConsoleLogs.restore();
      appendConsoleLogs.restore();
    });

    it('should get console logs and not send them to kafka if value not array and status non 0', async () => {
      const sessionKeyObj = {
        rails_session_id: 'abcd',
        consoleLogsEnabled: true,
        os: 'mac',
      };
      const retrieveConsoleLogs = sinon.stub(AdditionalSessionLogs.prototype, 'retrieveConsoleLogs').returns({ status: 30, value: 'asdsd' });
      const appendConsoleLogs = sinon.stub(AdditionalSessionLogs.prototype, 'appendConsoleLogs');
      const sessionLogs = new AdditionalSessionLogs(sessionKeyObj);
      await sessionLogs.retrieveAndUploadConsoleLogs();
      sinon.assert.notCalled(appendConsoleLogs);
      retrieveConsoleLogs.restore();
      appendConsoleLogs.restore();
    });

    it('should get console logs and not send them to kafka if unable to get console logs', async () => {
      const sessionKeyObj = {
        rails_session_id: 'abcd',
        consoleLogsEnabled: true,
        os: 'mac',
      };
      const retrieveConsoleLogs = sinon.stub(AdditionalSessionLogs.prototype, 'retrieveConsoleLogs').returns(undefined);
      const appendConsoleLogs = sinon.stub(AdditionalSessionLogs.prototype, 'appendConsoleLogs');
      const sessionLogs = new AdditionalSessionLogs(sessionKeyObj);
      await sessionLogs.retrieveAndUploadConsoleLogs();
      sinon.assert.notCalled(appendConsoleLogs);
      retrieveConsoleLogs.restore();
      appendConsoleLogs.restore();
    });
  });

  describe('retrieveConsoleLogs', () => {
    let sessionKeyObj;
    let miscLogger;
    beforeEach(() => {
      sessionKeyObj = {
        rails_session_id: 'abcd',
        consoleLogsEnabled: true,
        os: 'mac',
      };
      miscLogger = sinon.stub(HubLogger, 'miscLogger');
    });

    afterEach(() => {
      miscLogger.restore();
    });

    // { type: 'safariConsole' } is sent as body for ios chromium browser
    it('should get console logs from jar for ios chromium browser', async () => {
      sessionKeyObj = {
        rails_session_id: 'abcd',
        consoleLogsEnabled: true,
        os: 'ios',
        dialect: 'W3C',
        browser: 'chromium_iphone',
      };
      const requestlibCall = sinon.stub(requestlib, 'call').returns(Promise.resolve({ data: '{"test":"response"}' }));
      const sessionLogs = new AdditionalSessionLogs(sessionKeyObj);
      const consolelogs = await sessionLogs.retrieveConsoleLogs();
      assert.deepEqual(consolelogs, { test: 'response' });
      requestlibCall.restore();
    });

    it('should get console logs from jar', async () => {
      const requestlibCall = sinon.stub(requestlib, 'call').returns(Promise.resolve({ data: '{"test":"response"}' }));
      const sessionLogs = new AdditionalSessionLogs(sessionKeyObj);
      const consolelogs = await sessionLogs.retrieveConsoleLogs();
      assert.deepEqual(consolelogs, { test: 'response' });
      requestlibCall.restore();
    });

    it('should call seperate endpoit for w3c console logs from jar', async () => {
      sessionKeyObj = {
        rails_session_id: 'abcd',
        consoleLogsEnabled: true,
        os: 'mac',
        dialect: 'W3C',
        browser: 'firefox',
      };
      const requestlibCall = sinon.stub(requestlib, 'call').returns(Promise.resolve({ data: '{"test":"response"}' }));
      const sessionLogs = new AdditionalSessionLogs(sessionKeyObj);
      const consolelogs = await sessionLogs.retrieveConsoleLogs();
      assert.deepEqual(consolelogs, { test: 'response' });
      requestlibCall.restore();
    });

    it('should return empty in case of json error', async () => {
      const requestlibCall = sinon.stub(requestlib, 'call').returns(Promise.resolve({ data: '{"test":"response}' }));
      const sessionLogs = new AdditionalSessionLogs(sessionKeyObj);
      const consolelogs = await sessionLogs.retrieveConsoleLogs();
      assert.equal(consolelogs, undefined);
      requestlibCall.restore();
    });

    it('should return empty in case of request error', async () => {
      const requestlibCall = sinon.stub(requestlib, 'call').returns(Promise.reject(new Error('Error')));
      const sessionLogs = new AdditionalSessionLogs(sessionKeyObj);
      const consolelogs = await sessionLogs.retrieveConsoleLogs();
      assert.equal(consolelogs, undefined);
      requestlibCall.restore();
    });
  });

  describe('appendConsoleLogs', () => {
    it('', () => {
      const sessionKeyObj = {
        rails_session_id: 'abcd',
        consoleLogsEnabled: true,
        os: 'mac',
      };
      const sessionLogs = new AdditionalSessionLogs(sessionKeyObj);
      sessionLogs.appendConsoleLogs();
    });
  });

  describe('appendConsoleLogs', () => {
    let pingZombie;
    let kafkaLogProducerErrorToZombie;
    let uploadLogPartToKafka;
    let exceptionLogger;

    beforeEach(() => {
      pingZombie = sinon.spy(helper, 'PingZombie');
      kafkaLogProducerErrorToZombie = sinon.spy(helper, 'kafkaLogProducerErrorToZombie');
      uploadLogPartToKafka = sinon.stub(HubLogger, 'uploadLogPartToKafka');
      exceptionLogger = sinon.spy(HubLogger, 'exceptionLogger');
    });

    afterEach(() => {
      uploadLogPartToKafka.restore();
      kafkaLogProducerErrorToZombie.restore();
      exceptionLogger.restore();
      pingZombie.restore();
    });

    it('should return in case of aa session', () => {
      const log_1 = { timestamp: new Date().getTime(), level: 'INFO', message: 'logs1' };
      const log_2 = { timestamp: new Date().getTime(), level: 'INFO', message: 'logs1' };
      const expectedLog = `${log_1.timestamp}:${log_1.level}:${log_1.message}\r\n${log_2.timestamp}:${log_2.level}:${log_2.message}\r\n`;
      const logstring = JSON.stringify([log_1, log_2]);
      const sessionKeyObj = {
        rails_session_id: 'abcd',
        consoleLogsEnabled: true,
        os: 'mac',
        appTesting: true,
      };
      const sessionLogs = new AdditionalSessionLogs(sessionKeyObj);
      sessionLogs.appendConsoleLogs(logstring);
      sinon.assert.notCalled(uploadLogPartToKafka);
      sinon.assert.notCalled(exceptionLogger);
      sinon.assert.notCalled(kafkaLogProducerErrorToZombie);
      sinon.assert.notCalled(pingZombie);
    });

    it('should append console logs', () => {
      const log_1 = { timestamp: new Date().getTime(), level: 'INFO', message: 'logs1' };
      const log_2 = { timestamp: new Date().getTime(), level: 'INFO', message: 'logs1' };
      const expectedLog = `${log_1.timestamp}:${log_1.level}:${log_1.message}\r\n${log_2.timestamp}:${log_2.level}:${log_2.message}\r\n`;
      const logstring = JSON.stringify([log_1, log_2]);
      const sessionKeyObj = {
        rails_session_id: 'abcd',
        consoleLogsEnabled: true,
        os: 'mac',
        appTesting: false,
      };
      const sessionLogs = new AdditionalSessionLogs(sessionKeyObj);
      sessionLogs.appendConsoleLogs(logstring);
      sinon.assert.calledWith(uploadLogPartToKafka, sessionKeyObj, null, expectedLog, constants.kafkaConfig.console_logs_topic);
      sinon.assert.notCalled(exceptionLogger);
      sinon.assert.notCalled(kafkaLogProducerErrorToZombie);
      sinon.assert.notCalled(pingZombie);
    });

    it('should not append console logs when json parsing fails', () => {
      const sessionKeyObj = {
        rails_session_id: 'abcd',
        consoleLogsEnabled: true,
        os: 'mac',
        appTesting: false,
      };
      const sessionLogs = new AdditionalSessionLogs(sessionKeyObj);
      sessionLogs.appendConsoleLogs('this is some random string');
      assert(/SyntaxError/.test(exceptionLogger.getCall(0).args[3]));
      assert(/logString: this is some random string/.test(exceptionLogger.getCall(0).args[0]));
      sinon.assert.notCalled(uploadLogPartToKafka);
      sinon.assert.calledOnce(exceptionLogger);
      sinon.assert.calledOnce(kafkaLogProducerErrorToZombie);
      sinon.assert.calledOnce(pingZombie);
    });

    it('should not append console logs when null data is passed', () => {
      const sessionKeyObj = {
        rails_session_id: 'abcd',
        consoleLogsEnabled: true,
        os: 'mac',
        appTesting: false,
      };
      const sessionLogs = new AdditionalSessionLogs(sessionKeyObj);
      sessionLogs.appendConsoleLogs(null);
      assert(/logObject is undefined\/null/.test(exceptionLogger.getCall(0).args[3]));
      sinon.assert.notCalled(uploadLogPartToKafka);
      sinon.assert.calledOnce(exceptionLogger);
      sinon.assert.calledOnce(kafkaLogProducerErrorToZombie);
      sinon.assert.calledOnce(pingZombie);
    });

    it('should not append console logs when undefined data is passed', () => {
      const sessionKeyObj = {
        rails_session_id: 'abcd',
        consoleLogsEnabled: true,
        os: 'mac',
        appTesting: false,
      };
      const sessionLogs = new AdditionalSessionLogs(sessionKeyObj);
      sessionLogs.appendConsoleLogs(undefined);
      assert(/SyntaxError/.test(exceptionLogger.getCall(0).args[3]));
      // dont print logString when it is undefined
      assert.equal(false, /logString:/.test(exceptionLogger.getCall(0).args[0]));
      sinon.assert.notCalled(uploadLogPartToKafka);
      sinon.assert.calledOnce(exceptionLogger);
      sinon.assert.calledOnce(kafkaLogProducerErrorToZombie);
      sinon.assert.calledOnce(pingZombie);
    });
  });

  describe('generateNetworkLogs', () => {
    let sessionKeyObj;
    let miscLogger;
    beforeEach(() => {
      miscLogger = sinon.stub(HubLogger, 'miscLogger');
    });

    afterEach(() => {
      miscLogger.restore();
    });
    it('should not make requestlib call when sessionKeyObj is null/undefined', async () => {
      const requestlibCall = sinon.stub(requestlib, 'call').returns(Promise.resolve({ data: '{"test":"response"}' }));
      const sessionLogs = new AdditionalSessionLogs(null);
      await sessionLogs.generateNetworkLogs();
      sinon.assert.notCalled(requestlibCall);
      requestlibCall.restore();
    });

    it('should make requestlib call when sessionKeyObj is not null/undefined', async () => {
      sessionKeyObj = {
        rails_session_id: 'abcd',
        os: 'android',
        networkLogs: true,
      };
      const requestlibCall = sinon.stub(requestlib, 'call').returns(Promise.resolve({ data: '{"test":"response"}' }));
      const sessionLogs = new AdditionalSessionLogs(sessionKeyObj);
      await sessionLogs.generateNetworkLogs();
      sinon.assert.calledOnce(requestlibCall);
      requestlibCall.restore();
    });

    it('should make requestlib call when sessionKeyObj is not null/undefined', async () => {
      sessionKeyObj = {
        rails_session_id: 'abcd',
        os: 'android',
        networkLogs: true,
      };
      const requestlibCall = sinon.stub(requestlib, 'call').returns(Promise.resolve({ data: '{"test":"response"}' }));
      const sessionLogs = new AdditionalSessionLogs(sessionKeyObj);
      await sessionLogs.generateNetworkLogs();
      sinon.assert.calledOnce(requestlibCall);
      requestlibCall.restore();
    });
  });
});
