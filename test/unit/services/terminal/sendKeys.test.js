/* eslint-disable max-lines-per-function */

'use strict';

const rewire = require('rewire');

const sendKeys = rewire('../../../../services/terminal/sendKeys.js');
const testHelper = require('../../helper');
const requestlib = require('../../../../lib/request');
const sinon = require('sinon');
const { assert, expect } = require('chai');

describe('Terminal SendKeys Interface tests', () => {
  describe('getAttributeTypeOptions', () => {
    const requestStateObj = { rproxyHost: 'host1', request: { log_data: { someKey: 'test' } } };
    const getAttributeTypeOptions = sendKeys.__get__('getAttributeTypeOptions');
    const options = {
      path: '/session/dummysessionid/element/dummy-element-id/value',
      headers: { host: 'dummyhost.com' },
      port: 1234,
    };
    const urlParts = options.path.toString('utf8').split('/');
    const attributeUrlParts = urlParts.slice(0, urlParts.length - 1);

    it('should contain all keys', () => {
      const result = getAttributeTypeOptions(attributeUrlParts, requestStateObj, options);
      assert.deepEqual(result, {
        path: Buffer.from(attributeUrlParts.join('/'), 'utf8'),
        method: 'GET',
        hostname: 'host1',
        port: 1234,
        recordJarTime: true,
        headers: {
          'accept-encoding': 'gzip',
          connection: 'Keep-Alive',
          host: 'dummyhost.com',
        },
      });
    });
  });

  describe('getAttributeType', async () => {
    let requestLibCall;
    let stubgetAttributeTypeOptions;

    before(() => {
      const getAttributeTypeOptionsObj = { getAttributeTypeOptions: sendKeys.__get__('getAttributeTypeOptions') };
      stubgetAttributeTypeOptions = sinon.stub(getAttributeTypeOptionsObj, 'getAttributeTypeOptions').returns({ key: 'val1' });
      sendKeys.__set__('getAttributeTypeOptions', stubgetAttributeTypeOptions);
    });

    after(() => {
      stubgetAttributeTypeOptions.restore();
    });

    it('calls terminal using requestlib and returns a promise', async () => {
      requestLibCall = sinon.stub(requestlib, 'call').returns(Promise.resolve('some resp'));
      const requestStateObj = { rproxyHost: 'host1', request: { log_data: { someKey: 'test' } } };
      const options = {
        path: '/session/dummysessionid/element/dummy-element-id/value',
        headers: { host: 'dummyhost.com' },
        port: 1234,
      };
      const urlParts = options.path.toString('utf8').split('/');
      const attributeUrlParts = urlParts.slice(0, urlParts.length - 1);

      const res = await sendKeys.getAttributeType(requestStateObj, options, attributeUrlParts);
      sinon.assert.match(res, 'some resp');
      sinon.assert.called(stubgetAttributeTypeOptions);
      sinon.assert.calledWith(stubgetAttributeTypeOptions, attributeUrlParts, requestStateObj, options);
      sinon.assert.calledOnce(requestLibCall);
      sinon.assert.calledWith(requestLibCall, { key: 'val1' });
      requestLibCall.restore();
      stubgetAttributeTypeOptions.restore();
    });

    it('calls terminal using requestlib and throws error if call fails', async () => {
      requestLibCall = sinon.stub(requestlib, 'call').returns(Promise.reject(new Error('some err')));
      const requestStateObj = { rproxyHost: 'host1', request: { log_data: { someKey: 'test' } } };
      const options = {
        path: '/session/dummysessionid/element/dummy-element-id/value',
        headers: { host: 'dummyhost.com' },
        port: 1234,
      };
      const urlParts = options.path.toString('utf8').split('/');
      const attributeUrlParts = urlParts.slice(0, urlParts.length - 1);
      let err;
      try {
        await sendKeys.getAttributeType(requestStateObj, options, attributeUrlParts);
      } catch (e) {
        err = e;
      }
      expect(err).to.be.an.instanceOf(Error);
      sinon.assert.match(err.message, 'some err');
      requestLibCall.restore();
      stubgetAttributeTypeOptions.restore();
    });
  });

  describe('getScriptForSetValue', () => {
    const sendData = 'data to be sent';
    const getScriptForSetValue = sendKeys.__get__('getScriptForSetValue');

    it('returns script without onchange, oninput for ie 6, 7, 8', () => {
      const sessionKeyObj = { browser_version: '6.0', browser: 'internet explorer' };

      const res = getScriptForSetValue(sendData, sessionKeyObj);
      expect(res).to.eql(`arguments[0].value += 'data to be sent';
      function generateEvent(eventName) {
        var eventObj = document.createEventObject('Event');
        eventObj.eventName = eventName;
        eventObj.eventType = eventName;
        return eventObj;
      }
      arguments[0].fireEvent('onkeydown', generateEvent('keydown'));
      arguments[0].fireEvent('onkeyup', generateEvent('keyup'));
      arguments[0].click();`);
    });

    it('returns script with onchange by default', () => {
      const sessionKeyObj = { browser_version: '10.0', browser: 'internet explorer' };

      const res = getScriptForSetValue(sendData, sessionKeyObj);
      expect(res).to.eql(`arguments[0].value += 'data to be sent';
      function generateEvent(eventName) {
        var eventObj = document.createEvent('Event');
        eventObj.initEvent(eventName, true, true);
        return eventObj;
      }
      arguments[0].dispatchEvent(generateEvent('change'));
      arguments[0].dispatchEvent(generateEvent('input'));
      arguments[0].dispatchEvent(generateEvent('keydown'));
      arguments[0].dispatchEvent(generateEvent('keyup'));
      arguments[0].click();`);
    });
  });

  describe('getJsSetValueOptions', () => {
    const getJsSetValueOptions = sendKeys.__get__('getJsSetValueOptions');
    const sessionKeyObj = testHelper.getKeyObject();
    const requestStateObj = { rproxyHost: 'dummy-host' };
    const elementId = '2807e5ee-d192-4600-ae5a-2c9c2dddcc75';
    const options = {
      path: '/session/dummysessionid/element/dummy-element-id/value',
      headers: { host: 'dummyhost.com', 'BStack-Host': 'bstack-host', 'X-Source-Env-Type': 'development' },
      port: 1234,
    };
    const urlParts = options.path.toString('utf8').split('/');
    const sendData = 'data to be sent';
    let stubgetScriptForSetValue;

    before(() => {
      const getScriptForSetValueObj = { getScriptForSetValue: sendKeys.__get__('getScriptForSetValue') };
      stubgetScriptForSetValue = sinon.stub(getScriptForSetValueObj, 'getScriptForSetValue').returns('script');
      sendKeys.__set__('getScriptForSetValue', stubgetScriptForSetValue);
    });

    after(() => {
      stubgetScriptForSetValue.restore();
    });

    it('returns given options as it is if useDefaultOptions=true', () => {
      const res = getJsSetValueOptions(sessionKeyObj, requestStateObj, options, urlParts, sendData, elementId, true);
      assert.deepEqual(res, options);
    });

    it('returns set value options for w3c dialect', () => {
      const sessionKeyObjW3C = testHelper.getKeyObject();
      sessionKeyObjW3C.dialect = 'W3C';
      const jsSetValueUrlParts = urlParts.slice(0, urlParts.length - 3);
      jsSetValueUrlParts.push('execute/sync');

      const res = getJsSetValueOptions(sessionKeyObjW3C, requestStateObj, options, urlParts, sendData, elementId, false);

      sinon.assert.called(stubgetScriptForSetValue);
      sinon.assert.calledWith(stubgetScriptForSetValue, sendData, sessionKeyObjW3C);

      const expectedBody = `${JSON.stringify({
        script: 'script',
        args: [
          {
            ELEMENT: `${elementId}`,
            'element-6066-11e4-a52e-4f735466cecf': `${elementId}`,
          },
        ],
      })}`;
      const length = Buffer.from(expectedBody).length;

      assert.deepEqual(res, {
        path: Buffer.from(jsSetValueUrlParts.join('/'), 'utf8'),
        method: 'POST',
        hostname: 'dummy-host',
        port: 1234,
        headers: {
          'accept-encoding': 'gzip',
          connection: 'Keep-Alive',
          host: 'dummyhost.com',
          'BStack-Host': 'bstack-host',
          'X-Source-Env-Type': 'development',
          'content-length': length,
        },
        body: expectedBody,
      });
    });

    it('returns non w3c set value options by default', () => {
      const sessionKeyObj1 = testHelper.getKeyObject();
      sessionKeyObj1.dialect = 'not W3C';
      const jsSetValueUrlParts = urlParts.slice(0, urlParts.length - 3);
      jsSetValueUrlParts.push('execute');

      const res = getJsSetValueOptions(sessionKeyObj1, requestStateObj, options, urlParts, sendData, elementId, false);

      sinon.assert.called(stubgetScriptForSetValue);
      sinon.assert.calledWith(stubgetScriptForSetValue, sendData, sessionKeyObj1);

      const expectedBody = `${JSON.stringify({
        script: 'script',
        args: [
          {
            ELEMENT: `${elementId}`,
            'element-6066-11e4-a52e-4f735466cecf': `${elementId}`,
          },
        ],
      })}`;
      const length = Buffer.from(expectedBody).length;

      assert.deepEqual(res, {
        path: Buffer.from(jsSetValueUrlParts.join('/'), 'utf8'),
        method: 'POST',
        hostname: 'dummy-host',
        port: 1234,
        headers: {
          'accept-encoding': 'gzip',
          connection: 'Keep-Alive',
          host: 'dummyhost.com',
          'BStack-Host': 'bstack-host',
          'X-Source-Env-Type': 'development',
          'content-length': length,
        },
        body: expectedBody,
      });
    });
  });

  describe('sendKeysToElement', async () => {
    let stubgetJsSetValueOptions;
    const sessionKeyObj = testHelper.getKeyObject();
    const requestStateObj = { rproxyHost: 'dummy-host' };
    const elementId = '2807e5ee-d192-4600-ae5a-2c9c2dddcc75';
    const options = {
      path: '/session/dummysessionid/element/dummy-element-id/value',
      headers: { host: 'dummyhost.com', 'BStack-Host': 'bstack-host', 'X-Source-Env-Type': 'development' },
      port: 1234,
    };
    const urlParts = options.path.toString('utf8').split('/');

    const hookFunction = (data) => {
      data.abc = 1;
    };
    const sendData = 'data to be sent';
    const optionsToSend = { key: 'jsSetValOptions' };

    before(() => {
      const getJsSetValueOptionsObj = { getJsSetValueOptions: sendKeys.__get__('getJsSetValueOptions') };
      stubgetJsSetValueOptions = sinon.stub(getJsSetValueOptionsObj, 'getJsSetValueOptions').returns({ key: 'jsSetValOptions' });
      sendKeys.__set__('getJsSetValueOptions', stubgetJsSetValueOptions);
    });

    after(() => {
      stubgetJsSetValueOptions.restore();
    });

    it('calls requestLib with hook and returns promise', async () => {
      const requestLibCall = sinon.stub(requestlib, 'callWithHook').returns(Promise.resolve('some resp'));
      const res = await sendKeys.sendKeysToElement(sessionKeyObj, requestStateObj, options, urlParts, sendData, elementId, hookFunction, false);

      sinon.assert.calledOnce(stubgetJsSetValueOptions);
      sinon.assert.calledWith(stubgetJsSetValueOptions, sessionKeyObj, requestStateObj, options, urlParts, sendData, elementId, false);
      sinon.assert.calledOnce(requestLibCall);
      sinon.assert.calledWith(requestLibCall, optionsToSend, 0);
      expect(res).to.eql('some resp');
      requestLibCall.restore();
    });

    it('throws error if requestLib call with hook fails', async () => {
      const requestLibCall = sinon.stub(requestlib, 'callWithHook').returns(Promise.reject(new Error('some error')));
      let err;

      try {
        await sendKeys.sendKeysToElement(sessionKeyObj, requestStateObj, options, urlParts, sendData, elementId, hookFunction, false);
      } catch (e) {
        err = e;
      }
      sinon.assert.calledOnce(requestLibCall);
      sinon.assert.calledWith(requestLibCall, optionsToSend, 0);
      expect(err).to.be.an.instanceOf(Error);
      sinon.assert.match(err.message, 'some err');
      requestLibCall.restore();
    });
  });
});
