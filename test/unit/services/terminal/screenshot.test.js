'use strict';

const screenshotInterface = require('../../../../services/terminal/screenshot');
const testHelper = require('../../helper');
const requestlib = require('../../../../lib/request');
const sinon = require('sinon');
const { expect } = require('chai');

describe('Terminal ScreenshotInterface tests', () => {
  describe('realIOSScreenshot', () => {
    const sessionKeyObj = testHelper.getKeyObject();
    sessionKeyObj.deviceOrientation = 'portrait';
    sessionKeyObj.device = 'iosdevice';

    it('should call terminal using request lib and return promise', async () => {
      const requestLibCall = sinon.stub(requestlib, 'call').returns(Promise.resolve({ data: 'some_data' }));
      const val = await screenshotInterface.realIOSScreenshot(sessionKeyObj);
      expect(val).to.eql({ data: 'some_data' });
      sinon.assert.calledOnce(requestLibCall);
      sinon.assert.calledWith(requestLibCall, {
        method: 'GET',
        hostname: sessionKeyObj.rproxyHost,
        port: 45671,
        path: `/custom_screenshot?sessionId=${sessionKeyObj.rails_session_id}&orientation=portrait&device=iosdevice`,
        headers: { 'BStack-Host': sessionKeyObj.name, 'X-Source-Env-Type': 'development' },
      });
      requestLibCall.restore();
    });

    it('should raise exception if request lib call raises exception', async () => {
      const requestLibCall = sinon.stub(requestlib, 'call').returns(Promise.reject(new Error('some err1')));
      let err;

      try {
        await screenshotInterface.realIOSScreenshot(sessionKeyObj);
      } catch (e) {
        err = e;
      }
      expect(err).to.eql(new Error('some err1'));
      sinon.assert.calledOnce(requestLibCall);
      sinon.assert.calledWith(requestLibCall, {
        method: 'GET',
        hostname: sessionKeyObj.rproxyHost,
        port: 45671,
        path: `/custom_screenshot?sessionId=${sessionKeyObj.rails_session_id}&orientation=portrait&device=iosdevice`,
        headers: { 'BStack-Host': sessionKeyObj.name, 'X-Source-Env-Type': 'development' },
      });
      requestLibCall.restore();
    });
  });
});
