'use strict';

const browserSSL = require('../../../../services/terminal/browserSSL');
const testHelper = require('../../helper');
const requestlib = require('../../../../lib/request');
const sinon = require('sinon');
const constants = require('../../../../constants');
const { expect } = require('chai');

describe('browserSSL TerminalInterface tests', () => {
  const sessionKeyObj = testHelper.getKeyObject();
  const params = 'someParam=abc';
  const hostname = 'somehostname';
  sessionKeyObj.os = 'some_os mac';
  sessionKeyObj.name = hostname;

  describe('acceptFirefoxSsl', () => {
    it('calls terminal accept ff ssl endpoint and returns promise', async () => {
      const requestLibCall = sinon.stub(requestlib, 'call').returns(Promise.resolve({ key1: 'val1' }));
      const res = await browserSSL.acceptFirefoxSsl(sessionKeyObj, params);

      expect(res).to.eql({ key1: 'val1' });
      sinon.assert.calledOnce(requestLibCall);
      sinon.assert.calledWith(requestLibCall, {
        method: 'GET',
        hostname: sessionKeyObj.rproxyHost,
        port: 45671,
        recordJarTime: true,
        timeout: constants.acceptSslffRequestTimeout,
        path: '/acceptssl_ff?someParam=abc',
        headers: { 'BStack-Host': 'somehostname', 'X-Source-Env-Type': 'development' },
      });
      requestLibCall.restore();
    });

    it('throws error if call to terminal accept ff ssl endpoint throws error', async () => {
      const requestLibCall = sinon.stub(requestlib, 'call').returns(Promise.reject(new Error('err1')));
      let err;
      try {
        await browserSSL.acceptFirefoxSsl(sessionKeyObj, params);
      } catch (e) {
        err = e;
      }
      expect(err).to.eql(new Error('err1'));
      sinon.assert.calledOnce(requestLibCall);
      sinon.assert.calledWith(requestLibCall, {
        method: 'GET',
        hostname: sessionKeyObj.rproxyHost,
        port: 45671,
        recordJarTime: true,
        timeout: constants.acceptSslffRequestTimeout,
        path: '/acceptssl_ff?someParam=abc',
        headers: { 'BStack-Host': 'somehostname', 'X-Source-Env-Type': 'development' },
      });
      requestLibCall.restore();
    });
  });
});
