

const { afterEach } = require('mocha');
const rewire = require('rewire');
const sinon = require('sinon');
const { globalS3ClientRegistry } = require('../../../constants');
const { assert } = require('chai');


describe('getS3Client', () => {
  let mod;
  let s3CLientStub;

  beforeEach(() => {
    mod = rewire('../../../helpers/s3UploadHelper');
    s3CLientStub = sinon.stub();
  });

  afterEach(() => {
    // Remove all properties
    for (const key in globalS3ClientRegistry) {
      delete globalS3ClientRegistry[key];
    }
  });

  it('should return s3 client', () => {
    const key = {
      s3key: 'key',
      s3secret: 'secret',
      s3bucket: 'bucket',
    };

    globalS3ClientRegistry[key.s3bucket] = {};
    globalS3ClientRegistry[key.s3bucket][key.s3key] = {};
    globalS3ClientRegistry[key.s3bucket][key.s3key][key.s3secret] = s3CLientStub;

    const s3Client = mod.__get__('getS3Client')(key);
    assert.deepEqual(s3Client, s3CLientStub);
  });

  it('should create s3 client', () => {
    const key1 = {
      s3key: 'key',
      s3secret: 'secret',
      s3bucket: 'bucket',
    };

    globalS3ClientRegistry[key1.s3bucket] = {};
    globalS3ClientRegistry[key1.s3bucket][key1.s3key] = {};
    globalS3ClientRegistry[key1.s3bucket][key1.s3key][key1.s3secret] = s3CLientStub;

    const key = {
      s3Key: 'key',
      s3Secret: 'secret',
      s3Bucket: 'bucket2',
    };

    const s3CLientStub2 = sinon.stub();
    const s3Stub = sinon.stub().returns(s3CLientStub2);

    mod.__set__('AWS', { S3: s3Stub });

    const s3Client = mod.__get__('getS3Client')(key);
    assert.deepEqual(s3Client, new s3CLientStub2);
  });
});

