'use strict';

const sessionManagerHelper = require('../../../../services/session/sessionManagerHelper');
const instrumentation = require('../../../../helpers/instrumentation');
const constants = require('../../../../constants');
const util = require('util');
const { assert } = require('chai');
const sinon = require('sinon');

describe('sessionManagerHelper tests', () => {
  describe('getKeyObject', () => {
    it('should return form global_registry if exists', async () => {
      const randomSessionId = (Math.random() + 1).toString(36).substring(7);
      const keyObj = {
        rails_session_id: randomSessionId,
      };
      constants.global_registry[randomSessionId] = keyObj;
      const sessionKeyObj = await sessionManagerHelper.getKeyObject(randomSessionId);
      assert.deepEqual(sessionKeyObj, keyObj);
      delete constants.global_registry[randomSessionId];
    });

    it('should extract sesson from redis if not in global_registry, doesn;t exists in redis', async () => {
      const promisify = sinon.stub(util, 'promisify').returns(() => {});
      const sessionKeyObj = await sessionManagerHelper.getKeyObject('randomSessionId');
      assert.deepEqual(sessionKeyObj, undefined);
      promisify.restore();
    });

    it('should extract sesson from redis if not in global_registry, exists in redis', async () => {
      const keyObj = {
        rails_session_id: 'tests',
      };
      const promisify = sinon.stub(util, 'promisify').returns(() => keyObj);
      const sessionKeyObj = await sessionManagerHelper.getKeyObject('randomSessionId');
      assert.deepEqual(sessionKeyObj, keyObj);
      promisify.restore();
    });
  });

  describe('removeFromMemory', () => {
    it('should remove from memory', () => {
      const randomSessionId = (Math.random() + 1).toString(36).substring(7);
      const sessionKeyObj = {
        rails_session_id: randomSessionId,
        os: 'win',
        terminate: sinon.stub(),
      };
      constants.sessions_registry[sessionKeyObj.rails_session_id] = sessionKeyObj;
      constants.stop_sessions_registry[sessionKeyObj.os] = {};
      constants.stop_sessions_registry[sessionKeyObj.os][sessionKeyObj.rails_session_id] = sessionKeyObj;
      constants.global_registry[sessionKeyObj.rails_session_id] = sessionKeyObj;
      constants.timeout_registry[sessionKeyObj.rails_session_id] = sessionKeyObj;
      constants.execution_time_registry[sessionKeyObj.rails_session_id] = sessionKeyObj;
      constants.global_ws_registry[sessionKeyObj.rails_session_id] = sessionKeyObj;
      const pushFeatureUsage = sinon.stub(instrumentation, 'pushFeatureUsage');
      sessionManagerHelper.removeFromMemory(sessionKeyObj);
      assert.deepEqual(constants.sessions_registry[sessionKeyObj.rails_session_id], undefined);
      assert.deepEqual(constants.stop_sessions_registry[sessionKeyObj.os][sessionKeyObj.rails_session_id], undefined);
      assert.deepEqual(constants.global_registry[sessionKeyObj.rails_session_id], undefined);
      assert.deepEqual(constants.timeout_registry[sessionKeyObj.rails_session_id], undefined);
      assert.deepEqual(constants.execution_time_registry[sessionKeyObj.rails_session_id], undefined);
      assert.deepEqual(constants.global_ws_registry[sessionKeyObj.rails_session_id], undefined);
      sinon.assert.calledOnce(pushFeatureUsage);
      sinon.assert.calledOnce(sessionKeyObj.terminate);
      pushFeatureUsage.restore();
    });
  });
});
