var helper = require('./helper'),
  constants = require('../../constants'),
  LL = constants.LOG_LEVEL,
  origHelper = require('../../helper'),
  requestlib = require('../../lib/request'),
  HubLogger = require('../../log'),
  supporting = require('../../supporting'),
  pubSub = require('../../pubSub'),
  sinon = require('sinon'),
  chai = require('chai').should();

describe("origHelper tests", function() {
  describe("isHash function", function() {
    it("behaves properly", function(done) {
      origHelper.isHash({ "a" : "hash" }).should.be.true;
      origHelper.isHash(true).should.be.false;
      origHelper.isHash(2).should.be.false;
      origHelper.isHash("dsafsdf").should.be.false;
      origHelper.isHash([ "Asd" ]).should.be.false;
      origHelper.isHash(undefined).should.be.false;
      origHelper.isHash(null).should.be.false;
      done();
    });
  });

  describe("getHashOrEmpty function", function() {
    it("behaves properly", function(done) {
      origHelper.getHashOrEmpty({ "a" : "hash" }).should.eql({ "a" : "hash" });
      origHelper.getHashOrEmpty(true).should.eql({});
      origHelper.getHashOrEmpty(2).should.eql({});
      origHelper.getHashOrEmpty("dsafsdf").should.eql({});
      origHelper.getHashOrEmpty([ "Asd" ]).should.eql({});
      origHelper.getHashOrEmpty(undefined).should.eql({});
      done();
    });
  });

  describe("getArrayOrEmpty function", function() {
    it("behaves properly", function(done) {
      origHelper.getArrayOrEmpty({ "a" : "hash" }).should.eql([]);
      origHelper.getArrayOrEmpty(true).should.eql([]);
      origHelper.getArrayOrEmpty(2).should.eql([]);
      origHelper.getArrayOrEmpty("dsafsdf").should.eql([]);
      origHelper.getArrayOrEmpty([ "Asd" ]).should.eql([ "Asd" ]);
      origHelper.getArrayOrEmpty(undefined).should.eql([]);
      done();
    });
  });

  describe("versionCompare function", function() {
    it("behaves properly", function(done) {
      origHelper.versionCompare("1.2.3", "1.2.3").should.eql(0);
      origHelper.versionCompare("1.2", "1.2").should.eql(0);
      origHelper.versionCompare("1", "1").should.eql(0);

      origHelper.versionCompare("1", "2.3").should.eql(-1);
      origHelper.versionCompare("2.3", "1").should.eql(1);
      origHelper.versionCompare("1.2.3", "2.3").should.eql(-1);
      origHelper.versionCompare("2.3", "1.2.3").should.eql(1);
      origHelper.versionCompare("1", "2.3.1").should.eql(-1);
      origHelper.versionCompare("2.3.1", "1").should.eql(1);
      origHelper.versionCompare("2", "1.11.0").should.eql(1);


      origHelper.versionCompare("3.1.0", "3.5.5").should.eql(-4);
      origHelper.versionCompare("3.14.4", "3.4.5").should.eql(10);

      origHelper.versionCompare("3.3", "3.4.5").should.eql(-1);
      origHelper.versionCompare("3.4", "3.4.5").should.eql(-1);
      origHelper.versionCompare("3.5", "3.4.5").should.eql(1);
      origHelper.versionCompare("3", "3.4.5").should.eql(-2);
      origHelper.versionCompare("2.1", "3.1").should.eql(-1);
      origHelper.versionCompare("3.1", "2.1").should.eql(1);
      origHelper.versionCompare("1.2.4", "1.2.3").should.eql(1);
      origHelper.versionCompare("1.4.3", "1.2.3").should.eql(2);
      origHelper.versionCompare("4.2.3", "1.2.3").should.eql(3);

      done();
    });
  });

  describe("isTrue function", function() {
    it("isTrue returns boolean true only for string / boolean true", function(done) {
      origHelper.isTrue(true).should.be.true;
      origHelper.isTrue("true").should.be.true;
      origHelper.isTrue("True").should.be.true;
      origHelper.isTrue(false).should.be.false;
      origHelper.isTrue("false").should.be.false;
      origHelper.isTrue("random").should.be.false;
      origHelper.isTrue([]).should.be.false;
      origHelper.isTrue({}).should.be.false;
      origHelper.isTrue(undefined).should.be.false;
      origHelper.isTrue(null).should.be.false;
      done();
    });
  });

  describe("getRandom function", function() {
    it("getRandom returns values between min and max", function(done) {
      var maxIter = 100;
      var rangeArr = [
        { min: 0, max: 5 },
        { min: -5, max: 5 },
        { min: 0, max: 1 },
        { min: -1, max: 1 }
      ];
      rangeArr.forEach((rangeHash) => {
        for(var iter = 0; iter < maxIter; iter += 1) {
          origHelper.getRandom(rangeHash.min, rangeHash.max).should.within(rangeHash.min, rangeHash.max);
        }
      });
      done();
    });
  });
  describe("getRandom function", function() {
    it("getRandom returns values between min and max", function(done) {
      var maxIter = 100;
      var rangeArr = [
        { min: 0, max: 5 },
        { min: -5, max: 5 },
        { min: 0, max: 1 },
        { min: -1, max: 1 }
      ];
      rangeArr.forEach((rangeHash) => {
        for(var iter = 0; iter < maxIter; iter += 1) {
          origHelper.getRandom(rangeHash.min, rangeHash.max).should.within(rangeHash.min, rangeHash.max);
        }
      });
      done();
    });
  });
});

describe('Supporting', function() {
  describe('checkURLStatus', function() {
    xit('Sends a terminal request to get status when called');
  });
  describe('modifySecondaryStateForTest', function() {
    var parsedJSON, keyObject, options;

    beforeEach(function() {
      parsedJSON = {
        p45691: 'up',
        status: 400,
        header: {
          'www-authenticate': "Basic realm=\"Application\""
        },
        message: 'default'
      };
      keyObject = helper.getKeyObject();
      constants['global_registry'][keyObject.rails_session_id] = {
        secondary_state: constants.secondary_states.SUCCESS
      };
      options = {
        isBauth: false
      };

      sinon.stub(pubSub, 'publish');
      sinon.stub(origHelper, 'pushToCLS');
      sinon.stub(origHelper, 'getString');
    });
    it('Status: 401 required', function() {
      origHelper.getString.returns('401');

      supporting.modifySecondaryStateForTest(parsedJSON, keyObject.rails_session_id, options, keyObject);

      origHelper.getString.called.should.be.true;
      origHelper.getString.calledWith(parsedJSON.status).should.be.true;
      pubSub.publish.called.should.be.true;
      pubSub.publish.calledWith(constants.updateKeyObject, {
        session: keyObject.rails_session_id,
        changed: {
          secondary_state: constants.global_registry[keyObject.rails_session_id].secondary_state,
          check_url: constants.global_registry[keyObject.rails_session_id].secondary_state
        }
      }).should.be.true;
      origHelper.pushToCLS.called.should.be.true;
      origHelper.pushToCLS.calledWith('modifySecondaryState', {
        session_id: keyObject.rails_session_id,
        secondary_state: constants.global_registry[keyObject.rails_session_id].secondary_state,
        checkURLMessage: parsedJSON
      }).should.be.true;

      constants.global_registry[keyObject.rails_session_id].secondary_state.should.equal(constants.secondary_states["401"][parsedJSON.header['www-authenticate']] + '-required');
      constants.global_registry[keyObject.rails_session_id].secondary_state.should.not.equal('unknown-error');
    });
    it('Status: 401 failed', function() {
      options.isBauth = true;
      origHelper.getString.returns('401');

      supporting.modifySecondaryStateForTest(parsedJSON, keyObject.rails_session_id, options, keyObject);

      origHelper.getString.called.should.be.true;
      origHelper.getString.calledWith(parsedJSON.status).should.be.true;
      pubSub.publish.called.should.be.true;
      pubSub.publish.calledWith(constants.updateKeyObject, {
        session: keyObject.rails_session_id,
        changed: {
          secondary_state: constants.global_registry[keyObject.rails_session_id].secondary_state,
          check_url: constants.global_registry[keyObject.rails_session_id].secondary_state
        }
      }).should.be.true;
      origHelper.pushToCLS.called.should.be.true;
      origHelper.pushToCLS.calledWith('modifySecondaryState', {
        session_id: keyObject.rails_session_id,
        secondary_state: constants.global_registry[keyObject.rails_session_id].secondary_state,
        checkURLMessage: parsedJSON
      }).should.be.true;

      constants.global_registry[keyObject.rails_session_id].secondary_state.should.equal(constants.secondary_states["401"][parsedJSON.header['www-authenticate']] + '-failed');
      constants.global_registry[keyObject.rails_session_id].secondary_state.should.not.equal('unknown-error');
    });
    it('Status: 502', function() {
      origHelper.getString.returns('502');

      supporting.modifySecondaryStateForTest(parsedJSON, keyObject.rails_session_id, options, keyObject);

      origHelper.getString.called.should.be.true;
      origHelper.getString.calledWith(parsedJSON.status).should.be.true;
      pubSub.publish.called.should.be.true;
      pubSub.publish.calledWith(constants.updateKeyObject, {
        session: keyObject.rails_session_id,
        changed: {
          secondary_state: constants.global_registry[keyObject.rails_session_id].secondary_state,
          check_url: constants.global_registry[keyObject.rails_session_id].secondary_state
        }
      }).should.be.true;
      origHelper.pushToCLS.called.should.be.true;
      origHelper.pushToCLS.calledWith('modifySecondaryState', {
        session_id: keyObject.rails_session_id,
        secondary_state: constants.global_registry[keyObject.rails_session_id].secondary_state,
        checkURLMessage: parsedJSON
      }).should.be.true;

      constants.global_registry[keyObject.rails_session_id].secondary_state.should.equal('success');
      constants.global_registry[keyObject.rails_session_id].secondary_state.should.not.equal('unknown-error');
    });
    it('Status: 503 with tunnel', function() {
      origHelper.getString.returns('503');
      constants.global_registry[keyObject.rails_session_id].tunnel = true;

      supporting.modifySecondaryStateForTest(parsedJSON, keyObject.rails_session_id, options, keyObject);

      origHelper.getString.called.should.be.true;
      origHelper.getString.calledWith(parsedJSON.status).should.be.true;
      pubSub.publish.called.should.be.true;
      pubSub.publish.calledWith(constants.updateKeyObject, {
        session: keyObject.rails_session_id,
        changed: {
          secondary_state: constants.global_registry[keyObject.rails_session_id].secondary_state,
          check_url: constants.global_registry[keyObject.rails_session_id].secondary_state
        }
      }).should.be.true;
      origHelper.pushToCLS.called.should.be.true;
      origHelper.pushToCLS.calledWith('modifySecondaryState', {
        session_id: keyObject.rails_session_id,
        secondary_state: constants.global_registry[keyObject.rails_session_id].secondary_state,
        checkURLMessage: parsedJSON
      }).should.be.true;

      constants.global_registry[keyObject.rails_session_id].secondary_state.should.equal('success');
      constants.global_registry[keyObject.rails_session_id].secondary_state.should.not.equal('unknown-error');
    });
    xit('Status: 503 without tunnel', function() {
      origHelper.getString.returns('503');
      constants.global_registry[keyObject.rails_session_id].tunnel = false;

      supporting.modifySecondaryStateForTest(parsedJSON, keyObject.rails_session_id, options, keyObject);

      origHelper.getString.called.should.be.true;
      origHelper.getString.calledWith(parsedJSON.status).should.be.true;
      pubSub.publish.called.should.be.true;
      pubSub.publish.calledWith(constants.updateKeyObject, {
        session: keyObject.rails_session_id,
        changed: {
          secondary_state: constants.global_registry[keyObject.rails_session_id].secondary_state,
          check_url: constants.global_registry[keyObject.rails_session_id].secondary_state
        }
      }).should.be.true;
      origHelper.pushToCLS.called.should.be.true;
      origHelper.pushToCLS.calledWith('modifySecondaryState', {
        session_id: keyObject.rails_session_id,
        secondary_state: constants.global_registry[keyObject.rails_session_id].secondary_state,
        checkURLMessage: parsedJSON
      }).should.be.true;

      constants.global_registry[keyObject.rails_session_id].secondary_state.should.equal('tunnel-required');
      constants.global_registry[keyObject.rails_session_id].secondary_state.should.not.equal('unknown-error');
    });
    it('Status: Otherwise on 45691 down', function() {
      origHelper.getString.restore();
      sinon.stub(origHelper, 'getString', function(arg) {
        if(arg === parsedJSON.status) {
          return 20;
        } else {
          return "false";
        }
      });
      constants.global_registry[keyObject.rails_session_id].tunnel = false;

      supporting.modifySecondaryStateForTest(parsedJSON, keyObject.rails_session_id, options, keyObject);

      origHelper.getString.called.should.be.true;
      origHelper.getString.calledWith(parsedJSON.status).should.be.true;
      origHelper.getString.calledWith(parsedJSON.p45691).should.be.true;
      pubSub.publish.called.should.be.true;
      pubSub.publish.calledWith(constants.updateKeyObject, {
        session: keyObject.rails_session_id,
        changed: {
          secondary_state: constants.global_registry[keyObject.rails_session_id].secondary_state,
          check_url: constants.global_registry[keyObject.rails_session_id].secondary_state
        }
      }).should.be.true;
      origHelper.pushToCLS.called.should.be.true;
      origHelper.pushToCLS.calledWith('modifySecondaryState', {
        session_id: keyObject.rails_session_id,
        secondary_state: constants.global_registry[keyObject.rails_session_id].secondary_state,
        checkURLMessage: parsedJSON
      }).should.be.true;

      constants.global_registry[keyObject.rails_session_id].secondary_state.should.equal('45691 down ');
      constants.global_registry[keyObject.rails_session_id].secondary_state.should.not.equal('unknown-error');
    });
    it('Status: Otherwise on tunnel required', function() {
      origHelper.getString.restore();
      sinon.stub(origHelper, 'getString', function(arg) {
        if(arg === parsedJSON.status) {
          return 20;
        } else {
          return "true";
        }
      });
      constants.global_registry[keyObject.rails_session_id].tunnel = false;

      supporting.modifySecondaryStateForTest(parsedJSON, keyObject.rails_session_id, options, keyObject);

      origHelper.getString.called.should.be.true;
      origHelper.getString.calledWith(parsedJSON.status).should.be.true;
      origHelper.getString.calledWith(parsedJSON.p45691).should.be.true;
      pubSub.publish.called.should.be.true;
      pubSub.publish.calledWith(constants.updateKeyObject, {
        session: keyObject.rails_session_id,
        changed: {
          secondary_state: constants.global_registry[keyObject.rails_session_id].secondary_state,
          check_url: constants.global_registry[keyObject.rails_session_id].secondary_state
        }
      }).should.be.true;
      origHelper.pushToCLS.called.should.be.true;
      origHelper.pushToCLS.calledWith('modifySecondaryState', {
        session_id: keyObject.rails_session_id,
        secondary_state: constants.global_registry[keyObject.rails_session_id].secondary_state,
        checkURLMessage: parsedJSON
      }).should.be.true;

      constants.global_registry[keyObject.rails_session_id].secondary_state.should.equal('success');
    });
    it('Status: 1', function() {
      origHelper.getString.returns('1');
      constants.global_registry[keyObject.rails_session_id].tunnel = false;

      supporting.modifySecondaryStateForTest(parsedJSON, keyObject.rails_session_id, options, keyObject);

      origHelper.getString.called.should.be.true;
      origHelper.getString.calledWith(parsedJSON.status).should.be.true;
      origHelper.getString.calledWith(parsedJSON.p45691).should.be.true;
      pubSub.publish.called.should.be.true;
      pubSub.publish.calledWith(constants.updateKeyObject, {
        session: keyObject.rails_session_id,
        changed: {
          secondary_state: constants.global_registry[keyObject.rails_session_id].secondary_state,
          check_url: constants.global_registry[keyObject.rails_session_id].secondary_state
        }
      }).should.be.true;
      origHelper.pushToCLS.called.should.be.true;
      origHelper.pushToCLS.calledWith('modifySecondaryState', {
        session_id: keyObject.rails_session_id,
        secondary_state: constants.global_registry[keyObject.rails_session_id].secondary_state,
        checkURLMessage: parsedJSON
      }).should.be.true;

      constants.global_registry[keyObject.rails_session_id].secondary_state.should.equal('socket-error');
    });
    it('Status: 2', function() {
      origHelper.getString.returns('2');
      constants.global_registry[keyObject.rails_session_id].tunnel = false;

      supporting.modifySecondaryStateForTest(parsedJSON, keyObject.rails_session_id, options, keyObject);

      origHelper.getString.called.should.be.true;
      origHelper.getString.calledWith(parsedJSON.status).should.be.true;
      origHelper.getString.calledWith(parsedJSON.p45691).should.be.true;
      pubSub.publish.called.should.be.true;
      pubSub.publish.calledWith(constants.updateKeyObject, {
        session: keyObject.rails_session_id,
        changed: {
          secondary_state: constants.global_registry[keyObject.rails_session_id].secondary_state,
          check_url: constants.global_registry[keyObject.rails_session_id].secondary_state
        }
      }).should.be.true;
      origHelper.pushToCLS.called.should.be.true;
      origHelper.pushToCLS.calledWith('modifySecondaryState', {
        session_id: keyObject.rails_session_id,
        secondary_state: constants.global_registry[keyObject.rails_session_id].secondary_state,
        checkURLMessage: parsedJSON
      }).should.be.true;

      constants.global_registry[keyObject.rails_session_id].secondary_state.should.equal('host-unreachable');
    });
    it('Status: 3', function() {
      origHelper.getString.returns('3');
      constants.global_registry[keyObject.rails_session_id].tunnel = false;

      supporting.modifySecondaryStateForTest(parsedJSON, keyObject.rails_session_id, options, keyObject);

      origHelper.getString.called.should.be.true;
      origHelper.getString.calledWith(parsedJSON.status).should.be.true;
      origHelper.getString.calledWith(parsedJSON.p45691).should.be.true;
      pubSub.publish.called.should.be.true;
      pubSub.publish.calledWith(constants.updateKeyObject, {
        session: keyObject.rails_session_id,
        changed: {
          secondary_state: constants.global_registry[keyObject.rails_session_id].secondary_state,
          check_url: constants.global_registry[keyObject.rails_session_id].secondary_state
        }
      }).should.be.true;
      origHelper.pushToCLS.called.should.be.true;
      origHelper.pushToCLS.calledWith('modifySecondaryState', {
        session_id: keyObject.rails_session_id,
        secondary_state: constants.global_registry[keyObject.rails_session_id].secondary_state,
        checkURLMessage: parsedJSON
      }).should.be.true;

      constants.global_registry[keyObject.rails_session_id].secondary_state.should.equal('connection-refused');
    });
    it('Status: 4', function() {
      origHelper.getString.returns('4');
      constants.global_registry[keyObject.rails_session_id].tunnel = false;

      supporting.modifySecondaryStateForTest(parsedJSON, keyObject.rails_session_id, options, keyObject);

      origHelper.getString.called.should.be.true;
      origHelper.getString.calledWith(parsedJSON.status).should.be.true;
      origHelper.getString.calledWith(parsedJSON.p45691).should.be.true;
      pubSub.publish.called.should.be.true;
      pubSub.publish.calledWith(constants.updateKeyObject, {
        session: keyObject.rails_session_id,
        changed: {
          secondary_state: constants.global_registry[keyObject.rails_session_id].secondary_state,
          check_url: constants.global_registry[keyObject.rails_session_id].secondary_state
        }
      }).should.be.true;
      origHelper.pushToCLS.called.should.be.true;
      origHelper.pushToCLS.calledWith('modifySecondaryState', {
        session_id: keyObject.rails_session_id,
        secondary_state: constants.global_registry[keyObject.rails_session_id].secondary_state,
        checkURLMessage: parsedJSON
      }).should.be.true;

      constants.global_registry[keyObject.rails_session_id].secondary_state.should.equal('timeout');
    });
    it('Status: 5', function() {
      origHelper.getString.returns('5');
      constants.global_registry[keyObject.rails_session_id].tunnel = false;

      supporting.modifySecondaryStateForTest(parsedJSON, keyObject.rails_session_id, options, keyObject);

      origHelper.getString.called.should.be.true;
      origHelper.getString.calledWith(parsedJSON.status).should.be.true;
      origHelper.getString.calledWith(parsedJSON.p45691).should.be.true;
      pubSub.publish.called.should.be.true;
      pubSub.publish.calledWith(constants.updateKeyObject, {
        session: keyObject.rails_session_id,
        changed: {
          secondary_state: constants.global_registry[keyObject.rails_session_id].secondary_state,
          check_url: constants.global_registry[keyObject.rails_session_id].secondary_state
        }
      }).should.be.true;
      origHelper.pushToCLS.called.should.be.true;
      origHelper.pushToCLS.calledWith('modifySecondaryState', {
        session_id: keyObject.rails_session_id,
        secondary_state: constants.global_registry[keyObject.rails_session_id].secondary_state,
        checkURLMessage: parsedJSON
      }).should.be.true;

      constants.global_registry[keyObject.rails_session_id].secondary_state.should.equal('invalid-uri');
    });
    it('Status: 6', function() {
      origHelper.getString.returns('6');
      constants.global_registry[keyObject.rails_session_id].tunnel = false;

      supporting.modifySecondaryStateForTest(parsedJSON, keyObject.rails_session_id, options, keyObject);

      origHelper.getString.called.should.be.true;
      origHelper.getString.calledWith(parsedJSON.status).should.be.true;
      origHelper.getString.calledWith(parsedJSON.p45691).should.be.true;
      pubSub.publish.called.should.be.true;
      pubSub.publish.calledWith(constants.updateKeyObject, {
        session: keyObject.rails_session_id,
        changed: {
          secondary_state: constants.global_registry[keyObject.rails_session_id].secondary_state,
          check_url: constants.global_registry[keyObject.rails_session_id].secondary_state
        }
      }).should.be.true;
      origHelper.pushToCLS.called.should.be.true;
      origHelper.pushToCLS.calledWith('modifySecondaryState', {
        session_id: keyObject.rails_session_id,
        secondary_state: constants.global_registry[keyObject.rails_session_id].secondary_state,
        checkURLMessage: parsedJSON
      }).should.be.true;

      constants.global_registry[keyObject.rails_session_id].secondary_state.should.equal('http-error');
    });
    it('Status: 7', function() {
      origHelper.getString.returns('7');
      constants.global_registry[keyObject.rails_session_id].tunnel = false;

      supporting.modifySecondaryStateForTest(parsedJSON, keyObject.rails_session_id, options, keyObject);

      origHelper.getString.called.should.be.true;
      origHelper.getString.calledWith(parsedJSON.status).should.be.true;
      origHelper.getString.calledWith(parsedJSON.p45691).should.be.true;
      pubSub.publish.called.should.be.true;
      pubSub.publish.calledWith(constants.updateKeyObject, {
        session: keyObject.rails_session_id,
        changed: {
          secondary_state: constants.global_registry[keyObject.rails_session_id].secondary_state,
          check_url: constants.global_registry[keyObject.rails_session_id].secondary_state
        }
      }).should.be.true;
      origHelper.pushToCLS.called.should.be.true;
      origHelper.pushToCLS.calledWith('modifySecondaryState', {
        session_id: keyObject.rails_session_id,
        secondary_state: constants.global_registry[keyObject.rails_session_id].secondary_state,
        checkURLMessage: parsedJSON
      }).should.be.true;

      constants.global_registry[keyObject.rails_session_id].secondary_state.should.equal('max-redirects');
    });
    it('Status: 8', function() {
      origHelper.getString.returns('8');
      constants.global_registry[keyObject.rails_session_id].tunnel = false;

      supporting.modifySecondaryStateForTest(parsedJSON, keyObject.rails_session_id, options, keyObject);

      origHelper.getString.called.should.be.true;
      origHelper.getString.calledWith(parsedJSON.status).should.be.true;
      origHelper.getString.calledWith(parsedJSON.p45691).should.be.true;
      pubSub.publish.called.should.be.true;
      pubSub.publish.calledWith(constants.updateKeyObject, {
        session: keyObject.rails_session_id,
        changed: {
          secondary_state: constants.global_registry[keyObject.rails_session_id].secondary_state,
          check_url: constants.global_registry[keyObject.rails_session_id].secondary_state
        }
      }).should.be.true;
      origHelper.pushToCLS.called.should.be.true;
      origHelper.pushToCLS.calledWith('modifySecondaryState', {
        session_id: keyObject.rails_session_id,
        secondary_state: constants.global_registry[keyObject.rails_session_id].secondary_state,
        checkURLMessage: parsedJSON
      }).should.be.true;

      constants.global_registry[keyObject.rails_session_id].secondary_state.should.equal('default');
    });
    it('terminal down', function() {
      parsedJSON.p45691 = 'down';
      parsedJSON.p45696 = 'down';
      origHelper.getString.returns('8');
      constants.global_registry[keyObject.rails_session_id].tunnel = true;

      supporting.modifySecondaryStateForTest(parsedJSON, keyObject.rails_session_id, options, keyObject);

      origHelper.getString.called.should.be.true;
      origHelper.getString.calledWith(parsedJSON.status).should.be.true;
      origHelper.getString.calledWith(parsedJSON.p45691).should.be.true;
      pubSub.publish.called.should.be.true;
      pubSub.publish.calledWith(constants.updateKeyObject, {
        session: keyObject.rails_session_id,
        changed: {
          secondary_state: constants.global_registry[keyObject.rails_session_id].secondary_state,
          check_url: constants.global_registry[keyObject.rails_session_id].secondary_state
        }
      }).should.be.true;
      origHelper.pushToCLS.called.should.be.true;
      origHelper.pushToCLS.calledWith('modifySecondaryState', {
        session_id: keyObject.rails_session_id,
        secondary_state: constants.global_registry[keyObject.rails_session_id].secondary_state,
        checkURLMessage: parsedJSON
      }).should.be.true;

      constants.global_registry[keyObject.rails_session_id].secondary_state.should.equal('proxy-down-on-terminal');
    });
    afterEach(function() {
      pubSub.publish.restore();
      origHelper.pushToCLS.restore();
      origHelper.getString.restore();
      delete constants.global_registry[keyObject.rails_session_id];
    })
  });
  describe('checkActiveElementOnTerminal', function() {
    it('Execute script request to check active element', function() {
      var keyObject = helper.getKeyObject(),
        success_callback = sinon.stub(),
        error_callback = sinon.stub(),
        request_json = {
          "script":"return document.activeElement",
          "args": []
        },
        options = {
          hostname: keyObject.rproxyHost,
          port: keyObject.port,
          path: "/wd/hub/session/"+keyObject.key+"/execute",
          method: "POST",
          body: JSON.stringify(request_json),
          timeout: constants.NODE_DIED_IN,
          headers: requestlib.appendBStackHostHeader(keyObject.name),
          recordJarTime: true,
        },
        data = '{"value":"data"}',
        res = {
          data: data
        },
        catch_stub = sinon.stub().callsArgWith(0, 'err'),
        then_stub = sinon.stub().callsArgWith(0, res).returns({
          catch: catch_stub
        });

      sinon.stub(requestlib, 'call').returns({
        then: then_stub
      });
      sinon.stub(HubLogger, 'miscLogger');
      sinon.stub(HubLogger, 'exceptionLogger');

      constants['global_registry'][keyObject.rails_session_id] = {
        secondary_state: constants.secondary_states.SUCCESS
      };

      supporting.checkActiveElementOnTerminal(keyObject, success_callback, error_callback);

      HubLogger.miscLogger.called.should.be.true;
      HubLogger.miscLogger.calledWith("checkActiveElementOnTerminal", "sessionId: " + keyObject.rails_session_id + " status: in function" + keyObject.key, LL.INFO).should.be.true;
      HubLogger.miscLogger.calledWith("checkActiveElementOnTerminal", "sessionId: " + keyObject.rails_session_id + " status: success, data: " + data, LL.INFO).should.be.true;
      HubLogger.exceptionLogger.called.should.be.true;
      HubLogger.exceptionLogger.calledWith("checkActiveElementOnTerminal", "sessionId: " + keyObject.rails_session_id + " status: request errorerr").should.be.true;
      requestlib.call.called.should.be.true;
      requestlib.call.calledWith(options).should.be.true;
      constants.global_registry[keyObject.rails_session_id].isMetaPageLoadTimeout.should.be.true;
      success_callback.called.should.be.true;
      success_callback.calledWith(JSON.parse(data), res).should.be.true;
      error_callback.called.should.be.true
      error_callback.calledWith('err').should.be.true;

      requestlib.call.restore();
      HubLogger.miscLogger.restore();
      HubLogger.exceptionLogger.restore();
      delete constants.global_registry[keyObject.rails_session_id];
    });
  });
  describe('checkPageLoadOnTerminal', function() {
    it('Execute script requests to check page loading status', function() {
      var keyObject = helper.getKeyObject(),
        success_callback = sinon.stub(),
        error_callback = sinon.stub(),
        request_json = {
          "script":"return document.readyState",
          "args": []
        },
        body = JSON.stringify(request_json),
        headers = {
          "accept": "application/json",
          "content-type": "application/json; charset=utf-8",
          "content-length": body.length,
          "accept-encoding": "gzip;q=1.0,deflate;q=0.6,identity;q=0.3"
        },
        options = {
          hostname: keyObject.rproxyHost,
          port: keyObject.port,
          path: "/wd/hub/session/"+keyObject.key+"/execute",
          method: "POST",
          body: body,
          headers: headers,
          recordJarTime: true,
          timeout: 10000
        },
        data = '{"value":"data"}',
        res = {
          data: data
        },
        catch_stub = sinon.stub().callsArgWith(0, 'err'),
        then_stub = sinon.stub().callsArgWith(0, res).returns({
          catch: catch_stub
        });
      requestlib.appendBStackHostHeader(keyObject.name, options.headers);

      sinon.stub(requestlib, 'call').returns({
        then: then_stub
      });
      sinon.stub(HubLogger, 'miscLogger');
      sinon.stub(HubLogger, 'exceptionLogger');

      constants['global_registry'][keyObject.rails_session_id] = {
        secondary_state: constants.secondary_states.SUCCESS
      };

      supporting.checkPageLoadOnTerminal(keyObject, success_callback, error_callback, true);

      data = JSON.parse(data);

      HubLogger.miscLogger.called.should.be.true;
      HubLogger.miscLogger.calledWith("checkPageLoadOnTerminal", "sessionId: " + keyObject.rails_session_id + " status: in function " + keyObject.key, LL.DEBUG).should.be.true;
      HubLogger.miscLogger.calledWith("checkPageLoadOnTerminal", "sessionId: " + keyObject.rails_session_id + " status: success \"data\"", LL.DEBUG).should.be.true;
      HubLogger.miscLogger.calledWith("checkPageLoadOnTerminal", "sessionId: " + keyObject.rails_session_id + " status: error" + JSON.stringify(data["value"] && data["value"]["message"] || ""), LL.INFO).should.be.true;
      HubLogger.exceptionLogger.called.should.be.true;
      HubLogger.exceptionLogger.calledWith("checkPageLoadOnTerminal", "sessionId: " + keyObject.rails_session_id + " status: request errorerr").should.be.true;
      requestlib.call.called.should.be.true;
      requestlib.call.calledWith(options).should.be.true;
      constants.global_registry[keyObject.rails_session_id].isMetaPageLoadTimeout.should.be.true;
      success_callback.called.should.be.true;
      success_callback.calledWith(data, res).should.be.true;
      error_callback.called.should.be.true
      error_callback.calledWith('err').should.be.true;

      requestlib.call.restore();
      HubLogger.miscLogger.restore();
      HubLogger.exceptionLogger.restore();
      delete constants.global_registry[keyObject.rails_session_id];
    });
  });
  describe('openURLOnTimeout', function() {
    it('Open URL request for about:blank', function() {
      var keyObject = helper.getKeyObject(),
        success_callback = sinon.stub(),
        error_callback = sinon.stub(),
        request_json = {
          "url":"about:blank"
        },
        options = {
          hostname: keyObject.rproxyHost,
          port: keyObject.port,
          path: "/wd/hub/session/"+keyObject.key+"/url",
          method: "POST",
          body: JSON.stringify(request_json),
          timeout: constants.BS_TIMEOUT,
          headers: requestlib.appendBStackHostHeader(keyObject.name),
        },
        data = '{"value":"data"}',
        res = {
          data: data
        },
        catch_stub = sinon.stub().callsArgWith(0, 'err'),
        then_stub = sinon.stub().callsArgWith(0, res).returns({
          catch: catch_stub
        });

      sinon.stub(requestlib, 'call').returns({
        then: then_stub
      });
      sinon.stub(HubLogger, 'miscLogger');
      sinon.stub(HubLogger, 'exceptionLogger');

      supporting.openURLOnTimeout(keyObject, success_callback, error_callback);

      data = JSON.parse(data);

      HubLogger.miscLogger.called.should.be.true;
      HubLogger.miscLogger.calledWith("openURLOnTimeout", "sessionId: " + keyObject.rails_session_id + " status: in function" + keyObject.key, LL.INFO).should.be.true;
      HubLogger.miscLogger.calledWith("openURLOnTimeout", "sessionId: " + keyObject.rails_session_id + " status: success", LL.INFO).should.be.true;
      HubLogger.exceptionLogger.called.should.be.true;
      HubLogger.exceptionLogger.calledWith("openURLOnTimeout", "sessionId: " + keyObject.rails_session_id + " status: request errorerr").should.be.true;
      requestlib.call.called.should.be.true;
      requestlib.call.calledWith(options).should.be.true;
      success_callback.called.should.be.true;
      success_callback.calledWith(data, res).should.be.true;
      error_callback.called.should.be.true
      error_callback.calledWith('err').should.be.true;

      requestlib.call.restore();
      HubLogger.miscLogger.restore();
      HubLogger.exceptionLogger.restore();
    });
  });
  describe('setPageLoadTimeoutOnTerminal', function() {
    it('Set page load timeout request on terminal', function() {
      var keyObject = helper.getKeyObject(),
        callback = sinon.stub(),
        timeout = 5000,
        request_json = {
          "pageLoad": timeout
        },
        options = {
          hostname: keyObject.rproxyHost,
          port: keyObject.port,
          path: "/wd/hub/session/"+keyObject.key+"/timeouts",
          method: "POST",
          body: JSON.stringify(request_json),
          timeout: constants.BS_TIMEOUT,
          headers: requestlib.appendBStackHostHeader(keyObject.name),
          mockPerformanceJarEndpoint: true,
        },
        data = '{"value":"data"}',
        res = {
          data: data
        },
        catch_stub = sinon.stub().callsArgWith(0, 'err'),
        second_then_stub = sinon.stub().callsArg(0),
        then_stub = sinon.stub().callsArgWith(0, res).returns({
          catch: catch_stub.returns({
            then: second_then_stub
          })
        });
      keyObject.mockPerformanceJarEndpoint = true;
      sinon.stub(requestlib, 'call').returns({
        then: then_stub
      });
      sinon.stub(HubLogger, 'miscLogger');
      sinon.stub(HubLogger, 'exceptionLogger');

      constants['global_registry'][keyObject.rails_session_id] = {
        secondary_state: constants.secondary_states.SUCCESS
      };

      supporting.setPageLoadTimeoutOnTerminal(keyObject, callback, timeout);

      HubLogger.miscLogger.called.should.be.true;
      HubLogger.miscLogger.calledWith("setPageLoadTimeoutOnTerminal", "sessionId: " + keyObject.rails_session_id + " status: in function", LL.DEBUG).should.be.true;
      HubLogger.miscLogger.calledWith("setPageLoadTimeoutOnTerminal", "sessionId: " + keyObject.rails_session_id + " status: success, data: " + data, LL.DEBUG).should.be.true;
      HubLogger.exceptionLogger.called.should.be.true;
      HubLogger.exceptionLogger.calledWith("setPageLoadTimeoutOnTerminal", "sessionId: " + keyObject.rails_session_id + " status: error, error err").should.be.true;
      requestlib.call.called.should.be.true;
      requestlib.call.calledWith(options).should.be.true;
      constants.global_registry[keyObject.rails_session_id].isMetaPageLoadTimeout.should.be.true;
      second_then_stub.called.should.be.true;
      second_then_stub.calledWith(callback).should.be.true;
      callback.called.should.be.true;

      requestlib.call.restore();
      HubLogger.miscLogger.restore();
      HubLogger.exceptionLogger.restore();
      delete constants.global_registry[keyObject.rails_session_id];
    });
  });
});
