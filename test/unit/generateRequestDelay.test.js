'use strict';

/* eslint-disable no-underscore-dangle */

const { assert, expect } = require('chai');
const rewire = require('rewire');
const constants = require('../../constants');

const rewiredBrowserStack = rewire('../../browserstack');

describe('generateRequestDelay', () => {
  let generateRequestDelay;

  beforeEach(() => {
    generateRequestDelay = rewiredBrowserStack.__get__('generateRequestDelay');
  });

  it('should select DEFAULT_RETRY as default', () => {
    const retryMethod = 'random';
    const len = constants.DEFAULT_RETRY_DELAY.length;
    for(let attempt = 0; attempt < len; attempt++) {
      const result = generateRequestDelay(retryMethod, attempt);
      expect(result).to.equal(constants.DEFAULT_RETRY_DELAY[attempt]);
    }
  });
});
