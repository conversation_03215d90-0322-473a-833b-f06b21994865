const debug = require('../../debug');
const net = require('net');
const assert = require('assert');
const sinon = require('sinon');

describe('isPort Open', () => {
  const resCallback = () => { };
  it('checks isPortOpen', (done) => {
    const socket = new net.Socket({});
    const x = sinon.stub(net, 'connect').returns(socket);
    debug.isPortOpen('test', 4022, resCallback);
    assert(x.calledOnce === true);
    x.restore();
    done();
  });
});
