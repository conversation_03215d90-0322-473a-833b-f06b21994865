'use strict';

const rewire = require('rewire');
const pubSub = require('../../../../../pubSub');
const baseHandlerHelper = require('../../../../../controllers/seleniumCommand/helpers/baseHandlerHelper');

const baseHandlerHelperRewired = rewire('../../../../../controllers/seleniumCommand/helpers/baseHandlerHelper');
const testHelper = require('../../../helper');
const constants = require('../../../../../constants');

const sinon = require('sinon');
const { expect, assert } = require('chai');

describe('baseHandlerHelper tests', () => {
  describe('updateKeyObjectForUdpKeys', () => {
    let publish;

    beforeEach(() => {
      publish = sinon.stub(pubSub, 'publish');
    });

    afterEach(() => {
      publish.restore();
    });

    it('should return without calling pubSub.publish if sessionKeyObj.udpKeys is falsy', () => {
      const sessionKeyObj = testHelper.getKeyObject();
      sessionKeyObj.udpKeys = false;
      baseHandlerHelper.updateKeyObjectForUdpKeys(sessionKeyObj, {}, { status: 13, value: 'operation timed out' }, 2);
      sinon.assert.notCalled(publish);
    });

    it('should check for non-zero status if keyObject.udpKeys.non_zero_status is not set and publish', () => {
      const sessionKeyObj = testHelper.getKeyObject();
      sessionKeyObj.udpKeys = {};
      baseHandlerHelper.updateKeyObjectForUdpKeys(sessionKeyObj, { status: 13, value: 'operation timed out' }, 'hash1', 2);
      sinon.assert.calledOnce(publish);
      expect(sessionKeyObj.udpKeys.non_zero_status).to.eql('operation_timedout');
      sinon.assert.calledWith(publish, constants.updateKeyObject, sinon.match({
        session: sessionKeyObj.rails_session_id,
        changed: sinon.match({
          udpKeys: sessionKeyObj.udpKeys,
        }),
      }));
      expect(sessionKeyObj.udpKeys.implicit_timeout).to.eql(undefined);
    });

    it('should keyObject.udpKeys.implicit_timeout if hash is POST:timeouts and publish', () => {
      const sessionKeyObj = testHelper.getKeyObject();
      sessionKeyObj.udpKeys = {};
      baseHandlerHelper.updateKeyObjectForUdpKeys(sessionKeyObj, { status: 13, value: 'operation timed out' }, 'POST:timeouts', 2);
      sinon.assert.calledOnce(publish);
      expect(sessionKeyObj.udpKeys.non_zero_status).to.eql('operation_timedout');
      sinon.assert.calledWith(publish, constants.updateKeyObject, sinon.match({
        session: sessionKeyObj.rails_session_id,
        changed: sinon.match({
          udpKeys: sessionKeyObj.udpKeys,
        }),
      }));
      expect(sessionKeyObj.udpKeys.implicit_timeout).to.eql(1);
    });
  });

  describe('checkForNonZeroStatus', () => {
    const checkForNonZeroStatus = baseHandlerHelperRewired.__get__('checkForNonZeroStatus');

    it('should return obj with needToUpdateKeyObject false if parsedData.value is falsy', () => {
      expect(checkForNonZeroStatus({ value: false }, 2).needToUpdateKeyObject).to.eql(false);
    });

    it('should return obj with needToUpdateKeyObject true and status command_timeout only if timeout conditions match', () => {
      assert.deepEqual(checkForNonZeroStatus({ status: 21, value: 'Command duration or timeout' }, 2), {
        needToUpdateKeyObject: true,
        nonZeroStatus: 'command_timeout',
      });

      assert.deepEqual(checkForNonZeroStatus({ status: 567, value: 'Command duration or timeout' }, 2), {
        needToUpdateKeyObject: false,
        nonZeroStatus: undefined,
      });

      assert.deepEqual(checkForNonZeroStatus({ status: 28, value: 'random' }, 2), {
        needToUpdateKeyObject: false,
        nonZeroStatus: undefined,
      });
    });

    it('should return obj with needToUpdateKeyObject true and status server_error only if server_error conditions match', () => {
      assert.deepEqual(checkForNonZeroStatus({ status: 13, value: 'An unknown server-side error occurred' }, 2), {
        needToUpdateKeyObject: true,
        nonZeroStatus: 'server_error',
      });

      assert.deepEqual(checkForNonZeroStatus({ status: 5464, value: 'An unknown server-side error occurred' }, 2), {
        needToUpdateKeyObject: false,
        nonZeroStatus: undefined,
      });

      assert.deepEqual(checkForNonZeroStatus({ status: 13, value: 'random123' }, 2), {
        needToUpdateKeyObject: false,
        nonZeroStatus: undefined,
      });
    });

    it('should return obj with needToUpdateKeyObject true and status operation_timedout only if server_error conditions match', () => {
      assert.deepEqual(checkForNonZeroStatus({ status: 13, value: 'operation timed out' }, 2), {
        needToUpdateKeyObject: true,
        nonZeroStatus: 'operation_timedout',
      });

      assert.deepEqual(checkForNonZeroStatus({ status: 5464, value: 'operation timed out' }, 2), {
        needToUpdateKeyObject: false,
        nonZeroStatus: undefined,
      });

      assert.deepEqual(checkForNonZeroStatus({ status: 13, value: 'random123' }, 2), {
        needToUpdateKeyObject: false,
        nonZeroStatus: undefined,
      });
    });
  });
});
