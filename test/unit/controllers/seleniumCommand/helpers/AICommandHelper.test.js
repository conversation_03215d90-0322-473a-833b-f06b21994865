'use strict';

const AICommandHelper = require('../../../../../controllers/seleniumCommand/helpers/AICommandHelper');
const helper = require('../../../../../helper');
const constants = require('../../../../../constants');
const requestlib = require('../../../../../lib/request');
const sinon = require('sinon');
const { expect, assert } = require('chai');

describe('AICommandHelper tests', () => {
  describe('#getRedisKey', () => {
    it('should construct redis key', () => {
      const rails_session_id = 'rails_session_id';
      const expected = `${AICommandHelper.AI_REDIS_KEY}${rails_session_id}`;
      const result = AICommandHelper.getRedisKey(rails_session_id);
      expect(result).to.equal(expected);
    });
  });

  describe('#constructCommand', () => {
    it('should construct command for provided method and URL', () => {
      const sandbox = sinon.sandbox.create();
      const now = new Date();
      const clock = sinon.useFakeTimers(now.getTime());
      const method = 'POST';
      const reqUrl = 'some_url';
      const expected = '{"k":"POST:some_url","t":"0"},';
      const result = AICommandHelper.constructCommand(method, reqUrl, now.getTime());
      expect(result).to.equal(expected);
      sandbox.restore();
      clock.restore();
    });
  });

  describe('#getRedisClient', () => {
    it('should return redis client Ai', () => {
      const redisClient = AICommandHelper.getRedisClient();
      expect(redisClient).to.equal(helper.redisClientAi);
    });
  });

  describe('makeRequestTcg', () => {
    const method = 'POST';
    const path = '/api/resource';
    const body = { key: 'value' };
    const headers = { 'Content-Type': 'application/json' };

    let ogConstants;
    beforeEach(() => {
      ogConstants = constants.TCG_SERVICE;
    });

    afterEach(() => {
      constants.TCG_SERVICE = ogConstants;
    });

    it('should make a request to the TCG service', async () => {
      const hostname = 'example.com';
      constants.TCG_SERVICE = {
        scheme: 'https',
        username: 'username',
        password: 'password',
      };
      const requestlibCallStub = sinon.stub(requestlib, 'call').returns('response');
      const result = await AICommandHelper.makeRequestTcg(method, path, body, headers, hostname);

      sinon.assert.calledOnce(requestlibCallStub);
      sinon.assert.calledWith(
        requestlibCallStub,
        {
          method: 'POST',
          path: '/api/resource',
          body: { key: 'value' },
          headers: { 'Content-Type': 'application/json' },
          hostname: 'example.com',
          scheme: 'https',
          auth: 'username:password',
          port: '',
        }
      );

      expect(result).to.equal('response');
      requestlibCallStub.restore();
    });
  });

  describe('#getFirecmdTcgOptions', () => {
    let ogConstants;
    beforeEach(() => {
      ogConstants = constants.TCG_SERVICE;
    });

    afterEach(() => {
      constants.TCG_SERVICE = ogConstants;
    });

    it('should modify the get options with the ai_enabled_session parameter', () => {
      const options = {
        browserstackParams: {
          'browserstack.ai_enabled_session': 'true',
        },
      };
      constants.TCG_SERVICE = {
        scheme: 'https',
        regions: {
          'us-east-1': {
            endpoint: 'tcg.bsstag.com',
            elb: 'tcg.bsstag.com',
            s3_upload_endpoint: 'browserstack-testcasegeneration-stag-euc1.s3.eu-central-1.amazonaws.com',
          },
          'us-west-2': {
            endpoint: 'tcg-usw.bsstag.com',
            elb: 'tcg.bsstag.com',
            s3_upload_endpoint: 'browserstack-testcasegeneration-stag-euc1.s3.eu-central-1.amazonaws.com',
          },
        },
      };
      const expected = {
        enabled: true,
        tcg_scheme: constants.TCG_SERVICE.scheme,
        tcg_hostname: AICommandHelper.getTcgEndpoint() || AICommandHelper.DEFAULT_TCG_ENDPOINT,
        tcg_s3_upload_hostname: AICommandHelper.getTcgS3UploadEndpoint() || AICommandHelper.DEFAULT_TCG_S3_ENDPOINT,
        eds_hostname: AICommandHelper.DEFAULT_EDS_ENDPOINT,
      };
      const result = AICommandHelper.getFirecmdTcgOptions(options);
      assert.deepEqual(JSON.parse(result), expected);
    });

    it('shouldset ai_enabled_session false if the ai_enabled_session parameter is not present', () => {
      const options = {
        browserstackParams: {},
      };
      const result = AICommandHelper.getFirecmdTcgOptions(options);
      expect(result).to.equal('{ "enabled": "false" }');
    });

    it('should set ai_enabled_session false if the ai_enabled_session parameter is not set to true', () => {
      const options = {
        browserstackParams: {
          'browserstack.ai_enabled_session': 'false',
        },
      };
      const result = AICommandHelper.getFirecmdTcgOptions(options);
      expect(result).to.equal('{ "enabled": "false" }');
    });
  });

  describe('#getFirecmdAIProxyOptions', () => {
    it('should set enabled true if the ai_enabled_session parameter is set to true', () => {
      const options = {
        realMobile: true,
        browserstackParams: {
          'browserstack.ai_enabled_session': 'true',
          'browserstack.ai_details': '{"enable_ai_healing": "true"}',
        },
      };
      const result = AICommandHelper.getFirecmdAIProxyOptions(options);
      expect(result).to.contain('"enabled":true');
    });

    it('should set enabled true for appTesting sessions if the ai_enabled_session parameter is set to true', () => {
      const options = {
        realMobile: true,
        bsCaps: {
          app: 'bs://abcd',
          bundleId: 'com.android.browserstack',
          orig_os: 'android',
        },
        browserstackParams: {
          'browserstack.ai_enabled_session': 'true',
          'browserstack.ai_details': '{"enable_ai_healing": "true"}',
        },
      };
      const result = AICommandHelper.getFirecmdAIProxyOptions(options);
      expect(result).to.contain('"enabled":true');
    });

    it('should set enabled true for appTesting sessions if the testbed collection parameter is set to true', () => {
      const options = {
        realMobile: true,
        bsCaps: {
          app: 'bs://abcd',
          bundleId: 'com.android.browserstack',
          orig_os: 'android',
        },
        browserstackParams: {
          'browserstack.ai_enabled_session': 'true',
          'browserstack.ai_details': '{"is_test_bed_data_collection_enabled": "true"}',
        },
      };
      const result = AICommandHelper.getFirecmdAIProxyOptions(options);
      expect(result).to.contain('"enabled":true');
    });

    it('should set enabled false if the ai_enabled_session parameter is not present', () => {
      const options = {
        browserstackParams: {},
      };
      const result = AICommandHelper.getFirecmdAIProxyOptions(options);
      expect(result).to.equal('{ "enabled": "false" }');
    });

    it('should set enabled false if the ai_enabled_session parameter is not set to true', () => {
      const options = {
        browserstackParams: {
          'browserstack.ai_enabled_session': 'false',
        },
      };
      const result = AICommandHelper.getFirecmdAIProxyOptions(options);
      expect(result).to.equal('{ "enabled": "false" }');
    });
  });

  describe('getTcgEndpoint', () => {
    let ogRegion;
    beforeEach(() => {
      ogRegion = constants.region;
    });
    afterEach(() => {
      constants.region = ogRegion;
    });
    it('returns the correct TCG endpoint based on the region', () => {
      constants.region = 'us-east-1';
      constants.TCG_SERVICE = {
        regions: {
          'us-east-1': {
            endpoint: 'tcg-use.browserstack.com',
          },
          'eu-west-1': {
            endpoint: 'tcg-euw.browserstack.com',
          },
        },
      };
      const endpoint = AICommandHelper.getTcgEndpoint();
      expect(endpoint).to.equal('tcg-use.browserstack.com');
    });

    it('returns the correct TCG endpoint based on the region (EU)', () => {
      constants.region = 'eu-west-1';
      constants.TCG_SERVICE = {
        regions: {
          'us-east-1': {
            endpoint: 'tcg-use.browserstack.com',
          },
          'eu-west-1': {
            endpoint: 'tcg-euw.browserstack.com',
          },
        },
      };
      const endpoint = AICommandHelper.getTcgEndpoint();
      expect(endpoint).to.equal('tcg-euw.browserstack.com');
    });
  });

  describe('#getSanitisedString', () => {
    it('should return an empty string when input is undefined', () => {
      expect(AICommandHelper.getSanitisedString(undefined)).to.equal('');
    });

    it('should return an empty string when input is not a string', () => {
      expect(AICommandHelper.getSanitisedString(123)).to.equal('');
      expect(AICommandHelper.getSanitisedString(true)).to.equal('');
      expect(AICommandHelper.getSanitisedString({ key: 'value' })).to.equal('');
    });

    it('should sanitize single quotes', () => {
      expect(AICommandHelper.getSanitisedString('\'aswift_1\'')).to.equal('\\\'aswift_1\\\'');
    });
  });

  describe('#evaluateKeyObjectDiff', () => {
    let keyObject;
    let keyObjectDiffHash;

    beforeEach(() => {
      keyObject = {
        selfHealingSuccess: null,
        softHealingSuccess: null,
      };
      keyObjectDiffHash = {};
    });

    it('should set selfHealingSuccess to "true" when selfHealingSuccess equals PUBLISH_AI_SUCCESS', () => {
      keyObject.selfHealingSuccess = AICommandHelper.PUBLISH_AI_SUCCESS;

      AICommandHelper.evaluateKeyObjectDiff(keyObject, keyObjectDiffHash);

      expect(keyObject.selfHealingSuccess).to.equal('true');
      expect(keyObjectDiffHash.selfHealingSuccess).to.equal('true');
    });

    it('should set softHealingSuccess to "true" when softHealingSuccess equals PUBLISH_AI_SUCCESS', () => {
      keyObject.softHealingSuccess = AICommandHelper.PUBLISH_AI_SUCCESS;

      AICommandHelper.evaluateKeyObjectDiff(keyObject, keyObjectDiffHash);

      expect(keyObject.softHealingSuccess).to.equal('true');
      expect(keyObjectDiffHash.softHealingSuccess).to.equal('true');
    });

    it('should not modify keyObject or keyObjectDiffHash if neither selfHealingSuccess nor softHealingSuccess is PUBLISH_AI_SUCCESS', () => {
      keyObject.selfHealingSuccess = 'someOtherValue';
      keyObject.softHealingSuccess = 'someOtherValue';

      AICommandHelper.evaluateKeyObjectDiff(keyObject, keyObjectDiffHash);

      expect(keyObject.selfHealingSuccess).to.equal('someOtherValue');
      expect(keyObject.softHealingSuccess).to.equal('someOtherValue');
      expect(keyObjectDiffHash).to.not.have.property('selfHealingSuccess');
      expect(keyObjectDiffHash).to.not.have.property('softHealingSuccess');
    });
  });

  describe('updateAIHealingDetails', () => {
    let sessionKeyObj;

    beforeEach(() => {
      sessionKeyObj = {
        ai_healing_details: {
          total_healing_enabled_request: 0,
          total_healing_request: 0,
          healing_success_count: 0,
          healing_failure_count: 0,
          pre_check_failure_count: 0,
          script_exec_error_count: 0,
        },
      };
    });

    it('should increment the specified key by 1 when the key exists in ai_healing_details', () => {
      AICommandHelper.updateAIHealingDetails(sessionKeyObj, 'total_healing_request');
      expect(sessionKeyObj.ai_healing_details.total_healing_request).to.equal(1);

      AICommandHelper.updateAIHealingDetails(sessionKeyObj, 'pre_check_failure_count');
      expect(sessionKeyObj.ai_healing_details.pre_check_failure_count).to.equal(1);

      AICommandHelper.updateAIHealingDetails(sessionKeyObj, 'total_healing_request');
      expect(sessionKeyObj.ai_healing_details.total_healing_request).to.equal(2);

      expect(sessionKeyObj.ai_healing_details.healing_success_count).to.equal(0);
    });

    it('should not modify ai_healing_details if the key does not exist', () => {
      AICommandHelper.updateAIHealingDetails(sessionKeyObj, 'non_existent_key');
      expect(sessionKeyObj.ai_healing_details).to.deep.equal({
        total_healing_enabled_request: 0,
        total_healing_request: 0,
        healing_success_count: 0,
        healing_failure_count: 0,
        pre_check_failure_count: 0,
        script_exec_error_count: 0,
      });
    });

    it('should not throw an error if sessionKeyObj is null or undefined', () => {
      expect(() => AICommandHelper.updateAIHealingDetails(null, 'total_healing_request')).to.not.throw();
      expect(() => AICommandHelper.updateAIHealingDetails(undefined, 'total_healing_request')).to.not.throw();
    });

    it('should not throw an error if ai_healing_details is null or undefined', () => {
      sessionKeyObj.ai_healing_details = null;
      expect(() => AICommandHelper.updateAIHealingDetails(sessionKeyObj, 'total_healing_request')).to.not.throw();
    });

    it('should handle multiple updates to the same key', () => {
      AICommandHelper.updateAIHealingDetails(sessionKeyObj, 'healing_success_count');
      AICommandHelper.updateAIHealingDetails(sessionKeyObj, 'healing_success_count');
      expect(sessionKeyObj.ai_healing_details.healing_success_count).to.equal(2);
    });

    it('should not modify other keys when updating a specific key', () => {
      AICommandHelper.updateAIHealingDetails(sessionKeyObj, 'pre_check_failure_count');
      expect(sessionKeyObj.ai_healing_details).to.deep.equal({
        total_healing_enabled_request: 0,
        total_healing_request: 0,
        healing_success_count: 0,
        healing_failure_count: 0,
        pre_check_failure_count: 1,
        script_exec_error_count: 0,
      });
    });
  });
});
