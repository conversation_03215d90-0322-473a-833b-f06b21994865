'use strict';

const SeleniumClient = require('../../../../../seleniumClient');
const requestlib = require('../../../../../lib/request');
const bridge = require('../../../../../bridge');
const sinon = require('sinon');
const { assert } = require('chai');
const { W3CSendKeysHandler } = require('../../../../../controllers/seleniumCommand/handlers/W3CSendKeysHandler');

describe('W3CSendKeysHandler tests', () => {
  let sendKeysHandler;
  let sessionKeyObj;
  let request;
  let response;
  let requestStateObj;
  let reqData;

  beforeEach(() => {
    sessionKeyObj = {};
    requestStateObj = { data: ['test', 'data'] };
    reqData = '{}';
    sinon.stub(bridge, 'sendResponse');
    sendKeysHandler = new W3CSendKeysHandler(sessionKeyObj, request, response);
  });

  afterEach(() => {
    bridge.sendResponse.restore();
  });

  describe('.processCommand', () => {
    it('should send status 0 in case no error', async () => {
      sinon.stub(sendKeysHandler.selenium, 'getActiveElement');
      sinon.stub(sendKeysHandler.selenium, 'elementValue');
      await sendKeysHandler.processCommand(requestStateObj, reqData);
      sinon.assert.calledWith(bridge.sendResponse, sessionKeyObj, { data: '{"status":0,"value":null}' });
    });

    it('should send status 13 in case of error', async () => {
      sinon.stub(sendKeysHandler.selenium, 'getActiveElement').throws('error');
      await sendKeysHandler.processCommand(requestStateObj, reqData);
      sinon.assert.calledWith(bridge.sendResponse, sessionKeyObj, { data: '{"status":13,"value":null}' });
    });
  });
});
