'use strict';

const { ExecuteScriptHandler } = require('../../../../../controllers/seleniumCommand/handlers/ExecuteScriptHandler');
const jsExecutor = require('../../../../../lib/customSeleniumHandler/jsExecutor');
const testHelper = require('../../../helper');
const sinon = require('sinon');
const { assert } = require('chai');

describe('ExecuteScriptHandler tests', () => {
  const sessionKeyObj = testHelper.getKeyObject();
  const host = 'http://some_host';
  const key = 'abc123';
  const queryParams = `?sessionId=${key}`;
  const request = {
    url: host + queryParams,
  };
  const response = { data: 'some_data' };

  describe('processCommand', () => {
    const requestStateObj = { key: 'value1', hash: 'POST:basic-auth-edge' };
    let checkandExecuteIfBstackExecutor;

    beforeEach(() => {
      checkandExecuteIfBstackExecutor = sinon.stub(jsExecutor, 'checkandExecuteIfBstackExecutor').returns(true);
    });

    afterEach(() => {
      checkandExecuteIfBstackExecutor.restore();
    });

    it('should call jsExecutor and return Promise that will resolve to object containing return value', async () => {
      const handler = new ExecuteScriptHandler(sessionKeyObj, request, response);
      const res = await handler.processCommand(requestStateObj);

      sinon.assert.calledOnce(checkandExecuteIfBstackExecutor);
      sinon.assert.calledWith(checkandExecuteIfBstackExecutor, sessionKeyObj, requestStateObj);
      assert.deepEqual(res, { returns: true });
    });
  });
});
