'use strict';

const { AndroidMultiApksHandler } = require('../../../../../controllers/seleniumCommand/handlers/AndroidMultiApksHandler');
const bridge = require('../../../../../bridge');
const constants = require('../../../../../constants');
const sinon = require('sinon');
const { assert } = require('chai');

describe('AndroidMultiApksHandler tests', () => {
  describe('processCommand', () => {
    it('should return error if data not in right format', () => {
      const sendResponse = sinon.stub(bridge, 'sendResponse', (sessionKeyObj, requestStateObj) => {
        assert.deepEqual(requestStateObj.data, JSON.stringify({ sessionId: 'random', status: 13, value: { message: 'Invalid format: Request data should be in JSON.' } }));
      });
      const androidPushFile = new AndroidMultiApksHandler({ rails_session_id: 'random' }, {}, {});
      const res = androidPushFile.processCommand({}, 'random');
      assert.equal(res.returns, true);
      sinon.assert.calledOnce(sendResponse);
      sendResponse.restore();
    });

    it('should return error if mobile:installMultipleApks', () => {
      const sendResponse = sinon.stub(bridge, 'sendResponse', (sessionKeyObj, requestStateObj) => {
        assert.deepEqual(requestStateObj.data, JSON.stringify({ sessionId: 'random', status: 13, value: { message: constants.COMMAND_ERROR_MESSAGES.multipleApksNotSupported } }));
      });
      const androidPushFile = new AndroidMultiApksHandler({ rails_session_id: 'random', deviceName: 'Samsung Galaxy S10-9.0' }, { is_app_automate_session: true }, {});
      const res = androidPushFile.processCommand({}, JSON.stringify({ script: 'mobile:installMultipleApks', args: ['{}kabsha', 'adkfbad'] }));
      assert.equal(res.returns, true);
      sinon.assert.calledOnce(sendResponse);
      sendResponse.restore();
    });

    it('should let command run if data is as expected', () => {
      const sendResponse = sinon.stub(bridge, 'sendResponse');
      const androidPushFile = new AndroidMultiApksHandler({ rails_session_id: 'random' }, {}, {});
      const res = androidPushFile.processCommand({}, JSON.stringify({ script: 'random', args: ['value1'] }));
      assert.equal(res.returns, false);
      sinon.assert.notCalled(sendResponse);
      sendResponse.restore();
    });
  });
});
