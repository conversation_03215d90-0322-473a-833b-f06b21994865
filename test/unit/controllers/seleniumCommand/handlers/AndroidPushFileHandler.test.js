'use strict';

const { AndroidPushFileHandler } = require('../../../../../controllers/seleniumCommand/handlers/AndroidPushFileHandler');
const bridge = require('../../../../../bridge');
const errorMessages = require('../../../../../errorMessages');
const sinon = require('sinon');
const { assert } = require('chai');

describe('AndroidPushFileHandler tests', () => {
  describe('processCommand', () => {
    it('should return error if data not in right format', () => {
      const sendResponse = sinon.stub(bridge, 'sendResponse', (sessionKeyObj, requestStateObj) => {
        assert.deepEqual(requestStateObj.data, JSON.stringify({ sessionId: 'random', status: 13, value: { message: 'Invalid format of the request data, require JSON. Please check the request data.' } }));
      });
      const androidPushFile = new AndroidPushFileHandler({ rails_session_id: 'random' }, {}, {});
      const res = androidPushFile.processCommand({}, 'random');
      assert.equal(res.returns, true);
      sinon.assert.calledOnce(sendResponse);
      sendResponse.restore();
    });

    it('should return error if path not in expected path', () => {
      const sendResponse = sinon.stub(bridge, 'sendResponse', (sessionKeyObj, requestStateObj) => {
        const browserstackInvalidFilePathError = errorMessages.COMMON_PUSH_FILE_ERROR + errorMessages.ANDROID_11_BELOW_PUSH_FILE_ERROR + errorMessages.APP_AUTOMATE_PUSH_FILE_LINK;
        assert.deepEqual(requestStateObj.data, JSON.stringify({ sessionId: 'random', status: 13, value: { message: browserstackInvalidFilePathError } }));
      });
      const androidPushFile = new AndroidPushFileHandler({ rails_session_id: 'random', deviceName: 'Samsung Galaxy S10-9.0' }, { is_app_automate_session: true }, {});
      const res = androidPushFile.processCommand({}, JSON.stringify({ path: '/some/path' }));
      assert.equal(res.returns, true);
      sinon.assert.calledOnce(sendResponse);
      sendResponse.restore();
    });

    it('should return error if path not in expected path for mobile: pushFile', () => {
      const sendResponse = sinon.stub(bridge, 'sendResponse', (sessionKeyObj, requestStateObj) => {
        const browserstackInvalidFilePathError = errorMessages.COMMON_PUSH_FILE_ERROR + errorMessages.ANDROID_11_BELOW_PUSH_FILE_ERROR + errorMessages.APP_AUTOMATE_PUSH_FILE_LINK;
        assert.deepEqual(requestStateObj.data, JSON.stringify({ sessionId: 'random', status: 13, value: { message: browserstackInvalidFilePathError } }));
      });
      const androidPushFile = new AndroidPushFileHandler({ rails_session_id: 'random', deviceName: 'Samsung Galaxy S10-9.0' }, { is_app_automate_session: true }, {});
      const res = androidPushFile.processCommand({}, JSON.stringify({ args: [{ remotePath: '/some/path' }] }));
      assert.equal(res.returns, true);
      sinon.assert.calledOnce(sendResponse);
      sendResponse.restore();
    });

    it('should let command run if data is as expected', () => {
      const sendResponse = sinon.stub(bridge, 'sendResponse');
      const androidPushFile = new AndroidPushFileHandler({ rails_session_id: 'random' }, {}, {});
      const res = androidPushFile.processCommand({}, JSON.stringify({ path: '/sdcard/Download/random' }));
      assert.equal(res.returns, false);
      sinon.assert.notCalled(sendResponse);
      sendResponse.restore();
    });

    it('should let command run if data is as expected for mobile: pushFile', () => {
      const sendResponse = sinon.stub(bridge, 'sendResponse');
      const androidPushFile = new AndroidPushFileHandler({ rails_session_id: 'random' }, {}, {});
      const res = androidPushFile.processCommand({}, JSON.stringify({ args: [{ remotePath: '/sdcard/Download/random' }] }));
      assert.equal(res.returns, false);
      sinon.assert.notCalled(sendResponse);
      sendResponse.restore();
    });
  });
});
