'use strict';

const { TestCafeDummyResponseHandler } = require('../../../../../controllers/seleniumCommand/handlers/TestCafeDummyResponseHandler');
const bridge = require('../../../../../bridge');
const sinon = require('sinon');
const { assert } = require('chai');

describe('TestCafeDummyResponseHandler tests', () => {
  describe('processCommand', () => {
    it('should return dummy response', () => {
      const sendResponse = sinon.stub(bridge, 'sendResponse', (sessionKeyObj, requestStateObj) => {
        assert.deepEqual(requestStateObj.data, JSON.stringify({ sessionId: 'random', status: 0, value: 'https://dummy-url.browserstack.com' }));
      });
      const dummyResponse = new TestCafeDummyResponseHandler({ rails_session_id: 'random' }, {}, {});
      dummyResponse.processCommand({});
      sinon.assert.calledOnce(sendResponse);
      sendResponse.restore();
    });
  });
});
