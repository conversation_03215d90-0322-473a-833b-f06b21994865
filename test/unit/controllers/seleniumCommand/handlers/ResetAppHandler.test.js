'use strict';

const { ResetAppHandler } = require('../../../../../controllers/seleniumCommand/handlers/ResetAppHandler');
const SeleniumClient = require('../../../../../seleniumClient');
const requestlib = require('../../../../../lib/request');
const bridge = require('../../../../../bridge');
const sinon = require('sinon');
const { assert } = require('chai');

describe('ResetAppHandler tests', () => {
  describe('processCommand', () => {
    it('should send status 0 in case no error', async () => {
      const sendResponse = sinon.stub(bridge, 'sendResponse', (sessionKeyObj, requestStateObj) => {
        assert.deepEqual(requestStateObj.data, JSON.stringify({ sessionId: 'random', status: 0, value: null }));
      });
      const requestCall = sinon.stub(requestlib, 'call').returns(Promise.resolve({}));
      const resetApp = sinon.stub(SeleniumClient.prototype, 'resetApp').returns(Promise.resolve({}));
      const resetAppHandler = new ResetAppHandler({ rails_session_id: 'random' }, {}, {});
      await resetAppHandler.processCommand({});
      sinon.assert.calledOnce(requestCall);
      sinon.assert.calledOnce(sendResponse);
      sinon.assert.calledOnce(resetApp);
      requestCall.restore();
      sendResponse.restore();
      resetApp.restore();
    });

    it('should send status 13 in case of an error', async () => {
      const sendResponse = sinon.stub(bridge, 'sendResponse', (sessionKeyObj, requestStateObj) => {
        assert.deepEqual(requestStateObj.data, JSON.stringify({ sessionId: 'random', status: 13, value: null }));
      });
      const requestCall = sinon.stub(requestlib, 'call').returns(Promise.resolve({}));
      const launchApp = sinon.stub(SeleniumClient.prototype, 'resetApp').returns(Promise.reject(new Error('error')));
      const resetAppHandler = new ResetAppHandler({ rails_session_id: 'random' }, {}, {});
      await resetAppHandler.processCommand({});
      sinon.assert.calledOnce(requestCall);
      sinon.assert.calledOnce(sendResponse);
      sinon.assert.calledOnce(launchApp);
      requestCall.restore();
      sendResponse.restore();
      launchApp.restore();
    });
  });
});
