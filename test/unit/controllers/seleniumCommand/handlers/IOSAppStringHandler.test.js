'use strict';

const { IOSAppStringHandler } = require('../../../../../controllers/seleniumCommand/handlers/IOSAppStringHandler');
const testHelper = require('../../../helper');
const sinon = require('sinon');
const terminal = require('../../../../../services/terminal');
const constants = require('../../../../../constants');
const bridge = require('../../../../../bridge');
const { expect, assert } = require('chai');

describe('IOSAppStringHandler tests', () => {
  const sessionKeyObj = testHelper.getKeyObject();
  const host = 'http://some_host';
  const key = 'abc123';
  const queryParams = `?sessionId=${key}`;
  const request = {
    url: host + queryParams,
  };
  const hostname = 'some_host';
  sessionKeyObj.name = hostname;
  const response = { data: 'some_data' };

  describe('processCommand', () => {
    let sendResponse;
    let getIOSAppString;
    const handler = new IOSAppStringHandler(sessionKeyObj, request, response);

    beforeEach(() => {
      sendResponse = sinon.stub(bridge, 'sendResponse').returns('val1');
    });

    afterEach(() => {
      sendResponse.restore();
      if (getIOSAppString) {
        getIOSAppString.restore();
      }
    });

    it('should call bridge.sendResponse with status 0 in data when terminal call for get app string returns 200', async () => {
      const requestStateObj = { key1: 'val1' };
      getIOSAppString = sinon.stub(terminal, 'getIOSAppString').returns(Promise.resolve({ statusCode: 200, data: JSON.stringify({ k: 'success!' }) }));
      const res = await handler.processCommand(requestStateObj);
      expect(res.returns).to.eql(true);
      sinon.assert.calledOnce(getIOSAppString);
      sinon.assert.calledWith(getIOSAppString, request.url, sessionKeyObj, requestStateObj);
      expect(requestStateObj.data).to.eql(JSON.stringify({
        sessionId: sessionKeyObj.rails_session_id, status: 0, value: { k: 'success!' },
      }));
      sinon.assert.calledOnce(sendResponse);
      sinon.assert.calledWith(sendResponse, sessionKeyObj, requestStateObj);
      getIOSAppString.restore();
    });

    it('should call bridge.sendResponse with status 13 in data and statusCode 500 in output when terminal call for get app string returns non 200', async () => {
      const requestStateObj = { key1: 'val1' };
      getIOSAppString = sinon.stub(terminal, 'getIOSAppString').returns(Promise.resolve({ statusCode: 503, data: JSON.stringify({ k: 'notsuccess' }) }));
      const res = await handler.processCommand(requestStateObj);
      expect(res.returns).to.eql(true);
      sinon.assert.calledOnce(getIOSAppString);
      sinon.assert.calledWith(getIOSAppString, request.url, sessionKeyObj, requestStateObj);
      expect(requestStateObj.data).to.eql(JSON.stringify({
        sessionId: sessionKeyObj.rails_session_id, status: 13, value: { message: JSON.stringify({ k: 'notsuccess' }) },
      }));
      assert.deepEqual(requestStateObj.output, { headers: constants.CHUNKED_HEADER, statusCode: 500 });
      sinon.assert.calledOnce(sendResponse);
      sinon.assert.calledWith(sendResponse, sessionKeyObj, requestStateObj);
      getIOSAppString.restore();
    });

    it('should call bridge.sendResponse with status 13 in data and statusCode 500 in output when terminal call for get app string raises error', async () => {
      const requestStateObj = { key1: 'val1' };
      getIOSAppString = sinon.stub(terminal, 'getIOSAppString').returns(Promise.reject(new Error('some err1')));
      const res = await handler.processCommand(requestStateObj);
      expect(res.returns).to.eql(true);
      sinon.assert.calledOnce(getIOSAppString);
      sinon.assert.calledWith(getIOSAppString, request.url, sessionKeyObj, requestStateObj);
      expect(requestStateObj.data).to.eql(JSON.stringify({
        sessionId: sessionKeyObj.rails_session_id, status: 13, value: null,
      }));
      assert.deepEqual(requestStateObj.output, { headers: constants.CHUNKED_HEADER, statusCode: 500 });
      sinon.assert.calledOnce(sendResponse);
      sinon.assert.calledWith(sendResponse, sessionKeyObj, requestStateObj);
      getIOSAppString.restore();
    });
  });
});
