/* eslint-disable max-lines-per-function */

'use strict';

const { IOSScreenshotHandler } = require('../../../../../controllers/seleniumCommand/handlers/IOSScreenshotHandler');
const HubLogger = require('../../../../../log');
const testHelper = require('../../../helper');
const terminal = require('../../../../../services/terminal');
const sinon = require('sinon');
const { assert } = require('chai');
const hub = require('../../../../../hub');

describe('IOSScreenshotHandler tests', () => {
  const sessionKeyObj = testHelper.getKeyObject();
  const response = { data: 'some_data' };

  describe('processCommand', async () => {
    let realIOSScreenshot;
    let processResponse;
    let tempExceptionLogger;

    beforeEach(() => {
      processResponse = sinon.stub(hub, 'processResponse');
      tempExceptionLogger = sinon.stub(HubLogger, 'tempExceptionLogger');
    });

    afterEach(() => {
      if (realIOSScreenshot) {
        realIOSScreenshot.restore();
      }
      processResponse.restore();
      tempExceptionLogger.restore();
    });

    it('should call terminal.realIOSScreenshot, hub.processRespone with updated data and return object with returns key true', async () => {
      const requestStateObj = {
        data: 'somed1', output: 'op1', index_counter: 1, callbacks: {}, originalUrl: 'some url',
      };

      realIOSScreenshot = sinon.stub(terminal, 'realIOSScreenshot').returns(Promise.resolve({ data: 'some_data' }));
      const request = {};
      const handler = new IOSScreenshotHandler(sessionKeyObj, request, response);

      const res = await handler.processCommand(requestStateObj);
      assert.deepEqual(res, { returns: true });
      sinon.assert.calledOnce(realIOSScreenshot);
      sinon.assert.calledWith(realIOSScreenshot, sessionKeyObj);
      sinon.assert.calledOnce(processResponse);
      sinon.assert.calledWith(processResponse, request, response, sessionKeyObj, {
        data: 'some_data',
        output: 'op1',
        remoteSessionID: sessionKeyObj.key,
        clientSessionID: sessionKeyObj.rails_session_id,
        index_counter: 1,
        callbacks: {},
        hostname: sessionKeyObj.name,
        originalUrl: 'some url',
      });
      sinon.assert.notCalled(tempExceptionLogger);
    });

    it('handles exception in terminal call, calls hub.processResponse and returns object with returns key true', async () => {
      const requestStateObj = {
        data: 'somed1', output: 'op1', index_counter: 1, callbacks: {}, originalUrl: 'some url',
      };

      realIOSScreenshot = sinon.stub(terminal, 'realIOSScreenshot').returns(Promise.reject(new Error('some err')));
      const request = {};
      const handler = new IOSScreenshotHandler(sessionKeyObj, request, response);

      const res = await handler.processCommand(requestStateObj);
      assert.deepEqual(res, { returns: true });
      sinon.assert.calledOnce(realIOSScreenshot);
      sinon.assert.calledWith(realIOSScreenshot, sessionKeyObj);
      sinon.assert.calledOnce(tempExceptionLogger);
      sinon.assert.calledWith(tempExceptionLogger, 'Error getting custom screenshot for real IOS device', new Error('some err'), undefined, sessionKeyObj.name, request.url);
      sinon.assert.calledOnce(processResponse);
      sinon.assert.calledWith(processResponse, request, response, sessionKeyObj, {
        data: 'somed1',
        output: 'op1',
        remoteSessionID: sessionKeyObj.key,
        clientSessionID: sessionKeyObj.rails_session_id,
        index_counter: 1,
        callbacks: {},
        hostname: sessionKeyObj.name,
        originalUrl: 'some url',
      });
    });
  });
});
