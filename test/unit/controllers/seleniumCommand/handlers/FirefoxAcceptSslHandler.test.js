'use strict';

const { FirefoxAcceptSslHandler } = require('../../../../../controllers/seleniumCommand/handlers/FirefoxAcceptSslHandler');
const testHelper = require('../../../helper');
const sinon = require('sinon');
const terminal = require('../../../../../services/terminal');
const selenium = require('../../../../../services/selenium');
const constants = require('../../../../../constants');
const bridge = require('../../../../../bridge');
const { expect } = require('chai');

describe('FirefoxAcceptSslHandler tests', () => {
  const sessionKeyObj = testHelper.getKeyObject();
  const host = 'http://some_host';
  const key = 'abc123';
  const queryParams = `?sessionId=${key}`;
  const request = {
    url: host + queryParams,
  };
  const hostname = 'some_host';
  sessionKeyObj.name = hostname;
  const response = { data: 'some_data' };

  describe('getParamsForAcceptSsl', () => {
    it('returns empty string when sessionKeyObj.os includes mac', () => {
      const sessionKeyObj1 = testHelper.getKeyObject();
      sessionKeyObj1.os = 'mac hs';
      const handler = new FirefoxAcceptSslHandler(sessionKeyObj1, request, response);
      expect(handler.getParamsForAcceptSsl()).to.eql('');
    });

    it('returns numTabs=3 when os is not mac and browser version > 49', () => {
      const sessionKeyObj1 = testHelper.getKeyObject();
      sessionKeyObj1.os = 'win';
      sessionKeyObj1.browser_version = '50';
      const handler = new FirefoxAcceptSslHandler(sessionKeyObj1, request, response);
      expect(handler.getParamsForAcceptSsl()).to.eql('numTabs=3');
    });

    it('returns numTabs=2 when os is not mac and browser version <= 49', () => {
      const sessionKeyObj1 = testHelper.getKeyObject();
      sessionKeyObj1.os = 'win';
      sessionKeyObj1.browser_version = '49';
      const handler = new FirefoxAcceptSslHandler(sessionKeyObj1, request, response);
      expect(handler.getParamsForAcceptSsl()).to.eql('numTabs=2');
    });
  });

  describe('handleCommandResult', () => {
    let sendResponse;
    let getParamsForAcceptSsl;
    let sendErrorResponse;

    beforeEach(() => {
      getParamsForAcceptSsl = sinon.stub(FirefoxAcceptSslHandler.prototype, 'getParamsForAcceptSsl').returns('param1');
      sendResponse = sinon.stub(bridge, 'sendResponse').returns('val1');
      sendErrorResponse = sinon.stub(bridge, 'sendErrorResponse').returns('val2');
    });

    afterEach(() => {
      getParamsForAcceptSsl.restore();
      sendResponse.restore();
      sendErrorResponse.restore();
    });

    it('calls terminal accept ffssl, waits for page load and then calls sendResponse with updated hash and statuscode', async () => {
      const requestStateObj = { key: 'value1', hash: 'POST:firefox-acceptssl', output: {} };
      const acceptFirefoxSsl = sinon.stub(terminal, 'acceptFirefoxSsl').returns(Promise.resolve({ data: 'some_data' }));
      const waitForPageLoad = sinon.stub(selenium, 'waitForPageLoad').returns(Promise.resolve({}));
      const handler = new FirefoxAcceptSslHandler(sessionKeyObj, request, response);
      await handler.handleCommandResult(requestStateObj);

      sinon.assert.calledOnce(getParamsForAcceptSsl);
      sinon.assert.calledOnce(acceptFirefoxSsl);
      sinon.assert.calledWith(acceptFirefoxSsl, sessionKeyObj, 'param1');
      sinon.assert.calledOnce(waitForPageLoad);
      sinon.assert.calledWith(waitForPageLoad, sessionKeyObj, constants.acceptSslffReturnTimeout);
      expect(requestStateObj.hash).to.eql('POST:url');
      expect(requestStateObj.output.statusCode).to.eql(200);
      expect(requestStateObj.data).to.eql(JSON.stringify({ sessionId: sessionKeyObj.rails_session_id, status: 0, value: null }));
      sinon.assert.calledOnce(sendResponse);
      acceptFirefoxSsl.restore();
      waitForPageLoad.restore();
    });

    it('catches error during terminal call and calls bridge.sendErrorResponse with updated hash', async () => {
      const requestStateObj = { key: 'value1', hash: 'POST:firefox-acceptssl', output: {} };
      const acceptFirefoxSsl = sinon.stub(terminal, 'acceptFirefoxSsl').returns(Promise.reject(new Error('err1')));
      const waitForPageLoad = sinon.stub(selenium, 'waitForPageLoad').returns(Promise.resolve({}));
      const handler = new FirefoxAcceptSslHandler(sessionKeyObj, request, response);
      await handler.handleCommandResult(requestStateObj);

      sinon.assert.calledOnce(getParamsForAcceptSsl);
      sinon.assert.calledOnce(acceptFirefoxSsl);
      sinon.assert.calledWith(acceptFirefoxSsl, sessionKeyObj, 'param1');
      sinon.assert.notCalled(waitForPageLoad);
      expect(requestStateObj.hash).to.eql('POST:url');
      sinon.assert.calledOnce(sendErrorResponse);
      acceptFirefoxSsl.restore();
      waitForPageLoad.restore();
    });
  });
});
