'use strict';

const { BasicAuthEdgeHandler } = require('../../../../../controllers/seleniumCommand/handlers/BasicAuthEdgeHandler');
const urlModule = require('url');
const testHelper = require('../../../helper');
const sinon = require('sinon');
const terminal = require('../../../../../services/terminal');
const constants = require('../../../../../constants');
const bridge = require('../../../../../bridge');
const { assert } = require('chai');

describe('BasicAuthEdgeHandler tests', () => {
  const sessionKeyObject = testHelper.getKeyObject();
  const host = 'http://some_host';
  const key = 'abc123';
  const queryParams = `?sessionId=${key}`;
  const request = {
    url: host + queryParams,
  };
  const response = { data: 'some_data' };
  const userUrl = '***************************.com/path1?q=23';
  const options = { some_key: 'val1', headers: {} };
  const reqData = JSON.stringify({ url: userUrl, key1: 'val2' });

  describe('setEdgeAuthParams', () => {
    it('should set edge_url, edge_username and edge_password from userUrl if basic auth present in userUrl', async () => {
      const handler = new BasicAuthEdgeHandler(sessionKeyObject, request, response);
      handler.userUrl = urlModule.parse(userUrl);
      handler.setEdgeAuthParams();
      sinon.assert.match(handler.edge_url, 'https://some_domain.com/path1?q=23');
      sinon.assert.match(handler.edge_username, 'abc');
      sinon.assert.match(handler.edge_password, '123');
    });

    it('should set empty edge_url, edge_username and edge_password from userUrl if basic auth not present in userUrl', async () => {
      const handler = new BasicAuthEdgeHandler(sessionKeyObject, request, response);
      handler.userUrl = urlModule.parse('https://some_domain.com/path1?q=23');
      handler.setEdgeAuthParams();
      sinon.assert.match(handler.edge_url, 'https://some_domain.com/path1?q=23');
      sinon.assert.match(handler.edge_username, '');
      sinon.assert.match(handler.edge_password, '');
    });
  });

  describe('processCommand', () => {
    const requestStateObj = { key: 'value1', hash: 'POST:basic-auth-edge' };
    let sendResponse;

    beforeEach(() => {
      // createBridgeClientAndNode = sinon.stub(hub, 'createBridgeClientAndNode').returns('val');
      sendResponse = sinon.stub(bridge, 'sendResponse').returns('val1');
    });

    afterEach(() => {
      // cancelSafariPopup.restore();
      // createBridgeClientAndNode.restore();
      sendResponse.restore();
    });

    it('should set edge auth params, update options and call basicAuthEdge after timeout', async () => {
      const setEdgeAuthParams = sinon.stub(BasicAuthEdgeHandler.prototype, 'setEdgeAuthParams');
      const basicAuthEdgeCall = sinon.stub(BasicAuthEdgeHandler.prototype, 'basicAuthEdgeCall');
      const handler = new BasicAuthEdgeHandler(sessionKeyObject, request, response);
      const edgeUrl = 'https://some_domain.com/path1';
      const expectedReqData = JSON.stringify({ url: edgeUrl });

      handler.edge_url = edgeUrl;
      handler.edge_username = 'abc';
      handler.edge_password = '123';
      const res = handler.processCommand(reqData, requestStateObj, options);

      sinon.assert.notCalled(sendResponse);
      sinon.assert.calledOnce(setEdgeAuthParams);
      sinon.assert.match(options, {
        some_key: 'val1',
        body: JSON.stringify({ url: edgeUrl }),
        headers: { 'content-length': expectedReqData.length },
      });
      assert.notEqual(requestStateObj.edgeBasicAuthCheckTimeout, undefined);
      const clock = sinon.useFakeTimers();
      clock.tick(constants.EDGE_AUTH_TIMEOUT);
      sinon.assert.match(res, { options, reqData: expectedReqData });

      setEdgeAuthParams.restore();
      basicAuthEdgeCall.restore();
      clock.restore();
    });

    it('should call bridge.sendResponse and return if exception occurs in parsing userUrl', async () => {
      const invalidReqData = 1234;
      const handler = new BasicAuthEdgeHandler(sessionKeyObject, request, response);
      const res = handler.processCommand(invalidReqData, requestStateObj, options);
      sinon.assert.calledOnce(sendResponse);
      sinon.assert.calledWith(sendResponse, handler.sessionKeyObject, requestStateObj);
      sinon.assert.match(res, { returnData: 'val1' });
    });
  });

  describe('basicAuthEdgeCall', () => {
    const requestStateObj = { key: 'value1', hash: 'POST:basic-auth-edge' };
    let basicAuthEdge;
    let sendResponse;

    beforeEach(() => {
      sendResponse = sinon.stub(bridge, 'sendResponse').returns('val1');
    });

    afterEach(() => {
      // cancelSafariPopup.restore();
      basicAuthEdge.restore();
      sendResponse.restore();
    });
    it('should set edgeBasicAuthCheckTimeout to undefined, call basicAuthEdge on terminal and set hash to POST:url', async () => {
      basicAuthEdge = sinon.stub(terminal, 'basicAuthEdge').returns({ data: 'some_data' });
      const handler = new BasicAuthEdgeHandler(sessionKeyObject, request, response);
      const edgeUrl = 'https://some_domain.com/path1';
      handler.edge_url = edgeUrl;
      handler.edge_username = 'abc';
      handler.edge_password = '123';

      await handler.basicAuthEdgeCall(requestStateObj);
      sinon.assert.calledOnce(basicAuthEdge);
      sinon.assert.calledWith(basicAuthEdge, sessionKeyObject, handler.edge_username, handler.edge_password);
      sinon.assert.match(requestStateObj, { hash: 'POST:url' });
    });

    it('should call sendResponse if requestStateObject.returned is set', async () => {
      basicAuthEdge = sinon.stub(terminal, 'basicAuthEdge').returns({ data: 'some_data' });
      const handler = new BasicAuthEdgeHandler(sessionKeyObject, request, response);
      requestStateObj.returned = true;
      const edgeUrl = 'https://some_domain.com/path1';
      handler.edge_url = edgeUrl;
      handler.edge_username = 'abc';
      handler.edge_password = '123';

      await handler.basicAuthEdgeCall(requestStateObj);
      sinon.assert.calledOnce(basicAuthEdge);
      sinon.assert.calledOnce(sendResponse);
      sinon.assert.calledWith(sendResponse, sessionKeyObject, requestStateObj);
    });

    it('should catch error or promise rejection in basicAuthEdge call', async () => {
      basicAuthEdge = sinon.stub(terminal, 'basicAuthEdge').returns(Promise.reject(new Error('some_error')));
      const handler = new BasicAuthEdgeHandler(sessionKeyObject, request, response);
      requestStateObj.returned = true;
      const edgeUrl = 'https://some_domain.com/path1';
      handler.edge_url = edgeUrl;
      handler.edge_username = 'abc';
      handler.edge_password = '123';

      await handler.basicAuthEdgeCall(requestStateObj);
      sinon.assert.calledOnce(basicAuthEdge);
      sinon.assert.calledOnce(sendResponse);
      sinon.assert.calledWith(sendResponse, sessionKeyObject, requestStateObj);
      sinon.assert.match(requestStateObj, { hash: 'POST:url' });
    });
  });
});
