'use strict';

const AICommandHelper = require('../../../../../controllers/seleniumCommand/helpers/AICommandHelper');
const { TCG_ENDPOINTS, TCG_HEADERS } = require('../../../../../controllers/seleniumCommand/helpers/AICommandHelper');
const { AICommandHandler } = require('../../../../../controllers/seleniumCommand/handlers/AICommandHandler');
const constants = require('../../../../../constants');
const sinon = require('sinon');
const HubLogger = require('../../../../../log');
const { expect, assert } = require('chai');
const helper = require('../../../../../helper');

const LL = constants.LOG_LEVEL;
const TAG = 'AICommandHandler';

describe('AICommandHandler tests', () => {
  describe('#processResponse()', () => {
    const noModifyResponse = { modifyResponse: false };
    constants.execution_time_registry[1234] = {};

    it('should callSuccessEvent if output.statusCode is 200', async () => {
      const aiCommandHandler = new AICommandHandler({
        rails_session_id: 1234,
        dialect: 'W3C',
        aiSessionDetails: { session_name: 'random', enable_ai_healing: 'true', tcg_ai_enabled: 'true' },
        ai_healing_details: {
          total_healing_enabled_request: 0,
          total_healing_request: 0,
          script_exec_error_count: 0,
          pre_check_failure_count: 0,
          healing_failure_count: 0,
          healing_success_count: 0,
        },
      },
      { url: 'wd/hub/session/abcd/element' }, {});
      const callSuccessEvent = sinon.stub(aiCommandHandler, 'callSuccessEvent');
      const edsStub = sinon.stub(aiCommandHandler, 'sendEdsEvent').returns(null);
      const res = await aiCommandHandler.processResponse({ statusCode: 200 }, { data: { value: { key: 'id' } } });
      assert.deepEqual(res, noModifyResponse);
      sinon.assert.calledOnce(callSuccessEvent);
      callSuccessEvent.restore();
    });

    it('should callSuccessEvent if output.statusCode is 200 in async', async () => {
      constants.DO_CALL_TO_AI_EXTENSION_IN_ASYNC = true;
      const aiCommandHandler = new AICommandHandler({
        rails_session_id: 1234,
        dialect: 'W3C',
        aiSessionDetails: { session_name: 'random', enable_ai_healing: 'true', tcg_ai_enabled: 'true' },
        ai_healing_details: {
          total_healing_enabled_request: 0,
          total_healing_request: 0,
          script_exec_error_count: 0,
          pre_check_failure_count: 0,
          healing_failure_count: 0,
          healing_success_count: 0,
        },
      }, { url: 'wd/hub/session/abcd/element' }, {});
      const callSuccessEvent = sinon.stub(aiCommandHandler, 'callSuccessEvent');
      const edsStub = sinon.stub(aiCommandHandler, 'sendEdsEvent').returns(null);
      const res = await aiCommandHandler.processResponse({ statusCode: 200 }, { data: { value: { key: 'id' } } });
      assert.deepEqual(res, noModifyResponse);
      sinon.assert.calledOnce(callSuccessEvent);
      callSuccessEvent.restore();
      constants.DO_CALL_TO_AI_EXTENSION_IN_ASYNC = false;
    });

    context('should not execute callFailureEvent if healing is disabled', async () => {
      const healedJarResponse = 'test';
      const healingSuccessResponse = { modifyResponse: true, modifyData: healedJarResponse };
      it('handleFindElementFailure returns healingSuccess as false', async () => {
        const aiCommandHandler = new AICommandHandler({ dialect: 'W3C', aiSessionDetails: { session_name: 'random', enable_ai_healing: 'false' } }, {}, {});
        const callFailureEvent = sinon.stub(aiCommandHandler, 'callFailureEvent').returns({ healingSuccess: false });
        const edsStub = sinon.stub(aiCommandHandler, 'sendEdsEvent').returns(null);
        const res = await aiCommandHandler.processResponse({ statusCode: 404 }, {});
        assert.deepEqual(res, noModifyResponse);
        sinon.assert.notCalled(callFailureEvent);
        callFailureEvent.restore();
      });

      it('handleFindElementFailure returns healingSuccess as true', async () => {
        const aiCommandHandler = new AICommandHandler({ dialect: 'W3C', aiSessionDetails: { session_name: 'random', enable_ai_healing: 'false' } }, {}, {});
        const callFailureEvent = sinon.stub(aiCommandHandler, 'callFailureEvent').returns({ healingSuccess: true, healingResponse: healedJarResponse });
        const edsStub = sinon.stub(aiCommandHandler, 'sendEdsEvent').returns(null);
        const res = await aiCommandHandler.processResponse({ statusCode: 500 }, {});
        assert.deepEqual(res, noModifyResponse);
        sinon.assert.notCalled(callFailureEvent);
        callFailureEvent.restore();
      });
    });

    context('should callFailureEvent if output.statusCode is 404 and isW3CDialect is true', async () => {
      const healedJarResponse = 'test';
      const healingSuccessResponse = { modifyResponse: true, modifyData: healedJarResponse };

      it('callFailureEvent called for healing disabled but soft heal enabled', async () => {
        const aiCommandHandler = new AICommandHandler({
          dialect: 'OSS',
          aiSessionDetails: {
            session_name: 'random',
            enable_ai_healing: 'false',
            ai_soft_heal: 'true',
            tcg_ai_enabled: 'true',
          },
          ai_healing_details: {
            total_healing_enabled_request: 0,
            total_healing_request: 0,
            script_exec_error_count: 0,
            pre_check_failure_count: 0,
            healing_failure_count: 0,
            healing_success_count: 0,
          },
        }, {}, {});
        const callFailureEvent = sinon.stub(aiCommandHandler, 'callFailureEvent').returns({ healingSuccess: false });
        await aiCommandHandler.processResponse({ statusCode: 500 }, {});
        sinon.assert.calledOnce(callFailureEvent);
        callFailureEvent.restore();
      });


      it('callFailureEvent called for healing disabled but soft heal enabled', async () => {
        const aiCommandHandler = new AICommandHandler({
          dialect: 'OSS',
          aiSessionDetails: {
            session_name: 'random',
            enable_ai_healing: 'false',
            ai_soft_heal: 'true',
            tcg_ai_enabled: 'true',
          },
          ai_healing_details: {
            total_healing_enabled_request: 0,
            total_healing_request: 0,
            script_exec_error_count: 0,
            pre_check_failure_count: 0,
            healing_failure_count: 0,
            healing_success_count: 0,
          },
          rails_session_id: 1234,
        }, {}, {});
        const callFailureEvent = sinon.stub(aiCommandHandler, 'callFailureEvent').returns({ healingSuccess: false, preCheckFailed: false });
        await aiCommandHandler.processResponse({ statusCode: 500 }, {});
        sinon.assert.calledOnce(callFailureEvent);
        callFailureEvent.restore();
      });

      it('handleFindElementFailure returns healingSuccess as false', async () => {
        const aiCommandHandler = new AICommandHandler({
          rails_session_id: 1234,
          dialect: 'W3C',
          aiSessionDetails: { session_name: 'random', enable_ai_healing: 'true', tcg_ai_enabled: 'true' },
          ai_healing_details: {
            total_healing_enabled_request: 0,
            total_healing_request: 0,
            script_exec_error_count: 0,
            pre_check_failure_count: 0,
            healing_failure_count: 0,
            healing_success_count: 0,
          },
        }, {}, {});
        const callFailureEvent = sinon.stub(aiCommandHandler, 'callFailureEvent').returns({ healingSuccess: false, preCheckFailed: false });
        const edsStub = sinon.stub(aiCommandHandler, 'sendEdsEvent').returns(null);
        const res = await aiCommandHandler.processResponse({ statusCode: 404 }, {});
        const noModifyRes = { aiFailureEvent: true, aiRetryCount: undefined, modifyResponse: false };
        assert.deepEqual(res, noModifyRes);
        sinon.assert.calledOnce(callFailureEvent);
        callFailureEvent.restore();
      });

      it('handleFindElementFailure returns healingSuccess as true', async () => {
        const aiCommandHandler = new AICommandHandler({
          dialect: 'W3C',
          aiSessionDetails: { session_name: 'random', enable_ai_healing: 'true', tcg_ai_enabled: 'true' },
          ai_healing_details: {
            total_healing_enabled_request: 0,
            total_healing_request: 0,
            script_exec_error_count: 0,
            pre_check_failure_count: 0,
            healing_failure_count: 0,
            healing_success_count: 0,
          },
        }, {}, {});
        const callFailureEvent = sinon.stub(aiCommandHandler, 'callFailureEvent').returns({ healingSuccess: true, healingResponse: healedJarResponse, preCheckFailed: false });
        const edsStub = sinon.stub(aiCommandHandler, 'sendEdsEvent').returns(null);
        const res = await aiCommandHandler.processResponse({ statusCode: 404 }, {});
        sinon.assert.calledOnce(callFailureEvent);
        callFailureEvent.restore();
      });
    });

    context('should callFailureEvent if output.statusCode is 500 and isW3CDialect is false', async () => {
      const healedJarResponse = 'test';
      const healingSuccessResponse = { modifyResponse: true, modifyData: healedJarResponse };

      it('callFailureEvent called for healing disabled but soft heal enabled', async () => {
        const aiCommandHandler = new AICommandHandler({
          dialect: 'OSS',
          aiSessionDetails: {
            session_name: 'random',
            enable_ai_healing: 'false',
            ai_soft_heal: 'true',
            tcg_ai_enabled: 'true',
          },
          ai_healing_details: {
            total_healing_enabled_request: 0,
            total_healing_request: 0,
            script_exec_error_count: 0,
            pre_check_failure_count: 0,
            healing_failure_count: 0,
            healing_success_count: 0,
          },
        }, {}, {});
        const callFailureEvent = sinon.stub(aiCommandHandler, 'callFailureEvent').returns({ healingSuccess: true, healingResponse: healedJarResponse, preCheckFailed: false });
        await aiCommandHandler.processResponse({ statusCode: 500 }, {});
        sinon.assert.calledOnce(callFailureEvent);
        callFailureEvent.restore();
      });

      it('handleFindElementFailure returns healingSuccess as false', async () => {
        const aiCommandHandler = new AICommandHandler({
          dialect: 'OSS',
          aiSessionDetails: { session_name: 'random', enable_ai_healing: 'true', tcg_ai_enabled: 'true' },
          ai_healing_details: {
            total_healing_enabled_request: 0,
            total_healing_request: 0,
            script_exec_error_count: 0,
            pre_check_failure_count: 0,
            healing_failure_count: 0,
            healing_success_count: 0,
          },
        }, {}, {});
        const callFailureEvent = sinon.stub(aiCommandHandler, 'callFailureEvent').returns({ healingSuccess: false, preCheckFailed: false });
        const edsStub = sinon.stub(aiCommandHandler, 'sendEdsEvent').returns(null);
        const res = await aiCommandHandler.processResponse({ statusCode: 500 }, {});
        const noModifyRes = { aiFailureEvent: true, aiRetryCount: undefined, modifyResponse: false };
        assert.deepEqual(res, noModifyRes);
        sinon.assert.calledOnce(callFailureEvent);
        callFailureEvent.restore();
      });

      it('handleFindElementFailure returns healingSuccess as true', async () => {
        const aiCommandHandler = new AICommandHandler({
          dialect: 'OSS',
          aiSessionDetails: { session_name: 'random', enable_ai_healing: 'true', tcg_ai_enabled: 'true' },
          ai_healing_details: {
            total_healing_enabled_request: 0,
            total_healing_request: 0,
            script_exec_error_count: 0,
            pre_check_failure_count: 0,
            healing_failure_count: 0,
            healing_success_count: 0,
          },
        }, {}, {});
        const callFailureEvent = sinon.stub(aiCommandHandler, 'callFailureEvent').returns({ healingSuccess: true, healingResponse: healedJarResponse, preCheckFailed: false });
        const edsStub = sinon.stub(aiCommandHandler, 'sendEdsEvent').returns(null);
        const res = await aiCommandHandler.processResponse({ statusCode: 500 }, {});
        sinon.assert.calledOnce(callFailureEvent);
        callFailureEvent.restore();
      });
    });

    it('should call nothing if not in desired outputs', async () => {
      const aiCommandHandler = new AICommandHandler({ dialect: 'OSS', aiSessionDetails: { session_name: 'random', enable_ai_healing: 'true', tcg_ai_enabled: 'true' } }, {}, {});
      const callFailureEvent = sinon.stub(aiCommandHandler, 'callFailureEvent');
      const edsStub = sinon.stub(aiCommandHandler, 'sendEdsEvent').returns(null);
      const res = await aiCommandHandler.processResponse({ statusCode: 204 }, {});
      assert.deepEqual(res, noModifyResponse);
      sinon.assert.notCalled(callFailureEvent);
      callFailureEvent.restore();
    });
  });

  describe('#callSuccessEvent()', () => {
    it('should call the success event with correct parameters', async () => {
      const params = {
        body: JSON.stringify({
          using: 'testUsing',
          value: 'testValue',
        }),
      };

      const sessionKeyObj = {
        aiSessionDetails: {
          session_name: 'testSession',
          project_name: 'testProject',
          enable_ai_healing: 'true',
          tcg_ai_enabled: 'true',
          is_test_bed_data_collection_enabled: 'false',
        },
        ai_healing_details: {
          total_healing_enabled_request: 0,
          total_healing_request: 0,
          script_exec_error_count: 0,
          pre_check_failure_count: 0,
          healing_failure_count: 0,
          healing_success_count: 0,
        },
        rails_session_id: 1234,
        group_id: 2,
        aiTcgDetails: JSON.stringify({ region: 'us-east-1', tcgUrls: { 'us-east-1': 'tcg.browserstack.com' } }),
      };
      const aiCommandHandler = new AICommandHandler(sessionKeyObj, { url: 'wd/hub/session/abcd/element' });
      const callEventStub = sinon.stub(aiCommandHandler, 'callEvent');
      const listOfCommandsStub = sinon.stub(AICommandHandler, 'getCommandRedis').returns('commands');
      await aiCommandHandler.callSuccessEvent(params, { data: '{"value":{"key":"refid"}}' });
      const expectedEvent = 'window.dispatchEvent(new CustomEvent(\'ai-heal-find-element-success\',{ detail: {\'testUsing\': \'testValue\', testName: \'testSession\', projectName: \'testProject\', groupId: \'2\', listOfCommands: \'[commands]\', sessionId: \'1234\', tcgDetails: \'{\\"region\\":\\"us-east-1\\",\\"tcgUrls\\":{\\"us-east-1\\":\\"tcg.browserstack.com\\"}}\', rootId: \'undefined\', referenceId: \'refid\', isGetShadowRoot: false, isTestBedDataCollectionEnabled: false }}))';
      sinon.assert.calledWith(callEventStub, expectedEvent, 'success');
      callEventStub.restore();
      listOfCommandsStub.restore();
    });

    it('should call the get shadow root success event with correct parameters', async () => {
      const params = {
        body: JSON.stringify({
          using: 'testUsing',
          value: 'testValue',
        }),
      };

      const sessionKeyObj = {
        aiSessionDetails: {
          session_name: 'testSession',
          project_name: 'testProject',
          enable_ai_healing: 'true',
          tcg_ai_enabled: 'true',
          is_test_bed_data_collection_enabled: 'false',
        },
        ai_healing_details: {
          total_healing_enabled_request: 0,
          total_healing_request: 0,
          script_exec_error_count: 0,
          pre_check_failure_count: 0,
          healing_failure_count: 0,
          healing_success_count: 0,
        },
        rails_session_id: 1234,
        group_id: 2,
        aiTcgDetails: JSON.stringify({ region: 'us-east-1', tcgUrls: { 'us-east-1': 'tcg.browserstack.com' } }),
      };
      const aiCommandHandler = new AICommandHandler(sessionKeyObj, { url: 'wd/hub/session/abcd/element/efgh/shadow' });
      const callEventStub = sinon.stub(aiCommandHandler, 'callEvent');
      const listOfCommandsStub = sinon.stub(AICommandHandler, 'getCommandRedis').returns('commands');
      await aiCommandHandler.callSuccessEvent(params, { data: '{"value":{"key":"refid"}}' });
      const expectedEvent = 'window.dispatchEvent(new CustomEvent(\'ai-heal-find-element-success\',{ detail: {\'\': \'\', testName: \'testSession\', projectName: \'testProject\', groupId: \'2\', listOfCommands: \'[commands]\', sessionId: \'1234\', tcgDetails: \'{\\"region\\":\\"us-east-1\\",\\"tcgUrls\\":{\\"us-east-1\\":\\"tcg.browserstack.com\\"}}\', rootId: \'efgh\', referenceId: \'refid\', isGetShadowRoot: true, isTestBedDataCollectionEnabled: false }}))';
      sinon.assert.calledWith(callEventStub, expectedEvent, 'success');
      callEventStub.restore();
      listOfCommandsStub.restore();
    });

    it('should call the success event with correct parameters even when no using/value is present', async () => {
      const params = {
        body: '{}',
      };

      const sessionKeyObj = {
        aiSessionDetails: {
          session_name: 'testSession',
          project_name: 'testProject',
          enable_ai_healing: 'true',
          tcg_ai_enabled: 'true',
          is_test_bed_data_collection_enabled: 'false',
        },
        ai_healing_details: {
          total_healing_enabled_request: 0,
          total_healing_request: 0,
          script_exec_error_count: 0,
          pre_check_failure_count: 0,
          healing_failure_count: 0,
          healing_success_count: 0,
        },
        rails_session_id: 1234,
        group_id: 2,
        aiTcgDetails: JSON.stringify({ region: 'us-east-1', tcgUrls: { 'us-east-1': 'tcg.browserstack.com' } }),
      };
      const aiCommandHandler = new AICommandHandler(sessionKeyObj, { url: 'wd/hub/session/abcd/element' });
      const callEventStub = sinon.stub(aiCommandHandler, 'callEvent');
      const listOfCommandsStub = sinon.stub(AICommandHandler, 'getCommandRedis').returns('commands');
      await aiCommandHandler.callSuccessEvent(params, { data: '{"value":{"key":"refid"}}' });
      const expectedEvent = 'window.dispatchEvent(new CustomEvent(\'ai-heal-find-element-success\',{ detail: {\'\': \'\', testName: \'testSession\', projectName: \'testProject\', groupId: \'2\', listOfCommands: \'[commands]\', sessionId: \'1234\', tcgDetails: \'{\\"region\\":\\"us-east-1\\",\\"tcgUrls\\":{\\"us-east-1\\":\\"tcg.browserstack.com\\"}}\', rootId: \'undefined\', referenceId: \'refid\', isGetShadowRoot: false, isTestBedDataCollectionEnabled: false }}))';
      sinon.assert.calledWith(callEventStub, expectedEvent, 'success');
      callEventStub.restore();
      listOfCommandsStub.restore();
    });
  });

  describe('#callFailureEvent()', () => {
    it('should call the failure event with correct parameters', async () => {
      const params = {
        body: JSON.stringify({
          using: 'testUsing',
          value: 'testValue',
        }),
      };

      const sessionKeyObj = {
        aiSessionDetails: {
          session_name: 'testSession',
          project_name: 'testProject',
          enable_ai_healing: 'true',
          tcg_ai_enabled: 'true',
          grr_region: 'eu',
          find_element_timeout: 1,
          is_test_bed_data_collection_enabled: 'false',
        },
        ai_healing_details: {
          total_healing_enabled_request: 0,
          total_healing_request: 0,
          script_exec_error_count: 0,
          pre_check_failure_count: 0,
          healing_failure_count: 0,
          healing_success_count: 0,
        },
        group_id: 2,
        rails_session_id: 1234,
        aiTcgDetails: JSON.stringify({ region: 'us-east-1', tcgUrls: { 'us-east-1': 'tcg.browserstack.com' } }),
      };

      const aiCommandHandler = new AICommandHandler(sessionKeyObj);
      const listOfCommandsStub = sinon.stub(AICommandHandler, 'getCommandRedis').returns('commands');
      const callEventStub = sinon.stub(aiCommandHandler, 'callEvent').returns(JSON.stringify({
        value: {
          triggerTCG: true,
          preCheckFailed: false,
          url: '',
          preCheckReason: '',
          errMsg: '',
        },
      }));
      const handleFindElementFailureStub = sinon.stub(aiCommandHandler, 'handleFindElementFailure').returns({ healingSuccess: false });
      await aiCommandHandler.callFailureEvent(params);
      sinon.assert.calledWith(callEventStub, sinon.match.any, 'failure');
      sinon.assert.calledOnce(handleFindElementFailureStub);
      callEventStub.restore();
      listOfCommandsStub.restore();
      handleFindElementFailureStub.restore();
    });

    it('should call the failure event with correct parameters for shadow dom', async () => {
      const params = {
        body: JSON.stringify({
          using: 'testUsing',
          value: 'testValue',
        }),
      };

      const sessionKeyObj = {
        aiSessionDetails: {
          session_name: 'testSession',
          project_name: 'testProject',
          enable_ai_healing: 'true',
          tcg_ai_enabled: 'true',
          grr_region: 'eu',
          find_element_timeout: 1,
          is_test_bed_data_collection_enabled: 'false',
        },
        ai_healing_details: {
          total_healing_enabled_request: 0,
          total_healing_request: 0,
          script_exec_error_count: 0,
          pre_check_failure_count: 0,
          healing_failure_count: 0,
          healing_success_count: 0,
        },
        group_id: 2,
        rails_session_id: 1234,
        aiTcgDetails: JSON.stringify({ region: 'us-east-1', tcgUrls: { 'us-east-1': 'tcg.browserstack.com' } }),
      };

      const aiCommandHandler = new AICommandHandler(sessionKeyObj, { url: 'wd/hub/session/abcd/shadow/efgh/element' });
      const listOfCommandsStub = sinon.stub(AICommandHandler, 'getCommandRedis').returns('commands');
      const callEventStub = sinon.stub(aiCommandHandler, 'callEvent').returns(JSON.stringify({
        value: {
          triggerTCG: true,
          preCheckFailed: false,
          url: 'https://www.abc.com',
          preCheckReason: '',
          errMsg: '',
          scriptExecError: false,
        },
      }));
      const handleFindElementFailureStub = sinon.stub(aiCommandHandler, 'handleFindElementFailure').returns({
        healingSuccess: false,
        healingResponse: {},
        aiRetryCount: 0,
        preCheckFailed: false,
        triggerTCG: true,
        scriptExecError: false,
      });
      const result = await aiCommandHandler.callFailureEvent(params);
      expect(result).to.deep.equal({
        healingSuccess: false,
        healingResponse: {},
        aiRetryCount: 0,
        triggerTCG: true,
        preCheckFailed: false,
        scriptExecError: false,
      });
      sinon.assert.calledWith(callEventStub, sinon.match.any, 'failure');
      sinon.assert.calledOnce(handleFindElementFailureStub);
      callEventStub.restore();
      listOfCommandsStub.restore();
      handleFindElementFailureStub.restore();
    });

    it('should call the failure event with correct parameters even when no using/value is present', async () => {
      const params = {
        body: '{}',
      };

      const sessionKeyObj = {
        aiSessionDetails: {
          session_name: 'testSession',
          project_name: 'testProject',
          enable_ai_healing: 'true',
          tcg_ai_enabled: 'true',
          grr_region: 'eu',
          find_element_timeout: 1,
          is_test_bed_data_collection_enabled: 'false',
        },
        ai_healing_details: {
          total_healing_enabled_request: 0,
          total_healing_request: 0,
          script_exec_error_count: 0,
          pre_check_failure_count: 0,
          healing_failure_count: 0,
          healing_success_count: 0,
        },
        group_id: 2,
        rails_session_id: 1234,
        aiTcgDetails: JSON.stringify({ region: 'us-east-1', tcgUrls: { 'us-east-1': 'tcg.browserstack.com' } }),
      };

      const aiCommandHandler = new AICommandHandler(sessionKeyObj);
      const listOfCommandsStub = sinon.stub(AICommandHandler, 'getCommandRedis').returns('commands');
      const callEventStub = sinon.stub(aiCommandHandler, 'callEvent').returns(JSON.stringify({
        value: {
          triggerTCG: true,
          preCheckFailed: false,
          url: 'https://www.abc.com',
          preCheckReason: '',
          errMsg: '',
          scriptExecError: false,
        },
      }));
      const handleFindElementFailureStub = sinon.stub(aiCommandHandler, 'handleFindElementFailure').returns({
        healingSuccess: false,
        healingResponse: {},
        aiRetryCount: 0,
        preCheckFailed: false,
        triggerTCG: true,
        scriptExecError: false,
      });
      const result = await aiCommandHandler.callFailureEvent(params);
      expect(result).to.deep.equal({
        healingSuccess: false,
        healingResponse: {},
        aiRetryCount: 0,
        triggerTCG: true,
        preCheckFailed: false,
        scriptExecError: false,
      });
      sinon.assert.calledWith(callEventStub, sinon.match.any, 'failure');
      sinon.assert.calledOnce(handleFindElementFailureStub);
      callEventStub.restore();
      listOfCommandsStub.restore();
      handleFindElementFailureStub.restore();
    });

    it('should return defaultResponseForNonTCG when triggerTCG is false and preCheckFailed is false', async () => {
      const params = {
        body: JSON.stringify({
          using: 'testUsing',
          value: 'testValue',
        }),
      };

      const sessionKeyObj = {
        aiSessionDetails: {
          session_name: 'testSession',
          project_name: 'testProject',
          enable_ai_healing: 'true',
          tcg_ai_enabled: 'true',
          grr_region: 'eu',
          find_element_timeout: 1,
          is_test_bed_data_collection_enabled: 'false',
        },
        ai_healing_details: {
          total_healing_enabled_request: 0,
          total_healing_request: 0,
          script_exec_error_count: 0,
          pre_check_failure_count: 0,
          healing_failure_count: 0,
          healing_success_count: 0,
        },
        group_id: 2,
        rails_session_id: 1234,
        aiTcgDetails: JSON.stringify({ region: 'us-east-1', tcgUrls: { 'us-east-1': 'tcg.browserstack.com' } }),
      };

      const aiCommandHandler = new AICommandHandler(sessionKeyObj);
      const callEventStub = sinon.stub(aiCommandHandler, 'callEvent').returns(JSON.stringify({
        value: {
          triggerTCG: false,
          preCheckFailed: false,
          url: 'https://www.abc.com',
          preCheckReason: '',
          errMsg: 'some error',
          scriptExecError: true,
        },
      }));

      const result = await aiCommandHandler.callFailureEvent(params);
      expect(result).to.deep.equal({
        healingSuccess: false,
        healingResponse: {},
        aiRetryCount: 0,
        triggerTCG: false,
        preCheckFailed: false,
        scriptExecError: true,
      });

      callEventStub.restore();
    });

    it('should call handleFindElementFailure when triggerTCG is true and preCheckFailed is false', async () => {
      const params = {
        body: JSON.stringify({
          using: 'testUsing',
          value: 'testValue',
        }),
      };

      const sessionKeyObj = {
        aiSessionDetails: {
          session_name: 'testSession',
          project_name: 'testProject',
          enable_ai_healing: 'true',
          tcg_ai_enabled: 'true',
          grr_region: 'eu',
          find_element_timeout: 1,
          is_test_bed_data_collection_enabled: 'false',
        },
        ai_healing_details: {
          total_healing_enabled_request: 0,
          total_healing_request: 0,
          script_exec_error_count: 0,
          pre_check_failure_count: 0,
          healing_failure_count: 0,
          healing_success_count: 0,
        },
        group_id: 2,
        rails_session_id: 1234,
        aiTcgDetails: JSON.stringify({ region: 'us-east-1', tcgUrls: { 'us-east-1': 'tcg.browserstack.com' } }),
      };

      const aiCommandHandler = new AICommandHandler(sessionKeyObj);
      const callEventStub = sinon.stub(aiCommandHandler, 'callEvent').returns(JSON.stringify({
        value: {
          triggerTCG: true,
          preCheckFailed: false,
          url: '',
          preCheckReason: '',
          errMsg: '',
          scriptExecError: false,
        },
      }));
      const handleFindElementFailureStub = sinon.stub(aiCommandHandler, 'handleFindElementFailure').returns({ healingSuccess: true, preCheckFailed: false });

      const result = await aiCommandHandler.callFailureEvent(params);
      sinon.assert.calledOnce(handleFindElementFailureStub);

      callEventStub.restore();
      handleFindElementFailureStub.restore();
    });

    it('should skip healing when preCheckFailed is true', async () => {
      const params = {
        body: JSON.stringify({
          using: 'testUsing',
          value: 'testValue',
        }),
      };

      const sessionKeyObj = {
        aiSessionDetails: {
          session_name: 'testSession',
          project_name: 'testProject',
          enable_ai_healing: 'true',
          tcg_ai_enabled: 'true',
          grr_region: 'eu',
          find_element_timeout: 1,
          is_test_bed_data_collection_enabled: 'false',
        },
        ai_healing_details: {
          total_healing_enabled_request: 0,
          total_healing_request: 0,
          script_exec_error_count: 0,
          pre_check_failure_count: 0,
          healing_failure_count: 0,
          healing_success_count: 0,
        },
        group_id: 2,
        rails_session_id: 1234,
        aiTcgDetails: JSON.stringify({ region: 'us-east-1', tcgUrls: { 'us-east-1': 'tcg.browserstack.com' } }),
      };

      const aiCommandHandler = new AICommandHandler(sessionKeyObj);
      const callEventStub = sinon.stub(aiCommandHandler, 'callEvent').returns(JSON.stringify({
        value: {
          triggerTCG: true,
          preCheckFailed: true,
          preCheckReason: 'Some reason',
          url: 'https://www.abc.com',
          errMsg: '',
          scriptExecError: false,
        },
      }));

      const result = await aiCommandHandler.callFailureEvent(params);
      expect(result).to.deep.equal({
        healingSuccess: false,
        healingResponse: {},
        aiRetryCount: 0,
        triggerTCG: true,
        preCheckFailed: true,
        scriptExecError: false,
      });

      callEventStub.restore();
    });

    it('should handle connection error and skip healing', async () => {
      const params = {
        body: JSON.stringify({
          using: 'testUsing',
          value: 'testValue',
        }),
      };

      const sessionKeyObj = {
        aiSessionDetails: {
          session_name: 'testSession',
          project_name: 'testProject',
          enable_ai_healing: 'true',
          tcg_ai_enabled: 'true',
          grr_region: 'eu',
          find_element_timeout: 1,
          is_test_bed_data_collection_enabled: 'false',
        },
        ai_healing_details: {
          total_healing_enabled_request: 0,
          total_healing_request: 0,
          script_exec_error_count: 0,
          pre_check_failure_count: 0,
          healing_failure_count: 0,
          healing_success_count: 0,
        },
        group_id: 2,
        rails_session_id: 1234,
        aiTcgDetails: JSON.stringify({ region: 'us-east-1', tcgUrls: { 'us-east-1': 'tcg.browserstack.com' } }),
      };

      const aiCommandHandler = new AICommandHandler(sessionKeyObj);
      const callEventStub = sinon.stub(aiCommandHandler, 'callEvent').throws(new Error('Connection error'));

      const result = await aiCommandHandler.callFailureEvent(params);
      expect(result).to.deep.equal({
        healingSuccess: false,
        healingResponse: {},
        aiRetryCount: 0,
        triggerTCG: false,
        preCheckFailed: false,
        scriptExecError: true,
      });

      callEventStub.restore();
    });

    it('should handle errors in callEvent gracefully', async () => {
      const params = {
        body: JSON.stringify({
          using: 'testUsing',
          value: 'testValue',
        }),
      };

      const sessionKeyObj = {
        aiSessionDetails: {
          session_name: 'testSession',
          project_name: 'testProject',
          enable_ai_healing: 'true',
          tcg_ai_enabled: 'true',
          grr_region: 'eu',
          find_element_timeout: 1,
          is_test_bed_data_collection_enabled: 'false',
        },
        ai_healing_details: {
          total_healing_enabled_request: 0,
          total_healing_request: 0,
          script_exec_error_count: 0,
          pre_check_failure_count: 0,
          healing_failure_count: 0,
          healing_success_count: 0,
        },
        group_id: 2,
        rails_session_id: 1234,
        aiTcgDetails: JSON.stringify({ region: 'us-east-1', tcgUrls: { 'us-east-1': 'tcg.browserstack.com' } }),
      };
      const aiCommandHandler = new AICommandHandler(sessionKeyObj);
      const callEventStub = sinon.stub(aiCommandHandler, 'callEvent').throws(new Error('Test error'));
      const result = await aiCommandHandler.callFailureEvent(params);
      expect(result.scriptExecError).to.be.true;
      callEventStub.restore();
    });
  });

  describe('#callEvent()', () => {
    it('should call the event and log the result', async () => {
      const event = 'testEvent';
      const type = 'testType';
      const sessionKeyObj = { rails_session_id: 'testSessionId' };

      const aiCommandHandler = new AICommandHandler(sessionKeyObj);
      const executeScriptStub = sinon.stub(aiCommandHandler.seleniumClient, 'executeScript').returns(Promise.resolve('testResult'));
      const logStub = sinon.stub(HubLogger, 'newCGLogger');

      await aiCommandHandler.callEvent(event, type);

      sinon.assert.calledWith(executeScriptStub, { script: event, args: [] }, { returnResp: true, parseRespose: false });
      sinon.assert.calledWith(logStub, 'AICommandHandler', `Result of  ${type} event: testResult for event: ${event} and retries: 0, executeasync: false, aicallasync: false`, LL.DEBUG, sessionKeyObj.rails_session_id);
      logStub.restore();
      executeScriptStub.restore();
    });

    it('should call the event and log the result with executeScriptAsync', async () => {
      constants.USE_SELENIUM_EXECUTE_ASYNC_IN_AI = true;
      const event = 'testEvent';
      const type = 'testType';
      const sessionKeyObj = { rails_session_id: 'testSessionId' };

      const aiCommandHandler = new AICommandHandler(sessionKeyObj);
      const executeScriptStub = sinon.stub(aiCommandHandler.seleniumClient, 'executeScriptAsync').returns(Promise.resolve('testResult'));
      const logStub = sinon.stub(HubLogger, 'newCGLogger');

      await aiCommandHandler.callEvent(event, type);
      const asyncEvent = `let done = arguments[0]; ${event}; done();`;
      sinon.assert.calledWith(executeScriptStub, { script: asyncEvent, args: [] }, { returnResp: true, parseRespose: false });
      sinon.assert.calledWith(logStub, 'AICommandHandler', `Result of  ${type} event: testResult for event: ${asyncEvent} and retries: 0, executeasync: true, aicallasync: false`, LL.DEBUG, sessionKeyObj.rails_session_id);
      logStub.restore();
      executeScriptStub.restore();
      constants.USE_SELENIUM_EXECUTE_ASYNC_IN_AI = false;
    });

    it('should retry and log the error if the event execution fails', async () => {
      const event = 'testEvent';
      const type = 'testType';
      const sessionKeyObj = { rails_session_id: 'testSessionId' };

      const aiCommandHandler = new AICommandHandler(sessionKeyObj);
      const executeScriptStub = sinon.stub(aiCommandHandler.seleniumClient, 'executeScript').throws(new Error('testError'));
      const logStub = sinon.stub(HubLogger, 'newCGLogger');

      await aiCommandHandler.callEvent(event, type);

      sinon.assert.calledWith(executeScriptStub, { script: event, args: [] }, { returnResp: true, parseRespose: false });
      sinon.assert.calledWith(logStub, 'AICommandHandler', `Error in executing ${type} event: ${event} with error: Error: testError and retries: 0`, LL.ERROR, sessionKeyObj.rails_session_id);
      logStub.restore();
      executeScriptStub.restore();
    });
  });

  describe('#pushCommandRedis', () => {
    let logStub; let sessionKeyObj; let constructCommandStub; let
      request;

    beforeEach(() => {
      sessionKeyObj = {
        rails_session_id: '1234',
        aiSessionTimestamp: 123456789,
      };
      request = {
        url: '/wd/hub/session/1234/element',
      };
      logStub = sinon.stub(HubLogger, 'newCGLogger');
      constructCommandStub = sinon.stub(AICommandHelper, 'constructCommand').returns('command');
    });

    afterEach(() => {
      logStub.restore();
      constructCommandStub.restore();
    });

    it('should not call redis client if session id is undefined', async () => {
      const appendStub = sinon.stub();
      const redisClient = {
        append: appendStub,
      };
      const getRedisClientStub = sinon.stub(AICommandHelper, 'getRedisClient').returns(redisClient);
      await AICommandHandler.pushCommandRedis({}, request);
      expect(appendStub.notCalled);
      getRedisClientStub.restore();
    });

    it('should call append even if request URL not present', async () => {
      const appendStub = sinon.stub();
      const redisClient = {
        append: appendStub,
      };
      const getRedisClientStub = sinon.stub(AICommandHelper, 'getRedisClient').returns(redisClient);
      await AICommandHandler.pushCommandRedis(sessionKeyObj, {});
      expect(appendStub.calledOnce);
      getRedisClientStub.restore();
    });

    it('should log an error if there is an error pushing to redis', async () => {
      const error = new Error('test error');
      const redisClient = {
        append: sinon.stub().throws(error),
      };
      const getRedisClientStub = sinon.stub(AICommandHelper, 'getRedisClient').returns(redisClient);
      await AICommandHandler.pushCommandRedis(sessionKeyObj, request);
      sinon.assert.calledWith(
        logStub,
        'AICommandHandler',
        `Error pushing command to redis: ${error.message}`,
        LL.ERROR,
        sessionKeyObj.rails_session_id
      );
      getRedisClientStub.restore();
    });
  });

  describe('#getCommandRedis', () => {
    let logStub; let sessionKeyObj; let
      constructCommandStub;

    beforeEach(() => {
      sessionKeyObj = {
        rails_session_id: '1234',
        aiSessionTimestamp: 123456789,
      };
      logStub = sinon.stub(HubLogger, 'newCGLogger');
      constructCommandStub = sinon.stub(AICommandHelper, 'constructCommand').returns('command');
    });

    afterEach(() => {
      logStub.restore();
      constructCommandStub.restore();
    });

    it('should return an empty string if the redis client or session id is undefined', async () => {
      const result = await AICommandHandler.getCommandRedis({});
      expect(result).to.equal('');
    });

    it('should retrieve the commands from redis', async () => {
      const redisClientStub = sinon.stub().returns('command1,command2');
      const getRedisClientStub = sinon.stub(AICommandHelper, 'getRedisClient').returns({ getset: redisClientStub });
      const result = await AICommandHandler.getCommandRedis(sessionKeyObj);
      expect(result).to.equal('command1,command2');
      getRedisClientStub.restore();
    });

    it('should retrieve the commands from redis and remove comma', async () => {
      const redisClientStub = sinon.stub().returns('command1,command2,');
      const getRedisClientStub = sinon.stub(AICommandHelper, 'getRedisClient').returns({ getset: redisClientStub });
      const result = await AICommandHandler.getCommandRedis(sessionKeyObj);
      expect(result).to.equal('command1,command2');
      getRedisClientStub.restore();
    });

    it('should log an error if there is an error retrieving the commands from redis', async () => {
      const error = new Error('test error');
      const redisClientStub = sinon.stub().throws(error);
      const getRedisClientStub = sinon.stub(AICommandHelper, 'getRedisClient').returns({ getset: redisClientStub });
      await AICommandHandler.getCommandRedis(sessionKeyObj);
      sinon.assert.calledWith(
        logStub,
        'AICommandHandler',
        `Error retrieving command: ${error.message}`,
        LL.ERROR,
        sessionKeyObj.rails_session_id
      );
      getRedisClientStub.restore();
    });
  });

  describe('#handleRedisKeyRemoval', () => {
    let logStub; let sessionKeyObj; let
      constructCommandStub;

    beforeEach(() => {
      sessionKeyObj = {
        rails_session_id: '1234',
        aiSessionTimestamp: 123456789,
      };
      logStub = sinon.stub(HubLogger, 'newCGLogger');
      constructCommandStub = sinon.stub(AICommandHelper, 'constructCommand').returns('command');
    });

    afterEach(() => {
      logStub.restore();
      constructCommandStub.restore();
    });

    it('should not call redis client if session id is undefined', async () => {
      const appendStub = sinon.stub();
      const redisClient = {
        del: appendStub,
      };
      const getRedisClientStub = sinon.stub(AICommandHelper, 'getRedisClient').returns(redisClient);
      await AICommandHandler.handleRedisKeyRemoval();
      expect(appendStub.notCalled);
      getRedisClientStub.restore();
    });

    it('should log an error if there is an error deleting the key from redis', async () => {
      const error = new Error('test error');
      const redisClientStub = sinon.stub().throws(error);
      const getRedisClientStub = sinon.stub(AICommandHelper, 'getRedisClient').returns({ del: redisClientStub });
      await AICommandHandler.handleRedisKeyRemoval(sessionKeyObj.rails_session_id);
      sinon.assert.calledWith(
        logStub,
        'AICommandHandler',
        `Error removing redis key: ${error.message}`,
        LL.ERROR,
        sessionKeyObj.rails_session_id
      );
      getRedisClientStub.restore();
    });
  });

  describe('#getResultsFromTcg', () => {
    const sessionKeyObj = {
      aiSessionDetails: {
        session_name: 'testSession',
        project_name: 'testProject',
        enable_ai_healing: 'true',
        tcg_ai_enabled: 'true',
        ai_ext_localstorage_flag: false,
      },
      rails_session_id: 1234,
      group_id: 2,
    };
    let originalSleep;
    beforeEach(() => {
      originalSleep = AICommandHelper.TCG_ENDPOINTS.GET_RESULT.sleep;
    });
    afterEach(() => {
      AICommandHelper.TCG_ENDPOINTS.GET_RESULT.sleep = originalSleep;
    });
    it('should return the expected response when the TCG service returns a 200 status code with a successful response', async () => {
      const data = '{"data": {"sessionId": "1234"}}';
      const expectedResponse = {
        aiRetryCount: 0,
        success: true,
        data: {
          testing: 'abcd',
        },
      };
      const mockResponse = {
        statusCode: 200,
        data: JSON.stringify(expectedResponse),
      };
      const aiCommandHandler = new AICommandHandler(sessionKeyObj);
      const stubMakeRequestTcg = sinon.stub(AICommandHelper, 'makeRequestTcg').returns(mockResponse);

      const response = await aiCommandHandler.getResultsFromTcg(data, 3);

      assert.deepEqual(response, expectedResponse);
      sinon.assert.calledWith(
        stubMakeRequestTcg,
        TCG_ENDPOINTS.GET_RESULT.method,
        TCG_ENDPOINTS.GET_RESULT.path,
        data,
        TCG_HEADERS
      );
      stubMakeRequestTcg.restore();
    });

    it('should return the expected response when the localstorage  returns a 200 status code with a successful response', async () => {
      const data = '{"data": {"sessionId": "1234"}}';
      const expectedResponse = {
        aiRetryCount: 0,
        success: true,
        data: {
          testing: 'abcd',
        },
      };
      const mockResponse = {
        statusCode: 200,
        data: JSON.stringify(expectedResponse),
      };
      sessionKeyObj.aiSessionDetails.ai_ext_localstorage_flag = true;
      const aiCommandHandler = new AICommandHandler(sessionKeyObj);
      const stubMakeRequestLocalStorage = sinon.stub(aiCommandHandler, 'getLocalStorageResults').returns(mockResponse);

      const response = await aiCommandHandler.getResultsFromTcg(data, 3);

      assert.deepEqual(response, expectedResponse);
      sessionKeyObj.aiSessionDetails.ai_ext_localstorage_flag = false;
      stubMakeRequestLocalStorage.restore();
    });

    it('should retry and then send success as false if TCG service returns a non 200 status code', async () => {
      const data = '{"data": {"sessionId": "1234"}}';
      const expectedResponse = {
        success: false,
        data: {
          aiRetryCount: 3,
        },
      };
      const mockResponse = {
        statusCode: 205,
        data: 'error',
      };
      const aiCommandHandler = new AICommandHandler(sessionKeyObj);
      const stubMakeRequestTcg = sinon.stub(AICommandHelper, 'makeRequestTcg').returns(mockResponse);
      AICommandHelper.TCG_ENDPOINTS.GET_RESULT.sleep = 10;

      const response = await aiCommandHandler.getResultsFromTcg(data, 3);

      assert.deepEqual(response, expectedResponse);
      sinon.assert.calledWith(
        stubMakeRequestTcg,
        TCG_ENDPOINTS.GET_RESULT.method,
        TCG_ENDPOINTS.GET_RESULT.path,
        data,
        TCG_HEADERS
      );
      stubMakeRequestTcg.restore();
    });

    it('should retry and then send success as false if response is 200 but TCG service success data isn\'t present', async () => {
      const data = '{"data": {"sessionId": "1234"}}';
      const expectedResponse = {
        success: false,
        data: {
          aiRetryCount: 3,
        },
      };
      const mockResponse = {
        statusCode: 200,
        data: '{ "test": "test"}',
      };
      const aiCommandHandler = new AICommandHandler(sessionKeyObj);
      const stubMakeRequestTcg = sinon.stub(AICommandHelper, 'makeRequestTcg').returns(mockResponse);
      AICommandHelper.TCG_ENDPOINTS.GET_RESULT.sleep = 10;

      const response = await aiCommandHandler.getResultsFromTcg(data, 3);

      assert.deepEqual(response, expectedResponse);
      sinon.assert.calledWith(
        stubMakeRequestTcg,
        TCG_ENDPOINTS.GET_RESULT.method,
        TCG_ENDPOINTS.GET_RESULT.path,
        data,
        TCG_HEADERS
      );
      stubMakeRequestTcg.restore();
    });

    it('should retry and then send success as false if TCG service returns a 200 status code but response data as false', async () => {
      const data = '{"data": {"sessionId": "1234"}}';
      const expectedResponse = {
        success: false,
        data: {
          aiRetryCount: 3,
        },
      };
      const mockResponse = {
        statusCode: 205,
        data: JSON.stringify(expectedResponse),
      };
      const aiCommandHandler = new AICommandHandler(sessionKeyObj);
      const stubMakeRequestTcg = sinon.stub(AICommandHelper, 'makeRequestTcg').returns(mockResponse);
      AICommandHelper.TCG_ENDPOINTS.GET_RESULT.sleep = 10;

      const response = await aiCommandHandler.getResultsFromTcg(data, 3);

      assert.deepEqual(response, expectedResponse);
      sinon.assert.calledWith(
        stubMakeRequestTcg,
        TCG_ENDPOINTS.GET_RESULT.method,
        TCG_ENDPOINTS.GET_RESULT.path,
        data,
        TCG_HEADERS
      );
      stubMakeRequestTcg.restore();
    });

    it('should retry and then send success as false if TCG service response data doesn\'t have success param', async () => {
      const data = '{"data": {"sessionId": "1234"}}';
      const expectedResponse = {
        success: false,
        data: {
          aiRetryCount: 3,
        },
      };
      const mockResponse = {
        statusCode: 205,
        data: JSON.stringify(expectedResponse),
      };
      const aiCommandHandler = new AICommandHandler(sessionKeyObj);
      const stubMakeRequestTcg = sinon.stub(AICommandHelper, 'makeRequestTcg').returns(mockResponse);
      AICommandHelper.TCG_ENDPOINTS.GET_RESULT.sleep = 10;

      const response = await aiCommandHandler.getResultsFromTcg(data, 3);

      assert.deepEqual(response, expectedResponse);
      sinon.assert.calledWith(
        stubMakeRequestTcg,
        TCG_ENDPOINTS.GET_RESULT.method,
        TCG_ENDPOINTS.GET_RESULT.path,
        data,
        TCG_HEADERS
      );
      stubMakeRequestTcg.restore();
    });

    it('should retry and then send success as false if error is thrown during call to TCG service', async () => {
      const data = '{"data": {"sessionId": "1234"}}';
      const expectedResponse = {
        success: false,
        data: {
          aiRetryCount: 3,
        },
      };
      const aiCommandHandler = new AICommandHandler(sessionKeyObj);
      const stubMakeRequestTcg = sinon.stub(AICommandHelper, 'makeRequestTcg').throws(new Error('error'));
      AICommandHelper.TCG_ENDPOINTS.GET_RESULT.sleep = 10;

      const response = await aiCommandHandler.getResultsFromTcg(data, 3);

      assert.deepEqual(response, expectedResponse);
      sinon.assert.calledWith(
        stubMakeRequestTcg,
        TCG_ENDPOINTS.GET_RESULT.method,
        TCG_ENDPOINTS.GET_RESULT.path,
        data,
        TCG_HEADERS
      );
      stubMakeRequestTcg.restore();
    });
  });

  describe('#handleFindElementFailure', () => {
    let sessionKeyObj;
    let output;
    beforeEach(() => {
      sessionKeyObj = {
        aiSessionDetails: {
          session_name: 'testSession',
          project_name: 'testProject',
          enable_ai_healing: 'true',
          tcg_ai_enabled: 'true',
          ai_soft_heal: 'false',
        },
        rails_session_id: 1234,
        group_id: 2,
      };
      output = {
        data: '{}',
      };
    });

    it('should return the expected response when getResultsFromTcg returns a successful response and findElement is successful', async () => {
      constants.global_registry[1234] = {};
      const tcgResponse = {
        success: true,
        data: {
          using: 'css selector',
          value: '.example-class',
        },
      };
      const jarResponse = { a: 'b' };
      const findElementResponse = {
        statusCode: 200,
        data: JSON.stringify(jarResponse),
      };

      const aiCommandHandler = new AICommandHandler(sessionKeyObj, { url: 'wd/hub/session/abcd/element' });
      sinon.stub(aiCommandHandler, 'getResultsFromTcg').returns(tcgResponse);
      sinon.stub(aiCommandHandler.seleniumClient, 'findElement').returns(findElementResponse);

      const result = await aiCommandHandler.handleFindElementFailure(output);

      expect(result.healingSuccess).to.be.true;
      expect(result.preCheckFailed).to.be.false;
      expect(result.scriptExecError).to.be.false;
      expect(result.healingResponse).to.deep.equal(findElementResponse);

      aiCommandHandler.getResultsFromTcg.restore();
      aiCommandHandler.seleniumClient.findElement.restore();
      constants.global_registry = {};
    });

    it('should return the expected response when getResultsFromTcg returns a successful response and findElement is successful within findInTimeout', async () => {
      constants.global_registry[1234] = {};
      const tcgResponse = {
        success: true,
        data: {},
        additionalData: {
          foundInTimeout: true,
        },
      };
      const jarResponse = { a: 'b' };
      const findElementResponse = {
        statusCode: 200,
        data: JSON.stringify(jarResponse),
      };
      const request = {
        log_data: JSON.stringify({
          using: 'css selector',
          value: '.example-class',
        }),
        url: 'wd/hub/session/abcd/element',
      };

      const aiCommandHandler = new AICommandHandler(sessionKeyObj, request);
      sinon.stub(aiCommandHandler, 'getResultsFromTcg').returns(tcgResponse);
      sinon.stub(aiCommandHandler.seleniumClient, 'findElement').returns(findElementResponse);

      const result = await aiCommandHandler.handleFindElementFailure(output);

      expect(result.healingSuccess).to.be.true;
      expect(result.preCheckFailed).to.be.false;
      expect(result.scriptExecError).to.be.false;
      expect(result.healingResponse).to.deep.equal(findElementResponse);

      aiCommandHandler.getResultsFromTcg.restore();
      aiCommandHandler.seleniumClient.findElement.restore();
      constants.global_registry = {};
    });

    it('should return the expected response when getResultsFromTcg returns a successful response and findElement is successful and soft healing has already been performed', async () => {
      const tcgResponse = {
        success: true,
        data: {
          'css selector': '.example-class',
        },
      };

      sessionKeyObj.aiSessionDetails.ai_soft_heal = true;
      sessionKeyObj.softHealingSuccess = 'true';
      const aiCommandHandler = new AICommandHandler(sessionKeyObj);
      sinon.stub(aiCommandHandler, 'getResultsFromTcg').returns(tcgResponse);

      const result = await aiCommandHandler.handleFindElementFailure({
        data: '{}',
      });

      expect(result.healingSuccess).to.be.true;
      expect(result.healingResponse).to.deep.equal({
        data: JSON.stringify({
          healedSelector: {
            using: 'css selector',
            value: '.example-class',
          },
        }),
      });

      aiCommandHandler.getResultsFromTcg.restore();
      delete sessionKeyObj.aiSessionDetails.ai_soft_heal;
      delete sessionKeyObj.softHealingSuccess;
    });

    it('should return the expected response when getResultsFromTcg returns a successful response and findElement is successful and healing has already been performed', async () => {
      constants.global_registry[1234] = {};
      const tcgResponse = {
        success: true,
        data: {
          'css selector': '.example-class',
        },
      };
      const jarResponse = { a: 'b' };
      const findElementResponse = {
        statusCode: 200,
        data: JSON.stringify(jarResponse),
      };

      sessionKeyObj.selfHealingSuccess = 'true';
      const aiCommandHandler = new AICommandHandler(sessionKeyObj, { url: 'wd/hub/session/abcd/element' });
      sinon.stub(aiCommandHandler, 'getResultsFromTcg').returns(tcgResponse);
      sinon.stub(aiCommandHandler.seleniumClient, 'findElement').returns(findElementResponse);

      const result = await aiCommandHandler.handleFindElementFailure();

      expect(result.healingSuccess).to.be.true;
      expect(result.healingResponse).to.deep.equal(findElementResponse);

      aiCommandHandler.getResultsFromTcg.restore();
      aiCommandHandler.seleniumClient.findElement.restore();
      constants.global_registry = {};
    });

    it('should return the expected response when getResultsFromTcg returns a successful response and findShadowElement is successful', async () => {
      const tcgResponse = {
        success: true,
        data: {
          using: 'css selector',
          value: '.example-class',
        },
      };
      const jarResponse = { a: 'b' };
      const findElementResponse = {
        statusCode: 200,
        data: JSON.stringify(jarResponse),
      };

      const aiCommandHandler = new AICommandHandler(sessionKeyObj, { url: 'wd/hub/session/abcd/shadow/efgh/element' });
      sinon.stub(aiCommandHandler, 'getResultsFromTcg').returns({ success: true, data: tcgResponse });
      sinon.stub(aiCommandHandler.seleniumClient, 'findShadowElement').returns(findElementResponse);

      const result = await aiCommandHandler.handleFindElementFailure(output);

      expect(result.healingSuccess).to.be.true;
      expect(result.healingResponse).to.deep.equal(findElementResponse);

      aiCommandHandler.getResultsFromTcg.restore();
      aiCommandHandler.seleniumClient.findShadowElement.restore();
    });

    it('should return healingSuccess as false response when getResultsFromTcg returns an unsuccessful response', async () => {
      constants.global_registry[1234] = {};
      const aiCommandHandler = new AICommandHandler(sessionKeyObj);
      sinon.stub(aiCommandHandler, 'getResultsFromTcg').returns({ success: false, data: null });
      const result = await aiCommandHandler.handleFindElementFailure(output);

      expect(result.healingSuccess).to.be.false;
      expect(result.preCheckFailed).to.be.false;
      expect(result.scriptExecError).to.be.false;
      expect(result.healingResponse).to.deep.equal({});

      aiCommandHandler.getResultsFromTcg.restore();
      constants.global_registry = {};
    });

    it('should return healingSuccess as false response when getResultsFromTcg returns 200 response but no selectors', async () => {
      const aiCommandHandler = new AICommandHandler(sessionKeyObj);
      sinon.stub(aiCommandHandler, 'getResultsFromTcg').returns({ success: true, data: { } });
      const result = await aiCommandHandler.handleFindElementFailure(output);

      expect(result.healingSuccess).to.be.false;
      expect(result.preCheckFailed).to.be.false;
      expect(result.scriptExecError).to.be.false;
      expect(result.healingResponse).to.deep.equal({});

      aiCommandHandler.getResultsFromTcg.restore();
    });

    it('should return healingSuccess as false response when getResultsFromTcg returns 200 response but no selectors', async () => {
      const aiCommandHandler = new AICommandHandler(sessionKeyObj);
      sinon.stub(aiCommandHandler, 'getResultsFromTcg').returns({ success: true, data: { using: 'xpath' } });
      const result = await aiCommandHandler.handleFindElementFailure(output);

      expect(result.healingSuccess).to.be.false;
      expect(result.preCheckFailed).to.be.false;
      expect(result.scriptExecError).to.be.false;
      expect(result.healingResponse).to.deep.equal({});

      aiCommandHandler.getResultsFromTcg.restore();
    });

    it('should return healingSuccess as false response when findElement fails', async () => {
      constants.global_registry[1234] = {};
      const tcgResponse = {
        success: true,
        data: {
          using: 'css selector',
          value: '.example-class',
        },
      };
      const findElementResponse = {
        statusCode: 400,
        data: {},
      };

      const aiCommandHandler = new AICommandHandler(sessionKeyObj);
      sinon.stub(aiCommandHandler, 'getResultsFromTcg').returns(tcgResponse);
      sinon.stub(aiCommandHandler.seleniumClient, 'findElement').returns(findElementResponse);

      const result = await aiCommandHandler.handleFindElementFailure(output);

      expect(result.healingSuccess).to.be.false;
      expect(result.preCheckFailed).to.be.false;
      expect(result.scriptExecError).to.be.false;
      expect(result.healingResponse).to.deep.equal({});

      aiCommandHandler.getResultsFromTcg.restore();
      aiCommandHandler.seleniumClient.findElement.restore();
      constants.global_registry = {};
    });

    it('should log an error when an error occurs during findElement', async () => {
      constants.global_registry[1234] = {};
      const tcgResponse = {
        success: true,
        data: {
          using: 'css selector',
          value: '.example-class',
        },
      };
      const error = new Error('Some error');

      const loggerStub = sinon.stub(HubLogger, 'newCGLogger');

      const aiCommandHandler = new AICommandHandler(sessionKeyObj, { url: 'wd/hub/session/abcd/element' });
      sinon.stub(aiCommandHandler, 'getResultsFromTcg').returns(tcgResponse);
      sinon.stub(aiCommandHandler.seleniumClient, 'findElement').throws(error);
      await aiCommandHandler.handleFindElementFailure(output);

      sinon.assert.calledOnce(loggerStub);
      sinon.assert.calledWith(loggerStub, TAG, `Error while sending healed command : ${error}`, LL.ERROR, sessionKeyObj.rails_session_id);

      aiCommandHandler.getResultsFromTcg.restore();
      aiCommandHandler.seleniumClient.findElement.restore();
      loggerStub.restore();
      constants.global_registry = {};
    });

    it('should not call findElement for soft healing', async () => {
      constants.global_registry[1234] = {};
      const tcgResponse = {
        success: true,
        data: {
          using: 'css selector',
          value: '.example-class',
        },
      };

      sessionKeyObj.aiSessionDetails = {
        session_name: 'testSession',
        project_name: 'testProject',
        enable_ai_healing: 'false',
        ai_soft_heal: 'true',
        tcg_ai_enabled: 'true',
      };
      const aiCommandHandler = new AICommandHandler(sessionKeyObj);
      sinon.stub(aiCommandHandler, 'getResultsFromTcg').returns(tcgResponse);
      const findElementStub = sinon.stub(aiCommandHandler.seleniumClient, 'findElement');
      await aiCommandHandler.handleFindElementFailure(output);

      sinon.assert.notCalled(findElementStub);

      aiCommandHandler.getResultsFromTcg.restore();
      aiCommandHandler.seleniumClient.findElement.restore();
      findElementStub.restore();
      constants.global_registry = {};
    });
  });

  describe('#retrieveRawLogData', () => {
    it('should return raw log data', () => {
      const jsonData = {
        healedSelector: {
          using: 'xpath',
          value: '//*[@id="1"]/p',
        },
      };
      const rawLogData = AICommandHandler.retrieveRawLogData(jsonData);
      expect(JSON.parse(rawLogData)).to.deep.equal({
        status: 'true',
        data: {
          using: 'xpath',
          value: '//*[@id="1"]/p',
        },
      });
    });

    it('should return empty object if jsonData is undefined', () => {
      const rawLogData = AICommandHandler.retrieveRawLogData(undefined);
      expect(JSON.parse(rawLogData)).to.deep.equal({
        status: 'false',
      });
    });

    it('should return empty object if jsonData.healedSelector is undefined', () => {
      const jsonData = {};
      const rawLogData = AICommandHandler.retrieveRawLogData(jsonData);
      expect(JSON.parse(rawLogData)).to.deep.equal({
        status: 'false',
      });
    });

    it('should return isExecuted as false if other flags has isHealSkipped as false', () => {
      const jsonData = {};
      const other_flags = { isHealSkipped: true };
      const rawLogData = AICommandHandler.retrieveRawLogData(jsonData, other_flags);
      expect(JSON.parse(rawLogData)).to.deep.equal({
        status: 'false',
        isExecuted: 'false',
      });
    });
  });
  describe('#sendEdsEvent', () => {
    const sessionKeyObj = {
      aiSessionDetails: {
        session_name: 'testSession',
        project_name: 'testProject',
        enable_ai_healing: 'true',
        tcg_ai_enabled: 'true',
      },
      rails_session_id: 1234,
      group_id: 2,
    };
    it('should return healingSuccess as false response when getResultsFromTcg returns an unsuccessful response', async () => {
      const aiCommandHandler = new AICommandHandler(sessionKeyObj);
      const edsEventStub = sinon.stub(helper, 'sendToEDS');
      await aiCommandHandler.sendEdsEvent(1, 2);
      sinon.assert.calledOnce(edsEventStub);
      edsEventStub.restore();
    });
  });
  describe('#localStorageResults', () => {
    const sessionKeyObj = {
      aiSessionDetails: {
        session_name: 'testSession',
        project_name: 'testProject',
        enable_ai_healing: 'true',
        tcg_ai_enabled: 'true',
      },
      rails_session_id: 1234,
      group_id: 2,
    };
    it('should return response with proper data when call event returns a response', async () => {
      const aiCommandHandler = new AICommandHandler(sessionKeyObj);
      const resp = JSON.stringify({
        value: JSON.stringify({
          robust: {
            locator: 'cssPath',
            path: 'button:#button',
          },
          success: true,
        }),
      });
      const callEventStub = sinon.stub(aiCommandHandler, 'callEvent').returns(resp);
      const localStorageResp = await aiCommandHandler.getLocalStorageResults();
      const expectedResp = {
        data: JSON.stringify({
          data: {
            'css selector': 'button:#button',
          },
          success: true,
        }),
        statusCode: 200,
      };
      assert.deepEqual(expectedResp, localStorageResp);
      callEventStub.restore();
    });
    it('should return response with proper data when call event returns a response but not in valid format', async () => {
      const aiCommandHandler = new AICommandHandler(sessionKeyObj);
      const resp = JSON.stringify({
        value: JSON.stringify({
          robust: {
          },
          success: false,
        }),
      });
      const callEventStub = sinon.stub(aiCommandHandler, 'callEvent').returns(resp);
      const localStorageResp = await aiCommandHandler.getLocalStorageResults();
      const expectedResp = {
        data: JSON.stringify({
          data: JSON.parse(JSON.parse(resp).value),
          success: true,
        }),
        statusCode: 200,

      };
      assert.deepEqual(expectedResp, localStorageResp);
      callEventStub.restore();
    });
    it('should return response with proper data when call event returns a response', async () => {
      const aiCommandHandler = new AICommandHandler(sessionKeyObj);
      const resp = JSON.stringify({
        value: JSON.stringify({
          robust: {
            locator: 'xpath',
            path: 'button:#button',
          },
          success: true,
        }),
      });
      const callEventStub = sinon.stub(aiCommandHandler, 'callEvent').returns(resp);
      const localStorageResp = await aiCommandHandler.getLocalStorageResults();
      const expectedResp = {
        data: JSON.stringify({
          data: {
            xpath: 'button:#button',
          },
          success: true,
        }),
        statusCode: 200,
      };
      assert.deepEqual(expectedResp, localStorageResp);
      callEventStub.restore();
    });
  });
});
