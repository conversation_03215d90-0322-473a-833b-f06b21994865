'use strict';

const { IOSFileUploadHandler } = require('../../../../../controllers/seleniumCommand/handlers/IOSFileUploadHandler');
const testHelper = require('../../../helper');
const sinon = require('sinon');
const selenium = require('../../../../../services/selenium');
const { assert } = require('chai');

describe('IOSFileUploadHandler tests', () => {
  const sessionKeyObj = testHelper.getKeyObject();
  const host = 'http://some_host';
  const key = 'abc123';
  const queryParams = `?sessionId=${key}`;
  const request = {
    url: host + queryParams,
  };
  const response = { data: 'some_data' };

  describe('processCommand', () => {
    let realIOSFileUpload;

    beforeEach(() => {
      realIOSFileUpload = sinon.stub(selenium, 'realIOSFileUpload').returns({ statusCode: 422 });
    });

    afterEach(() => {
      realIOSFileUpload.restore();
    });

    it('should return data returned by realIOSFileUpload call in returnData key', async () => {
      const requestStateObj = { key: 'value1' };
      const handler = new IOSFileUploadHandler(sessionKeyObj, request, response);
      const res = await handler.processCommand(requestStateObj);

      assert.deepEqual(res, { returnData: { statusCode: 422 } });
      sinon.assert.calledOnce(realIOSFileUpload);
      sinon.assert.calledWith(realIOSFileUpload, sessionKeyObj);
    });
  });
});
