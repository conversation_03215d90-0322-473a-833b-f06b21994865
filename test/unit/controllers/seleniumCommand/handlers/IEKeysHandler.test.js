'use strict';

const { IEKeysHandler } = require('../../../../../controllers/seleniumCommand/handlers/IEKeysHandler');
const testHelper = require('../../../helper');
const HubLogger = require('../../../../../log');
const sinon = require('sinon');
const terminal = require('../../../../../services/terminal');
const bridge = require('../../../../../bridge');
const helper = require('../../../../../helper');
const pubSub = require('../../../../../pubSub');
const { assert, expect } = require('chai');

describe('IEKeysHandler tests', () => {
  let getAutoitText;
  let sendResponse;
  let sendErrorResponse;
  const sessionKeyObj = testHelper.getKeyObject();
  const host = 'http://some_host';
  const key = 'abc123';
  const queryParams = `?sessionId=${key}`;
  let request = {
    url: host + queryParams,
  };
  const response = { data: 'some_data' };
  const options = { opt: 'some_val' };

  describe('sendKeys', () => {
    const logDataObj = { value: 'val' };

    beforeEach(() => {
      getAutoitText = sinon.stub(helper, 'getAutoitText').returns('some text value');
      sendResponse = sinon.stub(bridge, 'sendResponse');
      sendErrorResponse = sinon.stub(bridge, 'sendErrorResponse');
    });

    afterEach(() => {
      getAutoitText.restore();
      sendResponse.restore();
      sendErrorResponse.restore();
    });
    it('should call terminal send keys and sends response', async () => {
      const autoItSendKeys = sinon.stub(terminal, 'autoItSendKeys').returns(Promise.resolve(true));
      const requestStateObj = { key: 'value1', hash: 'POST:ie-keys' };
      const handler = new IEKeysHandler(sessionKeyObj, request, response);
      handler.logDataObj = logDataObj;
      await handler.sendKeys(requestStateObj, options);
      sinon.assert.calledOnce(getAutoitText);
      sinon.assert.calledOnce(autoItSendKeys);
      sinon.assert.calledWith(getAutoitText, sessionKeyObj, 'val', pubSub);
      expect(requestStateObj.hash).to.eql('POST:keys');
      expect(requestStateObj.data).to.eql(JSON.stringify({
        sessionId: sessionKeyObj.rails_session_id, status: 0, value: null,
      }));
      sinon.assert.calledWith(autoItSendKeys, sessionKeyObj, requestStateObj, options, 'some text value');
      sinon.assert.calledOnce(sendResponse);
      sinon.assert.calledWith(sendResponse, sessionKeyObj, requestStateObj);
      autoItSendKeys.restore();
    });

    it('should catch terminal send keys call error and send error response', async () => {
      const autoItSendKeys = sinon.stub(terminal, 'autoItSendKeys').returns(Promise.reject(new Error('some err')));
      const requestStateObj = { key: 'value1', hash: 'POST:ie-keys' };
      const handler = new IEKeysHandler(sessionKeyObj, request, response);
      handler.logDataObj = logDataObj;
      await handler.sendKeys(requestStateObj, options);
      sinon.assert.calledOnce(getAutoitText);
      sinon.assert.calledOnce(autoItSendKeys);
      sinon.assert.calledWith(getAutoitText, sessionKeyObj, 'val', pubSub);
      expect(requestStateObj.hash).to.eql('POST:ie-keys');
      sinon.assert.calledWith(autoItSendKeys, sessionKeyObj, requestStateObj, options, 'some text value');
      sinon.assert.calledOnce(sendErrorResponse);
      sinon.assert.calledWith(sendErrorResponse, sessionKeyObj, requestStateObj);
      autoItSendKeys.restore();
    });
  });

  describe('processCommand', () => {
    // eslint-disable-next-line camelcase
    const log_data = JSON.stringify({ value: 'val' });

    beforeEach(() => {
      getAutoitText = sinon.stub(helper, 'getAutoitText').returns('some text value');
      sendResponse = sinon.stub(bridge, 'sendResponse');
    });

    afterEach(() => {
      getAutoitText.restore();
      sendResponse.restore();
    });

    it('should call sendKeys if request has log_data and return object with returns true', async () => {
      const sendKeys = sinon.stub(IEKeysHandler.prototype, 'sendKeys');
      // eslint-disable-next-line camelcase
      request.log_data = log_data;
      const requestStateObj = { key: 'value1', hash: 'POST:ie-keys', request: { log_data } };

      const handler = new IEKeysHandler(sessionKeyObj, request, response);
      const res = await handler.processCommand(requestStateObj, options);
      expect(res.returns).to.eql(true);
      sinon.assert.calledOnce(sendKeys);
      sinon.assert.calledWith(sendKeys, requestStateObj, options);
      sendKeys.restore();
    });

    it('should not call terminal if request does not have value in log_data and return object with returns false', async () => {
      const sendKeys = sinon.stub(IEKeysHandler.prototype, 'sendKeys');
      const requestStateObj = { key: 'value1', hash: 'POST:ie-keys', request: {} };
      request = {};
      const handler = new IEKeysHandler(sessionKeyObj, request, response);
      const res = await handler.processCommand(requestStateObj, options);
      expect(res.returns).to.eql(false);
      sinon.assert.notCalled(sendKeys);
      sendKeys.restore();
    });

    it('should handle unparsable log_data and logs exception and return object with returns false', async () => {
      const sendKeys = sinon.stub(IEKeysHandler.prototype, 'sendKeys');
      const exceptionLogger = sinon.stub(HubLogger, 'exceptionLogger');
      // eslint-disable-next-line camelcase
      request.log_data = [];
      const requestStateObj = { key: 'value1', hash: 'POST:ie-keys', request: { log_data: [] } };

      const handler = new IEKeysHandler(sessionKeyObj, request, response);
      const res = await handler.processCommand(requestStateObj, options);
      expect(res.returns).to.eql(false);
      sinon.assert.notCalled(sendKeys);
      sinon.assert.calledTwice(exceptionLogger);
      sinon.assert.calledWith(exceptionLogger, `IEKeys: Error in parsing request for ${sessionKeyObj.rails_session_id} log_data: ${requestStateObj.request.log_data}`);
      sendKeys.restore();
      exceptionLogger.restore();
    });
  });
});
