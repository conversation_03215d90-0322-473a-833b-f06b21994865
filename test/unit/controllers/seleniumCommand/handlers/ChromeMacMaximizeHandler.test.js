'use strict';

const { ChromeMacMaximizeHandler } = require('../../../../../controllers/seleniumCommand/handlers/ChromeMacMaximizeHandler');
const testHelper = require('../../../helper');
const sinon = require('sinon');
const selenium = require('../../../../../services/selenium');
const { assert } = require('chai');
const hub = require('../../../../../hub');

describe('ChromeMacMaximizeHandler tests', () => {
  const sessionKeyObj = testHelper.getKeyObject();
  const host = 'http://some_host';
  const key = 'abc123';
  const queryParams = `?sessionId=${key}`;
  const request = {
    url: host + queryParams,
  };
  const response = { data: 'some_data' };

  describe('processCommand', () => {
    let chromeMacMaximize;
    let processResponse;

    beforeEach(() => {
      chromeMacMaximize = sinon.stub(selenium, 'chromeMacMaximize').returns({ status: 0 });
      processResponse = sinon.stub(hub, 'processResponse');
    });

    afterEach(() => {
      chromeMacMaximize.restore();
      processResponse.restore();
    });

    it('should call hub.processResponse with response of terminal.chromeMacMaximize and return', async () => {
      const requestStateObj = {
        key: 'value1',
        rails_session_id: 'some rails_session_id',
        index_counter: 'some index_counter',
        callbacks: 'some callbacks',
        hostname: 'some hostname',
        originalUrl: 'some originalUrl',
      };
      const handler = new ChromeMacMaximizeHandler(sessionKeyObj, request, response);
      const res = await handler.processCommand(requestStateObj);

      assert.deepEqual(res, { returns: true });
      sinon.assert.calledOnce(chromeMacMaximize);
      sinon.assert.calledOnce(processResponse);
      sinon.assert.calledWith(processResponse, request, response, sessionKeyObj, {
        data: JSON.stringify({ status: 0 }),
        remoteSessionID: sessionKeyObj.key,
        clientSessionID: sessionKeyObj.rails_session_id,
        index_counter: requestStateObj.index_counter,
        callbacks: requestStateObj.callbacks,
        hostname: requestStateObj.hostname,
        originalUrl: requestStateObj.originalUrl,
      });
    });
  });
});
