'use strict';

const { AndroidOrientationHandler } = require('../../../../../controllers/seleniumCommand/handlers/AndroidOrientationHandler');
const bridge = require('../../../../../bridge');
const requestlib = require('../../../../../lib/request');
const sinon = require('sinon');
const helper = require('../../../../../helper');
const HubLogger = require('../../../../../log');
const constants = require('../../../../../constants');

describe('AndroidOrientationHandler tests', () => {
  describe('processCommand', () => {
    it('should get orientation and send response', async () => {
      const clock = sinon.useFakeTimers();
      const sendResponse = sinon.stub(bridge, 'sendResponse');
      const requestCall = sinon.stub(requestlib, 'call').returns(Promise.resolve({}));
      const androidOrientation = new AndroidOrientationHandler({}, { method: 'GET' }, {});
      await androidOrientation.processCommand('{}', { hash: 'GET:orientation' }, 0);
      clock.tick(2000);
      sinon.assert.calledOnce(sendResponse);
      sinon.assert.calledOnce(requestCall);
      requestCall.restore();
      sendResponse.restore();
      clock.restore();
    });

    it('should set orientation and send response', async () => {
      const clock = sinon.useFakeTimers();
      const sendResponse = sinon.stub(bridge, 'sendResponse');
      const requestCall = sinon.stub(requestlib, 'call').returns(Promise.resolve({}));
      const androidOrientation = new AndroidOrientationHandler({ deviceName: 'samsung galaxy tab 4' }, { method: 'POST' }, {});
      await androidOrientation.processCommand('{ "orientation": "LANDSCAPE"}', { hash: 'GET:orientation' }, 0);
      clock.tick(2000);
      sinon.assert.calledOnce(sendResponse);
      sinon.assert.calledOnce(requestCall);
      requestCall.restore();
      sendResponse.restore();
      clock.restore();
    });

    it('should send response even in case of set orientation error', async () => {
      const sendResponse = sinon.stub(bridge, 'sendResponse');
      const requestCall = sinon.stub(requestlib, 'call').returns(Promise.reject(new Error()));
      const androidOrientation = new AndroidOrientationHandler({ deviceOrientation: 'portrait', os: 'ios' }, { method: 'POST' }, {});
      await androidOrientation.processCommand('{}', { hash: 'GET:orientation' }, 0);
      sinon.assert.calledOnce(sendResponse);
      sinon.assert.calledOnce(requestCall);
      sendResponse.restore();
      requestCall.restore();
    });

    it('will set portrait orientation if invalid value is given', async () => {
      const clock = sinon.useFakeTimers();
      const sendResponse = sinon.stub(bridge, 'sendResponse');
      const requestCall = sinon.stub(requestlib, 'call').returns(Promise.resolve({}));
      const androidOrientation = new AndroidOrientationHandler({ deviceName: 'test' }, { method: 'POST' }, {});
      await androidOrientation.processCommand('{ "orientation": "invalid" }', { hash: 'GET:orientation' }, 0);
      clock.tick(2000);
      sinon.assert.calledOnce(sendResponse);
      sinon.assert.calledOnce(requestCall);
      const expectedCallData = { body: 'device=undefined&orientation=PORTRAIT' };
      sinon.assert.calledWith(requestCall, sinon.match(expectedCallData));
      requestCall.restore();
      sendResponse.restore();
      clock.restore();
    });

    it('should set longPageLoadTimeout if requestStateObj.hash is GET:screenshot or POST:url and attempt is less than 2', async () => {
      const clock = sinon.useFakeTimers();
      const sendResponse = sinon.stub(bridge, 'sendResponse');
      const checkActiveWindowOnTerminalStub = sinon.stub(helper, 'checkActiveWindowOnTerminal');
      const loggerStub = sinon.stub(HubLogger, 'miscLogger');

      const originalActiveWindowLongPageLoadTime = constants.activeWindowLongPageLoadTime;
      const originalActiveWindowCheckBrowsers = constants.activeWindowCheckBrowsers;

      constants.activeWindowLongPageLoadTime = 2000;
      constants.activeWindowCheckBrowsers = ['chrome', 'firefox'];

      const sessionKeyObj = { browser: 'chrome', rails_session_id: '1234', debugSession: true };

      const androidOrientation = new AndroidOrientationHandler(sessionKeyObj, { method: 'POST' }, {});

      const requestStateObj = { hash: 'POST:url' };
      const timeout = await androidOrientation.processCommand('{}', requestStateObj, 0);

      clock.tick(constants.activeWindowLongPageLoadTime);

      sinon.assert.calledOnce(checkActiveWindowOnTerminalStub);
      sinon.assert.calledWith(checkActiveWindowOnTerminalStub, sessionKeyObj);

      constants.activeWindowLongPageLoadTime = originalActiveWindowLongPageLoadTime;
      constants.activeWindowCheckBrowsers = originalActiveWindowCheckBrowsers;

      checkActiveWindowOnTerminalStub.restore();
      loggerStub.restore();
      clock.restore();
      sendResponse.restore();
    });

    it('should not set longPageLoadTimeout if attempt is 2 or greater', async () => {
      const clock = sinon.useFakeTimers();
      const sendResponse = sinon.stub(bridge, 'sendResponse');
      const checkActiveWindowOnTerminalStub = sinon.stub(helper, 'checkActiveWindowOnTerminal');
      const loggerStub = sinon.stub(HubLogger, 'miscLogger');

      const originalActiveWindowLongPageLoadTime = constants.activeWindowLongPageLoadTime;
      const originalActiveWindowCheckBrowsers = constants.activeWindowCheckBrowsers;

      constants.activeWindowLongPageLoadTime = 2000;
      constants.activeWindowCheckBrowsers = ['chrome', 'firefox'];

      const sessionKeyObj = { browser: 'chrome', rails_session_id: '1234', debugSession: true };

      const androidOrientation = new AndroidOrientationHandler(sessionKeyObj, { method: 'POST' }, {});

      const requestStateObj = { hash: 'POST:url' };
      const timeout = await androidOrientation.processCommand('{}', requestStateObj, 2);

      clock.tick(constants.activeWindowLongPageLoadTime);

      sinon.assert.notCalled(checkActiveWindowOnTerminalStub);

      constants.activeWindowLongPageLoadTime = originalActiveWindowLongPageLoadTime;
      constants.activeWindowCheckBrowsers = originalActiveWindowCheckBrowsers;

      checkActiveWindowOnTerminalStub.restore();
      loggerStub.restore();
      clock.restore();
      sendResponse.restore();
    });
  });
});
