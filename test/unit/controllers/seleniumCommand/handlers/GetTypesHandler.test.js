'use strict';

const bridge = require('../../../../../bridge');
const sinon = require('sinon');
const { GetTypesHandler } = require('../../../../../controllers/seleniumCommand/handlers/GetTypesHandler');
const SeleniumClient = require('../../../../../seleniumClient');


describe('GetTypes', () => {
  let getTypesHandler;
  let sessionKeyObj;
  let request;
  let response;
  beforeEach(() => {
    sessionKeyObj = {};
    request = {};
    response = {};
    getTypesHandler = new GetTypesHandler(sessionKeyObj, request, response);
    sinon.stub(bridge, 'sendResponse');
    sinon.stub(SeleniumClient.prototype, 'getTypes').returns({ value: ['browser', 'server'] });
    sinon.stub(SeleniumClient.prototype, 'getLogTypesMobile').returns({ value: ['browser', 'server', 'perf'] });
  });
  afterEach(() => {
    bridge.sendResponse.restore();
    SeleniumClient.prototype.getTypes.restore();
    SeleniumClient.prototype.getLogTypesMobile.restore();
  });

  describe('.processCommand', () => {
    it('should send static response for android', async () => {
      sessionKeyObj.os = 'android';
      await getTypesHandler.processCommand({});
      sinon.assert.calledWith(bridge.sendResponse, { os: 'android' }, { data: '{"status":0,"value":["browser","driver"]}' });
    });

    it('should send static response for firefox or safari', async () => {
      sessionKeyObj.browser = 'safari';
      await getTypesHandler.processCommand({});
      sinon.assert.calledWith(bridge.sendResponse, { browser: 'safari' }, { data: '{"status":0,"value":["server"]}' });
    });

    it('should make request to /se/log/types for non android', async () => {
      sessionKeyObj.os = 'windows';
      await getTypesHandler.processCommand({});
      sinon.assert.calledWith(bridge.sendResponse, { os: 'windows' }, { data: '{"status":0,"value":["browser","server"]}' });
    });

    it('should return status 13 if error', async () => {
      SeleniumClient.prototype.getTypes.restore();
      sinon.stub(SeleniumClient.prototype, 'getTypes').throws('error');
      await getTypesHandler.processCommand({});
      sinon.assert.calledWith(bridge.sendResponse, {}, { data: '{"status":13,"value":null}' });
    });
  });

  describe('.processCommand for SE Log type', () => {
    it('should send static response for android', async () => {
      sessionKeyObj.os = 'android';
      await getTypesHandler.processCommand({}, true);
      sinon.assert.calledWith(bridge.sendResponse, { os: 'android' }, { data: '{"status":0,"value":["browser","driver"]}' });
    });

    it('should make request to /log/types for non android', async () => {
      sessionKeyObj.os = 'ios';
      await getTypesHandler.processCommand({}, true);
      sinon.assert.calledWith(bridge.sendResponse, { os: 'ios' }, { data: '{"status":0,"value":["browser","server","perf"]}' });
    });

    it('should return status 13 if error', async () => {
      SeleniumClient.prototype.getLogTypesMobile.restore();
      sinon.stub(SeleniumClient.prototype, 'getLogTypesMobile').throws('error');
      await getTypesHandler.processCommand({}, true);
      sinon.assert.calledWith(bridge.sendResponse, {}, { data: '{"status":13,"value":null}' });
    });
  });
});
