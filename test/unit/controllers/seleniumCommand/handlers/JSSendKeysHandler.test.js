/* eslint-disable max-lines-per-function */

'use strict';

const { J<PERSON>end<PERSON>eysHandler } = require('../../../../../controllers/seleniumCommand/handlers/JSSendKeysHandler');
const helper = require('../../../../../helper');
const bridge = require('../../../../../bridge');
const pubSub = require('../../../../../pubSub');
const constants = require('../../../../../constants');
const testHelper = require('../../../helper');
const terminal = require('../../../../../services/terminal');
const sinon = require('sinon');
const { expect, assert } = require('chai');

describe('JSSendKeysHandler tests', () => {
  const sessionKeyObj = testHelper.getKeyObject();
  sessionKeyObj.lastResponseTime = Math.random() * 100;
  sessionKeyObj.lastRequestTime = Math.random() * 100;
  sessionKeyObj.outsideBrowserstackTime = Math.random() * 100;
  sessionKeyObj.userHubLatency = Math.random() * 100;
  sessionKeyObj.seleniumRequestsCount = Math.random() * 100;

  const requestStateObj = { request: { log_data: '' } };
  const options = {
    path: '/session/dummysessionid/element/dummy-element-id/value',
    headers: { host: 'dummyhost.com' },
    port: 1234,
  };
  const response = { data: 'some_data' };
  const urlParts = ['', 'session', 'dummysessionid', 'element', 'dummy-element-id', 'value'];
  const elementId = 'dummy-element-id';
  const attributeUrlParts = ['', 'session', 'dummysessionid', 'element', 'dummy-element-id', 'attribute/type'];

  describe('#extractText', () => {
    const extractText = JSSendKeysHandler.extractText;
    it('it should return text data when value key is of type string', () => {
      const result = extractText({ value: 'browserstack' });
      expect(result).to.be.a('string');
      expect(result).to.be.equal('browserstack');
    });

    it('it should return text data when value key is of type array', () => {
      const result = extractText({ value: 'browserstack'.split('') });
      expect(result).to.be.a('string');
      expect(result).to.be.equal('browserstack');
    });

    it('it should return string data when text key is of type string', () => {
      const result = extractText({ text: 'browserstack' });
      expect(result).to.be.a('string');
      expect(result).to.be.equal('browserstack');
    });

    it('it should return null when text key is of type array', () => {
      const result = extractText({ text: 'browserstack'.split('') });
      expect(result).to.be.a('null');
    });

    it('it should return null when non null object with random keys', () => {
      const result = extractText({ someKey: 'browserstack' });
      expect(result).to.be.a('null');
    });

    it('it should return null when non array/string value key is provided', () => {
      const result = extractText({ value: true });
      expect(result).to.be.a('null');
    });
  });

  describe('#notFileType', () => {
    const notFileType = JSSendKeysHandler.notFileType;
    it('returns true when status code is 200 and type is not file', () => {
      const attributeTypeData = { value: 'val' };
      const getAttributeTypeResponse = { statusCode: 200 };

      assert.equal(notFileType(attributeTypeData, getAttributeTypeResponse), true);
    });

    it('returns false when status code is not 200', () => {
      const attributeTypeData = { value: 'val' };
      const getAttributeTypeResponse = { statusCode: 500 };

      assert.equal(notFileType(attributeTypeData, getAttributeTypeResponse), false);
    });

    it('returns null when attributeTypeData is null', () => {
      const attributeTypeData = null;
      const getAttributeTypeResponse = { statusCode: 200 };

      assert.equal(notFileType(attributeTypeData, getAttributeTypeResponse), null);
    });

    it('returns false when attributeTypeData has value file', () => {
      const attributeTypeData = { value: 'FiLe' };
      const getAttributeTypeResponse = { statusCode: 200 };

      assert.equal(notFileType(attributeTypeData, getAttributeTypeResponse), false);
    });
  });

  describe('extractSendData', () => {
    describe('returns false when', () => {
      it('logDataObj is not JSON parsable', () => {
        const request = {};
        request.log_data = null;
        const handler = new JSSendKeysHandler(sessionKeyObj, request, response);
        const res = handler.extractSendData();
        assert.equal(res, false);
      });

      it('logDataObj does not have text or value field as string or array', () => {
        const request = {};
        request.log_data = JSON.stringify({ text: null, value: null });
        const handler = new JSSendKeysHandler(sessionKeyObj, request, response);
        const res = handler.extractSendData();
        assert.equal(res, false);
      });

      it('special character like key up is passed', () => {
        const request = {};
        request.log_data = JSON.stringify({ text: String.fromCharCode(0xF8FF) });
        const handler = new JSSendKeysHandler(sessionKeyObj, request, response);
        const res = handler.extractSendData();
        assert.equal(res, false);

        const request2 = {};
        request.log_data = JSON.stringify({ text: String.fromCharCode(0xE000) });
        const handler2 = new JSSendKeysHandler(sessionKeyObj, request2, response);
        const res2 = handler2.extractSendData();
        assert.equal(res2, false);
      });
    });

    it('parses and sets sendData and returns true', () => {
      const request = {};
      request.log_data = JSON.stringify({ text: 'some text !@#$%^&*()_+' });
      const handler = new JSSendKeysHandler(sessionKeyObj, request, response);
      const res = handler.extractSendData(requestStateObj);
      assert.equal(res, true);
      assert.equal(handler.sendData, 'some text !@#$%^&*()_+');
    });
  });

  describe('processCommand', async () => {
    let extractSendData;
    let getAttributeTypeAndSetValue;

    beforeEach(() => {
      getAttributeTypeAndSetValue = sinon.stub(JSSendKeysHandler.prototype, 'getAttributeTypeAndSetValue').returns(Promise.resolve('data1'));
    });

    afterEach(() => {
      getAttributeTypeAndSetValue.restore();
    });

    it('returns promise after extracting and setting data', async () => {
      extractSendData = sinon.stub(JSSendKeysHandler.prototype, 'extractSendData').returns(true);
      const request = {};
      const handler = new JSSendKeysHandler(sessionKeyObj, request, response);

      const res = await handler.processCommand(requestStateObj, options);
      expect(res).to.eql('data1');
      sinon.assert.calledOnce(extractSendData);
      sinon.assert.calledOnce(getAttributeTypeAndSetValue);
      sinon.assert.calledWith(getAttributeTypeAndSetValue, requestStateObj, options);
      extractSendData.restore();
    });

    it('returns null if data to be sent is not extracted properly', async () => {
      extractSendData = sinon.stub(JSSendKeysHandler.prototype, 'extractSendData').returns(false);
      const request = {};
      const handler = new JSSendKeysHandler(sessionKeyObj, request, response);

      const res = await handler.processCommand(requestStateObj, options);
      expect(res).to.eql(null);
      sinon.assert.calledOnce(extractSendData);
      sinon.assert.notCalled(getAttributeTypeAndSetValue);
      extractSendData.restore();
    });
  });

  describe('requestHook', async () => {
    it('updates timeout manager and sends response', () => {
      const pubSubCall = sinon.stub(pubSub, 'publish');
      const timeoutManagerUpdateTimeout = sinon.stub(helper, 'timeoutManagerUpdateTimeout');
      const sendResponse = sinon.stub(bridge, 'sendResponse');

      const request = {};
      const handler = new JSSendKeysHandler(sessionKeyObj, request, response);

      handler.requestHook(requestStateObj);
      sinon.assert.calledOnce(pubSubCall);
      sinon.assert.calledWith(pubSubCall, constants.updateKeyObject, {
        session: sessionKeyObj.rails_session_id,
        changed: {
          lastResponseTime: sessionKeyObj.lastResponseTime,
          lastRequestTime: sessionKeyObj.lastRequestTime,
          outsideBrowserstackTime: sessionKeyObj.outsideBrowserstackTime,
          userHubLatency: sessionKeyObj.userHubLatency,
          seleniumRequestsCount: sessionKeyObj.seleniumRequestsCount,
        },
      }, false);
      sinon.assert.calledOnce(timeoutManagerUpdateTimeout);
      sinon.assert.calledWith(timeoutManagerUpdateTimeout, sessionKeyObj.rails_session_id, sessionKeyObj);
      sinon.assert.calledOnce(timeoutManagerUpdateTimeout);
      sinon.assert.calledWith(timeoutManagerUpdateTimeout, sessionKeyObj.rails_session_id, sessionKeyObj);
      expect(requestStateObj.hash).to.eql('POST:value');
      expect(requestStateObj.data).to.eql(JSON.stringify({
        sessionId: sessionKeyObj.rails_session_id,
        status: 0,
        value: null,
      }));
      sinon.assert.calledOnce(sendResponse);
      sinon.assert.calledWith(sendResponse, sessionKeyObj, requestStateObj);
      pubSubCall.restore();
      timeoutManagerUpdateTimeout.restore();
      sendResponse.restore();
    });
  });

  describe('getAttributeTypeAndSetValue', async () => {
    let extractSendData;
    const notFileTypeResponse = {
      data: JSON.stringify({ value: 'not file' }),
      statusCode: 200,
    };
    const fileTypeResponse = {
      data: JSON.stringify({ value: 'file' }),
      statusCode: 200,
    };

    beforeEach(() => {
      extractSendData = sinon.stub(JSSendKeysHandler.prototype, 'extractSendData');
    });

    afterEach(() => {
      extractSendData.restore();
    });

    it('gets attribute type and calls sendKeysToElement on terminal if not file type, returns promise', async () => {
      const getAttributeType = sinon.stub(terminal, 'getAttributeType').returns(Promise.resolve(notFileTypeResponse));
      const sendKeysToElement = sinon.stub(terminal, 'sendKeysToElement').returns(Promise.resolve('successful'));

      const request = {};
      const handler = new JSSendKeysHandler(sessionKeyObj, request, response);

      const res = await handler.getAttributeTypeAndSetValue(requestStateObj, options);
      sinon.assert.calledOnce(getAttributeType);
      sinon.assert.calledWith(getAttributeType, requestStateObj, options, attributeUrlParts);
      sinon.assert.calledOnce(sendKeysToElement);
      sinon.assert.calledWith(sendKeysToElement, sessionKeyObj, requestStateObj, options, urlParts, handler.sendData, elementId, sinon.match.any, false);
      expect(res).to.eql('successful');
      getAttributeType.restore();
      sendKeysToElement.restore();
    });

    it('calls sendKeysToElement with default options=true if getAttributeType fails', async () => {
      const getAttributeType = sinon.stub(terminal, 'getAttributeType').returns(Promise.reject(new Error('some error')));
      const sendKeysToElement = sinon.stub(terminal, 'sendKeysToElement').returns(Promise.resolve('successful'));

      const request = {};
      const handler = new JSSendKeysHandler(sessionKeyObj, request, response);

      const res = await handler.getAttributeTypeAndSetValue(requestStateObj, options);
      sinon.assert.calledOnce(getAttributeType);
      sinon.assert.calledWith(getAttributeType, requestStateObj, options, attributeUrlParts);
      sinon.assert.calledOnce(sendKeysToElement);
      sinon.assert.calledWith(sendKeysToElement, sessionKeyObj, requestStateObj, options, urlParts, handler.sendData, elementId, sinon.match.any, true);
      expect(res).to.eql('successful');
      getAttributeType.restore();
      sendKeysToElement.restore();
    });

    it('calls sendKeysToElement with default options=true if attribute type is file', async () => {
      const getAttributeType = sinon.stub(terminal, 'getAttributeType').returns(Promise.resolve(fileTypeResponse));
      const sendKeysToElement = sinon.stub(terminal, 'sendKeysToElement').returns(Promise.resolve('successful'));

      const request = {};
      const handler = new JSSendKeysHandler(sessionKeyObj, request, response);

      const res = await handler.getAttributeTypeAndSetValue(requestStateObj, options);
      sinon.assert.calledOnce(getAttributeType);
      sinon.assert.calledWith(getAttributeType, requestStateObj, options, attributeUrlParts);
      sinon.assert.calledOnce(sendKeysToElement);
      sinon.assert.calledWith(sendKeysToElement, sessionKeyObj, requestStateObj, options, urlParts, handler.sendData, elementId, sinon.match.any, true);
      expect(res).to.eql('successful');
      getAttributeType.restore();
      sendKeysToElement.restore();
    });

    it('sends error response if sendKeysToElement fails after successful attribute type call', async () => {
      const getAttributeType = sinon.stub(terminal, 'getAttributeType').returns(Promise.resolve(notFileTypeResponse));
      const sendKeysToElement = sinon.stub(terminal, 'sendKeysToElement').returns(Promise.reject(new Error('err1')));
      const sendErrorResponse = sinon.stub(bridge, 'sendErrorResponse').returns(Promise.resolve('resp2'));

      const request = {};
      const handler = new JSSendKeysHandler(sessionKeyObj, request, response);
      const res = await handler.getAttributeTypeAndSetValue(requestStateObj, options);
      sinon.assert.calledOnce(getAttributeType);
      sinon.assert.calledWith(getAttributeType, requestStateObj, options, attributeUrlParts);
      sinon.assert.calledOnce(sendKeysToElement);
      sinon.assert.calledWith(sendKeysToElement, sessionKeyObj, requestStateObj, options, urlParts, handler.sendData, elementId, sinon.match.any, false);
      expect(res).to.eql('resp2');
      expect(requestStateObj.data).to.eql(JSON.stringify({
        sessionId: sessionKeyObj.rails_session_id,
        status: 13,
        value: {
          message: 'error occurred with sendkeys, try turning "browserstack.sendKeys" off',
        },
      }));
      sinon.assert.calledOnce(sendErrorResponse);
      sinon.assert.calledWith(sendErrorResponse, sessionKeyObj, requestStateObj);
      getAttributeType.restore();
      sendKeysToElement.restore();
      sendErrorResponse.restore();
    });
  });
});
