'use strict';

const { DesktopOrientationHandler } = require('../../../../../controllers/seleniumCommand/handlers/DesktopOrientationHandler');
const bridge = require('../../../../../bridge');
const sinon = require('sinon');
const { assert } = require('chai');

describe('DesktopOrientationHandler tests', () => {
  describe('processCommand', () => {
    it('should send LANDSCAPE in response', async () => {
      const sendResponse = sinon.stub(bridge, 'sendResponse', (sessionKeyObj, requestStateObj) => {
        assert.deepEqual(requestStateObj.data, JSON.stringify({ sessionId: 'random', status: 0, value: 'LANDSCAPE' }));
      });
      const desktopOrientationHandler = new DesktopOrientationHandler({ rails_session_id: 'random' }, {}, {});
      await desktopOrientationHandler.processCommand({});
      sinon.assert.calledOnce(sendResponse);
      sendResponse.restore();
    });
  });
});
