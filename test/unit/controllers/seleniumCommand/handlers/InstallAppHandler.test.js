'use strict';

const { InstallAppHandler } = require('../../../../../controllers/seleniumCommand/handlers/InstallAppHandler');
const requestlib = require('../../../../../lib/request');
const bridge = require('../../../../../bridge');
const sinon = require('sinon');
const { assert } = require('chai');

describe('InstallAppHandler tests', () => {
  describe('processCommand', () => {
    it('should send status 13 in case ios and public url passed', async () => {
      const sendResponse = sinon.stub(bridge, 'sendResponse', (sessionKeyObj, requestStateObj) => {
        assert.deepEqual(requestStateObj.data, JSON.stringify({ sessionId: 'random', status: 13, value: { message: "An unknown server-side error occurred while processing the command. For using install app command for iOS on BrowserStack, please use 'otherApps' or 'midSessionInstallApps' capability and pass the app_url(of the app to be installed) inside install app command. Example: driver.install_app('bs://hashedid')." } }));
      });
      const requestCall = sinon.stub(requestlib, 'call').returns(Promise.resolve({}));
      const installAppHandler = new InstallAppHandler({ rails_session_id: 'random', realMobile: true, os: 'ios' }, {}, {});
      const res = await installAppHandler.processCommand({}, JSON.stringify({ appPath: 'random_public_path' }));
      assert.equal(res.returns, true);
      sinon.assert.notCalled(requestCall);
      sinon.assert.calledOnce(sendResponse);
      requestCall.restore();
      sendResponse.restore();
    });

    it('should send status 13 in case ios and public url passed in new format', async () => {
      const sendResponse = sinon.stub(bridge, 'sendResponse', (sessionKeyObj, requestStateObj) => {
        assert.deepEqual(requestStateObj.data, JSON.stringify({ sessionId: 'random', status: 13, value: { message: "An unknown server-side error occurred while processing the command. For using install app command for iOS on BrowserStack, please use 'otherApps' or 'midSessionInstallApps' capability and pass the app_url(of the app to be installed) inside install app command. Example: driver.install_app('bs://hashedid')." } }));
      });
      const requestCall = sinon.stub(requestlib, 'call').returns(Promise.resolve({}));
      const installAppHandler = new InstallAppHandler({ rails_session_id: 'random', realMobile: true, os: 'ios' }, {}, {});
      const res = await installAppHandler.processCommand({}, JSON.stringify({ script: 'mobile: installApp', args: [{ appPath: 'random_public_path' }] }));
      assert.equal(res.returns, true);
      sinon.assert.notCalled(requestCall);
      sinon.assert.calledOnce(sendResponse);
      requestCall.restore();
      sendResponse.restore();
    });

    it('should send status 13 in case ios and app URL passed as non String', async () => {
      const sendResponse = sinon.stub(bridge, 'sendResponse', (sessionKeyObj, requestStateObj) => {
        assert.deepEqual(requestStateObj.data, JSON.stringify({ sessionId: 'random', status: 13, value: { message: "An unknown server-side error occurred while processing the command. For using install app command for iOS on BrowserStack, please use 'otherApps' or 'midSessionInstallApps' capability and pass the app_url(of the app to be installed) inside install app command. Example: driver.install_app('bs://hashedid')." } }));
      });
      const requestCall = sinon.stub(requestlib, 'call').returns(Promise.resolve({}));
      const installAppHandler = new InstallAppHandler({ rails_session_id: 'random', realMobile: true, os: 'ios' }, {}, {});
      const res = await installAppHandler.processCommand({}, JSON.stringify({ appPath: ['bs://valid_app_url'] }));
      assert.equal(res.returns, true);
      sinon.assert.notCalled(requestCall);
      sinon.assert.calledOnce(sendResponse);
      requestCall.restore();
      sendResponse.restore();
    });

    it('should send status 13 in case ios and app URL passed as non String in new format', async () => {
      const sendResponse = sinon.stub(bridge, 'sendResponse', (sessionKeyObj, requestStateObj) => {
        assert.deepEqual(requestStateObj.data, JSON.stringify({ sessionId: 'random', status: 13, value: { message: "An unknown server-side error occurred while processing the command. For using install app command for iOS on BrowserStack, please use 'otherApps' or 'midSessionInstallApps' capability and pass the app_url(of the app to be installed) inside install app command. Example: driver.install_app('bs://hashedid')." } }));
      });
      const requestCall = sinon.stub(requestlib, 'call').returns(Promise.resolve({}));
      const installAppHandler = new InstallAppHandler({ rails_session_id: 'random', realMobile: true, os: 'ios' }, {}, {});
      const res = await installAppHandler.processCommand({}, JSON.stringify({ script: 'mobile: installApp', args: [{ appPath: ['bs://valid_app_url'] }] }));
      assert.equal(res.returns, true);
      sinon.assert.notCalled(requestCall);
      sinon.assert.calledOnce(sendResponse);
      requestCall.restore();
      sendResponse.restore();
    });

    it('should send status 0 in case appPath a bs hashedid and install success', async () => {
      const sendResponse = sinon.stub(bridge, 'sendResponse', (sessionKeyObj, requestStateObj) => {
        assert.deepEqual(requestStateObj.data, JSON.stringify({ sessionId: 'random', status: 0, value: 'install success' }));
      });
      const requestCall = sinon.stub(requestlib, 'call').returns(Promise.resolve({ statusCode: 200, data: 'install success' }));
      const installAppHandler = new InstallAppHandler({ rails_session_id: 'random', realMobile: true, os: 'ios' }, {}, {});
      const res = await installAppHandler.processCommand({}, JSON.stringify({ appPath: 'bs://random_bs_hash' }));
      assert.equal(res.returns, true);
      sinon.assert.calledOnce(requestCall);
      sinon.assert.calledOnce(sendResponse);
      requestCall.restore();
      sendResponse.restore();
    });

    it('should send status 0 in case appPath a bs hashedid and install success with new format', async () => {
      const sendResponse = sinon.stub(bridge, 'sendResponse', (sessionKeyObj, requestStateObj) => {
        assert.deepEqual(requestStateObj.data, JSON.stringify({ sessionId: 'random', status: 0, value: 'install success' }));
      });
      const requestCall = sinon.stub(requestlib, 'call').returns(Promise.resolve({ statusCode: 200, data: 'install success' }));
      const installAppHandler = new InstallAppHandler({ rails_session_id: 'random', realMobile: true, os: 'ios' }, {}, {});
      const res = await installAppHandler.processCommand({}, JSON.stringify({ script: 'mobile: installApp', args: [{ appPath: 'bs://random_bs_hash' }] }));
      assert.equal(res.returns, true);
      sinon.assert.calledOnce(requestCall);
      sinon.assert.calledOnce(sendResponse);
      requestCall.restore();
      sendResponse.restore();
    });

    it('should send status 13 in case appPath a bs hashedid and install failed', async () => {
      const sendResponse = sinon.stub(bridge, 'sendResponse', (sessionKeyObj, requestStateObj) => {
        assert.deepEqual(requestStateObj.data, JSON.stringify({ sessionId: 'random', status: 13, value: { message: 'install failed' } }));
      });
      const requestCall = sinon.stub(requestlib, 'call').returns(Promise.resolve({ statusCode: 500, data: 'install failed' }));
      const installAppHandler = new InstallAppHandler({ rails_session_id: 'random', realMobile: true, os: 'ios' }, {}, {});
      const res = await installAppHandler.processCommand({}, JSON.stringify({ appPath: 'bs://random_bs_hash' }));
      assert.equal(res.returns, true);
      sinon.assert.calledOnce(requestCall);
      sinon.assert.calledOnce(sendResponse);
      requestCall.restore();
      sendResponse.restore();
    });

    it('should send status 13 in case appPath a bs hashedid and install failed with new format', async () => {
      const sendResponse = sinon.stub(bridge, 'sendResponse', (sessionKeyObj, requestStateObj) => {
        assert.deepEqual(requestStateObj.data, JSON.stringify({ sessionId: 'random', status: 13, value: { message: 'install failed' } }));
      });
      const requestCall = sinon.stub(requestlib, 'call').returns(Promise.resolve({ statusCode: 500, data: 'install failed' }));
      const installAppHandler = new InstallAppHandler({ rails_session_id: 'random', realMobile: true, os: 'ios' }, {}, {});
      const res = await installAppHandler.processCommand({}, JSON.stringify({ script: 'mobile: installApp', args: [{ appPath: 'bs://random_bs_hash' }] }));
      assert.equal(res.returns, true);
      sinon.assert.calledOnce(requestCall);
      sinon.assert.calledOnce(sendResponse);
      requestCall.restore();
      sendResponse.restore();
    });

    it('should send status 13 in case appPath a bs hashedid and install request error', async () => {
      const sendResponse = sinon.stub(bridge, 'sendResponse', (sessionKeyObj, requestStateObj) => {
        assert.deepEqual(requestStateObj.data, JSON.stringify({ sessionId: 'random', status: 13, value: null }));
      });
      const requestCall = sinon.stub(requestlib, 'call').returns(Promise.reject(new Error('error')));
      const installAppHandler = new InstallAppHandler({ rails_session_id: 'random', realMobile: true, os: 'ios' }, {}, {});
      const res = await installAppHandler.processCommand({}, JSON.stringify({ appPath: 'bs://random_bs_hash' }));
      assert.equal(res.returns, true);
      sinon.assert.calledOnce(requestCall);
      sinon.assert.calledOnce(sendResponse);
      requestCall.restore();
      sendResponse.restore();
    });

    it('should send status 13 in case appPath a bs hashedid and install request error with new format', async () => {
      const sendResponse = sinon.stub(bridge, 'sendResponse', (sessionKeyObj, requestStateObj) => {
        assert.deepEqual(requestStateObj.data, JSON.stringify({ sessionId: 'random', status: 13, value: null }));
      });
      const requestCall = sinon.stub(requestlib, 'call').returns(Promise.reject(new Error('error')));
      const installAppHandler = new InstallAppHandler({ rails_session_id: 'random', realMobile: true, os: 'ios' }, {}, {});
      const res = await installAppHandler.processCommand({}, JSON.stringify({ script: 'mobile: installApp', args: [{ appPath: 'bs://random_bs_hash' }] }));
      assert.equal(res.returns, true);
      sinon.assert.calledOnce(requestCall);
      sinon.assert.calledOnce(sendResponse);
      requestCall.restore();
      sendResponse.restore();
    });

    it('should continue in case andorid with public url', async () => {
      const sendResponse = sinon.stub(bridge, 'sendResponse');
      const requestCall = sinon.stub(requestlib, 'call').returns(Promise.resolve({}));
      const installAppHandler = new InstallAppHandler({ rails_session_id: 'random', realMobile: true, os: 'andorid' }, {}, {});
      const res = await installAppHandler.processCommand({}, JSON.stringify({ appPath: 'random_public_path' }));
      assert.equal(res.returns, false);
      sinon.assert.notCalled(requestCall);
      sinon.assert.notCalled(sendResponse);
      requestCall.restore();
      sendResponse.restore();
    });

    it('should continue in case andorid with public url with new format', async () => {
      const sendResponse = sinon.stub(bridge, 'sendResponse');
      const requestCall = sinon.stub(requestlib, 'call').returns(Promise.resolve({}));
      const installAppHandler = new InstallAppHandler({ rails_session_id: 'random', realMobile: true, os: 'andorid' }, {}, {});
      const res = await installAppHandler.processCommand({}, JSON.stringify({ script: 'mobile: installApp', args: [{ appPath: 'random_public_path' }] }));
      assert.equal(res.returns, false);
      sinon.assert.notCalled(requestCall);
      sinon.assert.notCalled(sendResponse);
      requestCall.restore();
      sendResponse.restore();
    });

    it('should return false in case of incorrect new format', async () => {
      const sendResponse = sinon.stub(bridge, 'sendResponse');
      const requestCall = sinon.stub(requestlib, 'call').returns(Promise.resolve({}));
      const installAppHandler = new InstallAppHandler({ rails_session_id: 'random', realMobile: true, os: 'andorid' }, {}, {});
      const res = await installAppHandler.processCommand({}, JSON.stringify({ script: 'mobile: installApp', args: [{ appPathy: 'random_public_path' }] }));
      assert.equal(res.returns, false);
      sinon.assert.notCalled(requestCall);
      sinon.assert.notCalled(sendResponse);
      requestCall.restore();
      sendResponse.restore();
    });
  });
});
