'use strict';

const { IOSLocationHandler } = require('../../../../../controllers/seleniumCommand/handlers/IOSLocationHandler');
const requestlib = require('../../../../../lib/request');
const bridge = require('../../../../../bridge');
const sinon = require('sinon');
const { assert } = require('chai');

describe('IOSLocationHandler tests', () => {
  describe('processCommand', () => {
    it('should send request to platform and return status 0 in case of no error', async () => {
      const sendResponse = sinon.stub(bridge, 'sendResponse', (sessionKeyObj, requestStateObj) => {
        assert.deepEqual(requestStateObj.data, JSON.stringify({ sessionId: 'random', status: 0, value: {} }));
      });
      const requestCall = sinon.stub(requestlib, 'call').returns(Promise.resolve({ statusCode: 200, data: {} }));
      const handler = new IOSLocationHandler({ rails_session_id: 'random' }, {}, {});
      await handler.processCommand({}, '{"location": {"latitude" : 45, "longitude" : 56}}');
      sinon.assert.calledOnce(requestCall);
      sinon.assert.calledOnce(sendResponse);
      requestCall.restore();
      sendResponse.restore();
    });
    it('should send request to platform and return status 0 in case of no error and idletimeout greater than 20', async () => {
      const sendResponse = sinon.stub(bridge, 'sendResponse', (sessionKeyObj, requestStateObj) => {
        assert.deepEqual(requestStateObj.data, JSON.stringify({ sessionId: 'random', status: 0, value: {} }));
      });
      const requestCall = sinon.stub(requestlib, 'call').returns(Promise.resolve({ statusCode: 200, data: {} }));
      const handler = new IOSLocationHandler({ rails_session_id: 'random', idle_timeout: 21 }, {}, {});
      await handler.processCommand({}, '{"location": {"latitude" : 45, "longitude" : 56}}');
      sinon.assert.calledOnce(requestCall);
      sinon.assert.calledOnce(sendResponse);
      requestCall.restore();
      sendResponse.restore();
    });
    it('should send status 13 in case of non 200 response form platform', async () => {
      const sendResponse = sinon.stub(bridge, 'sendResponse', (sessionKeyObj, requestStateObj) => {
        assert.deepEqual(requestStateObj.data, JSON.stringify({ sessionId: 'random', status: 13, value: { message: '[BROWSERSTACK_INTERNAL_ERROR] We couldn’t execute the ‘set location’ command successfully. Please try again.' } }));
      });
      const requestCall = sinon.stub(requestlib, 'call').returns(Promise.resolve({ statusCode: 500, data: {} }));
      const handler = new IOSLocationHandler({ rails_session_id: 'random' }, {}, {});
      await handler.processCommand({}, '{"location": {"latitude" : 45, "longitude" : 56}}');
      sinon.assert.calledOnce(requestCall);
      sinon.assert.calledOnce(sendResponse);
      requestCall.restore();
      sendResponse.restore();
    });

    it('should send status 14 in case of execption', async () => {
      const sendResponse = sinon.stub(bridge, 'sendResponse', (sessionKeyObj, requestStateObj) => {
        assert.deepEqual(requestStateObj.data, JSON.stringify({ sessionId: 'random', status: 14, value: { message: '[BROWSERSTACK_INTERNAL_ERROR] We couldn’t execute the ‘set location’ command successfully. Please try again.' } }));
      });
      const requestCall = sinon.stub(requestlib, 'call').throws(new Error('some random error'));
      const handler = new IOSLocationHandler({ rails_session_id: 'random' }, {}, {});
      await handler.processCommand({}, '{"location": {"latitude" : 45, "longitude" : 56}}');
      sinon.assert.calledOnce(requestCall);
      sinon.assert.calledOnce(sendResponse);
      requestCall.restore();
      sendResponse.restore();
    });
  });
  describe('validateRequestErrors', () => {
    it('should send appropriate error in case of invalid json in params', () => {
      const res = IOSLocationHandler.validateRequestData({});
      assert.strictEqual('Request data must contain a \'location\' key.', res[0]);
    });
    it('should send appropriate error in case of latitude of longitude missing in params', () => {
      const res = IOSLocationHandler.validateRequestData({ location: { longitude: 56 } });
      assert.strictEqual('[BROWSERSTACK_MISSING_PARAMETER] We couldn’t execute the ‘set location’ command successfully as one or more required parameters are missing. Please pass valid parameters and try again. ', res[0]);
    });
    it('should send appraopriate error in case lat or long are not numeric', () => {
      const res = IOSLocationHandler.validateRequestData({ location: { latitude: 'fds', longitude: 56 } });
      assert.strictEqual('[BROWSERSTACK_INVALID_VALUE_PASSED] Invalid value passed in ‘set location’ command. Please pass a valid value and try again.', res[0]);
    });
    it('should send appraopriate error in case long is not numeric', () => {
      const res = IOSLocationHandler.validateRequestData({ location: { latitude: 56, longitude: 'fds' } });
      assert.strictEqual('[BROWSERSTACK_INVALID_VALUE_PASSED] Invalid value passed in ‘set location’ command. Please pass a valid value and try again.', res[0]);
    });
    it('should send null if no errors', () => {
      const res = IOSLocationHandler.validateRequestData({ location: { latitude: 56, longitude: 56 } });
      assert.strictEqual(null, res);
    });
  });
});
