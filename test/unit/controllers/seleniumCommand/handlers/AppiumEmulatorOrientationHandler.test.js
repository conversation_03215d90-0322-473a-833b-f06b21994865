'use strict';

const { AppiumEmulatorOrientationHandler } = require('../../../../../controllers/seleniumCommand/handlers/AppiumEmulatorOrientationHandler');

const hub = require('../../../../../hub');
const requestlib = require('../../../../../lib/request');
const bridge = require('../../../../../bridge');
const sinon = require('sinon');

describe('AppiumEmulatorOrientationHandler tests', () => {
  describe('processCommand', () => {
    it('should retrieve orientation in case of get call', () => {
      const processResponse = sinon.stub(hub, 'processResponse');
      const emulatorOrientation = new AppiumEmulatorOrientationHandler({ clientSessionID: 'abcd' }, { method: 'GET' }, {});
      emulatorOrientation.processCommand({});
      sinon.assert.calledOnce(processResponse);
      processResponse.restore();
    });

    it('should change orientation in case of post call in case of terminal call success', async () => {
      const clock = sinon.useFakeTimers();
      const sendResponse = sinon.stub(bridge, 'sendResponse');
      const requestCall = sinon.stub(requestlib, 'call').returns(Promise.resolve());
      const emulatorOrientation = new AppiumEmulatorOrientationHandler({ clientSessionID: 'abcd' }, { method: 'POST', log_data: JSON.stringify({ orientation: 'random' }) }, {});
      await emulatorOrientation.processCommand({});
      clock.tick(1500);
      sinon.assert.calledOnce(sendResponse);
      sinon.assert.calledOnce(requestCall);
      clock.restore();
      sendResponse.restore();
      requestCall.restore();
    });

    it('should change orientation in case of post call in case of terminal call error', async () => {
      const processResponse = sinon.stub(hub, 'processResponse');
      const requestCall = sinon.stub(requestlib, 'call').returns(Promise.reject());
      const emulatorOrientation = new AppiumEmulatorOrientationHandler({ clientSessionID: 'abcd' }, { method: 'POST' }, {});
      await emulatorOrientation.processCommand({});
      sinon.assert.calledOnce(processResponse);
      sinon.assert.calledOnce(requestCall);
      processResponse.restore();
      requestCall.restore();
    });
  });
});
