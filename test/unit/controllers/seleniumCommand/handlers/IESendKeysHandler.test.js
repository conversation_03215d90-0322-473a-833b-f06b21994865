'use strict';

const { IESend<PERSON>eysHandler } = require('../../../../../controllers/seleniumCommand/handlers/IESendKeysHandler');
const testHelper = require('../../../helper');
const HubLogger = require('../../../../../log');
const sinon = require('sinon');
const terminal = require('../../../../../services/terminal');
const bridge = require('../../../../../bridge');
const helper = require('../../../../../helper');
const pubSub = require('../../../../../pubSub');
const { assert } = require('chai');

describe('IESendKeysHandler tests', () => {
  let getAutoitText;
  let sendResponse;
  const sessionKeyObject = testHelper.getKeyObject();
  const host = 'http://some_host';
  const key = 'abc123';
  const queryParams = `?sessionId=${key}`;
  const request = {
    url: host + queryParams,
  };
  const response = { data: 'some_data' };
  const options = { opt: 'some_val' };

  describe('sendErrorResponse', () => {
    let sendErrorResponse;
    beforeEach(() => {
      sendErrorResponse = sinon.stub(bridge, 'sendErrorResponse').returns('val');
    });

    afterEach(() => {
      sendErrorResponse.restore();
    });

    it('calls bridge.sendErrorResponse', async () => {
      const requestStateObj = { key: 'value1', hash: 'POST:ie-sendkeys' };
      const handler = new IESendKeysHandler(sessionKeyObject, request, response, options);
      const res = handler.sendErrorResponse(requestStateObj);
      sinon.assert.match(res, 'val');
      sinon.assert.calledOnce(sendErrorResponse);
      sinon.assert.calledWith(sendErrorResponse, sessionKeyObject, requestStateObj);
    });
  });

  describe('sendKeys', () => {
    const logDataObj = { value: 'val' };

    beforeEach(() => {
      getAutoitText = sinon.stub(helper, 'getAutoitText').returns('some text value');
      sendResponse = sinon.stub(bridge, 'sendResponse');
    });

    afterEach(() => {
      getAutoitText.restore();
      sendResponse.restore();
    });
    it('calls terminal send keys ', async () => {
      const autoItSendKeys = sinon.stub(terminal, 'autoItSendKeys').returns(Promise.resolve(true));
      const requestStateObj = { key: 'value1', hash: 'POST:ie-sendkeys' };
      const handler = new IESendKeysHandler(sessionKeyObject, request, response, options);
      handler.logDataObj = logDataObj;
      await handler.sendKeys(requestStateObj);
      sinon.assert.calledOnce(getAutoitText);
      sinon.assert.calledOnce(autoItSendKeys);
      sinon.assert.calledWith(getAutoitText, sessionKeyObject, 'val', pubSub);
      sinon.assert.calledWith(autoItSendKeys, sessionKeyObject, requestStateObj, options, 'some text value');
      autoItSendKeys.restore();
    });
  });

  describe('processCommand', () => {
    // eslint-disable-next-line camelcase
    const log_data = JSON.stringify({ value: 'val' });
    const logDataObj = { value: 'val' };

    beforeEach(() => {
      getAutoitText = sinon.stub(helper, 'getAutoitText').returns('some text value');
      sendResponse = sinon.stub(bridge, 'sendResponse');
    });

    afterEach(() => {
      getAutoitText.restore();
      sendResponse.restore();
    });
    it('returns true and calls terminal click and then sendKeys if request has log_data', async () => {
      const sendKeys = sinon.stub(IESendKeysHandler.prototype, 'sendKeys');
      const sendErrorResponse = sinon.stub(IESendKeysHandler.prototype, 'sendErrorResponse');
      const ieClick = sinon.stub(bridge, 'ieClick').yields();
      const requestStateObj = { key: 'value1', hash: 'POST:ie-sendkeys', request: { log_data } };

      const handler = new IESendKeysHandler(sessionKeyObject, request, response, options);
      const res = handler.processCommand(requestStateObj);
      assert.equal(res, true);
      sinon.assert.calledOnce(ieClick);
      sinon.assert.calledOnce(sendKeys);
      sinon.assert.calledWith(ieClick, sessionKeyObject, requestStateObj, options, logDataObj, sinon.match.any, sinon.match.any);
      sinon.assert.calledWith(sendKeys, requestStateObj);
      sinon.assert.notCalled(sendErrorResponse);
      ieClick.restore();
      sendKeys.restore();
      sendErrorResponse.restore();
    });

    it('returns false and does not call terminal if request does not have value in log_data', async () => {
      const sendKeys = sinon.stub(IESendKeysHandler.prototype, 'sendKeys');
      const ieClick = sinon.stub(bridge, 'ieClick').yields();
      const requestStateObj = { key: 'value1', hash: 'POST:ie-sendkeys', request: {} };

      const handler = new IESendKeysHandler(sessionKeyObject, request, response, options);
      const res = handler.processCommand(requestStateObj);
      assert.equal(res, false);
      sinon.assert.notCalled(ieClick);
      sinon.assert.notCalled(sendKeys);
      ieClick.restore();
      sendKeys.restore();
    });

    it('handles terminal click error and calls sendErrorResponse', async () => {
      const sendKeys = sinon.stub(IESendKeysHandler.prototype, 'sendKeys');
      const sendErrorResponse = sinon.stub(IESendKeysHandler.prototype, 'sendErrorResponse');
      const ieClick = sinon.stub(bridge, 'ieClick').yieldsRight();
      const requestStateObj = { key: 'value1', hash: 'POST:ie-sendkeys', request: { log_data } };

      const handler = new IESendKeysHandler(sessionKeyObject, request, response, options);
      handler.processCommand(requestStateObj);
      sinon.assert.calledOnce(ieClick);
      sinon.assert.notCalled(sendKeys);
      sinon.assert.calledWith(ieClick, sessionKeyObject, requestStateObj, options, logDataObj, sinon.match.any, sinon.match.any);
      sinon.assert.called(sendErrorResponse);
      ieClick.restore();
      sendKeys.restore();
      sendErrorResponse.restore();
    });

    it('handles unparsable req_data and logs exception', async () => {
      const sendKeys = sinon.stub(IESendKeysHandler.prototype, 'sendKeys');
      const sendErrorResponse = sinon.stub(IESendKeysHandler.prototype, 'sendErrorResponse');
      const ieClick = sinon.stub(bridge, 'ieClick').yieldsRight();
      const exceptionLogger = sinon.stub(HubLogger, 'exceptionLogger');
      const requestStateObj = { key: 'value1', hash: 'POST:ie-sendkeys', request: { log_data: null } };

      const handler = new IESendKeysHandler(sessionKeyObject, request, response, options);
      handler.processCommand(requestStateObj);
      sinon.assert.notCalled(ieClick);
      sinon.assert.notCalled(sendKeys);
      sinon.assert.notCalled(sendErrorResponse);
      sinon.assert.called(exceptionLogger);
      ieClick.restore();
      sendKeys.restore();
      sendErrorResponse.restore();
      exceptionLogger.restore();
    });
  });
});
