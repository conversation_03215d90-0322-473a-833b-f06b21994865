'use strict';

const { FirefoxConsoleLogs } = require('../../../../../controllers/seleniumCommand/handlers/FirefoxConsoleLogsHandler');
const { AdditionalSessionLogs } = require('../../../../../services/terminal/stopSessionInterface/AdditionalSessionLogs');
const hub = require('../../../../../hub');
const sinon = require('sinon');

describe('FirefoxConsoleLogs tests', () => {
  describe('processCommand', () => {
    it('should retrieve console logs and send response', async () => {
      const retrieveConsoleLogs = sinon.stub(AdditionalSessionLogs.prototype, 'retrieveConsoleLogs').returns(Promise.resolve());
      const processResponse = sinon.stub(hub, 'processResponse');
      const firefoxConsoleLog = new FirefoxConsoleLogs({}, {}, {});
      await firefoxConsoleLog.processCommand({});
      sinon.assert.calledOnce(retrieveConsoleLogs);
      sinon.assert.calledOnce(processResponse);
      retrieveConsoleLogs.restore();
      processResponse.restore();
    });
  });
});
