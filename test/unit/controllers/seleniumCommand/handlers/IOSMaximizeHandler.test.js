'use strict';

const { IOSMaximizeHandler } = require('../../../../../controllers/seleniumCommand/handlers/IOSMaximizeHandler');
const testHelper = require('../../../helper');
const sinon = require('sinon');
const selenium = require('../../../../../services/selenium');
const { assert } = require('chai');
const hub = require('../../../../../hub');

describe('IOSMaximizeHandler tests', () => {
  const sessionKeyObj = testHelper.getKeyObject();
  const host = 'http://some_host';
  const key = 'abc123';
  const queryParams = `?sessionId=${key}`;
  const request = {
    url: host + queryParams,
  };
  const response = { data: 'some_data' };

  describe('processCommand', () => {
    let iOSMaximize;
    let processResponse;

    beforeEach(() => {
      iOSMaximize = sinon.stub(selenium, 'iOSMaximize').returns({ status: 0 });
      processResponse = sinon.stub(hub, 'processResponse');
    });

    afterEach(() => {
      iOSMaximize.restore();
      processResponse.restore();
    });

    it('should call hub.processResponse with response of terminal.iOSMaximize and return', async () => {
      const requestStateObj = {
        key: 'value1',
        rails_session_id: 'some rails_session_id',
        index_counter: 'some index_counter',
        callbacks: 'some callbacks',
        hostname: 'some hostname',
        originalUrl: 'some originalUrl',
      };
      const handler = new IOSMaximizeHandler(sessionKeyObj, request, response);
      const res = await handler.processCommand(requestStateObj);

      assert.deepEqual(res, { data: { status: 0 } });
      sinon.assert.calledOnce(iOSMaximize);
      sinon.assert.calledOnce(processResponse);
      sinon.assert.calledWith(processResponse, request, response, sessionKeyObj, {
        data: JSON.stringify({ status: 0 }),
        remoteSessionID: sessionKeyObj.key,
        clientSessionID: sessionKeyObj.rails_session_id,
        index_counter: requestStateObj.index_counter,
        callbacks: requestStateObj.callbacks,
        hostname: requestStateObj.hostname,
        originalUrl: requestStateObj.originalUrl,
      });
    });
  });
});
