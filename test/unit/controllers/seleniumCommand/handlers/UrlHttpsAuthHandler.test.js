'use strict';

const { UrlHttpsAuthHandler } = require('../../../../../controllers/seleniumCommand/handlers/UrlHttpsAuthHandler');
const testHelper = require('../../../helper');
const HubLogger = require('../../../../../log');
const sinon = require('sinon');
const terminal = require('../../../../../services/terminal');
const hub = require('../../../../../hub');
const bridge = require('../../../../../bridge');
const { assert } = require('chai');

describe('UrlHttpsAuthHandler tests', () => {
  const sessionKeyObject = testHelper.getKeyObject();
  const host = 'http://some_host';
  const key = 'abc123';
  const queryParams = `?sessionId=${key}`;
  const request = {
    url: host + queryParams,
  };
  let cancelSafariPopup;
  let createBridgeClientAndNode;
  let sendResponse;
  const response = { data: 'some_data' };

  describe('cancelPopupAndSendResponse', () => {
    const callbacks = {};
    const attempt = 0;
    const reqDataRetry = { data: 'some_data' };
    const indexCounter = {};

    beforeEach(() => {
      createBridgeClientAndNode = sinon.stub(hub, 'createBridgeClientAndNode').returns('val');
      sendResponse = sinon.stub(bridge, 'sendResponse').returns('val1');
    });

    afterEach(() => {
      // cancelSafariPopup.restore();
      createBridgeClientAndNode.restore();
      sendResponse.restore();
    });

    it('updates requestStateObject with hash POST:url if no popup', async () => {
      const requestStateObj = { key: 'value1', hash: 'POST:url-https-auth' };

      cancelSafariPopup = sinon.stub(terminal, 'cancelSafariPopup').returns(Promise.resolve({ data: 'no popup' }));
      const handler = new UrlHttpsAuthHandler(sessionKeyObject, request, response);
      await handler.cancelPopupAndSendResponse(requestStateObj, callbacks, attempt, indexCounter, reqDataRetry);
      sinon.assert.notCalled(createBridgeClientAndNode);
      sinon.assert.calledOnce(cancelSafariPopup);
      assert.equal(requestStateObj.hash, 'POST:url');
      assert.equal(requestStateObj.safariPopupCheckTimeout, undefined);
      cancelSafariPopup.restore();
    });

    it('calls createBridgeClientAndNode with increased attempt counter if popup existed POST:url-https-auth', async () => {
      const requestStateObj = { key: 'value1', hash: 'POST:url-https-auth' };
      cancelSafariPopup = sinon.stub(terminal, 'cancelSafariPopup').returns(Promise.resolve({ data: 'abcd' }));
      const handler = new UrlHttpsAuthHandler(sessionKeyObject, request, response);
      await handler.cancelPopupAndSendResponse(requestStateObj, callbacks, attempt, indexCounter, reqDataRetry);
      sinon.assert.calledOnce(cancelSafariPopup);
      sinon.assert.calledOnce(createBridgeClientAndNode);
      sinon.assert.calledWith(createBridgeClientAndNode, sessionKeyObject, request, response, callbacks, attempt + 1, indexCounter, reqDataRetry);
      assert.equal(requestStateObj.hash, 'POST:url-https-auth');
      assert.equal(requestStateObj.safariPopupCheckTimeout, undefined);
      cancelSafariPopup.restore();
    });

    it('catches errors during cancel popup requests', async () => {
      const requestStateObj = { key: 'value1', hash: 'POST:url-https-auth' };
      // eslint-disable-next-line prefer-promise-reject-errors
      cancelSafariPopup = sinon.stub(terminal, 'cancelSafariPopup').returns(Promise.reject('some_error'));
      const miscLogger = sinon.stub(HubLogger, 'miscLogger');
      const handler = new UrlHttpsAuthHandler(sessionKeyObject, request, response);
      await handler.cancelPopupAndSendResponse(requestStateObj, callbacks, attempt, indexCounter, reqDataRetry);
      sinon.assert.calledOnce(cancelSafariPopup);
      sinon.assert.calledOnce(miscLogger);
      assert.equal(requestStateObj.safariPopupCheckTimeout, undefined);
      miscLogger.restore();
      cancelSafariPopup.restore();
    });
  });

  describe('processCommand', () => {
    const callbacks = {};
    const attempt = 0;
    const reqDataRetry = { data: 'some_data' };
    const indexCounter = {};

    beforeEach(() => {
      createBridgeClientAndNode = sinon.stub(hub, 'createBridgeClientAndNode').returns('val');
      sendResponse = sinon.stub(bridge, 'sendResponse').returns('val1');
    });

    afterEach(() => {
      if (cancelSafariPopup) cancelSafariPopup.restore();
      createBridgeClientAndNode.restore();
      sendResponse.restore();
    });

    it('should set timeout in requestStateObject', async () => {
      const requestStateObj = { key: 'value1', hash: 'POST:url-https-auth' };
      const handler = new UrlHttpsAuthHandler(sessionKeyObject, request, response);
      handler.processCommand(requestStateObj, callbacks, attempt, indexCounter, reqDataRetry);
      assert.notEqual(requestStateObj.safariPopupCheckTimeout, undefined);
    });
  });
});
