'use strict';

const { IOSGetLocationHandler } = require('../../../../../controllers/seleniumCommand/handlers/IOSGetLocationHandler');
const HubLogger = require('../../../../../log');
const testHelper = require('../../../helper');
const sinon = require('sinon');
const selenium = require('../../../../../services/selenium');
const bridge = require('../../../../../bridge');
const loggingHelper = require('../../../../../utils/logger/loggingHelper');
const { expect, assert } = require('chai');

describe('IOSGetLocationHandler tests', () => {
  const sessionKeyObj = testHelper.getKeyObject();
  const host = 'http://some_host';
  const key = 'abc123';
  const queryParams = `?sessionId=${key}`;
  const request = {
    url: host + queryParams,
  };
  const url = host + queryParams;
  const response = { data: 'some_data' };

  describe('processCommand', () => {
    let getIOSLocation;
    let sendCombinedLogs;
    let sendErrorResponse;

    beforeEach(() => {
      getIOSLocation = sinon.stub(selenium, 'getIOSLocation').returns({ status: 13 });
      sendCombinedLogs = sinon.stub(loggingHelper, 'sendCombinedLogs');
      sendErrorResponse = sinon.stub(bridge, 'sendErrorResponse').returns('some_data');
      sinon.stub(HubLogger, 'miscLogger');
    });

    afterEach(() => {
      getIOSLocation.restore();
      sendCombinedLogs.restore();
      sendErrorResponse.restore();
      HubLogger.miscLogger.restore();
    });

    it('should call bridge.sendErrorResponse with data of getIOSLocaton result and return response in returnData key', async () => {
      const requestStateObj = { key: 'value1' };
      const handler = new IOSGetLocationHandler(sessionKeyObj, request, response);
      const res = await handler.processCommand(requestStateObj, url);
      assert.deepEqual(res, { returnData: 'some_data' });
      expect(requestStateObj.data).to.eql(JSON.stringify({ status: 13 }));

      sinon.assert.calledOnce(HubLogger.miscLogger);
      sinon.assert.calledWith(HubLogger.miscLogger, 'Blocking request', `Get Location command intercepted : ${url.toString()}. session: ${sessionKeyObj.rails_session_id}`, sinon.match.any);
      sinon.assert.calledOnce(getIOSLocation);
      sinon.assert.calledWith(getIOSLocation, sessionKeyObj);
      sinon.assert.calledOnce(sendCombinedLogs);
      sinon.assert.calledWith(sendCombinedLogs, request, sessionKeyObj, requestStateObj.data, false);
      sinon.assert.calledOnce(sendErrorResponse);
      sinon.assert.calledWith(sendErrorResponse, sessionKeyObj, requestStateObj);
    });
  });
});
