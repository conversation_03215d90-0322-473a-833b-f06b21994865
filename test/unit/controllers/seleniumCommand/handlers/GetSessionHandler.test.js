'use strict';

const { GetSessionHandler } = require('../../../../../controllers/seleniumCommand/handlers/GetSessionHandler');
const bridge = require('../../../../../bridge');
const sinon = require('sinon');

describe('GetSessionHandler tests', () => {
  describe('processCommand', () => {
    it('should send some session details', async () => {
      const sessionKeyObj = {
        rails_session_id: 'abcd',
      };
      const request = {};
      const response = {};
      const requestDataObj = {};
      const expectedResponse = {
        data: '{"sessionId":"abcd","status":0,"state":"success","value":{"capabilities":{"sessionId":"abcd"},"sessionId":"abcd"}}',
      };
      const handler = new GetSessionHandler(sessionKeyObj, request, response);
      const sendResponseStub = sinon.stub(bridge, 'sendResponse');
      handler.processCommand(requestDataObj);
      sinon.assert.calledWith(sendResponseStub, sessionKeyObj, expectedResponse);
      sendResponseStub.restore();
    });
  });
});
