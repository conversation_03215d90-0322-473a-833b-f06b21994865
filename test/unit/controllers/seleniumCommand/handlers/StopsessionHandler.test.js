'use strict';

const { StopSessionHandler } = require('../../../../../controllers/seleniumCommand/handlers/StopSessionHandler');
const { AdditionalSessionLogs } = require('../../../../../services/terminal/stopSessionInterface/AdditionalSessionLogs');
const constants = require('../../../../../constants');
const myLock = require('../../../../../semaphore');
const pubSub = require('../../../../../pubSub');
const helper = require('../../../../../helper');
const HubLogger = require('../../../../../log');
const redisClient = require('../../../../../redisUtils').redisClient;
const ha = require('../../../../../ha');
const sinon = require('sinon');
const { assert } = require('chai');


describe('StopSessionHandler tests', () => {
  describe('handle', () => {
    let request; let response;
    beforeEach(() => {
      request = {};
      response = {
        writeHead: sinon.stub(),
        end: sinon.stub(),
      };
    });

    it('return in case pendingDelete session', async () => {
      const sessionKeyObj = {
        pendingDelete: true,
      };
      const ttlSemaphore = sinon.stub(myLock, 'ttlSemaphore').returns(Promise.resolve());
      const stopSession = new StopSessionHandler(request, response, sessionKeyObj);
      await stopSession.handle();
      sinon.assert.calledOnce(ttlSemaphore);
      sinon.assert.calledOnce(response.writeHead);
      sinon.assert.calledOnce(response.end);
      ttlSemaphore.restore();
    });

    it('return in case lock session', async () => {
      const sessionKeyObj = {};
      const ttlSemaphore = sinon.stub(myLock, 'ttlSemaphore').returns(Promise.reject());
      const stopSession = new StopSessionHandler(request, response, sessionKeyObj);
      await stopSession.handle();
      sinon.assert.calledOnce(ttlSemaphore);
      sinon.assert.calledOnce(response.writeHead);
      sinon.assert.calledOnce(response.end);
      ttlSemaphore.restore();
    });

    it('should call process stop logs and delete session', async () => {
      const sessionKeyObj = {
        pendingDelete: false,
        rails_session_id: 'abcd',
      };
      const ttlSemaphore = sinon.stub(myLock, 'ttlSemaphore').returns(Promise.resolve());
      const publish = sinon.stub(pubSub, 'publish');
      const stopAndDeleteSession = sinon.stub(StopSessionHandler.prototype, 'stopAndDeleteSession').returns(Promise.resolve());
      const processStopLogs = sinon.stub(StopSessionHandler.prototype, 'processStopLogs').returns(Promise.resolve());
      const stopSession = new StopSessionHandler(request, response, sessionKeyObj);
      await stopSession.handle();
      sinon.assert.calledOnce(ttlSemaphore);
      sinon.assert.calledOnce(publish);
      sinon.assert.calledOnce(processStopLogs);
      sinon.assert.calledOnce(stopAndDeleteSession);
      ttlSemaphore.restore();
      publish.restore();
      processStopLogs.restore();
      stopAndDeleteSession.restore();
    });
  });

  describe('sessionHubCleanup', () => {
    it('should cheanup session from hub', () => {
      const request = {};
      const response = {};
      const sessionKeyObj = {
        pendingDelete: false,
        rails_session_id: 'abcd',
      };
      const addStopToRawLogs = sinon.stub(HubLogger, 'addStopToRawLogs');
      const pingDataToStats = sinon.stub(helper, 'pingDataToStats');
      const checkNonZeroStatusErrors = sinon.stub(helper, 'checkNonZeroStatusErrors');
      const checkUdpKeystoSend = sinon.stub(helper, 'checkUdpKeystoSend');
      const sessionRemovedFromRegionHook = sinon.stub(helper, 'sessionRemovedFromRegionHook');
      const stopSession = new StopSessionHandler(request, response, sessionKeyObj);
      stopSession.sessionHubCleanup();
      sinon.assert.calledOnce(addStopToRawLogs);
      sinon.assert.calledOnce(pingDataToStats);
      sinon.assert.calledOnce(checkNonZeroStatusErrors);
      sinon.assert.calledOnce(checkUdpKeystoSend);
      sinon.assert.calledOnce(sessionRemovedFromRegionHook);
      addStopToRawLogs.restore();
      pingDataToStats.restore();
      checkNonZeroStatusErrors.restore();
      checkUdpKeystoSend.restore();
      sessionRemovedFromRegionHook.restore();
    });
  });

  describe('getDeleteResponseDelayForGroup', () => {
    it('should return constants.SESSION_DELETE_RESPONSE_DELAY if should delay', async () => {
      const request = {};
      const response = {};
      const sessionKeyObj = {
        pendingDelete: false,
        rails_session_id: 'abcd',
        group_id: 0,
        session_start_time: 100,
        deviceName: 'iphoneX11',
      };
      const sismember = sinon.stub(redisClient, 'sismember').returns(true);
      const datetime = sinon.stub(Date, 'now').returns(1000);
      const stopSession = new StopSessionHandler(request, response, sessionKeyObj);
      const delayDuration = await stopSession.getDeleteResponseDelayForGroup();
      assert.equal(delayDuration, constants.SESSION_DELETE_RESPONSE_DELAY);
      sismember.restore();
      datetime.restore();
    });

    it('should return 0 if should not delay', async () => {
      const request = {};
      const response = {};
      const sessionKeyObj = {
        pendingDelete: false,
        rails_session_id: 'abcd',
        group_id: 0,
        session_start_time: 100,
        deviceName: 'iphoneXR',
      };
      const sismember = sinon.stub(redisClient, 'sismember').returns(true);
      const datetime = sinon.stub(Date, 'now').returns(1000);
      const stopSession = new StopSessionHandler(request, response, sessionKeyObj);
      const delayDuration = await stopSession.getDeleteResponseDelayForGroup();
      assert.equal(delayDuration, 0);
      sismember.restore();
      datetime.restore();
    });
  });

  describe('freeUserScript', () => {
    it('end with success response if deleteOptions not present', async () => {
      const request = {};
      const response = {
        end: sinon.stub(),
      };
      const sessionKeyObj = {
        pendingDelete: false,
        rails_session_id: 'abcd',
        group_id: 0,
        session_start_time: 100,
        deviceName: 'iphoneXR',
      };
      const stopSession = new StopSessionHandler(request, response, sessionKeyObj);
      await stopSession.freeUserScript();
      const updatedSessionKeyObj = {
        pendingDelete: false,
        rails_session_id: 'abcd',
        group_id: 0,
        session_start_time: 100,
        deviceName: 'iphoneXR',
        lastRequest: 'DELETE:abcd',
        lastResponseStatus: '200::0',
        userRequestStartTime: undefined,
      };
      assert.deepEqual(stopSession.sessionKeyObj, updatedSessionKeyObj);
      sinon.assert.calledOnce(response.end);
      sinon.assert.calledWithExactly(response.end, JSON.stringify(constants.SUCCESS_RESPONSE));
    });

    it('end with deleteOptions if present', async () => {
      const request = {};
      const response = {
        end: sinon.stub(),
      };
      const sessionKeyObj = {
        pendingDelete: false,
        rails_session_id: 'abcd',
        group_id: 0,
        session_start_time: 100,
        deviceName: 'iphoneXR',
      };
      const deleteOptions = {
        data: 'random',
      };
      const setData = sinon.stub(ha, 'setData');
      const stopSession = new StopSessionHandler(request, response, sessionKeyObj);
      await stopSession.freeUserScript(deleteOptions);
      const updatedSessionKeyObj = {
        pendingDelete: false,
        rails_session_id: 'abcd',
        group_id: 0,
        session_start_time: 100,
        deviceName: 'iphoneXR',
        lastRequest: 'DELETE:abcd',
        lastResponseStatus: '200::0',
        userRequestStartTime: undefined,
      };
      assert.deepEqual(stopSession.sessionKeyObj, updatedSessionKeyObj);
      sinon.assert.calledOnce(response.end);
      sinon.assert.calledWithExactly(response.end, deleteOptions.data.toString());
      setData.restore();
    });
  });

  describe('#enableJarDeleteForSession', () => {
    const request = {};
    const response = {};
    it('should return true if its mac', async () => {
      const sessionKeyObj = { rails_session_id: 'test-seession-id', key: 'test-key', os: 'mac' };
      assert(await new StopSessionHandler(request, response, sessionKeyObj).enableJarDeleteForSession(sessionKeyObj));
    });

    it('should return false if its playwright', async () => {
      const sessionKeyObj = {
        rails_session_id: 'test-seession-id', key: 'test-key', os: 'mac', isPlaywright: true,
      };
      assert(await new StopSessionHandler(request, response, sessionKeyObj).enableJarDeleteForSession(sessionKeyObj) === false);
    });

    it('should return true the group is enabled in redis', async () => {
      const sessionKeyObj = {
        rails_session_id: 'test-seession-id', key: 'test-key', os: 'mac', group_id: '1234',
      };
      const sismember = sinon.stub(redisClient, 'sismember').returns(true);
      assert(await new StopSessionHandler(request, response, sessionKeyObj).enableJarDeleteForSession(sessionKeyObj));
      sismember.restore();
    });

    it('should return false if the group has not enabled and the os is not mac', async () => {
      const sessionKeyObj = {
        rails_session_id: 'test-seession-id', key: 'test-key', os: 'win', group_id: '1234',
      };
      const sismember = sinon.stub(redisClient, 'sismember').returns(false);
      assert(await new StopSessionHandler(request, response, sessionKeyObj).enableJarDeleteForSession(sessionKeyObj) === false);
      sismember.restore();
    });

    it('should return false in case of redis error', async () => {
      const sessionKeyObj = {
        rails_session_id: 'test-seession-id', key: 'test-key', os: 'win', group_id: '1234',
      };
      const sismember = sinon.stub(redisClient, 'sismember').returns(Promise.reject());
      assert(await new StopSessionHandler(request, response, sessionKeyObj).enableJarDeleteForSession(sessionKeyObj) === false);
      sismember.restore();
    });
  });

  describe('stopAndDeleteSession', () => {
    it('should stop and cleanup session', async () => {
      const request = {};
      const response = {};
      const sessionKeyObj = {
        pendingDelete: false,
        rails_session_id: 'abcd',
      };
      const enableJarDeleteForSession = sinon.stub(StopSessionHandler.prototype, 'enableJarDeleteForSession').returns(Promise.resolve(true));
      const sendDeleteRequestToSeleniumJar = sinon.stub(StopSessionHandler.prototype, 'sendDeleteRequestToSeleniumJar').returns('deleteOptions');
      const sendStopToBrowserstack = sinon.stub(StopSessionHandler.prototype, 'sendStopToBrowserstack').returns(Promise.resolve());
      const freeUserScript = sinon.stub(StopSessionHandler.prototype, 'freeUserScript').returns(Promise.resolve());
      const sessionHubCleanup = sinon.stub(StopSessionHandler.prototype, 'sessionHubCleanup');
      const stopSeleniumClock = sinon.stub(helper, 'stopSeleniumClock');
      const stopSession = new StopSessionHandler(request, response, sessionKeyObj);
      await stopSession.stopAndDeleteSession();
      sinon.assert.calledOnce(sendDeleteRequestToSeleniumJar);
      enableJarDeleteForSession.restore();
      sendDeleteRequestToSeleniumJar.restore();
      sendStopToBrowserstack.restore();
      freeUserScript.restore();
      sessionHubCleanup.restore();
      stopSeleniumClock.restore();
    });

    it('should stop and cleanup session even if stop to browserstack fails', async () => {
      const request = {};
      const response = {};
      const sessionKeyObj = {
        pendingDelete: false,
        rails_session_id: 'abcd',
      };
      const enableJarDeleteForSession = sinon.stub(StopSessionHandler.prototype, 'enableJarDeleteForSession').returns(Promise.resolve(false));
      const sendDeleteRequestToSeleniumJar = sinon.stub(StopSessionHandler.prototype, 'sendDeleteRequestToSeleniumJar').returns('deleteOptions');
      const sendStopToBrowserstack = sinon.stub(StopSessionHandler.prototype, 'sendStopToBrowserstack').returns(Promise.reject());
      const freeUserScript = sinon.stub(StopSessionHandler.prototype, 'freeUserScript').returns(Promise.resolve());
      const sessionHubCleanup = sinon.stub(StopSessionHandler.prototype, 'sessionHubCleanup');
      const stopSeleniumClock = sinon.stub(helper, 'stopSeleniumClock');
      const stopSession = new StopSessionHandler(request, response, sessionKeyObj);
      await stopSession.stopAndDeleteSession();
      sinon.assert.notCalled(sendDeleteRequestToSeleniumJar);
      enableJarDeleteForSession.restore();
      sendDeleteRequestToSeleniumJar.restore();
      sendStopToBrowserstack.restore();
      freeUserScript.restore();
      sessionHubCleanup.restore();
      stopSeleniumClock.restore();
    });
  });

  describe('processStopLogs', () => {
    it('should call additional session logs service to upload logs', async () => {
      const request = {};
      const response = {};
      const sessionKeyObj = {
        pendingDelete: false,
        rails_session_id: 'abcd',
      };
      const handleStop = sinon.stub(AdditionalSessionLogs.prototype, 'handleStop').returns(Promise.resolve());
      const stopSession = new StopSessionHandler(request, response, sessionKeyObj);
      await stopSession.processStopLogs();
      sinon.assert.calledOnce(handleStop);
      handleStop.restore();
    });
  });
});
