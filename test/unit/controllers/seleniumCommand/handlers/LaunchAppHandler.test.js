'use strict';

const { LaunchAppHandler } = require('../../../../../controllers/seleniumCommand/handlers/LaunchAppHandler');
const SeleniumClient = require('../../../../../seleniumClient');
const requestlib = require('../../../../../lib/request');
const bridge = require('../../../../../bridge');
const sinon = require('sinon');
const { assert } = require('chai');

describe('LaunchAppHandler tests', () => {
  describe('processCommand', () => {
    it('should send status 0 in case no error', async () => {
      const sendResponse = sinon.stub(bridge, 'sendResponse', (sessionKeyObj, requestStateObj) => {
        assert.deepEqual(requestStateObj.data, JSON.stringify({ sessionId: 'random', status: 0, value: null }));
      });
      const requestCall = sinon.stub(requestlib, 'call').returns(Promise.resolve({}));
      const launchApp = sinon.stub(SeleniumClient.prototype, 'launchApp').returns(Promise.resolve({}));
      const launchAppHandler = new LaunchAppHandler({ rails_session_id: 'random' }, {}, {});
      await launchAppHandler.processCommand({});
      sinon.assert.calledOnce(requestCall);
      sinon.assert.calledOnce(sendResponse);
      sinon.assert.calledOnce(launchApp);
      requestCall.restore();
      sendResponse.restore();
      launchApp.restore();
    });

    it('should send status 13 in case of an error', async () => {
      const sendResponse = sinon.stub(bridge, 'sendResponse', (sessionKeyObj, requestStateObj) => {
        assert.deepEqual(requestStateObj.data, JSON.stringify({ sessionId: 'random', status: 13, value: null }));
      });
      const requestCall = sinon.stub(requestlib, 'call').returns(Promise.resolve({}));
      const launchApp = sinon.stub(SeleniumClient.prototype, 'launchApp').returns(Promise.reject(new Error('error')));
      const launchAppHandler = new LaunchAppHandler({ rails_session_id: 'random' }, {}, {});
      await launchAppHandler.processCommand({});
      sinon.assert.calledOnce(requestCall);
      sinon.assert.calledOnce(sendResponse);
      sinon.assert.calledOnce(launchApp);
      requestCall.restore();
      sendResponse.restore();
      launchApp.restore();
    });
  });
});
