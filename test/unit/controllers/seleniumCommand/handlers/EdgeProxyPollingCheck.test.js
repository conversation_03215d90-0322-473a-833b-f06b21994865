'use strict';

const { EdgeProxyPollingCheck } = require('../../../../../controllers/seleniumCommand/handlers/EdgeProxyPollingCheck');
const HubLogger = require('../../../../../log');
const sinon = require('sinon');
const requestlib = require('../../../../../lib/request');

describe('EdgeProxyPollingCheck tests', () => {
  describe('processCommand', () => {
    it('should call terminal check and not log if no error', async () => {
      const requestCall = sinon.stub(requestlib, 'call').returns(Promise.resolve({}));
      const miscLogger = sinon.stub(HubLogger, 'miscLogger');
      const edgeProxyPoll = new EdgeProxyPollingCheck({ rails_session_id: 'random' }, {}, {});
      await edgeProxyPoll.processCommand({});
      sinon.assert.calledOnce(requestCall);
      sinon.assert.notCalled(miscLogger);
      requestCall.restore();
      miscLogger.restore();
    });

    it('should call terminal check and log if error', async () => {
      const requestCall = sinon.stub(requestlib, 'call').returns(Promise.reject(new Error('random')));
      const miscLogger = sinon.stub(HubLogger, 'miscLogger');
      const edgeProxyPoll = new EdgeProxyPollingCheck({ rails_session_id: 'random' }, {}, {});
      await edgeProxyPoll.processCommand({});
      sinon.assert.calledOnce(requestCall);
      sinon.assert.calledOnce(miscLogger);
      requestCall.restore();
      miscLogger.restore();
    });
  });
});
