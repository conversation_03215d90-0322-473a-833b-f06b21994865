'use strict';

const { Sessions<PERSON>ontroller } = require('../../../../controllers/sessions/SessionsController');
const { StopByBsHandler } = require('../../../../controllers/sessions/handlers/StopByBsHandler');
const { SessionTimedoutHandler } = require('../../../../controllers/sessions/handlers/SessionTimedoutHandler');
const { GetSessionDataHandler } = require('../../../../controllers/sessions/handlers/GetSessionDataHandler');
const sinon = require('sinon');

describe('SeleniumController tests', () => {
  it('should call handle StopByBs handler', async () => {
    const handler = sinon.stub(StopByBsHandler.prototype, 'handle').returns(Promise.resolve());
    const sendResponse = sinon.stub(StopByBsHandler.prototype, 'sendResponse');
    const sessionsController = new SessionsController();
    await sessionsController.stopByBs();
    sinon.assert.calledOnce(handler);
    sinon.assert.calledOnce(sendResponse);
    handler.restore();
    sendResponse.restore();
  });

  it('should call handle SessionTimedout handler', async () => {
    const handler = sinon.stub(SessionTimedoutHandler.prototype, 'handle').returns(Promise.resolve());
    const sendResponse = sinon.stub(SessionTimedoutHandler.prototype, 'sendResponse');
    const sessionsController = new SessionsController();
    await sessionsController.sessionTimedout({ url: 'some_url' });
    sinon.assert.calledOnce(handler);
    sinon.assert.calledOnce(sendResponse);
    sendResponse.restore();
    handler.restore();
  });

  it('should call handle GetSessionDataHandler handler', async () => {
    const handler = sinon.stub(GetSessionDataHandler.prototype, 'handle').returns(Promise.resolve());
    const sendResponse = sinon.stub(GetSessionDataHandler.prototype, 'sendResponse');
    const sessionsController = new SessionsController();
    await sessionsController.getSessionData();
    sinon.assert.calledOnce(handler);
    sinon.assert.calledOnce(sendResponse);
    handler.restore();
    sendResponse.restore();
  });
});
