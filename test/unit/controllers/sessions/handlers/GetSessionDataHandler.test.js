'use strict';

const { GetSessionDataHandler } = require('../../../../../controllers/sessions/handlers/GetSessionDataHandler');
const sessionManagerHelper = require('../../../../../services/session/sessionManagerHelper');
const helper = require('../../../../../helper');
const myLock = require('../../../../../semaphore');
const { assert } = require('chai');
const sinon = require('sinon');

describe('GetSessionDataHandler tests', () => {
  describe('handle', () => {
    let request; let
      response;

    beforeEach(() => {
      request = {
        url: '/wd/hub/abcd',
      };
      response = {};
    });

    it('should not get and send data if lock present', async () => {
      const getSessionData = sinon.stub(GetSessionDataHandler.prototype, 'getSessionData').returns(Promise.resolve());
      const ttlSemaphore = sinon.stub(myLock, 'checkSemaphore').returns(Promise.resolve());
      const sessionData = new GetSessionDataHandler(request, response);
      await sessionData.handle();
      sinon.assert.calledOnce(ttlSemaphore);
      sinon.assert.notCalled(getSessionData);
      assert.equal(sessionData.responseData.data, '{"found":false}');
      getSessionData.restore();
      ttlSemaphore.restore();
    });

    it('should get and send data if lock not present', async () => {
      const getSessionData = sinon.stub(GetSessionDataHandler.prototype, 'getSessionData').returns(Promise.resolve());
      const ttlSemaphore = sinon.stub(myLock, 'checkSemaphore').returns(Promise.reject());
      const sessionData = new GetSessionDataHandler(request, response);
      await sessionData.handle();
      sinon.assert.calledOnce(ttlSemaphore);
      sinon.assert.calledOnce(getSessionData);
      sinon.assert.calledWithExactly(getSessionData, 'abcd', undefined);
      getSessionData.restore();
      ttlSemaphore.restore();
    });
  });

  describe('getSessionData', () => {
    let request; let
      response;

    beforeEach(() => {
      request = {
        url: '/wd/hub/abcd',
      };
      response = {};
    });

    it('should not get and send data if sessionKeyObj not present', async () => {
      const getKeyObject = sinon.stub(sessionManagerHelper, 'getKeyObject').returns(undefined);
      const sessionData = new GetSessionDataHandler(request, response);
      await sessionData.getSessionData();
      assert.equal(sessionData.responseData.data, '{"found":false}');
      sinon.assert.calledOnce(getKeyObject);
      getKeyObject.restore();
    });

    it('should not get and send data if pending delete', async () => {
      const getKeyObject = sinon.stub(sessionManagerHelper, 'getKeyObject').returns({ pendingDelete: true });
      const sessionData = new GetSessionDataHandler(request, response);
      await sessionData.getSessionData();
      assert.equal(sessionData.responseData.data, '{"found":false}');
      sinon.assert.calledOnce(getKeyObject);
      getKeyObject.restore();
    });

    it('should get and send data if sessionKeyObj present nad not pending delete', async () => {
      const getKeyObject = sinon.stub(sessionManagerHelper, 'getKeyObject').returns({ pendingDelete: false });
      const sessionRemovedFromRegionHook = sinon.stub(helper, 'sessionRemovedFromRegionHook');
      const sessionData = new GetSessionDataHandler(request, response);
      await sessionData.getSessionData();
      assert.equal(sessionData.responseData.data, JSON.stringify({ pendingDelete: false, found: true }));
      sinon.assert.calledOnce(getKeyObject);
      sinon.assert.calledOnce(sessionRemovedFromRegionHook);
      getKeyObject.restore();
      sessionRemovedFromRegionHook.restore();
    });
  });

  describe('sendResponse', () => {
    it('end response if statusCode and statusMessage present', () => {
      const request = {};
      const response = {
        writeHead: sinon.stub(),
        end: sinon.stub(),
      };
      const sessionData = new GetSessionDataHandler(request, response);
      sessionData.responseData.statusCode = 200;
      sessionData.responseData.headers = { 'content-type': 'application/json; charset=utf-8' };
      sessionData.responseData.data = '{"found":false}';
      sessionData.sendResponse();
      sinon.assert.calledOnce(response.writeHead);
      sinon.assert.calledOnce(response.end);
      sinon.assert.calledWithExactly(response.writeHead, 200, { 'content-type': 'application/json; charset=utf-8' });
      sinon.assert.calledWithExactly(response.end, '{"found":false}');
    });
  });
});
