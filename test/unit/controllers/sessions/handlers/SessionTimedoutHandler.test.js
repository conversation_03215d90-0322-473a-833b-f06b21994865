'use strict';

const { SessionTimedoutHandler } = require('../../../../../controllers/sessions/handlers/SessionTimedoutHandler');
const testHelper = require('../../../helper');
const helper = require('../../../../../helper');
const HubLogger = require('../../../../../log');
const myLock = require('../../../../../semaphore');
const ha = require('../../../../../ha');
const sinon = require('sinon');
const pubSub = require('../../../../../pubSub');
const selenium = require('../../../../../services/selenium');
const browserstack = require('../../../../../browserstack');

describe('SessionTimedoutHandler tests', () => {
  const host = 'http://some_host';
  const key = 'abc123';
  const queryParams = `?sessionId=${key}`;
  const request = {
    url: host + queryParams,
    message: 'some message',
    errorMessage: 'some error',
  };
  let response;

  describe('handle', () => {
    let endConflictResponse; let ttlSemaphore; let
      _miscLogger;

    beforeEach(() => {
      _miscLogger = sinon.stub(HubLogger, 'miscLogger');
      endConflictResponse = sinon.stub(helper, 'endConflictResponse');
      ttlSemaphore = sinon.stub(myLock, 'ttlSemaphore');
    });

    afterEach(() => {
      _miscLogger.restore();
      endConflictResponse.restore();
      ttlSemaphore.restore();
    });

    it('should return undefined if sessionId is not present in query params', async () => {
      const emptyQueryRequest = { url: host };
      const sessionTimedOut = new SessionTimedoutHandler(emptyQueryRequest, response);

      await sessionTimedOut.handle();
    });

    it('should catch error thrown in clearStaleSession and set responseData.statusCode to 409', async () => {
      const sessionTimedOut = new SessionTimedoutHandler(request, response);
      const clearStaleSession = sinon.stub(sessionTimedOut, 'clearStaleSession').throws('Some error');
      await sessionTimedOut.handle();

      sinon.assert.calledOnce(ttlSemaphore);
      sinon.assert.calledWith(ttlSemaphore, 'session_stop_semaphore', key);
      sinon.assert.calledOnce(clearStaleSession);
      sinon.assert.match(sessionTimedOut.responseData.statusCode, 409);
    });

    it('should call ttlSemaphore on key and then clearStaleSession', async () => {
      const sessionTimedOut = new SessionTimedoutHandler(request, response);
      const clearStaleSession = sinon.stub(sessionTimedOut, 'clearStaleSession').returns(Promise.resolve('abcd'));
      await sessionTimedOut.handle();

      sinon.assert.calledOnce(ttlSemaphore);
      sinon.assert.calledWith(ttlSemaphore, 'session_stop_semaphore', key);
      sinon.assert.calledOnce(clearStaleSession);
      clearStaleSession.restore();
    });
  });

  describe('clearStaleSession', () => {
    let _miscLogger; let
      updateOutsideBrowserstackTime;
    const sessionId = 'some_session_id';
    const keyObject = testHelper.getKeyObject();
    keyObject.appTesting = true;
    keyObject.device = 'Google Pixel 3';

    beforeEach(() => {
      _miscLogger = sinon.stub(HubLogger, 'miscLogger');
      updateOutsideBrowserstackTime = sinon.stub(helper, 'updateOutsideBrowserstackTime');
      sinon.stub(pubSub, 'publish');
    });

    afterEach(() => {
      _miscLogger.restore();
      updateOutsideBrowserstackTime.restore();
      pubSub.publish.restore();
    });

    it('should set responseData.statusCode to 200 and return undefined if sessionKeyObject is undefined', async () => {
      const sessionTimedOut = new SessionTimedoutHandler(request, response);
      const keyObj = { sessionId };

      const haStub = sinon.stub(ha, 'getData').yields(false, undefined);
      const result = await sessionTimedOut.clearStaleSession(keyObj);

      sinon.assert.match(sessionTimedOut.responseData.statusCode, 200);
      sinon.assert.match(result, undefined);
      haStub.restore();
    });

    it('calls set responseData after successful processing of session delete and logging', async () => {
      const logAndClearSessionInAsync = sinon.stub(SessionTimedoutHandler.prototype, 'logAndClearSessionInAsync').returns(Promise.resolve('abcd'));

      const sessionTimedOut = new SessionTimedoutHandler(request, response);
      const haStub = sinon.stub(ha, 'getData').yields(false, keyObject);
      await sessionTimedOut.clearStaleSession({});

      sinon.assert.calledOnce(updateOutsideBrowserstackTime);
      sinon.assert.calledWith(updateOutsideBrowserstackTime, keyObject);
      sinon.assert.calledOnce(pubSub.publish);
      sinon.assert.calledOnce(logAndClearSessionInAsync);
      sinon.assert.match(sessionTimedOut.responseData.statusCode, 200);
      sinon.assert.match(sessionTimedOut.sessionKeyObject, keyObject);
      haStub.restore();
    });
  });

  describe('instrumentGetUrlStuck', () => {
    let exceptionLogger; let seleniumStats; let
      getOpenUrl;
    beforeEach(() => {
      exceptionLogger = sinon.stub(HubLogger, 'exceptionLogger');
      seleniumStats = sinon.stub(HubLogger, 'seleniumStats');
    });
    afterEach(() => {
      exceptionLogger.restore();
      seleniumStats.restore();
      getOpenUrl.restore();
    });

    it('should return without logging if keyObject.browser is not chrome', async () => {
      const sessionTimedOut = new SessionTimedoutHandler(request, response);
      sessionTimedOut.sessionKeyObject = { browser: 'firefox' };
      getOpenUrl = sinon.stub(selenium, 'getOpenUrl').returns(Promise.resolve('data'));
      await sessionTimedOut.instrumentGetUrlStuck();

      sinon.assert.notCalled(exceptionLogger);
      sinon.assert.notCalled(seleniumStats);
      sinon.assert.notCalled(getOpenUrl);
      getOpenUrl.restore();
    });

    it('should get open url from selenium and do logging if url is data;,', async () => {
      const sessionTimedOut = new SessionTimedoutHandler(request, response);
      sessionTimedOut.sessionKeyObject = { browser: 'chrome', rails_session_id: 'some_session_id', url_changed: true };
      const stuckUrlData = { value: 'data:,' };
      const body = { data: JSON.stringify(stuckUrlData) };
      getOpenUrl = sinon.stub(selenium, 'getOpenUrl').returns(Promise.resolve(body));
      await sessionTimedOut.instrumentGetUrlStuck();

      sinon.assert.calledOnce(exceptionLogger);
      sinon.assert.calledOnce(seleniumStats);
      sinon.assert.calledWith(exceptionLogger, 'GetURL Stuck at data;,', `SessionId: ${sessionTimedOut.sessionKeyObject.rails_session_id}`);
      sinon.assert.calledWith(seleniumStats, 'automate-url-stuck', sessionTimedOut.sessionKeyObject, 'exception', '', '', 'automate-errors');
      sinon.assert.calledOnce(getOpenUrl);
      sinon.assert.calledWith(getOpenUrl, sessionTimedOut.sessionKeyObject);
      getOpenUrl.restore();
    });
  });

  describe('removeStaleSession', () => {
    const keyObject = testHelper.getKeyObject();
    let sendStopToRails;
    keyObject.appTesting = true;
    keyObject.device = 'Google Pixel 3';

    beforeEach(() => {
      sendStopToRails = sinon.stub(SessionTimedoutHandler.prototype, 'sendStoptoRails');
      sinon.stub(browserstack, 'postBrowserStack').yields(JSON.stringify({ noErrorScreenshot: true }));
      sinon.stub(helper, 'updateOutsideBrowserstackTime').returns('Updated Successfully');
      // addStopToRawLogs = sinon.stub(HubLogger, 'addStopToRawLogs');
      keyObject.errorMessage = 'some message';
    });

    afterEach(() => {
      browserstack.postBrowserStack.restore();
      sendStopToRails.restore();
      helper.updateOutsideBrowserstackTime.restore();
      // addStopToRawLogs.restore();
    });

    it('should call sendStopToRails', async () => {
      const sessionTimedOut = new SessionTimedoutHandler(request, response);
      sessionTimedOut.sessionKeyObject = keyObject;
      await sessionTimedOut.removeStaleSession();

      sinon.assert.calledOnce(sendStopToRails);
    });
  });

  describe('sendStopToRails', () => {
    let buildRailsStopUrlParams; let postBrowserStack; let addStopToRawLogs; let pingDataToStats; let
      sessionRemovedFromRegionHook;

    beforeEach(() => {
      buildRailsStopUrlParams = sinon.stub(helper, 'buildRailsStopUrlParams').returns('stop_url', 'stop_params');
      postBrowserStack = sinon.stub(browserstack, 'postBrowserStack').yields(null, undefined);
      addStopToRawLogs = sinon.stub(HubLogger, 'addStopToRawLogs');
      pingDataToStats = sinon.stub(helper, 'pingDataToStats');
      sessionRemovedFromRegionHook = sinon.stub(helper, 'sessionRemovedFromRegionHook');
    });

    afterEach(() => {
      buildRailsStopUrlParams.restore();
      postBrowserStack.restore();
      addStopToRawLogs.restore();
      pingDataToStats.restore();
      sessionRemovedFromRegionHook.restore();
    });

    it('generates params and sends request to rails', async () => {
      const sessionTimedOut = new SessionTimedoutHandler(request, response);
      sessionTimedOut.sessionKeyObject = { browser: 'chrome', rails_session_id: 'some_session_id', url_changed: true };
      sessionTimedOut.sendStoptoRails('message', false);
      sinon.assert.calledOnce(buildRailsStopUrlParams);
      sinon.assert.calledWith(buildRailsStopUrlParams, sessionTimedOut.sessionKeyObject, sinon.match.any, 'message');
      sinon.assert.calledOnce(postBrowserStack);
      sinon.assert.calledOnce(addStopToRawLogs);
      sinon.assert.calledOnce(pingDataToStats);
      sinon.assert.calledOnce(sessionRemovedFromRegionHook);
    });
  });

  describe('sendResponse', () => {
    beforeEach(() => {
      response = {
        writeHead: sinon.stub(),
        end: sinon.stub(),
      };
    });

    it('should end response with statusCode if responseData.statusCode is set', async () => {
      const sessionTimedOut = new SessionTimedoutHandler(request, response);
      sessionTimedOut.responseData = { statusCode: 200 };
      sessionTimedOut.response = response;
      sessionTimedOut.sendResponse();
      sinon.assert.calledOnce(response.writeHead);
      sinon.assert.calledWith(response.writeHead, 200, {});
      sinon.assert.calledOnce(response.end);
    });
  });
});
