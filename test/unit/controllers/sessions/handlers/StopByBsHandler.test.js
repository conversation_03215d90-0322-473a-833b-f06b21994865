'use strict';

const { StopByBsHandler } = require('../../../../../controllers/sessions/handlers/StopByBsHandler');
const helper = require('../../../../../helper');
const HubLogger = require('../../../../../log');
const requestlib = require('../../../../../lib/request');
const redisClient = require('../../../../../redisUtils').redisClient;
const sessionManagerHelper = require('../../../../../services/session/sessionManagerHelper');
const { StopVideoRecordingService } = require('../../../../../services/terminal/stopSessionInterface/StopVideoRecordingService');
const myLock = require('../../../../../semaphore');
const sinon = require('sinon');
const { assert } = require('chai');
const util = require('util');

describe('StopByBsHandler tests', () => {
  describe('handleSessionCleanup', () => {
    let request; let response; let retrieveLogsAndRemoval; let setex; let
      addStopToRawLogs;

    beforeEach(() => {
      request = {};
      response = {};
      retrieveLogsAndRemoval = sinon.stub(StopByBsHandler.prototype, 'retrieveLogsAndRemoval');
      setex = sinon.stub(redisClient, 'setex');
      addStopToRawLogs = sinon.stub(HubLogger, 'addStopToRawLogs');
    });

    afterEach(() => {
      retrieveLogsAndRemoval.restore();
      setex.restore();
      addStopToRawLogs.restore();
    });

    it('non-200 status', () => {
      const stopByBs = new StopByBsHandler(request, response);
      stopByBs.handleSessionCleanup('abcd', 500, 'SESSION_LIMIT_REACHED');
      sinon.assert.calledOnce(retrieveLogsAndRemoval);
      sinon.assert.calledWith(retrieveLogsAndRemoval, 'abcd');
      sinon.assert.calledOnce(setex);
      sinon.assert.notCalled(addStopToRawLogs);
    });

    it('STOP_UI 200 status code for automate', () => {
      request = { url: '/stop_ui' };
      const stopByBs = new StopByBsHandler(request, response);
      stopByBs.handleSessionCleanup('abcd', 200, 'SESSION_LIMIT_REACHED', false);
      sinon.assert.calledOnce(retrieveLogsAndRemoval);
      sinon.assert.calledWith(retrieveLogsAndRemoval, 'abcd');
      sinon.assert.calledOnce(setex);
      sinon.assert.calledOnce(addStopToRawLogs);
    });

    it('STOP_UI 200 status code for app automate', () => {
      request = { url: '/stop_ui' };
      const stopByBs = new StopByBsHandler(request, response);
      stopByBs.handleSessionCleanup('abcd', 200, 'SESSION_LIMIT_REACHED', true);
      sinon.assert.calledOnce(retrieveLogsAndRemoval);
      sinon.assert.calledWith(retrieveLogsAndRemoval, 'abcd');
      sinon.assert.calledOnce(setex);
      sinon.assert.calledOnce(addStopToRawLogs);
    });

    it('STOP_LIMIT 200 status code', () => {
      request = { url: '/stop_limit' };
      const stopByBs = new StopByBsHandler(request, response);
      stopByBs.handleSessionCleanup('abcd', 200, 'SESSION_LIMIT_REACHED');
      sinon.assert.calledOnce(retrieveLogsAndRemoval);
      sinon.assert.calledWith(retrieveLogsAndRemoval, 'abcd');
      sinon.assert.calledOnce(setex);
      sinon.assert.calledOnce(addStopToRawLogs);
    });

    it('STOP_LIMIT_AA_FREEMIUM 200 status code', () => {
      request = { url: '/stop_limit_aa_freemium' };
      const stopByBs = new StopByBsHandler(request, response);
      stopByBs.handleSessionCleanup('abcd', 200, 'SESSION_LIMIT_REACHED');
      sinon.assert.calledOnce(retrieveLogsAndRemoval);
      sinon.assert.calledWith(retrieveLogsAndRemoval, 'abcd');
      sinon.assert.calledOnce(setex);
      sinon.assert.calledOnce(addStopToRawLogs);
    });

    it('STOP_SMD 200 status code', () => {
      request = { url: '/stop_smd' };
      const stopByBs = new StopByBsHandler(request, response);
      stopByBs.handleSessionCleanup('abcd', 200, 'SESSION_LIMIT_REACHED');
      sinon.assert.calledOnce(retrieveLogsAndRemoval);
      sinon.assert.calledWith(retrieveLogsAndRemoval, 'abcd');
      sinon.assert.calledOnce(setex);
      sinon.assert.notCalled(addStopToRawLogs);
    });
  });

  describe('processParams', () => {
    let request; let
      response;

    beforeEach(() => {
      request = {};
      response = {};
    });

    it('should return sessionIdKey', async () => {
      const readRequest = sinon.stub(requestlib, 'readRequest').returns(JSON.stringify({ k: 'abcd' }));
      const stopByBs = new StopByBsHandler(request, response);
      const processedParams = await stopByBs.processParams();
      assert.equal(processedParams.sessionIdKey, 'abcd');
      assert.equal(processedParams.timeoutLimit, undefined);
      sinon.assert.calledOnce(readRequest);
      readRequest.restore();
    });

    it('should return timeoutLimit', async () => {
      const readRequest = sinon.stub(requestlib, 'readRequest').returns(JSON.stringify({ k: 'abcd', timeout_limit: 3600 }));
      const stopByBs = new StopByBsHandler(request, response);
      const processedParams = await stopByBs.processParams();
      assert.equal(processedParams.sessionIdKey, 'abcd');
      assert.equal(processedParams.timeoutLimit, 3600);
      sinon.assert.calledOnce(readRequest);
      readRequest.restore();
    });

    it('should return undefined if right params not passed', async () => {
      const readRequest = sinon.stub(requestlib, 'readRequest').returns(JSON.stringify({ key: 'abcd' }));
      const stopByBs = new StopByBsHandler(request, response);
      const processedParams = await stopByBs.processParams();
      assert.equal(processedParams, undefined);
      sinon.assert.calledOnce(readRequest);
      readRequest.restore();
    });

    it('should return undefined if data is blank', async () => {
      const readRequest = sinon.stub(requestlib, 'readRequest').returns('');
      const stopByBs = new StopByBsHandler(request, response);
      const processedParams = await stopByBs.processParams();
      assert.equal(processedParams, undefined);
      sinon.assert.calledOnce(readRequest);
      readRequest.restore();
    });
  });

  describe('handle', () => {
    let request; let response; let updateOutsideBrowserstackTime; let
      timeoutManagerClearTimeout;

    beforeEach(() => {
      request = {};
      response = {};
      updateOutsideBrowserstackTime = sinon.stub(helper, 'updateOutsideBrowserstackTime');
      timeoutManagerClearTimeout = sinon.stub(helper, 'timeoutManagerClearTimeout');
    });

    afterEach(() => {
      updateOutsideBrowserstackTime.restore();
      timeoutManagerClearTimeout.restore();
    });

    it('should end response if sessionid is not present', async () => {
      const processParams = sinon.stub(StopByBsHandler.prototype, 'processParams').returns(Promise.resolve());
      const getKeyObject = sinon.stub(sessionManagerHelper, 'getKeyObject').returns(undefined);
      const stopByBs = new StopByBsHandler(request, response);
      await stopByBs.handle();
      sinon.assert.calledOnce(processParams);
      sinon.assert.notCalled(getKeyObject);
      processParams.restore();
      getKeyObject.restore();
    });

    it('should return if sessionKeyObj is not present', async () => {
      const processParams = sinon.stub(StopByBsHandler.prototype, 'processParams').returns(Promise.resolve({ sessionIdKey: 'abcde' }));
      const getKeyObject = sinon.stub(sessionManagerHelper, 'getKeyObject').returns(undefined);
      const stopByBs = new StopByBsHandler(request, response);
      await stopByBs.handle();
      sinon.assert.calledOnce(processParams);
      sinon.assert.calledOnce(getKeyObject);
      sinon.assert.notCalled(updateOutsideBrowserstackTime);
      sinon.assert.notCalled(timeoutManagerClearTimeout);
      processParams.restore();
      getKeyObject.restore();
    });

    it('should return if stop has been triggered already', async () => {
      const processParams = sinon.stub(StopByBsHandler.prototype, 'processParams').returns(Promise.resolve({ sessionIdKey: 'abcde' }));
      const getKeyObject = sinon.stub(sessionManagerHelper, 'getKeyObject').returns({});
      const ttlSemaphore = sinon.stub(myLock, 'ttlSemaphore').returns(Promise.reject());
      const stopByBs = new StopByBsHandler(request, response);
      await stopByBs.handle();
      sinon.assert.calledOnce(processParams);
      sinon.assert.calledOnce(getKeyObject);
      sinon.assert.calledOnce(updateOutsideBrowserstackTime);
      sinon.assert.calledOnce(timeoutManagerClearTimeout);
      processParams.restore();
      getKeyObject.restore();
      ttlSemaphore.restore();
    });

    it('should append timeout limit if app_testing is true and valid timeout time is passed', async () => {
      const processParams = sinon.stub(StopByBsHandler.prototype, 'processParams').returns(Promise.resolve({ sessionIdKey: 'abcde', timeoutLimit: 3600 }));
      const getKeyObject = sinon.stub(sessionManagerHelper, 'getKeyObject').returns({ realMobile: true, appTesting: true });
      const ttlSemaphore = sinon.stub(myLock, 'ttlSemaphore').returns(Promise.resolve());
      const handleSessionCleanup = sinon.stub(StopByBsHandler.prototype, 'handleSessionCleanup');
      const stopByBs = new StopByBsHandler(request, response);
      await stopByBs.handle();
      sinon.assert.calledOnce(processParams);
      sinon.assert.calledOnce(getKeyObject);
      sinon.assert.calledOnce(updateOutsideBrowserstackTime);
      sinon.assert.calledOnce(timeoutManagerClearTimeout);
      sinon.assert.calledOnce(handleSessionCleanup);
      assert.isTrue(handleSessionCleanup.args[0][2].indexOf('3600') !== -1);
      processParams.restore();
      getKeyObject.restore();
      ttlSemaphore.restore();
      handleSessionCleanup.restore();
    });

    it('should not append timeout limit if app_testing is true and timeout time is not passed', async () => {
      const processParams = sinon.stub(StopByBsHandler.prototype, 'processParams').returns(Promise.resolve({ sessionIdKey: 'abcde' }));
      const getKeyObject = sinon.stub(sessionManagerHelper, 'getKeyObject').returns({ realMobile: true, appTesting: true });
      const ttlSemaphore = sinon.stub(myLock, 'ttlSemaphore').returns(Promise.resolve());
      const handleSessionCleanup = sinon.stub(StopByBsHandler.prototype, 'handleSessionCleanup');
      const stopByBs = new StopByBsHandler(request, response);
      await stopByBs.handle();
      sinon.assert.calledOnce(processParams);
      sinon.assert.calledOnce(getKeyObject);
      sinon.assert.calledOnce(updateOutsideBrowserstackTime);
      sinon.assert.calledOnce(timeoutManagerClearTimeout);
      sinon.assert.calledOnce(handleSessionCleanup);
      assert.isFalse(handleSessionCleanup.args[0][2].indexOf('3600') !== -1);
      processParams.restore();
      getKeyObject.restore();
      ttlSemaphore.restore();
      handleSessionCleanup.restore();
    });

    it('should handle response if stop should be called for desktop sessions', async () => {
      const processParams = sinon.stub(StopByBsHandler.prototype, 'processParams').returns(Promise.resolve({ sessionIdKey: 'abcde' }));
      const getKeyObject = sinon.stub(sessionManagerHelper, 'getKeyObject').returns({ realMobile: false });
      const ttlSemaphore = sinon.stub(myLock, 'ttlSemaphore').returns(Promise.resolve());
      const handleStop = sinon.stub(StopVideoRecordingService.prototype, 'handleStop').returns(Promise.resolve());
      const handleSessionCleanup = sinon.stub(StopByBsHandler.prototype, 'handleSessionCleanup').returns(Promise.resolve('abcde'));
      const stopByBs = new StopByBsHandler(request, response);
      await stopByBs.handle();
      sinon.assert.calledOnce(processParams);
      sinon.assert.calledOnce(getKeyObject);
      sinon.assert.calledOnce(updateOutsideBrowserstackTime);
      sinon.assert.calledOnce(timeoutManagerClearTimeout);
      sinon.assert.calledOnce(handleStop);
      sinon.assert.calledOnce(handleSessionCleanup);
      processParams.restore();
      getKeyObject.restore();
      ttlSemaphore.restore();
      handleStop.restore();
      handleSessionCleanup.restore();
    });

    it('should handle response if stop should be called for mobile sessions', async () => {
      const processParams = sinon.stub(StopByBsHandler.prototype, 'processParams').returns(Promise.resolve({ sessionIdKey: 'abcde' }));
      const getKeyObject = sinon.stub(sessionManagerHelper, 'getKeyObject').returns({ realMobile: true, appTesting: true });
      const ttlSemaphore = sinon.stub(myLock, 'ttlSemaphore').returns(Promise.resolve());
      const handleStop = sinon.stub(StopVideoRecordingService.prototype, 'handleStop').returns(Promise.resolve());
      const handleSessionCleanup = sinon.stub(StopByBsHandler.prototype, 'handleSessionCleanup').returns(Promise.resolve('abcde'));
      const stopByBs = new StopByBsHandler(request, response);
      await stopByBs.handle();
      sinon.assert.calledOnce(processParams);
      sinon.assert.calledOnce(getKeyObject);
      sinon.assert.calledOnce(updateOutsideBrowserstackTime);
      sinon.assert.calledOnce(timeoutManagerClearTimeout);
      sinon.assert.notCalled(handleStop);
      sinon.assert.calledOnce(handleSessionCleanup);
      processParams.restore();
      getKeyObject.restore();
      ttlSemaphore.restore();
      handleStop.restore();
      handleSessionCleanup.restore();
    });

    it('should log error in case of any', async () => {
      const processParams = sinon.stub(StopByBsHandler.prototype, 'processParams').returns(Promise.resolve({ sessionIdKey: 'abcde' }));
      const getKeyObject = sinon.stub(sessionManagerHelper, 'getKeyObject').returns({ realMobile: true, appTesting: true });
      const ttlSemaphore = sinon.stub(myLock, 'ttlSemaphore').returns(Promise.reject());
      const handleStop = sinon.stub(StopVideoRecordingService.prototype, 'handleStop').returns(Promise.resolve());
      const handleSessionCleanup = sinon.stub(StopByBsHandler.prototype, 'handleSessionCleanup').returns(Promise.resolve('abcde'));
      const stopByBs = new StopByBsHandler(request, response);
      await stopByBs.handle();
      sinon.assert.calledOnce(processParams);
      sinon.assert.calledOnce(getKeyObject);
      sinon.assert.calledOnce(updateOutsideBrowserstackTime);
      sinon.assert.calledOnce(timeoutManagerClearTimeout);
      sinon.assert.notCalled(handleStop);
      sinon.assert.notCalled(handleSessionCleanup);
      processParams.restore();
      getKeyObject.restore();
      ttlSemaphore.restore();
      handleStop.restore();
      handleSessionCleanup.restore();
    });
  });

  describe('sendResponse', () => {
    it('end response if statusCode and header present', () => {
      const request = {};
      const response = {
        writeHead: sinon.stub(),
        end: sinon.stub(),
      };
      const stopByBs = new StopByBsHandler(request, response);
      stopByBs.responseData.statusCode = 200;
      stopByBs.sendResponse();
      sinon.assert.calledOnce(response.writeHead);
      sinon.assert.calledOnce(response.end);
      sinon.assert.calledWithExactly(response.writeHead, 200, { 'content-type': 'application/json; charset=utf-8' });
    });
  });

  describe('sendResponse lighthouse', () => {
    it('end response if statusCode and header present', async () => {
      const request = {};
      const response = {
        writeHead: sinon.stub(),
        end: sinon.stub(),
      };
      const processParams = sinon.stub(StopByBsHandler.prototype, 'processParams').returns(Promise.resolve({ sessionIdKey: 'abcde' }));
      const getKeyObject = sinon.stub(sessionManagerHelper, 'getKeyObject').returns({ realMobile: true, appTesting: true, lighthouseAutomate: { report_counter: 5 } });
      const ttlSemaphore = sinon.stub(myLock, 'ttlSemaphore').returns(Promise.reject());
      const handleStop = sinon.stub(StopVideoRecordingService.prototype, 'handleStop').returns(Promise.resolve());
      const handleSessionCleanup = sinon.stub(StopByBsHandler.prototype, 'handleSessionCleanup').returns(Promise.resolve('abcde'));
      const stopByBs = new StopByBsHandler(request, response);
      await stopByBs.handle();
      stopByBs.responseData.statusCode = 200;
      stopByBs.sendResponse();
      sinon.assert.calledOnce(response.writeHead);
      sinon.assert.calledOnce(response.end);
      sinon.assert.calledWithExactly(response.writeHead, 200, { 'content-type': 'application/json; charset=utf-8' });
      processParams.restore();
      getKeyObject.restore();
      ttlSemaphore.restore();
      handleStop.restore();
      handleSessionCleanup.restore();
    });
  });

  describe('sendResponse aiSessionDetails', () => {
    it('send healing success in stop session details is healing is true', async () => {
      const request = {};
      const response = {
        writeHead: sinon.stub(),
        end: sinon.stub(),
      };
      const processParams = sinon.stub(StopByBsHandler.prototype, 'processParams').returns(Promise.resolve({ sessionIdKey: 'abcde' }));
      const getKeyObject = sinon.stub(sessionManagerHelper, 'getKeyObject').returns({ realMobile: true, appTesting: true, selfHealingSuccess: true });
      const ttlSemaphore = sinon.stub(myLock, 'ttlSemaphore').returns(Promise.reject());
      const handleStop = sinon.stub(StopVideoRecordingService.prototype, 'handleStop').returns(Promise.resolve());
      const handleSessionCleanup = sinon.stub(StopByBsHandler.prototype, 'handleSessionCleanup').returns(Promise.resolve('abcde'));
      const stopByBs = new StopByBsHandler(request, response);
      await stopByBs.handle();
      stopByBs.responseData.statusCode = 200;
      stopByBs.sendResponse();
      sinon.assert.calledOnce(response.writeHead);
      sinon.assert.calledOnce(response.end);
      sinon.assert.calledWithExactly(response.writeHead, 200, { 'content-type': 'application/json; charset=utf-8' });
      processParams.restore();
      getKeyObject.restore();
      ttlSemaphore.restore();
      handleStop.restore();
      handleSessionCleanup.restore();
    });
    it('send soft healing success in stop session details if soft healing is true', async () => {
      const request = {};
      const response = {
        writeHead: sinon.stub(),
        end: sinon.stub(),
      };
      const processParams = sinon.stub(StopByBsHandler.prototype, 'processParams').returns(Promise.resolve({ sessionIdKey: 'abcde' }));
      const getKeyObject = sinon.stub(sessionManagerHelper, 'getKeyObject').returns({ realMobile: true, appTesting: true, softHealingSuccess: true });
      const ttlSemaphore = sinon.stub(myLock, 'ttlSemaphore').returns(Promise.reject());
      const handleStop = sinon.stub(StopVideoRecordingService.prototype, 'handleStop').returns(Promise.resolve());
      const handleSessionCleanup = sinon.stub(StopByBsHandler.prototype, 'handleSessionCleanup').returns(Promise.resolve('abcde'));
      const stopByBs = new StopByBsHandler(request, response);
      await stopByBs.handle();
      stopByBs.responseData.statusCode = 200;
      stopByBs.sendResponse();
      sinon.assert.calledOnce(response.writeHead);
      sinon.assert.calledOnce(response.end);
      sinon.assert.calledWithExactly(response.writeHead, 200, { 'content-type': 'application/json; charset=utf-8' });
      processParams.restore();
      getKeyObject.restore();
      ttlSemaphore.restore();
      handleStop.restore();
      handleSessionCleanup.restore();
    });
  });

  describe('sendResponse ai_healing_details', () => {
    it('should include ai_healing_details in the response if total_healing_enabled_request > 0', async () => {
      const request = {};
      const response = {
        writeHead: sinon.stub(),
        end: sinon.stub(),
      };
      const processParams = sinon.stub(StopByBsHandler.prototype, 'processParams').returns(Promise.resolve({ sessionIdKey: 'abcde' }));
      const getKeyObject = sinon.stub(sessionManagerHelper, 'getKeyObject').returns({
        ai_healing_details: {
          total_healing_enabled_request: 10,
          total_healing_request: 5,
          pre_check_failure_count: 1,
          script_exec_error_count: 2,
          healing_failure_count: 1,
          healing_success_count: 1,
        },
      });
      const ttlSemaphore = sinon.stub(myLock, 'ttlSemaphore').returns(Promise.reject());
      const handleStop = sinon.stub(StopVideoRecordingService.prototype, 'handleStop').returns(Promise.resolve());
      const handleSessionCleanup = sinon.stub(StopByBsHandler.prototype, 'handleSessionCleanup').returns(Promise.resolve('abcde'));
      const stopByBs = new StopByBsHandler(request, response);
      await stopByBs.handle();
      stopByBs.responseData.statusCode = 200;
      stopByBs.sendResponse();
      sinon.assert.calledOnce(response.writeHead);
      sinon.assert.calledOnce(response.end);
      sinon.assert.calledWithExactly(response.writeHead, 200, { 'content-type': 'application/json; charset=utf-8' });
      const responseBody = JSON.parse(response.end.getCall(0).args[0]);
      assert.deepEqual(responseBody.ai_healing_details, {
        total_healing_enabled_request: 10,
        total_healing_request: 5,
        pre_check_failure_count: 1,
        script_exec_error_count: 2,
        healing_failure_count: 1,
        healing_success_count: 1,
      });

      processParams.restore();
      getKeyObject.restore();
      ttlSemaphore.restore();
      handleStop.restore();
      handleSessionCleanup.restore();
    });

    it('should not include ai_healing_details in the response if ai_healing_details is not present', async () => {
      const request = {};
      const response = {
        writeHead: sinon.stub(),
        end: sinon.stub(),
      };
      const processParams = sinon.stub(StopByBsHandler.prototype, 'processParams').returns(Promise.resolve({ sessionIdKey: 'abcde' }));
      const getKeyObject = sinon.stub(sessionManagerHelper, 'getKeyObject').returns({});
      const ttlSemaphore = sinon.stub(myLock, 'ttlSemaphore').returns(Promise.reject());
      const handleStop = sinon.stub(StopVideoRecordingService.prototype, 'handleStop').returns(Promise.resolve());
      const handleSessionCleanup = sinon.stub(StopByBsHandler.prototype, 'handleSessionCleanup').returns(Promise.resolve('abcde'));
      const stopByBs = new StopByBsHandler(request, response);
      await stopByBs.handle();
      stopByBs.responseData.statusCode = 200;
      stopByBs.sendResponse();
      sinon.assert.calledOnce(response.writeHead);
      sinon.assert.calledOnce(response.end);
      sinon.assert.calledWithExactly(response.writeHead, 200, { 'content-type': 'application/json; charset=utf-8' });
      const responseBody = JSON.parse(response.end.getCall(0).args[0]);
      assert.notProperty(responseBody, 'ai_healing_details');

      processParams.restore();
      getKeyObject.restore();
      ttlSemaphore.restore();
      handleStop.restore();
      handleSessionCleanup.restore();
    });

    it('should not include ai_healing_details in the response if total_healing_enabled_request is 0', async () => {
      const request = {};
      const response = {
        writeHead: sinon.stub(),
        end: sinon.stub(),
      };
      const processParams = sinon.stub(StopByBsHandler.prototype, 'processParams').returns(Promise.resolve({ sessionIdKey: 'abcde' }));
      const getKeyObject = sinon.stub(sessionManagerHelper, 'getKeyObject').returns({
        ai_healing_details: {
          total_healing_enabled_request: 0,
        },
      });
      const ttlSemaphore = sinon.stub(myLock, 'ttlSemaphore').returns(Promise.reject());
      const handleStop = sinon.stub(StopVideoRecordingService.prototype, 'handleStop').returns(Promise.resolve());
      const handleSessionCleanup = sinon.stub(StopByBsHandler.prototype, 'handleSessionCleanup').returns(Promise.resolve('abcde'));
      const stopByBs = new StopByBsHandler(request, response);
      await stopByBs.handle();
      stopByBs.responseData.statusCode = 200;
      stopByBs.sendResponse();
      sinon.assert.calledOnce(response.writeHead);
      sinon.assert.calledOnce(response.end);
      sinon.assert.calledWithExactly(response.writeHead, 200, { 'content-type': 'application/json; charset=utf-8' });
      const responseBody = JSON.parse(response.end.getCall(0).args[0]);
      assert.notProperty(responseBody, 'ai_healing_details');

      processParams.restore();
      getKeyObject.restore();
      ttlSemaphore.restore();
      handleStop.restore();
      handleSessionCleanup.restore();
    });

    it('should not include ai_healing_details in the response if total_healing_enabled_request key is missing', async () => {
      const request = {};
      const response = {
        writeHead: sinon.stub(),
        end: sinon.stub(),
      };
      const processParams = sinon.stub(StopByBsHandler.prototype, 'processParams').returns(Promise.resolve({ sessionIdKey: 'abcde' }));
      const getKeyObject = sinon.stub(sessionManagerHelper, 'getKeyObject').returns({
        ai_healing_details: {
          total_healing_request: 5,
          pre_check_failure_count: 1,
          script_exec_error_count: 2,
          healing_failure_count: 1,
          healing_success_count: 1,
        },
      });
      const ttlSemaphore = sinon.stub(myLock, 'ttlSemaphore').returns(Promise.reject());
      const handleStop = sinon.stub(StopVideoRecordingService.prototype, 'handleStop').returns(Promise.resolve());
      const handleSessionCleanup = sinon.stub(StopByBsHandler.prototype, 'handleSessionCleanup').returns(Promise.resolve('abcde'));
      const stopByBs = new StopByBsHandler(request, response);
      await stopByBs.handle();
      stopByBs.responseData.statusCode = 200;
      stopByBs.sendResponse();
      sinon.assert.calledOnce(response.writeHead);
      sinon.assert.calledOnce(response.end);
      sinon.assert.calledWithExactly(response.writeHead, 200, { 'content-type': 'application/json; charset=utf-8' });
      const responseBody = JSON.parse(response.end.getCall(0).args[0]);
      assert.notProperty(responseBody, 'ai_healing_details');

      processParams.restore();
      getKeyObject.restore();
      ttlSemaphore.restore();
      handleStop.restore();
      handleSessionCleanup.restore();
    });

    it('should not include ai_healing_details in the response if ai_healing_details is empty', async () => {
      const request = {};
      const response = {
        writeHead: sinon.stub(),
        end: sinon.stub(),
      };
      const processParams = sinon.stub(StopByBsHandler.prototype, 'processParams').returns(Promise.resolve({ sessionIdKey: 'abcde' }));
      const getKeyObject = sinon.stub(sessionManagerHelper, 'getKeyObject').returns({
        ai_healing_details: {},
      });
      const ttlSemaphore = sinon.stub(myLock, 'ttlSemaphore').returns(Promise.reject());
      const handleStop = sinon.stub(StopVideoRecordingService.prototype, 'handleStop').returns(Promise.resolve());
      const handleSessionCleanup = sinon.stub(StopByBsHandler.prototype, 'handleSessionCleanup').returns(Promise.resolve('abcde'));
      const stopByBs = new StopByBsHandler(request, response);
      await stopByBs.handle();
      stopByBs.responseData.statusCode = 200;
      stopByBs.sendResponse();
      sinon.assert.calledOnce(response.writeHead);
      sinon.assert.calledOnce(response.end);
      sinon.assert.calledWithExactly(response.writeHead, 200, { 'content-type': 'application/json; charset=utf-8' });
      const responseBody = JSON.parse(response.end.getCall(0).args[0]);
      assert.notProperty(responseBody, 'ai_healing_details');

      processParams.restore();
      getKeyObject.restore();
      ttlSemaphore.restore();
      handleStop.restore();
      handleSessionCleanup.restore();
    });
  });

  describe('retrieveLogsAndRemoval', () => {
    it('should retrieve logs and remove session', async () => {
      const request = {};
      const response = {
        writeHead: sinon.stub(),
        end: sinon.stub(),
      };
      const sessionRemovedFromRegionHook = sinon.stub(helper, 'sessionRemovedFromRegionHook');
      const fetchAdditionalLogsPromisified = sinon.stub(util, 'promisify').returns(() => {});
      const stopByBs = new StopByBsHandler(request, response);
      await stopByBs.retrieveLogsAndRemoval();
      sinon.assert.calledOnce(sessionRemovedFromRegionHook);
      sinon.assert.calledOnce(fetchAdditionalLogsPromisified);
      sessionRemovedFromRegionHook.restore();
      fetchAdditionalLogsPromisified.restore();
    });
  });
});
