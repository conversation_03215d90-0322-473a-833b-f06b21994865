'use strict';

const { stub } = require('sinon');
const { assert } = require('chai');
const {
  describe, before, after, beforeEach, it, afterEach
} = require('mocha');
const SeleniumHandler = require('../../../socketManagers/seleniumHandler');
const logger = require('../../../logger').basicLogger;
const { wsProxy } = require('./socketHelpers');
const { RECONNECT_PREFIX } = require('../../../config/socketConstants');
const { global_registry: globalRegistry } = require('../../../constants');
const HA = require('../../../ha');
const origHelper = require('../../../helper');
const pubSub = require('../../../pubSub');
const nock = require('nock');
const expect = require('chai').expect;
const hub = require('../../../hub');
const sinon = require('sinon');
const sessionManagerHelper = require('../../../services/session/sessionManagerHelper');

describe('SeleniumHandler', () => {
  let seleniumHandler;
  let ws;

  before(() => {
    stub(logger, 'info');
    stub(logger, 'error');
    stub(logger, 'debug');
    stub(logger, 'warn');
  });

  after(() => {
    logger.info.restore();
    logger.error.restore();
    logger.debug.restore();
    logger.warn.restore();
  });

  beforeEach(() => {
    seleniumHandler = new SeleniumHandler();
    ws = wsProxy();
  });

  describe('#checkIfSessionOnOtherHubs', () => {
    let refreshAndAddKeyObjectToThisHub;
    beforeEach(() => {
      refreshAndAddKeyObjectToThisHub = sinon.stub(sessionManagerHelper, 'refreshAndAddKeyObjectToThisHub');
    });

    afterEach(() => {
      refreshAndAddKeyObjectToThisHub.restore();
    });
    it('should return session data if found on other hubs', (done) => {
      const sessionId = '123';

      const expectedData = { found: true, sessionId: '123', timeoutManagerIndex: 0, toggle: 1 };
      const n1 = nock('http://hub1.bsstag.com/')
        .get('/session/get_session_data?sessionId=123&cdp=true')
        .reply(200, JSON.stringify(expectedData));
      const n2 = nock('http://hub2.bsstag.com/')
        .get('/session/get_session_data?sessionId=123&cdp=true')
        .reply(200, JSON.stringify(expectedData));

      // Call the function and check the result
      const seleniumHandler = new SeleniumHandler();
      seleniumHandler.checkIfSessionOnOtherHubs(sessionId, (err, data) => {
        n1.isDone().should.be.true;
        n2.isDone().should.be.false; // should not call the second hub
        expect(err).to.be.null;
        expect(data.found).to.equal(expectedData.found);
        done();
      });
    });

    it('should return an error if session data not found on any hub', (done) => {
      const sessionId = '123';

      const n1 = nock('http://hub1.bsstag.com/')
        .get('/session/get_session_data?sessionId=123&cdp=true')
        .reply(404);
      const n2 = nock('http://hub2.bsstag.com/')
        .get('/session/get_session_data?sessionId=123&cdp=true')
        .reply(404);

      // Call the function and check the result
      const seleniumHandler = new SeleniumHandler();
      seleniumHandler.checkIfSessionOnOtherHubs(sessionId, (err, data) => {
        n1.isDone().should.be.true;
        n2.isDone().should.be.true;
        expect(err).to.be.an('error');
        expect(err.message).to.equal('sessionId state not found');
        expect(data).to.be.undefined;
        done();
      });
    });
  });

  describe('.messageHandler', () => {
    it('should call initialize reconnect, wait for allocation, update time, and process data', (done) => {
      const methods = ['initializeReconnect', 'waitForAllocation', 'updateTime', 'processData'];
      methods.forEach((method) => { stub(seleniumHandler, method); });
      seleniumHandler.messageHandler(ws, '{}').then(() => {
        methods.forEach((method) => {
          assert(seleniumHandler[method].calledOnce, `${method} was not called`);
          seleniumHandler[method].restore();
        });
        done();
      });
    });

    it('should not process data if RECONNECT message', () => {
      stub(seleniumHandler, 'processData');
      seleniumHandler.messageHandler(ws, RECONNECT_PREFIX);
      assert.isFalse(seleniumHandler.processData.calledOnce);
      seleniumHandler.processData.restore();
    });

    it('should close websocket if exception is caught', () => {
      stub(seleniumHandler, 'initializeReconnect').throwsException();
      seleniumHandler.messageHandler(ws, '{}');
      assert(ws.close.calledOnce, 'WebSocket not closed on exception');
      seleniumHandler.initializeReconnect.restore();
    });
  });

  describe('.waitForAllocation', () => {
    it('should return terminalAllocation state', () => {
      seleniumHandler.terminalAllocationState.set(ws.id, 'state');
      assert.equal(seleniumHandler.waitForAllocation(ws.id), 'state');
    });
  });

  describe('.preProcessAllocation', () => {
    it('should set upstream, start allocation and update time', (done) => {
      const methods = ['setUpstream', 'startAllocation', 'updateTime'];
      methods.forEach((method) => { stub(seleniumHandler, method); });
      seleniumHandler.preProcessAllocation(ws).then(() => {
        methods.forEach((method) => {
          assert(seleniumHandler[method].calledOnce, `${method} was not called`);
          seleniumHandler[method].restore();
        });
        done();
      });
    });

    it('should close websocket if exception is caught', () => {
      stub(seleniumHandler, 'setUpstream').throwsException();
      seleniumHandler.preProcessAllocation(ws);
      assert(ws.close.calledOnce, 'WebSocket not closed on exception');
      seleniumHandler.setUpstream.restore();
    });
  });

  describe('.connectToDesktopSocket', () => {
    it('should call and return desktop socket', () => {
      stub(seleniumHandler.desktopSocketManager, 'addNewSocket').returns('socket');
      assert.equal(seleniumHandler.connectToDesktopSocket(), 'socket');
      seleniumHandler.desktopSocketManager.addNewSocket.restore();
    });
  });

  describe('.startAllocation', () => {
    it('should set terminal allocation state', () => {
      stub(seleniumHandler.terminalAllocationState, 'set');
      stub(seleniumHandler, 'connectToDesktopSocket').returns(Promise.resolve());
      seleniumHandler.startAllocation(ws.id, ws);
      assert(seleniumHandler.terminalAllocationState.set.calledOnce, 'terminalAllocation.set not called');
      seleniumHandler.terminalAllocationState.set.restore();
      seleniumHandler.connectToDesktopSocket.restore();
    });
  });

  describe('.setUpstream', () => {
    afterEach(() => { delete globalRegistry[ws.id]; });

    it('should set cdp upstream and hostname if data is in memory', (done) => {
      globalRegistry[ws.id] = { seCdpUpstream: 'seCdpUpstreamMemory', name: '*******' };
      seleniumHandler.setUpstream(ws).then(() => {
        assert.equal(ws.upstream, 'seCdpUpstreamMemory');
        assert.equal(ws.hostname, '*******');
        done();
      });
    });

    it('should set cdp upstream if data is in redis', (done) => {
      stub(HA, 'getData').callsArgWith(1, undefined, { seCdpUpstream: 'seCdpUpstreamRedis' });
      seleniumHandler.setUpstream(ws).then(() => {
        assert.equal(ws.upstream, 'seCdpUpstreamRedis');
        done();
      });
      HA.getData.restore();
    });

    it('should set throw error if data is not in redis or memory', (done) => {
      stub(HA, 'getData').callsArgWith(1, undefined, null);
      seleniumHandler.setUpstream(ws).catch((err) => {
        assert.equal(err.message, 'sessionId state not found');
        done();
      });
      HA.getData.restore();
    });

    it('should set bidi upstream and hostname if data is in memory', (done) => {
      globalRegistry[ws.id] = { seBidiUpstream: 'seCdpUpstreamMemory', name: '*******' };
      seleniumHandler.setUpstream(ws).then(() => {
        assert.equal(ws.upstream, 'seCdpUpstreamMemory');
        assert.equal(ws.hostname, '*******');
        done();
      });
    });

    it('should not set upstream if bidi or cdp upstream not present and send reject', () => {
      globalRegistry[ws.id] = {};
      seleniumHandler.setUpstream(ws).catch(error => {
        expect(error).to.be.eql('[SETUPSTREAM] upStream url not present');
      });
    });
  });

  describe('.writeResponse', () => {
    it('should set rails browser mapping if not already exist', () => {
      seleniumHandler.writeResponse(ws, {
        result: {
          sessionId: 'browserSessionId'
        }
      });
      assert.equal(seleniumHandler.railsBrowserMapping.get(ws.id), 'browserSessionId');
    });

    it('should update response text logs', () => {
      stub(seleniumHandler, 'updateResponseLog');
      seleniumHandler.writeResponse(ws, {});
      assert(seleniumHandler.updateResponseLog.calledOnce, 'updateResponseLog not called');
    });

    it('should send the reponse over WebSocket', () => {
      seleniumHandler.writeResponse(ws, {});
      assert(ws.send.calledOnce, 'ws.send not called');
    });
  });

  describe('.updateTime', () => {
    beforeEach(() => {
      stub(origHelper, 'timeoutManagerUpdateTimeout');
      stub(HA, 'setData');
      stub(HA, 'getData').callsArgWith(1, undefined, {});
      stub(pubSub, 'publish');
    });

    afterEach(() => {
      delete globalRegistry[ws.id];
      origHelper.timeoutManagerUpdateTimeout.restore();
      HA.setData.restore();
      HA.getData.restore();
      pubSub.publish.restore();
    });

    it('should update timeout, change last request time, and pubsub', () => {
      globalRegistry[ws.id] = {};
      seleniumHandler.updateTime(ws);
      assert(origHelper.timeoutManagerUpdateTimeout.calledOnce, 'timeoutManagerUpdateTimeout not called');
      assert(HA.setData.calledOnce, 'setData not called');
      assert(pubSub.publish.calledOnce, 'publish not called');
    });

    it('should call timeout manager update timeout if data in redis', () => {
      seleniumHandler.updateTime(ws);
      assert(origHelper.timeoutManagerUpdateTimeout.calledOnce, 'timeoutManagerUpdateTimeout not called');
      assert(HA.setData.calledOnce, 'setData not called');
      assert(pubSub.publish.calledOnce, 'publish not called');
    });

    it('should close websocket if object not found in region', () => {
      HA.getData.restore();
      stub(HA, 'getData').callsArgWith(1, undefined, null);
      seleniumHandler.updateTime(ws);
      assert(ws.close.calledOnce, 'ws.close not called');
    });
  });

  describe('.clearState', () => {
    it('should delete all states and mappings', () => {
      const states = [
        'rawLogState',
        'terminalAllocationState',
        'sessionState',
        'railsSessionMapping',
        'railsBrowserMapping',
        'authRequests'
      ];
      states.forEach((state) => { seleniumHandler[state].set(ws.id, ws.id); });
      seleniumHandler.clearState(ws.id);
      states.forEach((state) => { assert.isUndefined(seleniumHandler[state].get(ws.id)); });
    });
  });
});
