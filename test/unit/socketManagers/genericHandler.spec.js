'use strict';

const sinon = require('sinon');
const rewire = require('rewire');

const rewireHandler = rewire('../../../socketManagers/baseSocketMessageHandler');
const { wsProxy } = require('./socketHelpers');
const { assert, expect } = require('chai');
const { kReqData, kDesktopDataReceived, kSendToDesktop } = require('../../../config/socketConstants');

describe('Generic response handler to clients', () => {
  const SocketMessageHandler = rewireHandler.__get__('SocketMessageHandler');
  let handler;
  let logOriginal;
  let logSpy;
  const callback = sinon.match.func;

  beforeEach(() => {
    sinon.stub(SocketMessageHandler.prototype, 'enableMethodMaps', () => {});
    sinon.stub(SocketMessageHandler.prototype, 'debugScreenshotMethodMaps', () => {});
    sinon.stub(SocketMessageHandler.prototype, 'assignLogBuilder', () => {});
    logOriginal = rewireHandler.__get__('sessionLog');
    logSpy = sinon.spy();
    rewireHandler.__set__('sessionLog', logSpy);
    handler = new SocketMessageHandler();


    handler.rawLogBuilder = {
      updateLogEntry: sinon.spy(),
    };
  });

  afterEach(() => {
    SocketMessageHandler.prototype.enableMethodMaps.restore();
    SocketMessageHandler.prototype.debugScreenshotMethodMaps.restore();
    SocketMessageHandler.prototype.assignLogBuilder.restore();
    rewireHandler.__set__('sessionLog', logOriginal);
  });

  it('should proxy to desktop connection', () => {
    sinon.stub(handler, 'getRegistryObject').returns(wsProxy());
    const data = {
      id: 12,
      method: 'Browser.getContexts',
    };
    const ws = wsProxy();

    handler.sessionState.set(ws.id, {});
    handler.genericHandler(ws, data);

    assert(logSpy.calledOnce);

    handler.sessionState.delete(ws.id);
    handler.getRegistryObject.restore();
  });

  it('should call the generic handler if no method found in methodHandler', () => {
    sinon.spy(handler, 'genericHandler');
    const methodName = 'Page.methodDoesnotExists';
    const data = {
      id: 12,
      method: 'Browser.getContexts',
    };
    const ws = wsProxy();

    const fn = handler.methodHandler(methodName);
    fn(ws, data);
    assert(handler.genericHandler.calledOnce);

    handler.genericHandler.restore();
  });

  context(
    "modify the sendToTerminal behaviour in case of playwrightVersion < 1.15 & client.playwrightVersion >= 1.15",
    () => {
      let ws;
      const sendToRequestLogStub = {};
      const getRegistryObjectStub = {};
      const desktopSocketManagerStub = {};
      const desktopSocketManagerStubSpy = sinon.spy();
      beforeEach(() => {
        ws = wsProxy();
        ws.isPlaywright = true;
        handler.sessionState.set(ws.id, ws);
        sendToRequestLogStub.stub = sinon.stub(handler, "sendToRequestLog");
        sendToRequestLogStub.stub.returns({});
        getRegistryObjectStub.stub = sinon.stub(handler, "getRegistryObject");
        desktopSocketManagerStub.stub = sinon.stub(
          handler.desktopSocketManager,
          "emit",
          desktopSocketManagerStubSpy
        );
      });
      afterEach(() => {
        sendToRequestLogStub.stub.restore();
        getRegistryObjectStub.stub.restore();
        desktopSocketManagerStub.stub.restore();
        handler.sessionState.delete(ws.id);
      });
      it("should emit the kDesktopDataReceived to desktopSocketManger for initialize method", () => {
        let data = {
          id: 1,
          method: "initialize",
          params: {
            sdkLanguage: "javascript",
          },
        };
        getRegistryObjectStub.stub.returns({
          railsSesionId: "rails-1234",
          playwrightVersion: "1.12.1",
        });
        ws[kReqData] = {};
        const socketObjData = { "client.playwrightVersion": "1.15.0" };
        ws[kReqData].data = socketObjData;
        handler.genericHandler(ws, data);
        sinon.assert.calledOnce(sendToRequestLogStub.stub);
        sinon.assert.calledTwice(getRegistryObjectStub.stub);
        expect(ws[kReqData]["data"]["sdkLanguage"]).to.deep.equal(data.params.sdkLanguage)
        let response = { id: 1, result: { playwright: { guid: 'Playwright' } } };
        setTimeout(() => {
          sinon.assert.calledOnce(desktopSocketManagerStub.stub);
          sinon.assert.calledWithExactly(desktopSocketManagerStubSpy, kDesktopDataReceived, ws.id, JSON.stringify(response), callback);
        }, 1500);
      });

      it("should emit the kDesktopDataReceived to desktopSocketManger for waitForTimeout method", () => {
        let data = {
          id: 1,
          method: "waitForTimeout",
          params: {
            timeout: 500
          },
        };
        getRegistryObjectStub.stub.returns({
          railsSesionId: "rails-1234",
          playwrightVersion: "1.14.1",
        });
        ws[kReqData] = {};
        const socketObjData = { "client.playwrightVersion": "1.16.0" };
        ws[kReqData].data = socketObjData;
        handler.genericHandler(ws, data);
        sinon.assert.calledOnce(sendToRequestLogStub.stub);
        sinon.assert.calledTwice(getRegistryObjectStub.stub);
        let response = { id: 1 };
        setTimeout(() => {
          sinon.assert.calledOnce(desktopSocketManagerStub.stub);
          sinon.assert.calledWithExactly(desktopSocketManagerStubSpy, kDesktopDataReceived, ws.id, JSON.stringify(response), callback);
        }, 25000);
      });

      it("should emit the kSendToDesktop to desktopSocketManger for other method", () => {
        let data = {
          id: 1,
          method: "BrowserContext",
          params: {}
        };
        getRegistryObjectStub.stub.returns({
          railsSesionId: "rails-1234",
          playwrightVersion: "1.12.1",
        });
        ws[kReqData] = {};
        const socketObjData = { "client.playwrightVersion": "1.15.0", sdkLanguage: "javascript"};
        ws[kReqData].data = socketObjData;
        handler.genericHandler(ws, data);
        sinon.assert.calledOnce(sendToRequestLogStub.stub);
        sinon.assert.calledTwice(getRegistryObjectStub.stub);

        // Store the exact same object that will be passed to the spy
        const response_data = {
          id: 1,
          method: "BrowserContext",
          params: {
            sdkLanguage: "javascript",
          },
        };

        // Use the exact object in the assertion
        sinon.assert.calledOnce(desktopSocketManagerStub.stub);

        // Use the exact function signature that matches what the test is expecting
        sinon.assert.calledWithExactly(
          desktopSocketManagerStubSpy,
          kSendToDesktop,
          ws.id,
          response_data,
          callback
        );
      });
    }
  );

  context(
    'modify the sendToTerminal behaviour in case of playwrightVersion >= 1.15 & client.playwrightVersion < 1.15',
    () => {
      let ws;
      const sendToRequestLogStub = {};
      const getRegistryObjectStub = {};
      const desktopSocketManagerStub = {};
      const desktopSocketManagerStubSpy = sinon.spy();

      beforeEach(() => {
        ws = wsProxy();
        ws.isPlaywright = true;

        handler.sessionState.set(ws.id, ws);
        sendToRequestLogStub.stub = sinon.stub(handler, 'sendToRequestLog');
        sendToRequestLogStub.stub.returns({});
        getRegistryObjectStub.stub = sinon.stub(handler, 'getRegistryObject');
        desktopSocketManagerStub.stub = sinon.stub(
          handler.desktopSocketManager,
          'emit',
          desktopSocketManagerStubSpy
        );
      });

      afterEach(() => {
        sendToRequestLogStub.stub.restore();
        getRegistryObjectStub.stub.restore();
        desktopSocketManagerStub.stub.restore();
        handler.sessionState.delete(ws.id);
      });

      it('should emit the kSendToDesktop to desktopSocketManger with modified JSON', () => {
        const data = {
          id: 1,
          method: 'BrowserContext',
          params: {}
        };
        getRegistryObjectStub.stub.returns({
          railsSesionId: 'rails1234',
          playwrightVersion: '1.15.2',
        });
        ws[kReqData] = {};
        const socketObjData = { 'client.playwrightVersion': '1.14.1' };
        ws[kReqData].data = socketObjData;
        handler.genericHandler(ws, data);
        sinon.assert.calledOnce(sendToRequestLogStub.stub);
        sinon.assert.calledTwice(getRegistryObjectStub.stub);
        const responseData = {
          id: 1,
          method: 'BrowserContext',
          params: {},
          metadata: { stack: [], apiName: '' }
        };

        sinon.assert.calledOnce(desktopSocketManagerStub.stub);
        sinon.assert.calledWithExactly(desktopSocketManagerStubSpy, kSendToDesktop, ws.id, responseData, sinon.match.func);
      });
    }
  );

  context(
    'modify playwright tracing behaviour for client server version mismatches',
    () => {
      let ws;
      const sendToRequestLogStub = {};
      const getRegistryObjectStub = {};
      const desktopSocketManagerStub = {};
      const desktopSocketManagerStubSpy = sinon.spy();

      beforeEach(() => {
        ws = wsProxy();
        ws.isPlaywright = true;

        handler.sessionState.set(ws.id, ws);
        sendToRequestLogStub.stub = sinon.stub(handler, 'sendToRequestLog');
        sendToRequestLogStub.stub.returns({});
        getRegistryObjectStub.stub = sinon.stub(handler, 'getRegistryObject');
        desktopSocketManagerStub.stub = sinon.stub(
          handler.desktopSocketManager,
          'emit',
          desktopSocketManagerStubSpy
        );
      });

      afterEach(() => {
        sendToRequestLogStub.stub.restore();
        getRegistryObjectStub.stub.restore();
        desktopSocketManagerStub.stub.restore();
        handler.sessionState.delete(ws.id);
      });

      it('should modify tracingStartChunk when client version >= 1.15 and server version >= 1.17', () => {
        const data = {
          id: 1,
          method: 'tracingStartChunk',
          params: undefined
        };
        getRegistryObjectStub.stub.returns({
          railsSesionId: 'rails1234',
          playwrightVersion: '1.17.2',
        });
        ws[kReqData] = {};
        const socketObjData = { 'client.playwrightVersion': '1.16.3' };
        ws[kReqData].data = socketObjData;
        handler.genericHandler(ws, data);
        sinon.assert.calledOnce(sendToRequestLogStub.stub);
        sinon.assert.calledTwice(getRegistryObjectStub.stub);
        const responseData = {
          id: 1,
          method: 'tracingStartChunk',
          params: {}
        };
        sinon.assert.calledOnce(desktopSocketManagerStub.stub);
        sinon.assert.calledWithExactly(desktopSocketManagerStubSpy, kSendToDesktop, ws.id, responseData, callback);
      });

      it('should modify tracingStartChunk when client version >= 1.15 and server version >= 1.15 and < 1.17', () => {
        const data = {
          id: 1,
          method: 'tracingStartChunk',
          params: {}
        };
        getRegistryObjectStub.stub.returns({
          railsSesionId: 'rails1234',
          playwrightVersion: '1.16.3',
        });
        ws[kReqData] = {};
        const socketObjData = { 'client.playwrightVersion': '1.17.2' };
        ws[kReqData].data = socketObjData;
        handler.genericHandler(ws, data);
        sinon.assert.calledOnce(sendToRequestLogStub.stub);
        sinon.assert.calledTwice(getRegistryObjectStub.stub);
        const responseData = {
          id: 1,
          method: 'tracingStartChunk',
          params: undefined
        };
        sinon.assert.calledOnce(desktopSocketManagerStub.stub);
        sinon.assert.calledWithExactly(desktopSocketManagerStubSpy, kSendToDesktop, ws.id, responseData, callback);
      });

      it('should modify tracingStopChunk when client version >= 1.15 and server version >= 1.15 and < 1.18.0', () => {
        const data = {
          id: 1,
          method: 'tracingStopChunk',
          params: { save: true }
        };
        getRegistryObjectStub.stub.returns({
          railsSesionId: 'rails1234',
          playwrightVersion: '1.17.2',
        });
        ws[kReqData] = {};
        const socketObjData = { 'client.playwrightVersion': '1.15.2' };
        ws[kReqData].data = socketObjData;
        handler.genericHandler(ws, data);
        sinon.assert.calledOnce(sendToRequestLogStub.stub);
        sinon.assert.calledTwice(getRegistryObjectStub.stub);
        const responseData = {
          id: 1,
          method: 'tracingStopChunk',
          params: { mode: 'compressTrace', save: true, skipCompress: false }
        };
        sinon.assert.calledOnce(desktopSocketManagerStub.stub);
        sinon.assert.calledWithExactly(desktopSocketManagerStubSpy, kSendToDesktop, ws.id, responseData, callback);
      });

      it('should modify tracingStopChunk when client version >= 1.15 and server version >= 1.18', () => {
        const data = {
          id: 1,
          method: 'tracingStopChunk',
          params: { save: true }
        };
        getRegistryObjectStub.stub.returns({
          railsSesionId: 'rails1234',
          playwrightVersion: '1.18.0',
        });
        ws[kReqData] = {};
        const socketObjData = { 'client.playwrightVersion': '1.16.3' };
        ws[kReqData].data = socketObjData;
        handler.genericHandler(ws, data);
        sinon.assert.calledOnce(sendToRequestLogStub.stub);
        sinon.assert.calledTwice(getRegistryObjectStub.stub);
        const responseData = {
          id: 1,
          method: 'tracingStopChunk',
          params: { mode: 'compressTrace', save: true, skipCompress: false }
        };
        sinon.assert.calledOnce(desktopSocketManagerStub.stub);
        sinon.assert.calledWithExactly(desktopSocketManagerStubSpy, kSendToDesktop, ws.id, responseData, callback);
      });

      it('should not send tracingStartChunk, and emit response id to client when client version >= 1.15 and server version < 1.15', () => {
        const data = {
          id: 1,
          method: 'tracingStartChunk',
          params: {}
        };
        getRegistryObjectStub.stub.returns({
          railsSesionId: 'rails1234',
          playwrightVersion: '1.14.1',
        });
        ws[kReqData] = {};
        const socketObjData = { 'client.playwrightVersion': '1.16.3' };
        ws[kReqData].data = socketObjData;
        handler.genericHandler(ws, data);
        sinon.assert.calledOnce(sendToRequestLogStub.stub);
        sinon.assert.calledTwice(getRegistryObjectStub.stub);
        const responseData = {
          id: 1
        };
        sinon.assert.calledOnce(desktopSocketManagerStub.stub);
        sinon.assert.calledWithExactly(desktopSocketManagerStubSpy, kDesktopDataReceived, ws.id, JSON.stringify(responseData));
      });

      it('should modify tracingStopChunk, and emit response when client version >= 1.15 and server version < 1.15 and >= 1.14 ', () => {
        const data = {
          id: 1,
          method: 'tracingStopChunk',
          params: {}
        };
        getRegistryObjectStub.stub.returns({
          railsSesionId: 'rails1234',
          playwrightVersion: '1.14.1',
        });
        ws[kReqData] = {};
        const socketObjData = { 'client.playwrightVersion': '1.16.3' };
        ws[kReqData].data = socketObjData;
        handler.genericHandler(ws, data);
        sinon.assert.calledOnce(sendToRequestLogStub.stub);
        sinon.assert.calledTwice(getRegistryObjectStub.stub);
        const responseData = {
          id: 1,
          method: 'tracingExport',
          params: undefined
        };
        sinon.assert.calledOnce(desktopSocketManagerStub.stub);
        sinon.assert.calledWithExactly(desktopSocketManagerStubSpy, kSendToDesktop, ws.id, responseData, callback);
      });

      it('should modify tracingStartChunk when client version < 1.19.0 and server version >= 1.19.0', () => {
        const data = {
          id: 1,
          guid: 'browser-context@1234',
          method: 'tracingStartChunk',
          params: {}
        };
        getRegistryObjectStub.stub.returns({
          railsSesionId: 'rails1234',
          playwrightVersion: '1.19.1',
        });
        ws[kReqData] = {};
        const socketObjData = { 'client.playwrightVersion': '1.18.1', 'tracingGuid': 'tracing@1234' };
        ws[kReqData].data = socketObjData;
        handler.genericHandler(ws, data);
        sinon.assert.calledOnce(sendToRequestLogStub.stub);
        sinon.assert.calledTwice(getRegistryObjectStub.stub);
        const responseData = {
          id: 1,
          guid: 'tracing@1234',
          method: 'tracingStartChunk',
          params: {}
        };
        sinon.assert.calledOnce(desktopSocketManagerStub.stub);
        sinon.assert.calledWithExactly(desktopSocketManagerStubSpy, kSendToDesktop, ws.id, responseData, callback);
      });

      it('should modify tracingStartChunk when client version >= 1.19.0 and server version < 1.19.0', () => {
        const data = {
          id: 1,
          guid: 'tracing@1234',
          method: 'tracingStartChunk',
          params: {}
        };
        getRegistryObjectStub.stub.returns({
          railsSesionId: 'rails1234',
          playwrightVersion: '1.18.1',
        });
        ws[kReqData] = {};
        const socketObjData = { 'client.playwrightVersion': '1.19.1', 'browserContextGuid': 'browser-context@1234' };
        ws[kReqData].data = socketObjData;
        handler.genericHandler(ws, data);
        sinon.assert.calledOnce(sendToRequestLogStub.stub);
        sinon.assert.calledTwice(getRegistryObjectStub.stub);
        const responseData = {
          id: 1,
          guid: 'browser-context@1234',
          method: 'tracingStartChunk',
          params: {}
        };
        sinon.assert.calledOnce(desktopSocketManagerStub.stub);
        sinon.assert.calledWithExactly(desktopSocketManagerStubSpy, kSendToDesktop, ws.id, responseData, callback);
      });

      it('should modify metadata.internal when client version < 1.25.1 and server version >= 1.25.1 for initialize method', () => {
        const data = {
          id: 1,
          guid: '',
          method: 'initialize',
          params: { sdkLanguage: 'javascript' },
          metadata : { internal: false}
        }
        getRegistryObjectStub.stub.returns({
          railsSesionId: 'rails1234',
          playwrightVersion: '1.27.1',
        });
        ws[kReqData] = {};
        const socketObjData = { 'client.playwrightVersion': '1.23.1', 'browserContextGuid': 'browser-context@1234' };
        ws[kReqData].data = socketObjData;
        handler.genericHandler(ws, data);
        sinon.assert.calledOnce(sendToRequestLogStub.stub);
        sinon.assert.calledTwice(getRegistryObjectStub.stub);
        const responseData = {
          id: 1,
          guid: '',
          method: 'initialize',
          params: { sdkLanguage: 'javascript' },
          metadata : { internal: true}
        };
        sinon.assert.calledOnce(desktopSocketManagerStub.stub);
        sinon.assert.calledWithExactly(desktopSocketManagerStubSpy, kSendToDesktop, ws.id, responseData, callback);
      });

      it('should modify textContent selector param when client version >= 1.28.1 and server version < 1.28.1', () => {
        const data = {
          id: 5,
          guid: "frame@cae7063582538a0377cbed458feff9b5",
          method: "textContent",
          params: {
            selector: "internal:testid=[data-testid=\"caption-text\"s]",
            strict: true,
          },
          metadata: {
            stack: [],
            apiName: "locator.textContent",
            internal: false,
          },
        }
        getRegistryObjectStub.stub.returns({
          railsSesionId: 'rails1234',
          playwrightVersion: '1.27.1',
        });
        ws[kReqData] = {};
        const socketObjData = { 'client.playwrightVersion': '1.30.0' };
        ws[kReqData].data = socketObjData;
        handler.genericHandler(ws, data);
        sinon.assert.calledOnce(sendToRequestLogStub.stub);
        sinon.assert.calledTwice(getRegistryObjectStub.stub);
        const responseData = {
          id: 5,
          guid: "frame@cae7063582538a0377cbed458feff9b5",
          method: "textContent",
          params: {
            selector: "internal:attr=[data-testid=\"caption-text\"s]",
            strict: true,
          },
          metadata: {
            stack: [],
            apiName: "locator.textContent",
            internal: false,
          },
        }
        sinon.assert.calledOnce(desktopSocketManagerStub.stub);
        sinon.assert.calledWithExactly(desktopSocketManagerStubSpy, kSendToDesktop, ws.id, responseData, callback);
      });

      it('should not modify textContent selector param when client version >= 1.28.1 and server version >= 1.28.1', () => {
        const data = {
          id: 5,
          guid: "frame@cae7063582538a0377cbed458feff9b5",
          method: "textContent",
          params: {
            selector: "internal:testid=[data-testid=\"caption-text\"s]",
            strict: true,
          },
          metadata: {
            stack: [],
            apiName: "locator.textContent",
            internal: false,
          },
        }
        getRegistryObjectStub.stub.returns({
          railsSesionId: 'rails1234',
          playwrightVersion: '1.28.1',
        });
        ws[kReqData] = {};
        const socketObjData = { 'client.playwrightVersion': '1.29.2' };
        ws[kReqData].data = socketObjData;
        handler.genericHandler(ws, data);
        sinon.assert.calledOnce(sendToRequestLogStub.stub);
        sinon.assert.calledTwice(getRegistryObjectStub.stub);
        const responseData = {
          id: 5,
          guid: "frame@cae7063582538a0377cbed458feff9b5",
          method: "textContent",
          params: {
            selector: "internal:testid=[data-testid=\"caption-text\"s]",
            strict: true,
          },
          metadata: {
            stack: [],
            apiName: "locator.textContent",
            internal: false,
          },
        }
        sinon.assert.calledOnce(desktopSocketManagerStub.stub);
        sinon.assert.calledWithExactly(desktopSocketManagerStubSpy, kSendToDesktop, ws.id, responseData, callback);
      });

      it('should remove reason param from browserContext.close for iOS devices with PW version >= 1.40.0', () => {
        const data = {
          id: 5,
          guid: "browserContext@abc123",
          method: "browserContext.close",
          params: {
            reason: "Test closing browser context"
          },
          metadata: {
            stack: [],
            apiName: "browserContext.close",
            internal: false,
          },
        };

        getRegistryObjectStub.stub.returns({
          railsSesionId: 'rails1234',
          playwrightVersion: '1.42.1',
          os: 'iOS',
          deviceName: 'iPhone 14'
        });

        ws[kReqData] = {};
        const socketObjData = { 'client.playwrightVersion': '1.40.0' };
        ws[kReqData].data = socketObjData;

        handler.genericHandler(ws, data);

        sinon.assert.calledOnce(sendToRequestLogStub.stub);
        sinon.assert.calledTwice(getRegistryObjectStub.stub);

        const expectedResponseData = {
          id: 5,
          guid: "browserContext@abc123",
          method: "browserContext.close",
          params: {},
          metadata: {
            stack: [],
            apiName: "browserContext.close",
            internal: false,
          },
        };

        sinon.assert.calledOnce(desktopSocketManagerStub.stub);
        sinon.assert.calledWithExactly(
          desktopSocketManagerStubSpy,
          kSendToDesktop,
          ws.id,
          expectedResponseData,
          callback
        );
      });

      it('should not remove reason param from browserContext.close for non-iOS devices with PW version >= 1.40.0', () => {
        const data = {
          id: 5,
          guid: "browserContext@abc123",
          method: "browserContext.close",
          params: {
            reason: "Test closing browser context"
          },
          metadata: {
            stack: [],
            apiName: "browserContext.close",
            internal: false,
          },
        };

        getRegistryObjectStub.stub.returns({
          railsSesionId: 'rails1234',
          playwrightVersion: '1.42.1',
          os: 'Android',
          deviceName: 'Google Pixel 6'
        });

        ws[kReqData] = {};
        const socketObjData = { 'client.playwrightVersion': '1.40.0' };
        ws[kReqData].data = socketObjData;

        handler.genericHandler(ws, data);

        sinon.assert.calledOnce(sendToRequestLogStub.stub);
        sinon.assert.calledTwice(getRegistryObjectStub.stub);

        const expectedResponseData = {
          id: 5,
          guid: "browserContext@abc123",
          method: "browserContext.close",
          params: {
            reason: "Test closing browser context"
          },
          metadata: {
            stack: [],
            apiName: "browserContext.close",
            internal: false,
          },
        };

        sinon.assert.calledOnce(desktopSocketManagerStub.stub);
        sinon.assert.calledWithExactly(
          desktopSocketManagerStubSpy,
          kSendToDesktop,
          ws.id,
          expectedResponseData,
          callback
        );
      });

      it('should not remove reason param from browserContext.close for desktop sessions with PW version >= 1.40.0', () => {
        const data = {
          id: 5,
          guid: "browserContext@abc123",
          method: "browserContext.close",
          params: {
            reason: "Test closing browser context"
          },
          metadata: {
            stack: [],
            apiName: "browserContext.close",
            internal: false,
          },
        };

        getRegistryObjectStub.stub.returns({
          railsSesionId: 'rails1234',
          playwrightVersion: '1.42.1',
          os: 'windows'
        });

        ws[kReqData] = {};
        const socketObjData = { 'client.playwrightVersion': '1.40.0' };
        ws[kReqData].data = socketObjData;

        handler.genericHandler(ws, data);

        sinon.assert.calledOnce(sendToRequestLogStub.stub);
        sinon.assert.calledTwice(getRegistryObjectStub.stub);

        const expectedResponseData = {
          id: 5,
          guid: "browserContext@abc123",
          method: "browserContext.close",
          params: {
            reason: "Test closing browser context"
          },
          metadata: {
            stack: [],
            apiName: "browserContext.close",
            internal: false,
          },
        };

        sinon.assert.calledOnce(desktopSocketManagerStub.stub);
        sinon.assert.calledWithExactly(
          desktopSocketManagerStubSpy,
          kSendToDesktop,
          ws.id,
          expectedResponseData,
          callback
        );
      });

      it('should not remove reason param from browserContext.close for iOS with PW version < 1.40.0', () => {
        const data = {
          id: 5,
          guid: "browserContext@abc123",
          method: "browserContext.close",
          params: {
            reason: "Test closing browser context"
          },
          metadata: {
            stack: [],
            apiName: "browserContext.close",
            internal: false,
          },
        };

        getRegistryObjectStub.stub.returns({
          railsSesionId: 'rails1234',
          playwrightVersion: '1.39.0',
          os: 'iOS',
          deviceName: 'iPhone 14'
        });

        ws[kReqData] = {};
        const socketObjData = { 'client.playwrightVersion': '1.39.0' };
        ws[kReqData].data = socketObjData;

        handler.genericHandler(ws, data);

        sinon.assert.calledOnce(sendToRequestLogStub.stub);
        sinon.assert.calledTwice(getRegistryObjectStub.stub);

        const expectedResponseData = {
          id: 5,
          guid: "browserContext@abc123",
          method: "browserContext.close",
          params: {
            reason: "Test closing browser context"
          },
          metadata: {
            stack: [],
            apiName: "browserContext.close",
            internal: false,
          },
        };

        sinon.assert.calledOnce(desktopSocketManagerStub.stub);
        sinon.assert.calledWithExactly(
          desktopSocketManagerStubSpy,
          kSendToDesktop,
          ws.id,
          expectedResponseData,
          callback
        );
      });

      it('should remove reason param when guid match browserContext and method close for iOS devices with PW version >= 1.40.0', () => {
        const data = {
          id: 5,
          guid: "browser-context@abc123",
          method: "browserContext.close",
          params: {
            reason: "Test closing browser context"
          }
        };

        getRegistryObjectStub.stub.returns({
          railsSesionId: 'rails1234',
          playwrightVersion: '1.42.1',
          os: 'iOS',
          deviceName: 'iPhone 14'
        });

        ws[kReqData] = {};
        const socketObjData = { 'client.playwrightVersion': '1.40.0' };
        ws[kReqData].data = socketObjData;

        handler.genericHandler(ws, data);

        sinon.assert.calledOnce(sendToRequestLogStub.stub);
        sinon.assert.calledTwice(getRegistryObjectStub.stub);

        const expectedResponseData = {
          id: 5,
          guid: "browserContext@abc123",
          method: "browserContext.close",
          params: {},
          metadata: {
            stack: [],
            apiName: "browserContext.close",
            internal: false,
          },
        };

        sinon.assert.calledOnce(desktopSocketManagerStub.stub);
        sinon.assert.calledWithExactly(
          desktopSocketManagerStubSpy,
          kSendToDesktop,
          ws.id,
          expectedResponseData,
          callback
        );
      });

      it('should not remove reason param when guid match browserContext and method is not close for iOS devices with PW version >= 1.40.0', () => {
        const data = {
          id: 5,
          guid: "browser-context@abc123",
          method: "someothermethod",
          params: {
            reason: "Test closing browser context"
          }
        };

        getRegistryObjectStub.stub.returns({
          railsSesionId: 'rails1234',
          playwrightVersion: '1.42.1',
          os: 'iOS',
          deviceName: 'iPhone 14'
        });

        ws[kReqData] = {};
        const socketObjData = { 'client.playwrightVersion': '1.40.0' };
        ws[kReqData].data = socketObjData;

        handler.genericHandler(ws, data);

        sinon.assert.calledOnce(sendToRequestLogStub.stub);
        sinon.assert.calledTwice(getRegistryObjectStub.stub);

        const expectedResponseData = {
          id: 5,
          guid: "browser-context@abc123",
          method: "someothermethod",
          params: {
            reason: "Test closing browser context"
          }
        };

        sinon.assert.calledOnce(desktopSocketManagerStub.stub);
        sinon.assert.calledWithExactly(
          desktopSocketManagerStubSpy,
          kSendToDesktop,
          ws.id,
          expectedResponseData,
          callback
        );
      });

      it('should not remove reason param when guid match browserContext and method close for non-iOS devices with PW version >= 1.40.0', () => {
        const data = {
          id: 5,
          guid: "browser-context@abc123",
          method: "close",
          params: {
            reason: "Test closing browser context"
          }
        };

        getRegistryObjectStub.stub.returns({
          railsSesionId: 'rails1234',
          playwrightVersion: '1.42.1',
          os: 'Android',
          deviceName: 'Google Pixel 6'
        });

        ws[kReqData] = {};
        const socketObjData = { 'client.playwrightVersion': '1.40.0' };
        ws[kReqData].data = socketObjData;

        handler.genericHandler(ws, data);

        sinon.assert.calledOnce(sendToRequestLogStub.stub);
        sinon.assert.calledTwice(getRegistryObjectStub.stub);

        const expectedResponseData = {
          id: 5,
          guid: "browser-context@abc123",
          method: "close",
          params: {
            reason: "Test closing browser context"
          }
        };

        sinon.assert.calledOnce(desktopSocketManagerStub.stub);
        sinon.assert.calledWithExactly(
          desktopSocketManagerStubSpy,
          kSendToDesktop,
          ws.id,
          expectedResponseData,
          callback
        );
      });

      it('should not remove reason param when guid match browserContext and method close for desktop sessions with PW version >= 1.40.0', () => {
        const data = {
          id: 5,
          guid: "browser-context@abc123",
          method: "close",
          params: {
            reason: "Test closing browser context"
          }
        };

        getRegistryObjectStub.stub.returns({
          railsSesionId: 'rails1234',
          playwrightVersion: '1.42.1',
          os: 'windows'
        });

        ws[kReqData] = {};
        const socketObjData = { 'client.playwrightVersion': '1.40.0' };
        ws[kReqData].data = socketObjData;

        handler.genericHandler(ws, data);

        sinon.assert.calledOnce(sendToRequestLogStub.stub);
        sinon.assert.calledTwice(getRegistryObjectStub.stub);

        const expectedResponseData = {
          id: 5,
          guid: "browser-context@abc123",
          method: "close",
          params: {
            reason: "Test closing browser context"
          }
        };

        sinon.assert.calledOnce(desktopSocketManagerStub.stub);
        sinon.assert.calledWithExactly(
          desktopSocketManagerStubSpy,
          kSendToDesktop,
          ws.id,
          expectedResponseData,
          callback
        );
      });

      it('should not remove reason param when guid match browserContext and method close for iOS with PW version < 1.40.0', () => {
        const data = {
          id: 5,
          guid: "browser-context@abc123",
          method: "close",
          params: {
            reason: "Test closing browser context"
          }
        };

        getRegistryObjectStub.stub.returns({
          railsSesionId: 'rails1234',
          playwrightVersion: '1.39.0',
          os: 'iOS',
          deviceName: 'iPhone 14'
        });

        ws[kReqData] = {};
        const socketObjData = { 'client.playwrightVersion': '1.39.0' };
        ws[kReqData].data = socketObjData;

        handler.genericHandler(ws, data);

        sinon.assert.calledOnce(sendToRequestLogStub.stub);
        sinon.assert.calledTwice(getRegistryObjectStub.stub);

        const expectedResponseData = {
          id: 5,
          guid: "browser-context@abc123",
          method: "close",
          params: {
            reason: "Test closing browser context"
          }
        };

        sinon.assert.calledOnce(desktopSocketManagerStub.stub);
        sinon.assert.calledWithExactly(
          desktopSocketManagerStubSpy,
          kSendToDesktop,
          ws.id,
          expectedResponseData,
          callback
        );
      });
    }
  );

  context(
    'For PW performance',
    () => {
      let ws;
      const sendToRequestLogStub = {};
      const getRegistryObjectStub = {};

      beforeEach(() => {
        ws = wsProxy();
        ws.isPlaywright = true;

        handler.sessionState.set(ws.id, ws);
        sendToRequestLogStub.stub = sinon.stub(handler, 'sendToRequestLog');
        sendToRequestLogStub.stub.returns({});
        getRegistryObjectStub.stub = sinon.stub(handler, 'getRegistryObject');
      });

      afterEach(() => {
        sendToRequestLogStub.stub.restore();
        getRegistryObjectStub.stub.restore();
        handler.sessionState.delete(ws.id);
      });

      it('should modify metadata.internal when client version < 1.25.1 and server version >= 1.25.1 for initialize method', () => {
        const data = {
          id: 1,
          guid: '',
          method: 'initialize',
          params: { sdkLanguage: 'javascript' },
          metadata : { internal: false}
        }

        const keyObject = {
          railsSesionId: 'rails1234',
          playwrightVersion: '1.27.1',
        };

        getRegistryObjectStub.stub.returns(keyObject);
        ws[kReqData] = {};
        const socketObjData = { 'client.playwrightVersion': '1.23.1', 'browserContextGuid': 'browser-context@1234' };
        ws[kReqData].data = socketObjData;
        // wsStub will take send method and do nothing
        const wsStub = {
          send: sinon.stub()
        };
        handler.desktopSocketManager.socketsStates.set("1234", wsStub);
        handler.genericHandler(ws, data, { reqMetaData: { startTime: process.hrtime.bigint() } });
        expect(keyObject.C2PprocessingTime).to.be.greaterThan(0);
        sinon.assert.calledOnce(wsStub.send);
        sinon.assert.calledOnce(sendToRequestLogStub.stub);
        sinon.assert.calledTwice(getRegistryObjectStub.stub);
      });

      it('Cover empty callback as well', () => {
        const data = {
          id: 1,
          guid: '',
          method: 'initialize',
          params: { sdkLanguage: 'javascript' },
          metadata : { internal: false}
        }

        const keyObject = {
          railsSesionId: 'rails1234',
          playwrightVersion: '1.27.1',
        };
        ws.isPlaywright = false;
        getRegistryObjectStub.stub.returns(keyObject);
        ws[kReqData] = {};
        const socketObjData = { 'client.playwrightVersion': '1.23.1', 'browserContextGuid': 'browser-context@1234' };
        ws[kReqData].data = socketObjData;
        // wsStub will take send method and do nothing
        const wsStub = {
          send: sinon.stub()
        };
        handler.desktopSocketManager.socketsStates.set("1234", wsStub);
        handler.genericHandler(ws, data, { reqMetaData: { startTime: process.hrtime.bigint() } });
        sinon.assert.calledOnce(wsStub.send);
        sinon.assert.calledOnce(sendToRequestLogStub.stub);
      });
    });
});
