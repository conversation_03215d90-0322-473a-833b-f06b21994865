'use strict';

const sinon = require('sinon');
const HA = require('../../../ha');
const rewire = require('rewire');
const rewiredHandler = rewire('../../../socketManagers/puppeteerHandler');
const { assert } = require('chai');
const { mockLoggerSpies: mockLogger } = require('./socketHelpers');

describe('Initial message handler for terminal allocation', () => {
  let puppeteerHandler;
  const PuppeteerHandler = rewiredHandler.__get__('PuppeteerHandler');
  let originalAllocator;
  let originalBasicLogger;

  beforeEach(() => {
    puppeteerHandler = new PuppeteerHandler();
    originalAllocator = rewiredHandler.__get__('allocateTerminal');
    originalBasicLogger = rewiredHandler.__get__('logger');

    rewiredHandler.__set__('logger', mockLogger);
  });

  afterEach(() => {
    rewiredHandler.__set__('allocateTerminal', originalAllocator);
    rewiredHandler.__set__('logger', originalBasicLogger);
  });

  it('if new request them allocate the terminal', async () => {
    const wsProxy = {
      id: '1234',
    };
    const messageData = {
      method: 'Browser.getContexts',
      params: []
    };
    const successData = {
      status: 0,
      sessionId: '1234_selenium',
      value: {
        wsURL: 'ws://random-host.com',
      },
    };
    const allocatorStub = sinon.stub();
    allocatorStub.returns(new Promise((res) => {
      res(successData);
    }));

    rewiredHandler.__set__('allocateTerminal', allocatorStub);
    sinon.stub(puppeteerHandler, 'processData', () => {});
    sinon.stub(puppeteerHandler, 'connectToDesktopSocket', () => {});
    await puppeteerHandler.messageHandler(wsProxy, JSON.stringify(messageData));

    assert(puppeteerHandler.processData.calledOnce);
    assert(puppeteerHandler.connectToDesktopSocket.calledOnce);

    puppeteerHandler.processData.restore();
    puppeteerHandler.connectToDesktopSocket.restore();
  });

  it('should trigger lighthouse automate', async () => {
    const wsProxy = {
      id: '1234',
    };
    const messageData = {
      method: 'Page.navigate',
      params: []
    };
    const successData = {
      status: 0,
      sessionId: '1234_selenium',
      value: {
        wsURL: 'ws://random-host.com',
      },
    };
    const allocatorStub = sinon.stub();
    allocatorStub.returns(new Promise((res) => {
      res(successData);
    }));

    rewiredHandler.__set__('allocateTerminal', allocatorStub);
    sinon.stub(puppeteerHandler, 'getRegistryObject').returns({lighthouseAutomate: { report_limit: 5 }});
    sinon.stub(puppeteerHandler, 'processData', () => {});
    sinon.stub(puppeteerHandler, 'connectToDesktopSocket', () => {});
    await puppeteerHandler.messageHandler(wsProxy, JSON.stringify(messageData));

    assert(puppeteerHandler.processData.calledOnce);
    assert(puppeteerHandler.connectToDesktopSocket.calledOnce);

    puppeteerHandler.processData.restore();
    puppeteerHandler.connectToDesktopSocket.restore();
    puppeteerHandler.getRegistryObject.restore();
  });

  it('if ws url is undefined then close the socket', async () => {
    const wsProxy = {
      id: '1234',
      close: sinon.spy(),
    };
    const messageData = {
      method: 'Browser.getContexts',
      params: []
    };
    const successData = {
      status: 0,
      sessionId: '1234_selenium',
      value: {},
    };
    const allocatorStub = sinon.stub();
    allocatorStub.returns(new Promise((res) => {
      res(successData);
    }));

    rewiredHandler.__set__('allocateTerminal', allocatorStub);
    sinon.stub(puppeteerHandler, 'processData', () => {});
    sinon.stub(puppeteerHandler, 'connectToDesktopSocket', () => {});
    await puppeteerHandler.messageHandler(wsProxy, JSON.stringify(messageData));

    assert(puppeteerHandler.processData.notCalled);
    assert(puppeteerHandler.connectToDesktopSocket.notCalled);
    assert(wsProxy.close.calledOnce);

    puppeteerHandler.processData.restore();
    puppeteerHandler.connectToDesktopSocket.restore();
  });

  it('should close the socket because keyObject not found in redis', async () => {
    const wsProxy = {
      id: '1234',
      close: sinon.spy(),
    };
    const messageData = {
      method: 'Browser.getContexts',
      params: []
    };
    const successData = {
      status: 0,
      sessionId: '1234_selenium',
      value: {
        wsURL: 'ws://localhost:9222/json/version',
      },
    };
    const allocatorStub = sinon.stub();
    allocatorStub.returns(new Promise((res) => {
      res(successData);
    }));

    sinon.stub(HA, 'getData').yields(new Error('unable to find object'), null);

    rewiredHandler.__set__('allocateTerminal', allocatorStub);
    sinon.stub(puppeteerHandler, 'processData', () => {});
    sinon.stub(puppeteerHandler, 'connectToDesktopSocket', () => {});
    await puppeteerHandler.messageHandler(wsProxy, JSON.stringify(messageData));

    assert(puppeteerHandler.processData.notCalled);
    assert(puppeteerHandler.connectToDesktopSocket.calledOnce);
    assert(wsProxy.close.calledOnce);

    puppeteerHandler.processData.restore();
    puppeteerHandler.connectToDesktopSocket.restore();

    HA.getData.restore();
  });

  it('should reject the promise if selenium is not able to start', async () => {
    const wsProxy = {
      id: '1234',
      close: sinon.spy(),
    };
    const messageData = {
      method: 'Browser.getContexts',
      params: []
    };
    const successData = {
      status: 13,
      sessionId: '1234_selenium',
      value: {
        wsURL: 'ws://random-host.com',
        message: 'Unable to start session',
      },
    };
    const allocatorStub = sinon.stub();
    allocatorStub.returns(new Promise((res) => {
      res(successData);
    }));

    rewiredHandler.__set__('allocateTerminal', allocatorStub);
    sinon.stub(puppeteerHandler, 'processData', () => {});
    sinon.stub(puppeteerHandler, 'connectToDesktopSocket', () => {});
    await puppeteerHandler.messageHandler(wsProxy, JSON.stringify(messageData));
    assert(puppeteerHandler.processData.notCalled);
    assert(puppeteerHandler.connectToDesktopSocket.notCalled);
    assert(wsProxy.close.calledOnce);
    puppeteerHandler.processData.restore();
    puppeteerHandler.connectToDesktopSocket.restore();
  });

  it('if not able to allocate reject the connection', async () => {
    const wsProxy = {
      id: '1234',
      close: sinon.spy(),
      send: sinon.spy(),
    };
    const messageData = {
      id: 1,
      method: 'Browser.getContexts',
      params: []
    };
    const allocatorStub = sinon.stub();
    allocatorStub.returns(new Promise((_, rej) => {
      rej('Unable to allocate Terminal');
    }));

    rewiredHandler.__set__('allocateTerminal', allocatorStub);
    sinon.stub(puppeteerHandler, 'processData', () => {});
    sinon.stub(puppeteerHandler, 'connectToDesktopSocket', () => {});
    await puppeteerHandler.messageHandler(wsProxy, JSON.stringify(messageData));

    assert(puppeteerHandler.processData.notCalled);
    assert(puppeteerHandler.connectToDesktopSocket.notCalled);
    assert(wsProxy.send.calledOnce);
    assert(wsProxy.close.calledOnce);

    puppeteerHandler.processData.restore();
    puppeteerHandler.connectToDesktopSocket.restore();
  });

  it('if not able to allocate reject the connection, but not write to socket if id is not present', async () => {
    const wsProxy = {
      id: '1234',
      close: sinon.spy(),
      send: sinon.spy(),
    };
    const messageData = {
      method: 'Browser.getContexts',
      params: []
    };
    const allocatorStub = sinon.stub();
    allocatorStub.returns(new Promise((_, rej) => {
      rej('Unable to allocate Terminal');
    }));

    rewiredHandler.__set__('allocateTerminal', allocatorStub);
    sinon.stub(puppeteerHandler, 'processData', () => {});
    sinon.stub(puppeteerHandler, 'connectToDesktopSocket', () => {});
    await puppeteerHandler.messageHandler(wsProxy, JSON.stringify(messageData));

    assert(puppeteerHandler.processData.notCalled);
    assert(puppeteerHandler.connectToDesktopSocket.notCalled);
    assert(wsProxy.send.notCalled);
    assert(wsProxy.close.calledOnce);

    puppeteerHandler.processData.restore();
    puppeteerHandler.connectToDesktopSocket.restore();
  });
});

describe('eager close for puppeteer session', () => {
  let puppeteerHandler;
  const PuppeteerHandler = rewiredHandler.__get__('PuppeteerHandler');
  let originalRegistry;

  beforeEach(() => {
    puppeteerHandler = new PuppeteerHandler();
    originalRegistry = rewiredHandler.__get__('globalRegistry');
  });

  afterEach(() => {
    rewiredHandler.__set__('globalRegistry', originalRegistry);
  });

  it('should eager close the connection', async () => {
    const wsObj = {
      id: '1234',
    };

    const socketData = {
      id: 40,
      method: 'Browser.close',
      params: [],
    };

    rewiredHandler.__set__('globalRegistry', {
      1234: {
        rails_session_id: '1234-rails',
      },
    });
    sinon.stub(puppeteerHandler, 'setAllocationPromise', () => {});
    sinon.stub(puppeteerHandler, 'writeResponse', () => {});
    sinon.stub(puppeteerHandler, 'closeTerminalSocket', () => {});
    sinon.stub(puppeteerHandler, 'startTerminalCleanup', () => {});
    await puppeteerHandler.messageHandler(wsObj, JSON.stringify(socketData));

    assert(puppeteerHandler.writeResponse.calledOnce);
    const args = puppeteerHandler.writeResponse.getCall(0).args[1];
    assert.deepEqual(args, { id: socketData.id, method: socketData.method, result: {} });
    assert(puppeteerHandler.closeTerminalSocket.calledOnce);
    assert(puppeteerHandler.startTerminalCleanup.calledOnce);

    puppeteerHandler.writeResponse.restore();
    puppeteerHandler.closeTerminalSocket.restore();
    puppeteerHandler.startTerminalCleanup.restore();
    puppeteerHandler.setAllocationPromise.restore();
  });
});

describe('should close the socket if not found in registry', () => {
  let puppeteerHandler;
  const PuppeteerHandler = rewiredHandler.__get__('PuppeteerHandler');
  let originalRegistry;

  beforeEach(() => {
    puppeteerHandler = new PuppeteerHandler();
    originalRegistry = rewiredHandler.__get__('globalRegistry');
  });

  afterEach(() => {
    rewiredHandler.__set__('globalRegistry', originalRegistry);
  });

  it('should not close the socket connection', async () => {
    const wsObj = {
      id: 1234,
      close: sinon.spy(),
    };

    const socketData = {
      id: 40,
      sessionId: '1234',
      method: 'Page.captureScrenshots',
      params: [],
    };

    rewiredHandler.__set__('globalRegistry', {
      12345: {
        rails_session_id: '1234-rails',
      },
    });
    sinon.stub(puppeteerHandler, 'setAllocationPromise', () => {});
    sinon.stub(puppeteerHandler, 'writeResponse', () => {});
    sinon.stub(puppeteerHandler, 'closeTerminalSocket', () => {});
    sinon.stub(puppeteerHandler, 'startTerminalCleanup', () => {});
    await puppeteerHandler.messageHandler(wsObj, JSON.stringify(socketData));

    assert(wsObj.close.notCalled);
    puppeteerHandler.writeResponse.restore();
    puppeteerHandler.closeTerminalSocket.restore();
    puppeteerHandler.startTerminalCleanup.restore();
    puppeteerHandler.setAllocationPromise.restore();
  });
});
