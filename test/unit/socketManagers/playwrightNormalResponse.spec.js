'use strict';

/* eslint-disable no-underscore-dangle */

const { expect } = require('chai');
const sinon = require('sinon');
const rewire = require('rewire');
const { wsProxy } = require('./socketHelpers');
const { kStartTimer } = require('../../../config/socketConstants');

const handler = rewire('../../../socketManagers/playwrightHandler');

describe('Playwright response log', () => {
  const PLAYWRIGHT_HANDLER = handler.__get__('PlaywrightHandler');
  const SOCKET_MANAGER = handler.__get__('SocketMessageHandler');
  let obj;
  let socketObj;
  const startSpy = sinon.spy();
  const RAILS_SESSION_ID = '1234-rails';
  const method = 'screenshot';
  let data;
  let keyObject;
  const mockSendToKafka = {};
  const mockTakeScreenshot = {};
  let stateData;

  const superClassMethod = [
    'addRequestLog', 'addNormalResponse', 'addDebugLog', 'takeScreenshotHandler'
  ];

  beforeEach(() => {
    obj = new PLAYWRIGHT_HANDLER();
    socketObj = wsProxy();
    data = {
      id: 123,
      params: {
        key: 'value'
      },
      result: {
        binary: "abcd"
      }
    };
    obj.on(kStartTimer, startSpy);
    obj.railsSessionMapping.set(socketObj.id, RAILS_SESSION_ID);
    handler.__set__('globalRegistry', {});
    superClassMethod.forEach((methodName) => {
      sinon.stub(SOCKET_MANAGER.prototype, methodName);
    });
    mockTakeScreenshot.original = handler.__get__('takeScreenshotAndUpload');
    mockTakeScreenshot.stub = sinon.stub();
    mockSendToKafka.stub = sinon.spy(obj, 'sendToKafka');
    handler.__set__('takeScreenshotAndUpload', mockTakeScreenshot.stub);
    stateData = {
      123: {
        request: {
          method
        },
        response: {},
        debug: 'debug'
      }
    };
    obj.rawLogState.set(RAILS_SESSION_ID, stateData);
  });

  afterEach(() => {
    startSpy.reset();
    obj.railsSessionMapping.clear();
    obj.rawLogState.clear();
    mockSendToKafka.stub.restore();
    superClassMethod.forEach(methodName => SOCKET_MANAGER.prototype[methodName].restore());
    handler.__set__('takeScreenshotAndUpload', mockTakeScreenshot.original);
  });

  it('should not send event if stop is present', () => {
    obj.addNormalResponse(data, keyObject, RAILS_SESSION_ID, { stop: true });
    expect(mockSendToKafka.stub.calledOnce).to.be.false;
  });

  it('should send to kafka if response request is present in state', () => {
    obj.addNormalResponse(data, keyObject, RAILS_SESSION_ID);
    expect(mockSendToKafka.stub.calledOnce).to.be.true;
  });

  it('should not send to kafka if the request is not founded', () => {
    obj.rawLogState.clear();
    obj.addNormalResponse(data, keyObject, RAILS_SESSION_ID);
    expect(mockSendToKafka.stub.calledOnce).to.be.false;
  });

  it('If debug state not present, should not call takeScreenshotAndUpload', () => {
    delete stateData['123'].debug;
    obj.addNormalResponse(data, keyObject, RAILS_SESSION_ID);
    expect(mockSendToKafka.stub.calledOnce).to.be.true;
    expect(mockTakeScreenshot.stub.calledOnce).to.be.false;
  });

  it('If debug is present, then only should take the screenshot for the allowed method', () => {
    obj.debugScreenshotMethodMapping.add(method);
    obj.addNormalResponse(data, keyObject, RAILS_SESSION_ID);
    expect(mockSendToKafka.stub.calledOnce).to.be.true;
    expect(mockTakeScreenshot.stub.calledOnce).to.be.true;
  });

  it('If debug is present, and method is not present in debugScreenshotMethodMapping', () => {
    obj.addNormalResponse(data, keyObject, RAILS_SESSION_ID);
    expect(mockSendToKafka.stub.calledOnce).to.be.true;
    expect(mockTakeScreenshot.stub.calledOnce).to.be.false;
  });

  it('should not send to kafka if method is in ignore method', () => {
    stateData['123'].method = 'read';
    obj.rawLogState.set(RAILS_SESSION_ID, stateData);
    obj.addNormalResponse(data, keyObject, RAILS_SESSION_ID);
    expect(mockSendToKafka.stub.calledOnce).to.be.false;
  });

  it('should send to kafka if method is not in ignore method', () => {
    stateData['123'].method = 'goto';
    obj.rawLogState.set(RAILS_SESSION_ID, stateData);
    obj.addNormalResponse(data, keyObject, RAILS_SESSION_ID);
    expect(mockSendToKafka.stub.calledOnce).to.be.true;
  });
});
