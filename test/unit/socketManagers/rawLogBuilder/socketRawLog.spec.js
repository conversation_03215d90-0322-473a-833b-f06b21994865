'use strict';

const SocketRawLog = require('../../../../socketManagers/socketRawLog');
const { NAVIGATE_TO, GENERIC } = require('../../../../helpers/customSeleniumHandling/frameworkConstants');
const sinon = require('sinon');
const { assert, expect } = require('chai');

describe('Base socket raw log handler', () => {
  let logHandler;
  const SESSION_ID = 'sessionid-1234';
  const methodName = 'Page.navigate';

  beforeEach(() => {
    sinon.stub(SocketRawLog.prototype, 'generateMethodLogMapping', () => {
    });
    sinon.stub(SocketRawLog.prototype, 'generatePayloadMapping');

    logHandler = new SocketRawLog();
  });

  afterEach(() => {
    SocketRawLog.prototype.generateMethodLogMapping.restore();
    SocketRawLog.prototype.generatePayloadMapping.restore();
  });

  describe('Generate method log mapping', () => {
    afterEach(() => {
      logHandler.methodLogMap.clear();
    });

    it('should generate the correct command type if method log exists', () => {
      logHandler.methodLogMap.set(methodName, NAVIGATE_TO);

      const response = logHandler.generateAnnotateArgs(SESSION_ID, methodName);
      const expectedResponse = {
        requestStateObj: { url: '/session/sessionid-1234/execute' },
        commandType: 'NAVIGATE_TO',
      };

      expect(response).to.deep.equal(expectedResponse);
    });

    it('if no mapping for method is found then use GENERIC type', () => {
      const response = logHandler.generateAnnotateArgs(SESSION_ID, methodName);
      const expectedResponse = {
        requestStateObj: { url: '/session/sessionid-1234/execute' },
        commandType: GENERIC,
      };

      expect(response).to.deep.equal(expectedResponse);
    });
  });

  describe('Get the log string for method', () => {
    const sampleData = {
      key: 'OK value',
    };

    let clock;

    beforeEach(() => {
      const date = new Date(1970, 0, 1, 1);
      clock = sinon.useFakeTimers(date.getTime());
    });

    afterEach(() => {
      clock.restore();
    });

    it('should create entry for request', () => {
      sinon.spy(SocketRawLog, 'isRequest');
      sinon.spy(SocketRawLog, 'isResponse');

      const resp = logHandler.updateLogEntry('REQUEST', methodName, {
        sessionId: SESSION_ID,
        data: sampleData,
      });

      const expected = '1970-1-1 1:0:0:0 REQUEST [1970-1-1 1:0:0:0] POST /session/sessionid-1234/execute {"text":{"key":"OK value"}}';
      expect(resp).to.be.eql(expected);

      assert(SocketRawLog.isRequest.calledOnce);
      assert(SocketRawLog.isResponse.notCalled);

      SocketRawLog.isRequest.restore();
      SocketRawLog.isResponse.restore();
    });

    it('should create entry for response', () => {
      sinon.spy(SocketRawLog, 'isRequest');
      sinon.spy(SocketRawLog, 'isResponse');

      const resp = logHandler.updateLogEntry('RESPONSE', methodName, {
        sessionId: SESSION_ID,
        data: sampleData,
      });

      const expected = '1970-1-1 1:0:0:0 RESPONSE {"text":{"key":"OK value"}}';
      expect(resp).to.be.eql(expected);

      assert(SocketRawLog.isRequest.calledOnce);
      assert(SocketRawLog.isResponse.calledOnce);

      SocketRawLog.isRequest.restore();
      SocketRawLog.isResponse.restore();
    });

    it('if ambigous type is sent then return the default string', () => {
      sinon.spy(SocketRawLog, 'isRequest');
      sinon.spy(SocketRawLog, 'isResponse');

      const resp = logHandler.updateLogEntry('SOME_RANDOM_TYPE', methodName, {
        sessionId: SESSION_ID,
        data: sampleData,
      });

      const expected = 'SOME_RANDOM_TYPE Page.navigate {"key":"OK value"}';
      expect(resp).to.be.eql(expected);

      assert(SocketRawLog.isRequest.calledOnce);
      assert(SocketRawLog.isResponse.calledOnce);

      SocketRawLog.isRequest.restore();
      SocketRawLog.isResponse.restore();
    });
  });
});

describe('Method mapping for raw log', () => {
  it('should throw error since the methodLogMapping does not exists', () => {
    try {
      class DummyClass extends SocketRawLog {
      }

      const obj = new DummyClass();
      assert(false);
    } catch (err) {
      expect(err.message).to.be.eql('Need to be implemented by the sub-classes');
    }
  });
});

describe('#generateMethodLogMapping', () => {
  it('should be implemented by the sub-classes', () => {
    try {
      SocketRawLog.prototype.generateMethodLogMapping();
    } catch (error) {
      expect(error.message).to.be.eql('Need to be implemented by the sub-classes');
    }
  });
});

describe('#generatePayloadMapping', () => {
  it('should be implemented by the sub-classes', () => {
    try {
      SocketRawLog.prototype.generatePayloadMapping();
    } catch (error) {
      expect(error.message).to.be.eql('Need to be implemented by the sub-classes');
    }
  });
});
