'use strict';

const PuppeteerRawLog = require('../../../../socketManagers/puppeteerRawLog');
const { expect } = require('chai');
const sinon = require('sinon');
const SocketRawLog =  require('../../../../socketManagers/socketRawLog')

describe('Puppeteer Raw Log Service', () => {
  let handler;
  let clock;
  const SESSION_ID = 'session-1234';

  beforeEach(() => {
    handler = new PuppeteerRawLog();
    const date = new Date(1970, 0, 1, 1);
    clock = sinon.useFakeTimers(date.getTime());
  });

  afterEach(() => {
    clock.restore();
  });

  it('Should render correct stiring for open url', () => {
    const data = {
      method: 'Page.navigate',
      params: {
        url: 'https://google.com',
      },
    };

    const resp = handler.updateLogEntry('REQUEST', 'Page.navigate', {
      sessionId: SESSION_ID,
      data,
    });

    const expected = '1970-1-1 1:0:0:0 REQUEST [1970-1-1 1:0:0:0] POST /session/session-1234/url {"url":"https://google.com"}';

    expect(resp).to.be.eql(expected);
  });

  it('in case of response should fetch data from result', () => {
    const data = {
      id: 1,
      result: {
        sessionId: SESSION_ID,
      },
    };

    const resp = handler.updateLogEntry('RESPONSE', undefined, {
      sessionId: SESSION_ID,
      data,
    });

    const expected = '1970-1-1 1:0:0:0 RESPONSE {"text":{"sessionId":"session-1234"}}';

    expect(resp).to.be.eql(expected);
  });

  it('in case of stop should print the data', () => {
    const data = {
      id: 1,
      results: {
        sessionId: SESSION_ID,
      },
    };

    const resp = handler.updateLogEntry('STOP', undefined, {
      sessionId: SESSION_ID,
      data,
    });

    const expected = '1970-1-1 1:0:0:0 STOP_SESSION {"text":{"id":1,"results":{"sessionId":"session-1234"}}}';

    expect(resp).to.be.eql(expected);
  });

  it('in case of takeScreenshot print without the \'text\' syntax', () => {
    const data = {
      id: 1,
      result: {
        sessionId: SESSION_ID,
        value: 's3_url_of_ss'
      },
    };
  
    const resp = handler.updateLogEntry('RESPONSE', 'resp-Page.captureScreenshot', {
      sessionId: SESSION_ID,
      data,
    });
  
    const expected = '1970-1-1 1:0:0:0 RESPONSE {"value":"s3_url_of_ss"}';
  
    expect(resp).to.be.eql(expected);
  })

  it('If no valid type is passed then return the geenric response', () => {
    const data = {
      id: 1,
      results: {
        sessionId: SESSION_ID,
      },
    };

    const resp = handler.updateLogEntry('SOME_RANDOM_TYPE', undefined, {
      sessionId: SESSION_ID,
      data,
    });

    const expected = 'SOME_RANDOM_TYPE undefined {"id":1,"results":{"sessionId":"session-1234"}}';

    expect(resp).to.be.eql(expected);
  });

  it('should truncate performance data if truncate is passed', () => {
    const data = {
      id: 1,
      result: {
        sessionId: SESSION_ID,
        result: {
          type: 'string',
          value: '{ "status": "passed", "report": "NOT_TRUNCATED" }'
        }
      },
      performance: {
        enabled: true,
        truncate: true
      }
    };
  
    const resp = handler.updateLogEntry('RESPONSE', 'evaluateExpression', {
      sessionId: SESSION_ID,
      data,
    });
  
    const expected = '1970-1-1 1:0:0:0 RESPONSE {"value":{"status":"passed","report":"TRUNCATED"}}';
    expect(resp).to.be.eql(expected);
  });
});

describe('navigate', () => {
  const EXECUTE_NAVIGATE = 'Page.navigate';
  const mockSetNavigate = {};
  let obj,data;
  beforeEach(() => {
    mockSetNavigate.stub = sinon.spy(PuppeteerRawLog, 'navigate');
    obj = new PuppeteerRawLog();
    data = {
      url: "xyz.com"
    };
  });

  afterEach(() => {
    mockSetNavigate.stub.restore();
  });

  it('Should call the navigate method and return correct data', () => {
    const resp = obj.fetchPayloadHandler(EXECUTE_NAVIGATE)(data);
    const expectedResponse = {
      url: "xyz.com"
    };
    expect(mockSetNavigate.stub.calledOnce).to.be.true;
    expect(resp).to.deep.eql(expectedResponse);
  });

  it('If nothing is passed then return an empty string', () => {
    data = {};
    const resp = obj.fetchPayloadHandler(EXECUTE_NAVIGATE)(data);
    const expectedResponse = {
      url: ''
    };
    expect(mockSetNavigate.stub.calledOnce).to.be.true;
    expect(resp).to.deep.eql(expectedResponse);
  });
});

describe('dispatchMouseEvent', () => {
  const EXECUTE_MOUSE_EVENT = 'Input.dispatchMouseEvent';
  const mockSetMouseEvent = {};
  let obj,data;
  beforeEach(() => {
    mockSetMouseEvent.stub = sinon.spy(PuppeteerRawLog, 'dispatchMouseEvent');
    obj = new PuppeteerRawLog();
    data = {
      type: '#input',
      x: 10,
      y: 20
    };
  });

  afterEach(() => {
    mockSetMouseEvent.stub.restore();
  });

  it('Should call the dispatchMouseEvent method and return correct data', () => {
    const resp = obj.fetchPayloadHandler(EXECUTE_MOUSE_EVENT)(data);
    const expectedResponse = {
      mouseEvent: '#input',
      x: 10,
      y: 20
    };
    expect(mockSetMouseEvent.stub.calledOnce).to.be.true;
    expect(resp).to.deep.eql(expectedResponse);
  });

  it('If nothing is passed then return the default data', () => {
    data = {};
    const resp = obj.fetchPayloadHandler(EXECUTE_MOUSE_EVENT)(data);
    const expectedResponse = {
      mouseEvent: '',
      x: 0,
      y: 0
    };
    expect(mockSetMouseEvent.stub.calledOnce).to.be.true;
    expect(resp).to.deep.eql(expectedResponse);
  });
});

describe('dispatchTouchEvent', () => {
  const EXECUTE_TOUCH_EVENT = 'Input.dispatchTouchEvent';
  const mockSetTouchEvent = {};
  let obj,data1,data2;
  beforeEach(() => {
    mockSetTouchEvent.stub = sinon.spy(PuppeteerRawLog, 'dispatchTouchEvent');
    obj = new PuppeteerRawLog();
    data1 = {
      type: 'touchStart',
      touchPoints: [
      {x: 10, y: 20}
      ]
    };
    data2 = {
      type: 'touchEnd'
    }
  });

  afterEach(() => {
    mockSetTouchEvent.stub.restore();
  });

  it('Should call the dispatchTouchEvent method and return x and y parameters if type is touchStart', () => {
    const resp = obj.fetchPayloadHandler(EXECUTE_TOUCH_EVENT)(data1);
    const expectedResponse = {
      type: 'touchStart',
      touchPoints: [
      {x: 10, y: 20}
      ]
    };
    expect(mockSetTouchEvent.stub.calledOnce).to.be.true;
    expect(resp).to.deep.eql(expectedResponse);
  });

  it('Should call the dispatchTouchEvent method and return only type, if type is not touchStart', () => {
    const resp = obj.fetchPayloadHandler(EXECUTE_TOUCH_EVENT)(data2);
    const expectedResponse = {
      type: 'touchEnd'
    };
    expect(mockSetTouchEvent.stub.calledOnce).to.be.true;
    expect(resp).to.eql(expectedResponse);
  });

  it('If type is touchStart and nothing is passed then return the empty hash list', () => {
    data1 = {
      type: 'touchStart'
    };
    const resp = obj.fetchPayloadHandler(EXECUTE_TOUCH_EVENT)(data1);
    const expectedResponse = {
      type: 'touchStart',
      touchPoints: [ {} ]
    };
    expect(mockSetTouchEvent.stub.calledOnce).to.be.true;
    expect(resp).to.deep.eql(expectedResponse);
  });

  it('If type is not touchStart and nothing is passed then return the empty string', () => {
    data2 = {};
    const resp = obj.fetchPayloadHandler(EXECUTE_TOUCH_EVENT)(data2);
    const expectedResponse = {
      type: ''
    };
    expect(mockSetTouchEvent.stub.calledOnce).to.be.true;
    expect(resp).to.deep.eql(expectedResponse);
  });
});

describe('insertText', () => {
  const EXECUTE_INSERT_TEXT = 'Input.insertText';
  const mockSetInsertText = {};
  let obj,data;
  beforeEach(() => {
    mockSetInsertText.stub = sinon.spy(PuppeteerRawLog, 'insertText');
    obj = new PuppeteerRawLog();
    data = {
      text: "abcd"
    };
  });

  afterEach(() => {
    mockSetInsertText.stub.restore();
  });

  it('Should call the insertText method and return correct data', () => {
    const resp = obj.fetchPayloadHandler(EXECUTE_INSERT_TEXT)(data);
    const expectedResponse = {
      text: "abcd"
    };
    expect(mockSetInsertText.stub.calledOnce).to.be.true;
    expect(resp).to.deep.eql(expectedResponse);
  });

  it('If nothing is passed then return the empty string', () => {
    data = {};
    const resp = obj.fetchPayloadHandler(EXECUTE_INSERT_TEXT)(data);
    const expectedResponse = {
      text: ''
    };
    expect(mockSetInsertText.stub.calledOnce).to.be.true;
    expect(resp).to.deep.eql(expectedResponse);
  });
});

describe('setGeolocationOverride', () => {
  const EXECUTE_SET_GEOLOCATION = 'Emulation.setGeolocationOverride';
  const mockSetGeolationSet = {};
  let obj,data;
  beforeEach(() => {
    mockSetGeolationSet.stub = sinon.spy(PuppeteerRawLog, 'setGeolocationOverride');
    obj = new PuppeteerRawLog();
    data = {
      latitude: 10,
      longitude: 10
    };
  });

  afterEach(() => {
    mockSetGeolationSet.stub.restore();
  });

  it('Should call the setGeolocationOverride method and return correct data', () => {
    const resp = obj.fetchPayloadHandler(EXECUTE_SET_GEOLOCATION)(data);
    const expectedResponse = {
      latitude: 10,
      longitude: 10
    };
    expect(mockSetGeolationSet.stub.calledOnce).to.be.true;
    expect(resp).to.deep.eql(expectedResponse);
  });

  it('If nothing is passed then return the dafault value', () => {
    data = {};
    const resp = obj.fetchPayloadHandler(EXECUTE_SET_GEOLOCATION)(data);
    const expectedResponse = {
      latitude: 0,
      longitude: 0
    };
    expect(mockSetGeolationSet.stub.calledOnce).to.be.true;
    expect(resp).to.deep.eql(expectedResponse);
  });
});

describe('getCookies', () => {
  const EXECUTE_GET_COOKIES = 'Network.getCookies';
  const mockSetGetCookies = {};
  let obj,data;
  beforeEach(() => {
    mockSetGetCookies.stub = sinon.spy(PuppeteerRawLog, 'getCookies');
    obj = new PuppeteerRawLog();
    data = {
      urls: ["abcd","efgh"]
    };
  });

  afterEach(() => {
    mockSetGetCookies.stub.restore();
  });

  it('Should call the getCookies method and return correct data', () => {
    const resp = obj.fetchPayloadHandler(EXECUTE_GET_COOKIES)(data);
    const expectedResponse = {
      urls: ["abcd","efgh"]
    };
    expect(mockSetGetCookies.stub.calledOnce).to.be.true;
    expect(resp).to.deep.eql(expectedResponse);
  });

  it('If nothing is passed then return the empty list', () => {
    data = {};
    const resp = obj.fetchPayloadHandler(EXECUTE_GET_COOKIES)(data);
    const expectedResponse = {
      urls: []
    };
    expect(mockSetGetCookies.stub.calledOnce).to.be.true;
    expect(resp).to.deep.eql(expectedResponse);
  });
});

describe('dispatchKeyEvent', () => {
  const EXECUTE_EVENT_DISPATCH_KEY = 'Input.dispatchKeyEvent';
  const mockSetDispatchKey = {};
  let obj,data;
  beforeEach(() => {
    mockSetDispatchKey.stub = sinon.spy(PuppeteerRawLog, 'dispatchKeyEvent');
    obj = new PuppeteerRawLog();
    data = {
      value: "abcd"
    };
  });

  afterEach(() => {
    mockSetDispatchKey.stub.restore();
  });

  it('Should call the dispatchKeyEvent method and return correct data', () => {
    const resp = obj.fetchPayloadHandler(EXECUTE_EVENT_DISPATCH_KEY)(data);
    const expectedResponse = {
      value: "abcd"
    };
    expect(mockSetDispatchKey.stub.calledOnce).to.be.true;
    expect(resp).to.deep.eql(expectedResponse);
  });

  it('If nothing is passed then return the empty string', () => {
    data = {};
    const resp = obj.fetchPayloadHandler(EXECUTE_EVENT_DISPATCH_KEY)(data);
    const expectedResponse = {
      value: ''
    };
    expect(mockSetDispatchKey.stub.calledOnce).to.be.true;
    expect(resp).to.deep.eql(expectedResponse);
  });
});

describe('evaluateExpression', () => {
  const RUNTIME_EVALUATE = 'Runtime.evaluate';
  const mockevaluateExpressionKey = {};
  let obj,data;
  beforeEach(() => {
    mockevaluateExpressionKey.stub = sinon.spy(PuppeteerRawLog, 'evaluateExpression');
    obj = new PuppeteerRawLog();
    data = {
      expression: "abcd"
    };
  });

  afterEach(() => {
    mockevaluateExpressionKey.stub.restore();
  });

  it('Should call the evaluateExpression method and return correct data', () => {
    const resp = obj.fetchPayloadHandler(RUNTIME_EVALUATE)(data);
    const expectedResponse = {
      expression: "abcd"
    };
    expect(mockevaluateExpressionKey.stub.calledOnce).to.be.true;
    expect(resp).to.deep.eql(expectedResponse);
  });

  it('If nothing is passed then return the empty string', () => {
    data = {};
    const resp = obj.fetchPayloadHandler(RUNTIME_EVALUATE)(data);
    const expectedResponse = {
      expression: ''
    };
    expect(mockevaluateExpressionKey.stub.calledOnce).to.be.true;
    expect(resp).to.deep.eql(expectedResponse);
  });
});

describe('evaluateBSExecutorExpression', () => {
  const EVALUATE_EXPRESSION = 'evaluateExpression';
  const mockevaluateBSExecutorExpression = {};
  let obj;
  let data;
  beforeEach(() => {
    mockevaluateBSExecutorExpression.stub = sinon.spy(PuppeteerRawLog, 'evaluateBSExecutorExpression');
    obj = new PuppeteerRawLog();
    data = {
      result: {
        value: {
          script: 'browserstack_executor: {"action": "setSessionName", "arguments": {"name": "Trial Session"}}',
          args: [],
        }
      },
    };
  });

  afterEach(() => {
    mockevaluateBSExecutorExpression.stub.restore();
  });

  it('Should call the evaluateBSExecutorExpression method and return correct data', () => {
    const resp = obj.fetchPayloadHandler(EVALUATE_EXPRESSION)(data);
    expect(mockevaluateBSExecutorExpression.stub.calledOnce).to.be.true;
    expect(resp).to.deep.eql(data.result.value);
  });

  it('Should not call evaluateBSExecutorExpression for other methods', () => {
    obj.fetchPayloadHandler('RANDOM_METHOD')(data);
    expect(mockevaluateBSExecutorExpression.stub.calledOnce).to.be.false;
  });

  it('Should return data if data.result is not hash', () => {
    const input = { result: '{}' };
    const resp = obj.fetchPayloadHandler(EVALUATE_EXPRESSION)(input);
    expect(mockevaluateBSExecutorExpression.stub.calledOnce).to.be.true;
    expect(resp).to.deep.eql(input);
  });
});

describe('mouseMovedEvent', () => {
  const MOUSE_MOVED = 'Input.mouseMovedEvent';
  const mockMouseMovedKey = {};
  let obj,data;
  beforeEach(() => {
    mockMouseMovedKey.stub = sinon.spy(PuppeteerRawLog, 'mouseMovedReleasedPressed');
    obj = new PuppeteerRawLog();
    data = {
     x: 12,
     y: 12
    };
  });

  afterEach(() => {
    mockMouseMovedKey.stub.restore();
  });

  it('Should call the mouseMovedReleasedPressed method and return correct data', () => {
    const resp = obj.fetchPayloadHandler(MOUSE_MOVED)(data);
    const expectedResponse = {
      x: 12,
      y: 12
    };
    expect(mockMouseMovedKey.stub.calledOnce).to.be.true;
    expect(resp).to.deep.eql(expectedResponse);
  });

  it('If nothing is passed then return the default values', () => {
    data = {};
    const resp = obj.fetchPayloadHandler(MOUSE_MOVED)(data);
    const expectedResponse = {
      x: 0,
      y: 0
    };
    expect(mockMouseMovedKey.stub.calledOnce).to.be.true;
    expect(resp).to.deep.eql(expectedResponse);
  });
});

describe('mousePressedEvent', () => {
  const MOUSE_PRESSED = 'Input.mousePressedEvent';
  const mockMousePressedKey = {};
  let obj,data;
  beforeEach(() => {
    mockMousePressedKey.stub = sinon.spy(PuppeteerRawLog, 'mouseMovedReleasedPressed');
    obj = new PuppeteerRawLog();
    data = {
      x: 12,
      y: 12
    };
  });

  afterEach(() => {
    mockMousePressedKey.stub.restore();
  });

  it('Should call the mouseMovedReleasedPressed method and return correct data', () => {
    const resp = obj.fetchPayloadHandler(MOUSE_PRESSED)(data);
    const expectedResponse = {
      x: 12,
      y: 12
    };
    expect(mockMousePressedKey.stub.calledOnce).to.be.true;
    expect(resp).to.deep.eql(expectedResponse);
  });

  it('If nothing is passed then return the default values', () => {
    data = {};
    const resp = obj.fetchPayloadHandler(MOUSE_PRESSED)(data);
    const expectedResponse = {
      x: 0,
      y: 0
    };
    expect(mockMousePressedKey.stub.calledOnce).to.be.true;
    expect(resp).to.deep.eql(expectedResponse);
  });
});

describe('mouseReleaseEvent', () => {
  const MOUSE_RELEASED = 'Input.mouseReleasedEvent';
  const mockMouseReleasedKey = {};
  let obj,data;
  beforeEach(() => {
    mockMouseReleasedKey.stub = sinon.spy(PuppeteerRawLog, 'mouseMovedReleasedPressed');
    obj = new PuppeteerRawLog();
    data = {
      x: 12,
      y: 12
    };
  });

  afterEach(() => {
    mockMouseReleasedKey.stub.restore();
  });

  it('Should call the mouseMovedReleasedPressed method and return correct data', () => {
    const resp = obj.fetchPayloadHandler(MOUSE_RELEASED)(data);
    const expectedResponse = {
      x: 12,
      y: 12
    };
    expect(mockMouseReleasedKey.stub.calledOnce).to.be.true;
    expect(resp).to.deep.eql(expectedResponse);
  });

  it('If nothing is passed then return the default values', () => {
    data = {};
    const resp = obj.fetchPayloadHandler(MOUSE_RELEASED)(data);
    const expectedResponse = {
      x: 0,
      y: 0
    };
    expect(mockMouseReleasedKey.stub.calledOnce).to.be.true;
    expect(resp).to.deep.eql(expectedResponse);
  });
});

describe('customQuerySelector', () => {
  const FIND_ELEMENT = 'Page.customQuerySelector';
  const mockFindElementKey = {};
  let obj,data;
  beforeEach(() => {
    mockFindElementKey.stub = sinon.spy(PuppeteerRawLog, 'findElement');
    obj = new PuppeteerRawLog();
  });

  afterEach(() => {
    mockFindElementKey.stub.restore();
  });

  it('Should call the findElement method and return correct data', () => {
    data = {
      value: 'functionDefinition',
      args: [{objID: '0SHNBJK'}, { value: 'abcd'}]
    };
    const resp = obj.fetchPayloadHandler(FIND_ELEMENT)(data);
    const expectedResponse = {
      element: 'abcd'
    };
    expect(mockFindElementKey.stub.calledOnce).to.be.true;
    expect(resp).to.deep.eql(expectedResponse);
  });

  it('If the array size is less than 1, then return the empty string', () => {
    data = {
      value: 'functionDefinition',
      args: [{objID: '0SHNBJK'}]
    }
    const resp = obj.fetchPayloadHandler(FIND_ELEMENT)(data);
    const expectedResponse = {
      element: ''
    };
    expect(mockFindElementKey.stub.calledOnce).to.be.true;
    expect(resp).to.deep.eql(expectedResponse);
  });

  it('If the array size is greater than 1 but value is not present then return the empty string', () => {
    data = {
      value: 'functionDefinition',
      args: [{objID: '0SHNBJK'}, {abcd: 'abcd'}]
    }
    const resp = obj.fetchPayloadHandler(FIND_ELEMENT)(data);
    const expectedResponse = {
      element: ''
    };
    expect(mockFindElementKey.stub.calledOnce).to.be.true;
    expect(resp).to.deep.eql(expectedResponse);
  });

  it('If the args array is not present then return the empty string', () => {
    data = {
      value: 'functionDefinition',
    }
    const resp = obj.fetchPayloadHandler(FIND_ELEMENT)(data);
    const expectedResponse = {
      element: ''
    };
    expect(mockFindElementKey.stub.calledOnce).to.be.true;
    expect(resp).to.deep.eql(expectedResponse);
  });
});

describe('focusElement', () => {
  const FOCUS_ELEMENT = 'Page.customFocus';
  const mockFocusElementKey = {};
  let obj,data;
  beforeEach(() => {
    mockFocusElementKey.stub = sinon.spy(PuppeteerRawLog, 'focusElement');
    obj = new PuppeteerRawLog();
    data = {
      value: 'abcd'
    };
  });

  afterEach(() => {
    mockFocusElementKey.stub.restore();
  });

  it('Should call the focusElement method and return correct data', () => {
    const resp = obj.fetchPayloadHandler(FOCUS_ELEMENT)(data);
    const expectedResponse = {
      value: 'abcd'
    };
    expect(mockFocusElementKey.stub.calledOnce).to.be.true;
    expect(resp).to.deep.eql(expectedResponse);
  });

  it('If nothing is passed then return the empty string', () => {
    data = {};
    const resp = obj.fetchPayloadHandler(FOCUS_ELEMENT)(data);
    const expectedResponse = {
      value: ''
    };
    expect(mockFocusElementKey.stub.calledOnce).to.be.true;
    expect(resp).to.deep.eql(expectedResponse);
  });
});

describe('runJSFunction', () => {
  const RUNTIME_FUNCTION = 'Runtime.callFunctionOn';
  const mockRuntimeFunctionKey = {};
  let obj,data;
  beforeEach(() => {
    mockRuntimeFunctionKey.stub = sinon.spy(PuppeteerRawLog, 'callJSFunction');
    obj = new PuppeteerRawLog();
    data = {
      value: 'abcd'
    };
  });

  afterEach(() => {
    mockRuntimeFunctionKey.stub.restore();
  });

  it('Should call the callJSFunction method and return correct data', () => {
    const resp = obj.fetchPayloadHandler(RUNTIME_FUNCTION)(data);
    const expectedResponse = {
      value: 'abcd'
    };
    expect(mockRuntimeFunctionKey.stub.calledOnce).to.be.true;
    expect(resp).to.deep.eql(expectedResponse);
  });

  it('If nothing is passed then return the empty string', () => {
    data = {};
    const resp = obj.fetchPayloadHandler(RUNTIME_FUNCTION)(data);
    const expectedResponse = {
      value: ''
    };
    expect(mockRuntimeFunctionKey.stub.calledOnce).to.be.true;
    expect(resp).to.deep.eql(expectedResponse);
  });
});
