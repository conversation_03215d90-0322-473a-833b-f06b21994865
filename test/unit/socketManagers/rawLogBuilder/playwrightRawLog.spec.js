'use strict';

const sinon = require('sinon');
const { expect } = require('chai');
const PlaywrightRawLog = require('../../../../socketManagers/playwrightRawLog');

describe('Custom payload for playwright logs based on type', () => {
  let data;
  let obj;

  describe('Open URL', () => {
    const OPEN_METHOD = 'goto';
    const mockPageOpen = {};

    beforeEach(() => {
      mockPageOpen.stub = sinon.spy(PlaywrightRawLog, 'pageOpenPayload');
      obj = new PlaywrightRawLog();
      data = {
        url: 'https://google.com',
        waitUntil: 'loaded'
      };
    });

    afterEach(() => {
      mockPageOpen.stub.restore();
    });

    it('Should call the pageOpen method and return correct data', () => {
      const resp = obj.fetchPayloadHandler(OPEN_METHOD)(data);
      const expectedResponse = {
        url: 'https://google.com',
        waitUntil: 'loaded'
      };
      expect(mockPageOpen.stub.calledOnce).to.be.true;
      expect(resp).to.deep.eql(expectedResponse);
    });

    it('Should not set waitUntil in response if not defined', () => {
      delete data.waitUntil;
      const resp = obj.fetchPayloadHandler(OPEN_METHOD)(data);
      const expectedResponse = {
        url: 'https://google.com'
      };
      expect(mockPageOpen.stub.calledOnce).to.be.true;
      expect(resp).to.deep.eql(expectedResponse);
    });
  });

  describe('New context created', () => {
    const NEW_CONTEXT_METHOD = 'newContext';
    const mockNewContext = {};

    beforeEach(() => {
      mockNewContext.stub = sinon.spy(PlaywrightRawLog, 'newContextCreated');
      obj = new PlaywrightRawLog();
      data = {
        ignoreHTTPSErrors: true,
        noDefaultViewport: false,
        recordVideo: {
          path: 'randomPath'
        }
      };
    });

    afterEach(() => {
      mockNewContext.stub.restore();
    });

    it('Should call the newContextCreated method and return correct data', () => {
      const resp = obj.fetchPayloadHandler(NEW_CONTEXT_METHOD)(data);
      const expectedResponse = {
        ignoreHTTPSErrors: true,
        noDefaultViewport: false,
        recordVideo: {
          path: 'randomPath'
        }
      };
      expect(mockNewContext.stub.calledOnce).to.be.true;
      expect(resp).to.deep.eql(expectedResponse);
    });
  });

  describe('Set Input files', () => {
    const SET_INPUT = 'setInputFiles';
    const mockSetInput = {};

    beforeEach(() => {
      mockSetInput.stub = sinon.spy(PlaywrightRawLog, 'setInputFiles');
      obj = new PlaywrightRawLog();
      data = {
        files: [
          { name: 'file1.js', data: 'base64:...' },
          { name: 'file2.js', data: 'base64:...' },
          { name: 'file3.js', data: 'base64:...' },
        ],
        selector: '#input'
      };
    });

    afterEach(() => {
      mockSetInput.stub.restore();
    });

    it('Should call the setInputFiles method and return correct data', () => {
      const resp = obj.fetchPayloadHandler(SET_INPUT)(data);
      const expectedResponse = {
        name: 'file1.js,file2.js,file3.js',
        selector: '#input'
      };
      expect(mockSetInput.stub.calledOnce).to.be.true;
      expect(resp).to.deep.eql(expectedResponse);
    });

    it('If empty array passed then also should work', () => {
      delete data.files;
      const resp = obj.fetchPayloadHandler(SET_INPUT)(data);
      const expectedResponse = {
        name: '',
        selector: '#input'
      };
      expect(mockSetInput.stub.calledOnce).to.be.true;
      expect(resp).to.deep.eql(expectedResponse);
    });
  });

  describe('Tap', () => {
    const EXECUTE_TAP = 'tap';
    const mockSetTap = {};

    beforeEach(() => {
      mockSetTap.stub = sinon.spy(PlaywrightRawLog, 'executeTap');
      obj = new PlaywrightRawLog();
      data = {
        selector: '#input',
        position: {
          x: 10,
          y: 20
        }
      };
    });

    afterEach(() => {
      mockSetTap.stub.restore();
    });

    it('Should call the executeTap method and return correct data', () => {
      const resp = obj.fetchPayloadHandler(EXECUTE_TAP)(data);
      const expectedResponse = {
        selector: '#input',
        position: {
          x: 10,
          y: 20
        }
      };
      expect(mockSetTap.stub.calledOnce).to.be.true;
      expect(resp).to.deep.eql(expectedResponse);
    });

    it('If nothing is passed then return the default data', () => {
      data = {};
      const resp = obj.fetchPayloadHandler(EXECUTE_TAP)(data);
      const expectedResponse = {
        selector: '',
        position: { x: 0, y: 0 }
      };
      expect(mockSetTap.stub.calledOnce).to.be.true;
      expect(resp).to.deep.eql(expectedResponse);
    });
  });

  describe('Execute Script', () => {
    const EXECUTE_SCRIPT = 'evaluate';
    const mockExecuteScript = {};

    beforeEach(() => {
      mockExecuteScript.stub = sinon.spy(PlaywrightRawLog, 'executeScript');
      obj = new PlaywrightRawLog();
      data = {
        execute: '() => {"randomFunc"}'
      };
    });

    afterEach(() => {
      mockExecuteScript.stub.restore();
    });

    it('Should call the executeScript method and return correct data', () => {
      const resp = obj.fetchPayloadHandler(EXECUTE_SCRIPT)(data);
      const expectedResponse = {
        execute: '() => {"randomFunc"}'
      };
      expect(mockExecuteScript.stub.calledOnce).to.be.true;
      expect(resp).to.deep.eql(expectedResponse);
    });

    it('If nothing is passed then return an empty string', () => {
      data = {};
      const resp = obj.fetchPayloadHandler(EXECUTE_SCRIPT)(data);
      const expectedResponse = {
        execute: ''
      };
      expect(mockExecuteScript.stub.calledOnce).to.be.true;
      expect(resp).to.deep.eql(expectedResponse);
    });
  });

  describe('Click', () => {
    const EXECUTE_CLICK = 'click';
    const mockExecuteClick = {};

    beforeEach(() => {
      mockExecuteClick.stub = sinon.spy(PlaywrightRawLog, 'executeClick');
      obj = new PlaywrightRawLog();
      data = {
        selector: '#input'
      };
    });

    afterEach(() => {
      mockExecuteClick.stub.restore();
    });

    it('Should call the executeClick method and return correct data', () => {
      const resp = obj.fetchPayloadHandler(EXECUTE_CLICK)(data);
      const expectedResponse = {
        selector: '#input'
      };
      expect(mockExecuteClick.stub.calledOnce).to.be.true;
      expect(resp).to.deep.eql(expectedResponse);
    });

    it('If nothing is passed then return an empty string', () => {
      data = {};
      const resp = obj.fetchPayloadHandler(EXECUTE_CLICK)(data);
      const expectedResponse = {
        selector: ''
      };
      expect(mockExecuteClick.stub.calledOnce).to.be.true;
      expect(resp).to.deep.eql(expectedResponse);
    });
  });

  describe('Find Element Selector', () => {
    const FIND_ELEMENT = 'querySelector';
    const mockFindElement = {};

    beforeEach(() => {
      mockFindElement.stub = sinon.spy(PlaywrightRawLog, 'findElementSelector');
      obj = new PlaywrightRawLog();
      data = {
        selector: '#input'
      };
    });

    afterEach(() => {
      mockFindElement.stub.restore();
    });

    it('Should call the findElementSelector method and return correct data', () => {
      const resp = obj.fetchPayloadHandler(FIND_ELEMENT)(data);
      const expectedResponse = {
        selector: '#input'
      };
      expect(mockFindElement.stub.calledOnce).to.be.true;
      expect(resp).to.deep.eql(expectedResponse);
    });

    it('If nothing is passed then return an empty string', () => {
      data = {};
      const resp = obj.fetchPayloadHandler(FIND_ELEMENT)(data);
      const expectedResponse = {
        selector: ''
      };
      expect(mockFindElement.stub.calledOnce).to.be.true;
      expect(resp).to.deep.eql(expectedResponse);
    });
  });

  describe('evaluateExpression', () => {
    const EVALUATE_EXPRESSION = 'evaluateExpression';
    const mockEvaluateExpression = {};

    beforeEach(() => {
      mockEvaluateExpression.stub = sinon.spy(PlaywrightRawLog, 'evaluateExpression');
      obj = new PlaywrightRawLog();
      data = {
        value: {
          s: {
            script: 'browserstack_executor: {"action": "setSessionName", "arguments": {"name": "Trial Session"}}',
            args: [],
          },
        },
      };
    });

    afterEach(() => {
      mockEvaluateExpression.stub.restore();
    });

    it('Should call the evaluateExpression method and return correct data', () => {
      const resp = obj.fetchPayloadHandler(EVALUATE_EXPRESSION)(data);
      expect(mockEvaluateExpression.stub.calledOnce).to.be.true;
      expect(resp).to.deep.eql(data.value.s);
    });

    it('Should not call the evaluateExpression method', () => {
      const resp = obj.fetchPayloadHandler('RANDOM_METHOD')(data);
      expect(mockEvaluateExpression.stub.calledOnce).to.be.false;
      expect(resp).to.not.eql(data.value.s);
    });

    it('Should return input if input is str', () => {
      const input = '{}';
      const resp = obj.fetchPayloadHandler(EVALUATE_EXPRESSION)(input);
      expect(resp).to.eql(input);
    });

    it('Should return input if value is str', () => {
      const input = { value: '{}' };
      const resp = obj.fetchPayloadHandler(EVALUATE_EXPRESSION)(input);
      expect(resp).to.eql(input);
    });
  });

  describe('takeScreenshot', () => {
    const  TAKE_SCREENSHOT = 'resp-screenshot';
    const mockTakeScreenshot = {};

    beforeEach(() => {
      mockTakeScreenshot.stub = sinon.spy(PlaywrightRawLog, 'takeScreenshot');
      obj = new PlaywrightRawLog();
      data = {
        value: "s3_url_of_ss",
      };
    });

    afterEach(() => {
      mockTakeScreenshot.stub.restore();
    });

    it('should take screenshot and return correct data', () => {
      const resp = obj.fetchPayloadHandler(TAKE_SCREENSHOT)(data);
      expect(mockTakeScreenshot.stub.calledOnce).to.be.true;
      expect(resp).to.deep.eql(data);
    });
  });
});
