"use strict";

/* eslint-disable no-underscore-dangle */

const sinon = require("sinon");
const { wsProxy } = require("./socketHelpers");
const { kReqData } = require("../../../config/socketConstants");
const rewire = require("rewire");
const handler = rewire("../../../socketManagers/playwrightHandler");

describe("Playwright socket close handler", () => {
  const PLAYWRIGHT_HANDLER = handler.__get__("PlaywrightHandler");
  let obj;
  let ws;
  let mockAddRequestLog = {};
  let mockRegistryObject = {};
  let mockAddToCompletedSessions = {};

  context("marking sessions as completed", () => {
    beforeEach(() => {
      obj = new PLAYWRIGHT_HANDLER();
      ws = wsProxy();
      ws.isPlaywright = true;
      sinon.stub(obj, "sendToTerminal", () => {});
      mockAddRequestLog.stub = sinon.stub(obj, "addRequestLog");
      mockRegistryObject.stub = sinon.stub(obj, "getRegistryObject");
      mockRegistryObject.stub.returns({
        railsSesionId: "rails-1234",
        playwrightVersion: "1.20.0",
      });
      ws[kReqData] = {};
      const socketObjData = { "client.playwrightVersion": "1.20.0" };
      ws[kReqData].data = socketObjData;

      mockAddToCompletedSessions.stub = sinon.stub(
        obj,
        "addToCompletedSessions"
      );
    });

    afterEach(() => {
      obj.sendToTerminal.restore();
      mockAddRequestLog.stub.restore();
      mockRegistryObject.stub.restore();
      mockAddToCompletedSessions.stub.restore();
    });

    it("should add session to completed session if close request is browser.close", () => {
      const data = {
        guid: "browser@abcd",
        method: "close",
      };
      obj.closeHandler(ws, data);
      sinon.assert.calledOnce(mockAddToCompletedSessions.stub);
    });

    it("should not add session to completed session if close request is browser-context.close", () => {
      const data = {
        guid: "browser-context@abcd",
        method: "close",
      };
      obj.closeHandler(ws, data);
      sinon.assert.notCalled(mockAddToCompletedSessions.stub);
    });

    it("should not add session to completed session if close request is page.close", () => {
      const data = {
        guid: "page@abcd",
        method: "close",
      };
      obj.closeHandler(ws, data);
      sinon.assert.notCalled(mockAddToCompletedSessions.stub);
    });
  });

  context(
    "closing client socket for client.playwrightVersion <= 1.11.1 & browserstack.playwrightVersion > 1.12",
    () => {
      beforeEach(() => {
        obj = new PLAYWRIGHT_HANDLER();
        ws = wsProxy();
        ws.isPlaywright = true;
        sinon.stub(obj, "sendToTerminal", () => {});
        mockAddRequestLog.stub = sinon.stub(obj, "addRequestLog");
        mockRegistryObject.stub = sinon.stub(obj, "getRegistryObject");
      });

      afterEach(() => {
        obj.sendToTerminal.restore();
        mockAddRequestLog.stub.restore();
        mockRegistryObject.stub.restore();
      });

      it("should call ws.close in case of client version <= 1.11.1 & playwright Version >= 1.12", () => {
        mockRegistryObject.stub.returns({
          railsSesionId: "rails-1234",
          playwrightVersion: "1.12.1",
        });
        ws[kReqData] = {};
        const socketObjData = { "client.playwrightVersion": "1.11" };
        ws[kReqData].data = socketObjData;
        obj.closeHandler(ws, "");
        sinon.assert.calledOnce(mockRegistryObject.stub);
        sinon.assert.calledOnce(ws.close);
      });

      it("should not call ws.close in case of client version <= 1.11.1 & playwright Version < 1.12", () => {
        mockRegistryObject.stub.returns({
          railsSesionId: "rails-1234",
          playwrightVersion: "1.11.1",
        });
        ws[kReqData] = {};
        const socketObjData = { "client.playwrightVersion": "1.11.1" };
        ws[kReqData].data = socketObjData;
        obj.closeHandler(ws, "");
        sinon.assert.calledOnce(mockRegistryObject.stub);
        sinon.assert.notCalled(ws.close);
      });

      it("should not call ws.close in case of client version >= 1.12 & playwright Version >= 1.12", () => {
        mockRegistryObject.stub.returns({
          railsSesionId: "rails-1234",
          playwrightVersion: "1.12.3",
        });
        ws[kReqData] = {};
        const socketObjData = { "client.playwrightVersion": "1.12.3" };
        ws[kReqData].data = socketObjData;
        obj.closeHandler(ws, "");
        sinon.assert.calledOnce(mockRegistryObject.stub);
        sinon.assert.notCalled(ws.close);
      });

      it("should not call ws.close in case of client version >= 1.12 & playwright Version < 1.12", () => {
        mockRegistryObject.stub.returns({
          railsSesionId: "rails-1234",
          playwrightVersion: "1.11.1",
        });
        ws[kReqData] = {};
        const socketObjData = { "client.playwrightVersion": "1.12.3" };
        ws[kReqData].data = socketObjData;
        obj.closeHandler(ws, "");
        sinon.assert.calledOnce(mockRegistryObject.stub);
        sinon.assert.notCalled(ws.close);
      });
    }
  );
});
