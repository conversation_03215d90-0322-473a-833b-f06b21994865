'use strict';

/* eslint-disable no-underscore-dangle */

const { expect } = require('chai');
const sinon = require('sinon');
const rewire = require('rewire');
const { wsProxy, mockLoggerSpies } = require('../socketHelpers');
const { kStartTimer } = require('../../../../config/socketConstants');

const handler = rewire('../../../../socketManagers/detoxHandler');

describe('Detox preProcessAllocation', () => {
  const DETOX_HANDLER = handler.__get__('DetoxHandler');
  let obj;
  let socketObj;
  const mockAllocateTerminal = {};
  const mockDesktopSocket = {};
  const mockLogger = {};
  const mockHA = {};
  const startSpy = sinon.spy();

  beforeEach(() => {
    obj = new DETOX_HANDLER();
    socketObj = wsProxy();
    mockAllocateTerminal.original = handler.__get__('allocateTerminal');
    mockAllocateTerminal.stub = sinon.stub();
    mockDesktopSocket.original = obj.connectToDesktopSocket;
    mockDesktopSocket.stub = sinon.stub();
    mockHA.original = handler.__get__('HA');
    mockHA.stub = sinon.stub(mockHA.original, 'getData');
    mockLogger.original = handler.__get__('logger');
    Object.keys(mockLoggerSpies).forEach((el) => {
      mockLoggerSpies[el] = sinon.spy();
    });
    mockLogger.stub = mockLoggerSpies;
    obj.on(kStartTimer, startSpy);
    handler.__set__('logger', mockLogger.stub);
    obj.connectToDesktopSocket = mockDesktopSocket.stub;
    handler.__set__('allocateTerminal', mockAllocateTerminal.stub);
    handler.__set__('globalRegistry', {});
  });

  afterEach(() => {
    handler.__set__('allocateTerminal', mockAllocateTerminal.original);
    obj.connectToDesktopSocket = mockDesktopSocket.original;
    handler.__set__('logger', mockLogger.original);
    mockHA.stub.restore();
    startSpy.reset();
  });

  it('If no websokcet url present should close the socket conenction', async () => {
    mockAllocateTerminal.stub.returns(Promise.resolve({
      status: 0,
      sessionId: '12345',
      value: {}
    }));

    await obj.preProcessAllocation(socketObj);
    expect(socketObj.close.calledOnce).to.be.true;
    const [code, message] = socketObj.close.args[0];
    expect(code).to.be.eql(1011);
    expect(message).to.be.eql('Error: Unable to get the websocket connection');
  });

  it('If status 13 is sent from upstream then throw attempt error', async () => {
    mockAllocateTerminal.stub.returns(Promise.resolve({
      status: 13,
      sessionId: '12345',
      value: { message: 'There is an error communicating to detox', wsURL: 'xxx' }
    }));

    await obj.preProcessAllocation(socketObj);
    expect(socketObj.close.calledOnce).to.be.true;
    const [code, message] = socketObj.close.args[0];
    expect(code).to.be.eql(1011);
    expect(message).to.be.eql('Error: There is an error communicating to detox');
  });

  it('If should throw detox server start error', async () => {
    mockAllocateTerminal.stub.returns(Promise.resolve({
      status: undefined,
      sessionId: undefined,
      value: {wsURL: 'xxx'}
    }));

    await obj.preProcessAllocation(socketObj);
    expect(socketObj.close.calledOnce).to.be.true;
    const [code, message] = socketObj.close.args[0];
    expect(code).to.be.eql(1011);
    expect(message).to.be.eql('Error: Unable to start detox server');
  });

  it('If undefined status code is sent from upstream then throw an attempt error', async () => {
    mockAllocateTerminal.stub.returns(Promise.resolve({
      status: 999,
      sessionId: '12345',
      value: { message: 'There is an error communicating to detox' }
    }));

    await obj.preProcessAllocation(socketObj);
    expect(socketObj.close.calledOnce).to.be.true;
    const [code, message] = socketObj.close.args[0];
    expect(code).to.be.eql(1011);
    expect(message).to.be.eql('Error: There is an error communicating to detox');
  });

  it('If allocate terminal fails then closes the socket connection', async () => {
    mockAllocateTerminal.stub.throws('Something is not a function');
    await obj.preProcessAllocation(socketObj);
    expect(socketObj.close.calledOnce).to.be.true;
    const [code, message] = socketObj.close.args[0];
    expect(code).to.be.eql(1011);
    expect(message).to.be.eql('Something is not a function');
  });

  it('Event emitted when the allocation is successfull to start the timer', async () => {
    mockAllocateTerminal.stub.returns(Promise.resolve({
      status: 0,
      sessionId: '12345',
      value: {
        wsURL: 'ws://127.0.0.1:9222/something-random',
        wsHostname: '***********'
      }
    }));
    mockDesktopSocket.stub.returns(Promise.resolve());
    mockHA.stub.yields(null, { id: '12345' });

    await obj.preProcessAllocation(socketObj);
    expect(socketObj.close.calledOnce).to.be.false;
    expect(startSpy.calledOnce).to.be.true;
  });

  it('Should close the socket if unable to refresh the keyObject', async () => {
    mockAllocateTerminal.stub.returns(Promise.resolve({
      status: 0,
      sessionId: '12345',
      value: {
        wsURL: 'ws://127.0.0.1:9222/something-random',
        wsHostname: '***********'
      }
    }));
    mockDesktopSocket.stub.returns(Promise.resolve());
    mockHA.stub.yields(new Error('Unable to setup connection to redis'));

    await obj.preProcessAllocation(socketObj);
    expect(socketObj.close.calledOnce).to.be.true;
    const [code, message] = socketObj.close.args[0];
    expect(code).to.be.eql(1011);
    expect(message).to.be.eql('Error: sessionId state not found');
    expect(startSpy.calledOnce).to.be.false;
  });
  it('Truncate the close message if it is greater than 123 bytes to avoid range error', async () => {
    let errorMsg = 'This is a test message to test 123 bytes range error handling on websocket close';
    let lengthyMsg = errorMsg + errorMsg;
    mockAllocateTerminal.stub.throws(lengthyMsg);
    await obj.preProcessAllocation(socketObj);
    expect(socketObj.close.calledOnce).to.be.true;
    const [code, message] = socketObj.close.args[0];
    expect(code).to.be.eql(1011);
    expect(message).to.be.eql(lengthyMsg.substring(0, 122));
  });
});
