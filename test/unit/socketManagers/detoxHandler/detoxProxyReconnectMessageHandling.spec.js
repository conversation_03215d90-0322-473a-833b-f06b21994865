'use strict';

const sinon = require('sinon');
const { expect } = require('chai');
const rewire = require('rewire');
const { wsProxy } = require('../socketHelpers');

const handler = rewire('../../../../socketManagers/detoxHandler.js');

describe('Reconnect message handling', () => {
  const DETOX_HANDLER = handler.__get__('DetoxHandler');

  let obj;
  let ws;
  let initializeReconnect;
  let waitForAllocation;
  let processData;
  let reconnectMessage;

  beforeEach(() => {
    obj = new DETOX_HANDLER();
    ws = wsProxy();
    initializeReconnect = sinon.stub(obj, 'initializeReconnect');
    waitForAllocation = sinon.stub(obj, 'waitForAllocation');
    processData = sinon.stub(obj, 'processData');
    reconnectMessage = `RECONNECT ${JSON.stringify({})}`;
  });

  it('Should return if reconnect message is received', async () => {
    initializeReconnect.returns(Promise.resolve());
    await obj.messageHandler(ws, reconnectMessage);
    expect(waitForAllocation.notCalled).to.be.true;
    expect(processData.notCalled).to.be.true;
  });

  it('Should continue if other message is received', async () => {
    initializeReconnect.returns(Promise.resolve());
    await obj.messageHandler(ws, JSON.stringify({ type: 'random' }));
    expect(waitForAllocation.calledOnce).to.be.true;
    expect(processData.calledOnce).to.be.true;
    expect(initializeReconnect.calledOnce).to.be.true;
  });
});
