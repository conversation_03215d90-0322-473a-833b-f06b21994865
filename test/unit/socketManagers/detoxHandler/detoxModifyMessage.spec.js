'use strict';

const sinon = require('sinon');
const { expect } = require('chai');
const rewire = require('rewire');
const { wsProxy } = require('../socketHelpers');
const handler = rewire('../../../../socketManagers/detoxHandler');

describe('modifyDetoxMessage', () => {
  const DETOX_HANDLER = handler.__get__('DetoxHandler');
  let samplePayload, expectedPayload, railsObj, obj, ws, mockInjectCloudSessionId, mockCloseClientConnection;

  beforeEach(() => {
    obj = new DETOX_HANDLER();
    ws = wsProxy();

    railsObj = {
      sessionId: '1234-rails'
    };
  });

  it('should replace detox sessionId with railsSessionId for message', async () => {
    samplePayload = {
      type: 'invoke',
      params: {
        method: 'login',
        sessionId: '123'
      },
      messageId: 0
    };

    expectedPayload = {
      type: 'invoke',
      params: { 
        method: 'login',
        sessionId: '1234-rails' 
      },
      messageId: 0,
      cloudSessionId: '1234-rails'
    }

    const modifiedData = await obj.modifyDetoxMessage(samplePayload, railsObj.sessionId, ws);
    expect(modifiedData).to.deep.equal(expectedPayload);
  });

  it('should thow error and close session if error in modifying login message.', async () => {
    samplePayload = {
      type: "login",
      params: {
        method: "login"
      },
      cloudSessionId: railsObj.sessionId,
      messageId: 0
    };

    mockInjectCloudSessionId = sinon.stub(obj, 'injectCloudSessionId');
    mockCloseClientConnection = sinon.stub(obj, 'closeClientConnection');
    mockInjectCloudSessionId.throws();

    await obj.modifyDetoxMessage(samplePayload, railsObj.sessionId, ws);    
    expect(mockCloseClientConnection.calledOnce).to.be.true;
    mockInjectCloudSessionId.restore();
    mockCloseClientConnection.restore();
  });

  it('should thow error and not close session if type is not login.', async () => {
    samplePayload = {
      type: "invoke",
      params: {
        method: "random"
      },
      cloudSessionId: railsObj.sessionId,
      messageId: 0
    };

    mockInjectCloudSessionId = sinon.stub(obj, 'injectCloudSessionId');
    mockCloseClientConnection = sinon.stub(obj, 'closeClientConnection');
    mockInjectCloudSessionId.throws();

    await obj.modifyDetoxMessage(samplePayload, railsObj.sessionId, ws);
    expect(mockCloseClientConnection.calledOnce).to.be.false;
    mockInjectCloudSessionId.restore();
    mockCloseClientConnection.restore();
  });
});
