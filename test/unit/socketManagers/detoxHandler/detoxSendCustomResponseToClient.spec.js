'use strict';

const sinon = require('sinon');
const rewire = require('rewire');
const { wsProxy } = require('../socketHelpers');
const handler = rewire('../../../../socketManagers/detoxHandler');

describe('sendCustomResponseToClient', () => {
  const DETOX_HANDLER = handler.__get__('DetoxHandler');
  let railsObj, obj, ws;

  beforeEach(() => {
    obj = new DETOX_HANDLER();
    ws = wsProxy();

    railsObj = {
      sessionId: '1234-rails'
    };
  });

  it('should send message to tester for success response', async () => {
    const response = {success: true, message: "success message"};
    const clientMessage = {
      type: 'type',
      response: {
        method: 'method',
        success: true,
        message: ''
      },
      messageId: 0,
      cloudSessionId: railsObj.sessionId
    }
    obj.sendCustomResponseToClient(response, ws, 'method', 'type', 0, railsObj.sessionId);
    sinon.assert.calledWith(ws.send, JSON.stringify(clientMessage));
  });

  it('should send message to tester for failure response', async () => {
    const response = {success: false, message: "failure message"};
    const clientMessage = {
      type: 'type',
      response: {
        method: 'method',
        success: false,
        message: "failure message"
      },
      messageId: 0,
      cloudSessionId: railsObj.sessionId
    }
    obj.sendCustomResponseToClient(response, ws, 'method', 'type', 0, railsObj.sessionId);
    sinon.assert.calledWith(ws.send, JSON.stringify(clientMessage));
  });
});
