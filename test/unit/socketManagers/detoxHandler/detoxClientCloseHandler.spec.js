'use strict';

/* eslint-disable no-underscore-dangle */

const { expect } = require('chai');
const sinon = require('sinon');
const rewire = require('rewire');
const { wsProxy, mockLoggerSpies } = require('../socketHelpers');
const { kStartTimer } = require('../../../../config/socketConstants');

const handler = rewire('../../../../socketManagers/detoxHandler');

describe('Detox client close handler', () => {
  const DETOX_HANDLER = handler.__get__('DetoxHandler');
  const BASE_SOCKET = handler.__get__('SocketMessageHandler');
  let obj;
  let socketObj;
  const startSpy = sinon.spy();
  const mockTerminalCleanup = {};
  const mockMarkIdle = {};
  const mockSuperClose = {};
  const mockRailsSessions = {};
  const mockCloseDesktop = {};
  const mockLogger = {};

  beforeEach(() => {
    obj = new DETOX_HANDLER();
    socketObj = wsProxy();
    obj.on(kStartTimer, startSpy);
    handler.__set__('globalRegistry', {});
    mockMarkIdle.original = handler.__get__('markIdle');
    mockMarkIdle.stub = sinon.stub();
    mockLogger.original = handler.__get__('logger');
    Object.values(mockLoggerSpies).forEach(el => el.reset());
    mockLogger.stub = mockLoggerSpies;
    mockRailsSessions.stub = sinon.stub(obj.railsSessionMapping, 'has');
    mockTerminalCleanup.stub = sinon.stub(obj, 'startTerminalCleanup');
    mockCloseDesktop.stub = sinon.stub(obj, 'closeTerminalSocket');
    mockSuperClose.stub = sinon.stub(BASE_SOCKET.prototype, 'clientCloseHandler');
    handler.__set__('logger', mockLogger.stub);
    handler.__set__('markIdle', mockMarkIdle.stub);
  });

  afterEach(() => {
    startSpy.reset();
    mockTerminalCleanup.stub.restore();
    mockSuperClose.stub.restore();
    mockRailsSessions.stub.restore();
    mockCloseDesktop.stub.restore();
    handler.__set__('markIdle', mockMarkIdle.original);
    handler.__set__('logger', mockLogger.original);
  });

  it('In case of graceful close if railsSessionMapping does not exists do not send to terminal', async () => {
    mockRailsSessions.stub.returns(false);
    await obj.clientCloseHandler(socketObj, { code: 1001 });
    expect(mockTerminalCleanup.stub.notCalled).to.be.true;
    expect(mockMarkIdle.stub.notCalled).to.be.true;
    expect(mockCloseDesktop.stub.notCalled).to.be.true;
    expect(mockSuperClose.stub.calledOnce).to.be.true;
  });

  it('In case of code 1008 mark the session as IDLE_TIMEOUT', async () => {
    mockRailsSessions.stub.returns(true);
    await obj.clientCloseHandler(socketObj, { code: 1008 });
    expect(mockTerminalCleanup.stub.notCalled).to.be.true;
    expect(mockMarkIdle.stub.calledOnce).to.be.true;
    const { 1: message } = mockMarkIdle.stub.args[0];
    expect(message).to.be.eql('TIMEOUT');
    expect(mockCloseDesktop.stub.notCalled).to.be.true;
    expect(mockSuperClose.stub.calledOnce).to.be.true;
  });

  it('In case of graceful close', async () => {
    mockRailsSessions.stub.returns(true);
    await obj.clientCloseHandler(socketObj, { code: 1001 });
    expect(mockMarkIdle.stub.notCalled).to.be.true;
    expect(mockTerminalCleanup.stub.calledOnce).to.be.true;
    expect(mockSuperClose.stub.calledOnce).to.be.true;
  });

  it('In case of abrupt exit mark the session as ABRUPT_CLOSE', async () => {
    mockRailsSessions.stub.returns(true);
    await obj.clientCloseHandler(socketObj, { code: 1006 });
    expect(mockMarkIdle.stub.calledOnce).to.be.true;
    const { 1: message } = mockMarkIdle.stub.args[0];
    expect(message).to.be.eql('ABRUPT_CLOSE');
    expect(mockSuperClose.stub.calledOnce).to.be.true;
  });

  it('In case idle timeout threw error it should log', async () => {
    mockRailsSessions.stub.returns(true);
    mockMarkIdle.stub.throws('Unable to stop the terminal');
    await obj.clientCloseHandler(socketObj, { code: 1008 });
    expect(mockMarkIdle.stub.calledOnce).to.be.true;
    const { 1: message } = mockMarkIdle.stub.args[0];
    expect(message).to.be.eql('TIMEOUT');
    expect(mockSuperClose.stub.calledOnce).to.be.true;
    expect(mockLoggerSpies.error.calledOnce).to.be.true;
  });

  it('If session is not present in railsSessionMapping and abrupt close is received then do not clean', async () => {
    mockRailsSessions.stub.returns(false);
    await obj.clientCloseHandler(socketObj, { code: 1006 });
    expect(mockMarkIdle.stub.calledOnce).to.be.false;
    expect(mockSuperClose.stub.calledOnce).to.be.true;
  });
});
