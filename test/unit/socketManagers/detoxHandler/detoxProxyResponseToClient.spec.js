'use strict';

const { expect } = require('chai');
const sinon = require('sinon');
const rewire = require('rewire');
const handler = rewire('../../../../socketManagers/detoxHandler');

describe('proxyResponseToClient', () => {
  const DETOX_HANDLER = handler.__get__('DetoxHandler');
  const BASE_SOCKET = handler.__get__('SocketMessageHandler');
  const mockProxyResponseToClient = {};
  const mockWaitForPendingApiRequests = {};
  let railsObj, obj;

  beforeEach(() => {
    obj = new DETOX_HANDLER();

    railsObj = {
      sessionId: '1234-rails'
    };

    obj.railsSessionMapping.set('1234', railsObj.sessionId);
    mockProxyResponseToClient.stub = sinon.stub(BASE_SOCKET.prototype, 'proxyResponseToClient');
    mockWaitForPendingApiRequests.stub = sinon.stub(obj, 'waitForPendingApiRequests').returns(Promise.resolve());  
  });

  afterEach(() => {
    mockProxyResponseToClient.stub.restore();
    mockWaitForPendingApiRequests.stub.restore();
  });

  it('should wait for pending API requests', async () => {
    await obj.proxyResponseToClient('1234', JSON.stringify({}));
    expect(mockWaitForPendingApiRequests.stub.calledOnce).to.be.true;
  });

  it('should add rails session id as cloudSession Id to response message to client', async () => {
    await obj.proxyResponseToClient('1234', JSON.stringify({}));
    expect(mockWaitForPendingApiRequests.stub.calledOnce).to.be.true;
    expect(mockProxyResponseToClient.stub.calledOnce).to.be.true;
  });
});
