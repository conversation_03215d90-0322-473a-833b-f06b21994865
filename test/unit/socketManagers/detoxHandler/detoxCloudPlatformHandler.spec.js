'use strict';

const sinon = require('sinon');
const { expect } = require('chai');
const rewire = require('rewire');
const { wsProxy } = require('../socketHelpers');
const handler = rewire('../../../../socketManagers/detoxHandler');

describe('cloudPlatformTypeHandler', () => {
  const DETOX_HANDLER = handler.__get__('DetoxHandler');
  let samplePayload, railsObj, obj, ws, globalRegistry, logger, sendRequestToPlatform;
  const mockAllocationStub = {};
  const mockSendTerminal = {};
  const mockSendCustomResponseToClient = {};
  const mockGenerateRequestUrl = {};
  const originalSendRequestToPlatform = handler.__get__('sendRequestToPlatform');

  beforeEach(() => {
    obj = new DETOX_HANDLER();
    ws = wsProxy();

    railsObj = {
      sessionId: '1234-rails'
    };

    obj.railsSessionMapping = new Map();
    obj.railsSessionMapping.set(ws.id, railsObj.sessionId);

    logger = {
      error: sinon.stub(),
    };

    globalRegistry = {
      '1234-rails': {
        rproxyHost: 'test_rproxy_host',
        device: 'xyz'
      },
    };
    handler.__set__('globalRegistry', globalRegistry);

    sendRequestToPlatform = sinon.stub();
    handler.__set__('sendRequestToPlatform', sendRequestToPlatform);

    mockAllocationStub.stub = sinon.stub(obj, 'waitForAllocation');
    mockSendTerminal.stub = sinon.stub(obj, 'sendToTerminal');
    mockAllocationStub.stub.returns(Promise.resolve());
    mockSendCustomResponseToClient.stub = sinon.stub(obj, 'sendCustomResponseToClient');
    mockGenerateRequestUrl.stub = sinon.stub(obj, 'generateRequestUrl');
  });

  afterEach(() => {
    mockAllocationStub.stub.restore();
    mockSendTerminal.stub.restore();
    mockSendCustomResponseToClient.stub.restore();
    mockGenerateRequestUrl.stub.restore();
    handler.__set__('sendRequestToPlatform', originalSendRequestToPlatform);
  });

  it('should send request to platform for app launch', async () => {
    samplePayload = {
      type: "CloudPlatform",
      params: {
        method: "launchApp"
      },
      cloudSessionId: railsObj.sessionId,
      messageId: 0
    };

    await obj.messageHandler(ws, JSON.stringify(samplePayload));
    expect(mockGenerateRequestUrl.stub.calledOnce).to.be.true;
    expect(sendRequestToPlatform.calledOnce).to.be.true;
  });

  it('should send request to platform for terminate app', async () => {
    samplePayload = {
      type: "CloudPlatform",
      params: {
        method: "terminateApp"
      },
      cloudSessionId: railsObj.sessionId,
      messageId: 0
    };

    await obj.messageHandler(ws, JSON.stringify(samplePayload));
    expect(mockGenerateRequestUrl.stub.calledOnce).to.be.true;
    expect(sendRequestToPlatform.calledOnce).to.be.true;
  });
  
  it('should send warning message to tester if method is not handled', async () => {
    samplePayload = {
      type: "CloudPlatform",
      params: {
        method: "randomMethod"
      },
      cloudSessionId: railsObj.sessionId,
      messageId: 0
    };
    const error = new Error("some fake error");
    mockGenerateRequestUrl.stub.throws(error);

    await obj.messageHandler(ws, JSON.stringify(samplePayload));
    expect(mockGenerateRequestUrl.stub.calledOnce).to.be.true;
    expect(mockSendCustomResponseToClient.stub.calledOnce).to.be.true;
    expect(sendRequestToPlatform.calledOnce).to.be.false;
  });

  it('should close connection if error in making reqeust to platform', async () => {
    const dummyRequest = (_, __, b) => {
      b.onResolve();
    };

    sendRequestToPlatform.stub = sinon.spy(dummyRequest);
    handler.__set__('sendRequestToPlatform', sendRequestToPlatform.stub);

    samplePayload = {
      type: "CloudPlatform",
      params: {
        method: "terminateApp"
      },
      cloudSessionId: railsObj.sessionId,
      messageId: 0
    };

    await obj.messageHandler(ws, JSON.stringify(samplePayload));
    expect(mockGenerateRequestUrl.stub.calledOnce).to.be.true;
    expect(sendRequestToPlatform.stub.calledOnce).to.be.true;
    expect(ws.close.calledOnce).to.be.true;
  });

  it('should send response to detox tester on platform call success', async () => {
    const dummyResponse = {
      statusCode : 200,
      data: JSON.stringify({
          success: true
    })};
    const dummyRequest = (_, __, ___, ____, _____, ______, _______, onSuccess) => {
      onSuccess(dummyResponse);
    };

    sendRequestToPlatform.stub = sinon.spy(dummyRequest);
    handler.__set__('sendRequestToPlatform', sendRequestToPlatform.stub);

    samplePayload = {
      type: "CloudPlatform",
      params: {
        method: "terminateApp"
      },
      cloudSessionId: railsObj.sessionId,
      messageId: 0
    };

    await obj.messageHandler(ws, JSON.stringify(samplePayload));
    expect(mockGenerateRequestUrl.stub.calledOnce).to.be.true;
    expect(sendRequestToPlatform.stub.calledOnce).to.be.true;
    expect(ws.close.calledOnce).to.be.false;
    expect(mockSendCustomResponseToClient.stub.calledOnce).to.be.true;
  });

  it('should send response to detox tester on platform call success', async () => {
    const dummyResponse = {
      statusCode : 200,
      data: JSON.stringify({
          success: true
    })};
    const dummyRequest = (_, __, ___, ____, _____, ______, _______, onSuccess) => {
      onSuccess(dummyResponse);
    };

    sendRequestToPlatform.stub = sinon.spy(dummyRequest);
    handler.__set__('sendRequestToPlatform', sendRequestToPlatform.stub);

    samplePayload = {
      type: "CloudPlatform",
      params: {
        method: "terminateApp"
      },
      cloudSessionId: railsObj.sessionId,
      messageId: 0
    };

    await obj.messageHandler(ws, JSON.stringify(samplePayload));
    expect(mockGenerateRequestUrl.stub.calledOnce).to.be.true;
    expect(sendRequestToPlatform.stub.calledOnce).to.be.true;
    expect(ws.close.calledOnce).to.be.false;
    expect(mockSendCustomResponseToClient.stub.calledOnce).to.be.true;
  });

  it('should close connection when failed to parse response body', async () => {
    const dummyResponse = {
      statusCode : 200,
      data: {
          success: true
      }
    };
    const dummyRequest = (_, __, ___, ____, _____, ______, _______, onSuccess) => {
      onSuccess(dummyResponse);
    };

    sendRequestToPlatform.stub = sinon.spy(dummyRequest);
    handler.__set__('sendRequestToPlatform', sendRequestToPlatform.stub);

    samplePayload = {
      type: "CloudPlatform",
      params: {
        method: "terminateApp"
      },
      cloudSessionId: railsObj.sessionId,
      messageId: 0
    };


    try{
      await obj.messageHandler(ws, JSON.stringify(samplePayload));
    } catch (e) {
      expect(jsonStub).to.throw(e);
    }
    expect(mockGenerateRequestUrl.stub.calledOnce).to.be.true;
    expect(sendRequestToPlatform.stub.calledOnce).to.be.true;
    expect(ws.close.calledOnce).to.be.true;
    expect(mockSendCustomResponseToClient.stub.calledOnce).to.be.false;
  });
});
