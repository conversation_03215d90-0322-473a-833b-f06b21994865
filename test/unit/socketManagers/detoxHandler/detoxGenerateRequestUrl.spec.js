'use strict';

const { expect } = require('chai');
const rewire = require('rewire');
const handler = rewire('../../../../socketManagers/detoxHandler');

describe('generateRequestUrl', () => {
  const DETOX_HANDLER = handler.__get__('DetoxHandler');
  let railsObj, obj, ws;

  beforeEach(() => {
    obj = new DETOX_HANDLER();

    railsObj = {
      sessionId: '1234-rails'
    };
  });

  it('should give launch detox instru url without terminate', async () => {
    const url = obj.generateRequestUrl('launchApp', {device: 'xyz'}, {x: 'y'}, railsObj.sessionId);
    const expectedUrl = '/launch_detox_instrumentation?device=xyz&session_id=1234-rails&args=%7B%22x%22%3A%22y%22%7D';
    expect(url).to.equal(expectedUrl);
  });

  it('should give launch detox instru url with terminate', async () => {
    const url = obj.generateRequestUrl('terminateApp', {device: 'xyz'}, {x: 'y'}, railsObj.sessionId);
    const expectedUrl = '/launch_detox_instrumentation?device=xyz&session_id=1234-rails&args=%7B%22x%22%3A%22y%22%7D&terminate_app=true';
    expect(url).to.equal(expectedUrl);
  });

  it('should throw error for unhandled method', async () => {
    expect(function(){obj.generateRequestUrl('random')}).to.throw('Method random is not handled.');
  });
});
