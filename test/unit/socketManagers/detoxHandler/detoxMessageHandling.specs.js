'use strict';

/* eslint-disable no-underscore-dangle */

const { expect } = require('chai');
const sinon = require('sinon');
const rewire = require('rewire');
const { wsProxy, mockLoggerSpies } = require('../socketHelpers');
const { kStartTimer } = require('../../../../config/socketConstants');

const handler = rewire('../../../../socketManagers/detoxHandler');

describe('Detox process message handling', () => {
  const DETOX_HANDLER = handler.__get__('DetoxHandler');
  let obj;
  let socketObj;
  const mockWaitForAllocation = {};
  const mockMethodHandler = {};
  let sendRequestToPlatform = {};
  const mockSendTerminal = {};
  const mockSendCustomResponseToClient = {};
  const mockGenerateRequestUrl = {};
  const mockGenericFn = {};
  const mockMethodMappingHas = {};
  const mockMethodMappingGet = {};
  const mockLogger = {};
  const startSpy = sinon.spy();
  let message;

  beforeEach(() => {
    obj = new DETOX_HANDLER();
    socketObj = wsProxy();

    message = {
      id: 10,
      type: 'CloudPlatform',
      params: {
        method: 'launchApp'
      },
      cloudSessionId: '123',
      messageId: 0
    };

    obj.railsSessionMapping = new Map();
    obj.railsSessionMapping.set(socketObj.id, '123');

    sendRequestToPlatform = sinon.stub();
    handler.__set__('sendRequestToPlatform', sendRequestToPlatform);

    obj.on(kStartTimer, startSpy);
    mockLogger.original = handler.__get__('logger');
    Object.values(mockLoggerSpies).forEach(el => el.reset());
    mockLogger.stub = mockLoggerSpies;
    mockWaitForAllocation.stub = sinon.stub(obj, 'waitForAllocation');
    mockMethodHandler.stub = sinon.spy(obj, 'methodHandler');
    mockSendCustomResponseToClient.stub = sinon.stub(obj, 'sendCustomResponseToClient');
    mockGenerateRequestUrl.stub = sinon.stub(obj, 'generateRequestUrl');
    mockSendTerminal.stub = sinon.stub(obj, 'sendToTerminal');
    mockGenericFn.stub = sinon.stub(obj, 'genericHandler');
    mockMethodMappingHas.stub = sinon.spy(obj.methodMapping, 'has');
    mockMethodMappingGet.stub = sinon.spy(obj.methodMapping, 'get');
    handler.__set__('logger', mockLogger.stub);
    handler.__set__('globalRegistry', {
      '123': {
        rproxyHost: 'test_rproxy_host',
        device: 'xyz'
      },
    });
  });

  afterEach(() => {
    startSpy.reset();
    mockWaitForAllocation.stub.restore();
    mockGenericFn.stub.restore();
    mockMethodHandler.stub.restore();
    mockMethodMappingHas.stub.restore();
    mockMethodMappingGet.stub.restore();
    handler.__set__('logger', mockLogger.original);
  });

  it('should run the method defined in method mapping', async () => {
    mockWaitForAllocation.stub.returns(Promise.resolve());
    await obj.messageHandler(socketObj, JSON.stringify(message));
    expect(socketObj.close.notCalled).to.be.true;
    expect(mockMethodMappingGet.stub.calledOnce).to.be.true;
  });

  it('should run generic handler if no method mapping present', async () => {
    message.type = 'random';
    mockWaitForAllocation.stub.returns(Promise.resolve());
    await obj.messageHandler(socketObj, JSON.stringify(message));
    expect(socketObj.close.notCalled).to.be.true;
    expect(mockMethodMappingGet.stub.notCalled).to.be.true;
  });

  it('should close the websocket in case of error', async () => {
    mockWaitForAllocation.stub.throws("Unable to allocate terminal");
    await obj.messageHandler(socketObj, JSON.stringify(message));
    expect(socketObj.close.calledOnce).to.be.true;
    expect(mockMethodMappingGet.stub.notCalled).to.be.true;
  });
});
