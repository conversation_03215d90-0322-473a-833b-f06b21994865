'use strict';

const { expect } = require('chai');
const rewire = require('rewire');
const handler = rewire('../../../../socketManagers/detoxHandler');

describe('injectCloudSessionId', () => {
  const DETOX_HANDLER = handler.__get__('DetoxHandler');
  let railsObj, obj;

  beforeEach(() => {
    obj = new DETOX_HANDLER();

    railsObj = {
      sessionId: '1234-rails'
    };
  });

  it('should add rails session id as cloudSession Id to given message', async () => {
    const data = {};
    const newData = obj.injectCloudSessionId(data, railsObj.sessionId);
    expect(newData).deep.equal({cloudSessionId: '1234-rails'});
  });
});
