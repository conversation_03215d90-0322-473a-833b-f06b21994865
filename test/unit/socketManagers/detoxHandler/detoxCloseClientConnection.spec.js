'use strict';

const sinon = require('sinon');
const { expect, assert } = require('chai');
const rewire = require('rewire');
const { wsProxy } = require('../socketHelpers');
const handler = rewire('../../../../socketManagers/detoxHandler');
const { firecmd_custom_exceptions } = require('../../../../constants')

describe('closeClientConnection', () => {
  const DETOX_HANDLER = handler.__get__('DetoxHandler');
  const BASE_SOCKET = handler.__get__('SocketMessageHandler');
  let railsObj, obj, ws, mockSuperClose;

  beforeEach(() => {
    obj = new DETOX_HANDLER();
    ws = wsProxy();

    railsObj = {
      sessionId: '1234-rails'
    };
    mockSuperClose = sinon.stub(BASE_SOCKET.prototype, 'closeClientConnection');
  });

  afterEach(() => {
    mockSuperClose.restore();
  })

  it('should send default message to client before closing socket connection', async () => {
    const clientMessage = {type: 'serverError', response: {message: "Something went wrong!"}};
    obj.closeClientConnection(ws);
    sinon.assert.calledWith(ws.send, JSON.stringify(clientMessage));
    expect(mockSuperClose.calledOnce).to.be.true;
  });

  it('should send Attempt error message to client before closing the socket connection', async () => {
    const attemptError = {message: firecmd_custom_exceptions.dac_download_and_install_app};
    const clientMessage = {type: 'serverError', response: {message: attemptError.message}};
    obj.closeClientConnection(ws, attemptError, 1011);
    sinon.assert.calledWith(ws.send, JSON.stringify(clientMessage));
    expect(mockSuperClose.calledOnce).to.be.true;
  });

  it('should send Attempt error message to client before closing the socket connection', async () => {
    const attemptError = {message: firecmd_custom_exceptions.detox_startup_failure};
    const clientMessage = {type: 'serverError', response: {message: attemptError.message}};
    obj.closeClientConnection(ws, attemptError, 1011);
    sinon.assert.calledWith(ws.send, JSON.stringify(clientMessage));
    expect(mockSuperClose.calledOnce).to.be.true;
  });

  it('should send Idle socket error message to client before closing the socket connection', async () => {
    const attemptError = {message: firecmd_custom_exceptions.detox_startup_failure};
    const clientMessage = {type: 'serverError', response: {message: 'Socket idle from a long time'}};
    obj.closeClientConnection(ws, 'Socket idle from a long time', 1001);
    sinon.assert.calledWith(ws.send, JSON.stringify(clientMessage));
    expect(mockSuperClose.calledOnce).to.be.true;
  });

  it('should not send Attempt error message to client if socket connection is closed', async () => {
    const attemptError = {message: firecmd_custom_exceptions.detox_startup_failure};
    const jsonStub = sinon.stub(JSON, 'stringify');
    jsonStub.throws(new Error('Something went wrong'));
    try{
      obj.closeClientConnection(ws, attemptError, 1011);
    } catch (e) {
      expect(jsonStub).to.throw(e);
    }
    sinon.assert.notCalled(ws.send);
    jsonStub.restore();
  });
});
