'use strict';

const { expect } = require('chai');
const { REMOTE_DEBUGGER_PORT } = require('../../../config/socketConstants');
const BrowserContext = require('../../../socketManagers/browserContext.js');

describe('#getBrowserArgs', () => {
  let data;
  beforeEach(() => {
    data = {};
  })

  context('Framework specific', () => {
    it('should raise no error for puppeteer', () => {
      const browserContext = new BrowserContext('chrome', 'puppeteer');
      let error;
      try {
        data = browserContext.getBrowserArgs(data)
      } catch (e) {
        error = e;
      }
      expect(error).to.be.undefined;
    })

    it('should throw error for non puppeteer frameworks', () => {
      const browserContext = new BrowserContext('chrome', 'playwright');
      let error;
      try {
        data = browserContext.getBrowserArgs(data)
      } catch (e) {
        error = e;
      }
      expect(error).to.not.be.undefined;
    })
  })

  context('Browser specific', () => {
    it('should return default chrome args', () => {
      const browserContext = new BrowserContext('chrome', 'puppeteer');
      data = browserContext.getBrowserArgs(data)
      expect(data).to.be.a('object');
      expect(data).to.have.keys(['chromeOptions'])
      expect(data['chromeOptions']).to.be.an('object');
      expect(data['chromeOptions']['args']).to.be.an('array');
      expect(data['chromeOptions']['args']).to.include(`--remote-debugging-port=${REMOTE_DEBUGGER_PORT}`)
    })

    it('should remove remote debugging port and replace with default for chrome', () => {
      const browserContext = new BrowserContext('chrome', 'puppeteer');
      data = {chromeOptions: {args: ['--remote-debugging-port=3000']}}
      data = browserContext.getBrowserArgs(data)
      expect(data).to.be.a('object');
      expect(data).to.have.keys(['chromeOptions'])
      expect(data['chromeOptions']).to.be.an('object');
      expect(data['chromeOptions']['args']).to.be.an('array');
      expect(data['chromeOptions']['args']).to.include(`--remote-debugging-port=${REMOTE_DEBUGGER_PORT}`)
    })

    it('should return default firefox args', () => {
      const browserContext = new BrowserContext('firefox', 'puppeteer');
      data = browserContext.getBrowserArgs(data)
      expect(data).to.be.a('object');
      expect(data).to.have.keys(['moz:firefoxOptions'])
      expect(data['moz:firefoxOptions']).to.be.an('object');
      expect(data['moz:firefoxOptions']['args']).to.be.an('array');
      expect(data['moz:firefoxOptions']['args']).to.include(`--remote-debugging-port=${REMOTE_DEBUGGER_PORT}`)
    })

    it('should remove remote debugging port and replace with default for firefox', () => {
      const browserContext = new BrowserContext('firefox', 'puppeteer');
      data = {'moz:firefoxOptions': {args: ['--remote-debugging-port=3000']}}
      data = browserContext.getBrowserArgs(data)
      expect(data).to.be.a('object');
      expect(data).to.have.keys(['moz:firefoxOptions'])
      expect(data['moz:firefoxOptions']).to.be.an('object');
      expect(data['moz:firefoxOptions']['args']).to.be.an('array');
      expect(data['moz:firefoxOptions']['args']).to.include(`--remote-debugging-port=${REMOTE_DEBUGGER_PORT}`)
    })

    it('should return default chromeOptions for edge', () => {
      const browserContext = new BrowserContext('edge', 'puppeteer');
      data = browserContext.getBrowserArgs(data)
      expect(data).to.be.a('object');
      expect(data).to.have.keys(['chromeOptions'])
      expect(data['chromeOptions']).to.be.an('object');
      expect(data['chromeOptions']['args']).to.be.an('array');
      expect(data['chromeOptions']['args']).to.include(`--remote-debugging-port=${REMOTE_DEBUGGER_PORT}`)
    })

    it('should remove remote debugging port and replace with default for edge', () => {
      const browserContext = new BrowserContext('edge', 'puppeteer');
      data = {chromeOptions: {args: ['--remote-debugging-port=3000']}}
      data = browserContext.getBrowserArgs(data)
      expect(data).to.be.a('object');
      expect(data).to.have.keys(['chromeOptions'])
      expect(data['chromeOptions']).to.be.an('object');
      expect(data['chromeOptions']['args']).to.be.an('array');
      expect(data['chromeOptions']['args']).to.include(`--remote-debugging-port=${REMOTE_DEBUGGER_PORT}`)
    })

    it('should throw error for other browsers', () => {
      const browserContext = new BrowserContext('opera', 'puppeteer');
      let error;
      try {
        data = browserContext.getBrowserArgs(data)
      } catch (e) {
        error = e;
      }
      expect(error).to.not.be.undefined;
    })
  })
})
 