'use strict';

const sinon = require('sinon');
const rewire = require('rewire');
const rewireHandler = rewire('../../../socketManagers/baseSocketMessageHandler');
const { assert } = require('chai');
const { mockLoggerSpies: mockLogger } = require('./socketHelpers');
const { wsProxy } = require('./socketHelpers');
const { kReqData } = require('../../../config/socketConstants');

describe('Proxy response from desktop to client', () => {
  const SocketMessageHandler = rewireHandler.__get__('SocketMessageHandler');
  let handler;
  const sessionId = "1234-session";
  let originalBasicLogger;

  beforeEach(() => {
    sinon.stub(SocketMessageHandler.prototype, 'enableMethodMaps', () => {});
    sinon.stub(SocketMessageHandler.prototype, 'debugScreenshotMethodMaps', () => {});
    sinon.stub(SocketMessageHandler.prototype, 'assignLogBuilder', () => {});
    handler = new SocketMessageHandler();
    originalBasicLogger = rewireHandler.__get__('logger');

    rewireHandler.__set__('logger', mockLogger);
  });

  afterEach(() => {
    SocketMessageHandler.prototype.enableMethodMaps.restore();
    SocketMessageHandler.prototype.debugScreenshotMethodMaps.restore();
    rewireHandler.__set__('logger', originalBasicLogger);
    SocketMessageHandler.prototype.assignLogBuilder.restore();
  });

  it('write the response on client socket', () => {
    const responseData = {
      id: 10,
      sessionId: '1234',
      results: [],
    };

    handler.sessionState.set(sessionId, {});
    sinon.stub(handler, 'writeResponse', () => {});
    handler.proxyResponseToClient(sessionId, JSON.stringify(responseData));

    assert(handler.writeResponse.calledOnce);

    handler.writeResponse.restore();
    handler.sessionState.delete(sessionId);
  });

  it('should not call the write response if socket is not present in state', () => {
    const responseData = {
      id: 10,
      results: {
        sessionId: '1234',
      },
    };

    sinon.stub(handler, 'writeResponse', () => {});
    handler.proxyResponseToClient(sessionId, JSON.stringify(responseData));

    assert(handler.writeResponse.notCalled);

    handler.writeResponse.restore();
  });

  it('handle error in the data when invalid JSON', () => {
    const responseData = 'Invalid JSON';

    handler.sessionState.set(sessionId, {});
    sinon.stub(handler, 'writeResponse', () => {});
    handler.proxyResponseToClient(sessionId, responseData);

    assert(handler.writeResponse.notCalled);

    handler.writeResponse.restore();
    handler.sessionState.delete(sessionId);
  });

  context('when server.playwrightVersion >= 1.15.0 and client.playwrightVersion < 1.15.0', () => {
    let ws;
    const getRegistryObjectStub = {};

    beforeEach(() => {
      handler = new SocketMessageHandler();
      originalBasicLogger = rewireHandler.__get__('logger');
      rewireHandler.__set__('logger', mockLogger);

      ws = wsProxy();
      ws.isPlaywright = true;
      handler.sessionState.set(ws.id, ws);
      getRegistryObjectStub.stub = sinon.stub(handler, "getRegistryObject");
      getRegistryObjectStub.stub.returns({ playwrightVersion: '1.15.2' });
    });

    afterEach(() => {
      getRegistryObjectStub.stub.restore();
      handler.sessionState.delete(ws.id);
    });

    it('modifies json data and then writes response to client socket when param type is Fetchrequest', () => {
      ws[kReqData] = {};
      const socketObjData = { 'client.playwrightVersion': '1.14.1' };
      ws[kReqData].data = socketObjData;
      const responseData = {
        id: 1,
        sessionId: '1234',
        results: [],
        method: '__create__',
        params: { type: 'FetchRequest' }
      };

      handler.sessionState.set(ws.id, ws);
      const writeResponseStub = sinon.stub(handler, 'writeResponse', () => {});
      handler.proxyResponseToClient(ws.id, JSON.stringify(responseData));

      sinon.assert.notCalled(writeResponseStub);

      handler.writeResponse.restore();
    });

    it('returns without writing to client socket when param type is Response', () => {
      ws[kReqData] = {};
      const socketObjData = { 'client.playwrightVersion': '1.14.1' };
      ws[kReqData].data = socketObjData;
      const responseData = {
        id: 10,
        sessionId: '1234',
        results: [],
        method: '__create__',
        params: { type: 'Response', initializer: { headers: [{ xyz: 'Headers' }] } }
      };

      const expectedResponseData = {
        id: 10,
        sessionId: '1234',
        results: [],
        method: '__create__',
        params: { type: 'Response', initializer: { headers: [{ xyz: 'Headers' }], requestHeaders: [{ xyz: 'Headers' }] } }
      };

      const writeResponseStub = sinon.stub(handler, 'writeResponse', () => {});
      handler.proxyResponseToClient(ws.id, JSON.stringify(responseData));

      sinon.assert.calledWithExactly(writeResponseStub, sinon.match.any, expectedResponseData, sinon.match.any);

      handler.writeResponse.restore();
    });
  });

  context('when server.playwrightVersion < 1.47.0 and client.playwrightVersion >= 1.47.0', () => {
    let ws;
    const getRegistryObjectStub = {};

    beforeEach(() => {
      handler = new SocketMessageHandler();
      originalBasicLogger = rewireHandler.__get__('logger');
      rewireHandler.__set__('logger', mockLogger);

      ws = wsProxy();
      ws.isPlaywright = true;
      handler.sessionState.set(ws.id, ws);
      getRegistryObjectStub.stub = sinon.stub(handler, "getRegistryObject");
    });

    afterEach(() => {
      getRegistryObjectStub.stub.restore();
      handler.sessionState.delete(ws.id);
    });

    it('modifies json data when param type is Playwright and client > 1.47 and server version < 1.47', () => {
      ws[kReqData] = {};
      const socketObjData = { 'client.playwrightVersion': '1.47.1' };
      getRegistryObjectStub.stub.returns({ playwrightVersion: '1.46.0' });
      ws[kReqData].data = socketObjData;
      const responseData = {
        id: 1,
        sessionId: "1234",
        results: [],
        method: "__create__",
        params: {
          type: "Playwright",
          initializer: {
            chromium: { guid: "browser-type@26a7b074909b4f84567db538d2fbb383" },
            firefox: { guid: "browser-type@1a5121386ac4a54eb33c44124e753fe0" },
            webkit: { guid: "browser-type@7a2cfe9f48ef659089e268648bd2f911" },
          },
        },
      };

      const expectedResponseData = {
        id: 1,
        sessionId: "1234",
        results: [],
        method: "__create__",
        params: {
          type: "Playwright",
          initializer: {
            chromium: { guid: "browser-type@26a7b074909b4f84567db538d2fbb383" },
            firefox: { guid: "browser-type@1a5121386ac4a54eb33c44124e753fe0" },
            webkit: { guid: "browser-type@7a2cfe9f48ef659089e268648bd2f911" },
            bidi: { guid: "browser-type@26a7b074909b4f84567db538d2fbb383" },
          },
        },
      };

      handler.sessionState.set(ws.id, ws);
      const writeResponseStub = sinon.stub(handler, 'writeResponse', () => {});
      handler.proxyResponseToClient(ws.id, JSON.stringify(responseData));

      sinon.assert.calledWithExactly(writeResponseStub, sinon.match.any, expectedResponseData, sinon.match.any);

      handler.writeResponse.restore();
    });

    it('modifies json data when param type is Playwright but server version > 1.47 and client version > 1.47.0', () => {
      ws[kReqData] = {};
      const socketObjData = { 'client.playwrightVersion': '1.48.0' };
      getRegistryObjectStub.stub.returns({ playwrightVersion: '1.47.0' });
      ws[kReqData].data = socketObjData;
      const responseData = {
        id: 1,
        sessionId: "1234",
        results: [],
        method: "__create__",
        params: {
          type: "Playwright",
          initializer: {
            bidiChromium: { guid: "browser-type@26a7b074909b4f84567db538d2fbb383" },
            bidiFirefox: { guid: "browser-type@1a5121386ac4a54eb33c44124e753fe0" },
            chromium: { guid: "browser-type@26a7b074909b4f84567db538d2fbb383" },
            firefox: { guid: "browser-type@1a5121386ac4a54eb33c44124e753fe0" },
            webkit: { guid: "browser-type@7a2cfe9f48ef659089e268648bd2f911" }
          },
        },
      };

      handler.sessionState.set(ws.id, ws);
      const writeResponseStub = sinon.stub(handler, 'writeResponse', () => {});
      handler.proxyResponseToClient(ws.id, JSON.stringify(responseData));

      sinon.assert.calledWithExactly(writeResponseStub, sinon.match.any, responseData, sinon.match.any);

      handler.writeResponse.restore();
    });

  });

  context('when server.playwrightVersion < 1.48.0 and client.playwrightVersion >= 1.48.0', () => {
    let ws;
    const getRegistryObjectStub = {};

    beforeEach(() => {
      handler = new SocketMessageHandler();
      originalBasicLogger = rewireHandler.__get__('logger');
      rewireHandler.__set__('logger', mockLogger);

      ws = wsProxy();
      ws.isPlaywright = true;
      handler.sessionState.set(ws.id, ws);
      getRegistryObjectStub.stub = sinon.stub(handler, "getRegistryObject");
    });

    afterEach(() => {
      getRegistryObjectStub.stub.restore();
      handler.sessionState.delete(ws.id);
    });

    it('modifies json data when param type is Playwright and client > 1.47 and server version < 1.47', () => {
      ws[kReqData] = {};
      const socketObjData = { 'client.playwrightVersion': '1.48.1' };
      getRegistryObjectStub.stub.returns({ playwrightVersion: '1.46.0' });
      ws[kReqData].data = socketObjData;
      const responseData = {
        id: 1,
        sessionId: "1234",
        results: [],
        method: "__create__",
        params: {
          type: "Playwright",
          initializer: {
            chromium: { guid: "browser-type@26a7b074909b4f84567db538d2fbb383" },
            firefox: { guid: "browser-type@1a5121386ac4a54eb33c44124e753fe0" },
            webkit: { guid: "browser-type@7a2cfe9f48ef659089e268648bd2f911" },
          },
        },
      };

      const expectedResponseData = {
        id: 1,
        sessionId: "1234",
        results: [],
        method: "__create__",
        params: {
          type: "Playwright",
          initializer: {
            chromium: { guid: "browser-type@26a7b074909b4f84567db538d2fbb383" },
            firefox: { guid: "browser-type@1a5121386ac4a54eb33c44124e753fe0" },
            webkit: { guid: "browser-type@7a2cfe9f48ef659089e268648bd2f911" },
            bidiChromium: { guid: "browser-type@26a7b074909b4f84567db538d2fbb383" },
            bidiFirefox: { guid: "browser-type@1a5121386ac4a54eb33c44124e753fe0" },
          },
        },
      };

      handler.sessionState.set(ws.id, ws);
      const writeResponseStub = sinon.stub(handler, 'writeResponse', () => {});
      handler.proxyResponseToClient(ws.id, JSON.stringify(responseData));

      sinon.assert.calledWithExactly(writeResponseStub, sinon.match.any, expectedResponseData, sinon.match.any);

      handler.writeResponse.restore();
    });

    it('doesn\'t modify json data when param type is Playwright and client >= 1.49 and server version <= 1.49', () => {
      ws[kReqData] = {};
      const socketObjData = { 'client.playwrightVersion': '1.49.0' };
      getRegistryObjectStub.stub.returns({ playwrightVersion: '1.49.0' });
      ws[kReqData].data = socketObjData;
      const responseData = {
        id: 1,
        sessionId: "1234",
        results: [],
        method: "__create__",
        params: {
          type: "Playwright",
          initializer: {
            chromium: { guid: "browser-type@26a7b074909b4f84567db538d2fbb383" },
            firefox: { guid: "browser-type@1a5121386ac4a54eb33c44124e753fe0" },
            webkit: { guid: "browser-type@7a2cfe9f48ef659089e268648bd2f911" },
          },
        },
      };

      const expectedResponseData = {
        id: 1,
        sessionId: "1234",
        results: [],
        method: "__create__",
        params: {
          type: "Playwright",
          initializer: {
            chromium: { guid: "browser-type@26a7b074909b4f84567db538d2fbb383" },
            firefox: { guid: "browser-type@1a5121386ac4a54eb33c44124e753fe0" },
            webkit: { guid: "browser-type@7a2cfe9f48ef659089e268648bd2f911" }
          },
        },
      };

      handler.sessionState.set(ws.id, ws);
      const writeResponseStub = sinon.stub(handler, 'writeResponse', () => {});
      handler.proxyResponseToClient(ws.id, JSON.stringify(responseData));

      sinon.assert.calledWithExactly(writeResponseStub, sinon.match.any, expectedResponseData, sinon.match.any);

      handler.writeResponse.restore();
    });

    it('modifies json data when param type is Playwright but server version > 1.47', () => {
      ws[kReqData] = {};
      const socketObjData = { 'client.playwrightVersion': '1.47.1' };
      getRegistryObjectStub.stub.returns({ playwrightVersion: '1.47.0' });
      ws[kReqData].data = socketObjData;
      const responseData = {
        id: 1,
        sessionId: "1234",
        results: [],
        method: "__create__",
        params: {
          type: "Playwright",
          initializer: {
            bidi: { guid: "browser-type@26a7b074909b4f84567db538d2fbb383" },
            bidiChromium: { guid: "browser-type@26a7b074909b4f84567db538d2fbb383" },
            bidiFirefox: { guid: "browser-type@1a5121386ac4a54eb33c44124e753fe0" },
            chromium: { guid: "browser-type@26a7b074909b4f84567db538d2fbb383" },
            firefox: { guid: "browser-type@1a5121386ac4a54eb33c44124e753fe0" },
            webkit: { guid: "browser-type@7a2cfe9f48ef659089e268648bd2f911" }
          },
        },
      };

      handler.sessionState.set(ws.id, ws);
      const writeResponseStub = sinon.stub(handler, 'writeResponse', () => {});
      handler.proxyResponseToClient(ws.id, JSON.stringify(responseData));

      sinon.assert.calledWithExactly(writeResponseStub, sinon.match.any, responseData, sinon.match.any);

      handler.writeResponse.restore();
    });

  });
});
