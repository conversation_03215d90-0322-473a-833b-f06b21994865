'use strict';

/* eslint-disable no-underscore-dangle */

const sinon = require('sinon');
const PUPPETEER_HANDLER = require('../../../socketManagers/puppeteerHandler');
const PLAYWRIGHT_HANDLER = require('../../../socketManagers/playwrightHandler');
const { expect } = require('chai');
const { wsProxy } = require('./socketHelpers');

describe('Abort request for authentication', () => {
  let pupHandler;
  let playHandler;
  let reqProxy;
  let ws;

  beforeEach(() => {
    pupHandler = new PUPPETEER_HANDLER();
    playHandler = new PLAYWRIGHT_HANDLER();
    reqProxy = {
      abort: sinon.stub()
    };
    ws = wsProxy();
  });

  describe('Puppeteer', () => {
    beforeEach(() => {
      pupHandler.authRequests.set(ws.id, reqProxy);
    });

    it('Should abort the upstream request if not cleared', async () => {
      await pupHandler.clientCloseHandler(ws, { code: 1006 });
      expect(reqProxy.abort.calledOnce).to.be.true;
      expect(pupHandler.authRequests.size).to.be.eql(0);
    });

    it('Should not do anything if no upstream request present', async () => {
      pupHandler.authRequests = new Map();
      pupHandler.authRequests.set('some-random-id', reqProxy);
      await pupHandler.clientCloseHandler(ws, { code: 1006 });
      expect(reqProxy.abort.notCalled).to.be.true;
      expect(pupHandler.authRequests.size).to.be.eql(1);
    });
  });

  describe('Playwright', () => {
    beforeEach(() => {
      playHandler.authRequests.set(ws.id, reqProxy);
    });

    it('Should abort the upstream request if not cleared', async () => {
      await playHandler.clientCloseHandler(ws, { code: 1006 });
      expect(reqProxy.abort.calledOnce).to.be.true;
      expect(playHandler.authRequests.size).to.be.eql(0);
    });

    it('Should not do anything if no upstream request present', async () => {
      playHandler.authRequests = new Map();
      playHandler.authRequests.set('some-random-id', reqProxy);
      await playHandler.clientCloseHandler(ws, { code: 1006 });
      expect(reqProxy.abort.notCalled).to.be.true;
      expect(playHandler.authRequests.size).to.be.eql(1);
    });
  });
});
