'use strict';

const sinon = require('sinon');
const rewire = require('rewire');
const { expect } = require('chai');

const rewireHandler = rewire('../../../socketManagers/puppeteerHandler');

describe('isdispatchMouseEvent', () => {
  let PuppeteerHandler;
  beforeEach(() => {
    PuppeteerHandler  = rewireHandler.__get__('PuppeteerHandler');
  });

  it('if the method is Input.dispatchMouseEvent should return true', () => {
    expect(PuppeteerHandler.isdispatchMouseEvent('Input.dispatchMouseEvent')).to.eql(true)
  });

  it('if the method is not Input.dispatchMouseEvent should return true', () => {
    expect(PuppeteerHandler.isdispatchMouseEvent('abcd')).to.eql(false)
  });
});

describe('modifyMouseMethod', () => {
  let PuppeteerHandler;
  let method;
  let data;
  beforeEach(() => {
    PuppeteerHandler  = rewireHandler.__get__('PuppeteerHandler');
  });

  it('if the data passed is valid, then the method returned is Input.${methodtype}Event', () => {
    data = {
      params: {
        type: 'efgh'
      }
    };
    method = 'Input.dispatchMouseEvent';
    let expectMethod = 'Input.efghEvent';
    expect(PuppeteerHandler.modifyMouseMethod(data, method)).to.eql(expectMethod);
  });

  it('if the data has no type, then the return method should be equal to the passed method', () => {
    data = {
      params: {}
    };
    method = 'qrst';
    let expectMethod = method
    expect(PuppeteerHandler.modifyMouseMethod(data, method)).to.eql(expectMethod);
  });

  it('if the data has no params, then the return method should be equal to the passed method', () => {
    data = {};
    method = 'qrst';
    let expectMethod = method
    expect(PuppeteerHandler.modifyMouseMethod(data, method)).to.eql(expectMethod);
  });
});


