'use strict';

const DesktopSocketManager = require('../../../socketManagers/desktopSocket');
const { kDesktopDataReceived, kReqData } = require('../../../config/socketConstants');
const { assert, expect } = require('chai');
const sinon = require('sinon');
const WebSocket = require('ws');

describe('Desktop socket manager', () => {
  let desktopSocketManager;
  let mockServer;
  const getGlobalWsObjectStub = {};
  const getGlobalRegistryObjectStub = {};
  const fakeURL = 'ws://localhost:8999';
  const SESSION_ID = 'session-1234';
  const socketOptions = {
    hostname: 'localhost',
  };

  beforeEach(() => {
    desktopSocketManager = new DesktopSocketManager();
    mockServer = new WebSocket.Server({ port: 8999 });
    getGlobalWsObjectStub.stub = sinon.stub(desktopSocketManager, 'getGlobalWsObject').returns({ isPlaywright: false });
    getGlobalRegistryObjectStub.stub = sinon.stub(desktopSocketManager, 'getGlobalRegistryObject').returns({ playwrightVersion: '1.15.2' });
  });

  afterEach(() => {
    sinon.restore();
    mockServer.close();
  });

  it('add socket to the state and resolve promise', async () => {
    mockServer.on('connection', (socket) => {
      socket.emit('open');
    });
    try {
      await desktopSocketManager.addNewSocket(SESSION_ID, fakeURL, socketOptions);
      assert(desktopSocketManager.socketsStates.size === 1);
    } catch (err) {
      assert(false);
    }
  });

  it('add socket to the state and resolve promise', async () => {
    const temp = {};
    temp.isPlaywright = true;
    temp[kReqData] = { data: { 'client.playwrightVersion': '1.14.1' } };
    getGlobalRegistryObjectStub.stub.returns({ railsSesionId: 'rails-1234', playwrightVersion: '1.15.2' });
    getGlobalWsObjectStub.stub.returns(temp);

    mockServer.on('connection', (socket) => {
      socket.emit('open');
    });
    try {
      await desktopSocketManager.addNewSocket(SESSION_ID, fakeURL, socketOptions);
      const responseData = {
        id: 1,
        guid: '',
        method: 'initialize',
        params: { sdkLanguage: 'javascript' },
        metadata: { stack: [], apiName: '' }
      };
      const desktopSocketManagerStub = {};
      const desktopSocketManagerStubSpy = sinon.spy();
      desktopSocketManagerStub.stub = sinon.stub(
        desktopSocketManager,
        'emit',
        desktopSocketManagerStubSpy
      );
      setTimeout(() => {
        sinon.assert.calledOnce(desktopSocketManagerStub.stub);
        sinon.assert.calledWithExactly(desktopSocketManagerStubSpy, kDesktopDataReceived, SESSION_ID, responseData);
        desktopSocketManagerStub.restore();
        desktopSocketManagerStubSpy.restore();
      }, 1500);
      assert(desktopSocketManager.socketsStates.size === 1);
    } catch (err) {
      assert(false);
    }
  });

  it('should emit event data received on socket from desktop', (done) => {
    mockServer.on('connection', (socket) => {
      socket.send('some-random-string');
    });

    try {
      desktopSocketManager.on(kDesktopDataReceived, (sessionId, data) => {
        expect(sessionId).to.eql(SESSION_ID);
        expect(data).to.eql('some-random-string');
        done();
      });
      desktopSocketManager.addNewSocket(SESSION_ID, fakeURL, socketOptions).catch(() => {
        assert(false);
        done();
      });
    } catch (err) {
      assert(false);
      done();
    }
  });

  it('should send data to desktop', async () => {
    mockServer.on('connection', (socket) => {
      socket.emit('open');  
    });

    try {
      await desktopSocketManager.addNewSocket(SESSION_ID, fakeURL, socketOptions);
      sinon.spy(desktopSocketManager.socketsStates, 'get');
      desktopSocketManager.sendDataToDesktop(SESSION_ID, 'some-random-string');
      assert(desktopSocketManager.socketsStates.size === 1);
      assert(desktopSocketManager.socketsStates.get.calledOnce);
    } catch (err) {
      console.log(err);
      assert(false);
    } finally {
      desktopSocketManager.socketsStates.get.restore();
    }
  });

  it('should terminate the desktop socket', () => {
    const spyObj = {
      terminate: sinon.spy(),
    };
    desktopSocketManager.socketsStates.set(SESSION_ID, spyObj);

    desktopSocketManager.terminateSocket(SESSION_ID);

    assert(desktopSocketManager.socketsStates.size === 0);
    assert(spyObj.terminate.calledOnce);
  });

  it('should terminate the desktop socket', () => {
    const spyObj = {
      terminate: sinon.spy(),
    };
    desktopSocketManager.socketsStates.set(SESSION_ID + '-random', spyObj);

    desktopSocketManager.terminateSocket(SESSION_ID);

    assert(desktopSocketManager.socketsStates.size === 1);
    assert(spyObj.terminate.notCalled);

    desktopSocketManager.socketsStates.clear();
  });

  describe('#getGlobalRegistryObject', () => {
    beforeEach(() => {
      desktopSocketManager = new DesktopSocketManager();
    });

    it('should return the correct object for a session id', () => {
      sinon.stub(desktopSocketManager, 'getGlobalRegistryObject').returns({ playwrightVersion: '1.15.2' });
      const result = desktopSocketManager.getGlobalRegistryObject('rails-1234');
      expect(result).to.eql({ playwrightVersion: '1.15.2' });
    });
  
    it('should return undefined for an invalid id', () => {
      const result = desktopSocketManager.getGlobalRegistryObject('dummy');
      expect(result).to.be.undefined;
    });
  });

  describe('#getGlobalWsObject', () => {
    beforeEach(() => {
      desktopSocketManager = new DesktopSocketManager();
    });

    it('should return the correct object for a session id', () => {
      sinon.stub(desktopSocketManager, 'getGlobalWsObject').returns({ playwrightVersion: '1.15.2' });
      const result = desktopSocketManager.getGlobalWsObject('rails-1234');
      expect(result).to.eql({ playwrightVersion: '1.15.2' });
    });
  
    it('should return undefined for an invalid id', () => {
      const result = desktopSocketManager.getGlobalWsObject('dummy');
      expect(result).to.be.undefined;
    });
  });
});
