'use strict';

const sinon = require('sinon');
const { expect } = require('chai');
const rewire = require('rewire');
const { wsProxy } = require('./socketHelpers');
const { kReqData } = require('../../../config/socketConstants');

const handler = rewire('../../../socketManagers/playwrightHandler');

describe('New Context creation in playwright session', () => {
  const PLAYWRIGHT_HANDLER = handler.__get__('PlaywrightHandler');
  const mockAllocationStub = {};
  const mockRegistryObject = {};
  const mockAddRequestLog = {};
  const mockDesktopSocketManager = {};
  const mockSendTerminal = {};
  let samplePayload;
  let railsObj;
  let ws;
  let obj;

  beforeEach(() => {
    obj = new PLAYWRIGHT_HANDLER();
    ws = wsProxy();
    railsObj = {
      sessionId: '1234-rails'
    };
    samplePayload = {
      id: 12,
      method: 'newContext',
      params: {
        sdkLanguage: 'javascript'
      }
    };
    mockAddRequestLog.stub = sinon.stub(obj, 'addRequestLog');
    mockAllocationStub.stub = sinon.stub(obj, 'waitForAllocation');
    mockRegistryObject.stub = sinon.stub(obj, 'getRegistryObject');
    mockDesktopSocketManager.stub = sinon.stub(obj.desktopSocketManager, 'emit');
    mockSendTerminal.stub = sinon.stub(obj, 'sendToTerminal');
    mockAllocationStub.stub.returns(Promise.resolve());
  });

  afterEach(() => {
    mockAllocationStub.stub.restore();
    mockRegistryObject.stub.restore();
    mockDesktopSocketManager.stub.restore();
    mockSendTerminal.stub.restore();
    mockAddRequestLog.stub.restore();
  });

  it('Should delete the unsupported caps from the params in newContext', async () => {
    samplePayload.params.recordVideo = { dir: './' };
    samplePayload.params.recordHar = { path: './random' };
    await obj.messageHandler(ws, JSON.stringify(samplePayload));
    expect(mockSendTerminal.stub.calledOnce).to.be.true;
    const { 0: options } = mockSendTerminal.stub.args[0];
    expect(options.params).to.not.have.any.keys('recordVideo', 'recordHar', '_traceDir');
  });

  it('Should not delete supported caps in newContext', async () => {
    samplePayload.params.validKey = 'validValue';
    await obj.messageHandler(ws, JSON.stringify(samplePayload));
    expect(mockSendTerminal.stub.calledOnce).to.be.true;
    const { 0: options } = mockSendTerminal.stub.args[0];
    expect(options.params).to.not.have.any.keys('recordVideo', 'recordHar', '_traceDir');
    expect(options.params).to.have.all.keys('sdkLanguage', 'validKey');
  });

  describe('.ignoreHTTPSErrorsHandler', () => {
    it('should set ignoreHTTPSErrors true when playwright-firefox + networksLogs', () => {
      ws[kReqData] = {};
      const socketObjData = { browser: 'playwright-firefox', 'browserstack.networkLogs': 'true' };
      ws[kReqData].data = socketObjData;
      const data = {};
      obj.ignoreHTTPSErrorsHandler(ws, data);
      expect(data.params.ignoreHTTPSErrors).to.be.true;
    });
  });
});
