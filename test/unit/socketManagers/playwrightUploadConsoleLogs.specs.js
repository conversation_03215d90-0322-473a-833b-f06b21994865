
/* eslint-disable no-underscore-dangle */

const { expect } = require('chai');
const sinon = require('sinon');
const rewire = require('rewire');
const HubLogger = require('../../../log');
const helper = require('../helper');
const assert = require('assert');
const { CONSOLE_LOG_LEVELS, VERBOSE_LOG_LEVEL } = require('../../../constants');
const handler = rewire('../../../socketManagers/playwrightHandler');

describe('canUploadConsoleLog' , () => {
  const PLAYWRIGHT_HANDLER = handler.__get__('PlaywrightHandler');
  let obj;
  const CONSOLE_MESSAGE = 'ConsoleMessage';
  const CREATE = '__create__';
  const CONSOLE_METHOD = 'console';

  beforeEach(() => {
    obj = new PLAYWRIGHT_HANDLER();
  });

  it('canUploadConsoleLog returns false when initializer type is undefined and initializer text is defined and type is not verbose', () => {
    let keyObject = helper.getKeyObject();
    uploadLogPartToKafka = sinon.stub(HubLogger, 'uploadLogPartToKafka');
    data = { method: CREATE, params: { type: CONSOLE_MESSAGE, initializer: { text: 'eventText' }} };
    PLAYWRIGHT_HANDLER.uploadConsoleLog(keyObject, data, CONSOLE_LOG_LEVELS.get('warnings'), '1.38.1');
    assert(uploadLogPartToKafka.called === false);
    HubLogger.uploadLogPartToKafka.restore();
  });

  it('canUploadConsoleLog returns false when messagetype is defined and text is defined and type is not verbose for playwright version > 1.39.0', () => {
    let keyObject = helper.getKeyObject();
    uploadLogPartToKafka = sinon.stub(HubLogger, 'uploadLogPartToKafka');
    data = { method: CONSOLE_METHOD, params: { type: 'log', text: 'eventText' } };
    PLAYWRIGHT_HANDLER.uploadConsoleLog(keyObject, data, CONSOLE_LOG_LEVELS.get('warnings'), '1.44.1');
    assert(uploadLogPartToKafka.called === false);
    HubLogger.uploadLogPartToKafka.restore();
  });

  it('canUploadConsoleLog returns true when initializer type is undefined and initializer text is defined and type is verbose', () => {
    let keyObject = helper.getKeyObject();
    uploadLogPartToKafka = sinon.stub(HubLogger, 'uploadLogPartToKafka');
    data = { method: CREATE, params: { type: CONSOLE_MESSAGE, initializer: { text: 'eventText' }} };
    PLAYWRIGHT_HANDLER.uploadConsoleLog(keyObject, data, CONSOLE_LOG_LEVELS.get('verbose'), '1.38.1');
    assert(uploadLogPartToKafka.called === true);
    HubLogger.uploadLogPartToKafka.restore();
  });

  it('canUploadConsoleLog returns true when messagetype is defined and text is defined and type is verbose for playwright version > 1.39.0', () => {
    let keyObject = helper.getKeyObject();
    uploadLogPartToKafka = sinon.stub(HubLogger, 'uploadLogPartToKafka');
    data = { method: CONSOLE_METHOD, params: { type: 'log', text: 'eventText' } };
    PLAYWRIGHT_HANDLER.uploadConsoleLog(keyObject, data, CONSOLE_LOG_LEVELS.get('verbose'), '1.44.1');
    assert(uploadLogPartToKafka.called === true);
    HubLogger.uploadLogPartToKafka.restore();
  });
});

describe('uploadLogPartToKafka and canUploadConsoleLog' , () => {
  const PLAYWRIGHT_HANDLER = handler.__get__('PlaywrightHandler');
  let keyObject, data, uploadLogPartToKafka;
  const CONSOLE_MESSAGE = 'ConsoleMessage';
  const CREATE = '__create__';

  beforeEach(() => {
    keyObject = helper.getKeyObject();
    uploadLogPartToKafka = sinon.stub(HubLogger, 'uploadLogPartToKafka');
  });

  afterEach(() => {
    HubLogger.uploadLogPartToKafka.restore();
  });

  it('if canUploadConsoleLog returns true and we have valid data then log uploaded to kafka', () => {
    data = { method: CREATE, params: { type: CONSOLE_MESSAGE, initializer: { type: 'log', text: 'eventText' }} };
    PLAYWRIGHT_HANDLER.uploadConsoleLog(keyObject, data, VERBOSE_LOG_LEVEL, '1.38.1');
    assert(uploadLogPartToKafka.calledOnce === true);
  });

  it('if canUploadConsoleLog returns false then dont upload log to kafka even if we have valid data', () => {
    data = { method: CREATE, params: { type: CONSOLE_MESSAGE, initializer: { type: 'warnings', text: 'eventText' }} };
    PLAYWRIGHT_HANDLER.uploadConsoleLog(keyObject, data, CONSOLE_LOG_LEVELS.get('log'), '1.44.1');
    assert(uploadLogPartToKafka.called === false);
  });
});

describe('uploadLogPartToKafka and data' , () => {
  const PLAYWRIGHT_HANDLER = handler.__get__('PlaywrightHandler');
  let keyObject, data, uploadLogPartToKafka;
  const CONSOLE_MESSAGE = 'ConsoleMessage';
  const CREATE = '__create__';

  beforeEach(() => {
    keyObject = helper.getKeyObject();
    uploadLogPartToKafka = sinon.stub(HubLogger, 'uploadLogPartToKafka');
  });

  afterEach(() => {
    HubLogger.uploadLogPartToKafka.restore();
  });

  it('if canUploadConsoleLog returns true but data.method is not create then no log uploaded to kafka', () => {
    data = { method: 'abc', params: { type: CONSOLE_MESSAGE, initializer: { type: 'eventType', text: 'eventText' }} };
    PLAYWRIGHT_HANDLER.uploadConsoleLog(keyObject, data, CONSOLE_LOG_LEVELS.get('verbose'), '1.38.1');
    assert(uploadLogPartToKafka.called === false);
  });

  it('if canUploadConsoleLog returns true but data.method is undefined then no log uploaded to kafka', () => {
    data = { params: { type: CONSOLE_MESSAGE, initializer: { type: 'eventType', text: 'eventText' }} };
    PLAYWRIGHT_HANDLER.uploadConsoleLog(keyObject, data, CONSOLE_LOG_LEVELS.get('verbose'), '1.38.1');
    assert(uploadLogPartToKafka.called === false);
  });

  it('if canUploadConsoleLog returns true but data.params.type is not ConsoleMessage then no log uploaded to kafka', () => {
    data = { method: CREATE, params: { type: 'abc', initializer: { type: 'eventType', text: 'eventText' }} };
    PLAYWRIGHT_HANDLER.uploadConsoleLog(keyObject, data, CONSOLE_LOG_LEVELS.get('verbose'), '1.38.1');
    assert(uploadLogPartToKafka.called === false);
  });

  it('if canUploadConsoleLog returns true but data.params.type is undefined then no log uploaded to kafka', () => {
    data = { method: CREATE, params: { initializer: { type: 'eventType', text: 'eventText' }} };
    PLAYWRIGHT_HANDLER.uploadConsoleLog(keyObject, data, CONSOLE_LOG_LEVELS.get('verbose'), '1.38.1');
    assert(uploadLogPartToKafka.called === false);
  });

  it('if canUploadConsoleLog returns true but data.params.initializer.type is undefined then no log uploaded to kafka', () => {
    data = { method: CREATE, params: { type: 'abc', initializer: { text: 'eventText' }} };
    PLAYWRIGHT_HANDLER.uploadConsoleLog(keyObject, data, CONSOLE_LOG_LEVELS.get('verbose'), '1.38.1');
    assert(uploadLogPartToKafka.called === false);
  });

  it('if params not present in data then no log uploaded to kafka', () => {
    data = { method: CREATE };
    PLAYWRIGHT_HANDLER.uploadConsoleLog(keyObject, data, CONSOLE_LOG_LEVELS.get('verbose'), '1.38.1');
    assert(uploadLogPartToKafka.called === false);
  });

  it('if params is empty hash then no log uploaded to kafka', () => {
    data = { method: CREATE, params: {} };
    PLAYWRIGHT_HANDLER.uploadConsoleLog(keyObject, data, CONSOLE_LOG_LEVELS.get('verbose'), '1.38.1');
    assert(uploadLogPartToKafka.called === false);
  });

  it('if initializer not present in data then no log uploaded to kafka', () => {
    data = { method: CREATE, params: { type: 'abc'} };
    PLAYWRIGHT_HANDLER.uploadConsoleLog(keyObject, data, CONSOLE_LOG_LEVELS.get('verbose'), '1.38.1');
    assert(uploadLogPartToKafka.called === false);
  });

  it('if initializer is empty hash then no log uploaded to kafka', () => {
    data = { method: CREATE, params: { type: 'abc', initializer: {}} };
    PLAYWRIGHT_HANDLER.uploadConsoleLog(keyObject, data, CONSOLE_LOG_LEVELS.get('verbose'), '1.38.1');
    assert(uploadLogPartToKafka.called === false);
  });
});
