'use strict';

const { expect } = require('chai');
const rewire = require('rewire');

const handler = rewire('../../../socketManagers/playwrightHandler');

describe('getPrivoxyPort in playwright-android session', () => {
  const PLAYWRIGHT_HANDLER = handler.__get__('PlaywrightHandler');
	let obj;

	beforeEach(() => {
    obj = new PLAYWRIGHT_HANDLER();
	});

  it('should return empty strings if specified port is undefined or an empty string or not an integer or less than 32000', () => {
    expect(obj.getPrivoxyPort()).to.be.equal("");
    expect(obj.getPrivoxyPort("")).to.be.equal("");
    expect(obj.getPrivoxyPort("abc")).to.be.equal("");
    expect(obj.getPrivoxyPort("30")).to.be.equal("");
    expect(obj.getPrivoxyPort("31999")).to.be.equal("");
    expect(obj.getPrivoxyPort(31999)).to.be.equal("");
  });

  it('should return valid port strings if specified port is not undefined and is an integer or convertible string to int and more than 32000', () => {
    expect(obj.getPrivoxyPort("38080")).to.be.equal("26080");
    expect(obj.getPrivoxyPort(38081)).to.be.equal("26081");
    expect(obj.getPrivoxyPort("58081")).to.be.equal("226081");
  });
});
