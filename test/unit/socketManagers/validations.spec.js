'use strict';


const { checkCredentials, sessionDebug, modifyPlaywright<PERSON>lient, extractPlaywrightConfig, getConsoleLogLevel, updateProxyBypassList } = require('../../../socketManagers/validations');
const { assert, expect } = require('chai');
const { CONSOLE_LOG_LEVELS, LH_MIN_CHROME } = require('../../../constants');
const { PLAYWRIGHT_LH_PORT } = require('../../../config/socketConstants');
const http = require('http');

describe('Validation on the json body in cdp', () => {
  it('should emit when username and access key is not present', () => {
    try {
      checkCredentials({});
    } catch (err) {
      assert(true);
      expect(err.message).to.eql('Access credentials missing');
    }
  });

  it('should allow when the username and passwords are present', () => {
    try {
      checkCredentials({
        'browserstack.username': 'some-username',
        'browserstack.accessKey': 'some-password',
      });
    } catch (err) {
      assert(false);
    }
  });

  it('should allow when the username and passwords are present in selenium mode', () => {
    try {
      checkCredentials({
        'browserstack.user': 'some-username',
        'browserstack.key': 'some-password',
      });
    } catch (err) {
      assert(false);
    }
  });

  it('should allow when the username and passwords are present in detox mode', () => {
    try {
      checkCredentials({
        'username': 'some-username',
        'accessKey': 'some-password',
      });
    } catch (err) {
      assert(false);
    }
  });
});

describe('Validation on the browserstack.debug flag', () => {
  it('should return false if value is not hash', () => {
    [undefined, null, true, false, 'random value', 10].forEach((val) => {
      expect(sessionDebug(val)).to.be.eql(false);
    });
  });

  it('should return false if data value is not hash', () => {
    [undefined, null, true, false, 'random value', 10].forEach((val) => {
      expect(sessionDebug({ data: val })).to.be.eql(false);
    });
  });

  it('should return false if the browserstack.debug key does not exists', () => {
    expect(sessionDebug({
      data: {
        key: 'OK',
      },
    })).to.be.eql(false);
  });

  it('should return true if the browserstack.debug key exists and is true', () => {
    [true, 'True', 'true', 'TrUe'].forEach((val) => {
      expect(sessionDebug({
        data: {
          'browserstack.debug': val,
        },
      })).to.be.eql(true);
    });
  });
});

describe('Check playwright user-agent', () => {
  let caps;
  let request;

  beforeEach(() => {
    caps = {
      'random-key': 'random-value',
      'client.playwrightVersion': '1.10.0'
    }
    request = new http.IncomingMessage();
  });

  describe('user-agent header is present', () => {
    it('If invalid format then no modification', () => {
      request.headers['user-agent'] = 'some-key';
      modifyPlaywrightClient(request, caps);
      expect(caps['client.playwrightVersion']).to.be.eql('1.10.0');
    });

    it('If valid format then update the version', () => {
      request.headers['user-agent'] = 'Playwright/1.11.1 (some_os_info)';
      modifyPlaywrightClient(request, caps);
      expect(caps['client.playwrightVersion']).to.be.eql('1.11.1');
    });

    it('If upstream version then update the numeric part', () => {
      request.headers['user-agent'] = 'Playwright/1.13.0-next (some_os_info)';
      modifyPlaywrightClient(request, caps);
      expect(caps['client.playwrightVersion']).to.be.eql('1.13.0');
    });
  });

  describe('user-agent header is not present', () => {
    it('If headers present but user-agent is not present', () => {
      modifyPlaywrightClient(request, caps);
      expect(caps['client.playwrightVersion']).to.be.eql('1.10.0');
    });

    it('If header is not present and an invaid object is sent', () => {
      modifyPlaywrightClient({}, caps);
      expect(caps['client.playwrightVersion']).to.be.eql('1.10.0');
    });
  });
});

describe('Validation on the extractPlaywrightConfig', () => {
  let railsCaps;
  let lCaps;
  let playwright_lh_arg;
  let proxyBypassList;

  beforeEach(() => {
    railsCaps = {};
    lCaps = {};
    playwright_lh_arg = `--remote-debugging-port=${PLAYWRIGHT_LH_PORT}`;
    proxyBypassList = `--proxy-bypass-list=a11y-engine.bsstag.com;a11y-engine-preprod.bsstag.com;a11y-engine.browserstack.com;<-loopback>`;
  });

  it('Should extract the playwright version if browser is not present then chromium', () => {
    railsCaps = { ...railsCaps, 'browserstack.playwrightVersion': '1.8.0' };
    const expectedResponse = {
      playwrightVersion: '1.8.0',
      browser: 'chromium',
      launchPersistentContext: 'false',
    };
    const resp = extractPlaywrightConfig(railsCaps, lCaps);
    expect(resp).to.deep.eql(expectedResponse);
  });

  it('Should extract the launch persistent context if mediaFiles caps is present', () => {
    railsCaps = { ...railsCaps, 'browserstack.playwrightVersion': '1.8.0', 'mediaFiles': ['somefile'] };
    const expectedResponse = {
      playwrightVersion: '1.8.0',
      browser: 'chromium',
      launchPersistentContext: 'true',
    };
    const resp = extractPlaywrightConfig(railsCaps, lCaps);
    expect(resp).to.deep.eql(expectedResponse);
  });

  it('Should return only browser if version is not present', () => {
    const expectedResponse = {
      browser: 'chromium',
      launchPersistentContext: 'false',
    };
    const resp = extractPlaywrightConfig(railsCaps, lCaps);
    expect(resp).to.deep.eql(expectedResponse);
  });

  it('Should return object with user passed browser and version of playwright from rails', () => {
    railsCaps = { ...railsCaps, 'browserstack.playwrightVersion': '1.8.0' };
    lCaps = { ...lCaps, browser: 'firefox' };
    const expectedResponse = {
      playwrightVersion: '1.8.0',
      browser: 'firefox',
      launchPersistentContext: 'false',
    };
    const resp = extractPlaywrightConfig(railsCaps, lCaps);
    expect(resp).to.deep.eql(expectedResponse);
  });
  
  it("Should return object with user passed args and firefox prefs of playwright", () => {
    railsCaps = { ...railsCaps, "browserstack.playwrightVersion": "1.8.0" };
    lCaps = { ...lCaps, browser: "firefox", args: ['--browserargs'], firefox_user_prefs: { 'user_pref' : true } };
    const expectedResponse = {
      playwrightVersion: "1.8.0",
      browser: "firefox",
      args: ["--browserargs"],
      firefoxUserPrefs: { user_pref: true },
      launchPersistentContext: 'false',
    };
    const resp = extractPlaywrightConfig(railsCaps, lCaps);
    expect(resp).to.deep.eql(expectedResponse);
  });

  it('Should return object with useBundledBrowsers if it is set.', () => {
    railsCaps = { ...railsCaps, 'browserstack.playwrightVersion': '1.8.0', 'browserstack.playwrightBundledBrowser': 'true' };
    lCaps = {
      ...lCaps,
      browser: "firefox"
    };
    const expectedResponse = {
      playwrightVersion: "1.8.0",
      browser: "firefox",
      useBundledBrowser: true,
      launchPersistentContext: 'false',
    };
    const resp = extractPlaywrightConfig(railsCaps, lCaps);
    expect(resp).to.deep.eql(expectedResponse);
  });

  it('Should add bypass proxy if accessibility is true', () => {
    railsCaps = { ...railsCaps, "browserstack.playwrightVersion": "1.8.0", "browserstack.accessibility": "true" };
    lCaps = { ...lCaps, browser: "firefox", args: ['--browserargs'], firefox_user_prefs: { 'user_pref' : true } };
    const resp = extractPlaywrightConfig(railsCaps, lCaps);
    expect(resp.args.includes(proxyBypassList)).eql(true);
  });

  it('Should not add bypass proxy if accessibility is false', () => {
    railsCaps = { ...railsCaps, "browserstack.playwrightVersion": "1.8.0", "browserstack.accessibility": "false" };
    lCaps = { ...lCaps, browser: "firefox", args: ['--browserargs'], firefox_user_prefs: { 'user_pref' : true } };
    const resp = extractPlaywrightConfig(railsCaps, lCaps);
    expect(resp.args.includes(proxyBypassList)).eql(false);
  });

  it('Should not add bypass proxy if accessibility is absent', () => {
    railsCaps = { ...railsCaps, "browserstack.playwrightVersion": "1.8.0" };
    lCaps = { ...lCaps, browser: "firefox", args: ['--browserargs'], firefox_user_prefs: { 'user_pref' : true } };
    const resp = extractPlaywrightConfig(railsCaps, lCaps);
    expect(resp.args.includes(proxyBypassList)).eql(false);
  });

  it('Should add bypass proxy if accessibility is true and args is missing', () => {
    railsCaps = { ...railsCaps, "browserstack.playwrightVersion": "1.8.0", "browserstack.accessibility": "true" };
    lCaps = { ...lCaps, browser: "firefox", firefox_user_prefs: { 'user_pref' : true } };
    const resp = extractPlaywrightConfig(railsCaps, lCaps);
    expect(resp.args.includes(proxyBypassList)).eql(true);
  });

  context('Playwright lighthouse validations', () => {
    const testSupportedVersion = (LH_MIN_CHROME + 10).toString();
    const testUnsupportedVersion = (LH_MIN_CHROME - 10).toString();
    it('should add remote debugging port browser is supported and version is supported', () => {
      lCaps = { ...lCaps, args: ['--browserargs'], browser_version: testSupportedVersion };
      console.log(lCaps)
      const expectedResponse = {
        browser: 'chromium',
        args: ['--browserargs', playwright_lh_arg],
        launchPersistentContext: 'false',
      };
      const resp = extractPlaywrightConfig(railsCaps, lCaps);
      expect(resp).to.deep.eql(expectedResponse);
    });

    it('should add remote debugging port browser is chrome and version is supported, even if args not present', () => {
      lCaps = { ...lCaps, browser_version: testSupportedVersion };
      console.log(lCaps)
      const expectedResponse = {
        browser: 'chromium',
        args: [playwright_lh_arg],
        launchPersistentContext: 'false',
      };
      const resp = extractPlaywrightConfig(railsCaps, lCaps);
      expect(resp).to.deep.eql(expectedResponse);
    });

    it('should not add remote debugging port when browser is supported and version is not supported', () => {
      lCaps = { ...lCaps, args: ['--browserargs'], browser_version: testUnsupportedVersion };
      const expectedResponse = {
        browser: 'chromium',
        args: ['--browserargs'],
        launchPersistentContext: 'false',
      };
      const resp = extractPlaywrightConfig(railsCaps, lCaps);
      expect(resp).to.deep.eql(expectedResponse);
    });

    it('should not add remote debugging port when browser is not supported', () => {
      lCaps = { ...lCaps, browser: 'firefox' };
      const expectedResponse = {
        browser: 'firefox',
        launchPersistentContext: 'false',
      };
      const resp = extractPlaywrightConfig(railsCaps, lCaps);
      expect(resp).to.deep.eql(expectedResponse);
    });

    it('should add ignoreDefaultArgs if present', () => {
      lCaps = { ...lCaps, ignoreDefaultArgs: ["--disable-component-update"] };
      const expectedResponse = {
        browser: 'chromium',
        ignoreDefaultArgs: ["--disable-component-update"],
        launchPersistentContext: 'false',
      };
      const resp = extractPlaywrightConfig(railsCaps, lCaps);
      expect(resp).to.deep.eql(expectedResponse);
    });
  })
});

describe('getConsoleLogLevel', () => {
  let options;
  let errorLogLevel = CONSOLE_LOG_LEVELS.get('errors');

  it('if options is undefined then should return default console log level errors', () => {
    expect(getConsoleLogLevel(options)).to.be.eql(errorLogLevel);
  });

  it('if options is empty hash and options.data is undefined then should return default console log level errors', () => {
    options = {};
    expect(getConsoleLogLevel(options)).to.be.eql(errorLogLevel);
  });

  it('if options.data is empty hash then should return default console log level errors', () => {
    options = {
      data: {}
    };
    expect(getConsoleLogLevel(options)).to.be.eql(errorLogLevel);
  });

  it('if options.data dont have any key browserstack.console then should return the default console log level errors', () => {
    options = {
      data: {
        'abc': 'xyz'
      }
    };
    expect(getConsoleLogLevel(options)).to.be.eql(errorLogLevel);
  });

  it('if options.data have key browserstack.console then should return the log level if it is valid', () => {
    options = {
      data: {
        'browserstack.console': 'warnings'
      }
    };
    expect(getConsoleLogLevel(options)).to.be.eql(CONSOLE_LOG_LEVELS.get(options.data['browserstack.console']));
  });

  it('if options.data have key browserstack.console then should return the errors as log level if it is no valid', () => {
    options = {
      data: {
        'browserstack.console': 'xyz'
      }
    };
    expect(getConsoleLogLevel(options)).to.be.eql(errorLogLevel);
  });
});

describe('Validation on the updateProxyBypassList', () => {
  let proxyPaths = "browserstack.com;<loopback>"
  it('--proxy-bypass-list is missing', () => {
    const config = { 
      args: [],
    }
    const expectedResponse = {
      args: [`--proxy-bypass-list=${proxyPaths}`]
    };
    updateProxyBypassList(config, proxyPaths);
    expect(config).to.deep.eql(expectedResponse);
  });

  it('--proxy-bypass-list is there ended by ;', () => {
    const testArg = "--proxy-bypass-list=testurl.com;"
    const config = { 
      args: [testArg],
    }
    const expectedResponse = {
      args: [`${testArg}${proxyPaths}`]
    };
    updateProxyBypassList(config, proxyPaths);
    expect(config).to.deep.eql(expectedResponse);
  });

  it('--proxy-bypass-list is there ended by =', () => {
    const testArg = "--proxy-bypass-list="
    const config = { 
      args: [testArg],
    }
    const expectedResponse = {
      args: [`${testArg}${proxyPaths}`]
    };
    updateProxyBypassList(config, proxyPaths);
    expect(config).to.deep.eql(expectedResponse);
  });

  it('--proxy-bypass-list is there ended by nothin', () => {
    const testArg = "--proxy-bypass-list=testurl.com"
    const config = { 
      args: [testArg],
    }
    const expectedResponse = {
      args: [`${testArg};${proxyPaths}`]
    };
    updateProxyBypassList(config, proxyPaths);
    expect(config).to.deep.eql(expectedResponse);
  });

  it('make sure function does not mutate something else', () => {
    const testArg = "--proxy-bypass-list=testurl.com;"
    const config = { 
      args: ["--someArg=ok;" , testArg , "--someArg2=", "--someArg3=ok"],
    }
    const expectedResponse = {
      args: ["--someArg=ok;" , `${testArg}${proxyPaths}`, "--someArg2=", "--someArg3=ok"]
    };
    updateProxyBypassList(config, proxyPaths);
    expect(config).to.deep.eql(expectedResponse);
  });

});
