'use strict';

const sinon = require('sinon');
const rewire = require('rewire');

const rewireHandler = rewire('../../../socketManagers/baseSocketMessageHandler');
const { assert } = require('chai');
const { mockLoggerSpies: mockLogger } = require('./socketHelpers');

describe('Proxy close from desktop to client', () => {
  const SocketMessageHandler = rewireHandler.__get__('SocketMessageHandler');
  let handler;
  const sessionId = '1234-session';
  let originalBasicLogger;

  beforeEach(() => {
    sinon.stub(SocketMessageHandler.prototype, 'enableMethodMaps', () => {});
    sinon.stub(SocketMessageHandler.prototype, 'debugScreenshotMethodMaps', () => {});
    sinon.stub(SocketMessageHandler.prototype, 'assignLogBuilder', () => {});
    handler = new SocketMessageHandler();
    originalBasicLogger = rewireHandler.__get__('logger');

    rewireHandler.__set__('logger', mockLogger);
  });

  afterEach(() => {
    SocketMessageHandler.prototype.enableMethodMaps.restore();
    SocketMessageHandler.prototype.debugScreenshotMethodMaps.restore();
    rewireHandler.__set__('logger', originalBasicLogger);
    SocketMessageHandler.prototype.assignLogBuilder.restore();
  });

  it('write close on client socket for valid status code', () => {
    const code = 1000;
    const reason = 'testing';
    const mockGetRegistryObject = sinon.stub(handler, 'getRegistryObject').returns({ serverAbruptClose: 'true', errorMessage: "some error" });
    handler.sessionState.set(sessionId, {});
    sinon.stub(handler, 'closeClientConnection', () => {});
    handler.proxyCloseToClient(sessionId, code, reason);
    assert(handler.closeClientConnection.calledOnce);
    sinon.assert.calledWith(handler.closeClientConnection, {}, reason, code);
    handler.closeClientConnection.restore();
    handler.sessionState.delete(sessionId);
    mockGetRegistryObject.restore();
  });

  it('terminate client socket for invalid status code', () => {
    const code = 100;
    const reason = 'testing';
    const mockGetRegistryObject = sinon.stub(handler, 'getRegistryObject').returns({ serverAbruptClose: 'true', errorMessage: "some error" });
    const terminateStub = sinon.stub();
    handler.sessionState.set(sessionId, { terminate: terminateStub });
    sinon.stub(handler, 'closeClientConnection', () => {});
    handler.proxyCloseToClient(sessionId, code, reason);
    assert(terminateStub.calledOnce);
    handler.closeClientConnection.restore();
    handler.sessionState.delete(sessionId);
    mockGetRegistryObject.restore();
  });
});
