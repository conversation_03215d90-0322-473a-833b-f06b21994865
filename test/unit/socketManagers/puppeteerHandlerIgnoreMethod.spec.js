'use strict';

const sinon = require('sinon');
const rewire = require('rewire');

const rewireHandler = rewire('../../../socketManagers/puppeteerHandler');



describe('should not upload raw logs for methods in ignoreMethods', () => {
  const PuppeteerHandler = rewireHandler.__get__('PuppeteerHandler');
  const RAILS_SESSION_ID = 'rails-1234';

  let handler;
  let stateData;
  let mockSendToKafka = {};
  let data;
  let keyObject;

  beforeEach(() => {
    handler = new PuppeteerHandler();
    mockSendToKafka.stub = sinon.stub(handler, 'sendToKafka');
    data = {
      id: 123,
      params: {
        key: 'value'
      }
    };
  });

  afterEach(() => {
    handler.rawLogState.clear();
    mockSendToKafka.stub.restore();
  });

  it('if method is present in ignoreMethod, sendtoKafka should not get called', () => {

    stateData = {
      123: {
      method: "Target.setAutoAttach",
      response: {},
      debug: {}
      }
    };
    handler.rawLogState.set(RAILS_SESSION_ID, stateData);
    handler.addNormalResponse(data, keyObject, RAILS_SESSION_ID);
    sinon.assert.notCalled(mockSendToKafka.stub);
  });

  it('if method is not present in ignoreMethod, sendtoKafka should get called', () => {
    stateData = {
      123: {
        method: "abcd",
        response: {},
        debug: {}
      }
    };
    handler.rawLogState.set(RAILS_SESSION_ID, stateData);
    handler.addNormalResponse(data, keyObject, RAILS_SESSION_ID);
    sinon.assert.calledOnce(mockSendToKafka.stub);
  });
});
