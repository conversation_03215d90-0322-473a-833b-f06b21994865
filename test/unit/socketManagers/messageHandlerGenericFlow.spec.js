'use strict';

const rewire = require('rewire');
const { assert, expect } = require('chai');
const { wsProxy } = require('./socketHelpers');
const sinon = require('sinon');
const { mockLoggerSpies: mockLogger } = require('./socketHelpers');

const rewireHandler = rewire('../../../socketManagers/baseSocketMessageHandler');

describe('message handler generic flow', () => {
  const SocketMessageHandler = rewireHandler.__get__('SocketMessageHandler');
  let handler;
  let logOriginal;
  let logSpy;
  let originalBasicLogger;

  beforeEach(() => {
    sinon.stub(SocketMessageHandler.prototype, 'enableMethodMaps', () => {});
    sinon.stub(SocketMessageHandler.prototype, 'debugScreenshotMethodMaps', () => {});
    sinon.stub(SocketMessageHandler.prototype, 'assignLogBuilder', () => {});
    logOriginal = rewireHandler.__get__('sessionLog');
    logSpy = sinon.spy();
    rewireHandler.__set__('sessionLog', logSpy);
    handler = new SocketMessageHandler();
    originalBasicLogger = rewireHandler.__get__('logger');

    rewireHandler.__set__('logger', mockLogger);
  });

  afterEach(() => {
    SocketMessageHandler.prototype.enableMethodMaps.restore();
    SocketMessageHandler.prototype.debugScreenshotMethodMaps.restore();
    SocketMessageHandler.prototype.assignLogBuilder.restore();
    rewireHandler.__set__('sessionLog', logOriginal);
    rewireHandler.__set__('logger', originalBasicLogger);
  });

  it('should follow the generic flow of message handler', async () => {
    const sampleData = {
      id: 1,
      method: 'Target.getBrowserContexts',
    };
    const message = JSON.stringify(sampleData);
    const ws = wsProxy();
    let processDataSpy;
    try {
      processDataSpy = sinon.stub(SocketMessageHandler.prototype, 'processData', sinon.spy());
      await handler.messageHandler(ws, message);

      assert(processDataSpy.calledOnce);
    } catch (err) {
      assert(false);
    } finally {
      processDataSpy.restore();
    }
  });
});

describe('mark session as idle', () => {
  const SocketMessageHandler = rewireHandler.__get__('SocketMessageHandler');
  let handler;
  let logOriginal;
  let logSpy;

  beforeEach(() => {
    sinon.stub(SocketMessageHandler.prototype, 'enableMethodMaps', () => {});
    sinon.stub(SocketMessageHandler.prototype, 'debugScreenshotMethodMaps', () => {});
    sinon.stub(SocketMessageHandler.prototype, 'assignLogBuilder', () => {});
    logOriginal = rewireHandler.__get__('sessionLog');
    logSpy = sinon.spy();
    rewireHandler.__set__('sessionLog', logSpy);
    handler = new SocketMessageHandler();
  });

  afterEach(() => {
    SocketMessageHandler.prototype.enableMethodMaps.restore();
    SocketMessageHandler.prototype.debugScreenshotMethodMaps.restore();
    SocketMessageHandler.prototype.assignLogBuilder.restore();
    rewireHandler.__set__('sessionLog', logOriginal);
  });
});

describe('close terminal socket', () => {
  const SocketMessageHandler = rewireHandler.__get__('SocketMessageHandler');
  let handler;
  let logOriginal;
  let logSpy;

  beforeEach(() => {
    sinon.stub(SocketMessageHandler.prototype, 'enableMethodMaps', () => {});
    sinon.stub(SocketMessageHandler.prototype, 'debugScreenshotMethodMaps', () => {});
    sinon.stub(SocketMessageHandler.prototype, 'assignLogBuilder', () => {});
    logOriginal = rewireHandler.__get__('sessionLog');
    logSpy = sinon.spy();
    rewireHandler.__set__('sessionLog', logSpy);
    handler = new SocketMessageHandler();
  });

  afterEach(() => {
    SocketMessageHandler.prototype.enableMethodMaps.restore();
    SocketMessageHandler.prototype.debugScreenshotMethodMaps.restore();
    SocketMessageHandler.prototype.assignLogBuilder.restore();
    rewireHandler.__set__('sessionLog', logOriginal);
  });

  it('closeterminalsocket and notify desktop manager', async () => {
    const ws = wsProxy();
    sinon.stub(SocketMessageHandler.prototype, 'getRegistryObject', () => ({
      railsSesionId: 'rails-1234',
    }));

    sinon.stub(handler.desktopSocketManager, 'terminateSocket', sinon.spy());

    handler.closeTerminalSocket(ws);

    assert(handler.desktopSocketManager.terminateSocket.calledOnce);

    SocketMessageHandler.prototype.getRegistryObject.restore();
    handler.desktopSocketManager.terminateSocket.restore();
  });
});

describe('connectToDesktop', () => {
  const SocketMessageHandler = rewireHandler.__get__('SocketMessageHandler');
  let handler;
  let logOriginal;
  let logSpy;

  beforeEach(() => {
    sinon.stub(SocketMessageHandler.prototype, 'enableMethodMaps', () => {});
    sinon.stub(SocketMessageHandler.prototype, 'debugScreenshotMethodMaps', () => {});
    sinon.stub(SocketMessageHandler.prototype, 'assignLogBuilder', () => {});
    logOriginal = rewireHandler.__get__('sessionLog');
    logSpy = sinon.spy();
    rewireHandler.__set__('sessionLog', logSpy);
    handler = new SocketMessageHandler();
  });

  afterEach(() => {
    SocketMessageHandler.prototype.enableMethodMaps.restore();
    SocketMessageHandler.prototype.debugScreenshotMethodMaps.restore();
    SocketMessageHandler.prototype.assignLogBuilder.restore();
    rewireHandler.__set__('sessionLog', logOriginal);
  });

  it('if promise resolves for desktop then resolves', async () => {
    const ws = wsProxy();
    sinon.stub(SocketMessageHandler.prototype, 'getRegistryObject', () => ({
      railsSesionId: 'rails-1234',
    }));

    sinon.stub(handler.desktopSocketManager, 'addNewSocket', () => Promise.resolve());

    await handler.connectToDesktopSocket('randomSessionId', 'ws://localhost:8080');

    assert(handler.desktopSocketManager.addNewSocket.calledOnce);

    SocketMessageHandler.prototype.getRegistryObject.restore();
    handler.desktopSocketManager.addNewSocket.restore();
  });
});

describe('should throw error for enable method maps', () => {
  const SocketMessageHandler = rewireHandler.__get__('SocketMessageHandler');

  it('should throw error if new subclass is created without enableMethodMaps impl', () => {
    try {
      class DummyClass extends SocketMessageHandler {
      }
      const obj = new DummyClass();
      assert(false);
    } catch (err) {
      expect(err.message).to.be.eql('Need to be implemented by the sub-classes');
    }
  });
});
