'use strict';

const sinon = require('sinon');
const { expect } = require('chai');
const rewire = require('rewire');
const { wsProxy } = require('./socketHelpers');
const ha = require('../../../ha');

const handler = rewire('../../../socketManagers/puppeteerHandler');

describe('Custom javascript executor for puppeteer', () => {
  const PUPPETEER_HANDLER = handler.__get__('PuppeteerHandler');
  const mockRegistryObject = {};
  const mockExecuteBStack = {};
  const mockDesktopSocketManager = {};
  const mockSendTerminal = {};
  const mockSendRequestLog = {};
  let samplePayload;
  let railsObj;
  let ws;
  let obj;

  beforeEach(() => {
    obj = new PUPPETEER_HANDLER();
    ws = wsProxy();
    railsObj = {
      sessionId: '1234-rails'
    };
    samplePayload = {
      id: 12,
      method: 'Runtime.callFunctionOn',
      sessionId: '1234',
      params: {
        arguments: [
          {
            value: 'browserstack_executor: {"status":"passed"}'
          }
        ],
        functionDeclaration: '_ => {}'
      }
    };
    const dummyExecutor = (_, b) => {
      b.onResolve();
    };
    mockSendRequestLog.stub = sinon.stub(obj, 'sendToRequestLog');
    mockExecuteBStack.original = handler.__get__('executeBStackExecutor');
    mockExecuteBStack.stub = sinon.spy(dummyExecutor);
    handler.__set__('executeBStackExecutor', mockExecuteBStack.stub);
    mockRegistryObject.stub = sinon.stub(obj, 'getRegistryObject');
    mockDesktopSocketManager.stub = sinon.stub(obj.desktopSocketManager, 'emit');
    mockSendTerminal.stub = sinon.stub(obj, 'sendToTerminal');
  });

  afterEach(() => {
    mockRegistryObject.stub.restore();
    mockSendRequestLog.stub.restore();
    handler.__set__('executeBStackExecutor', mockExecuteBStack.original);
    mockDesktopSocketManager.stub.restore();
    mockSendTerminal.stub.restore();
  });

  it('Should call the desktop manager to send response to client', async () => {
    mockRegistryObject.stub.returns(railsObj);
    await obj.executeJavascriptFunction(ws, samplePayload);
    expect(mockSendRequestLog.stub.calledOnce).to.be.true;
    expect(mockDesktopSocketManager.stub.calledOnce).to.be.true;
    expect(mockSendTerminal.stub.notCalled).to.be.true;
    const { 1: websocketId, 2: messageStr } = mockDesktopSocketManager.stub.args[0];
    const message = JSON.parse(messageStr);
    expect(websocketId).to.be.eql(ws.id);
    expect(message.id).to.be.eql(samplePayload.id);
  });

  it('Should not call the bstack executor if not a valid string', async () => {
    samplePayload.params.arguments[0].value = 'random string';
    await obj.executeJavascriptFunction(ws, samplePayload);
    expect(mockDesktopSocketManager.stub.notCalled).to.be.true;
    expect(mockSendTerminal.stub.calledOnce).to.be.true;
  });

  it('Should fetch the registry object from redis', async () => {
    mockRegistryObject.stub.returns(undefined);
    sinon.stub(ha, 'getData').yields(false, railsObj);
    await obj.executeJavascriptFunction(ws, samplePayload);
    expect(mockSendRequestLog.stub.calledOnce).to.be.true;
    expect(mockDesktopSocketManager.stub.calledOnce).to.be.true;
    expect(mockSendTerminal.stub.notCalled).to.be.true;
    const { 1: websocketId, 2: messageStr } = mockDesktopSocketManager.stub.args[0];
    const message = JSON.parse(messageStr);
    expect(websocketId).to.be.eql(ws.id);
    expect(message.id).to.be.eql(samplePayload.id);
    ha.getData.restore();
  });
});
