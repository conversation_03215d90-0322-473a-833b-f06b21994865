'use strict';

const rewire = require('rewire');
const { assert, expect } = require('chai');

const sinon = require('sinon');

const rewireHandler = rewire('../../../socketManagers/baseSocketMessageHandler');

describe('cleanup flow for cdp session', () => {
  const SocketMessageHandler = rewireHandler.__get__('SocketMessageHandler');
  let handler;
  let logOriginal;
  let logSpy;

  beforeEach(() => {
    sinon.stub(SocketMessageHandler.prototype, 'enableMethodMaps', () => {});
    sinon.stub(SocketMessageHandler.prototype, 'debugScreenshotMethodMaps', () => {});
    sinon.stub(SocketMessageHandler.prototype, 'assignLogBuilder', () => {});
    logOriginal = rewireHandler.__get__('sessionLog');
    logSpy = sinon.spy();
    rewireHandler.__set__('sessionLog', logSpy);
    handler = new SocketMessageHandler();
  });

  afterEach(() => {
    SocketMessageHandler.prototype.enableMethodMaps.restore();
    SocketMessageHandler.prototype.debugScreenshotMethodMaps.restore();
    SocketMessageHandler.prototype.assignLogBuilder.restore();
    rewireHandler.__set__('sessionLog', logOriginal);
  });

  describe('startTerminal cleanup', () => {
    let originalStop;
    let stopTerminalSpy;

    beforeEach(() => {
      stopTerminalSpy = sinon.stub();
      originalStop = rewireHandler.__get__('stopTerminal');
      rewireHandler.__set__('stopTerminal', stopTerminalSpy);
    });

    afterEach(() => {
      rewireHandler.__set__('stopTerminal', originalStop);
    });

    it('if no error in stop terminal should return data', async () => {
      try {
        const sampleData = {
          key: 'OK',
        };
        handler.railsSessionMapping.set('1234-session', '1234-result');
        stopTerminalSpy.returns(new Promise((res) => {
          res(sampleData);
        }));
        const data = await handler.startTerminalCleanup('1234-session');
        expect(data).to.deep.equal(sampleData);
      } catch (err) {
        assert(false);
      } finally {
        handler.railsSessionMapping.delete('1234-session');
      }
    });

    it('in case of error should return nil', async () => {
      try {
        handler.railsSessionMapping.set('1234-session', '1234-result');
        stopTerminalSpy.throws(new Error('unable to clean terminal'));
        const data = await handler.startTerminalCleanup('1234-session');
        expect(data).to.eql(null);
      } catch (err) {
        assert(false);
      } finally {
        handler.railsSessionMapping.delete('1234-session');
      }
    });
  });
});
