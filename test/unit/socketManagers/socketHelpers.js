'use strict';

const sinon = require('sinon');

const wsProxy = () => ({
  id: '1234',
  send: sinon.spy(),
  close: sinon.spy(),
});

const USERNAME = 'some-username';
const PASSWORD = 'some-password';

const generateRequest = (caps, kName = 'caps') => ({
  url: `/puppeteer?${kName}=${encodeURIComponent(JSON.stringify(caps))}`,
  headers: {
    authorization: `Basic ${Buffer.from(`${USERNAME}:${PASSWORD}`).toString('base64')}`,
    host: 'localhost:8081',
  },
});

const mockLoggerSpies = {
  error: sinon.spy(),
  info: sinon.spy(),
  warn: sinon.spy(),
  debug: sinon.spy(),
};

module.exports = {
  wsProxy,
  generateRequest,
  USERNAME,
  PASSWORD,
  mockLoggerSpies,
};
