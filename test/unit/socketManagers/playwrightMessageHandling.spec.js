'use strict';

/* eslint-disable no-underscore-dangle */

const { expect } = require('chai');
const sinon = require('sinon');
const rewire = require('rewire');
const { wsProxy, mockLoggerSpies } = require('./socketHelpers');
const { kStartTimer } = require('../../../config/socketConstants');

const handler = rewire('../../../socketManagers/playwrightHandler');

describe('Playwright process message handling', () => {
  const PLAYWRIGHT_HANDLER = handler.__get__('PlaywrightHandler');
  let obj;
  let socketObj;
  let railsObj;
  const mockPingZombie = {};
  const mockRegistryObject = {};
  const mockCheckValidPlaywrightCommand = {};
  const mockWaitForAllocation = {};
  const mockMethodHandler = {};
  const mockCloseHandler = {};
  const mockGenericFn = {};
  const mockSendToTermianl = {};
  const mockRequestLog = {};
  const mockMethodMappingHas = {};
  const mockMethodMappingGet = {};
  const mockLogger = {};
  const startSpy = sinon.spy();
  let message;

  beforeEach(() => {
    obj = new PLAYWRIGHT_HANDLER();
    socketObj = wsProxy();
    message = {
      id: 10,
      method: 'close'
    };
    railsObj = {
      sessionId: '1234-rails'
    };
    obj.on(kStartTimer, startSpy);
    mockLogger.original = handler.__get__('logger');
    Object.values(mockLoggerSpies).forEach(el => el.reset());
    mockLogger.stub = mockLoggerSpies;
    mockWaitForAllocation.stub = sinon.stub(obj, 'waitForAllocation');
    mockMethodHandler.stub = sinon.spy(obj, 'methodHandler');
    mockCloseHandler.stub = sinon.stub(obj, 'closeHandler');
    mockGenericFn.stub = sinon.stub(obj, 'genericHandler');
    mockSendToTermianl.stub = sinon.stub(obj, 'sendToTerminal');
    mockRequestLog.stub = sinon.stub(obj, 'addRequestLog');
    mockRegistryObject.stub = sinon.stub(obj, 'getRegistryObject');
    mockMethodMappingHas.stub = sinon.spy(obj.methodMapping, 'has');
    mockMethodMappingGet.stub = sinon.spy(obj.methodMapping, 'get');
    handler.__set__('logger', mockLogger.stub);
    handler.__set__('globalRegistry', {});
    mockPingZombie.original = handler.__get__('PingZombie');
    mockPingZombie.stub = sinon.stub();
    mockCheckValidPlaywrightCommand.original = handler.__get__('checkValidPlaywrightCommand');
    mockCheckValidPlaywrightCommand.stub = sinon.stub();
    handler.__set__('PingZombie', mockPingZombie.stub);
  });

  afterEach(() => {
    startSpy.reset();
    mockCloseHandler.stub.restore();
    mockWaitForAllocation.stub.restore();
    mockGenericFn.stub.restore();
    mockMethodHandler.stub.restore();
    mockSendToTermianl.stub.restore();
    mockRequestLog.stub.restore();
    mockMethodMappingHas.stub.restore();
    mockMethodMappingGet.stub.restore();
    mockRegistryObject.stub.restore();
    handler.__set__('logger', mockLogger.original);
    handler.__set__('PingZombie', mockPingZombie.original);
    handler.__set__('checkValidPlaywrightCommand', mockCheckValidPlaywrightCommand.original);
  });

  it('should run the method defined in method mapping', async () => {
    mockWaitForAllocation.stub.returns(Promise.resolve());
    await obj.messageHandler(socketObj, JSON.stringify(message));
    expect(socketObj.close.notCalled).to.be.true;
    expect(mockMethodMappingGet.stub.calledOnce).to.be.true;
  });

  it('should run generic handler if no method mapping present', async () => {
    message.method = 'random';
    mockWaitForAllocation.stub.returns(Promise.resolve());
    await obj.messageHandler(socketObj, JSON.stringify(message));
    expect(socketObj.close.notCalled).to.be.true;
    expect(mockMethodMappingGet.stub.notCalled).to.be.true;
  });

  it('should close the websocket in case of error', async () => {
    mockWaitForAllocation.stub.throws("Unable to allocate terminal");
    await obj.messageHandler(socketObj, JSON.stringify(message));
    expect(socketObj.close.calledOnce).to.be.true;
    expect(mockMethodMappingGet.stub.notCalled).to.be.true;
  });

  it('should send zombie event when method is `expectScreenshot`', async () => {
    const msg = {
      id: 10,
      method: 'expectScreenshot',
      params: {
        expected: "some buffer data",
        isNot: false
      }
    };

    mockWaitForAllocation.stub.returns(Promise.resolve());
    mockRegistryObject.stub.returns(railsObj);
    await obj.messageHandler(socketObj, JSON.stringify(msg));
    expect(mockPingZombie.stub.called).to.be.true;
  });

  it('should not send zombie event when method is not `expectScreenshot`', async () => {
    const msg = {
      id: 10,
      method: 'goto',
      params: {}
    };

    mockWaitForAllocation.stub.returns(Promise.resolve());
    mockRegistryObject.stub.returns(railsObj);
    mockCheckValidPlaywrightCommand.stub.returns(true)
    await obj.messageHandler(socketObj, JSON.stringify(msg));
    expect(mockPingZombie.stub.notCalled).to.be.true;
  });

  it("should not send zombie event when method is not `expectScreenshot` and is valid playwright command", async () => {
    const msg = {
      id: 10,
      method: "goto",
      params: {
        url: "http://localhost:3000",
      },
    };

    railsObj.translateLocalhostUrl = true;

    mockWaitForAllocation.stub.returns(Promise.resolve());
    mockRegistryObject.stub.returns(railsObj);
    mockCheckValidPlaywrightCommand.stub.returns(true);
    await obj.messageHandler(socketObj, JSON.stringify(msg));
    expect(mockPingZombie.stub.notCalled).to.be.true;
  });

  it('should not send zombie event when method is not `expectScreenshot` and is invalid playwright command', async () => {
    const msg = {
      id: 10,
      method: 'Page.navigate',
      params: {}
    };

    mockWaitForAllocation.stub.returns(Promise.resolve());
    mockRegistryObject.stub.returns(railsObj);
    mockCheckValidPlaywrightCommand.stub.returns(false)
    await obj.messageHandler(socketObj, JSON.stringify(msg));
    expect(mockPingZombie.stub.notCalled).to.be.true;
  });

  it('should not send zombie event when pingZombie throws error', async () => {
    const msg = {
      id: 10,
      method: 'expectScreenshot',
      params: {}
    };

    mockWaitForAllocation.stub.returns(Promise.resolve());
    mockRegistryObject.stub.returns(railsObj);
    mockPingZombie.stub.throws(new Error('Something went wrong'));
    try{
      await obj.messageHandler(socketObj, JSON.stringify(msg));
    } catch (e) {
      expect(mockPingZombie.stub).to.throw(e);
    }
  });

  it('should not send zombie event when no method is send i.e. with null msg', async () => {
    const msg = null

    mockWaitForAllocation.stub.returns(Promise.resolve());
    mockRegistryObject.stub.returns(railsObj);
    await obj.messageHandler(socketObj, msg);
    expect(mockPingZombie.stub.notCalled).to.be.true;
  });
});
