'use strict';

const rewire = require('rewire');
const { EventEmitter } = require('events');
const { assert, expect } = require('chai');
const sinon = require('sinon');
const { wsProxy, generateRequest } = require('./socketHelpers');
const logger = require('../../../logger').basicLogger;
const { PUPPETEER, PLAYWRIGHT } = require('../../../config/socketConstants');

const rewireHandler = rewire('../../../webSocketHandler');

describe('Websocket handler', () => {
  const WebSocketHandler = rewireHandler.__get__('WebSocketHandler');
  let handler;
  let puppeteerMessageSpy;
  let puppeteerCloseSpy;
  let originalPuppeteerHandler;
  let mockPlaywright;
  let mockPuppeteer;

  let playwrightMessageSpy;
  let playwrightCloseSpy;
  let originalPlaywrightHandler;

  beforeEach(() => {
    sinon.stub(logger, 'info');
    handler = new WebSocketHandler({ noServer: true });
    this.clock = sinon.useFakeTimers();
    puppeteerMessageSpy = sinon.spy();
    puppeteerCloseSpy = sinon.spy();
    playwrightMessageSpy = sinon.spy();
    playwrightCloseSpy = sinon.spy();

    mockPuppeteer = {
      preProcessAllocation: () => {},
      messageHandler: puppeteerMessageSpy,
      clientCloseHandler: puppeteerCloseSpy,
    };

    mockPlaywright = {
      preProcessAllocation: () => {},
      messageHandler: playwrightMessageSpy,
      clientCloseHandler: playwrightCloseSpy,
      getRegistryObject: sinon.stub(),
      updateTime: sinon.stub(),
      closeClientConnection: sinon.stub(),
    };

    originalPuppeteerHandler = rewireHandler.__get__('puppeteerHandler');
    originalPlaywrightHandler = rewireHandler.__get__('playwrightHandler');

    rewireHandler.__set__('puppeteerHandler', mockPuppeteer);
    rewireHandler.__set__('playwrightHandler', mockPlaywright);
  });

  afterEach(() => {
    logger.info.restore();
    rewireHandler.__set__('puppeteerHandler', originalPuppeteerHandler);
    rewireHandler.__set__('playwrightHandler', originalPlaywrightHandler);
    this.clock.restore();
  });

  it('should send to puppeteer message handler on message', () => {
    const ws = new EventEmitter();
    handler.connectionHandler(ws, { product: PUPPETEER, headers: { 'x-forwarded-for': 'abc' }  }, {});
    ws.emit('message', 'some-random-message');

    assert(puppeteerMessageSpy.calledOnce);
  });

  it('should call the close handler when disconnected', () => {
    const ws = new EventEmitter();
    sinon.stub(handler.idleSockets, 'has', () => true);
    handler.connectionHandler(ws, { product: PUPPETEER, headers: { 'x-forwarded-for': 'abc' } }, {});
    ws.emit('close');

    assert(puppeteerCloseSpy.calledOnce);

    handler.idleSockets.has.restore();
  });


  it('should receive ping & log', () => {
    const ws = new EventEmitter();
    handler.connectionHandler(ws, { product: PUPPETEER, headers: { 'x-forwarded-for': 'abc' } }, {});
    ws.emit('pong', () => {});
    expect(logger.info.callCount).to.be.eql(2);
  });

  describe('basic request payload checks', () => {
    it('should reject the promise since no authorization present', (done) => {
      const req = generateRequest({
        build: 'some-random-build',
      });

      delete req.headers.authorization;

      WebSocketHandler.checkSocketUpgrade(req, (err) => {
        expect(err).to.not.be.undefined;
        expect(err.message).to.eql('Access credentials missing');
        done();
      });
    });

    it('should resolve the promise in case of valid request', (done) => {
      const req = generateRequest({
        build: 'some-random-build',
      });

      WebSocketHandler.checkSocketUpgrade(req, (err, data) => {
        expect(err).to.be.null;
        expect(data).to.have.all.keys('build', 'browserstack.user', 'browserstack.key');
        done();
      });
    });
  });

  it('should handle the upgrade and call connection', (done) => {
    const ws = new EventEmitter();
    ws.product = PUPPETEER;
    sinon.stub(handler.webSocketServer, 'handleUpgrade').yields(ws);

    handler.webSocketServer.on('connection', () => {
      assert(true);
      done();
    });

    const dummyReq = { product: PUPPETEER, headers: { 'x-forwarded-for': 'abc' } };

    handler.handleUpgrade(dummyReq);
    handler.webSocketServer.handleUpgrade.restore();
  });

  it('should close the socket when time is greater than the idle time', () => {
    const ws = wsProxy();
    rewireHandler.__set__('puppeteerHandler', originalPuppeteerHandler);
    ws.product = PUPPETEER;
    const kUpdateTime = rewireHandler.__get__('kUpdateTime');
    const maxSocketTime = rewireHandler.__get__('MAX_SOCKET_IDLE');

    ws[kUpdateTime] = (new Date() - maxSocketTime);

    WebSocketHandler.prototype.closeIdleSockets(ws);
    assert(ws.close.calledOnce);
  });

  it('test provideHandler', () => {
    const provideHandler = rewireHandler.__get__('provideHandler');
    const playwrightHandler = rewireHandler.__get__('playwrightHandler');
    const seleniumHandler = rewireHandler.__get__('seleniumHandler');
    const puppeteerHandler = rewireHandler.__get__('puppeteerHandler');
    const detoxHandler = rewireHandler.__get__('detoxHandler');
    const handlerPlaywrite = provideHandler('playwright');
    assert(handlerPlaywrite === playwrightHandler);
    const handlerSelenium = provideHandler('selenium');
    assert(handlerSelenium === seleniumHandler);
    const handlerPuppeteer = provideHandler('puppeteer');
    assert(handlerPuppeteer === puppeteerHandler);
    const handlerDetox = provideHandler('detox');
    assert(handlerDetox === detoxHandler);
    assert.throws(() => {provideHandler('random');}, Error)
  });

  it('should not close the socket when time is greater than the idle time for playwright', () => {
    const USER_IDLE_TIME_VAL = 30000;
    const ws = wsProxy();
    ws.product = PLAYWRIGHT;
    mockPlaywright.getRegistryObject.returns({idle_timeout: USER_IDLE_TIME_VAL});
    const kUpdateTime = rewireHandler.__get__('kUpdateTime');
    const maxSocketTime = rewireHandler.__get__('MAX_SOCKET_IDLE');

    console.log(maxSocketTime)
    ws[kUpdateTime] = (new Date() - (USER_IDLE_TIME_VAL - 1000));

    WebSocketHandler.prototype.closeIdleSockets(ws);

    assert(mockPlaywright.closeClientConnection.notCalled);
  });


  it('should close the socket when time is greater than the idle time for playwright', () => {
    const USER_IDLE_TIME_VAL = 30000;
    const ws = wsProxy();
    ws.product = PLAYWRIGHT;
    mockPlaywright.getRegistryObject.returns({idle_timeout: USER_IDLE_TIME_VAL});
    const kUpdateTime = rewireHandler.__get__('kUpdateTime');
    const maxSocketTime = rewireHandler.__get__('MAX_SOCKET_IDLE');

    console.log(maxSocketTime)
    ws[kUpdateTime] = (new Date() - (USER_IDLE_TIME_VAL + 1000));

    WebSocketHandler.prototype.closeIdleSockets(ws);
    assert(mockPlaywright.closeClientConnection.calledOnce);
  });

});
