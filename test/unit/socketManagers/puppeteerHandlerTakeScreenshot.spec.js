'use strict';

const sinon = require('sinon');
const rewire = require('rewire');

const rewireHandler = rewire('../../../socketManagers/puppeteerHandler');
const { wsProxy } = require('./socketHelpers');
const { expect } = require('chai');
const S3UploadHelper = require('../../../helpers/s3UploadHelper');

describe('Take screenshots for puppeteer session', () => {
  const PuppeteerHandler = rewireHandler.__get__('PuppeteerHandler');
  const SocketMessageHandler = rewireHandler.__get__('SocketMessageHandler');
  const RAILS_SESSION_ID = 'rails-1234';

  let handler;
  let screenshotUploadStub;
  let writeResponseStub;

  beforeEach(() => {
    handler = new PuppeteerHandler();

    sinon.stub(SocketMessageHandler.prototype, 'addRequestLog');
    sinon.stub(SocketMessageHandler.prototype, 'addNormalResponse');
    writeResponseStub = sinon.stub(SocketMessageHandler.prototype, 'writeResponse');
    screenshotUploadStub = sinon
      .stub(S3UploadHelper, 'uploadScreenshotToS3')
      .returns(JSON.stringify({
        data:'base_64_string', 
        value: 's3_url_of_screenshot'
      }));
  });

  afterEach(() => {
    SocketMessageHandler.prototype.addRequestLog.restore();
    SocketMessageHandler.prototype.addNormalResponse.restore();
    writeResponseStub.restore();
    screenshotUploadStub.restore();
  });


  it('should take screenshot', () => {
    const ws = wsProxy();
    const keyObject = {
      s3bucket: 'test-bucket',
      rails_session_id: RAILS_SESSION_ID,
    };

    const stateObj = {
      id: 1,
      request: { method: 'Page.captureScreenshot' },
      response: {
        result: {
          data: 'base_64_string'
        }
      },
    };

    handler.railsSessionMapping.set(ws.id, RAILS_SESSION_ID);
    handler.sendToKafka(keyObject, stateObj, RAILS_SESSION_ID, {});
    expect(screenshotUploadStub.calledOnce).to.be.true;
  });

  it('should have data key in payload after takeScreenshot', () => {
    const ws = wsProxy();
    const keyObject = {
      s3bucket: 'test-bucket',
      rails_session_id: RAILS_SESSION_ID,
    };

    const stateObj = {
      id: 1,
      request: { method: 'Page.captureScreenshot' },
      response: {
        result: {
          data: 'base_64_string'
        }
      },
    };

    handler.railsSessionMapping.set(ws.id, RAILS_SESSION_ID);
    handler.sendToKafka(keyObject, stateObj, RAILS_SESSION_ID, {});
    expect(screenshotUploadStub.calledOnce).to.be.true;
    sinon.assert.match(stateObj.response.result, sinon.match.has('data'));
    sinon.assert.match(stateObj.response.result.data, 'base_64_string');
  });

  it('should should not take screenshot', () => {
    const ws = wsProxy();
    const keyObject = {
      s3bucket: 'test-bucket',
      rails_session_id: RAILS_SESSION_ID,
    };

    const stateObj = {
      id: 1,
      request: { method: 'Page.NOTcaptureScreenshot' },
      response: {
        result: {
          data: 'base_64_string'
        }
      },
    };

    handler.railsSessionMapping.set(ws.id, RAILS_SESSION_ID);
    handler.sendToKafka(keyObject, stateObj, RAILS_SESSION_ID, {});
    expect(screenshotUploadStub.calledOnce).to.be.false;
  });

  it('should call write response id for debug screenshots', () => {
    const ws = wsProxy();
    
    const data = {
      id: 1,
      method: 'Page.captureScreenshot',
      results: {},
    };

    handler.railsSessionMapping.set(ws.id, RAILS_SESSION_ID);
    handler.writeResponse(ws, data);
    expect(writeResponseStub.calledOnce).to.be.true;
  });
});
