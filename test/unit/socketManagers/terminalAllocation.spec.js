'use strict';

const rewire = require('rewire');

const rewiredTerminalAllocation = rewire('../../../socketManagers/terminalAllocation');
const sinon = require('sinon');
const { assert, expect } = require('chai');
const { describe } = require('mocha');
const logger = require('../../../logger').basicLogger;

describe('Terminal allocation for puppeteer session', () => {
  let originalRequest;
  let allocateTerminal;

  const defaultData = {
    os: 'os x',
    os_version: 'high sierra',
    browser: 'chrome',
    browser_version: 'latest',
    'browserstack.username': 'randomuser',
  };


  beforeEach(() => {
    originalRequest = rewiredTerminalAllocation.__get__('request');
    allocateTerminal = rewiredTerminalAllocation.__get__('allocateTerminal');
  });

  afterEach(() => {
    rewiredTerminalAllocation.__set__('request', originalRequest);
  });

  it('should set remote debugging options for chrome', async () => {
    try {
      const requestStub = sinon.stub();
      const basicData = { key: 'OK' };
      requestStub.returns({
        statusCode: 200,
        data: JSON.stringify(basicData),
      });
      rewiredTerminalAllocation.__set__('request', requestStub);
      const response = await allocateTerminal({ data: defaultData });

      assert(requestStub.calledOnce);
      assert.deepEqual(response, basicData);
    } catch (err) {
      assert(false);
    }
  });

  it('should set chrome options passed by user', async () => {
    try {
      const requestStub = sinon.stub();
      const data = {
        os: 'os x',
        os_version: 'high sierra',
        browser: 'chrome',
        browser_version: 'latest',
        'browserstack.username': 'randomuser',
        chromeOptions: {
          args: ['someArgs'],
          detach: 'false',
        },
      };
      const basicData = { key: 'OK' };
      requestStub.returns({
        statusCode: 200,
        data: JSON.stringify(basicData),
      });
      rewiredTerminalAllocation.__set__('request', requestStub);
      const response = await allocateTerminal({ data });

      assert(requestStub.calledOnce);
      assert.deepEqual(response, basicData);
    } catch (err) {
      assert(false);
    }
  });

  it('should sanitize chrome arguments if remote debugging port passed by user', async () => {
    try {
      const requestStub = sinon.stub();
      const data = {
        os: 'os x',
        os_version: 'high sierra',
        browser: 'chrome',
        browser_version: 'latest',
        'browserstack.username': 'randomuser',
        chromeOptions: {
          args: ['--remote-debugging-port=1111', 'test'],
          detach: 'true',
        },
      };
      const expectedResponse = {
        key: 'OK',
        data: {
          os: 'os x',
          os_version: 'high sierra',
          browser: 'chrome',
          browser_version: 'latest',
          'browserstack.username': 'randomuser',
          chromeOptions: {
            args: ['--remote-debugging-port=9222', 'test'],
            detach: 'true',
          },
        },
      };

      requestStub.returns({
        statusCode: 200,
        data: JSON.stringify(expectedResponse),
      });
      rewiredTerminalAllocation.__set__('request', requestStub);
      const response = await allocateTerminal({ data });

      assert(requestStub.calledOnce);
      assert.deepEqual(response, expectedResponse);
    } catch (err) {
      assert(false);
    }
  });

  it('should handle the request error when starting the allocation', async () => {
    const errObj = new Error('Unable to make request');
    try {
      const requestStub = sinon.stub();
      requestStub.throws(errObj);
      rewiredTerminalAllocation.__set__('request', requestStub);
      const response = await allocateTerminal({ data: defaultData });
      assert(requestStub.calledOnce);
    } catch (err) {
      expect(err.message).to.exist;
    }
  });

  it('should throw error when request code is non-200', async () => {
    try {
      const requestStub = sinon.stub();
      const basicData = { key: 'Page not found' };
      requestStub.returns({
        statusCode: 404,
        data: JSON.stringify(basicData),
      });
      rewiredTerminalAllocation.__set__('request', requestStub);
      await allocateTerminal({ data: defaultData });

      assert(false);
    } catch (err) {
      expect(err.message).to.exist;
    }
  });
});

describe('stop terminal flow for puppeteer', () => {
  let originalRequest;
  let stopTerminal;

  const railsSessionID = 'rails_1234';

  beforeEach(() => {
    originalRequest = rewiredTerminalAllocation.__get__('request');
    stopTerminal = rewiredTerminalAllocation.__get__('stopTerminal');
  });

  afterEach(() => {
    rewiredTerminalAllocation.__set__('request', originalRequest);
  });

  it('should handle the error in the request', async () => {
    const errObj = new Error('Unable to make request');
    try {
      const requestStub = sinon.stub();
      requestStub.throws(errObj);
      rewiredTerminalAllocation.__set__('request', requestStub);
      await stopTerminal(railsSessionID);
    } catch (err) {
      assert(err.message === 'Unable to stop the terminal');
    }
  });

  it('should return json data when successfull', async () => {
    try {
      const basicState = {
        key: 'OK',
      };
      const requestStub = sinon.stub();
      requestStub.returns({
        statusCode: 200,
        data: JSON.stringify(basicState),
      });
      rewiredTerminalAllocation.__set__('request', requestStub);
      const response = await stopTerminal(railsSessionID);

      assert(requestStub.calledOnce);
      assert.deepEqual(response, basicState);
    } catch (err) {
      assert(err.message === 'Unable to stop the terminal');
    }
  });

  it('should throw error when request code is non-200', async () => {
    try {
      const basicState = {
        key: 'Unable to process request',
      };
      const requestStub = sinon.stub();
      requestStub.returns({
        statusCode: 400,
        data: JSON.stringify(basicState),
      });
      rewiredTerminalAllocation.__set__('request', requestStub);
      await stopTerminal(railsSessionID);

      assert(false);
    } catch (err) {
      expect(err.message).to.be.eql('Unable to stop the terminal');
    }
  });
});

describe('IDLE Timeout flow for puppeteer', () => {
  let originalRequest;
  let markIdle;

  const railsSessionID = 'rails_1234';

  beforeEach(() => {
    originalRequest = rewiredTerminalAllocation.__get__('request');
    markIdle = rewiredTerminalAllocation.__get__('markIdle');
  });

  afterEach(() => {
    rewiredTerminalAllocation.__set__('request', originalRequest);
  });

  it('should handle the error in the request', async () => {
    const errObj = new Error('Unable to make request');
    try {
      const requestStub = sinon.stub();
      requestStub.throws(errObj);
      rewiredTerminalAllocation.__set__('request', requestStub);
      await markIdle(railsSessionID);
    } catch (err) {
      assert(err.message === 'Unable to make request');
    }
  });

  it('should return data when successfull', async () => {
    try {
      const basicState = {
        key: 'OK',
      };
      const requestStub = sinon.stub();
      requestStub.returns({
        statusCode: 200,
        data: JSON.stringify(basicState),
      });
      rewiredTerminalAllocation.__set__('request', requestStub);
      const response = await markIdle(railsSessionID);

      assert(requestStub.calledOnce);
      expect(response).to.be.eql(JSON.stringify(basicState));
    } catch (err) {
      assert(err.message === 'Unable to stop the terminal');
    }
  });

  it('should return data when successfull with error message', async () => {
    try {
      const basicState = {
        key: 'OK',
      };
      const requestStub = sinon.stub();
      requestStub.returns({
        statusCode: 200,
        data: JSON.stringify(basicState),
      });
      rewiredTerminalAllocation.__set__('request', requestStub);
      const response = await markIdle(railsSessionID, 'reason', 'some error');

      assert(requestStub.calledOnce);
      expect(response).to.be.eql(JSON.stringify(basicState));
    } catch (err) {
      assert(err.message === 'Unable to stop the terminal');
    }
  });

  it('should throw error when request code is non-200', async () => {
    const basicState = {
      key: 'Unable to process request',
    };
    try {
      const requestStub = sinon.stub();
      requestStub.returns({
        statusCode: 400,
        data: JSON.stringify(basicState),
      });
      rewiredTerminalAllocation.__set__('request', requestStub);
      await markIdle(railsSessionID);

      assert(false);
    } catch (err) {
      expect(err.message).to.be.eql(JSON.stringify(basicState));
    }
  });
});

describe('Get session status', () => {
  let originalRequest;
  let getSessionStatus;

  const railsSessionID = 'rails_1234';

  beforeEach(() => {
    originalRequest = rewiredTerminalAllocation.__get__('request');
    getSessionStatus = rewiredTerminalAllocation.__get__('getSessionStatus');
  });

  afterEach(() => {
    rewiredTerminalAllocation.__set__('request', originalRequest);
  });

  it('should handle the error in the request', async () => {
    const errObj = new Error('Unable to make request');
    const requestStub = sinon.stub();
    requestStub.throws(errObj);
    rewiredTerminalAllocation.__set__('request', requestStub);
    const loggerStub = sinon.stub(logger, 'info');
    await getSessionStatus(railsSessionID);

    assert(loggerStub.called.should.be.true);
    const dataWritten = loggerStub.getCall(0).args[0];
    assert(dataWritten == "Error in getSessionStatus while getting URL: Error: Unable to make request");
    loggerStub.restore();
  });

  it('should return successfull', async () => {
    try {
      const basicState = {
        key: 'OK',
      };
      const requestStub = sinon.stub();
      requestStub.returns({
        statusCode: 200,
        data: JSON.stringify(basicState),
      });
      rewiredTerminalAllocation.__set__('request', requestStub);
      await getSessionStatus(railsSessionID);

      assert(requestStub.calledOnce);
    } catch (err) {
      assert(err.message === 'Unable to send GET session status request');
    }
  });

  it('should not throw error when request code is non-200', async () => {
    const basicState = {
      key: 'Unable to send GET session status request',
    };
    try {
      const requestStub = sinon.stub();
      requestStub.returns({
        statusCode: 400,
        data: JSON.stringify(basicState),
      });
      rewiredTerminalAllocation.__set__('request', requestStub);
      await getSessionStatus(railsSessionID);

      assert(requestStub.calledOnce);
    } catch (err) {
      expect(err.message).to.be.eql(JSON.stringify(basicState));
    }
  });

  describe('#markIdle', () => {
    
  })
});
