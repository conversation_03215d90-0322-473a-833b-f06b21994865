'use strict';

const sinon = require('sinon');
const { expect } = require('chai');
const rewire = require('rewire');
const { wsProxy } = require('./socketHelpers');
const ha = require('../../../ha');

const handler = rewire('../../../socketManagers/playwrightHandler');

describe('Custom javascript executor for playwright', () => {
  const PLAYWRIGHT_HANDLER = handler.__get__('PlaywrightHandler');
  const mockAllocationStub = {};
  const mockRegistryObject = {};
  const mockExecuteBStack = {};
  const mockSendRequestLog = {};
  const mockDesktopSocketManager = {};
  const mockSendTerminal = {};
  let samplePayload;
  let samplePayloadWithCdp;
  let samplePayloadWithCdpV2;
  let railsObj;
  let ws;
  let obj;

  beforeEach(() => {
    obj = new PLAYWRIGHT_HANDLER();
    ws = wsProxy();
    railsObj = {
      sessionId: '1234-rails'
    };
    samplePayload = {
      id: 12,
      method: 'evaluateExpression',
      params: {
        arg: {
          value: {
            s: 'browserstack_executor: {"passed": "true"}'
          }
        }
      }
    };
    samplePayloadWithCdp = {
      id: 12,
      method: 'Runtime.callFunctionOn',
      sessionId: '1234',
      params: {
        arguments: [
          {}, {} ,{}, {}, {}, {},
          {
            value: 'browserstack_executor: {"status":"passed"}'
          }
        ],
        functionDeclaration: '_ => {}'
      }
    };
    samplePayloadWithCdpV2 = {
      id: 13,
      method: 'Runtime.callFunctionOn',
      sessionId: '1234',
      params: {
        arguments: [
          {}, {} ,{}, {}, {},
          {
            value: 'browserstack_executor: {"status":"passed"}'
          }
        ],
        functionDeclaration: '_ => {}'
      }
    };
    const dummyExecutor = (_, b) => {
      b.onResolve();
    };
    mockSendRequestLog.stub = sinon.stub(obj, 'sendToRequestLog');
    mockExecuteBStack.original = handler.__get__('executeBStackExecutor');
    mockExecuteBStack.stub = sinon.spy(dummyExecutor);
    handler.__set__('executeBStackExecutor', mockExecuteBStack.stub);
    mockAllocationStub.stub = sinon.stub(obj, 'waitForAllocation');
    mockRegistryObject.stub = sinon.stub(obj, 'getRegistryObject');
    mockDesktopSocketManager.stub = sinon.stub(obj.desktopSocketManager, 'emit');
    mockSendTerminal.stub = sinon.stub(obj, 'sendToTerminal');
    mockAllocationStub.stub.returns(Promise.resolve());
  });

  afterEach(() => {
    mockAllocationStub.stub.restore();
    mockSendRequestLog.stub.restore();
    mockRegistryObject.stub.restore();
    handler.__set__('executeBStackExecutor', mockExecuteBStack.original);
    mockDesktopSocketManager.stub.restore();
    mockSendTerminal.stub.restore();
  });

  it('Should call the page evaluate method', async () => {
    mockRegistryObject.stub.returns(railsObj);
    await obj.messageHandler(ws, JSON.stringify(samplePayload));
    expect(mockExecuteBStack.stub.calledOnce).to.be.true;
    expect(mockDesktopSocketManager.stub.calledOnce).to.be.true;
    const { 1: websocketId, 2: messageStr } = mockDesktopSocketManager.stub.args[0]
    expect(websocketId).to.be.eql(ws.id);
    const message = JSON.parse(messageStr);
    expect(message.id).to.be.eql(samplePayload.id);
    expect(mockSendTerminal.stub.notCalled).to.be.true;
  });

  it('Should not call the bstack executor if not a valid string', async () => {
    samplePayload.params.arg.value.s = 'random string';
    await obj.messageHandler(ws, JSON.stringify(samplePayload));
    expect(mockExecuteBStack.stub.notCalled).to.be.true;
    expect(mockSendTerminal.stub.called).to.be.true;
  });

  it('Should fetch the registry object from redis', async () => {
    mockRegistryObject.stub.returns(undefined);
    sinon.stub(ha, 'getData').yields(false, railsObj);
    await obj.messageHandler(ws, JSON.stringify(samplePayload));
    expect(mockExecuteBStack.stub.calledOnce).to.be.true;
    expect(mockDesktopSocketManager.stub.calledOnce).to.be.true;
    const { 1: websocketId, 2: messageStr } = mockDesktopSocketManager.stub.args[0]
    expect(websocketId).to.be.eql(ws.id);
    const message = JSON.parse(messageStr);
    expect(message.id).to.be.eql(samplePayload.id);
    expect(mockSendTerminal.stub.notCalled).to.be.true;
    ha.getData.restore();
  });

  context('jsExecutor', () => {
    it('Should call the desktop manager to send response to client', async () => {
      mockRegistryObject.stub.returns(railsObj);
      await obj.executeJavascriptFunction(ws, samplePayloadWithCdp, {});
      expect(mockSendRequestLog.stub.calledOnce).to.be.false;
      expect(mockDesktopSocketManager.stub.calledOnce).to.be.true;
      expect(mockSendTerminal.stub.notCalled).to.be.true;
      const { 1: websocketId, 2: messageStr } = mockDesktopSocketManager.stub.args[0];
      const message = JSON.parse(messageStr);
      expect(websocketId).to.be.eql(ws.id);
      expect(message.id).to.be.eql(samplePayloadWithCdp.id);
    });

    it('Should call the bstack executor if last argument in arguments list is valid', async () => {
      await obj.executeJavascriptFunction(ws, samplePayloadWithCdpV2, {});
      expect(mockDesktopSocketManager.stub.calledOnce).to.be.true;
      expect(mockSendTerminal.stub.notCalled).to.be.true;
    });

    it('Should not call the bstack executor if last argument in arguments list is not valid string', async () => {
      samplePayloadWithCdpV2.params.arguments[samplePayloadWithCdpV2.params.arguments.length-1].value = 'random string';
      await obj.executeJavascriptFunction(ws, samplePayloadWithCdpV2, {});
      expect(mockDesktopSocketManager.stub.notCalled).to.be.true;
      expect(mockSendTerminal.stub.calledOnce).to.be.true;
    });

    it('Should not call the bstack executor if arguments list is not an array', async () => {
      samplePayloadWithCdp.params.arguments = 'random string';
      await obj.executeJavascriptFunction(ws, samplePayloadWithCdp, {});
      expect(mockDesktopSocketManager.stub.notCalled).to.be.true;
      expect(mockSendTerminal.stub.calledOnce).to.be.true;
    });

    it('Should not call the bstack executor if arguments list is undefined', async () => {
      samplePayloadWithCdp.params.arguments = undefined;
      await obj.executeJavascriptFunction(ws, samplePayloadWithCdp, {});
      expect(mockDesktopSocketManager.stub.notCalled).to.be.true;
      expect(mockSendTerminal.stub.calledOnce).to.be.true;
    });

    it('Should not call the bstack executor if not a valid string', async () => {
      samplePayloadWithCdp.params.arguments[6].value = 'random string';
      await obj.executeJavascriptFunction(ws, samplePayloadWithCdp, {});
      expect(mockDesktopSocketManager.stub.notCalled).to.be.true;
      expect(mockSendTerminal.stub.calledOnce).to.be.true;
    });

    it('Should fetch the registry object from redis', async () => {
      mockRegistryObject.stub.returns(undefined);
      sinon.stub(ha, 'getData').yields(false, railsObj);
      await obj.executeJavascriptFunction(ws, samplePayloadWithCdp, {});
      expect(mockSendRequestLog.stub.calledOnce).to.be.false;
      expect(mockDesktopSocketManager.stub.calledOnce).to.be.true;
      expect(mockSendTerminal.stub.notCalled).to.be.true;
      const { 1: websocketId, 2: messageStr } = mockDesktopSocketManager.stub.args[0];
      const message = JSON.parse(messageStr);
      expect(websocketId).to.be.eql(ws.id);
      expect(message.id).to.be.eql(samplePayloadWithCdp.id);
      ha.getData.restore();
    });
  });
});
