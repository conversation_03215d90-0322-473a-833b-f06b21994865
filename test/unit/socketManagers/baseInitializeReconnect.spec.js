'use strict';

const sinon = require('sinon');
const { expect } = require('chai');
const rewire = require('rewire');
const { kReconnect } = require('../../../config/socketConstants');
const { wsProxy } = require('./socketHelpers');

const handler = rewire('../../../socketManagers/baseSocketMessageHandler.js');

describe('Initialize reconnect promise', () => {
  const SocketMessageHandler = handler.__get__('SocketMessageHandler');
  let ws;
  let obj;
  let reconnectConfigured;
  let fillreconnectData;
  let reconnectMessage;

  beforeEach(() => {
    ws = wsProxy();
    sinon.stub(SocketMessageHandler.prototype, 'assignLogBuilder');
    sinon.stub(SocketMessageHandler.prototype, 'enableMethodMaps');
    sinon.stub(SocketMessageHandler.prototype, 'debugScreenshotMethodMaps');
    obj = new SocketMessageHandler();
    reconnectConfigured = sinon.spy(obj.reconnectConfigured, 'set');
    fillreconnectData = sinon.stub(obj, 'fillReconnectData');
    reconnectMessage = `RECONNECT ${JSON.stringify({ railsSessionId: 'rails-123' })}`;
  });

  afterEach(() => {
    SocketMessageHandler.prototype.assignLogBuilder.restore();
    SocketMessageHandler.prototype.enableMethodMaps.restore();
    SocketMessageHandler.prototype.debugScreenshotMethodMaps.restore();
  });

  it('If reconnect present then call the fill reconnect data', () => {
    ws[kReconnect] = true;
    obj.initializeReconnect(ws, reconnectMessage);
    expect(reconnectConfigured.calledOnce).to.be.true;
  });

  it('If reconnect is not present then return noop', () => {
    ws[kReconnect] = false;
    obj.initializeReconnect(ws, reconnectMessage);
    expect(reconnectConfigured.notCalled).to.be.true;
  });

  it('If reconnect promise already set do not perform duplicate allocation', () => {
    ws[kReconnect] = true;
    sinon.stub(obj.reconnectConfigured, 'has').returns(true);
    obj.initializeReconnect(ws, reconnectMessage);
    expect(reconnectConfigured.notCalled).to.be.true;
  });
});
