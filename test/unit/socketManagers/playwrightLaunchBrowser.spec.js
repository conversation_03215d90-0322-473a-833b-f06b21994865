'use strict';

const sinon = require('sinon');
const { expect } = require('chai');
const rewire = require('rewire');
const { wsProxy } = require('./socketHelpers');
const { kReqData } = require('../../../config/socketConstants');

const handler = rewire('../../../socketManagers/playwrightHandler');

describe('Launch Browser Handler in playwright-android session', () => {
  const PLAYWRIGHT_HANDLER = handler.__get__('PlaywrightHandler');
  const mockAllocationStub = {};
  const mockRegistryObject = {};
  const mockAddRequestLog = {};
  const mockDesktopSocketManager = {};
  const mockSendTerminal = {};
  let samplePayload;
  let railsObj;
  let ws;
  let obj;

  beforeEach(() => {
    obj = new PLAYWRIGHT_HANDLER();
    ws = wsProxy();
    railsObj = {
      sessionId: '1234-rails'
    };
    samplePayload = {
      id: 12,
      method: 'launchBrowser',
      params: {
        noDefaultViewport: false,
        args: ['--data']
      }
    };
    mockAddRequestLog.stub = sinon.stub(obj, 'addRequestLog');
    mockAllocationStub.stub = sinon.stub(obj, 'waitForAllocation');
    mockDesktopSocketManager.stub = sinon.stub(obj.desktopSocketManager, 'emit');
    mockSendTerminal.stub = sinon.stub(obj, 'sendToTerminal');
    mockAllocationStub.stub.returns(Promise.resolve());
  });

  afterEach(() => {
    mockAllocationStub.stub.restore();
    mockDesktopSocketManager.stub.restore();
		mockRegistryObject.stub.restore();
    mockSendTerminal.stub.restore();
    mockAddRequestLog.stub.restore();
  });

  it('should add the new proxy object in the params of the samplePayload', async () => {
		mockRegistryObject.stub = sinon.stub(obj, 'getRegistryObject').returns({name: "0.0.0.0", port: 38083});
    await obj.messageHandler(ws, JSON.stringify(samplePayload));
    expect(mockSendTerminal.stub.calledOnce).to.be.true;
    const { 0: options } = mockSendTerminal.stub.args[0];
    expect(options.params).to.have.all.keys('noDefaultViewport', 'proxy', 'args');
  });

  it('should add the new proxy object in the params of the samplePayload even when no params are sent', async () => {
		mockRegistryObject.stub = sinon.stub(obj, 'getRegistryObject').returns({name: "0.0.0.0", port: 38083});
		delete samplePayload.params;
    await obj.messageHandler(ws, JSON.stringify(samplePayload));
    expect(mockSendTerminal.stub.calledOnce).to.be.true;
    const { 0: options } = mockSendTerminal.stub.args[0];
    expect(options.params).to.have.all.keys('proxy', 'args');
		expect(options.params).to.not.have.all.keys('noDefaultViewport');
		
  });

  it('should not add proxy when globalRegistry does not contain name property', async () => {
		mockRegistryObject.stub = sinon.stub(obj, 'getRegistryObject').returns({port: 38083});
    await obj.messageHandler(ws, JSON.stringify(samplePayload));
    expect(mockSendTerminal.stub.calledOnce).to.be.true;
    const { 0: options } = mockSendTerminal.stub.args[0];
    expect(options.params).to.not.have.all.keys('proxy');
		expect(options.params).to.have.all.keys('noDefaultViewport', 'args');
  });

	it('should not add proxy when globalRegistry does not contain port property', async () => {
		mockRegistryObject.stub = sinon.stub(obj, 'getRegistryObject').returns({name: "0.0.0.0"});
    await obj.messageHandler(ws, JSON.stringify(samplePayload));
    expect(mockSendTerminal.stub.calledOnce).to.be.true;
    const { 0: options } = mockSendTerminal.stub.args[0];
    expect(options.params).to.not.have.all.keys('proxy');
		expect(options.params).to.have.all.keys('noDefaultViewport', 'args');
  });

	it('should not add proxy when globalRegistry does not contain name and port property', async () => {
		mockRegistryObject.stub = sinon.stub(obj, 'getRegistryObject').returns({});
    await obj.messageHandler(ws, JSON.stringify(samplePayload));
    expect(mockSendTerminal.stub.calledOnce).to.be.true;
    const { 0: options } = mockSendTerminal.stub.args[0];
    expect(options.params).to.not.have.all.keys('proxy');
		expect(options.params).to.have.all.keys('noDefaultViewport', 'args');
  });

  it('should add the new args object in the params of the samplePayload', async () => {
		mockRegistryObject.stub = sinon.stub(obj, 'getRegistryObject').returns({name: "0.0.0.0", port: 38083});
    await obj.messageHandler(ws, JSON.stringify(samplePayload));
    expect(mockSendTerminal.stub.calledOnce).to.be.true;
    const { 0: options } = mockSendTerminal.stub.args[0];
    expect(options.params).to.have.all.keys('noDefaultViewport', 'proxy', 'args');
  });

});
