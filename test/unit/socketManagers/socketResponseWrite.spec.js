'use strict';

const rewire = require('rewire');
const cdpRedactor = require("../../../helpers/redactor/cdpRedactor");

const rewireHandler = rewire('../../../socketManagers/baseSocketMessageHandler');
const sinon = require('sinon');
const { assert, expect } = require('chai');
const { wsProxy } = require('./socketHelpers');
const errorMessageConstants = require('../../../errorMessages');

describe('Websocket response write', () => {
  const BaseSocketMessageHandler = rewireHandler.__get__('SocketMessageHandler');
  let handler;
  let originalSessionLog;
  let logSpy;

  beforeEach(() => {
    sinon.stub(BaseSocketMessageHandler.prototype, 'enableMethodMaps', () => {});
    sinon.stub(BaseSocketMessageHandler.prototype, 'debugScreenshotMethodMaps', () => {});
    sinon.stub(BaseSocketMessageHandler.prototype, 'assignLogBuilder', () => {});
    logSpy = sinon.spy();
    originalSessionLog = rewireHandler.__get__('sessionLog');
    rewireHandler.__set__('sessionLog', logSpy);
    handler = new BaseSocketMessageHandler();
    handler.rawLogBuilder = {
      updateLogEntry: sinon.spy(),
    };
  });

  afterEach(() => {
    BaseSocketMessageHandler.prototype.enableMethodMaps.restore();
    BaseSocketMessageHandler.prototype.debugScreenshotMethodMaps.restore();
    BaseSocketMessageHandler.prototype.assignLogBuilder.restore();
    rewireHandler.__set__('sessionLog', originalSessionLog);
  });

  it('should call the send method on socket', () => {
    const proxy = wsProxy();
    const returnData = {
      id: '1234',
      sessionId: '1234',
      results: [],
    };

    const expectedResponse = { ...returnData, sessionId: '1234-selenium' };

    handler.railsSessionMapping.set(proxy.id, '1234-selenium');
    sinon.stub(Date, 'now', () => 'ddddd');
    const keyObject = {};
    sinon.stub(handler, 'getRegistryObject').returns(keyObject);

    handler.writeResponse(proxy, returnData);

    assert(proxy.send.calledOnce);

    const proxyData = proxy.send.getCalls()[0].args[0];
    expect(proxyData).to.be.eql(JSON.stringify(expectedResponse));

    assert(logSpy.calledOnce);
    Date.now.restore();
    handler.getRegistryObject.restore();
  });

  it('should log and push feature usage for Playwright iOS unsupported command', () => {
    const sessionId = 'test-session-id';
    const railsSessionId = 'test-rails-session-id';
    const data = {
      error: {
        error: {
          BROWSERSTACK_METHOD_NOT_IMPLEMENTED: 'createTempFiles'
        }
      }
    };

    handler.railsSessionMapping = new Map();
    handler.railsSessionMapping.set(sessionId, railsSessionId);

    const pushFeatureUsageStub = sinon.stub();
    rewireHandler.__set__('pushFeatureUsage', pushFeatureUsageStub);
    sinon.stub(Date, 'now').returns(12345);

    handler.instrumentNotImplementedCommands(sessionId, data);

    sinon.assert.calledWith(pushFeatureUsageStub, railsSessionId, {
      commands: {
        notImplemented: {
          message: 'createTempFiles',
          timestamp: sinon.match.number
        }
      }
    });

    Date.now.restore();
  });

  it('should set error message and log for Playwright iOS unsupported command', () => {
    const keyObject = {};
    const data = {
      error: {
        error: {
          message: "some error",
          name: "error",
          BROWSERSTACK_METHOD_NOT_IMPLEMENTED: "createTempFiles"
        }
      }
    };

    const expectedMessage = errorMessageConstants.PLAYWRIGHT_IOS_UNSUPPORTED_COMMAND_ERROR("createTempFiles");

    handler.setErrorMessage(keyObject, data);

    expect(keyObject.errorMessage).to.eql(
      JSON.stringify({
        message: expectedMessage,
        name: "error"
      })
    );

    expect(data.log).to.eql([expectedMessage]);
  });

  it('should set error message in keyObject', () => {
    const keyObject = {};
    const data = { error: { error: { message: "some error", name: "error" } } };
    handler.setErrorMessage(keyObject, data);
    expect(keyObject.errorMessage).eql(JSON.stringify(data.error.error));
  });

  it('should mask responseData to kafka if framework is playwright', function() {
    const keyObject = {};
    keyObject.isPlaywright = true;
    keyObject.maskCommands = ['sendtype','setwebauthncredentials'];
    const RAILS_SESSION_ID = '1234-rails';
    const data = {
      id: 123,
      params: {
        key: 'value'
      },
      result: {
        binary: "abcd",
        result: {
          credentials: "abcd"
        }
      }
    };
    const logData = {
      id: 123,
      params: {
        key: 'value'
      },
      result: {
        binary: "abcd",
        result: {
          credentials: "REDACTED VALUE"
        }
      }
    };
    const options = {};
    const performanceLogLine = handler.generatePerformanceLog(data, keyObject);
    const responseLogLine = handler.rawLogBuilder.updateLogEntry('RESPONSE', options.method, {
      data: logData,
      sessionId: RAILS_SESSION_ID,
    });
    handler.addNormalResponse(data, keyObject, RAILS_SESSION_ID);
    sinon.assert.calledWith(logSpy, keyObject, 'RESPONSE', performanceLogLine + responseLogLine);
  });

  describe('isFindElementRequest', () => {
    it('should return true for allowed method and valid selector', () => {
      const req = { method: 'click', params: { selector: '.foo' } };
      expect(handler.isFindElementRequest(req)).to.be.true;
    });

    it('should return false for disallowed method', () => {
      const req = { method: 'notAllowed', params: { selector: '.foo' } };
      expect(handler.isFindElementRequest(req)).to.be.false;
    });

    it('should return false for selector containing ">>"', () => {
      const req = { method: 'click', params: { selector: 'foo >> bar' } };
      expect(handler.isFindElementRequest(req)).to.be.false;
    });

    it('should return false if selector is not a string', () => {
      const req = { method: 'click', params: { selector: 123 } };
      expect(handler.isFindElementRequest(req)).to.be.false;
    });
  });

  describe('incrementPwPerformanceData', () => {
    let getRegistryObjectStub;
    const sessionId = 'mock-session-id';
    let keyObject;

    beforeEach(() => {
      keyObject = {};
      getRegistryObjectStub = sinon.stub(handler, 'getRegistryObject').returns(keyObject);
    });

    afterEach(() => {
      getRegistryObjectStub.restore();
    });

    it('should initialize and increment the correct key', () => {
      handler.incrementPwPerformanceData(sessionId, 'total_request');
      expect(keyObject.pwPerformanceData).to.be.an('object');
      expect(keyObject.pwPerformanceData.total_request).to.equal(1);
      expect(keyObject.pwPerformanceData.find_element_success).to.equal(0);
    });

    it('should increment existing key', () => {
      keyObject.pwPerformanceData = {
        find_element_failure: 2,
        total_request: 5,
        total_find_element_request: 1,
        find_element_success: 0,
      };
      handler.incrementPwPerformanceData(sessionId, 'find_element_failure');
      expect(keyObject.pwPerformanceData.find_element_failure).to.equal(3);
    });
  });

  describe('findElementRequests map', () => {
    it('should set a new request id for a session', () => {
      const sessionId = 'session-1';
      const requestId = 42;
      // Initially, the session should not exist
      expect(handler.findElementRequests.has(sessionId)).to.be.false;

      // Add a new Set for the session and add a requestId
      handler.findElementRequests.set(sessionId, new Set());
      handler.findElementRequests.get(sessionId).add(requestId);

      expect(handler.findElementRequests.has(sessionId)).to.be.true;
      expect(handler.findElementRequests.get(sessionId).has(requestId)).to.be.true;
    });

    it('should retrieve the set of request ids for a session', () => {
      const sessionId = 'session-2';
      const requestIds = [1, 2, 3];
      handler.findElementRequests.set(sessionId, new Set(requestIds));

      const storedSet = handler.findElementRequests.get(sessionId);
      expect(storedSet).to.be.instanceOf(Set);
      expect(Array.from(storedSet)).to.have.members(requestIds);
    });
  });
});
