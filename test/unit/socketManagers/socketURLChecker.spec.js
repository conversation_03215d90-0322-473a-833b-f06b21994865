'use strict';

const { getJSONData } = require('../../../socketManagers/socketURLChecker');

const { assert, expect } = require('chai');

const { USERNAME, PASSWORD, generateRequest } = require('./socketHelpers');


describe('Socket URL checker', () => {
  it('should update the payload with the request headers', () => {
    const req = generateRequest({
      build: 'some-random-build',
    });
    const data = getJSONData(req);
    expect(data['browserstack.user']).to.eql(USERNAME);
    expect(data['browserstack.key']).to.eql(PASSWORD);
  });

  it('should throw error when caps key is not present', () => {
    try {
      const req = generateRequest({
        build: 'some-random-build',
      }, 'randomKey');
      getJSONData(req);
    } catch (err) {
      assert(true);
      expect(err.message).to.eql('caps key not present in query Args');
    }
  });

  it('should throw error when pathname is any random string', () => {
    try {
      const req = generateRequest({
        build: 'some-random-build',
      });
      req.url = `/helloworld?caps=${encodeURIComponent(JSON.stringify({build: "some-random-build"}))}`
      getJSONData(req);
    } catch (err) {
      assert(true);
      expect(err.message).to.eql('Does not allow this pathname');
    }
  });

  it('should not throw error when pathname is puppeteer', () => {
    const req = generateRequest({
      build: 'some-random-build',
    });
    req.url = `/puppeteer?caps=${encodeURIComponent(JSON.stringify({build: "some-random-build"}))}`
    const data = getJSONData(req);
    expect(data['browserstack.user']).to.eql(USERNAME);
    expect(data['browserstack.key']).to.eql(PASSWORD);
  });

  it('should not throw error when pathname is /, aka puppeteer', () => {
    const req = generateRequest({
      build: 'some-random-build',
    });
    req.url = `/?caps=${encodeURIComponent(JSON.stringify({build: "some-random-build"}))}`
    const data = getJSONData(req);
    expect(data['browserstack.user']).to.eql(USERNAME);
    expect(data['browserstack.key']).to.eql(PASSWORD);
  });

  it('should throw error when pathname is empty', () => {
    try {
      const req = generateRequest({
        build: 'some-random-build',
      });
      req.url = `?caps=${encodeURIComponent(JSON.stringify({build: "some-random-build"}))}`
    } catch (err) {
      assert(true);
      expect(err.message).to.not.eql('Does not allow this pathname');
    }
  });

  it('should not throw error when pathname is playwright', () => {
    const req = generateRequest({
      build: 'some-random-build',
    });
    req.url = `/playwright?caps=${encodeURIComponent(JSON.stringify({build: "some-random-build"}))}`
    const data = getJSONData(req);
    expect(data['browserstack.user']).to.eql(USERNAME);
    expect(data['browserstack.key']).to.eql(PASSWORD);
  });

  it('should not throw error when pathname is detox', () => {
    const req = generateRequest({
      build: 'some-random-build',
    });
    req.url = `/init?caps=${encodeURIComponent(JSON.stringify({build: "some-random-build"}))}`
    const data = getJSONData(req);
    expect(data['browserstack.user']).to.eql(USERNAME);
    expect(data['browserstack.key']).to.eql(PASSWORD);
  });

  it('should not throw error when pathname is selenium', () => {
    const req = generateRequest({
      build: 'some-random-build',
    });
    req.url = `/session/60447d6a7e152268353ba798bfb36c94e8dcb61a/se/cdp`
    const data = getJSONData(req);
    expect(data).to.be.empty;
  });
});
