'use strict';

/* eslint-disable no-underscore-dangle */

const { expect } = require('chai');
const sinon = require('sinon');
const rewire = require('rewire');
const { wsProxy, mockLoggerSpies } = require('./socketHelpers');
const { kStartTimer, PUPPETEER } = require('../../../config/socketConstants');

const handler = rewire('../../../webSocketHandler');

describe('Websocket socket close handler', () => {
  const WebsocketHandler = handler.__get__('WebSocketHandler');
  let obj;
  let ws;
  const mockPuppeteerHandler = {};

  beforeEach(() => {
    obj = new WebsocketHandler({ noServer: true });
    ws = wsProxy();
    ws.isPlaywright = false;
    ws.product = PUPPETEER;
    mockPuppeteerHandler.orig = handler.__get__('puppeteerHandler');
    mockPuppeteerHandler.stub = sinon.stub(mockPuppeteerHandler.orig, 'clientCloseHandler');
  });

  afterEach(() => {
    mockPuppeteerHandler.stub.restore();
  });

  it('If sent 1005 code and empty object treat as conclude event', () => {
    obj.socketCloseHandler(1005, '', ws);
    const { 1: options } = mockPuppeteerHandler.stub.args[0];
    expect(options).to.have.any.keys('code');
    expect(options.code).to.be.eql(1001);
  });

  it('If sent 1005 code and non empty object then treat as non conclude', () => {
    obj.socketCloseHandler(1005, 'This is terminated by client', ws);
    const { 1: options } = mockPuppeteerHandler.stub.args[0];
    expect(options).to.have.any.keys('code');
    expect(options.code).to.be.eql(1005);
  });

  it('If message sent SERVICE_RESTART then it should clear idleTimeout', () => {
    obj.socketCloseHandler(undefined, 'Service Restart', ws);
    expect(mockPuppeteerHandler.stub.called).to.be.false; 
  });
});
