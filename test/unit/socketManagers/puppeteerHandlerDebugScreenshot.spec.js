'use strict';

const sinon = require('sinon');
const rewire = require('rewire');

const rewireHandler = rewire('../../../socketManagers/puppeteerHandler');
const { kSessionDebug } = require('../../../config/socketConstants');
const { wsProxy } = require('./socketHelpers');
const { expect } = require('chai');
const helper = require('../../../helper');

describe('Debug screenshots for puppeteer session', () => {
  const PuppeteerHandler = rewireHandler.__get__('PuppeteerHandler');
  const SocketMessageHandler = rewireHandler.__get__('SocketMessageHandler');
  const RAILS_SESSION_ID = 'rails-1234';

  let handler;
  let screenshotUploadStub;
  let writeResponseStub;
  let addNormalResponseStub;

  beforeEach(() => {
    handler = new PuppeteerHandler();

    sinon.stub(SocketMessageHandler.prototype, 'addRequestLog');
    sinon.stub(SocketMessageHandler.prototype, 'addDebugLog');
    writeResponseStub = sinon.stub(SocketMessageHandler.prototype, 'writeResponse');
    screenshotUploadStub = sinon.stub(helper, 'takeScreenshotAndUpload');
    addNormalResponseStub = sinon.stub(SocketMessageHandler.prototype, 'addNormalResponse');
  });

  afterEach(() => {
    SocketMessageHandler.prototype.addRequestLog.restore();
    SocketMessageHandler.prototype.addDebugLog.restore();
    writeResponseStub.restore();
    screenshotUploadStub.restore();
    addNormalResponseStub.restore();
  });

  context('when browserstack.debug is set to true', () => {
    it('should take screenshots', () => {
      const ws = wsProxy();
      const keyObject = {
        s3bucket: 'test-bucket',
        rails_session_id: RAILS_SESSION_ID,
      };

      const stateObj = {
        debug: {
          screenshotCounter: 1,
        },
        id: 1,
        request: { method: 'Page.navigate' },
        results: {},
      };

      ws[kSessionDebug] = true;

      handler.railsSessionMapping.set(ws.id, RAILS_SESSION_ID);
      expect(addNormalResponseStub.calledOnce).to.be.false;
      handler.sendToKafka(keyObject, stateObj, RAILS_SESSION_ID, {});
      expect(screenshotUploadStub.calledOnce).to.be.true;
    });

    it('should should not take screenshots', () => {
      const ws = wsProxy();
      const keyObject = {
        s3bucket: 'test-bucket',
        rails_session_id: RAILS_SESSION_ID,
      };

      const stateObj = {
        debug: {
          screenshotCounter: 1,
        },
        id: 1,
        request: { method: 'Page.someMethod' },
        results: {},
      };

      ws[kSessionDebug] = true;

      handler.railsSessionMapping.set(ws.id, RAILS_SESSION_ID);
      handler.sendToKafka(keyObject, stateObj, RAILS_SESSION_ID, {});
      expect(addNormalResponseStub.calledOnce).to.be.true;
      expect(screenshotUploadStub.calledOnce).to.be.false;
    });

    it('should call write response id for debug screenshots', () => {
      const ws = wsProxy();
      ws[kSessionDebug] = true;
      const data = {
        id: 1,
        method: 'Page.navigate',
        results: {},
      };

      handler.railsSessionMapping.set(ws.id, RAILS_SESSION_ID);
      handler.writeResponse(ws, data);
      expect(addNormalResponseStub.calledOnce).to.be.false;
      expect(writeResponseStub.calledOnce).to.be.true;
    });
  });
});
