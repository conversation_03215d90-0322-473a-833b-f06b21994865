'use strict';

const sinon = require('sinon');
const rewire = require('rewire');
const rewiredHandler = rewire('../../../socketManagers/puppeteerHandler');
const { expect } = require('chai');
const { mockLoggerSpies: mockLogger } = require('./socketHelpers');
const { wsProxy } = require('./socketHelpers');

describe('Puppeteer truncating input text events in raw logs', () => {
  let puppeteerHandler;
  const PuppeteerHandler = rewiredHandler.__get__('PuppeteerHandler');
  const SocketMessageHandler = rewiredHandler.__get__('SocketMessageHandler');
  let originalAllocator;
  let originalBasicLogger;
  const RAILS_SESSION_ID = 'rails-1234';
  const ID = 'random-id';
  let sampleResponse;
  let sampleData;


  beforeEach(() => {
    puppeteerHandler = new PuppeteerHandler();
    originalAllocator = rewiredHandler.__get__('allocateTerminal');
    originalBasicLogger = rewiredHandler.__get__('logger');

    sinon.stub(SocketMessageHandler.prototype, 'addRequestLog');
    sinon.stub(SocketMessageHandler.prototype, 'addNormalResponse');
    sinon.stub(SocketMessageHandler.prototype, 'addDebugLog');

    rewiredHandler.__set__('logger', mockLogger);
    sinon.spy(puppeteerHandler, 'sendToKafka');
    sampleResponse = {
      id: 12,
      results: {}
    };

    sampleData = {
      id: 12,
      sessionId: '1234',
      method: 'Page.evaluate',
      params: {},
    };
  });

  afterEach(() => {
    rewiredHandler.__set__('allocateTerminal', originalAllocator);
    rewiredHandler.__set__('logger', originalBasicLogger);
    puppeteerHandler.sendToKafka.restore();
    SocketMessageHandler.prototype.addRequestLog.restore();
    SocketMessageHandler.prototype.addNormalResponse.restore();
    SocketMessageHandler.prototype.addDebugLog.restore();
  });

  describe('request logging', () => {
    it('should all request to state after sending the present input text input to kafka', () => {
      puppeteerHandler.keyLogEvents.set(RAILS_SESSION_ID, 'Sample');
      puppeteerHandler.railsSessionMapping.set(ID, RAILS_SESSION_ID);
      sinon.spy(puppeteerHandler, 'addEntryToRawLog');

      puppeteerHandler.addRequestLog({}, 'Page.evaluate', sampleData, ID);

      expect(puppeteerHandler.addEntryToRawLog.callCount).to.be.eql(2);
      expect(puppeteerHandler.keyLogEvents.size).to.be.eql(0);

      puppeteerHandler.addEntryToRawLog.restore();
    });

    it('should not write to kafka when stop request come', () => {
      puppeteerHandler.keyLogEvents.set(RAILS_SESSION_ID, 'Sample');
      puppeteerHandler.railsSessionMapping.set(ID, RAILS_SESSION_ID);
      sinon.spy(puppeteerHandler, 'addEntryToRawLog');

      puppeteerHandler.addRequestLog({}, 'Page.evaluate', sampleData, ID, { stop: true });

      expect(puppeteerHandler.addEntryToRawLog.callCount).to.be.eql(1);
      expect(puppeteerHandler.keyLogEvents.size).to.be.eql(0);

      puppeteerHandler.addEntryToRawLog.restore();
    });

    it('if no text input present should continue send to kafka', () => {
      puppeteerHandler.railsSessionMapping.set(ID, RAILS_SESSION_ID);
      sinon.spy(puppeteerHandler, 'addEntryToRawLog');

      puppeteerHandler.addRequestLog({}, 'Page.evaluate', sampleData, ID);

      expect(puppeteerHandler.addEntryToRawLog.callCount).to.be.eql(1);
      expect(puppeteerHandler.keyLogEvents.size).to.be.eql(0);

      puppeteerHandler.addEntryToRawLog.restore();
    });
  });

  describe('response handling', () => {
    it('should not continue sending response to kafka when input text event received', () => {
      puppeteerHandler.keyLogEvents.set(RAILS_SESSION_ID, 'Sample');
      puppeteerHandler.railsSessionMapping.set(ID, RAILS_SESSION_ID);

      puppeteerHandler.addNormalResponse(sampleResponse, {}, RAILS_SESSION_ID, {});

      expect(puppeteerHandler.sendToKafka.callCount).to.be.eql(0);
    });

    it('should not continue sending response to kafka when stop received in normal response', () => {
      puppeteerHandler.keyLogEvents.set(RAILS_SESSION_ID, 'Sample');
      puppeteerHandler.railsSessionMapping.set(ID, RAILS_SESSION_ID);

      puppeteerHandler.addNormalResponse(sampleResponse, {}, RAILS_SESSION_ID, {
        stop: true,
      });

      expect(puppeteerHandler.sendToKafka.callCount).to.be.eql(0);
    });

    it('should send to kafka when there is no text input request in state', () => {
      puppeteerHandler.railsSessionMapping.set(ID, RAILS_SESSION_ID);
      puppeteerHandler.rawLogState.set(RAILS_SESSION_ID, {
        [sampleResponse.id]: {
          ...sampleResponse,
          request: {
            method: 'Page.someMethod'
          }
        },
      });

      puppeteerHandler.addNormalResponse(sampleResponse, {}, RAILS_SESSION_ID);

      expect(puppeteerHandler.sendToKafka.callCount).to.be.eql(1);
      expect(puppeteerHandler.rawLogState.get(RAILS_SESSION_ID)[sampleResponse.id]).to.be.undefined;
    });

    it('if request for response not present in state, should not push to kafka', () => {
      puppeteerHandler.railsSessionMapping.set(ID, RAILS_SESSION_ID);
      puppeteerHandler.rawLogState.set(RAILS_SESSION_ID, {
        [sampleResponse.id + 1]: sampleResponse,
      });

      puppeteerHandler.addNormalResponse(sampleResponse, {}, RAILS_SESSION_ID);

      expect(puppeteerHandler.sendToKafka.callCount).to.be.eql(0);
    });

    it('if there is no id in response should not send to kafka', () => {
      puppeteerHandler.railsSessionMapping.set(ID, RAILS_SESSION_ID);
      const mutResponseData = JSON.parse(JSON.stringify(sampleResponse));
      delete mutResponseData.id;

      puppeteerHandler.addNormalResponse(mutResponseData, {}, RAILS_SESSION_ID);

      expect(puppeteerHandler.sendToKafka.callCount).to.be.eql(0);
    });
  });

  describe('socket message handler', () => {
    let ws;

    beforeEach(() => {
      ws = wsProxy();
      sinon.stub(puppeteerHandler, 'sendToTerminal', () => {});
      sampleData = {
        ...sampleData,
        method: 'Input.dispatchKeyEvent',
      };
    });

    afterEach(() => {
      puppeteerHandler.sendToTerminal.restore();
    });

    it('should add to keyLogEvent when received the input text method', () => {
      sampleData.sessionId = RAILS_SESSION_ID;
      sampleData.params = {
        key: 'Y',
        type: 'keyDown',
      };

      puppeteerHandler.inputKeyHandler(ws, sampleData);

      expect(puppeteerHandler.keyLogEvents.size).to.be.eql(1);
      let currText = puppeteerHandler.keyLogEvents.get(RAILS_SESSION_ID);
      expect(currText).to.be.eql('Y');

      sampleData.params = {
        key: 'a',
        type: 'keyDown',
      };

      puppeteerHandler.inputKeyHandler(ws, sampleData);

      expect(puppeteerHandler.keyLogEvents.size).to.be.eql(1);
      currText = puppeteerHandler.keyLogEvents.get(RAILS_SESSION_ID);
      expect(currText).to.be.eql('Ya');

      sampleData.params = {
        key: 's',
        type: 'keyUp',
      };

      puppeteerHandler.inputKeyHandler(ws, sampleData);

      expect(puppeteerHandler.keyLogEvents.size).to.be.eql(1);
      currText = puppeteerHandler.keyLogEvents.get(RAILS_SESSION_ID);
      expect(currText).to.be.eql('Ya');
    });
  });

  describe('should clear the state in keyEvents in closing', () => {
    it('should clear the already present for text logs when closing', () => {
      puppeteerHandler.keyLogEvents.set(RAILS_SESSION_ID, 'random');
      puppeteerHandler.rawLogState.set(RAILS_SESSION_ID, {
        0: {},
      });
      puppeteerHandler.railsSessionMapping.set('1234', RAILS_SESSION_ID);

      puppeteerHandler.clearState('1234');

      expect(puppeteerHandler.railsSessionMapping.size).to.be.eql(0);
      expect(puppeteerHandler.keyLogEvents.size).to.be.eql(0);
      expect(puppeteerHandler.rawLogState.size).to.be.eql(0);
    });
  });
});
