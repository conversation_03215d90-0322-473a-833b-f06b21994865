'use strict';

/* eslint-disable no-underscore-dangle */

const { expect } = require('chai');
const sinon = require('sinon');
const rewire = require('rewire');
const { wsProxy, mockLoggerSpies } = require('./socketHelpers');
const { kStartTimer } = require('../../../config/socketConstants');

const handler = rewire('../../../socketManagers/playwrightHandler');

describe('modifyResponseToClient', () => {

  const PLAYWRIGHT_HANDLER = handler.__get__('PlaywrightHandler');
  let obj;
  let socketObj;
  const mockAllocateTerminal = {};
  const mockPingZombie = {};
  const mockNestedKeyValue = {};
  const mockDesktopSocket = {};
  const mockLogger = {};
  const mockHA = {};
  const startSpy = sinon.spy();

  beforeEach(() => {
    obj = new PLAYWRIGHT_HANDLER();
    socketObj = wsProxy();
    mockAllocateTerminal.original = handler.__get__('allocateTerminal');
    mockPingZombie.original = handler.__get__('PingZombie');
    mockNestedKeyValue.original = handler.__get__('nestedKeyValue');
    mockAllocateTerminal.stub = sinon.stub();
    mockPingZombie.stub = sinon.stub();
    mockNestedKeyValue.stub = sinon.stub();
    mockDesktopSocket.original = obj.connectToDesktopSocket;
    mockDesktopSocket.stub = sinon.stub();
    mockHA.original = handler.__get__('HA');
    mockHA.stub = sinon.stub(mockHA.original, 'getData');
    mockLogger.original = handler.__get__('logger');
    Object.keys(mockLoggerSpies).forEach((el) => {
      mockLoggerSpies[el] = sinon.spy();
    });
    mockLogger.stub = mockLoggerSpies;
    obj.on(kStartTimer, startSpy);
    handler.__set__('logger', mockLogger.stub);
    obj.connectToDesktopSocket = mockDesktopSocket.stub;
    handler.__set__('allocateTerminal', mockAllocateTerminal.stub);
    handler.__set__('PingZombie', mockPingZombie.stub);
    handler.__set__('nestedKeyValue', mockNestedKeyValue.stub);
    handler.__set__('globalRegistry', {});
  });

  afterEach(() => {
    handler.__set__('allocateTerminal', mockAllocateTerminal.original);
    handler.__set__('PingZombie', mockPingZombie.original);
    handler.__set__('nestedKeyValue', mockNestedKeyValue.original);
    obj.connectToDesktopSocket = mockDesktopSocket.original;
    handler.__set__('logger', mockLogger.original);
    mockHA.stub.restore();
    startSpy.reset();
  });

  it('modify data obj on diff client and server playwright versions for FetchRequest', () => {
    let data = {
      params: {
        type: 'FetchRequest'
      }
    }
    let expectedData = {
      params: {
        type: 'FetchRequest',
        initializer: {},
        guid: "fetchRequest@4676bbaaf8e2eca0f8d658b1a6105e68"
      }
    }
    mockNestedKeyValue.stub.returns("BrowserContext");
    PLAYWRIGHT_HANDLER.modifyResponseToClient(data, '1.24.0', '1.25.1');
    expect(data).to.be.eql(expectedData);
  });

  it('modify data obj on diff client and server playwright versions for BrowserContext', () => {
    let data = {
      params: {
        guid: "fetchRequest@4676bbaaf8e2eca0f8d658b1a6105e68",
        type: 'BrowserContext',
        initializer: {
          fetchRequest: {},
          APIRequestContext: {}
        }
      },
    }
    let expectedData = {
      params: {
        type: 'BrowserContext',
        initializer: {
          fetchRequest: { guid: "fetchRequest@4676bbaaf8e2eca0f8d658b1a6105e68" },
          APIRequestContext: { guid: "fetchRequest@4676bbaaf8e2eca0f8d658b1a6105e68" }
        },
        guid: "fetchRequest@4676bbaaf8e2eca0f8d658b1a6105e68"
      }
    }
    mockNestedKeyValue.stub.returns("BrowserContext");
    PLAYWRIGHT_HANDLER.modifyResponseToClient(data, '1.24.0', '1.25.1');
    expect(data).to.be.eql(expectedData);
  });

  describe('platform details check', () => {
    const platformDetails = {
      os: 'some os',
      os_version: 'some_version'
    };
    const sessionId = 'some session id';
    const browserName = "someBrowser"

    it('Should add platform details and session id in browser create', () => {
      const data = {
        method: '__create__',
        params: {
          type: 'Browser'
        }
      };
      const expectedParams = {
        type: 'Browser',
        bsParams: {
          sessionId,
          platformDetails: { ...platformDetails },
          browserName
        }
      };
      mockNestedKeyValue.stub.returns('Browser');
      PLAYWRIGHT_HANDLER.modifyResponseToClient(data, '1.24.0', '1.25.1', { railsSessionId: sessionId, platformDetails, browserName });
      expect(data.params).to.deep.equal(expectedParams);
    });

    it('Should add platform details and session id in AndroidDevice create', () => {
      const data = {
        method: '__create__',
        params: {
          type: 'AndroidDevice'
        }
      };
      const expectedParams = {
        type: 'AndroidDevice',
        bsParams: {
          sessionId,
          platformDetails: { ...platformDetails },
          browserName
        }
      };
      mockNestedKeyValue.stub.returns('AndroidDevice');
      PLAYWRIGHT_HANDLER.modifyResponseToClient(data, '1.24.0', '1.25.1', { railsSessionId: sessionId, platformDetails, browserName });
      expect(data.params).to.deep.equal(expectedParams);
    });
  
    it('Should add platform details and session id in browser create for connectOverCDP flow', () => {
      const data = {
        "id": 1,
        "result": {
          "userAgent":"some User Agent"
        }
      };
      const expectedData = {
        ...data,
        bStackParams : {
          sessionId,
          platformDetails: {
            ...platformDetails
          },
          browserName
        }
      };
      mockNestedKeyValue.stub.onCall(1).returns('some User Agent');
      PLAYWRIGHT_HANDLER.modifyResponseToClient(data, '1.24.0', '1.25.1', { railsSessionId: sessionId, platformDetails, browserName });
      mockNestedKeyValue.stub.onCall(2).returns(undefined);
      expect(data).to.deep.equal(expectedData);
    });
  
    it('No data Should be added when checks are not passing', () => {
      const data = {key: 'some value'}
      const expectedData = {key: 'some value'}
  
      PLAYWRIGHT_HANDLER.modifyResponseToClient(data, '1.24.0', '1.25.1', { railsSessionId: sessionId, platformDetails, browserName });
      expect(data).to.deep.equal(expectedData);
    });
  
  });
});

describe('Playwright preProcessAllocation', () => {
  const PLAYWRIGHT_HANDLER = handler.__get__('PlaywrightHandler');
  let obj;
  let socketObj;
  const mockAllocateTerminal = {};
  const mockPingZombie = {};
  const mockDesktopSocket = {};
  const mockLogger = {};
  const mockHA = {};
  const startSpy = sinon.spy();

  beforeEach(() => {
    obj = new PLAYWRIGHT_HANDLER();
    socketObj = wsProxy();
    mockAllocateTerminal.original = handler.__get__('allocateTerminal');
    mockPingZombie.original = handler.__get__('PingZombie');
    mockAllocateTerminal.stub = sinon.stub();
    mockPingZombie.stub = sinon.stub();
    mockDesktopSocket.original = obj.connectToDesktopSocket;
    mockDesktopSocket.stub = sinon.stub();
    mockHA.original = handler.__get__('HA');
    mockHA.stub = sinon.stub(mockHA.original, 'getData');
    mockLogger.original = handler.__get__('logger');
    mockLogger.original =
    Object.keys(mockLoggerSpies).forEach((el) => {
      mockLoggerSpies[el] = sinon.spy();
    });
    mockLogger.stub = mockLoggerSpies;
    obj.on(kStartTimer, startSpy);
    handler.__set__('logger', mockLogger.stub);
    obj.connectToDesktopSocket = mockDesktopSocket.stub;
    handler.__set__('allocateTerminal', mockAllocateTerminal.stub);
    handler.__set__('PingZombie', mockPingZombie.stub);
    handler.__set__('globalRegistry', {});
  });

  afterEach(() => {
    handler.__set__('allocateTerminal', mockAllocateTerminal.original);
    handler.__set__('PingZombie', mockPingZombie.original);
    obj.connectToDesktopSocket = mockDesktopSocket.original;
    handler.__set__('logger', mockLogger.original);
    mockHA.stub.restore();
    startSpy.reset();
  });

  it('If no websokcet url present should close the socket conenction', async () => {
    mockAllocateTerminal.stub.returns(Promise.resolve({
      status: 0,
      sessionId: '12345',
      value: {}
    }));

    mockPingZombie.stub.returns(true);

    await obj.preProcessAllocation(socketObj);
    expect(socketObj.close.calledOnce).to.be.true;
    const [code, message] = socketObj.close.args[0];
    expect(code).to.be.eql(1001);
    expect(message).to.be.eql('Error: Unable to get the websocket connection');
  });

  it('If status 13 is sent from upstream then the message should be pushed downstream', async () => {
    mockAllocateTerminal.stub.returns(Promise.resolve({
      status: 13,
      sessionId: '12345',
      value: { message: 'There is an error communicating to playwright' }
    }));

    mockPingZombie.stub.returns(true);

    await obj.preProcessAllocation(socketObj);
    expect(socketObj.close.calledOnce).to.be.true;
    const [code, message] = socketObj.close.args[0];
    expect(code).to.be.eql(1001);
    expect(message).to.be.eql('Error: There is an error communicating to playwright');
  });

  it('If undefiend status code is sent from upstream then the message should be pushed downstream', async () => {
    mockAllocateTerminal.stub.returns(Promise.resolve({
      status: 999,
      sessionId: '12345',
      value: { message: 'There is an error communicating to playwright' }
    }));

    mockPingZombie.stub.returns(true);

    await obj.preProcessAllocation(socketObj);
    expect(socketObj.close.calledOnce).to.be.true;
    const [code, message] = socketObj.close.args[0];
    expect(code).to.be.eql(1001);
    expect(message).to.be.eql('Error: There is an error communicating to playwright');
  });

  it('If allocate terminal fails then closes the socket conenction', async () => {
    mockAllocateTerminal.stub.throws('Something is not a function');
    await obj.preProcessAllocation(socketObj);
    expect(socketObj.close.calledOnce).to.be.true;
    const [code, message] = socketObj.close.args[0];
    expect(code).to.be.eql(1001);
    expect(message).to.be.eql('Something is not a function');
  });

  it('Event emitted when the allocation is successfull to start the timer', async () => {
    mockAllocateTerminal.stub.returns(Promise.resolve({
      status: 0,
      sessionId: '12345',
      value: {
        wsURL: 'ws://127.0.0.1:9222/something-random',
        wsHostname: '***********'
      }
    }));
    mockPingZombie.stub.returns(true);
    mockDesktopSocket.stub.returns(Promise.resolve());
    mockHA.stub.yields(null, { id: '12345' });

    await obj.preProcessAllocation(socketObj);
    expect(socketObj.close.calledOnce).to.be.false;
    expect(startSpy.calledOnce).to.be.true;
  });

  it('Should close the socket if unable to refresh the keyObject', async () => {
    mockAllocateTerminal.stub.returns(Promise.resolve({
      status: 0,
      sessionId: '12345',
      value: {
        wsURL: 'ws://127.0.0.1:9222/something-random',
        wsHostname: '***********'
      }
    }));
    mockPingZombie.stub.returns(true);
    mockDesktopSocket.stub.returns(Promise.resolve());
    mockHA.stub.yields(new Error('Unable to setup connection to redis'));

    await obj.preProcessAllocation(socketObj);
    expect(socketObj.close.calledOnce).to.be.true;
    const [code, message] = socketObj.close.args[0];
    expect(code).to.be.eql(1001);
    expect(message).to.be.eql('Error: sessionId state not found');
    expect(startSpy.calledOnce).to.be.false;
  });
  it('Truncate the close message if it is greater than 123 bytes to avoid range error', async () => {
    let errorMsg = 'This is a test message to test 123 bytes range error handling on websocket close';
    let lengthyMsg = errorMsg + errorMsg;
    mockAllocateTerminal.stub.throws(lengthyMsg);
    await obj.preProcessAllocation(socketObj);
    expect(socketObj.close.calledOnce).to.be.true;
    const [code, message] = socketObj.close.args[0];
    expect(code).to.be.eql(1001);
    expect(message).to.be.eql(lengthyMsg.substring(0, 122));
  });
});
