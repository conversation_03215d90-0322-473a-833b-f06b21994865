'use strict';

/* eslint-disable no-underscore-dangle */

const { expect } = require('chai');
const sinon = require('sinon');
const rewire = require('rewire');
const { wsProxy } = require('./socketHelpers');
const { kStartTimer } = require('../../../config/socketConstants');

const handler = rewire('../../../socketManagers/playwrightHandler');

describe('Playwright requestLog', () => {
  const PLAYWRIGHT_HANDLER = handler.__get__('PlaywrightHandler');
  let obj;
  let socketObj;
  const startSpy = sinon.spy();
  const RAILS_SESSION_ID = '1234-rails';
  const method = 'update';
  let data;
  // This keyObject is not useful in case of playwright since
  // there is no useful information present in it necessary for
  // logging.
  const keyObject = {};

  beforeEach(() => {
    obj = new PLAYWRIGHT_HANDLER();
    socketObj = wsProxy();
    data = {
      id: 123,
      params: {
        key: 'value'
      }
    };
    obj.on(kStartTimer, startSpy);
    obj.railsSessionMapping.set(socketObj.id, RAILS_SESSION_ID);
    handler.__set__('globalRegistry', {});
  });

  afterEach(() => {
    startSpy.reset();
    obj.railsSessionMapping.clear();
    obj.rawLogState.clear();
  });

  it('It should not set entry in log if stop is sent', () => {
    keyObject.isPlaywright = true;
    obj.addRequestLog(keyObject, method, data, socketObj.id, { stop: true });
    expect(obj.rawLogState.size).to.be.eql(0);
  });

  it('It should allow data with params key only in case of requests', () => {
    obj.addRequestLog(keyObject, method, data, socketObj.id);
    expect(obj.rawLogState.size).to.be.eql(1);
  });

  it('Should not allow setting data with params even as undefined', () => {
    data.params = undefined;
    obj.addRequestLog(keyObject, method, data, socketObj.id);
    expect(obj.rawLogState.size).to.be.eql(0);
  });
});
