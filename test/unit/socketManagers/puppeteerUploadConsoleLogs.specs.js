
/* eslint-disable no-underscore-dangle */

const { expect } = require('chai');
const sinon = require('sinon');
const rewire = require('rewire');
const helper = require('../helper.js');
const assert = require('assert');
const { CONSOLE_LOG_LEVELS } = require('../../../constants');
const handler = rewire('../../../socketManagers/puppeteerHandler');
const origHelper = require('../../../helper.js');

describe('uploadConsoleLog', () => {
  const PUPPETEER_HANDLER = handler.__get__('PuppeteerHandler');
  let keyObject, data;

  beforeEach(() => {
    keyObject = helper.getKeyObject();
    data = {
      method: "Log.entryAdded",
      params: {
        entry: {
          text: "eventText"
        }
      }
    };
    uploadCDPConsoleLog = sinon.stub(origHelper, 'uploadCDPConsoleLog');
  });

  afterEach(() => {
    uploadCDPConsoleLog.restore();
  });

  it('should call helper.uploadCDPConsoleLog with correct parameters', () => {
    PUPPETEER_HANDLER.uploadConsoleLog(keyObject, data, CONSOLE_LOG_LEVELS.get('verbose'));
    assert(uploadCDPConsoleLog.called === true);
    assert(uploadCDPConsoleLog.calledWith(keyObject, data, CONSOLE_LOG_LEVELS.get('verbose')) === true);
  });
});
