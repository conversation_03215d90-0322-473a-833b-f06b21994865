'use strict';

/* eslint-disable no-underscore-dangle */

const { expect } = require('chai');
const sinon = require('sinon');
const rewire = require('rewire');
const { mockLoggerSpies } = require('./socketHelpers');
const { kStartTimer } = require('../../../config/socketConstants');

const handler = rewire('../../../socketManagers/playwrightHandler');

describe('Playwright client close handler', () => {
  const PLAYWRIGHT_HANDLER = handler.__get__('PlaywrightHandler');
  const BASE_SOCKET = handler.__get__('SocketMessageHandler');
  let obj;
  const startSpy = sinon.spy();
  const mockSuperClearState = {};
  const mockLogger = {};
  const SESSION_ID = 'session-1234';
  const RAILS_SESSION_ID = 'rails-1234';
  const mockRawLogStateDel = {};
  const mockKeyLogEventDel = {};
  const mockRailsGet = {};

  beforeEach(() => {
    obj = new PLAYWRIGHT_HANDLER();
    obj.on(kStartTimer, startSpy);
    handler.__set__('globalRegistry', {});
    mockLogger.original = handler.__get__('logger');
    Object.values(mockLoggerSpies).forEach(el => el.reset());
    mockLogger.stub = mockLoggerSpies;
    mockRawLogStateDel.stub = sinon.stub(obj.rawLogState, 'delete');
    mockKeyLogEventDel.stub = sinon.stub(obj.keyLogEvents, 'delete');
    mockSuperClearState.stub = sinon.stub(BASE_SOCKET.prototype, 'clearState');
    mockRailsGet.stub = sinon.stub(obj.railsSessionMapping, 'get');
    handler.__set__('logger', mockLogger.stub);
  });

  afterEach(() => {
    startSpy.reset();
    handler.__set__('logger', mockLogger.original);
    mockSuperClearState.stub.restore();
    mockRawLogStateDel.stub.restore();
    mockKeyLogEventDel.stub.restore();
    mockRailsGet.stub.restore();
  });

  it('Should clear the rawLogState and keyLogEvents', () => {
    mockRailsGet.stub.returns(RAILS_SESSION_ID);
    obj.clearState(SESSION_ID);
    expect(mockSuperClearState.stub.calledOnce).to.be.true;
    expect(mockRawLogStateDel.stub.calledOnce).to.be.true;
    expect(mockKeyLogEventDel.stub.calledOnce).to.be.true;
  });
});
