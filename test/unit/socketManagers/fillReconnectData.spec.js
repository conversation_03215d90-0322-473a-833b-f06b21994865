'use strict';

const sinon = require('sinon');
const { expect } = require('chai');
const rewire = require('rewire');
const { wsProxy } = require('./socketHelpers');

const handler = rewire('../../../socketManagers/baseSocketMessageHandler.js');

describe('Fill reconnect data', () => {
  const SocketMessageHandler = handler.__get__('SocketMessageHandler');
  const HA = handler.__get__('HA');

  let obj;
  let haStub;
  let desktopconn;
  let ws;
  let reconnectData;

  beforeEach(() => {
    sinon.stub(SocketMessageHandler.prototype, 'assignLogBuilder');
    sinon.stub(SocketMessageHandler.prototype, 'enableMethodMaps');
    sinon.stub(SocketMessageHandler.prototype, 'debugScreenshotMethodMaps');
    ws = wsProxy();
    obj = new SocketMessageHandler();
    desktopconn = sinon.stub(obj, 'connectToDesktopSocket');
    haStub = sinon.stub(HA, 'getData');
    reconnectData = {
      remoteConnection: 'ws://some-url:port',
      browserSessionId: '1234',
      hostname: '127.0.0.1',
      rproxyHost: 'private-proxy.com'
    };
  });

  afterEach(() => {
    SocketMessageHandler.prototype.assignLogBuilder.restore();
    SocketMessageHandler.prototype.enableMethodMaps.restore();
    SocketMessageHandler.prototype.debugScreenshotMethodMaps.restore();
    haStub.restore();
  });

  it('If error in fetching the state then close promise', async () => {
    haStub.yields(new Error('Unable to connect to redis'), null);
    try {
      await obj.fillReconnectData(ws, ws.id, {});
      expect(false).to.be.true;
    } catch (err) {
      expect(err.message).to.eql('Unable to connect to redis');
    }
  });

  it('If data is fetched and successfully connected then resolve', async () => {
    haStub.yields(null, {
      name: '127.0.0.1',
      rproxyHost: 'private-proxy.com'
    });
    desktopconn.returns(Promise.resolve());
    try {
      await obj.fillReconnectData(ws, ws.id, reconnectData);
    } catch {
      expect(false).to.be.true;
    }
  });

  it('If unable to connect to desktop, reject the promise', async () => {
    haStub.yields(null, {
      name: '127.0.0.1',
      rproxyHost: 'private-proxy.com'
    });
    desktopconn.returns(Promise.reject(new Error('Unable to connect to terminal')));
    try {
      await obj.fillReconnectData(ws, ws.id, reconnectData);
      expect(false).to.be.true;
    } catch(err) {
      expect(err.message).to.eql('Unable to connect to terminal');
    }
  });
});
