'use strict';

const sinon = require('sinon');
const { expect } = require('chai');
const { STOP_SOCKET_DELAY, kReceivedLockAck } = require('../../../config/socketConstants');
const StopSocket = require('../../../socketManagers/stopSocket');
const EE = require('events');

describe('Stop socket flow', () => {
  let socket;
  let clock;

  beforeEach(() => {
    socket = new EE();
    socket.send = sinon.stub();
    socket.close = sinon.stub();
    clock = sinon.useFakeTimers();
  });

  afterEach(() => {
    clock.restore();
  });

  it('should close the socket in locked received', () => {
    StopSocket.addSocket(socket);
    socket.emit(kReceivedLockAck);
    clock.tick(STOP_SOCKET_DELAY);
    expect(socket.close.calledOnce).to.be.true;
    const [ code, message ] = socket.close.args[0];
    expect(code).to.be.eql(1001);
    expect(message).to.be.eql('Service Restart');
    expect(socket.send.calledOnce).to.be.true;
  });

  it('should not close the socket before time if lock received', () => {
    StopSocket.addSocket(socket);
    socket.emit(kReceivedLockAck);
    clock.tick(STOP_SOCKET_DELAY - 5000);
    expect(socket.close.calledOnce).to.be.false;
    expect(socket.send.calledOnce).to.be.true;
  });

  it('should not close the socket is locked not received', () => {
    StopSocket.addSocket(socket);
    clock.tick(STOP_SOCKET_DELAY);
    expect(socket.close.calledOnce).to.be.false;
    expect(socket.send.calledOnce).to.be.true;
  });
});
