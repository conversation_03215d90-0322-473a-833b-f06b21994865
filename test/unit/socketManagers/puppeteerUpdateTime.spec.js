'use strict';

const sinon = require('sinon');
const rewire = require('rewire');
const SpecHelper = require('../helper');
const { mockLoggerSpies: mockLogger } = require('./socketHelpers');

const rewireHandler = rewire('../../../socketManagers/baseSocketMessageHandler');
const { wsProxy } = require('./socketHelpers');
const { expect } = require('chai');

describe('Puppeteer Update time in keyObject', () => {
  const BaseHandler = rewireHandler.__get__('SocketMessageHandler');
  const RAILS_SESSION_ID = 'rails-1234';

  let handler;
  let registry;
  let originalBasicLogger;
  let HA;
  let ws;

  beforeEach(() => {
    sinon.stub(BaseHandler.prototype, 'enableMethodMaps', () => {});
    sinon.stub(BaseHandler.prototype, 'assignLogBuilder', () => {});
    sinon.stub(BaseHandler.prototype, 'debugScreenshotMethodMaps', () => {});
    handler = new BaseHandler();
    registry = rewireHandler.__get__('globalRegistry');
    originalBasicLogger = rewireHandler.__get__('logger');
    rewireHandler.__set__('logger', mockLogger);
    HA = rewireHandler.__get__('HA');
    ws = wsProxy();
    handler.railsSessionMapping.set(ws.id, RAILS_SESSION_ID);
    registry[RAILS_SESSION_ID] = SpecHelper.getKeyObject();
    sinon.stub(HA, 'setData');
  });

  afterEach(() => {
    BaseHandler.prototype.enableMethodMaps.restore();
    BaseHandler.prototype.assignLogBuilder.restore();
    BaseHandler.prototype.debugScreenshotMethodMaps.restore();
    rewireHandler.__set__('logger', originalBasicLogger);
    HA.setData.restore();
  });

  describe('successfully set data to redis', () => {
    beforeEach(() => {
      HA.setData.yields(null);
    });

    it('should set the data in redis if railsMapping exists', () => {
      handler.updateTime(ws);
      expect(HA.setData.calledOnce).to.be.true;
    });

    it('should not set data in redis if railsMapping does not exists', () => {
      handler.railsSessionMapping.clear();
      handler.updateTime(ws);
      expect(HA.setData.calledOnce).to.be.false;
    });
  });

  describe('Error in setting redis', () => {
    beforeEach(() => {
      HA.setData.yields(new Error('Unable to set Data'));
      mockLogger.error = sinon.spy();
    });

    it('should not set data since redis operation failed', () => {
      handler.updateTime(ws);
      expect(HA.setData.calledOnce).to.be.true;
      expect(mockLogger.error.calledOnce).to.be.true;
    });
  });
});
