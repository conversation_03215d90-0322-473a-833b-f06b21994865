const serializer = require('../../../../socketManagers/logSerializer/serializer');
const sinon = require('sinon');


const assert = require('assert');

describe('debugScreenshotString', function() {
  it('should return a formatted debug string', function() {
    const keyObject = 'testKeyObject';
    const screenshotCounter = 1;
    const now = new Date();
    var clock = sinon.useFakeTimers(now.getTime());
    const dd = ((1900 + now.getYear()) + "-" + (now.getMonth() + 1) + "-" + now.getDate() + " " + now.getHours() + ":" + now.getMinutes() + ":" + now.getSeconds() + ":" + now.getMilliseconds())
    const expectedString = `${dd} DEBUG https://automate.browserstack.com/s3-debug/undefined/undefined/screenshot-1.jpeg`;

    const result = serializer.debugScreenshotString(null, { keyObject, screenshotCounter });

    assert.strictEqual(result, expectedString);
    clock.restore();
  });
});
