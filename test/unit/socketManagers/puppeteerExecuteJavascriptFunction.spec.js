'use strict';

const sinon = require('sinon');
const rewire = require('rewire');

const rewiredHandler = rewire('../../../socketManagers/puppeteerHandler');
const { expect } = require('chai');
const { mockLoggerSpies: mockLogger } = require('./socketHelpers');
const { wsProxy } = require('./socketHelpers');

describe('Puppeteer trimming the javascript function executed', () => {
  let puppeteerHandler;
  const PuppeteerHandler = rewiredHandler.__get__('PuppeteerHandler');
  let originalAllocator;
  let originalBasicLogger;
  const RAILS_SESSION_ID = 'rails-1234';
  const ID = 'random-id';
  let sampleResponse;
  let sampleData;


  beforeEach(() => {
    puppeteerHandler = new PuppeteerHandler();
    originalAllocator = rewiredHandler.__get__('allocateTerminal');
    originalBasicLogger = rewiredHandler.__get__('logger');

    rewiredHandler.__set__('logger', mockLogger);
    sinon.stub(puppeteerHandler, 'sendToKafka', () => {});
    sampleResponse = {
      id: 12,
      results: {},
    };

    sampleData = {
      id: 12,
      method: 'Page.evaluate',
      params: {},
    };
  });

  afterEach(() => {
    rewiredHandler.__set__('allocateTerminal', originalAllocator);
    rewiredHandler.__set__('logger', originalBasicLogger);
    puppeteerHandler.sendToKafka.restore();
  });

  describe('socket message handler', () => {
    let ws;

    beforeEach(() => {
      ws = wsProxy();
      sinon.stub(puppeteerHandler, 'sendToTerminal', () => {});
      sampleData = {
        ...sampleData,
        method: 'Runtime.callFunctionOn',
      };
      sinon.spy(puppeteerHandler, 'sendToRequestLog');
    });

    afterEach(() => {
      puppeteerHandler.sendToTerminal.restore();
      puppeteerHandler.sendToRequestLog.restore();
    });

    it('should truncate the functionDeclaration string to readable format and change method name to Page.customFocus', () => {
      sampleData.sessionId = RAILS_SESSION_ID;
      sampleData.params = {
        functionDeclaration: '(element) => element.focus()\n//# sourceURL=__puppeteer_evaluation_script__\n',
        executionContextId: 3,
        arguments: [{ objectId: '{"injectedScriptId":3,"id":2}' }],
        returnByValue: true,
        awaitPromise: true,
        userGesture: true,
      };

      puppeteerHandler.executeJavascriptFunction(ws, sampleData);
      expect(puppeteerHandler.sendToRequestLog.callCount).to.be.eql(1);
      const [{ args }] = puppeteerHandler.sendToRequestLog.getCalls();
      const expected = {
        id: 12,
        sessionId: RAILS_SESSION_ID,
        method: 'Page.customFocus',
        params: {
          value: '(element) => element.focus()',
          args: [{ objectId: '{"injectedScriptId":3,"id":2}' }]
        }
      };
      expect(args[2]).to.deep.equal(expected);
    });

    it('should truncate the functionDeclaration string to readable format and change method name to Page.customQuerySelector', () => {
      sampleData.sessionId = RAILS_SESSION_ID;
      sampleData.params = {
        functionDeclaration: '(element, selector) => element.querySelector(selector)\n//# sourceURL=__puppeteer_evaluation_script__\n',
        executionContextId: 3,
        arguments: [{ objectId: '{"injectedScriptId":3,"id":2}' }],
        returnByValue: true,
        awaitPromise: true,
        userGesture: true,
      };

      puppeteerHandler.executeJavascriptFunction(ws, sampleData);
      expect(puppeteerHandler.sendToRequestLog.callCount).to.be.eql(1);
      const [{ args }] = puppeteerHandler.sendToRequestLog.getCalls();
      const expected = {
        id: 12,
        sessionId: RAILS_SESSION_ID,
        method: 'Page.customQuerySelector',
        params: {
          value: '(element, selector) => element.querySelector(selector)',
          args: [{ objectId: '{"injectedScriptId":3,"id":2}' }]
        },
      };
      expect(args[2]).to.deep.equal(expected);
    });
  });
});
