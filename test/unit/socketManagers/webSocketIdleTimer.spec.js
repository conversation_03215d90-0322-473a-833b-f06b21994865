'use strict';

const rewire = require('rewire');

const { kStartTimer, kUpdateTime, PUPPETEER } = require('../../../config/socketConstants');
const sinon = require('sinon');
const { expect } = require('chai');
const { wsProxy } = require('./socketHelpers');

describe('start idle timer on websocket', () => {
  let clock;
  let wsHandler;
  let ws;
  let puppeteerHandler;
  let WebSocketHandler;
  let rewiredHandler;

  beforeEach(() => {
    clock = sinon.useFakeTimers();
    rewiredHandler = rewire('../../../webSocketHandler');
    puppeteerHandler = rewiredHandler.__get__('puppeteerHandler');
    WebSocketHandler = rewiredHandler.__get__('WebSocketHandler');
    wsHandler = new WebSocketHandler({ noServer: true });
    ws = wsProxy();
  });

  afterEach(() => {
    clock.restore();
  });

  it('if event emitted on puppeteer handler then start the setInterval', () => {
    puppeteerHandler.emit(kStartTimer, ws);

    expect(wsHandler.idleSockets.size).to.be.eql(1);
  });

  describe('Update keyObject at regular intervals if not IDLE', () => {
    const RAILS_SESSIOND_ID = 'abc-def-ghi';
    beforeEach(() => {
      ws.product = PUPPETEER;
      puppeteerHandler.railsSessionMapping.set(ws.id, RAILS_SESSIOND_ID);
      ws[kUpdateTime] = new Date() + 30000;
      puppeteerHandler.emit(kStartTimer, ws);
      sinon.stub(puppeteerHandler, 'updateTime', () => {});
    });

    afterEach(() => {
      puppeteerHandler.railsSessionMapping.clear();
      puppeteerHandler.updateTime.restore();
    });

    it('should call the updateTime function after 90 seconds', () => {
      clock.tick(90000);
      expect(puppeteerHandler.updateTime.calledOnce).to.be.true;
    });

    it('should not updateTime function before 90 seconds', () => {
      clock.tick(40000);
      expect(puppeteerHandler.updateTime.calledOnce).to.be.false;
    });

    it('should not updateTime if it is idle from long time', () => {
      clock.tick(180000);
      expect(puppeteerHandler.updateTime.calledOnce).to.be.false;
    });
  });
});
