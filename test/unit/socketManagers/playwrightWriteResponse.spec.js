'use strict';

/* eslint-disable no-underscore-dangle */

const { expect, assert } = require('chai');
const sinon = require('sinon');
const rewire = require('rewire');
const { wsProxy } = require('./socketHelpers');
const { kStartTimer, kSessionDebug, kReqData } = require('../../../config/socketConstants');
const PlaywrightHandler = require('../../../socketManagers/playwrightHandler');
const { deviceDescriptors } = require('../../../config/deviceDescriptors');

const handler = rewire('../../../socketManagers/playwrightHandler');

describe('Playwright write response', () => {
  const PLAYWRIGHT_HANDLER = handler.__get__('PlaywrightHandler');
  let obj;
  let socketObj;
  let data;
  const startSpy = sinon.spy();
  const mockRegistryObject = {};

  beforeEach(() => {
    obj = new PLAYWRIGHT_HANDLER();
    socketObj = wsProxy();
    obj.on(kStartTimer, startSpy);
    mockRegistryObject.stub = sinon.stub(obj, 'getRegistryObject');
    mockRegistryObject.stub.returns({
      railsSesionId: 'rails-1234',
      playwrightVersion: '1.12.1',
    });
    data = { id: '10', result: { key: 'value' } };
  });

  afterEach(() => {
    startSpy.reset();
    obj.railsSessionMapping.clear();
    mockRegistryObject.stub.restore();
  });

  it('should set P2CprocessingTime to keyObject if options.respMetaData.startTime is defined', () => {
    const opts = {
      respMetaData: {
        startTime: process.hrtime.bigint(),
      },
    };
    const keyObject = {
      railsSesionId: 'rails-1234',
      playwrightVersion: '1.12.1',
    };

    socketObj[kReqData] = {};
    socketObj[kReqData].data = { 'client.playwrightVersion': '1.12.1' };
    mockRegistryObject.stub.returns(keyObject);
    obj.writeResponse(socketObj, data, opts);
    expect(keyObject.P2CprocessingTime).to.be.greaterThan(0);
  });

  it('should add to P2CprocessingTime of keyObject if options.respMetaData.startTime is defined', () => {
    const opts = {
      respMetaData: {
        startTime: process.hrtime.bigint(),
      },
    };
    const keyObject = {
      railsSesionId: 'rails-1234',
      playwrightVersion: '1.12.1',
      P2CprocessingTime: 1
    };

    socketObj[kReqData] = {};
    socketObj[kReqData].data = { 'client.playwrightVersion': '1.12.1' };
    mockRegistryObject.stub.returns(keyObject);
    obj.writeResponse(socketObj, data, opts);
    expect(keyObject.P2CprocessingTime).to.be.greaterThan(0);
  });

  it('should not capture the screenshot if not defined in websocket', () => {
    const opts = {};
    obj.writeResponse(socketObj, data, opts);
    expect(opts.debug).to.be.undefined;
  });

  it('should not capture screenshot if it does not have id in payload', () => {
    const opts = {};
    socketObj[kSessionDebug] = true;
    delete data.id;
    obj.writeResponse(socketObj, data, opts);
    expect(opts.debug).to.be.undefined;
  });

  it('If mapping for the session does not exists do not take debug screenshot', () => {
    socketObj[kSessionDebug] = true;
    data.id = '12345';
    const opts = {};
    obj.writeResponse(socketObj, data, opts);
    expect(opts.debug).to.be.undefined;
  });

  it('If mapping is present and id is present them set the debug state in options', () => {
    socketObj[kSessionDebug] = true;
    const opts = {};
    obj.railsSessionMapping.set(socketObj.id, true);
    obj.writeResponse(socketObj, data, opts);
    expect(opts.debug).to.deep.equal({ screenshotCounter: data.id });
  });

  it('If client.playwrightVersion is set to 1.11 and greater modify the response sent to client', () => {
    socketObj = {
      id: '1234',
      send: sinon.spy(),
      close: sinon.spy(),
    };
    socketObj[kReqData] = {};
    const socketObjData = { 'client.playwrightVersion': '1.11' };
    socketObj[kReqData].data = socketObjData;
    data.params = {
      type: 'RemoteBrowser',
      initializer: { selectors: 'selectors', browser: 'browser' },
      guid: 'RemoteBrowser',
    };
    mockRegistryObject.stub.returns({
      railsSesionId: 'rails-1234',
      playwrightVersion: '1.10.0',
    });
    obj.writeResponse(socketObj, data);
    expect(data.params.type).to.deep.equal("Playwright");
    expect(data.params.guid).to.deep.equal("Playwright");
    expect(socketObj.send.callCount).to.equal(6);
  });

  it('If client.playwrightVersion is set to 1.10 and less modify the response sent to client', () => {
    socketObj[kReqData] = {};
    const socketObjData = { 'client.playwrightVersion': '1.10' };
    socketObj[kReqData].data = socketObjData;
    data.params = {
      type: 'Playwright',
      initializer: { selectors: 'selectors', browser: 'browser' },
      guid: 'Playwright',
    };
    obj.writeResponse(socketObj, data);
    expect(data.params.type).to.deep.equal("RemoteBrowser");
    expect(data.params.guid).to.deep.equal("remoteBrowser");
  });

  it('If client.playwrightVersion is set to 1.10 and less do not modify the response sent to client if not valid type', () => {
    socketObj[kReqData] = {};
    const socketObjData = { 'client.playwrightVersion': '1.10' };
    socketObj[kReqData].data = socketObjData;
    data.params = {
      type: 'Selector',
      initializer: { selectors: 'selectors', browser: 'browser' },
      guid: 'Selector',
    };
    obj.writeResponse(socketObj, data);
    expect(data.params.type).to.eql('Selector');
    expect(data.params.guid).to.eql('Selector');
  });

  it('If client.playwrightVersion is not set do not modify the response sent to client', () => {
    socketObj[kReqData] = {};
    const socketObjData = {};
    socketObj[kReqData].data = socketObjData;
    data.params = {
      type: 'RemoteBrowser',
      initializer: { selectors: 'selectors', browser: 'browser' },
      guid: 'RemoteBrowser',
    };
    obj.writeResponse(socketObj, data);
    expect(data.params.type).to.deep.equal("RemoteBrowser");
    expect(data.params.guid).to.deep.equal("RemoteBrowser");
  });

  it('If client.playwrightVersion is set to 1.15 and greater modify the response sent to client for BrowserContext', () => {
    socketObj[kReqData] = {};
    const socketObjData = {'client.playwrightVersion': '1.15.0' };
    socketObj[kReqData].data = socketObjData;
    data.params = {
      type: 'BrowserContext',
      initializer: { selectors: 'selectors', browser: 'browser' },
      guid: 'BrowserContext',
    };
    obj.writeResponse(socketObj, data);
    expect(data.params.type).to.deep.equal("BrowserContext");
    expect(data.params.guid).to.deep.equal("BrowserContext");
    expect(data.params.initializer.fetchRequest).to.deep.equal({});
  });

  it('If client.playwrightVersion is set to 1.15 and greater and playwright version is greater than 1.15 do not modify the response sent to client for BrowserContext', () => {
    socketObj[kReqData] = {};
    const socketObjData = {'client.playwrightVersion': '1.15.0' };
    socketObj[kReqData].data = socketObjData;
    data.params = {
      type: 'BrowserContext',
      initializer: { selectors: 'selectors', browser: 'browser' , fetchRequest: 'fetchRequest'},
      guid: 'BrowserContext',
    };
    mockRegistryObject.stub.returns({
      railsSesionId: 'rails-1234',
      playwrightVersion: '1.15.0',
    });
    obj.writeResponse(socketObj, data);
    expect(data.params.type).to.deep.equal("BrowserContext");
    expect(data.params.guid).to.deep.equal("BrowserContext");
    expect(data.params.initializer.fetchRequest).to.equal('fetchRequest');
  });

  it('If client.playwrightVersion is set to 1.17 and greater and playwright version is less than 1.17 modify the response sent to client for FetchRequest', () => {
    socketObj[kReqData] = {};
    const socketObjData = { 'client.playwrightVersion': '1.17.0' };
    socketObj[kReqData].data = socketObjData;
    data = {
      method: '__create__',
      params: {
        type: 'FetchRequest',
        initializer: {
          selectors: 'selectors',
          browser: 'browser',
          fetchRequest: 'fetchRequest',
        },
      },
    };
    mockRegistryObject.stub.returns({
      railsSesionId: 'rails-1234',
      playwrightVersion: '1.15.0',
    });
    obj.writeResponse(socketObj, data);
    expect(data.params.type).to.deep.equal('APIRequestContext');
  });

  it('If client.playwrightVersion is set to 1.16 and lower to 1.15 and playwright version is greater than 1.17 modify the response sent to client for APIRequestContext', () => {
    socketObj[kReqData] = {};
    const socketObjData = { 'client.playwrightVersion': '1.16.3' };
    socketObj[kReqData].data = socketObjData;
    data = {
      method: '__create__',
      params: {
        type: 'APIRequestContext',
        initializer: {
          selectors: 'selectors',
          browser: 'browser'
        },
      },
    };
    mockRegistryObject.stub.returns({
      railsSesionId: 'rails-1234',
      playwrightVersion: '1.17.1',
    });
    obj.writeResponse(socketObj, data);
    expect(data.params.type).to.deep.equal('FetchRequest');
  });

  it('If client.playwrightVersion is set to lower than 1.15 and playwright version is greater than 1.17 modify the response sent to client for APIRequestContext', () => {
    socketObj[kReqData] = {};
    const socketObjData = { 'client.playwrightVersion': '1.11.1' };
    socketObj[kReqData].data = socketObjData;
    data = {
      method: '__create__',
      params: {
        type: 'APIRequestContext',
        initializer: {
          selectors: 'selectors',
          browser: 'browser'
        },
      },
    };
    mockRegistryObject.stub.returns({
      railsSesionId: 'rails-1234',
      playwrightVersion: '1.17.1',
    });
    obj.writeResponse(socketObj, data);
    expect(data.params.type).to.deep.equal('BrowserContext');
  });

  it('If client.playwrightVersion is set to 1.17 and greater and playwright version is less than 1.17 modify the response sent to client for BrowserContext', () => {
    socketObj[kReqData] = {};
    const socketObjData = { 'client.playwrightVersion': '1.17.0' };
    socketObj[kReqData].data = socketObjData;
    data = {
      method: '__create__',
      params: {
        type: 'BrowserContext',
        initializer: {
          selectors: 'selectors',
          browser: 'browser',
          fetchRequest: 'fetchRequest',
        },
        guid: 'BrowserContext',
      },
    };
    mockRegistryObject.stub.returns({
      railsSesionId: 'rails-1234',
      playwrightVersion: '1.15.0',
    });
    obj.writeResponse(socketObj, data);
    expect(data.params.type).to.deep.equal('BrowserContext');
    expect(data.params.initializer.APIRequestContext).to.equal('fetchRequest');
  });

  it('If client.playwrightVersion is set to 1.16 and lower and playwright version is greater than 1.17 modify the response sent to client for BrowserContext', () => {
    socketObj[kReqData] = {};
    const socketObjData = { 'client.playwrightVersion': '1.16.3' };
    socketObj[kReqData].data = socketObjData;
    data = {
      method: '__create__',
      params: {
        type: 'BrowserContext',
        initializer: {
          selectors: 'selectors',
          browser: 'browser',
          APIRequestContext: 'fetchRequest',
        },
        guid: 'BrowserContext',
      },
    };
    mockRegistryObject.stub.returns({
      railsSesionId: 'rails-1234',
      playwrightVersion: '1.17.1',
    });
    obj.writeResponse(socketObj, data);
    expect(data.params.type).to.deep.equal('BrowserContext');
    expect(data.params.initializer.fetchRequest).to.equal('fetchRequest');
  });

  it('If client.playwrightVersion is set to 1.17 and lower and playwright version is greater than 1.18 dont write the response back to client for LocalUtils', () => {
    socketObj = {
      id: '1234',
      send: sinon.spy(),
      close: sinon.spy(),
    };
    socketObj[kReqData] = {};
    const socketObjData = { 'client.playwrightVersion': '1.17.2' };
    socketObj[kReqData].data = socketObjData;
    data.params = {
      type: 'LocalUtils',
      initializer: {},
      guid: 'LocalUtils',
    };
    mockRegistryObject.stub.returns({
      railsSesionId: 'rails-1234',
      playwrightVersion: '1.18.0',
    });
    obj.writeResponse(socketObj, data);
    expect(data.params.type).to.deep.equal("LocalUtils");
    expect(data.params.guid).to.deep.equal("LocalUtils");
    expect(socketObj.send.notCalled);
  });

  it('If client.playwrightVersion is set to 1.17 and lower and playwright version is greater than 1.18 modify the response back to client for Playwright', () => {
    socketObj = {
      id: '1234',
      send: sinon.spy(),
      close: sinon.spy(),
    };
    socketObj[kReqData] = {};
    const socketObjData = { 'client.playwrightVersion': '1.17.2' };
    socketObj[kReqData].data = socketObjData;
    data.params = {
      type: 'Playwright',
      initializer: { selectors: 'selectors', browser: 'browser', utils: 'utils' },
      guid: 'Playwright',
    };
    mockRegistryObject.stub.returns({
      railsSesionId: 'rails-1234',
      playwrightVersion: '1.18.0',
    });
    obj.writeResponse(socketObj, data);
    expect(data.params.type).to.deep.equal("Playwright");
    expect(data.params.guid).to.deep.equal("Playwright");
    expect(data.params.initializer.utils).to.be.undefined;
  });

  it('If client.playwrightVersion is set to 1.18 and higher and playwright version is lower than 1.18 modify the response back to client for Playwright', () => {
    socketObj = {
      id: '1234',
      send: sinon.spy(),
      close: sinon.spy(),
    };
    socketObj[kReqData] = {};
    const socketObjData = { 'client.playwrightVersion': '1.18.0' };
    socketObj[kReqData].data = socketObjData;
    data.params = {
      type: 'Playwright',
      initializer: { selectors: 'selectors', browser: 'browser'},
      guid: 'Playwright',
    };
    mockRegistryObject.stub.returns({
      railsSesionId: 'rails-1234',
      playwrightVersion: '1.17.2',
    });
    obj.writeResponse(socketObj, data);
    expect(data.params.type).to.deep.equal("Playwright");
    expect(data.params.guid).to.deep.equal("Playwright");
    expect(data.params.initializer.utils).not.to.be.null;
  });

  it('If client.playwrightVersion is set to 1.18 and lower and playwright version is greater than 1.19 dont write the response back to client for Tracing', () => {
    socketObj = {
      id: '1234',
      send: sinon.spy(),
      close: sinon.spy(),
    };
    socketObj[kReqData] = {};
    const socketObjData = { 'client.playwrightVersion': '1.17.2' };
    socketObj[kReqData].data = socketObjData;
    data.params = {
      type: 'Tracing',
      initializer: {},
      guid: 'Tracing',
    };
    mockRegistryObject.stub.returns({
      railsSesionId: 'rails-1234',
      playwrightVersion: '1.19.0',
    });
    obj.writeResponse(socketObj, data);
    expect(data.params.type).to.deep.equal("Tracing");
    expect(data.params.guid).to.deep.equal("Tracing");
    expect(socketObj[kReqData].data.tracingGuid).to.deep.equal('Tracing');
    expect(socketObj.send.notCalled);
  });

  it('If client.playwrightVersion is set to 1.19 and higher and playwright version is lower than 1.19 modify the response back to client for Playwright for APIRequestContext', () => {
    socketObj = {
      id: '1234',
      send: sinon.spy(),
      close: sinon.spy(),
    };
    socketObj[kReqData] = {};
    const socketObjData = { 'client.playwrightVersion': '1.19.0' };
    socketObj[kReqData].data = socketObjData;
    data.params = {
      type: 'APIRequestContext',
      initializer: {},
      guid: 'APIRequestContext',
    };
    mockRegistryObject.stub.returns({
      railsSesionId: 'rails-1234',
      playwrightVersion: '1.18.1',
    });
    obj.writeResponse(socketObj, data);
    expect(data.params.type).to.deep.equal("APIRequestContext");
    expect(data.params.guid).to.deep.equal("APIRequestContext");
    expect(data.params.initializer.tracing).not.to.be.null;
    expect(data.params.initializer.tracing.guid).not.to.be.null;
    expect(socketObj.send.calledTwice);
  });

  it('If client.playwrightVersion is set to 1.19 and higher and playwright version is lower than 1.19 modify the response back to client for Playwright for BrowserContext', () => {
    socketObj = {
      id: '1234',
      send: sinon.spy(),
      close: sinon.spy(),
    };
    socketObj[kReqData] = {};
    const socketObjData = { 'client.playwrightVersion': '1.19.0' };
    socketObj[kReqData].data = socketObjData;
    data.params = {
      type: 'BrowserContext',
      initializer: {},
      guid: 'BrowserContext',
    };
    mockRegistryObject.stub.returns({
      railsSesionId: 'rails-1234',
      playwrightVersion: '1.18.1',
    });
    obj.writeResponse(socketObj, data);
    expect(data.params.type).to.deep.equal("BrowserContext");
    expect(data.params.guid).to.deep.equal("BrowserContext");
    expect(data.params.initializer.tracing).not.to.be.null;
    expect(data.params.initializer.tracing.guid).not.to.be.null;
    expect(socketObj.send.calledOnce);
  });

  it('If client.playwrightVersion is set to 1.19 and higher and playwright version is lower than 1.15 modify the response back to client for Playwright for BrowserContext', () => {
    socketObj = {
      id: '1234',
      send: sinon.spy(),
      close: sinon.spy(),
    };
    socketObj[kReqData] = {};
    const socketObjData = { 'client.playwrightVersion': '1.19.0' };
    socketObj[kReqData].data = socketObjData;
    data.params = {
      type: 'BrowserContext',
      initializer: {},
      guid: 'BrowserContext',
    };
    mockRegistryObject.stub.returns({
      railsSesionId: 'rails-1234',
      playwrightVersion: '1.14.1',
    });
    obj.writeResponse(socketObj, data);
    expect(data.params.type).to.deep.equal("BrowserContext");
    expect(data.params.guid).to.deep.equal("BrowserContext");
    expect(data.params.initializer.tracing).not.to.be.null;
    expect(data.params.initializer.tracing.guid).not.to.be.null;
    expect(socketObj.send.calledThrice);
  });

  it('If client.playwrightVersion is set to 1.19 and higher and playwright version is lower than 1.19 store the browserContextGuid for Tracing', () => {
    socketObj = {
      id: '1234',
      send: sinon.spy(),
      close: sinon.spy(),
    };
    socketObj[kReqData] = {};
    const socketObjData = { 'client.playwrightVersion': '1.19.0' };
    socketObj[kReqData].data = socketObjData;
    data.params = {
      type: 'BrowserContext',
      initializer: {},
      guid: 'BrowserContext',
    };
    mockRegistryObject.stub.returns({
      railsSesionId: 'rails-1234',
      playwrightVersion: '1.17.2',
    });
    obj.writeResponse(socketObj, data);
    expect(data.params.type).to.deep.equal("BrowserContext");
    expect(data.params.guid).to.deep.equal("BrowserContext");
    expect(socketObj[kReqData].data.browserContextGuid).to.deep.equal(data.params.guid);
    expect(data.params.initializer.tracing).not.to.be.null;
    expect(data.params.initializer.tracing.guid).not.to.be.null;
    expect(socketObj.send.calledThrice);
  });

  it('If client.playwrightVersion is set to 1.19 and higher and playwright version is lower than 1.19 modify the response back to client for Playwright for Artifact', () => {
    socketObj = {
      id: '1234',
      send: sinon.spy(),
      close: sinon.spy(),
    };
    socketObj[kReqData] = {};
    const socketObjData = { 'client.playwrightVersion': '1.19.0', browserContextGuid: 'browser-context@1234' };
    socketObj[kReqData].data = socketObjData;
    data.params = {
      type: 'Artifact',
      initializer: {},
      guid: 'Artifact',
    };
    data.guid = 'browser-context@1234';
    mockRegistryObject.stub.returns({
      railsSesionId: 'rails-1234',
      playwrightVersion: '1.17.2',
    });
    obj.writeResponse(socketObj, data);
    expect(data.guid).to.deep.equal('Tracing@fa1f6532f5bb25b919709c9c7339509e');
    expect(socketObj.send.calledOnce);
  });

  it('If client.playwrightVersion is set tolower than 1.19 and playwright version is 1.19 or greater then modify the guid back to client for Playwright for Tracing', () => {
    socketObj = {
      id: '1234',
      send: sinon.spy(),
      close: sinon.spy(),
    };
    socketObj[kReqData] = {};
    const socketObjData = { 'client.playwrightVersion': '1.18.0', browserContextGuid: 'browser-context@1234', tracingGuid: 'tracing@1234' };
    socketObj[kReqData].data = socketObjData;
    data.params = {
      type: 'Artifact',
      initializer: {},
      guid: 'Artifact',
    };
    data.guid = 'tracing@1234';
    mockRegistryObject.stub.returns({
      railsSesionId: 'rails-1234',
      playwrightVersion: '1.19.1',
    });
    obj.writeResponse(socketObj, data);
    expect(data.guid).to.deep.equal('browser-context@1234');
    expect(socketObj.send.calledOnce);
  });

  it('If client.playwrightVersion is set to 1.34.0 and greater and playwright version is lower than 1.34.0 dont write the response back to client for ConsoleMessage', () => {
    socketObj = {
      id: '1234',
      send: sinon.spy(),
      close: sinon.spy()
    };
    socketObj[kReqData] = {};
    const socketObjData = { 'client.playwrightVersion': '1.34.3' };
    socketObj[kReqData].data = socketObjData;
    data.method = '__create__'
    data.params = {
      type: 'ConsoleMessage',
      initializer: {},
      guid: 'console-message@15a29b0a42ecccb5a15e3ba9378067b2',
    };
    mockRegistryObject.stub.returns({
      railsSesionId: 'rails-1234',
      playwrightVersion: '1.33.0',
    });
    obj.writeResponse(socketObj, data);
    expect(data.params.type).to.deep.equal("ConsoleMessage");
    expect(socketObj.send.notCalled);
  });

  it('If client.playwrightVersion is set to 1.34.0 and greater and playwright version is lower than 1.34.0 dont write the response back to client for console method', () => {
    socketObj = {
      id: '1234',
      send: sinon.spy(),
      close: sinon.spy()
    };
    socketObj[kReqData] = {};
    const socketObjData = { 'client.playwrightVersion': '1.34.3' };
    socketObj[kReqData].data = socketObjData;
    data.method = 'console'
    data.params = {
      message: {
        guid: "console-message@15a29b0a42ecccb5a15e3ba9378067b2"
      }
    };
    mockRegistryObject.stub.returns({
      railsSesionId: 'rails-1234',
      playwrightVersion: '1.33.0',
    });
    obj.writeResponse(socketObj, data);
    expect(socketObj.send.notCalled);
  });

  it('backward compatibility for >= 1.39.0 client and <= 1.38.1 browserstack.playwright', () => {
    socketObj = {
      id: '1234',
      send: sinon.spy(),
      close: sinon.spy(),
    };
    socketObj[kReqData] = {};
    const socketObjData = { 'client.playwrightVersion': '1.39.0' };
    socketObj[kReqData].data = socketObjData;
    data.params = {
      type: 'LocalUtils',
      initializer: {},
      guid: 'LocalUtils',
    };
    mockRegistryObject.stub.returns({
      railsSesionId: 'rails-1234',
      playwrightVersion: '1.38.1',
    });
    obj.writeResponse(socketObj, data);
    expect(data.params.initializer.deviceDescriptors).to.deep.equal(deviceDescriptors);
  });
});
