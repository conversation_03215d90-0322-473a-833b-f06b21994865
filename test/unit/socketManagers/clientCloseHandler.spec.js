'use strict';

const sinon = require('sinon');
const rewire = require('rewire');

const rewireHandler = rewire('../../../socketManagers/baseSocketMessageHandler');
const { wsProxy } = require('./socketHelpers');
const { assert } = require('chai');
const { mockLoggerSpies: mockLogger } = require('./socketHelpers');

describe('Client close handler', () => {
  const SocketMessageHandler = rewireHandler.__get__('SocketMessageHandler');
  const DesktopSocketHandler = rewireHandler.__get__('DesktopSocketManager');
  let handler;
  let originalBasicLogger;

  beforeEach(() => {
    sinon.stub(SocketMessageHandler.prototype, 'enableMethodMaps', () => {});
    sinon.stub(SocketMessageHandler.prototype, 'debugScreenshotMethodMaps', () => {});
    sinon.stub(SocketMessageHandler.prototype, 'assignLogBuilder', () => {});
    handler = new SocketMessageHandler();
    originalBasicLogger = rewireHandler.__get__('logger');

    rewireHandler.__set__('logger', mockLogger);
  });

  afterEach(() => {
    SocketMessageHandler.prototype.enableMethodMaps.restore();
    SocketMessageHandler.prototype.debugScreenshotMethodMaps.restore();
    rewireHandler.__set__('logger', originalBasicLogger);
    SocketMessageHandler.prototype.assignLogBuilder.restore();
  });

  it('should proxy to desktop connection', () => {
    const ws = wsProxy();

    sinon.stub(DesktopSocketHandler.prototype, 'terminateSocket', () => {});
    handler.completedSessions.add(ws.id);
    handler.clientCloseHandler(ws);

    assert(DesktopSocketHandler.prototype.terminateSocket.calledOnce);

    DesktopSocketHandler.prototype.terminateSocket.restore();
  });
});
