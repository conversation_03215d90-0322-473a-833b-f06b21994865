'use strict';

const sinon = require('sinon');
const rewire = require('rewire');

const rewireHandler = rewire('../../../socketManagers/puppeteerHandler');
const { wsProxy } = require('./socketHelpers');
const { assert, expect } = require('chai');
const { mockLoggerSpies: mockLogger } = require('./socketHelpers');

describe('Client close handler', () => {
  const PuppeteerHandler = rewireHandler.__get__('PuppeteerHandler');
  const RAILS_SESSION_ID = 'rails-1234';

  let handler;
  let originalBasicLogger;
  let originalMarkIdle;
  let idleSpy;

  beforeEach(() => {
    handler = new PuppeteerHandler();
    originalBasicLogger = rewireHandler.__get__('logger');
    originalMarkIdle = rewireHandler.__get__('markIdle');
    idleSpy = sinon.stub();

    rewireHandler.__set__('logger', mockLogger);
    rewireHandler.__set__('markIdle', idleSpy);
    sinon.stub(handler, 'closeTerminalSocket', () => {});
    sinon.stub(handler, 'startTerminalCleanup', () => {});
  });

  afterEach(() => {
    rewireHandler.__set__('logger', originalBasicLogger);
    rewireHandler.__set__('markIdle', originalMarkIdle);
    handler.closeTerminalSocket.restore();
    handler.startTerminalCleanup.restore();
  });

  it('should call the idle when errorneous response code received', () => {
    const ws = wsProxy();
    handler.railsSessionMapping.set(ws.id, RAILS_SESSION_ID);
    handler.clientCloseHandler(ws, { code: 1008 });
    assert(idleSpy.calledOnce);
    const { 1: message } = idleSpy.args[0];
    expect(message).to.be.eql('TIMEOUT');
  });

  it('should call the idle when abrupt close code received', () => {
    const ws = wsProxy();
    handler.railsSessionMapping.set(ws.id, RAILS_SESSION_ID);
    handler.clientCloseHandler(ws, { code: 1006 });
    assert(idleSpy.calledOnce);
    const { 1: message } = idleSpy.args[0];
    expect(message).to.be.eql('ABRUPT_CLOSE');
  });

  it('in case of exception should call error log', () => {
    const ws = wsProxy();
    handler.railsSessionMapping.set(ws.id, RAILS_SESSION_ID);
    const errObj = new Error('unable to make request');
    idleSpy.throws(errObj);
    handler.clientCloseHandler(ws, { code: 1008 });
    assert(idleSpy.calledOnce);
  });

  it('startTerminalCleanup and closeTerminalSocket gets called when passed GRACEFULE_CLOSE_CODES', ()=>{
    const ws = wsProxy();
    handler.railsSessionMapping.set(ws.id, RAILS_SESSION_ID);
    handler.clientCloseHandler(ws, { code: 1001 });
    assert(handler.startTerminalCleanup.calledOnce);
    assert(handler.closeTerminalSocket.calledOnce);
  })

  it('startTerminalCleanup and closeTerminalSocket dont get called when not passed GRACEFULE_CLOSE_CODES', ()=>{
    const ws = wsProxy();
    handler.railsSessionMapping.set(ws.id, RAILS_SESSION_ID);
    handler.clientCloseHandler(ws, { code: 1009 });
    expect(handler.startTerminalCleanup.called).to.eql(false);
    expect(handler.closeTerminalSocket.called).to.eql(false);
  })

  it('startTerminalCleanup and closeTerminalSocket dont get called when passed GRACEFUL_CLOSE_CODES but session is already marked as completed', ()=>{
    const ws = wsProxy();
    handler.railsSessionMapping.set(ws.id, RAILS_SESSION_ID);
    handler.addToCompletedSessions(ws.id);
    handler.clientCloseHandler(ws, { code: 1001 });
    expect(handler.startTerminalCleanup.called).to.eql(false);
    expect(handler.closeTerminalSocket.called).to.eql(false);
  })
});
