'use strict';

/* eslint-disable no-unused-expressions */

const {
  describe, it, beforeEach, afterEach,
} = require('mocha');
const sinon = require('sinon');
const { expect } = require('chai');
const fs = require('fs');
const clusterUtil = require('../../clusterUtil');

describe('clusterUtil', () => {
  beforeEach(() => {
    sinon.stub(console, 'log', () => {});
  });

  afterEach(() => {
    console.log.restore();
  });

  it('should return false when json parsing is not valid', () => {
    sinon.stub(fs, 'readFileSync', () => 'invalid JSON parsing error');

    const safeStatus = clusterUtil.isSafeToStartProcess('validFilenamePath');
    expect(safeStatus).to.be.false;
    fs.readFileSync.restore();
  });

  it('should send false when file path is not valid', () => {
    sinon.stub(fs, 'readFileSync', () => {
      throw new Error('unable to read file');
    });

    const safeStatus = clusterUtil.isSafeToStartProcess('randomPath');
    expect(safeStatus).to.be.false;
    fs.readFileSync.restore();
  });

  it('should send true when filepath is correct', () => {
    sinon.stub(fs, 'readFileSync', () => JSON.stringify({ key: 'some random value' }));

    const safeStatus = clusterUtil.isSafeToStartProcess('randomPath');
    expect(safeStatus).to.be.true;
    fs.readFileSync.restore();
  });
});
