'use strict';

/* eslint-disable no-underscore-dangle */

const { assert } = require('chai');
const rewire = require('rewire');

const rewiredBrowserStack = rewire('../../browserstack');

describe('setBrowserProxy', () => {
  let lcaps;
  let bsParams;
  let browserOptions;
  let setBrowserProxy;

  beforeEach(() => {
    lcaps = {
      os: 'windows',
      os_version: 10,
    };
    bsParams = {};
    browserOptions = {};
    setBrowserProxy = rewiredBrowserStack.__get__('setBrowserProxy');
  });

  describe('chrome', () => {
    beforeEach(() => {
      lcaps = { ...lcaps, browser: 'chrome', browserName: 'chrome' };
    });

    it('should set loopback arg for chrome greater than 70 when setSocks enabled', () => {
      lcaps = { ...lcaps, setSocksProxy: true, browser_version: 85 };
      bsParams = {
        ...bsParams,
        'browserstack.tunnel': true,
        local_params: {
          tunnelHostServer: 'repeater-xyz.com',
          tunnelPorts: 8089,
        },
      };
      setBrowserProxy(lcaps, bsParams, browserOptions);

      const { chromeOptions: { args } } = lcaps;
      assert(args.includes('--proxy-bypass-list=<-loopback>'));
    });

    it('should not set loopback arg for chrome less than or equal to 70 when setSocks enabled', () => {
      lcaps = { ...lcaps, setSocksProxy: true, browser_version: 70 };
      bsParams = {
        ...bsParams,
        'browserstack.tunnel': true,
        local_params: {
          tunnelHostServer: 'repeater-xyz.com',
          tunnelPorts: 8089,
        },
      };
      setBrowserProxy(lcaps, bsParams, browserOptions);

      const { chromeOptions: { args } } = lcaps;
      assert(!args.includes('--proxy-bypass-list=<-loopback>'));
    });

    it('should set proxy file url for mac and browser version equal to 70 when setSocks disabled', () => {
      lcaps = { ...lcaps, setSocksProxy: false, browser_version: 70, orig_os: "mac" };
      bsParams = {
        ...bsParams,
        'browserstack.tunnel': true,
        local_params: {
          tunnelHostServer: 'repeater-xyz.com',
          tunnelPorts: 8089,
        },
      };
      setBrowserProxy(lcaps, bsParams, browserOptions);
      assert.deepEqual(lcaps["proxy"], { proxyType: 'PAC', proxyAutoconfigUrl: 'http://localhost:45671/pacfile' });
    });

    it('should set default proxy file URL when not mac and not internet explorer and setSocks disabled', () => {
      lcaps = { ...lcaps, setSocksProxy: false, browser_version: 70, orig_os: "win" };
      bsParams = {
        ...bsParams,
        'browserstack.tunnel': true,
        local_params: {
          tunnelHostServer: 'repeater-xyz.com',
          tunnelPorts: 8089,
        },
      };
      setBrowserProxy(lcaps, bsParams, browserOptions);
      assert.deepEqual(lcaps["proxy"], { proxyType: 'PAC', proxyAutoconfigUrl: 'file://c:/Users/<USER>/proxy.pac' });
    });

    it('should set default proxy file URL when not mac and not internet explorer and setSocks disabled', () => {
      lcaps = { ...lcaps, setSocksProxy: false, browser_version: 71, orig_os: "win" };
      bsParams = {
        ...bsParams,
        'browserstack.tunnel': true,
        local_params: {
          tunnelHostServer: 'repeater-xyz.com',
          tunnelPorts: 8089,
        },
      };
      setBrowserProxy(lcaps, bsParams, browserOptions);
      assert.deepEqual(lcaps["chromeOptions"], {
        args: [
          '--proxy-server=http://platform.browserstack.com:45696',
          '--proxy-bypass-list=<-loopback>;'
        ]
      });
    });
  });

  describe('internet explorer', () => {
    beforeEach(() => {
      lcaps = { ...lcaps, browser: 'internet explorer', browserName: 'internet explorer' };
    });

    it('should proxy file url for internet explorer less than or equal to 70 when setSocks disabled', () => {
      lcaps = { ...lcaps, setSocksProxy: false, browser_version: 70, orig_os: "win" };
      bsParams = {
        ...bsParams,
        'browserstack.tunnel': true,
        'browserstack.ie.alternateProxy': 'true',
        local_params: {
          tunnelHostServer: 'repeater-xyz.com',
          tunnelPorts: 8089,
        },
      };
      setBrowserProxy(lcaps, bsParams, browserOptions);
      assert.deepEqual(lcaps["proxy"], { proxyType: 'PAC', proxyAutoconfigUrl: 'http://localhost:4567/pacfile' });
    });
  });

  describe('firefox', () => {
    beforeEach(() => {
      lcaps = { ...lcaps, browser: 'firefox', browserName: 'firefox', version: 5 };
    });

    it('should proxy file url for internet explorer less than or equal to 70 when setSocks disabled', () => {
      lcaps = { ...lcaps, setSocksProxy: true, browser_version: 70, orig_os: "win" };
      bsParams = {
        ...bsParams,
        'browserstack.tunnel': true,
        local_params: {
          tunnelHostServer: 'repeater-xyz.com',
          tunnelPorts: 8089,
        },
      };
      setBrowserProxy(lcaps, bsParams, browserOptions);
      assert.deepEqual(lcaps["proxy"], { proxyType: 'MANUAL', socksProxy: 'repeater-xyz.com:8089' });
    });
  });
});
