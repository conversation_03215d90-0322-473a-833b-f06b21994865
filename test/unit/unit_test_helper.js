'use strict';

const helper = require('../../helper');
const should = require('chai').should();
const assert = require('assert');

const constants_mock = {
  A11Y_PRESIGNED_URL_KEY: 'A11Y-PRESIGNED-URL'
};

describe('helper.js', function() {
  it('should not add zipAlign params to fireCommandOptions', function () {
    var getOptions = {};
    var options = {"bsCaps":{"orig_os": "ios"}};

    helper.zipAlignCheck(getOptions, options);
    assert.strictEqual(getOptions.zip_align, undefined);
  });

  it('should add zipAlign params to fireCommandOptions', function () {
    var getOptions = {};
    var options = {"bsCaps":{"orig_os": "android", "zip_align": "true"}};

    helper.zipAlignCheck(getOptions, options);
    assert.strictEqual(getOptions["zip_align"], "true");
  });

  describe('setAccessibilityAutomationConfigJSON', () => {
    it('should set accessibility and accessibility options when browserstack.accessibility is true', () => {
      const getOptions = {};
      const options = {
        browserstackParams: {
          'browserstack.accessibility': true,
          'browserstack.accessibilityOptions.scannerVersion': '1.0',
          'browserstack.accessibilityOptions.wcagVersion': 'wcag21aaa',
          'browserstack.accessibilityOptions.scannerProcessingTimeout': 5,
          'browserstack.accessibilityOptions.includeIssueType.needsReview': true,
          'browserstack.accessibilityOptions.includeIssueType.bestPractice': false,
          'browserstack.accessibilityOptions.authToken': 'abc123'
        }
      };
      helper.setAccessibilityAutomationConfigJSON(getOptions, options);
      assert.deepStrictEqual(getOptions, {
        accessibility: true,
        accessibilityOptions: JSON.stringify({
          scannerVersion: '1.0',
          wcagVersion: 'wcag21aaa',
          scannerProcessingTimeout: 5,
          includeIssueType: {
            needsReview: true,
            bestPractice: false
          },
          auth: 'abc123'
        })
      });
    });
  
    it('should not set accessibility or accessibility options when browserstack.accessibility is false', () => {
      const getOptions = {};
      const options = {
        browserstackParams: {
          'browserstack.accessibility': false,
          'browserstack.accessibilityOptions.scannerVersion': '1.0',
          'browserstack.accessibilityOptions.wcagVersion': 'wcag21aaa',
          'browserstack.accessibilityOptions.scannerProcessingTimeout': 5,
          'browserstack.accessibilityOptions.includeIssueType.needsReview': true,
          'browserstack.accessibilityOptions.includeIssueType.bestPractice': false,
          'browserstack.accessibilityOptions.authToken': 'abc123'
        }
      };
      helper.setAccessibilityAutomationConfigJSON(getOptions, options);
      assert.deepStrictEqual(getOptions, {});
    });
    it('should set accessibility and accessibility options when browserstack.accessibility is true and non w3c caps', () => {
      const getOptions = {};
      const options = {
        browserstackParams: {
          'browserstack.accessibility': true,
          'browserstack.accessibilityOptions': {
            scannerVersion: '1.0',
            wcagVersion: 'wcag21aaa',
            scannerProcessingTimeout: 5,
            includeIssueType: {
              needsReview: true,
              bestPractice: false,
            },
            authToken: 'abc123'
          }
        }
      };
      helper.setAccessibilityAutomationConfigJSON(getOptions, options);
      assert.deepStrictEqual(getOptions, {
        accessibility: true,
        accessibilityOptions: JSON.stringify({
          scannerVersion: '1.0',
          wcagVersion: 'wcag21aaa',
          scannerProcessingTimeout: 5,
          includeIssueType: {
            needsReview: true,
            bestPractice: false
          },
          auth: 'abc123'
        })
      });
    });
  });

  describe('a11yIsURL', function() {
    it('should return true for a valid URL starting with "http"', function() {
      assert.strictEqual(helper.a11yIsURL('http://example.com'), true);
    });
  
    it('should return true for a valid URL starting with "https"', function() {
      assert.strictEqual(helper.a11yIsURL('https://example.com'), true);
    });
  
    it('should return false for a string shorter than 4 characters', function() {
      assert.strictEqual(helper.a11yIsURL('htt'), false);
    });
  
    it('should return false for a string not starting with "http"', function() {
      assert.strictEqual(helper.a11yIsURL('ftp://example.com'), false);
    });
  
    it('should return false for undefined', function() {
      assert.strictEqual(helper.a11yIsURL(undefined), false);
    });
  
    it('should return false for a non-string value', function() {
      assert.strictEqual(helper.a11yIsURL(1234), false);
      assert.strictEqual(helper.a11yIsURL([]), false);
      assert.strictEqual(helper.a11yIsURL({}), false);
    });
  });

  describe('a11yGetReplacementValue', function() {
    it('should return the URL without the query string for a presigned URL', function() {
      assert.strictEqual(helper.a11yGetReplacementValue('http://example.com/resource?signature=abc123'), 'http://example.com/resource');
    });
  
    it('should return the original URL if there is no query string', function() {
      assert.strictEqual(helper.a11yGetReplacementValue('http://example.com/resource'), 'http://example.com/resource');
    });
  
    it('should return the original value if it is not a URL', function() {
      assert.strictEqual(helper.a11yGetReplacementValue('Not a URL'), 'Not a URL');
    });
  
    it('should return the original value if it is null', function() {
      assert.strictEqual(helper.a11yGetReplacementValue(null), null);
    });
  
    it('should return the original value if it is undefined', function() {
      assert.strictEqual(helper.a11yGetReplacementValue(undefined), undefined);
    });
  });
  
  describe('a11yIsPresignedURL', function() {
    it('should return true for a presigned URL with a query string', function() {
      assert.strictEqual(helper.a11yIsPresignedURL('http://example.com/resource?signature=abc123'), true);
    });
  
    it('should return false for a URL without a query string', function() {
      assert.strictEqual(helper.a11yIsPresignedURL('http://example.com/resource'), false);
    });
  
    it('should return false for a non-URL string', function() {
      assert.strictEqual(helper.a11yIsPresignedURL('Not a URL'), false);
    });
  
    it('should return false for null', function() {
      assert.strictEqual(helper.a11yIsPresignedURL(null), false);
    });
  
    it('should return false for undefined', function() {
      assert.strictEqual(helper.a11yIsPresignedURL(undefined), false);
    });
  });

  describe('addAccessibilityAutomationPresignedURL', function() {
    it('should do nothing if "browserstack.accessibility" is not true', function() {
      let browserstackParams = { "browserstack.accessibility": false };
      let railsOmittedCaps = { [constants_mock.A11Y_PRESIGNED_URL_KEY]: 'presigned-url' };
      helper.addAccessibilityAutomationPresignedURL(browserstackParams, railsOmittedCaps);
      assert.strictEqual(browserstackParams["browserstack.accessibilityOptions"], undefined);
      assert.strictEqual(browserstackParams["browserstack.accessibilityOptions.scannerVersion"], undefined);
      assert.strictEqual(railsOmittedCaps[constants_mock.A11Y_PRESIGNED_URL_KEY], 'presigned-url');
    });
  
    it('should do nothing if "railsOmittedCaps[A11Y_PRESIGNED_URL_KEY]" is not present', function() {
      let browserstackParams = { "browserstack.accessibility": true };
      let railsOmittedCaps = {};
      helper.addAccessibilityAutomationPresignedURL(browserstackParams, railsOmittedCaps);
      assert.strictEqual(browserstackParams["browserstack.accessibilityOptions"], undefined);
      assert.strictEqual(browserstackParams["browserstack.accessibilityOptions.scannerVersion"], undefined);
    });
  
    it('should set "scannerVersion" in "browserstack.accessibilityOptions" if it exists', function() {
      let browserstackParams = {
        "browserstack.accessibility": true,
        "browserstack.accessibilityOptions": {}
      };
      let railsOmittedCaps = { [constants_mock.A11Y_PRESIGNED_URL_KEY]: 'presigned-url' };
      helper.addAccessibilityAutomationPresignedURL(browserstackParams, railsOmittedCaps);
      assert.strictEqual(browserstackParams["browserstack.accessibilityOptions"].scannerVersion, 'presigned-url');
      assert.strictEqual(railsOmittedCaps[constants_mock.A11Y_PRESIGNED_URL_KEY], undefined);
    });
  
    it('should delete "railsOmittedCaps[A11Y_PRESIGNED_URL_KEY]" after setting scannerVersion', function() {
      let browserstackParams = { "browserstack.accessibility": true };
      let railsOmittedCaps = { [constants_mock.A11Y_PRESIGNED_URL_KEY]: 'presigned-url' };
      helper.addAccessibilityAutomationPresignedURL(browserstackParams, railsOmittedCaps);
      assert.strictEqual(railsOmittedCaps[constants_mock.A11Y_PRESIGNED_URL_KEY], undefined);
    });
  });

  describe('removeAccessibilityAutomationPresignedURL', function() {
    it('should remove the presigned URL from "desiredCapabilities.bstack:options.accessibilityOptions.scannerVersion"', function() {
      let caps = {
        "desiredCapabilities": {
          "bstack:options": {
            "accessibilityOptions": {
              "scannerVersion": "http://example.com/resource?signature=abc123"
            }
          }
        }
      };
      let railsOmittedCaps = {};
  
      helper.removeAccessibilityAutomationPresignedURL(caps, railsOmittedCaps);
  
      assert.strictEqual(caps["desiredCapabilities"]["bstack:options"]["accessibilityOptions"]["scannerVersion"], "http://example.com/resource");
      assert.strictEqual(railsOmittedCaps[constants.A11Y_PRESIGNED_URL_KEY], "http://example.com/resource?signature=abc123");
    });
  
    it('should remove the presigned URL from "desiredCapabilities.accessibilityOptions.scannerVersion"', function() {
      let caps = {
        "desiredCapabilities": {
          "accessibilityOptions": {
            "scannerVersion": "http://example.com/resource?signature=abc123"
          }
        }
      };
      let railsOmittedCaps = {};
  
      helper.removeAccessibilityAutomationPresignedURL(caps, railsOmittedCaps);
  
      assert.strictEqual(caps["desiredCapabilities"]["accessibilityOptions"]["scannerVersion"], "http://example.com/resource");
      assert.strictEqual(railsOmittedCaps[constants.A11Y_PRESIGNED_URL_KEY], "http://example.com/resource?signature=abc123");
    });
  
    it('should remove the presigned URL from "desiredCapabilities.browserstack.accessibilityOptions.scannerVersion"', function() {
      let caps = {
        "desiredCapabilities": {
          "browserstack.accessibilityOptions.scannerVersion": "http://example.com/resource?signature=abc123"
        }
      };
      let railsOmittedCaps = {};
  
      helper.removeAccessibilityAutomationPresignedURL(caps, railsOmittedCaps);
  
      assert.strictEqual(caps["desiredCapabilities"]["browserstack.accessibilityOptions.scannerVersion"], "http://example.com/resource");
      assert.strictEqual(railsOmittedCaps[constants.A11Y_PRESIGNED_URL_KEY], "http://example.com/resource?signature=abc123");
    });
  
    it('should remove the presigned URL from "capabilities.alwaysMatch.bstack:options.accessibilityOptions.scannerVersion"', function() {
      let caps = {
        "capabilities": {
          "alwaysMatch": {
            "bstack:options": {
              "accessibilityOptions": {
                "scannerVersion": "http://example.com/resource?signature=abc123"
              }
            }
          }
        }
      };
      let railsOmittedCaps = {};
  
      helper.removeAccessibilityAutomationPresignedURL(caps, railsOmittedCaps);
  
      assert.strictEqual(caps["capabilities"]["alwaysMatch"]["bstack:options"]["accessibilityOptions"]["scannerVersion"], "http://example.com/resource");
      assert.strictEqual(railsOmittedCaps[constants.A11Y_PRESIGNED_URL_KEY], "http://example.com/resource?signature=abc123");
    });
  
    it('should remove the presigned URL from "capabilities.firstMatch[0].bstack:options.accessibilityOptions.scannerVersion"', function() {
      let caps = {
        "capabilities": {
          "firstMatch": [
            {
              "bstack:options": {
                "accessibilityOptions": {
                  "scannerVersion": "http://example.com/resource?signature=abc123"
                }
              }
            }
          ]
        }
      };
      let railsOmittedCaps = {};
  
      helper.removeAccessibilityAutomationPresignedURL(caps, railsOmittedCaps);
  
      assert.strictEqual(caps["capabilities"]["firstMatch"][0]["bstack:options"]["accessibilityOptions"]["scannerVersion"], "http://example.com/resource");
      assert.strictEqual(railsOmittedCaps[constants.A11Y_PRESIGNED_URL_KEY], "http://example.com/resource?signature=abc123");
    });
  
    it('should remove the presigned URL from "capabilities.firstMatch[0].accessibilityOptions.scannerVersion"', function() {
      let caps = {
        "capabilities": {
          "firstMatch": [
            {
              "accessibilityOptions": {
                "scannerVersion": "http://example.com/resource?signature=abc123"
              }
            }
          ]
        }
      };
      let railsOmittedCaps = {};
  
      helper.removeAccessibilityAutomationPresignedURL(caps, railsOmittedCaps);
  
      assert.strictEqual(caps["capabilities"]["firstMatch"][0]["accessibilityOptions"]["scannerVersion"], "http://example.com/resource");
      assert.strictEqual(railsOmittedCaps[constants.A11Y_PRESIGNED_URL_KEY], "http://example.com/resource?signature=abc123");
    });
  
    it('should not modify capabilities if scannerVersion is not a presigned URL', function() {
      let caps = {
        "desiredCapabilities": {
          "bstack:options": {
            "accessibilityOptions": {
              "scannerVersion": "http://example.com/resource"
            }
          }
        }
      };
      let railsOmittedCaps = {};
  
      helper.removeAccessibilityAutomationPresignedURL(caps, railsOmittedCaps);
  
      assert.strictEqual(caps["desiredCapabilities"]["bstack:options"]["accessibilityOptions"]["scannerVersion"], "http://example.com/resource");
      assert.strictEqual(railsOmittedCaps[constants.A11Y_PRESIGNED_URL_KEY], undefined);
    });
  
    it('should do nothing if capabilities do not contain scannerVersion', function() {
      let caps = {
        "desiredCapabilities": {}
      };
      let railsOmittedCaps = {};
  
      helper.removeAccessibilityAutomationPresignedURL(caps, railsOmittedCaps);
  
      assert.strictEqual(railsOmittedCaps[constants.A11Y_PRESIGNED_URL_KEY], undefined);
    });

    it('should remove the presigned URL from "alwaysMatch.accessibilityOptions.scannerVersion"', function() {
      let caps = {
        "capabilities": {
          "alwaysMatch": {
            "accessibilityOptions": {
              "scannerVersion": "http://example.com/resource?signature=abc123"
            }
          }
        }
      };
      let railsOmittedCaps = {};
  
      helper.removeAccessibilityAutomationPresignedURL(caps, railsOmittedCaps);
  
      // Check if the URL has been modified correctly
      assert.strictEqual(caps["capabilities"]["alwaysMatch"]["accessibilityOptions"]["scannerVersion"], "http://example.com/resource");
      
      // Check if the original presigned URL has been stored in railsOmittedCaps
      assert.strictEqual(railsOmittedCaps[constants_mock.A11Y_PRESIGNED_URL_KEY], "http://example.com/resource?signature=abc123");
    });
  
    it('should not modify "alwaysMatch.accessibilityOptions.scannerVersion" if it is not a presigned URL', function() {
      let caps = {
        "capabilities": {
          "alwaysMatch": {
            "accessibilityOptions": {
              "scannerVersion": "http://example.com/resource"
            }
          }
        }
      };
      let railsOmittedCaps = {};
  
      helper.removeAccessibilityAutomationPresignedURL(caps, railsOmittedCaps);
  
      // Check that the URL remains unchanged
      assert.strictEqual(caps["capabilities"]["alwaysMatch"]["accessibilityOptions"]["scannerVersion"], "http://example.com/resource");
      
      // Check that nothing was added to railsOmittedCaps
      assert.strictEqual(railsOmittedCaps[constants_mock.A11Y_PRESIGNED_URL_KEY], undefined);
    });
  });
});
