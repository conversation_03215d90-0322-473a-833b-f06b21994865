/* eslint-disable no-unused-expressions */
var origHelper = require('../../helper'),
helper = require('./helper'),
sinon = require('sinon'),
chai = require('chai').should(),
assert = require('assert'),
errorMessages = require('../../errorMessages')
constants = require('../../constants');
const requestLib = require('../../lib/request');
const HubLogger = require('../../log');
const { expect } = require("chai");
const redisClient = require('../../redisUtils').redisClient;
const pubSub = require('../../pubSub');
const rewire = require("rewire");
const Helper = rewire( "../../helper");
const instrumentation = require('../../helpers/instrumentation');
const { Events, Client: EdsClient, DWH} = require('browserstack-dwh');
const { isUndefined } = require('../../typeSanity');

describe('helper', function(){
  describe('sendNonZeroStatusCountToZombie', function(){
    beforeEach(function() {
      sinon.stub(origHelper, 'PingZombie');
    });

    afterEach(function() {
      origHelper.PingZombie.restore();
    });

    it('should not send any data to zombie if keyObject belongs to automate', function(){
      var keyObject = helper.getKeyObject();
      origHelper.sendNonZeroStatusCountToZombie(keyObject);
      origHelper.PingZombie.called.should.be.false;
    });

    it('should not send data to zombie if keyObject belongs to  app automate but it does not contains non empty required values', function(){
      var keyObject = helper.getAppAutomateKeyObject();
      keyObject['nonZeroStatusesCount'] = {};
      origHelper.sendNonZeroStatusCountToZombie(keyObject);
      origHelper.PingZombie.called.should.be.false;
    });

    it('should send data to zombie if keyObject belongs to  app automate and it contains non empty required values', function(){
      var keyObject = helper.getAppAutomateKeyObject();
      keyObject['nonZeroStatusesCount'] = {"Unknown Error": 5};
      origHelper.sendNonZeroStatusCountToZombie(keyObject);
      origHelper.PingZombie.called.should.be.true;
    });
  });

  describe('shouldCheckJarTime', function(){
    beforeEach(function() {
      constants.execution_time_registry = {
        'abcd-efgh-ijkl-mnop-qrst-uvwx-yz' : {
           'jarTime' : 0,
        }
      }
    });
    afterEach(function() {
      let sessionId = helper.getKeyObject().rails_session_id;
      delete constants.execution_time_registry[sessionId];
      delete constants.global_registry[sessionId];
    });

    it('should return false if the responseData is undefined', function(){
      var rails_session_id = helper.getKeyObject().rails_session_id;
      const responseData = undefined;
      assert(origHelper.shouldCheckJarTime(rails_session_id, responseData) == false);
    });

    it('should return false if the rails_session_id is undefined', function(){
      var rails_session_id = undefined;
      const responseData = {
      };
      assert(origHelper.shouldCheckJarTime(rails_session_id, responseData) == false);
    });

    it('should return false if the response object doesn\'t have currJarTime field required to derive jar time', function(){
      var rails_session_id = helper.getKeyObject().rails_session_id;
      const responseData = {
      };
      assert(origHelper.shouldCheckJarTime(rails_session_id, responseData) == false);
    });

    it('should return true if the response object has currJarTime field required to derive jar time', function(){
      var rails_session_id = helper.getKeyObject().rails_session_id;
      const responseData = {
        currJarTime: 123
      };
      assert(origHelper.shouldCheckJarTime(rails_session_id, responseData) == true);
    });
  });

  describe('addToJarTime', function(){
    beforeEach(function() {
      constants.execution_time_registry = {
        'abcd-efgh-ijkl-mnop-qrst-uvwx-yz' : {
           'jarTime' : 0,
        }
      }
    });

    afterEach(function() {
      let sessionId = helper.getKeyObject().rails_session_id;
      delete constants.execution_time_registry[sessionId];
      delete constants.global_registry[sessionId];
    });

    it('should increment jarTime if the responseData has currJarTime mentioned', function(){
      var rails_session_id = helper.getKeyObject().rails_session_id;
      const responseData = { currJarTime: 123 };
      origHelper.addToJarTime(rails_session_id, responseData);
      assert(constants.execution_time_registry[rails_session_id]["jarTime"] == 123);
    });

    it('should not update jarTime if the responseData is undefined', function(){
      var rails_session_id = helper.getKeyObject().rails_session_id;
      const responseData = {};
      origHelper.addToJarTime(rails_session_id, responseData);
      assert(constants.execution_time_registry[rails_session_id]["jarTime"] == 0);
    });
  });

  describe('#sendToEDS', function() {
    it('sends data to eds', function(){
      origHelper.sendToEDS({"hello":"world"});
    });

    it('remove kafkaZombieKeys keys exists', function(){
      const originalInstrumentationMechanismFlag = constants.instrumentationMechanismFlag;
      const originalCONSUMER_TOPIC = process.env.CONSUMER_TOPIC;
      constants.instrumentationMechanismFlag = 0;
      delete process.env.CONSUMER_TOPIC;

      origHelper.sendToEDS({"inside_bs_network_time":"test"});
      constants.instrumentationMechanismFlag = originalInstrumentationMechanismFlag;
    });

    it('remove kafkaZombieKeys keys does not exists', function(){
      const originalInstrumentationMechanismFlag = constants.instrumentationMechanismFlag;
      const originalCONSUMER_TOPIC = process.env.CONSUMER_TOPIC;
      constants.instrumentationMechanismFlag = 0;
      delete process.env.CONSUMER_TOPIC;

      origHelper.sendToEDS({"kind":"test", "sessionid":"test"});
      constants.instrumentationMechanismFlag = originalInstrumentationMechanismFlag;
    });

    it('remove kafkaZombieKeys keys does not exists 2', function(){
      const originalInstrumentationMechanismFlag = constants.instrumentationMechanismFlag;
      const originalCONSUMER_TOPIC = process.env.CONSUMER_TOPIC;
      constants.instrumentationMechanismFlag = 0;
      delete process.env.CONSUMER_TOPIC;

      origHelper.sendToEDS({"kind":"test", "session_id":"test"});
      constants.instrumentationMechanismFlag = originalInstrumentationMechanismFlag;
    });

    it('remove kafkaZombieKeys keys does not exists 3', function(){
      const originalInstrumentationMechanismFlag = constants.instrumentationMechanismFlag;
      const originalCONSUMER_TOPIC = process.env.CONSUMER_TOPIC;
      constants.instrumentationMechanismFlag = 0;
      delete process.env.CONSUMER_TOPIC;

      origHelper.sendToEDS({"kind":"test", "hashed_id":"test"});
      constants.instrumentationMechanismFlag = originalInstrumentationMechanismFlag;
    });

    it('playwright_session_stats test', function(){
      origHelper.sendToEDS({"kind":"playwright_session_stats", "sessionid":"test"});
    });
  });

  describe('#getPortForDesktopOS', function() {
    it('gets port for windows os', () => {
      const keyObject = helper.getKeyObject();
      keyObject['os'] = "windows 7";
      assert(origHelper.getPortForDesktopOS(keyObject) == '4567');
    });
    it('gets port for non windows os', () => {
      const keyObject = helper.getKeyObject();
      keyObject['os'] = "high sierra";
      assert(origHelper.getPortForDesktopOS(keyObject) == '45671');
    });
  });

  describe('#getPortForOS', function(){
    it('gets port for real mobile', () => {
      const keyObject = helper.getKeyObject();
      keyObject['os'] = "ios";

    });
  });

  describe('#isLocalhostDomainOrIP', function() {
    it('checks if url is local', function() {
      assert(origHelper.isLocalhostDomainOrIP("127.0.0.1"));
      assert(origHelper.isLocalhostDomainOrIP("localhost"));
      assert(!origHelper.isLocalhostDomainOrIP("***********"));
      assert(!origHelper.isLocalhostDomainOrIP(undefined));
      assert(!origHelper.isLocalhostDomainOrIP(null));
    })
  })

  describe('#PingZombie', function(){
    it('sends to zombie', function() {
      origHelper.PingZombie({"hello":"world"}, (error, bytes) => {
        assert(bytes);
      });
    });
  })

  describe('#getUDPSocket', function () {
    it('creates a udpSocket if undefined or returns it', () => {
      var udpSocket = origHelper.getUDPSocket();
      assert(udpSocket);
      var udpSocket2 = origHelper.getUDPSocket();
      assert(udpSocket === udpSocket2);
    });
  });

  describe('#pushBlockExecutionTime', () => {
    beforeEach(() => {
      sinon.stub(origHelper, 'sendToEDS');
    });
    afterEach(() => {
      origHelper.sendToEDS.restore();
    });
    it('should push execution time to eds', () => {
      origHelper.pushBlockExecutionTime('1234', 'start', {});
      origHelper.sendToEDS.called.should.be.true;
    });
  });

  describe('updateHeadersForSelenium4Jars', function(){
    it('should not update host if seleniumVersion is < 4', function(){
      headers = {"host": "random_host"};
      returned_headers = origHelper.updateHeadersForSelenium4Jars({selenium_version: "3.0.0"}, headers);
      returned_headers.host.should.be.equal("random_host");
    });

    it('should update host if seleniumVersion is > 4', function(){
      headers = {"host": "random_host"};
      returned_headers = origHelper.updateHeadersForSelenium4Jars({selenium_version: "4.1.0"}, headers);
      returned_headers.host.should.not.be.equal("random_host");
      returned_headers.host.should.be.equal("localhost");
    });
  });

  describe('#sendToEDS', function() {
    it('sends data to eds', function(){
      origHelper.sendToEDS({"hello":"world"});
    });
  });

  describe('#getPortForDesktopOS', function() {
    it('gets port for windows os', () => {
      const keyObject = helper.getKeyObject();
      keyObject['os'] = "windows 7";
      assert(origHelper.getPortForDesktopOS(keyObject) == '4567');
    });
    it('gets port for non windows os', () => {
      const keyObject = helper.getKeyObject();
      keyObject['os'] = "high sierra";
      assert(origHelper.getPortForDesktopOS(keyObject) == '45671');
    });
  });

  describe('#getPortForOS', function(){
    it('gets port for real mobile', () => {
      const keyObject = helper.getKeyObject();
      keyObject['os'] = "ios";

    });
  });

  describe('#PingZombie', function(){
    it('sends to zombie', function() {
      origHelper.PingZombie({"hello":"world"}, (error, bytes) => {
        assert(bytes);
      });
    });
  })

  describe('isW3C', function() {
    it('returns true if the session is w3c', function() {
      assert(origHelper.isW3C({dialect: 'W3C'}));
    });
    it('returns false if the session is w3c', function() {
      assert(!origHelper.isW3C({}));
    });
  });

  describe('selenium command performance', function() {
    describe('extractSeleniumCommandFromUrl', function() {
      describe('w3c', function() {
        it('should extract selenium command from request url', function() {
          let command = origHelper.extractSeleniumCommandFromUrl('1234', '/wd/hub/session/1234/element', true);
          assert(command == "element");
        });

        it('should extract element selenium command and sub_command from request url', function() {
          let command = origHelper.extractSeleniumCommandFromUrl('1234', '/wd/hub/session/1234/element/12345/value', true);
          assert(command == "element_value");
        });

        it('should extract execute selenium command and sub_command (sync) from request url if w3c', function() {
          let command = origHelper.extractSeleniumCommandFromUrl('1234', '/wd/hub/session/1234/execute/sync', true);
          assert(command == "execute_sync");
        });

        it('should extract alert selenium command and its sub command text', function() {
          let command = origHelper.extractSeleniumCommandFromUrl('1234', '/wd/hub/session/1234/alert/text', true);
          assert(command == "alert_text");
        });

        it('should extract alert selenium command and its sub command accept', function() {
          let command = origHelper.extractSeleniumCommandFromUrl('1234', '/wd/hub/session/1234/alert/accept', true);
          assert(command == "alert_accept");
        });

        it('should extract window selenium command and its sub command rect', function() {
          let command = origHelper.extractSeleniumCommandFromUrl('1234', '/wd/hub/session/1234/window/rect', true);
          assert(command == "window_rect");
        });

        it('should extract window selenium command and its sub command maximize', function() {
          let command = origHelper.extractSeleniumCommandFromUrl('1234', '/wd/hub/session/1234/window/maximize', true);
          assert(command == "window_maximize");
        });

        it('should extract selenium command from request url', function() {
          let command = origHelper.extractSeleniumCommandFromUrl('1234', '/wd/hub/session/1234', true);
          assert(command == "");
        });

        it('should extract selenium command from request url', function() {
          let command = origHelper.extractSeleniumCommandFromUrl('1234', '/wd/hub/session/1234/element/id', true);
          assert(command == "element");
        });

        it('should throw error if invalid command is passed', function() {
          assert.throws(() => {
            origHelper.extractSeleniumCommandFromUrl('1234', '/wd/1234/element/id', true);
          });
        });
      })
      describe('jwp', function() {
        it('should extract selenium command from request url', function() {
          let command = origHelper.extractSeleniumCommandFromUrl('1234', '/wd/hub/session/1234/element', false);
          assert(command == "element");
        });

        it('should extract element selenium command and sub_command from request url', function() {
          let command = origHelper.extractSeleniumCommandFromUrl('1234', '/wd/hub/session/1234/element/12345/value', false);
          assert(command == "element_value");
        });

        it('should extract execute selenium command for non-w3c', function() {
          let command = origHelper.extractSeleniumCommandFromUrl('1234', '/wd/hub/session/1234/execute/', false);
          assert(command == "execute");
        });

        it('should extract alert selenium command and its sub command text', function() {
          let command = origHelper.extractSeleniumCommandFromUrl('1234', '/wd/hub/session/1234/alert_text', false);
          assert(command == "alert_text");
        });

        it('should extract alert selenium command and its sub command accept', function() {
          let command = origHelper.extractSeleniumCommandFromUrl('1234', '/wd/hub/session/1234/alert_accept', false);
          assert(command == "alert_accept");
        });


        it('should extract window selenium command and its sub command maximize', function() {
          let command = origHelper.extractSeleniumCommandFromUrl('1234', '/wd/hub/session/1234/window/abcde/maximize', false);
          assert(command == "window_maximize");
        });

        it('should extract selenium command from request url', function() {
          let command = origHelper.extractSeleniumCommandFromUrl('1234', '/wd/hub/session/1234', false);
          assert(command == "");
        });

        it('should extract selenium command from request url', function() {
          let command = origHelper.extractSeleniumCommandFromUrl('1234', '/wd/hub/session/1234/element/id', false);
          assert(command == "element");
        });

        it('should throw error if invalid command is passed', function() {
          assert.throws(() => {
            origHelper.extractSeleniumCommandFromUrl('1234', '/wd/1234/element/id', false);
          });
        });
      })

    })

    describe('stopSeleniumClock', function() {
      beforeEach(function() {
        constants.execution_time_registry = {
          'abcd-efgh-ijkl-mnop-qrst-uvwx-yz' : {
            "requestMethod": 'GET',
             "seleniumCommand" : "element",
             "seleniumCommandStartTime" : 1605591259450,
             'terminalType' : 'mobile',
             'jarTime' : 0,
             'nginxOutTime' : 0,
             userToNginxTime: 0
          }
        }
        constants.global_registry = {
          'abcd-efgh-ijkl-mnop-qrst-uvwx-yz' : helper.getKeyObject()
        }
      });

      afterEach(function() {
        let sessionId = helper.getKeyObject().rails_session_id;
        delete constants.execution_time_registry[sessionId];
        delete constants.global_registry[sessionId];
        HubLogger.uploadLogPartToKafka.restore();

      });

      it('should upload performance log part to kafka if the session data exists in execution_time_registry without w3c', function() {
        const uploadLogPartToKafka = sinon.stub(HubLogger, 'uploadLogPartToKafka');
        let sessionId = helper.getKeyObject().rails_session_id;

        origHelper.stopSeleniumClock(sessionId, `/wd/hub/session/${sessionId}/element`, 'GET', false, 0, {"data": "randomdata"}, 1605591260450);
        assert(uploadLogPartToKafka.calledOnce);
      });

      it('should ignore stopping execution time if the command doesnt match in execution_time_registry without w3c', function() {
        const uploadLogPartToKafka = sinon.stub(HubLogger, 'uploadLogPartToKafka');
        let sessionId = helper.getKeyObject().rails_session_id;

        origHelper.stopSeleniumClock(sessionId, `/wd/hub/session/${sessionId}/log`, 'GET', false, 0, {"data": "randomdata"}, 1605591260450,);
        assert(uploadLogPartToKafka.calledOnce === false);
      });

      it('should ignore stopping execution time if the request method doesnt match in execution_time_registry without w3c', function() {
        const uploadLogPartToKafka = sinon.stub(HubLogger, 'uploadLogPartToKafka');
        let sessionId = helper.getKeyObject().rails_session_id;

        origHelper.stopSeleniumClock(sessionId, `/wd/hub/session/${sessionId}/element`, 'POST', 13, {"data": "randomdata"}, false, 1605591260450);
        assert(uploadLogPartToKafka.calledOnce === false);
      });

      it('should ignore stopping execution time if no such session in execution_time_registry without w3c', function() {
        const uploadLogPartToKafka = sinon.stub(HubLogger, 'uploadLogPartToKafka');
        let sessionId = "1234"

        origHelper.stopSeleniumClock(sessionId, `/wd/hub/session/${sessionId}/element`, 'POST', false, 13, {"data": "randomdata"}, 1605591260450);
        assert(uploadLogPartToKafka.calledOnce === false);
      });

      it('should ignore uploading to kafka if no such session in global_registry without w3c', function() {
        const uploadLogPartToKafka = sinon.stub(HubLogger, 'uploadLogPartToKafka');
        let sessionId = helper.getKeyObject().rails_session_id;
        delete constants.global_registry[sessionId];
        origHelper.stopSeleniumClock(sessionId, `/wd/hub/session/${sessionId}/element`, 'POST', false, 13, {"data": "randomdata"}, 1605591260450);
        assert(uploadLogPartToKafka.calledOnce === false);
      });

      it('should upload performance log part to kafka if the session data exists in execution_time_registry with w3c', function() {
        const uploadLogPartToKafka = sinon.stub(HubLogger, 'uploadLogPartToKafka');
        let sessionId = helper.getKeyObject().rails_session_id;

        origHelper.stopSeleniumClock(sessionId, `/wd/hub/session/${sessionId}/element`, 'GET', true, 13, {"error": "NoSuchElementError"}, 1605591260450);
        assert(uploadLogPartToKafka.calledOnce);
      });

      it('should ignore stopping execution time if the command doesnt match in execution_time_registry with w3c', function() {
        const uploadLogPartToKafka = sinon.stub(HubLogger, 'uploadLogPartToKafka');
        let sessionId = helper.getKeyObject().rails_session_id;

        origHelper.stopSeleniumClock(sessionId, `/wd/hub/session/${sessionId}/log`, 'GET', true, 0, {}, 1605591260450,);
        assert(uploadLogPartToKafka.calledOnce === false);
      });

      it('should ignore stopping execution time if the request method doesnt match in execution_time_registry with w3c', function() {
        const uploadLogPartToKafka = sinon.stub(HubLogger, 'uploadLogPartToKafka');
        let sessionId = helper.getKeyObject().rails_session_id;

        origHelper.stopSeleniumClock(sessionId, `/wd/hub/session/${sessionId}/element`, 'POST', true, 0, {}, 1605591260450);
        assert(uploadLogPartToKafka.calledOnce === false);
      });

      it('should ignore stopping execution time if no such session in execution_time_registry with w3c', function() {
        const uploadLogPartToKafka = sinon.stub(HubLogger, 'uploadLogPartToKafka');
        let sessionId = "1234"

        origHelper.stopSeleniumClock(sessionId, `/wd/hub/session/${sessionId}/element`, 'POST', true, 0, {}, 1605591260450);
        assert(uploadLogPartToKafka.calledOnce === false);
      });

      it('should ignore uploading to kafka if no such session in global_registry with w3c', function() {
        const uploadLogPartToKafka = sinon.stub(HubLogger, 'uploadLogPartToKafka');
        let sessionId = helper.getKeyObject().rails_session_id;
        delete constants.global_registry[sessionId];
        origHelper.stopSeleniumClock(sessionId, `/wd/hub/session/${sessionId}/element`, 'POST', true, 0, {}, 1605591260450);
        assert(uploadLogPartToKafka.calledOnce === false);
      });

      it('should instrument element not found to registry when status is 13', function() {
        const uploadLogPartToKafka = sinon.stub(HubLogger, 'uploadLogPartToKafka');
        constants.execution_time_registry['abcd-efgh-ijkl-mnop-qrst-uvwx-yz']['requestMethod'] = 'POST'
        let sessionId = helper.getKeyObject().rails_session_id;

        origHelper.stopSeleniumClock(sessionId, `/wd/hub/session/${sessionId}/element`, 'POST', true, 13, {"error": "NoSuchElementError"}, 1605591260450);
        assert(constants.global_registry[sessionId].elementNotFound === constants.ELEMENT_NOT_FOUND_DETECTED);
        assert(uploadLogPartToKafka.calledOnce);
      });

      it('should instrument element not found to registry when status is 400', function() {
        const uploadLogPartToKafka = sinon.stub(HubLogger, 'uploadLogPartToKafka');
        constants.execution_time_registry['abcd-efgh-ijkl-mnop-qrst-uvwx-yz']['requestMethod'] = 'POST'
        let sessionId = helper.getKeyObject().rails_session_id;

        origHelper.stopSeleniumClock(sessionId, `/wd/hub/session/${sessionId}/element`, 'POST', true, 400, {"error": "NoSuchElementError"}, 1605591260450);
        assert(constants.global_registry[sessionId].elementNotFound === constants.ELEMENT_NOT_FOUND_DETECTED);
        assert(uploadLogPartToKafka.calledOnce);
      });

    });

    describe('startSeleniumCommandClock', function() {
      afterEach(function() {
        let sessionId = helper.getKeyObject().rails_session_id;
        delete constants.execution_time_registry[sessionId];
        const sessionId2 = 'ABCIJKLMN3abcxyz1234fasdabshc12345hABCDagh6';
        delete constants.execution_time_registry[sessionId2];
        delete constants.global_registry[sessionId];
      });

      it('should set selenium command data to execution time registry with selenium command without w3c', function(){
          const startTime = Date.now();
          const sessionId = 'ABCIJKLMN3abcxyz1234fasdabshc12345hABCDagh6'
          const terminalTypes = ['desktop','mobile'];
          for(let terminalType in terminalTypes){
            origHelper.startSeleniumCommandClock(sessionId, `/wd/hub/session/${sessionId}/element`, 'GET', 1, false, false, 2, terminalType, startTime, startTime, 0, 0);
            expectedDictionary = {
                                  isAppAutomate: false,
                                  seleniumCommandIndex: 1,
                                  requestMethod: 'GET',
                                  seleniumCommand: 'element',
                                  seleniumCommandStartTime: startTime,
                                  terminalType: terminalType,
                                  nginxOutTime: startTime,
                                  jarTime: 0,
                                  userToNginxTime: 0
                                }
            assert.deepEqual(constants.execution_time_registry[sessionId], expectedDictionary);
          }
      });

      it('should set selenium command data to execution time registry with empty command without w3c', function(){
        const startTime = Date.now();
        const sessionId = 'ABCIJKLMN3abcxyz1234fasdabshc12345hABCDagh6'
        const terminalTypes = ['desktop','mobile'];
        for(let terminalType in terminalTypes){
          origHelper.startSeleniumCommandClock(sessionId, `/wd/hub/session/${sessionId}`, 'GET', 1, false, false, 2, terminalType, startTime, startTime, 0, 0);
          expectedDictionary = {
                                isAppAutomate: false,
                                seleniumCommandIndex: 1,
                                requestMethod: 'GET',
                                seleniumCommand: '',
                                seleniumCommandStartTime: startTime,
                                terminalType: terminalType,
                                nginxOutTime: startTime,
                                jarTime: 0,
                                userToNginxTime: 0
                              }
          assert.deepEqual(constants.execution_time_registry[sessionId], expectedDictionary);
        }
      });

      it('should set selenium command data to execution time registry with command suffix without w3c', function(){
        const startTime = Date.now();
        const sessionId = 'ABCIJKLMN3abcxyz1234fasdabshc12345hABCDagh6'
        const terminalTypes = ['desktop','mobile'];
        for(let terminalType in terminalTypes){
          origHelper.startSeleniumCommandClock(sessionId, `/wd/hub/session/${sessionId}/element/abcd`, 'GET', 1, false, false, 2, terminalType, startTime, startTime, 0, 0);
          expectedDictionary = {
                                isAppAutomate: false,
                                seleniumCommandIndex: 1,
                                requestMethod: 'GET',
                                seleniumCommand: 'element',
                                seleniumCommandStartTime: startTime,
                                terminalType: terminalType,
                                nginxOutTime: startTime,
                                jarTime: 0,
                                userToNginxTime: 0
                              }
          assert.deepEqual(constants.execution_time_registry[sessionId], expectedDictionary);
        }
      });

      it('should ignore if the command count is greater than max_command_count without w3c', function(){
        const startTime = Date.now();
        const sessionId = 'ABCIJKLMN3abcxyz1234fasdabshc12345hABCDagh6';
        const terminalTypes = ['desktop','mobile'];
        for(let terminalType in terminalTypes){
          origHelper.startSeleniumCommandClock(sessionId, `/wd/hub/session/${sessionId}/element/abcd`, 'GET', constants.MAX_SELENIUM_COMMANDS_INSTRUMENTATION + 1, false, false, 2, terminalType, startTime, startTime, 0, 0);
          assert(typeof(constants.execution_time_registry['1234']) === 'undefined');
        }
      });

      it('should set selenium command data to execution time registry with selenium command without w3c', function(){
        const startTime = Date.now();
        const sessionId = 'ABCIJKLMN3abcxyz1234fasdabshc12345hABCDagh6'
        const terminalTypes = ['desktop','mobile'];
        for(let terminalType in terminalTypes){
          origHelper.startSeleniumCommandClock(sessionId, `/wd/hub/session/${sessionId}/element`, 'GET', 1, false, false, 2, terminalType, startTime, startTime, 0, 0);
          expectedDictionary = {
                                isAppAutomate: false,
                                seleniumCommandIndex: 1,
                                requestMethod: 'GET',
                                seleniumCommand: 'element',
                                seleniumCommandStartTime: startTime,
                                terminalType: terminalType,
                                nginxOutTime: startTime,
                                jarTime: 0,
                                userToNginxTime: 0
                              }
          assert.deepEqual(constants.execution_time_registry[sessionId], expectedDictionary);
        }
      });

      it('should set selenium command data to execution time registry with empty command with w3c', function(){
        const startTime = Date.now();
        const sessionId = 'ABCIJKLMN3abcxyz1234fasdabshc12345hABCDagh6'
        const terminalTypes = ['desktop','mobile'];
        for(let terminalType in terminalTypes){
          origHelper.startSeleniumCommandClock(sessionId, `/wd/hub/session/${sessionId}`, 'GET', 1, false, true, 2, terminalType, startTime, startTime, 0, 0);
          expectedDictionary = {
                                isAppAutomate: false,
                                seleniumCommandIndex: 1,
                                requestMethod: 'GET',
                                seleniumCommand: '',
                                seleniumCommandStartTime: startTime,
                                terminalType: terminalType,
                                nginxOutTime: startTime,
                                jarTime: 0,
                                userToNginxTime: 0
                              }
          assert.deepEqual(constants.execution_time_registry[sessionId], expectedDictionary);
        }
      });

      it('should set selenium command data to execution time registry with command suffix with w3c', function(){
        const startTime = Date.now();
        const sessionId = 'ABCIJKLMN3abcxyz1234fasdabshc12345hABCDagh6'
        const terminalTypes = ['desktop','mobile'];
        for(let terminalType in terminalTypes){
          origHelper.startSeleniumCommandClock(sessionId, `/wd/hub/session/${sessionId}/element/abcd`, 'GET', 1, false, true, 2, terminalType, startTime, startTime, 0, 0);
          expectedDictionary = {
                                isAppAutomate: false,
                                seleniumCommandIndex: 1,
                                requestMethod: 'GET',
                                seleniumCommand: 'element',
                                seleniumCommandStartTime: startTime,
                                terminalType: terminalType,
                                nginxOutTime: startTime,
                                jarTime: 0,
                                userToNginxTime: 0
                              }
          assert.deepEqual(constants.execution_time_registry[sessionId], expectedDictionary);
        }
      });

      it('should ignore if the command count is greater than max_command_count with w3c', function(){
        const startTime = Date.now();
        const sessionId = 'ABCIJKLMN3abcxyz1234fasdabshc12345hABCDagh6'
        const terminalTypes = ['desktop','mobile'];
        for(let terminalType in terminalTypes){
          origHelper.startSeleniumCommandClock(sessionId, `/wd/hub/session/${sessionId}/element/abcd`, 'GET', constants.MAX_SELENIUM_COMMANDS_INSTRUMENTATION + 1, false, true, 2, terminalType, startTime, startTime, 0, 0);
          assert(typeof(constants.execution_time_registry[sessionId]) === 'undefined');
        }
      });
    });

    describe('addToHubProcessingRegistry', function() {
      beforeEach(function() {
        constants.global_registry = {
          'abcd-efgh-ijkl-mnop-qrst-uvwx-yz' : helper.getKeyObject({hubProcessingTime: 0, nginxToHubTime : 0, userToNginxTime: 0, jarTime : 0})
        };
      });

      afterEach(function() {
        let sessionId = helper.getKeyObject().rails_session_id;
        delete constants.global_registry[sessionId];
      });

      it('should update global registry for hubProcessingTime, jarTime and nginxToHubTime', function() {
        let sessionId = helper.getKeyObject().rails_session_id;

        const hubProcessingTimeBefore = constants.global_registry[sessionId].hubProcessingTime;
        const nginxToHubTimeBefore = constants.global_registry[sessionId].nginxToHubTime;
        const jarTimeBefore = constants.global_registry[sessionId].jarTime;

        origHelper.addToHubProcessingRegistry('GET',sessionId,1000,1000,1000);
        assert(constants.global_registry[sessionId].hubProcessingTime - hubProcessingTimeBefore == 1000);
        assert(constants.global_registry[sessionId].nginxToHubTime - nginxToHubTimeBefore == 1000);
        assert(constants.global_registry[sessionId].jarTime - jarTimeBefore == 1000);
      });

      it('should update hubProcessingRegistry', function() {
        let sessionId = helper.getKeyObject().rails_session_id;

        const hubRequestCountBefore = constants.hubProcessingRegistry.hubRequestCount;
        const hubProcessingTimeBefore = constants.hubProcessingRegistry.hubProcessingTime;
        const nginxToHubTimeBefore = constants.hubProcessingRegistry.nginxToHubTime;
        const jarTimeBefore = constants.hubProcessingRegistry.jarTime;

        origHelper.addToHubProcessingRegistry('GET',sessionId,1000,1000,1000);

        assert(constants.hubProcessingRegistry.hubRequestCount - hubRequestCountBefore == 1);
        assert(constants.hubProcessingRegistry.hubProcessingTime - hubProcessingTimeBefore == 1000);
        assert(constants.hubProcessingRegistry.nginxToHubTime - nginxToHubTimeBefore == 1000);
        assert(constants.hubProcessingRegistry.jarTime - jarTimeBefore == 1000);
      });
    });

    describe('resetHubProcessingRegistry', function() {
      beforeEach(function() {
        constants.hubProcessingRegistry = {
          'hubRequestCount': 100,
          'hubProcessingTime': 100,
          'nginxToHubTime': 100,
          'userToNginxTime': 100,
          'jarTime': 100
        };
      });

      afterEach(function() {
        origHelper.resetHubProcessingRegistry();
      });

      it('should reset hubProcessingRegistry', function() {
        const currentHubProcessingRegistry = origHelper.resetHubProcessingRegistry();
        assert.notEqual(currentHubProcessingRegistry,constants.hubProcessingRegistry);
      });

      it('should reset hubProcessingRegistry to default values', function() {
        origHelper.resetHubProcessingRegistry();
        console.log(constants.hubProcessingRegistry);
        assert.equal(constants.hubProcessingRegistry['hubProcessingTime'] ,0);
        assert.equal(constants.hubProcessingRegistry['nginxToHubTime'],0);
      });
    });

    describe('extractSeleniumMainCommandFromUrl', function() {
      it('extracts the parent command from the url without subCommand', function() {
        assert.equal(origHelper.extractSeleniumMainCommandFromUrl('1234', `/wd/hub/session/1234/element`), 'element');
      });

      it('extracts the parent command from the url with subCommand', function() {
        assert.equal(origHelper.extractSeleniumMainCommandFromUrl('1234', `/wd/hub/session/1234/element/abcd/text`), 'element');
      });
    });

    describe('extractSeleniumSubCommandFromUrl', function () {
      describe('w3c', function() {
        describe('element', function() {
          it('returns selenium subCommand from the url', function() {
            var response = origHelper.extractSeleniumSubCommandFromUrl('1234', '/wd/hub/session/1234/element/abcde/selected', true);
            assert.equal(response, 'selected');
          });

          it('returns selenium subCommand from the url', function() {
            var response = origHelper.extractSeleniumSubCommandFromUrl('1234', '/wd/hub/session/1234/element/active', true);
            assert.equal(response, 'active');
          });

          it('returns empty string if no subcommand', function() {
            var response = origHelper.extractSeleniumSubCommandFromUrl('1234', '/wd/hub/session/1234/element', true);
            assert.equal(response, '');
          });
        });

        describe('execute', function() {
          it('returns selenium subCommand from the url', function() {
            var response = origHelper.extractSeleniumSubCommandFromUrl('1234', '/wd/hub/session/1234/execute/sync', true);
            assert.equal(response, 'sync');
          });

          it('returns empty string if no subcommand', function() {
            var response = origHelper.extractSeleniumSubCommandFromUrl('1234', '/wd/hub/session/1234/execute', true);
            assert.equal(response, '');
          });
        });

        describe('alert', function() {
          it('returns selenium subCommand from the url', function() {
            var response = origHelper.extractSeleniumSubCommandFromUrl('1234', '/wd/hub/session/1234/alert/text', true);
            assert.equal(response, 'text');
          });

          it('returns empty string if no subcommand', function() {
            var response = origHelper.extractSeleniumSubCommandFromUrl('1234', '/wd/hub/session/1234/alert', true);
            assert.equal(response, '');
          });
        });

        describe('window', function() {
          it('returns selenium subCommand from the url', function() {
            var response = origHelper.extractSeleniumSubCommandFromUrl('1234', '/wd/hub/session/1234/window/maximize', true);
            assert.equal(response, 'maximize');
          });

          it('returns empty string if no subcommand', function() {
            var response = origHelper.extractSeleniumSubCommandFromUrl('1234', '/wd/hub/session/1234/window', true);
            assert.equal(response, '');
          });
        });

        describe('appium', function() {
          it('returns correct selenium subCommand from the url', function() {
            var response = origHelper.extractSeleniumSubCommandFromUrl('1234', '/wd/hub/session/1234/appium/device/installapp', false);
            assert.strictEqual(response, 'installapp');
          });

          it('returns correct selenium subCommand from the url', function() {
            var response = origHelper.extractSeleniumSubCommandFromUrl('1234', '/wd/hub/session/1234/appium/random', false);
            assert.strictEqual(response, 'random');
          });

          it('returns empty string if no subcommand', function() {
            var response = origHelper.extractSeleniumSubCommandFromUrl('1234', '/wd/hub/session/1234/appium', false);
            assert.strictEqual(response, '');
          });
        });

        describe('appium w3c', function() {
          it('returns correct selenium subCommand from the url', function() {
            var response = origHelper.extractSeleniumSubCommandFromUrl('1234', '/wd/hub/session/1234/appium/device/installapp', true);
            assert.strictEqual(response, 'installapp');
          });

          it('returns correct selenium subCommand from the url', function() {
            var response = origHelper.extractSeleniumSubCommandFromUrl('1234', '/wd/hub/session/1234/appium/random', true);
            assert.strictEqual(response, 'random');
          });

          it('returns empty string if no subcommand', function() {
            var response = origHelper.extractSeleniumSubCommandFromUrl('1234', '/wd/hub/session/1234/appium', true);
            assert.strictEqual(response, '');
          });
        });
      });

      describe('jwp', function() {
        describe('window', function() {
          it('returns selenium subCommand from the url', function() {
            var response = origHelper.extractSeleniumSubCommandFromUrl('1234', '/wd/hub/session/1234/window/abcde/size', false);
            assert.equal(response, 'size');
          });

          it('returns empty string if no subcommand', function() {
            var response = origHelper.extractSeleniumSubCommandFromUrl('1234', '/wd/hub/session/1234/window', false);
            assert.equal(response, '');
          });
        });

        describe('element', function() {
          it('returns selenium subCommand from the url', function() {
            var response = origHelper.extractSeleniumSubCommandFromUrl('1234', '/wd/hub/session/1234/element/abcde/size', false);
            assert.equal(response, 'size');
          });

          it('returns selenium subCommand from the url', function() {
            var response = origHelper.extractSeleniumSubCommandFromUrl('1234', '/wd/hub/session/1234/element/active', false);
            assert.equal(response, 'active');
          });

          it('returns empty string if no subcommand', function() {
            var response = origHelper.extractSeleniumSubCommandFromUrl('1234', '/wd/hub/session/1234/element', false);
            assert.equal(response, '');
          });
        });

        describe('ime', function() {
          it('returns selenium subCommand from the url', function() {
            var response = origHelper.extractSeleniumSubCommandFromUrl('1234', '/wd/hub/session/1234/ime/available_engines', false);
            assert.equal(response, 'available_engines');
          });

          it('returns empty string if no subcommand', function() {
            var response = origHelper.extractSeleniumSubCommandFromUrl('1234', '/wd/hub/session/1234/ime', false);
            assert.equal(response, '');
          });
        });

        describe('frame', function() {
          it('returns selenium subCommand from the url', function() {
            var response = origHelper.extractSeleniumSubCommandFromUrl('1234', '/wd/hub/session/1234/frame/parent', false);
            assert.equal(response, 'parent');
          });

          it('returns empty string if no subcommand', function() {
            var response = origHelper.extractSeleniumSubCommandFromUrl('1234', '/wd/hub/session/1234/frame', false);
            assert.equal(response, '');
          });
        });

        describe('touch', function() {
          it('returns selenium subCommand from the url', function() {
            var response = origHelper.extractSeleniumSubCommandFromUrl('1234', '/wd/hub/session/1234/touch/move', false);
            assert.equal(response, 'move');
          });

          it('returns empty string if no subcommand', function() {
            var response = origHelper.extractSeleniumSubCommandFromUrl('1234', '/wd/hub/session/1234/touch', false);
            assert.equal(response, '');
          });
        });

        describe('local_storage', function() {
          it('returns selenium subCommand from the url', function() {
            var response = origHelper.extractSeleniumSubCommandFromUrl('1234', '/wd/hub/session/1234/local_storage/key', false);
            assert.equal(response, 'key');
          });

          it('returns empty string if no subcommand', function() {
            var response = origHelper.extractSeleniumSubCommandFromUrl('1234', '/wd/hub/session/1234/local_storage', false);
            assert.equal(response, '');
          });
        });

        describe('session_storage', function() {
          it('returns selenium subCommand from the url', function() {
            var response = origHelper.extractSeleniumSubCommandFromUrl('1234', '/wd/hub/session/1234/session_storage/key', false);
            assert.equal(response, 'key');
          });

          it('returns empty string if no subcommand', function() {
            var response = origHelper.extractSeleniumSubCommandFromUrl('1234', '/wd/hub/session/1234/session_storage', false);
            assert.equal(response, '');
          });
        });

        describe('log', function() {
          it('returns selenium subCommand from the url', function() {
            var response = origHelper.extractSeleniumSubCommandFromUrl('1234', '/wd/hub/session/1234/log/types', false);
            assert.equal(response, 'types');
          });

          it('returns empty string if no subcommand', function() {
            var response = origHelper.extractSeleniumSubCommandFromUrl('1234', '/wd/hub/session/1234/log', false);
            assert.equal(response, '');
          });
        });

        describe('appium', function() {
          it('returns correct selenium subCommand from the url', function() {
            var response = origHelper.extractSeleniumSubCommandFromUrl('1234', '/wd/hub/session/1234/appium/device/installapp', false);
            assert.strictEqual(response, 'installapp');
          });

          it('returns correct selenium subCommand from the url', function() {
            var response = origHelper.extractSeleniumSubCommandFromUrl('1234', '/wd/hub/session/1234/appium/random', false);
            assert.strictEqual(response, 'random');
          });

          it('returns empty string if no subcommand', function() {
            var response = origHelper.extractSeleniumSubCommandFromUrl('1234', '/wd/hub/session/1234/appium', false);
            assert.strictEqual(response, '');
          });
        });

      });

    });

    describe('extractSeleniumCommandFromUrl', function() {
      describe('w3c', function() {
        it('generates selenium command with sub command from main url', function() {
          var response = origHelper.extractSeleniumCommandFromUrl('1234', '/wd/hub/session/1234/element/active', true);
          assert.equal(response, 'element_active');
          response = origHelper.extractSeleniumCommandFromUrl('1234', '/wd/hub/session/1234/element/abcde/selected', true);
          assert.equal(response, 'element_selected');
          response = origHelper.extractSeleniumCommandFromUrl('1234', '/wd/hub/session/1234/execute/sync', true);
          assert.equal(response, 'execute_sync');
          response = origHelper.extractSeleniumCommandFromUrl('1234', '/wd/hub/session/1234/execute', true);
          assert.equal(response, 'execute');
          response = origHelper.extractSeleniumCommandFromUrl('1234', '/wd/hub/session/1234/title', true);
          assert.equal(response, 'title');
        });
      });
      describe('jwp', function() {
        it('generates selenium command with sub command from main url', function() {
          var response = origHelper.extractSeleniumCommandFromUrl('1234', '/wd/hub/session/1234/element/active', false);
          assert.equal(response, 'element_active');
          response = origHelper.extractSeleniumCommandFromUrl('1234', '/wd/hub/session/1234/element/abcde/selected', false);
          assert.equal(response, 'element_selected');
          response = origHelper.extractSeleniumCommandFromUrl('1234', '/wd/hub/session/1234/execute', false);
          assert.equal(response, 'execute');
          response = origHelper.extractSeleniumCommandFromUrl('1234', '/wd/hub/session/1234/execute', false);
          assert.equal(response, 'execute');
          response = origHelper.extractSeleniumCommandFromUrl('1234', '/wd/hub/session/1234/title', false);
          assert.equal(response, 'title');
        });
      });
    });

    describe('extractSubUrlFromUrl', function() {
      it('extracts selenium subUrl from full url', function() {
        const response = origHelper.extractSubUrlFromUrl('12345', '/wd/hub/session/12345/element');
        assert.equal(response, 'element');
      });

      it('extracts selenium subUrl from full url', function() {
        const response = origHelper.extractSubUrlFromUrl('12345', '/wd/hub/session');
        assert.equal(response, '');
      });

      it('throws an error if invalid url', function() {
        assert.throws( () => origHelper.extractSubUrlFromUrl('12345', '/abc/12345/element'), Error, 'Invalid Selenium Command - /abc/12345/element');
      });
    });
  });

  describe('getCustomMessageForError', function() {
    it('should return undefined if keyValMessage is undefined', function(){
      var response = origHelper.getCustomMessageForError({}, undefined);
      assert.strictEqual(response, undefined);
    });

    it('should return keyValMessage as it is if it is not a string', function(){
      var keyValMessage = {"a": "b"};
      var response = origHelper.getCustomMessageForError({}, keyValMessage);
      assert.strictEqual(response, keyValMessage);
    });

    it('should return keyValMessage as it is if it does not falls in any of the categories', function(){
      var keyValMessage = "hello";
      var response = origHelper.getCustomMessageForError({"bsCaps":{}}, keyValMessage);
      assert.strictEqual(response, keyValMessage);
    });

    it('should return keyValMessage as it is if options.bsCaps[autoWebview] is not present even though keyValMessage is no context found', function(){
      var keyValMessage = "No such context found was found in app";
      var response = origHelper.getCustomMessageForError({"bsCaps":{}}, keyValMessage);
      assert.strictEqual(response, keyValMessage);
    });

    it('should return keyValMessage as it is if options.bsCaps[autoWebview] is present but is non true even though keyValMessage is no context found', function(){
      var keyValMessage = "No such context found was found in app";
      var response = origHelper.getCustomMessageForError({"bsCaps": {"autoWebview" : "false"}}, keyValMessage);
      assert.strictEqual(response, keyValMessage);
    });

    it('should append APP_AUTOMATE_CONTEXT_START_ERROR in keyValMessage and return it if option.bsCaps[autoWebview] is true and  keyValMessage is No such context found', function(){
      var keyValMessage = "No such context found was found in app";
      var response = origHelper.getCustomMessageForError({"bsCaps": {"autoWebview" : "true"}}, keyValMessage);
      assert.strictEqual(response, keyValMessage + errorMessages.APP_AUTOMATE_CONTEXT_START_ERROR);
    });

    it('should append APP_AUTOMATE_MISSING_APP_ID_KEY_ERROR in keyValMessage and return it if option.bsCaps[autoWebview] is true and  keyValMessage is Missing parameter: AppIdKey and os is >=14.5', function(){
      var keyValMessage = "Missing parameter: AppIdKey";
      var response = origHelper.getCustomMessageForError({"bsCaps": {"autoWebview" : "true", "mobile": {"version": "iPhone 12-14.6"}}}, keyValMessage);
      assert.strictEqual(response, keyValMessage + errorMessages.APP_AUTOMATE_MISSING_APP_ID_KEY_ERROR.replace("<current_device_name>", "iPhone 12-14.6"));
    });

    it('should not append APP_AUTOMATE_MISSING_APP_ID_KEY_ERROR in keyValMessage and return it if option.bsCaps[autoWebview] is true and  keyValMessage is Missing parameter: AppIdKey and os is < 14.5', function(){
      var keyValMessage = "Missing parameter: AppIdKey";
      var response = origHelper.getCustomMessageForError({"bsCaps": {"autoWebview" : "true", "mobile": {"version": "iPhone 12-14.0"}}}, keyValMessage);
      assert.strictEqual(response, keyValMessage);
    });

    it('should append APP_AUTOMATE_PM_CLEAR message in keyValMessage if keyValMessage contains shell pm clear', function(){
      var keyValMessage = "shell pm clear error";
      var response = origHelper.getCustomMessageForError({"bsCaps":{}}, keyValMessage);
      assert.strictEqual(response, keyValMessage + errorMessages.APP_AUTOMATE_PM_CLEAR);
    });

    it('should append APP_AUTOMATE_ORIENTATION_FAILED_ERROR message in keyValMessage if keyValMessage contains app refused to rotate', function(){
      var keyValMessage = "Error: Set the orientation, but app refused to rotate.";
      var response = origHelper.getCustomMessageForError({"bsCaps":{}}, keyValMessage);
      assert.strictEqual(response, keyValMessage + errorMessages.APP_AUTOMATE_ORIENTATION_FAILED_ERROR);
    });

    it('should append appropriate error message in keyValMessage for automationName', function () {
      var keyValMessage = "SOME_ERROR_MESSAGE";
      var response = origHelper.getCustomMessageForError({ "bsCaps": { "automationName": 'flutter' } }, keyValMessage);
      assert.strictEqual(response, keyValMessage + errorMessages.APP_AUTOMATE_AUTOMATION_ERROR_MESSAGES['flutter']);
    });

    it('should append no error message in keyValMessage for invalid automationName', function () {
      var keyValMessage = "SOME_ERROR_MESSAGE";
      var response = origHelper.getCustomMessageForError({ "bsCaps": { "automationName": 'uiautomator' } }, keyValMessage);
      assert.strictEqual(response, keyValMessage);
    });
  });

  describe('#sendToEDS', function() {
    it('sends data to eds', function(){
      origHelper.sendToEDS({"hello":"world"});
    });
  });

  describe('#getPortForDesktopOS', function() {
    it('gets port for windows os', () => {
      const keyObject = helper.getKeyObject();
      keyObject['os'] = "windows 7";
      assert(origHelper.getPortForDesktopOS(keyObject) == '4567');
    });
    it('gets port for non windows os', () => {
      const keyObject = helper.getKeyObject();
      keyObject['os'] = "high sierra";
      assert(origHelper.getPortForDesktopOS(keyObject) == '45671');
    });
  });

  describe('#getPortForOS', function(){
    it('gets port for real mobile', () => {
      const keyObject = helper.getKeyObject();
      keyObject['os'] = "ios";

    });
  });

  describe('helper methods', () => {
    it('test returned errors', (done) => {
      assert(origHelper.getGlobalExceptionGenre('random') === 'default');
      assert(origHelper.getGlobalExceptionGenre('socket hang up') === 'socket_error');
      assert(origHelper.getGlobalExceptionGenre('EADDRINUSE') === 'address_in_use');
      assert(origHelper.getGlobalExceptionGenre('ECONNRESET') === 'econn_reset');
      assert(origHelper.getGlobalExceptionGenre('EPIPE') === 'epipe');
      assert(origHelper.getGlobalExceptionGenre('ETIMEDOUT') === 'etimedout');
      assert(origHelper.getGlobalExceptionGenre('Client network socket disconnected before secure TLS connection was established') === 'network_tls');
      done();
    });
  });

  describe('isMobile', function () {
    it('should return true where device or deviceName is present in params', function () {
      let params = { 'device': 'testDevice', 'deviceName': 'testDevice' };
      let response = origHelper.isMobile(params);
      assert.strictEqual(response, true);
    });

    it('should return true where device or deviceName is present in params.desiredCapabilities', function () {
      // desiredCapabilities as string
      let desiredCapabilities = JSON.stringify({ 'device': 'testDevice', 'deviceName': 'testDevice' });
      let params = { 'desiredCapabilities': desiredCapabilities };
      let response = origHelper.isMobile(params);
      assert.strictEqual(response, true);

      // desiredCapabilities as object
      desiredCapabilities = { 'device': 'testDevice', 'deviceName': 'testDevice' };
      params = { 'desiredCapabilities': desiredCapabilities };
      response = origHelper.isMobile(params);
      assert.strictEqual(response, true);
    });

    it('should return false where device or deviceName is absent in params', function () {
      let params = { 'desiredCapabilities': {} };
      let response = origHelper.isMobile(params);
      assert.strictEqual(response, false);
    });
  });

  describe('test helper methods for queueing stats hoothoot', () => {
    it('check if correct key is returned acc to product', () => {
      (origHelper.getHoothootKeyForQueueing('automate')).should.equal(constants.monitoring.reqToTimeKeyAutomate);
      (origHelper.getHoothootKeyForQueueing('app-automate')).should.equal(constants.monitoring.reqToTimeKeyAppAutomate);
    });
    it('should add a request to queueing map', () => {
      const x = sinon.spy(origHelper.redisClient, 'zadd');
      origHelper.addToQueueingStatsHoothoot(true, 12345);
      assert(x.calledOnce == true);
    });
    it('should remove a served request from queueing map', () => {
      const x = sinon.spy(origHelper.redisClient, 'zrem');
      origHelper.removeFromQueueingStatsHoothoot(12345);
      assert(x.calledTwice == true);
    });
  });

  describe('status check for console logs command', () => {
    it('should return true', () => {
      (origHelper.checkResponseStatus({}, {os: 'android', appium_version: '1.15.0'}, null)).should.equal(true);
    });
    it('should return false', () => {
      (origHelper.checkResponseStatus({}, {os: 'ios', appium_version: '1.15.0'}, null)).should.equal(false);
    });
    it('should return true for if statusCode is 200', () => {
      (origHelper.checkResponseStatus({}, {dialect: 'W3C'}, 200)).should.equal(true);
    });
    it('should return false for if statusCode is 200 but dialect is OSS and status is not 0', () => {
      (origHelper.checkResponseStatus({status: 13}, {dialect: 'OSS'}, 200)).should.equal(false);
    });
    it('should return true for if statusCode is 200 but dialect is OSS and status is 0', () => {
      (origHelper.checkResponseStatus({status: 0}, {dialect: 'OSS'}, 200)).should.equal(true);
    });
    it('should return false for statusCode is non-200', () => {
      (origHelper.checkResponseStatus({}, {dialect: 'W3C'}, 500)).should.equal(false);
    });
  });

  describe('proxy polling conditions check', () => {
    it('should return true for microsoft edge', () => {
      (origHelper.checkDevicesForProxyPolling({browser: 'MicrosoftEdge'})).should.equal(true);
    });
    it('should return true for ie 11', () => {
      (origHelper.checkDevicesForProxyPolling({browser: 'internet explorer', browser_version: '11.0'})).should.equal(true);
    });
    it('should return false for ie 10', () => {
      (origHelper.checkDevicesForProxyPolling({browser: 'internet explorer', browser_version: '10.0'})).should.equal(false);
    });
  });

  describe('getConsoleStackTrace', function () {
    it('should filter out application level trace', function () {
      let stack = "Error: \n    at Object.exports.sessionRemovedFromRegionHook (/home/<USER>/SeleniumHub/releases/helper.js:546)\n  (/home/<USER>/SeleniumHub/shared/node_modules/bluebird/js/release/async.js:17)";
      let consoleStackTrace = origHelper.getConsoleStackTrace(stack);
      assert(consoleStackTrace.indexOf('helper.js') > -1);
      assert(consoleStackTrace.indexOf('async.js') === -1);
    });
  });

  describe('PingZombie', function() {
    beforeEach(function() {
      sinon.stub(origHelper, 'sendToEDS');
      sinon.stub(DWH, 'send');
    });

    afterEach(function() {
      origHelper.sendToEDS.restore();
      DWH.send.restore();
    });

    it('should send data to zombies and call send to eds', async function() {
      await origHelper.PingZombie({kind: 'random'});
      DWH.send.called.should.be.true;
      origHelper.sendToEDS.called.should.be.true;
    });

    it('should not send data to zombies of kind is blacklisted and call send to eds', async function() {
      await origHelper.PingZombie({kind: 'automation_session_stats'});
      DWH.send.called.should.be.false;
      origHelper.sendToEDS.called.should.be.true;
    });
  });

  describe('isBrowserStackGroup', function() {
    it('should return true for group 2', function() {
      (origHelper.isBrowserStackGroup(2)).should.equal(true);
    });
  });

  describe('didReachPrivoxyTimeoutForSafari', function() {
    it('should return true for larger duration', function() {
      (origHelper.didReachPrivoxyTimeoutForSafari(new Date("12-12-2012"), new Date())).should.equal(true);
    });
  });

  describe('isBlockedRequest', function() {
    it('should return true for the blocked request', function() {
      (origHelper.isBlockedRequest('/appium/app/reset', false)).should.equal(true);
    });
  });

  describe('retrieveConsoleLogs', function() {

    let kafkaLogProducerErrorToZombie;

    beforeEach(() => {
      sinon.stub(HubLogger, 'miscLogger').returns("nothing");
      kafkaLogProducerErrorToZombie = sinon.spy(origHelper, 'kafkaLogProducerErrorToZombie');
    });

    afterEach(() => {
      origHelper.kafkaLogProducerErrorToZombie.restore();
      HubLogger.miscLogger.restore();
    });

    it('should request console logs for firefox and w3c true', function(done) {
      var keyObject = helper.getKeyObject();
      keyObject.dialect = "W3C"
      keyObject.browser = "firefox";
      let resFromTerminal = {
        data: JSON.stringify('{"value":1}'),
        statusCode: 200
      }
      sinon.stub(requestLib, 'call').returns({
        then: sinon.stub().callsArgWith(0, resFromTerminal).returns({
          catch: () => {}
        })
      });
      origHelper.retrieveConsoleLogs(keyObject, function(resp) {
        resp = JSON.parse(resp)
        resp.value.should.equal(1);
        done();
        requestLib.call.restore();
      });
    });

    it('should call kafkaLogProducerErrorToZombie when json parsing fails', function(done) {
      var keyObject = helper.getKeyObject();
      keyObject.dialect = "W3C"
      keyObject.browser = "firefox";
      let resFromTerminal = {
        data: JSON.stringify('{"value":'),
        statusCode: 200
      }
      sinon.stub(requestLib, 'call').returns(Promise.resolve(resFromTerminal));
      origHelper.retrieveConsoleLogs(keyObject, function(resp) {
        assert(kafkaLogProducerErrorToZombie.calledOnce === true);
        done();
        requestLib.call.restore();
      });
    });

    it('should call kafkaLogProducerErrorToZombie when fetching logs fails', function(done) {
      var keyObject = helper.getKeyObject();
      keyObject.dialect = "W3C"
      keyObject.browser = "firefox";
      let resFromTerminal = {
        data: JSON.stringify('{"value":'),
        statusCode: 200
      }
      sinon.stub(requestLib, 'call').returns(Promise.reject("rejected"));
      origHelper.retrieveConsoleLogs(keyObject, function(resp) {
        assert(kafkaLogProducerErrorToZombie.calledOnce === true);
        done();
        requestLib.call.restore();
      });
    });

    it('should request console logs for firefox and w3c false', function(done) {
      var keyObject = helper.getKeyObject();
      keyObject.dialect = "W3C"
      keyObject.os = "android"
      let resFromTerminal = {
        data: JSON.stringify('{"value":1}'),
        statusCode: 200
      }
      sinon.stub(requestLib, 'call').returns({
        then: sinon.stub().callsArgWith(0, resFromTerminal).returns({
          catch: sinon.stub().callsArgWith(0, "error")
        })
      });
      origHelper.retrieveConsoleLogs(keyObject, function(resp) {
        resp = JSON.parse(resp)
        resp.value.should.equal(1);
        done();
        requestLib.call.restore();
      });
    });
  });

  describe('getMethodAndText', () => {
    let keyObject, data;

    beforeEach(() => {
      keyObject = helper.getKeyObject();
    });

    it('return the correct type and text when method is Runtime.consoleAPICalled', () =>{
      data = {
       params: {
        type: "eventType",
        args: [{
          value: "eventText"
        }],
        timestamp: "eventTimestamp"
        }
      };
      const expectedData = ["eventText", "eventType", "eventTimestamp"];
    expect(origHelper.getMethodAndText(data, "Runtime.consoleAPICalled")).eql(expectedData);
    });

    it('return the correct type and text when method is Log.entryAdded', ()=> {
      data = {
        params: {
          entry: {
            level: "eventType",
            text: "eventText",
            timestamp: "eventTimestamp"
          }
        }
      }
      const expectedData = ["eventText", "eventType", "eventTimestamp"];
      expect(origHelper.getMethodAndText(data, "Log.entryAdded")).eql(expectedData);
    });
  });

  describe('canUploadConsoleLog' , () => {
    it('if requested console log level is verbose then should return true whatever be the type', () => {
      let level = constants.CONSOLE_LOG_LEVELS.get('verbose');
      expect(origHelper.canUploadConsoleLog(level,'error')).to.be.eql(true);
      expect(origHelper.canUploadConsoleLog(level,'warning')).to.be.eql(true);
      expect(origHelper.canUploadConsoleLog(level,'info')).to.be.eql(true);
      expect(origHelper.canUploadConsoleLog(level,'log')).to.be.eql(true);
    });

    it('if requested console log level is errors then should return true when the type is error', () => {
      let level = constants.CONSOLE_LOG_LEVELS.get('errors');
      expect(origHelper.canUploadConsoleLog(level,'error')).to.be.eql(true);
      expect(origHelper.canUploadConsoleLog(level,'warning')).to.be.eql(false);
      expect(origHelper.canUploadConsoleLog(level,'info')).to.be.eql(false);
      expect(origHelper.canUploadConsoleLog(level,'log')).to.be.eql(false);
    });

    it('if requested console log level is warnings then should return true when the type is error and warning', () => {
      let level = constants.CONSOLE_LOG_LEVELS.get('warnings');
      expect(origHelper.canUploadConsoleLog(level,'error')).to.be.eql(true);
      expect(origHelper.canUploadConsoleLog(level,'warning')).to.be.eql(true);
      expect(origHelper.canUploadConsoleLog(level,'info')).to.be.eql(false);
      expect(origHelper.canUploadConsoleLog(level,'log')).to.be.eql(false);
    });

    it('if requested console log level is info then should return true when the type is error,warning and info', () => {
      let level = constants.CONSOLE_LOG_LEVELS.get('info');
      expect(origHelper.canUploadConsoleLog(level,'error')).to.be.eql(true);
      expect(origHelper.canUploadConsoleLog(level,'warning')).to.be.eql(true);
      expect(origHelper.canUploadConsoleLog(level,'info')).to.be.eql(true);
      expect(origHelper.canUploadConsoleLog(level,'log')).to.be.eql(false);
    });

    it('if requested console log level is disable then should return false whatever be the type', () => {
      let level = constants.CONSOLE_LOG_LEVELS.get('disable');
      expect(origHelper.canUploadConsoleLog(level,'error')).to.be.eql(false);
      expect(origHelper.canUploadConsoleLog(level,'warning')).to.be.eql(false);
      expect(origHelper.canUploadConsoleLog(level,'info')).to.be.eql(false);
      expect(origHelper.canUploadConsoleLog(level,'log')).to.be.eql(false);
    });
  });

  describe('uploadCDPConsoleLog', () => {
    let keyObject, data, uploadLogPartToKafka;

    beforeEach(() => {
      keyObject = helper.getKeyObject();
    });

    afterEach(() => {
      HubLogger.uploadLogPartToKafka.restore();
    });

    it('upload console logs successfully with method Log.entryAdded and valid params', () => {
      const uploadLogPartToKafka = sinon.stub(HubLogger, 'uploadLogPartToKafka');
      data = {
        method: "Log.entryAdded",
        params: {
          entry: {
            level: "eventType",
            text: "eventText",
            timestamp: "eventTimestamp"
          }
        }
      };
      origHelper.uploadCDPConsoleLog(keyObject, data, constants.CONSOLE_LOG_LEVELS.get('verbose'));
      assert(uploadLogPartToKafka.called === true);
    });

    it('upload console logs successfully with method Runtime.consoleAPICalled and valid params', () => {
      const uploadLogPartToKafka = sinon.stub(HubLogger, 'uploadLogPartToKafka');
      data = {
        method: "Runtime.consoleAPICalled",
        params: {
          type: "eventType",
          args: [{
            value: "eventText"
          }],
          timestamp: "eventTimestamp"
        }
      };
      origHelper.uploadCDPConsoleLog(keyObject, data, constants.CONSOLE_LOG_LEVELS.get('verbose'));
      assert(uploadLogPartToKafka.called === true);
    });

    it('dont upload console logs with invalid method', () => {
      const uploadLogPartToKafka = sinon.stub(HubLogger, 'uploadLogPartToKafka');
      data = {
        method: "invalidFunction",
        params: {
          type: "eventType",
          args: [{
            value: "eventText"
          }],
          timestamp: "eventTimestamp"
        }
      };
      origHelper.uploadCDPConsoleLog(keyObject, data, constants.CONSOLE_LOG_LEVELS.get('verbose'));
      assert(uploadLogPartToKafka.called === false);
    });

    it('dont upload console logs with method Runtime.consoleAPICalled and invalid params', () => {
      const uploadLogPartToKafka = sinon.stub(HubLogger, 'uploadLogPartToKafka');
      data = {
        method: "Runtime.consoleAPICalled",
        params: {
          type: "eventType",
          args: [{
          }],
          timestamp: "eventTimestamp"
        }
      };
      origHelper.uploadCDPConsoleLog(keyObject, data, constants.CONSOLE_LOG_LEVELS.get('verbose'));
      assert(uploadLogPartToKafka.called === false);
    });

    it('dont upload console logs with method Log.entryAdded and invalid params', () => {
      const uploadLogPartToKafka = sinon.stub(HubLogger, 'uploadLogPartToKafka');
      data = {
        method: "Log.entryAdded",
        params: {
          entry: {
            text: "eventText"
          }
        }
      };
      origHelper.uploadCDPConsoleLog(keyObject, data, constants.CONSOLE_LOG_LEVELS.get('verbose'));
      assert(uploadLogPartToKafka.called === false);
    });

    it('dont upload console logs with no data', () => {
      const uploadLogPartToKafka = sinon.stub(HubLogger, 'uploadLogPartToKafka');
      data = {};
      origHelper.uploadCDPConsoleLog(keyObject, data, constants.CONSOLE_LOG_LEVELS.get('verbose'));
      assert(uploadLogPartToKafka.called === false);
    });
  });


  describe('getAutomationNameErrorMessage', function(){
    it('should return expected error string for valid key passed', function(){
      var errString = " Error occured while starting the session. To verify if all the pre-requisites for running Appium Flutter Driver tests have been met, refer to - https://www.browserstack.com/docs/app-automate/appium/test-hybrid-apps/test-flutter-apps"
      const responseData = origHelper.getAutomationNameErrorMessage("flutter");
      assert.equal(responseData, errString);
    });

    it('should return empty string if invalid key is passed', function(){
      var errString = ''
      const responseData = origHelper.getAutomationNameErrorMessage("random");
      assert.equal(responseData, errString);
    });
  });

  describe('shouldLogNonZeroStatusErrors', function() {
    it('should call first arg when reply is received', function() {
      const yes_callback = sinon.spy();
      const no_callback = sinon.spy();
      var stubObj = sinon.stub(origHelper.redisClient, 'get').callsArgWith(1, false, true)
      origHelper.shouldLogNonZeroStatusErrors(yes_callback, no_callback)
      yes_callback.called.should.be.true;
      no_callback.called.should.be.false;
      stubObj.restore();
    });
    it('should call second arg when error is received', function() {
      const yes_callback = sinon.spy();
      const no_callback = sinon.spy();
      var stubObj = sinon.stub(origHelper.redisClient, 'get').callsArgWith(1, true, false)
      origHelper.shouldLogNonZeroStatusErrors(yes_callback, no_callback)
      yes_callback.called.should.be.false;
      no_callback.called.should.be.true;
      stubObj.restore();
    });
  });

  describe('shouldCheckTitleAfterOpenUrl', function() {
    let redisGet;
    after(() => {
      redisGet.restore();
    });

    it('should call first callback with default config', function() {
      const yes_callback = sinon.spy();
      const no_callback = sinon.spy();
      origHelper.shouldCheckTitleAfterOpenUrl(yes_callback, no_callback)
      yes_callback.called.should.be.true;
      no_callback.called.should.be.false;
    });

    it('should call first callback when check_title_after_open_url is true', function() {
      const yes_callback = sinon.spy();
      const no_callback = sinon.spy();
      constants.forceCheckTitleAfterOpenUrl = false
      redisGet = sinon.stub(redisClient, 'get').callsArgWith(1, null, true);
      origHelper.shouldCheckTitleAfterOpenUrl(yes_callback, no_callback)
      yes_callback.called.should.be.true;
      no_callback.called.should.be.false;
      redisGet.restore();
    })

    it('should call second callback when check_title_after_open_url is false', function() {
      const yes_callback = sinon.spy();
      const no_callback = sinon.spy();
      let defaultVal = constants.forceCheckTitleAfterOpenUrl
      redisGet = sinon.stub(redisClient, 'get').callsArgWith(1, null, false);
      constants.forceCheckTitleAfterOpenUrl = false
      origHelper.shouldCheckTitleAfterOpenUrl(yes_callback, no_callback)
      yes_callback.called.should.be.false;
      no_callback.called.should.be.true;
      constants.forceCheckTitleAfterOpenUrl = defaultVal
    })
  });

  describe('setOptionsToStartRemoteDebuggerFirefox', function() {
    it('should set moz firefox options', function() {
      let postOptions = {};
      origHelper.setOptionsToStartRemoteDebuggerFirefox(postOptions);
      (postOptions['moz:firefoxOptions']['args'].length == 2 &&
       postOptions['moz:firefoxOptions']['prefs'] != undefined).should.be.true;
    })
  })

  describe('checkNonZeroStatusErrors', function() {
    let redisGet;

    after(() => {
      redisGet.restore();
    });

    it('should call log non zero status errors', function() {
      redisGet = sinon.stub(redisClient, 'get').callsArgWith(1, null, true);
      origHelper.checkNonZeroStatusErrors({nonZeroIncrementCounters: ["error1"]})
    })
  })

  describe('retrieveUploadConsoleLogs', function() {
    afterEach(() => {
      origHelper.appendConsoleLogs.restore();
      requestLib.call.restore();
    });

    it('should not call appendConsoleLogs as data returned is undefined', function() {
      let resFromTerminal = {
        data: undefined,
        statusCode: 200
      }
      var keyObject = helper.getKeyObject();
      keyObject.dialect = "W3C"
      keyObject.browser = "chrome";
      sinon.stub(requestLib, 'call').returns({
        then: sinon.stub().callsArgWith(0, resFromTerminal).returns({
          catch: () => {}
        })
      });
      const callbackm = sinon.spy();
      const appendConsoleLogs = sinon.stub(origHelper, 'appendConsoleLogs')
      origHelper.retrieveUploadConsoleLogs(keyObject, callbackm);
      callbackm.called.should.be.true;
      appendConsoleLogs.called.should.be.false;
    })

    it('should call appendConsoleLogs when data returned is not undefined', function() {
      let resFromTerminal = {
        data: JSON.stringify({"value":[], "status":0}),
        statusCode: 200
      }
      var keyObject = helper.getKeyObject();
      keyObject.dialect = "W3C"
      keyObject.browser = "chrome";
      sinon.stub(requestLib, 'call').returns({
        then: sinon.stub().callsArgWith(0, resFromTerminal).returns({
          catch: () => {}
        })
      });
      const callbackm = sinon.spy();
      const appendConsoleLogs = sinon.stub(origHelper, 'appendConsoleLogs')
      origHelper.retrieveUploadConsoleLogs(keyObject, callbackm);
      callbackm.called.should.be.true;
      appendConsoleLogs.called.should.be.true;
    })
  })
  describe('appendConsoleLogs', function() {

    let pingZombie;
    let kafkaLogProducerErrorToZombie;

    afterEach(() => {
      HubLogger.uploadLogPartToKafka.restore();
      origHelper.kafkaLogProducerErrorToZombie.restore();
      HubLogger.exceptionLogger.restore();
      origHelper.PingZombie.restore();
    });

    beforeEach(() => {
      pingZombie = sinon.spy(origHelper, 'PingZombie');
      kafkaLogProducerErrorToZombie = sinon.spy(origHelper, 'kafkaLogProducerErrorToZombie');
    });

    it('should append console logs', function() {
      let log_1 = {timestamp: new Date().getTime(), level: "INFO", message: "logs1"};
      let log_2 = {timestamp: new Date().getTime(), level: "INFO", message: "logs1"};
      let expectedLog = `${log_1.timestamp}:${log_1.level}:${log_1.message}\r\n${log_2.timestamp}:${log_2.level}:${log_2.message}\r\n`;
      let logstring = JSON.stringify([log_1, log_2]);
      const uploadLogPartToKafka = sinon.stub(HubLogger, 'uploadLogPartToKafka');
      const exceptionLogger = sinon.spy(HubLogger, 'exceptionLogger');
      origHelper.appendConsoleLogs({appTesting: false}, logstring);
      uploadLogPartToKafka.calledWith({appTesting: false}, null, expectedLog, constants.kafkaConfig.console_logs_topic).should.be.true;
      assert(exceptionLogger.calledOnce === false);
      assert(kafkaLogProducerErrorToZombie.calledOnce === false);
      assert(pingZombie.calledOnce === false);
    })

    it('should not append console logs when json parsing fails', () => {
      const uploadLogPartToKafka = sinon.stub(HubLogger, 'uploadLogPartToKafka');
      const exceptionLogger = sinon.spy(HubLogger, 'exceptionLogger');
      origHelper.appendConsoleLogs({appTesting: false}, "this is some random string");
      assert(/SyntaxError/.test(exceptionLogger.getCall(0).args[3]));
      assert(/logString: this is some random string/.test(exceptionLogger.getCall(0).args[0]));
      assert(uploadLogPartToKafka.calledOnce === false);
      assert(exceptionLogger.calledOnce === true);
      assert(kafkaLogProducerErrorToZombie.calledOnce === true);
      assert(pingZombie.calledOnce === true);
    });

    it('should not append console logs when null data is passed', () => {
      const uploadLogPartToKafka = sinon.stub(HubLogger, 'uploadLogPartToKafka');
      const exceptionLogger = sinon.spy(HubLogger, 'exceptionLogger');
      origHelper.appendConsoleLogs({appTesting: false}, null);
      assert(/logObject is undefined\/null/.test(exceptionLogger.getCall(0).args[3]))
      assert(uploadLogPartToKafka.calledOnce === false);
      assert(exceptionLogger.calledOnce === true);
      assert(kafkaLogProducerErrorToZombie.calledOnce === true);
      assert(pingZombie.calledOnce === true);
    });

    it('should not append console logs when undefined data is passed', () => {
      const uploadLogPartToKafka = sinon.stub(HubLogger, 'uploadLogPartToKafka');
      const exceptionLogger = sinon.spy(HubLogger, 'exceptionLogger');
      origHelper.appendConsoleLogs({appTesting: false}, undefined);
      assert(/SyntaxError/.test(exceptionLogger.getCall(0).args[3]));
      // dont print logString when it is undefined
      assert.equal(false, /logString:/.test(exceptionLogger.getCall(0).args[0]));
      assert(uploadLogPartToKafka.calledOnce === false);
      assert(exceptionLogger.calledOnce === true);
      assert(kafkaLogProducerErrorToZombie.calledOnce === true);
      assert(pingZombie.calledOnce === true);
    });
  })

  describe('kafkaLogProducerErrorToZombie', () => {
    let pingZombie;
    let pushFeatureUsage;

    afterEach(() => {
      origHelper.PingZombie.restore();
      instrumentation.pushFeatureUsage.restore();
    });

    beforeEach(() => {
      pingZombie = sinon.spy(origHelper, 'PingZombie');
      pushFeatureUsage = sinon.spy(instrumentation, 'pushFeatureUsage');
    });

    it('should report to zombie and mark session as failure in feature usage', () => {
      origHelper.kafkaLogProducerErrorToZombie('kind', 'topic', 'sessionid');
      assert.strictEqual(pingZombie.calledOnce, true);
      // assert.strictEqual(pushFeatureUsage.calledWith())
      assert.strictEqual(pushFeatureUsage.calledOnce, true);
    });
  })

  describe('instrumentVideoDuration', () => {
    let pushFeatureUsage;

    afterEach(() => {
      instrumentation.pushFeatureUsage.restore();
    });

    beforeEach(() => {
      pushFeatureUsage = sinon.spy(instrumentation, 'pushFeatureUsage');
    });

    it('should push video duration to feature usage', () => {
      let keyObject = {"platform_video_start_time" : "1621858563.228419", "rails_session_id" : "abcdassasasas"}
      origHelper.instrumentVideoDuration(keyObject);
      assert.strictEqual(pushFeatureUsage.calledOnce, true);
    });

    it('should not push video duration to feature usage as video start time in nan', () => {
      let keyObject = {"platform_video_start_time" : "1621858asdas", "rails_session_id" : "abcdassasasas"}
      origHelper.instrumentVideoDuration(keyObject);
      assert.strictEqual(pushFeatureUsage.calledOnce, false);
    });
  })

  describe('isMobile', function() {
    it('should return true', function() {
      (origHelper.isMobile({desiredCapabilities : {device: 'iPhone'}})).should.be.true;
    })
    it('should return false', function() {
      (origHelper.isMobile({desiredCapabilities : {os: 'catalina'}})).should.be.false;
    })
  })

  describe('#pingDataToStats', function() {
    describe('checking pre quit buckets', () => {
      [
        {
          bucketName: "page load",
          host_params: {exceptionClass: "unreachablebrowserexception", browser: "safari", exceptionRequest: "POST:url"}
        },
        {

          bucketName: "page load",
          host_params: {exceptionClass: "timeoutexception", browser: "safari", exceptionRequest: "POST:url"}
        },
        {
          bucketName: "customer",
          host_params: {exceptionClass: "unreachablebrowserexception", browser: "safari"}
        },
        {
          bucketName: "customer",
          host_params: {exceptionMessage: "Cannot navigate to", browser: "safari"}
        },
        {
          bucketName: "customer",
          host_params: {exceptionClass: "unhandled-exception-class", browser: "safari"}
        },
        {
          bucketName: "crash_webdriverio",
          host_params: {exceptionClass: "unreachablebrowserexception", browser: "chrome", isWebDriverIOSession: true}
        },
        {
          bucketName: "crash",
          host_params: {exceptionClass: "unreachablebrowserexception", browser: "chrome"}
        },
        {
          bucketName: "browser",
          host_params : {exceptionMessage: "timeout", browser: "chrome", exceptionRequest: "POST:execute" }
        },
        {
          bucketName: "selenium",
          host_params: {exceptionClass: "nullpointerexception" }
        },
        {
          bucketName: "appium",
          host_params: {exceptionMessage: "Appium error" },
        },
        {
          bucketName: "safariPrivoxyTimeout",
          host_params: {safariPrivoxyTimeout: true }
        },
        {
          bucketName: "timeoutError",
          host_params: { errorMessage: JSON.stringify({ message: "some-error", name: "timeoutError" }) }
        }
      ].forEach(function(row){
          it('should check for bucket ' + row.bucketName , () => {
            let zombies = sinon.stub(origHelper, "PingZombie");
            origHelper.pingDataToStats(row.host_params);
            const zombiesCall = zombies.getCall(0)
            expect(zombiesCall.args.length > 0).to.be.true;
            expect(zombiesCall.args[0]).to.have.ownPropertyDescriptor("pre_quit_bucket");
            expect(zombiesCall.args[0].pre_quit_bucket).be.equal(row.bucketName)
            zombies.restore();
          })
        })
    })

    describe('checking event emission', () => {
      it('should emit event to hoothoot', () => {
        const zombies = sinon.stub(origHelper, "PingZombie");
        const host_params = {
          safariPrivoxyTimeout: true,
          hub_to_term_error: true,
          appTesting: false,
          user: 1,
          hub_to_term_error: true
        };
        const uniqueUserEvent = sinon.stub(HubLogger.hoothoot_user, "uniqueUserEvent")
        origHelper.pingDataToStats(host_params);
        uniqueUserEvent.called.should.be.true;
        zombies.restore();
        uniqueUserEvent.restore();
      })
    })

    describe('checking selenium_exception_counts', () => {
      let stub, host_params, expectedReqBody;
      beforeEach(() => {
        host_params = {
          rails_session_id: 'some_session_id',
          appTesting: true,
          nonZeroStatusesCount: {
            'someError': 1
          }
        }
        expectedReqBody = {
          sessionid: 'some_session_id',
          stop_time: NaN,
          check_url: '',
          pre_quit: '::',
          pre_quit_bucket: '',
          bs_bucket: '',
          total_sleep: undefined,
          num_sleep: undefined,
          kind: 'app_automation_session_stats',
          last_response: undefined,
          last_request: undefined
        }
        stub = sinon.stub(origHelper,"PingZombie");
      });

      afterEach(() => {
        origHelper.PingZombie.restore();
      });

      it('should not populate selenium exceptions for app-automate', ()=> {
        host_params.appTesting = true;
        expectedReqBody.kind = 'app_automation_session_stats';
        origHelper.pingDataToStats(host_params);
        sinon.assert.calledWithExactly(stub, expectedReqBody);
      });
      it('should calculate total and populate selenium exceptions counts json for automate', ()=> {
        host_params.appTesting = false;
        expectedReqBody.kind = 'automation_session_stats';
        expectedReqBody.selenium_exception_counts = {
          'someError': 1,
          'total': 1
        }
        origHelper.pingDataToStats(host_params);
        sinon.assert.calledWithExactly(stub, expectedReqBody);
      });
    });
  })

  describe('#logAppiumBridgeCommand', () => {
    let keyObject;
    before(() => {
      keyObject = {rails_session_id: "dymmy-sesion-id", rproxyHost: "dummy-hostname", name: "dummy-name", appiumLogs: true}
    })

    it('should not make request for android appium logs', async () => {
      keyObject.os = "android"
      keyObject.device = "Galaxy S8"
      const requestLibCall = sinon.stub(requestLib, 'call').returns({
        then: sinon.stub().callsArgWith(0, {statusCode: 200})
      })
      await origHelper.logAppiumBridgeCommand(keyObject, "dummy-bridge", "logType-dummy", true)
      requestLibCall.called.should.be.false;
      requestLibCall.restore();
    })

    it('should make request for ios appium logs', async () => {
      keyObject.os = "ios"
      keyObject.device= "iPhone XS"
      const requestLibCall = sinon.stub(requestLib, 'call').returns({
        then: sinon.stub().callsArgWith(0, {statusCode: 200})
      })
      await origHelper.logAppiumBridgeCommand(keyObject, "dummy-bridge", "logType-dummy", true)
      requestLibCall.called.should.be.true;
      requestLibCall.restore();
    })

    it('should retry when request fails first time for ios appium logs', async () => {
      keyObject.os = "ios"
      keyObject.device= "iPhone XS"
      const requestLibCall = sinon.stub(requestLib, 'call').returns({
        then: sinon.stub().callsArgWith(0, {statusCode: 400}).returns({
        catch: sinon.stub().callsArgWith(0, "error-message")})
      })
      await origHelper.logAppiumBridgeCommand(keyObject, "dummy-bridge", "logType-dummy", true)
      requestLibCall.called.should.be.true;
      requestLibCall.restore();
    })
  })

  describe('#getString', () => {
    it('returns string', () => {
      expect(origHelper.getString(null)).to.be.equal("");
    })
  })

  describe('#checkActiveWindowOnTerminal', () => {

    let publish, zombies;
    beforeEach(() => {
      publish = sinon.stub(pubSub, "publish")
      zombies = sinon.stub(origHelper, "PingZombie")
    })

    afterEach(() => {
      publish.restore();
      zombies.restore();
    })

    it('should not call pubsub with active window out of activeWindowBasicAuthValues', () => {
      const data = '{"active_window": "test-active-window", "active_window_bucket": "test-active-window-bucket"}'
      const requestLibCall = sinon.stub(requestLib, 'call').returns({
        then: sinon.stub().callsArgWith(0, { data }).returns({
        catch: sinon.stub().callsArgWith(0, "error-message")})
      })
      const keyObject = {rproxyHost: "test-hostname", os: "windows", name: 'test-name', rails_session_id: "test-session-id", appTesting: false}
      origHelper.checkActiveWindowOnTerminal(keyObject);
      publish.called.should.be.false;
      requestLibCall.restore();
    })

    it('should call pubsub with active window if value in activeWindowBasicAuthValues', () => {
      const requestLibCall = sinon.stub(requestLib, 'call').returns({
        then: sinon.stub().callsArgWith(0, { data : '{"active_window": "authentication required", "active_window_bucket": "test-active-window-bucket"}'}).returns({
        catch: sinon.stub().callsArgWith(0, "error-message")})
      })
      const keyObject = {rproxyHost: "test-hostname", os: "windows", name: 'test-name', rails_session_id: "test-session-id", appTesting: false}
      origHelper.checkActiveWindowOnTerminal(keyObject);
      publish.called.should.be.true;
      requestLibCall.restore();
    })
  })

  describe('#checkSessionAppAutomate', () => {
    it('should call callback with true when session is appautomate', () => {
      const x = sinon.stub();
      constants.global_registry['test-session-id'] = { appTesting: true };
      origHelper.checkSessionAppAutomate('test-session-id', x);
      x.called.should.be.true;
      expect(x.getCall(0).args[0]).to.be.equal(true);
      delete constants.global_registry['test-session-id'];
    });

    it('should call callback with false when session not present in cache', () => {
      const x = sinon.stub();
      origHelper.checkSessionAppAutomate('test-session-id', x);
      x.called.should.be.true;
      expect(x.getCall(0).args[0]).to.be.equal(false);
    });

    it('should call callback with false when not session is not of apptesting', () => {
      constants.global_registry['test-session-id'] = { appTesting: false };
      const x = sinon.stub();
      origHelper.checkSessionAppAutomate('test-session-id', x)
      x.called.should.be.true;
      expect(x.getCall(0).args[0]).to.be.equal(false);
    });
  });

  describe('#logUDPFailure', () => {
    it('should log when in case of error', () => {
      const logUDPFailure = Helper.__get__("logUDPFailure")
      const miscLogger = sinon.spy(HubLogger, "miscLogger")
      logUDPFailure("error-message")
      miscLogger.called.should.be.true;
      miscLogger.restore();
    })
  })

  describe('#sanitizeChromeOptionsArgsArray', () => {
    it('return empty array when null chrome options is given', () => {
      expect(origHelper.sanitizeChromeOptionsArgsArray(null).length).to.equal(0)
    })

    it('return empty array when null chrome options is given', () => {
      expect(origHelper.sanitizeChromeOptionsArgsArray("string").length).to.equal(1)
      expect(origHelper.sanitizeChromeOptionsArgsArray("string")[0]).to.equal("string")
    })

    it('return empty array when null chrome options is given', () => {
      expect(origHelper.sanitizeChromeOptionsArgsArray({}).length).to.equal(1)
      expect(origHelper.sanitizeChromeOptionsArgsArray({})[0]).to.equal("{}")
    })
  })

  describe('#createHashForAutomateErrorData', () => {
    it('should return non empty string hash', () => {
      expect(typeof origHelper.createHashForAutomateErrorData("test-id", + new Date())).to.equal("string")
      expect(origHelper.createHashForAutomateErrorData().length > 0).to.equal(true)
    })
  })

  describe('#timeoutManagerGetRedisWatchKey', () => {
    it('should return index with redis key prefix', () => {
      expect(origHelper.timeoutManagerGetRedisWatchKey("2")).to.equal(constants.timeoutManager.redisKeyPrefix + "_2")
    })
  })

  describe('#sendToEDS', () => {[
        'automation_session_stats',
        'app_automation_session_stats',
        Events.AUTOMATE_SESSION_TIME_COMPONENTS,
        Events.AUTOMATE_TEST_SESSIONS,
        Events.AUTOMATE_ERROR_DATA,
        Events.AUTOMATE_QUEUEING_DATA,
        Events.APP_AUTOMATE_ERROR_DATA,
        Events.APP_AUTOMATE_QUEUEING_DATA,
        Events.APP_AUTOMATE_TEST_SESSIONS
      ].forEach((row) => {
        it('should log data when sending ' + row + ' event to eds', () => {
          const miscLogger = sinon.stub(HubLogger, "miscLogger")
          const edsSend = sinon.stub(EdsClient.prototype, "send").callsArgWith(1, "error").returns({
            catch: sinon.stub().callsArgWith(0, null)
          })
          origHelper.sendToEDS({kind: row, sessionid: "test-session-id"})
          miscLogger.called.should.be.true;
          miscLogger.restore();
          edsSend.restore();
        })
      })
  })

  describe('#getAutoitText', () => {
    it('should return autoit text with control and special character', () => {
      const keyObject = {rails_session_id: "test-session-id", ieSpecialKeyPress : ["SHIFT"]}
      const publish = sinon.stub(pubSub, "publish");
      const result = origHelper.getAutoitText(keyObject, ["\uE009", "\uE008"], pubSub)
      expect(typeof result).be.equal("string");
      publish.called.should.be.true;
      publish.restore();
    })
  })

  describe('#popNextSessionFromQueue', () => {
    it('should call pubsub', () => {
      const lpop = sinon.stub(redisClient, "lpop").callsArgWith(1, null, "test-reply")
      const publish = sinon.stub(pubSub, "publish");
      origHelper.popNextSessionFromQueue('{"user":"test"}')
      publish.called.should.be.true;
      lpop.restore();
      publish.restore();
    })
  })

  describe("#getEncodedURLParams", () => {
    it("should return empty string", () => {
      expect(requestLib.getEncodedURLParams("abcd")).to.equal("")
    })
  })

  describe("#hoothootPusher", () => {
    it("should emit to hoothoot", () => {
      const emit = sinon.stub(HubLogger.hoothoot, "emit")
      expect(requestLib.getEncodedURLParams("abcd")).to.equal("")
      origHelper.hoothootPusher("hoothootKey", "data", "tags")
      emit.called.should.be.true;
      const resultArgs = emit.getCall(0).args
      expect(resultArgs[0]).to.equal("hoothootKey")
      expect(resultArgs[1]).to.equal("data")
      expect(resultArgs[2]).to.equal("tags")
      emit.restore();
    })
  })

  describe("#checkUdpKeystoSend", () => {
    it("shoukd ping zombie with udp keys for automate", () => {
      const keyObject = {udpKeys : {key1: "", key2: ""}, rails_session_id: "test-session-id"}
      const zombies = sinon.stub(origHelper, "PingZombie")
      origHelper.checkUdpKeystoSend(keyObject)
      zombies.called.should.be.true;
      zombies.restore();
    })

    it("shoukd ping zombie with udp keys for app automate", () => {
      const keyObject = {udpKeys : {key1: "", key2: ""}, rails_session_id: "test-session-id", appTesting:true}
      const zombies = sinon.stub(origHelper, "PingZombie")
      origHelper.checkUdpKeystoSend(keyObject)
      zombies.called.should.be.true;
      zombies.restore();
    })
  })

  describe("#SanitizeCaps", () => {
    it("should remove chrome extension from chrome options", () => {
      let caps = {desiredCapabilities: {chromeOptions: {extensions: "extension-base64-code"}}}
      const result = origHelper.SanitizeCaps(caps)
      expect(result.desiredCapabilities.chromeOptions.extensions).to.equal(undefined)
    })
  })

  describe("#sanitizeRequestCapsForCLS", () => {
    it("should return same string when json parsing throws error", () => {
      const requestBodyString = "{"
      expect(origHelper.sanitizeRequestCapsForCLS(requestBodyString)).to.equal(requestBodyString)
    })
  })

  describe("#getReadableMinute", () => {
    it("return readable time format", () => {
      expect(/\d{4}-\d{2}-\d{2}T\d{2}:\d{2}/.test(origHelper.getReadableMinute())).be.equal(true)
    })
  })

  describe("#updateStopReleaseUrlParams", () => {
    it("should return sessionwithexception as true", () => {
      let keyObject = {rails_session_id: "test-seession-id", key: "test-key", exceptionEncountered: true};
      let sessionHash = {secondary_state: "FAILED"};
      let queryParams = {}, postParams = {};
      const updateStopReleaseUrlParams = Helper.__get__("updateStopReleaseUrlParams")
      updateStopReleaseUrlParams(keyObject, sessionHash, queryParams, postParams, "test")
      expect(postParams.s).to.equal(sessionHash.secondary_state)
      expect(postParams.sessionWithException).to.equal(true)
    })

    it("should return client_ip, client_connection_sockets_count, request_count in postParams if request type is 'stop'", () => {
      let keyObject = {rails_session_id: "test-seession-id", key: "test-key", exceptionEncountered: true, client_ip: "***********", clientConnectionSocketsCount: 101, request_count: 12};
      let queryParams = {}, postParams = {};
      let sessionHash = {secondary_state: "FAILED"};
      const updateStopReleaseUrlParams = Helper.__get__("updateStopReleaseUrlParams")
      updateStopReleaseUrlParams(keyObject, sessionHash, queryParams, postParams, 'stop')
      expect(postParams["client_ip"]).to.equal(keyObject["client_ip"])
      expect(postParams["client_connection_sockets_count"]).to.equal(keyObject.clientConnectionSocketsCount)
      expect(postParams["request_count"]).to.equal(keyObject["request_count"])
    })

    it("should return client_ip, client_connection_sockets_count, request_count, aisSessionDetails in postParams if request type is 'stop'", () => {
      let keyObject = {rails_session_id: "test-seession-id", key: "test-key", exceptionEncountered: true, client_ip: "***********", clientConnectionSocketsCount: 101, request_count: 12, selfHealingSuccess: true };
      let queryParams = {}, postParams = {};
      let sessionHash = {secondary_state: "FAILED"};
      const updateStopReleaseUrlParams = Helper.__get__("updateStopReleaseUrlParams")
      updateStopReleaseUrlParams(keyObject, sessionHash, queryParams, postParams, 'stop')
      expect(postParams["client_ip"]).to.equal(keyObject["client_ip"])
      expect(postParams["client_connection_sockets_count"]).to.equal(keyObject.clientConnectionSocketsCount)
      expect(postParams["request_count"]).to.equal(keyObject["request_count"])
      expect(postParams["selfHealingSuccess"]).to.equal(keyObject.selfHealingSuccess)
    })

    it("should return elementNotFound, pageLoadError in postParams if request type is post", ()=>{
      let keyObject = {rails_session_id: "test-seession-id", key: "test-key", exceptionEncountered: true, client_ip: "***********", clientConnectionSocketsCount: 101, request_count: 12, elementNotFound: true, pageLoadError: true};
      let queryParams = {}, postParams = {};
      let sessionHash = {secondary_state: "FAILED"};
      const updateStopReleaseUrlParams = Helper.__get__("updateStopReleaseUrlParams")
      updateStopReleaseUrlParams(keyObject, sessionHash, queryParams, postParams, 'stop')
      expect(postParams["elementNotFound"]).to.equal(keyObject.elementNotFound)
      expect(postParams["pageLoadError"]).to.equal(keyObject.pageLoadError)
    })

    it('should add ai_healing_details to postParams if present and total_healing_enabled_request > 0', () => {
      const keyObject = {
        rails_session_id: 'test-session-id',
        ai_healing_details: {
          total_healing_enabled_request: 10,
          total_healing_request: 5,
          pre_check_failure_count: 1,
          script_exec_error_count: 2,
          healing_failure_count: 1,
          healing_success_count: 1,
        },
      };
      const sessionHash = { secondary_state: 'FAILED' };
      const queryParams = {};
      const postParams = {};

      const updateStopReleaseUrlParams = Helper.__get__('updateStopReleaseUrlParams');
      updateStopReleaseUrlParams(keyObject, sessionHash, queryParams, postParams, 'stop');

      expect(postParams.ai_healing_details).to.deep.equal(keyObject.ai_healing_details);
    });

    it('should not add ai_healing_details to postParams if total_healing_enabled_request is 0', () => {
      const keyObject = {
        rails_session_id: 'test-session-id',
        ai_healing_details: {
          total_healing_enabled_request: 0,
          total_healing_request: 0,
          script_exec_error_count: 0,
          pre_check_failure_count: 0,
          healing_failure_count: 0,
          healing_success_count: 0,
        },
      };
      const sessionHash = { secondary_state: 'FAILED' };
      const queryParams = {};
      const postParams = {};

      const updateStopReleaseUrlParams = Helper.__get__('updateStopReleaseUrlParams');
      updateStopReleaseUrlParams(keyObject, sessionHash, queryParams, postParams, 'stop');

      expect(postParams.ai_healing_details).to.be.undefined;
    });

    it('should not add ai_healing_details to postParams if ai_healing_details is missing', () => {
      const keyObject = {
        rails_session_id: 'test-session-id',
      };
      const sessionHash = { secondary_state: 'FAILED' };
      const queryParams = {};
      const postParams = {};

      const updateStopReleaseUrlParams = Helper.__get__('updateStopReleaseUrlParams');
      updateStopReleaseUrlParams(keyObject, sessionHash, queryParams, postParams, 'stop');

      expect(postParams.ai_healing_details).to.be.undefined;
    });

    it('should not add ai_healing_details to postParams if total_healing_enabled_request key is missing', () => {
      const keyObject = {
        rails_session_id: 'test-session-id',
        ai_healing_details: {
          total_healing_request: 5,
          script_exec_error_count: 1,
          pre_check_failure_count: 1,
          healing_failure_count: 1,
          healing_success_count: 2,
        },
      };
      const sessionHash = { secondary_state: 'FAILED' };
      const queryParams = {};
      const postParams = {};

      const updateStopReleaseUrlParams = Helper.__get__('updateStopReleaseUrlParams');
      updateStopReleaseUrlParams(keyObject, sessionHash, queryParams, postParams, 'stop');

      expect(postParams.ai_healing_details).to.be.undefined;
    });
  })

  describe('#updateOutsideBrowserstackTime', function() {
    let keyObject;
    it('should update outsideBrowserstackTime', function() {
      sinon.stub(Date, 'now').returns(20);
      keyObject = {
        lastResponseTime: 10,
        outsideBrowserstackTime: 50,
      };
      origHelper.updateOutsideBrowserstackTime(keyObject);
      assert.equal(keyObject.outsideBrowserstackTime, 60);
      Date.now.restore();
    });
  });

  describe('#updateAppiumDesktopUsage', function () {
    it('should not set appiumDesktopVersionUsed in keyObject', function () {
      const randomUserAgent = 'curl/7.54.0';
      const keyObject = {};
      origHelper.updateAppiumDesktopUsage(keyObject, randomUserAgent);
      assert.equal(isUndefined(keyObject.appiumDesktopVersionUsed), true);
    });

    it('should set appiumDesktopVersionUsed in keyObject', function () {
      const ADUserAgent = 'admc/wd/1.13.0 appium-inspector/1.20.2-4';
      const keyObject = {};
      origHelper.updateAppiumDesktopUsage(keyObject, ADUserAgent);
      assert.equal(keyObject.appiumDesktopVersionUsed, 'admc/wd/1.13.0 appium-inspector/1.20.2-4');
    });

    it('should set appiumDesktopVersionUsed in keyObject', function () {
      const ADUserAgent = 'wd/1.13.0 appium-desktop/1.20.2-1';
      const keyObject = {};
      origHelper.updateAppiumDesktopUsage(keyObject, ADUserAgent);
      assert.equal(keyObject.appiumDesktopVersionUsed, 'wd/1.13.0 appium-desktop/1.20.2-1');
    });
  });

  describe('#getDeleteResponseDelayForGroup', () => {
    it('should return constants.SESSION_DELETE_RESPONSE_DELAY', async () => {
      sinon.stub(HubLogger, 'miscLogger');
      const sismemberStub = sinon.stub(origHelper.redisClient, 'sismember');
      sismemberStub.returns(true);
      assert(
        await origHelper.getDeleteResponseDelayForGroup(
          0,
          constants.SESSION_TIME_THRESHOLD_FOR_DELAY_IN_DELETE_RESPONSE,
          'iphoneX11'
        ),
        constants.SESSION_DELETE_RESPONSE_DELAY
      );
      HubLogger.miscLogger.restore();
      sismemberStub.restore();
    });
  });

  describe('#handleSessionsWithStopFailedOnMaxRetries', () => {
    it('should log error when received', () => {
      const rpush = sinon.stub(origHelper.redisClient, 'rpush').callsArgWith(1, "error-message");
      origHelper.handleSessionsWithStopFailedOnMaxRetries();
      expect(rpush.called).to.eq(true);
      rpush.restore();
    })
  })

  describe('#respondWithError', () => {
    let request = {};
    let response = {};

    beforeEach(() => {
      request.id = 'sample-request-id';
      response.write = () => {};
      response.end = () => {};
    });

    it('should call response.end and response.write with error', () => {
      const responseEndSpy = sinon.spy(response, 'end');
      const responseWriteSpy = sinon.spy(response, 'write');

      const error = 'SampleError';
      origHelper.respondWithError(request, response, error);

      sinon.assert.calledOnce(responseEndSpy);
      sinon.assert.calledWith(responseWriteSpy, error);

      responseEndSpy.restore();
      responseWriteSpy.restore();
    });

    it('should call skip write when flag is specified', () => {
      const responseEndSpy = sinon.spy(response, 'end');
      const responseWriteSpy = sinon.spy(response, 'write');

      const error = 'SampleError';
      origHelper.respondWithError(request, response, error, true);

      sinon.assert.calledOnce(responseEndSpy);
      sinon.assert.notCalled(responseWriteSpy);

      responseEndSpy.restore();
      responseWriteSpy.restore();
    });
  });

  describe('#getDashboardFireCmdMessage', () => {
    let opt = {};
    let random_error = '[INSTALL_PARSE_FAILED_MANIFEST_MALFORMED: Failed parse during installPackageLI: /data/app/vmdl735287113.tmp/base.apk (at Binary XML file line #24): com.anaplan.app.widget.view.WidgetConfigurationActivity: Targeting S+ (version 31 and above) requires that an explicit value for android:exported be defined when intent filters are prese[INSTALL_PARSE_FAILED_MANIFEST_MALFORMED: Failed parse during installPackageLI: /data/app/vmdl735287113.tmp/base.apk (at Binary XML file line #24):'
    beforeEach(() => {})
    it('should show app installation error message', () => {
      opt = {'error_type': 'other_apps_install_failure', 'error_meta_data':{},'firecmd_error_message':'app installation failed', 'error_from': 'user_error'}
      expect(origHelper.getDashboardFireCmdMessage(opt)).to.equal("Could not start a session. Something went wrong with app launch. app installation failed");
    });
    it('should show app installation error message with correct length', () => {
      opt = {'error_type': 'other_apps_install_failure', 'error_meta_data':{},'firecmd_error_message':"app installation failed" + random_error, 'error_from': 'user_error'}
      expect(origHelper.getDashboardFireCmdMessage(opt)).to.equal("Could not start a session. Something went wrong with app launch. app installation failed[INSTALL_PARSE_FAILED_MANIFEST_MALFORMED: Failed parse during installPackageLI: /data/app/vmdl735287113.tmp/base.apk (at Binary XML file line #24): com.anaplan.app.widget.view.WidgetConfigurationActivity: Targeting S+ (version 31 and above) requires that an explicit value for android:exported be defined when intent filters are prese[INSTALL_PARSE_FAILED_MANIFEST_MALFORMED: Failed parse during installPackageLI: /data/app/vmdl735287113.tmp/base.apk (at Binary ");
    });
    it('should map to correct message is error_type is present', () => {
      opt = {'error_type': 'mid_session_apps_download_failure', 'error_meta_data':{},'firecmd_error_message':'app installation failed'}
      expect(origHelper.getDashboardFireCmdMessage(opt)).to.equal("Could not start a session. Something went wrong with app launch. Please try to run the test again.");
    });
    it('should show correct templated message', () => {
      opt = {'error_type': 'app_settings_invalid_key', 'error_meta_data':{'random':'random'},'firecmd_error_message':'app installation failed'}
      expect(origHelper.getDashboardFireCmdMessage(opt)).to.equal("Could not start a session : \'<key>\' setting in App Settings DSL does not exist in App’s Settings Bundle. Please check the DSL and try again. If the error persists, please reach out to support.");
    });
    it('should show unknow exception if no case matches', () => {
      opt = {'error_meta_data':{},'firecmd_error_message':'app installation failed'}
      expect(origHelper.getDashboardFireCmdMessage(opt)).to.equal("Could not start a session. Something went wrong with app launch. Please try to run the test again.");
    });
    it('should show proguarded error message for camera injection error', () => {
      opt = {'error_type': 'camera_injection_failure_as_proguarded', 'error_meta_data':{},'firecmd_error_message':'app installation failed', 'error_from': 'user_error'}
      expect(origHelper.getDashboardFireCmdMessage(opt)).to.equal("[BROWSERSTACK_APP_TYPE_NOT_SUPPORTED] Currently, we do not support the uploaded app type with our Camera Image Injection feature. For more details, please refer to our documentation or reach out to our support team.");
    });
    it('should show proguarded error message for biometric injection error', () => {
      opt = {'error_type': 'biometric_injection_failure_as_proguarded', 'error_meta_data':{},'firecmd_error_message':'app installation failed', 'error_from': 'user_error'}
      expect(origHelper.getDashboardFireCmdMessage(opt)).to.equal("[BROWSERSTACK_APP_TYPE_NOT_SUPPORTED] Currently, we do not support the uploaded app type with our Biometric Authentication feature. For more details, please refer to our documentation or reach out to our support team.");
    });
  });
});
