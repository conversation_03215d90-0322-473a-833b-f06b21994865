/* eslint-disable no-underscore-dangle */

'use strict';

const {
  describe, it, beforeEach, afterEach,
} = require('mocha');
const sinon = require('sinon');
const winston = require('winston');
const helper = require('../../../helper');
const rewire = require('rewire');
const IORedisMock = require('ioredis-mock');
const fs = require('fs');
const { expect, assert } = require('chai');
const { constants } = require('../../../log');
const { isNotUndefined } = require('../../../typeSanity');

const TimeoutsListener = rewire('../../../apps/timeoutManager/timeoutsListener');

const redisClient = new IORedisMock().createConnectedClient();

describe('TimeoutsListener', () => {
  let timeoutsListener;

  beforeEach(() => {
    process.env.HUB_ENV = 'testing';
    // TODO (yashLadha): Need to remove as `logFormatter` is not being covered
    // if winston methods are stubbed.
    sinon.stub(console._stdout, 'write', () => {});
    sinon.stub(console._stderr, 'write', () => {});
  });

  afterEach(() => {
    console._stdout.write.restore();
    console._stderr.write.restore();
  });

  describe('#constructor()', () => {
    let orginalConsumerId;
    it('should instantiate object', () => {
      orginalConsumerId = process.env.CONSUMER_ID;
      process.env.CONSUMER_ID = '';
      timeoutsListener = new TimeoutsListener();
      process.env.CONSUMER_ID = orginalConsumerId;
      timeoutsListener.isProductionEnv = true;
      expect(timeoutsListener.consumerId).to.be.equal('v3');
    });
  });

  describe('#initializeLogger()', () => {
    beforeEach(() => {
      timeoutsListener = new TimeoutsListener();
      sinon.stub(winston, 'configure');
    });

    afterEach(() => {
      winston.configure.restore();
    });

    it('should initialize logger', (done) => {
      timeoutsListener.initializeLogger();
      assert(winston.configure.called.should.be.true);
      done();
    });

    it('should initialize logger for production environment', (done) => {
      timeoutsListener.isProductionEnv = true;
      timeoutsListener.initializeLogger();
      assert(winston.configure.called.should.be.true);
      done();
    });
  });

  describe('#initializeRedis()', () => {
    let originalRedisClient;
    beforeEach(() => {
      TimeoutsListener.__set__('helper.redisClientSecond', redisClient);
      timeoutsListener = new TimeoutsListener();
      originalRedisClient = timeoutsListener.redisClientBlock;
    });

    afterEach(() => {
      timeoutsListener.redisClientBlock = originalRedisClient;
    });

    it('should set redis successfully', () => {
      sinon.stub(fs, 'readFileSync', () => JSON.stringify({ key: 'some random value' }));
      sinon.stub(redisClient, 'defineCommand');
      timeoutsListener.initializeRedis();
      assert(fs.readFileSync.called.should.be.true);
      assert(redisClient.defineCommand.called.should.be.true);
      fs.readFileSync.restore();
      redisClient.defineCommand.restore();
    });

    it('should fail setting redis', () => {
      sinon.stub(fs, 'readFileSync').yields(new Error('Read failed'));
      sinon.stub(helper, 'sendAlerts');
      sinon.stub(winston, 'error');

      timeoutsListener.initializeRedis();
      assert(fs.readFileSync.called.should.be.true);
      assert(helper.sendAlerts.called.should.be.true);
      assert(process.exit.called.should.be.true);
      assert(winston.error.called.should.be.true);
      fs.readFileSync.restore();
      winston.error.restore();
      helper.sendAlerts.restore();
    });
  });

  describe('#getGoAhead()', () => {
    let clock;
    let originalRedisClient;
    let callbackSpy;
    beforeEach(() => {
      originalRedisClient = timeoutsListener.redisClientBlock;
      timeoutsListener = new TimeoutsListener();
      TimeoutsListener.__set__('this.redisClientBlock', redisClient);
      callbackSpy = sinon.spy();
    });

    afterEach(() => {
      timeoutsListener.redisClientBlock = originalRedisClient;
    });

    it('should return master', () => {
      sinon.stub(redisClient, 'becomeMaster').yields(null, 'I can become master');
      timeoutsListener.getGoAhead(callbackSpy);
      assert(redisClient.becomeMaster.called.should.be.true);
      redisClient.becomeMaster.restore();
    });

    it('should return slave', () => {
      clock = sinon.useFakeTimers();
      sinon.stub(redisClient, 'becomeMaster').onFirstCall().yields(null, 'I can become slave');
      timeoutsListener.getGoAhead(callbackSpy);
      clock.tick(constants.timeoutManager.pingForMasterInterval);
      assert(redisClient.becomeMaster.called.should.be.true);
      redisClient.becomeMaster.restore();
      clock.restore();
    });

    it('#reload()', () => {
      timeoutsListener.toReload = isNotUndefined;
      timeoutsListener.getGoAhead(sinon.spy());
    });
  });

  describe('#ackAlive()', () => {
    let originalRedisClient;
    beforeEach(() => {
      originalRedisClient = timeoutsListener.redisClientBlock;
      timeoutsListener = new TimeoutsListener();
      TimeoutsListener.__set__('this.redisClientBlock', redisClient);
      sinon.stub(winston, 'silly', () => { });
    });

    afterEach(() => {
      timeoutsListener.redisClientBlock = originalRedisClient;
      winston.silly.restore();
    });

    it('should return success response', () => {
      sinon.stub(redisClient, 'hset');
      timeoutsListener.ackAlive();
      assert(redisClient.hset.called.should.be.true);
      redisClient.hset.restore();
    });

    it('should return error response', () => {
      sinon.stub(redisClient, 'hset').yields(new Error('Failed writing'));
      timeoutsListener.ackAlive();
      assert(redisClient.hset.called.should.be.true);
      redisClient.hset.restore();
    });
  });

  describe('#verifyMaster()', () => {
    let originalRedisClient;

    beforeEach(() => {
      originalRedisClient = timeoutsListener.redisClientBlock;
      timeoutsListener = new TimeoutsListener();
      TimeoutsListener.__set__('this.redisClientBlock', redisClient);
      sinon.stub(winston, 'silly', () => { });
    });

    afterEach(() => {
      timeoutsListener.redisClientBlock = originalRedisClient;
      winston.silly.restore();
    });

    it('should return success response', () => {
      sinon.stub(redisClient, 'hget').yields(null, process.pid.toString());
      timeoutsListener.verifyMaster();
      assert(redisClient.hget.called.should.be.true);
      redisClient.hget.restore();
    });

    it('should return error response', () => {
      sinon.stub(redisClient, 'hget').yields(new Error('Failed writing'));
      timeoutsListener.verifyMaster();
      assert(redisClient.hget.called.should.be.true);
      redisClient.hget.restore();
    });
  });

  describe('#setReloadFunction()', () => {
    let originalRedisClient;

    beforeEach(() => {
      originalRedisClient = timeoutsListener.redisClientBlock;
      timeoutsListener = new TimeoutsListener();
      TimeoutsListener.__set__('this.redisClientBlock', redisClient);
    });

    afterEach(() => {
      timeoutsListener.redisClientBlock = originalRedisClient;
    });

    it('success', () => {
      sinon.stub(winston, 'info');
      sinon.stub(redisClient, 'unsetMaster').yields(null, sinon.spy());
      timeoutsListener.setReloadFunction();
      assert(winston.info.called.should.be.true);
      assert(redisClient.unsetMaster.called.should.be.true);
      winston.info.restore();
      redisClient.unsetMaster.restore();
    });

    it('error', () => {
      sinon.stub(winston, 'error');
      sinon.stub(redisClient, 'unsetMaster').yields(new Error('Failed to unsetMaster'));
      timeoutsListener.setReloadFunction();
      assert(winston.error.called.should.be.true);
      assert(redisClient.unsetMaster.called.should.be.true);
      winston.error.restore();
      redisClient.unsetMaster.restore();
    });

    it('warn for new timeoutManager', () => {
      sinon.stub(winston, 'warn');
      sinon.stub(redisClient, 'unsetMaster').yields(null, sinon.spy());
      timeoutsListener.consumerId = 'v3';
      timeoutsListener.setReloadFunction();
      timeoutsListener.toReload();
      assert(winston.warn.called.should.be.true);
      assert(redisClient.unsetMaster.called.should.be.true);
      assert(process.exit.called.should.be.true);
      winston.warn.restore();
      redisClient.unsetMaster.restore();
    });

    it('success toReload ', () => {
      sinon.stub(winston, 'warn');
      sinon.stub(redisClient, 'unsetMaster').yields(null, sinon.spy());
      timeoutsListener.setReloadFunction();
      timeoutsListener.toReload();
      assert(winston.warn.called.should.be.true);
      assert(redisClient.unsetMaster.called.should.be.true);
      assert(process.exit.called.should.be.true);
      winston.warn.restore();
      redisClient.unsetMaster.restore();
    });
  });

  describe('#newRun', () => {
    beforeEach(() => {
      timeoutsListener = new TimeoutsListener();
      sinon.stub(timeoutsListener.redisClientBlock, 'multi').returns({
        zrangebyscore: sinon.stub().returnsThis(),
        zremrangebyscore: sinon.stub().returnsThis(),
        exec: sinon.stub(),
      });
    });

    afterEach(() => {
      timeoutsListener.redisClientBlock.multi.restore();
    });

    it('should check toReload()', () => {
      timeoutsListener.toReload = isNotUndefined;
      timeoutsListener.newRun();
      assert.typeOf(timeoutsListener.toReload, 'function');
    });

    it('should call zrangebyscore, zremrangebyscore and exec', () => {
      timeoutsListener.newRun();
      assert(timeoutsListener.redisClientBlock.multi.called.should.be.true);
      assert(timeoutsListener.redisClientBlock.multi().zrangebyscore.called.should.be.true);
      assert(timeoutsListener.redisClientBlock.multi().zremrangebyscore.called.should.be.true);
      assert(timeoutsListener.redisClientBlock.multi().exec.called.should.be.true);
    });
  });

  describe('#handleRange', () => {
    let validData;
    let erraneosData;

    beforeEach(() => {
      timeoutsListener = new TimeoutsListener();
      validData = [[null, ['session-id', 12]], [null, 1]];
      erraneosData = [[new Error('dummy')], [new Error('dummy')]];
      sinon.stub(winston, 'error');
      sinon.stub(timeoutsListener, 'newRun');
      sinon.stub(timeoutsListener.idleTimeoutManager, 'triggerTimeouts');
    });

    afterEach(() => {
      winston.error.restore();
      timeoutsListener.newRun.restore();
      assert(timeoutsListener.idleTimeoutManager.triggerTimeouts.restore);
    });

    it('should log error if error is passed', () => {
      timeoutsListener.handleRange(new Error('dummy'), validData);
      assert(winston.error.called.should.be.true);
    });

    it('should log error if erraneos_data is passed', () => {
      timeoutsListener.handleRange(null, erraneosData);
      assert(winston.error.called.should.be.true);
    });

    it('should call `timeoutManager.triggerTimeouts` for valid data', () => {
      timeoutsListener.handleRange(null, validData);
      assert(timeoutsListener.idleTimeoutManager.triggerTimeouts.called.should.be.true);
    });
  });
});
