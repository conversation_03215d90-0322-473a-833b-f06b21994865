'use strict';

const { assert } = require('chai');
const {
  describe,
  it,
  beforeEach,
  afterEach,
} = require('mocha');
const IORedisMock = require('ioredis-mock');
const helper = require('../../../helper');
const winston = require('winston');
const sinon = require('sinon');
const ha = require('../../../ha');
const constants = require('../../../constants');
const requestLib = require('../../../lib/request');
const IdleTimeoutManager = require('../../../apps/timeoutManager/timeoutManager');

const redisClient = new IORedisMock().createConnectedClient();

describe('IdleTimeoutManager', () => {
  let timeoutManager;
  let message;

  beforeEach(() => {
    timeoutManager = new IdleTimeoutManager({ winston, redisClient });
    message = {
      sessionId: 'abcd1234',
      timeout: 60,
    };
  });

  describe('#checkDelay()', () => {
    it('should return true when receivedDelay greater than delay', () => {
      const delay = 10000;
      message.time = Date.now() - delay;
      const response = timeoutManager.checkDelay(message);
      const receivedDelay = response.split(' ')[1];
      assert(receivedDelay >= delay);
    });
  });


  describe('#clearStaleSession()', () => {
    beforeEach(() => {
      this.clock = sinon.useFakeTimers();

      const promiseStub = sinon.stub().returns({
        catch: sinon.stub().callsArgWith(0, 'err'),
      });

      sinon.stub(requestLib, 'call').returns({
        then: promiseStub,
        catch: sinon.stub().callsArgWith(0, 'err'),
      });

      promiseStub.callsArgWith(0, {
        data: 'data',
      });
    });

    afterEach(() => {
      this.clock.restore();
      requestLib.call.restore();
    });

    it('should return mark session as timedout', (done) => {
      timeoutManager.clearStaleSession(message);
      assert(requestLib.call.called.should.be.true);
      done();
    });

    it('should retry again when ha fails', (done) => {
      sinon.stub(ha, 'getData').yields(false, message);
      timeoutManager.clearStaleSession(message);
      assert(requestLib.call.called.should.be.true);
      ha.getData.restore();
      done();
    });

    it('should call itself when retrying for v3', (done) => {
      sinon.stub(ha, 'getData').yields(false, message);
      timeoutManager.consumerId = 'v3';
      timeoutManager.clearStaleSession(message);
      ha.getData.restore();
      timeoutManager.consumerId = undefined;
      done();
    });
  });

  describe('#triggerTimeouts()', () => {
    beforeEach(() => {
      sinon.stub(timeoutManager, 'idleTimeout').returns(() => {});
      sinon.stub(winston, 'info');
    });

    afterEach(() => {
      timeoutManager.idleTimeout.restore();
      winston.info.restore();
    });

    it('should call idleTimeout for each timedout session', () => {
      timeoutManager.triggerTimeouts(['abcd', 10, 'bcda', 12, 'cdab', 14, 'dabc', 16]);
      assert(timeoutManager.idleTimeout.callCount, 4);
    });

    it('should not print if no sessions timeout', () => {
      timeoutManager.triggerTimeouts([]);
      assert(winston.info.called.should.not.be.true);
    });
  });

  describe('#pushHootHootStats()', () => {
    beforeEach(() => {
      sinon.stub(helper, 'pushStatsToHootHoot');
    });

    afterEach(() => {
      helper.pushStatsToHootHoot.restore();
    });

    it('should return true when called', () => {
      timeoutManager.pushHootHootStats();
      assert(helper.pushStatsToHootHoot.called.should.be.true);
      constants.timeoutManager.hoothootPerMinKeys.forEach((hoothootKey) => {
        timeoutManager.trackHootHootStats[hoothootKey].should.equal(0);
      });
    });

    it('should return true when timeouts null', () => {
      timeoutManager.timeouts = null;
      timeoutManager.pushHootHootStats();
      assert(helper.pushStatsToHootHoot.called.should.be.true);
      constants.timeoutManager.hoothootPerMinKeys.forEach((hoothootKey) => {
        timeoutManager.trackHootHootStats[hoothootKey].should.equal(0);
      });
    });
  });
});
