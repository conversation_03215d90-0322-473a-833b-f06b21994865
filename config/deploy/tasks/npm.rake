namespace :npm do
  desc <<-DESC
        Install the project dependencies via npm. By default, devDependencies \
        will not be installed. The install command is executed \
        with the --production, --silent and --no-spin flags.
        You can override any of these defaults by setting the variables shown below.
          set :npm_target_path, nil
          set :npm_flags, '--production --silent --no-spin'
          set :npm_roles, :all
          set :npm_env_variables, {}
  DESC
  task :install do
    on roles fetch(:npm_roles) do
      within fetch(:npm_install_path) do
        with fetch(:npm_env_variables, {}) do
          execute :npm, 'install', fetch(:npm_flags)
        end
      end
    end
  end
end

namespace :load do
  task :defaults do
    set :npm_flags, %w(--production --silent --no-spin)
    set :npm_prune_flags, '--production'
    set :npm_roles, :all
  end
end
