# nodes

# USE3b
server '74.50.105.98', user: 'app', roles: [:nodes, :cdp]
server '74.50.105.89', user: 'app', roles: [:nodes, :cdp]
server '74.50.105.104', user: 'app', roles: [:nodes, :cdp]
server '74.50.105.105', user: 'app', roles: [:nodes, :cdp]
server '74.50.105.106', user: 'app', roles: [:nodes, :cdp]
server '74.50.105.107', user: 'app', roles: [:nodes, :cdp]
server '74.50.105.110', user: 'app', roles: [:nodes, :cdp]
server '74.50.105.113', user: 'app', roles: [:nodes, :cdp]
server '74.50.105.80', user: 'app', roles: [:nodes, :cdp]
server '74.50.105.81', user: 'app', roles: [:nodes, :cdp]
server '74.50.105.82', user: 'app', roles: [:nodes, :cdp]
server '74.50.105.69', user: 'app', roles: [:nodes, :cdp]
server '74.50.105.70', user: 'app', roles: [:nodes, :cdp]
server '74.50.105.72', user: 'app', roles: [:nodes, :cdp]
server '74.50.105.75', user: 'app', roles: [:nodes, :cdp]
server '74.50.105.219', user: 'app', roles: [:nodes, :cdp]
server '74.50.105.220', user: 'app', roles: [:nodes, :cdp]
# timeout managers, monitoring services, kafka uploaders

# USE3b
server '74.50.105.114', user: 'app', roles: [:timeout, :monitoring, :kafka_uploader]
server '74.50.105.67', user: 'app', roles: [:timeout, :monitoring, :kafka_uploader]
server '74.50.105.73', user: 'app', roles: [:timeout, :monitoring, :kafka_uploader]
server '74.50.105.226', user: 'app', roles: [:timeout, :monitoring, :kafka_uploader]
