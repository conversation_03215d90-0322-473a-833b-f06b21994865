# nodes

# EU
server '45.148.168.33', user: 'app', roles: [:nodes, :cdp]
server '45.148.169.187', user: 'app', roles: [:nodes, :cdp]
server '45.148.168.20', user: 'app', roles: [:nodes, :cdp]
server '45.148.168.21', user: 'app', roles: [:nodes, :cdp]
server '45.148.168.22', user: 'app', roles: [:nodes, :cdp]
server '45.148.168.23', user: 'app', roles: [:nodes, :cdp]
server '45.148.169.189', user: 'app', roles: [:nodes, :cdp] # scale
server '45.148.168.25', user: 'app', roles: [:nodes, :cdp]
server '45.148.168.26', user: 'app', roles: [:nodes, :cdp]
server '45.148.168.36', user: 'app', roles: [:nodes, :cdp]
server '45.148.168.37', user: 'app', roles: [:nodes, :cdp]
server '45.148.168.38', user: 'app', roles: [:nodes, :cdp]
server '45.148.169.190', user: 'app', roles: [:nodes, :cdp]

# timeout managers, monitoring services, kafka uploaders

# EU
server '45.148.168.7', user: 'app', roles: [:timeout, :monitoring, :kafka_uploader]
server '45.148.168.48', user: 'app', roles: [:timeout, :monitoring, :kafka_uploader]
server '45.148.168.40', user: 'app', roles: [:timeout, :monitoring, :kafka_uploader]
server '45.148.169.170', user: 'app', roles: [:timeout, :monitoring, :kafka_uploader]
