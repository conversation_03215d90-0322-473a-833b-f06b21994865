'use strict';

const kSendToDesktop = Symbol('kSendToDesktop');
const kPlaywrightType = Symbol('kPlaywright');
const kDetoxType = Symbol('kDetox');
const kDesktopDataReceived = Symbol('kDesktopDataReceived');
const kDesktopCloseReceived = Symbol('kDesktopCloseReceived');
const kReqData = Symbol('kReqData');
const kSessionDebug = Symbol('kSessionDebug');
const kConsoleLog = Symbol('kConsoleLog');
const kStartTimer = Symbol('kStartTimer');
const kReqURL = Symbol('kReqURL');
const kSendReconnectInfo = Symbol('kSendReconnectInfo');
const STOP_SOCKET_DELAY = 10 * 1000;
const kReconnect = Symbol('kReconnect');
const kCloseRemote = Symbol('kCloseRemote');
const kReceivedLockAck = Symbol('kReceivedLockAck');
const kPingTime = Symbol('kPingTime');
const RECONNECT_PREFIX = 'RECONNECT ';
const REMOTE_DEBUGGER_PORT = 9222;
const REQUEST_TIMEOUT = 40 * 1000;
const DESKTOP_HANDSHAKE_TIMEOUT = 60 * 1000;
const SOCKET_IDLE_MESSAGE = 'Socket idle from a long time';
const SERVICE_RESTART = 'Service Restart';
const SOCKET_CLOSE_MESSAGE = 'Socket close from close event';
const GRACEFULE_CLOSE_CODES = new Set([1000, 1001]);
const ABRUPT_SOCKET_MESSAGES = new Map([
  [1006, 'ABRUPT_CLOSE'],
  [1008, 'TIMEOUT']
]);
const CLIENT_SIDE_ABRUPT_CLOSE = 'CLIENT';
const SERVER_SIDE_ABRUPT_CLOSE = 'SERVER';
const SERVER_ERROR_CLOSE_CODE = 1011;
const PROXY_LOCKED = 'PROXY_LOCKED';
const PROXY_RESTART = 'PROXY_RESTART';
const WS_CLOSE_TAG = 'ws_close_rate';
const WS_OPEN_TAG = 'ws_open_rate';
const WS_MESSAGE_TAG = 'ws_message_rate';
const WS_CLOSE_REMOTE_TAG = 'ws_close_remote_rate';
const WS_PONG_TAG = 'ws_pong_rate';
const WS_ERROR_TAG = 'ws_error_rate';
const WS_DESKTOP_OPEN_TAG = 'ws_desktop_open_rate';
const WS_DESKTOP_MESSAGE_TAG = 'ws_desktop_message_rate';
const WS_DESKTOP_CLOSE_TAG = 'ws_desktop_close_rate';
const WS_DESKTOP_ERROR_TAG = 'ws_desktop_error_rate';
const WS_STATS = 'ws_stats';

const PLAYWRIGHT = 'playwright';
const PUPPETEER = 'puppeteer';
const SELENIUM = 'selenium';
const DETOX = 'detox';
const ERROR_MESSAGE = "Something went wrong!"

const PLAYWRIGHT_LH_PORT = '9225'; // Port to be used for playwright lighthouse tests

// Holds a list of strings that mark the beginning of guids for playwright/puppeteer messages
// For eg: Browser API guids are of the form: 'browser@fc157839f53cd7dcbd35812a9afcfc80'
// Browser-context API guids are of the form: 'browser-context@fc157839f53cd7dcbd35812a9afcfc80'
const BROWSER_GUID_FORMAT = {};
BROWSER_GUID_FORMAT[PLAYWRIGHT] = [ "browser@" ];

module.exports = {
  kSendToDesktop,
  kDesktopDataReceived,
  kDesktopCloseReceived,
  kReqData,
  kStartTimer,
  kSessionDebug,
  kReqURL,
  kPlaywrightType,
  kDetoxType,
  REMOTE_DEBUGGER_PORT,
  REQUEST_TIMEOUT,
  DESKTOP_HANDSHAKE_TIMEOUT,
  SOCKET_IDLE_MESSAGE,
  GRACEFULE_CLOSE_CODES,
  ABRUPT_SOCKET_MESSAGES,
  CLIENT_SIDE_ABRUPT_CLOSE,
  SERVER_SIDE_ABRUPT_CLOSE,
  SERVICE_RESTART,
  RECONNECT_PREFIX,
  STOP_SOCKET_DELAY,
  PROXY_LOCKED,
  PROXY_RESTART,
  kReceivedLockAck,
  kReconnect,
  kSendReconnectInfo,
  kConsoleLog,
  kCloseRemote,
  kPingTime,
  WS_CLOSE_TAG,
  WS_OPEN_TAG,
  WS_MESSAGE_TAG,
  WS_CLOSE_REMOTE_TAG,
  WS_PONG_TAG,
  WS_ERROR_TAG,
  WS_DESKTOP_OPEN_TAG,
  WS_DESKTOP_MESSAGE_TAG,
  WS_DESKTOP_CLOSE_TAG,
  WS_DESKTOP_ERROR_TAG,
  WS_STATS,
  PLAYWRIGHT,
  DETOX,
  PUPPETEER,
  SELENIUM,
  SOCKET_CLOSE_MESSAGE,
  ERROR_MESSAGE,
  BROWSER_GUID_FORMAT,
  SERVER_ERROR_CLOSE_CODE,
  PLAYWRIGHT_LH_PORT
};
