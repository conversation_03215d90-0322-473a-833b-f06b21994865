require 'json'
require 'shellwords'
lock "3.4.0"

set :application, "ws-reconnect-proxy"
set :repo_url, "**************:browserstack/ws-reconnect-proxy.git"
set :branch, ENV['BRANCH'] || "main"
set :tmp_dir, "/home/<USER>/captmp-ws-proxy"
set :deploy_to, "/home/<USER>/ws-reconnect-proxy"
set :scm, :git
set :keep_releases, 5
set :linked_dirs, ["tmp", "node_modules"]

set :message, ENV["message"] || ask('Message: ', "dev")
set :deployer, ENV["deployer"] || ask('Deployer: ', "automate")

set :last_commit_id, nil
set :push_deploy_tags, ENV['push_deploy_tags'].to_s.downcase || "false"
set :npm_install_path, "/home/<USER>/ws-reconnect-proxy/shared/"

namespace :deploy do
  task :restart do
    on roles(:cdp) do |host|
      execute "touch", "#{current_path}/tmp/restart.txt"
    end
  end

  task :configure_proxy do
    on roles(:cdp) do |host|
      execute "ln", "-s", "#{fetch :deploy_to}/.private/config.json", "#{current_path}/lib/config/config.json"
      execute "cp", "/home/<USER>/SeleniumHub/current/cdp/cluster.js", "#{fetch :deploy_to}/shared/cluster.js"
      execute "cp", "/home/<USER>/SeleniumHub/current/cdp/package.json", "#{fetch :deploy_to}/shared/package.json"
      execute "cp", "/home/<USER>/SeleniumHub/current/cdp/package-lock.json", "#{fetch :deploy_to}/shared/package-lock.json"
      execute "cp", "-r",  "/home/<USER>/SeleniumHub/current/cdp/*", "#{current_path}/."
      execute "cd", current_path
      invoke "npm:install"
    end
  end

  task :notify do
    if fetch(:stage).match(/prod/)
      begin
        socket = UDPSocket.new
        deployment_data = {
          :deployer => fetch(:deployer),
          :message => fetch(:message),
          :commit_id => fetch(:last_commit_id),
          :kind => "deploy_log",
          :timestamp => Time.now.to_i,
          :app => "ws-reconnect-proxy"
        }
        socket.send(deployment_data.to_json, 0, "zombie.browserstack.com", 8000)
      rescue Exception => e
        puts "Exception while sending UDP packet: #{e.inspect}"
      end

      sh "curl -d people=automate -d deploy=true -d priority=\"P5\" -d subject=\"ws-reconnect-proxy deployed by #{fetch :deployer} -> #{fetch :message}\" -d mobile=critical -d message=\"ws-reconnect-proxy deployed\" https://alert.browserstack.com/alert"
    end
  end
end

after "deploy:published", "deploy:configure_proxy"
after "deploy:configure_proxy", "deploy:restart"
after "deploy:restart", "deploy:notify"
