require 'json'
require 'shellwords'
require 'capistrano/console'
# config valid only for current version of Capistrano
lock "3.4.0"

set :application, "SeleniumHub"
set :repo_url, "**************:browserstack/SeleniumHub.git"
set :branch, ENV['BRANCH'] || "master"
set :tmp_dir, "/home/<USER>/captmp"
set :deploy_to, "/home/<USER>/SeleniumHub"
set :scm, :git
set :keep_releases, 5
set :linked_dirs, ["tmp", "node_modules"]

set :message, ENV["message"] || ask('Message: ', "dev")
set :deployer, ENV["deployer"] || ask('Deployer: ', "automate")

set :deploy_prod, false
set :last_commit_id, nil
set :push_deploy_tags, ENV['push_deploy_tags'].to_s.downcase || "false"
set :npm_install_path, "/home/<USER>/SeleniumHub/shared/"

namespace :deploy do
  task :restart do
    on roles(:nodes), in: :sequence, wait: 5 do |host| # wait 5 seconds between each node restart
      execute "touch", "#{current_path}/tmp/restart.txt"
      execute "touch", "#{current_path}/tmp/sample-file.txt" # needed for memory_cron_v2.sh to run
    end

    on roles(:timeout) do |host|
      execute "touch", "#{current_path}/tmp/restart-timeout.txt"
    end

    on roles(:uploader) do |host|
      execute "touch", "#{current_path}/tmp/restart-uploader.txt"
    end

    on roles(:monitoring) do |host|
      execute "touch", "#{current_path}/tmp/restart-monitoring.txt"
    end

    on roles(:kafka_uploader) do |host|
      execute "touch", "#{current_path}/tmp/restart-uploader.txt"
    end
  end

  task :configure_hub do
    on roles(:all) do |host|
      execute "ln", "-s", "#{fetch :deploy_to}/.private/conf.json", "#{current_path}/conf.json"
      execute "cp", "#{current_path}/cluster.js", "#{fetch :deploy_to}/shared/cluster.js"
    end
  end

  # Increments and pushes tags to github and creates a corrosponding json file in current dir
  # By default increment and push is disabled for staging envs, pass push_deploy_tags=true to enable them
  task :configure_tags do
    is_prod = fetch(:stage).match(/prod/)
    should_increment = is_prod || fetch(:push_deploy_tags) == 'true'
    prefix = is_prod ? 'v' : "#{fetch(:stage)}_v"
    increment_by = should_increment ? 0.01 : 0.00

    system 'git fetch --tags' # optional, in case deploy.rb is old
    matching_tags = `git tag -l '#{prefix}*'`
    latest_version = matching_tags.split(Regexp.union([prefix,"\n"])).map(&:to_f).max rescue 0
    latest_version = (latest_version || 0).round(3)

    # major version gets bumped after 100 minor versions
    new_tag = "#{prefix}#{'%.2f' % (latest_version + increment_by)}"

    if should_increment
      # use annotated tags for prod light weight for staging
      extra_args = is_prod ? "-m '#{fetch(:message)}'" : ''
      system "git tag #{new_tag} #{extra_args}"
      system "git push origin tag #{new_tag} --no-verify"
    end

    build_information = {
      tag: new_tag
    }

    on roles(:all) do |host|
      if host.has_role?(:canary_node)
        build_information[:machine_type] = "canary"
      else
        build_information[:machine_type] = "production"
      end
      execute "echo #{Shellwords.escape(build_information.to_json)} >> #{current_path}/.browserstack_build_version.json"
    end
  end

  task :update do
    on roles(:all) do |host|
      execute "cp", "#{release_path}/package.json", "#{fetch :deploy_to}/shared/package.json"
      execute "cp", "#{release_path}/package-lock.json", "#{fetch :deploy_to}/shared/package-lock.json"
      invoke "npm:install"
    end
  end

  task :notify do
    if fetch(:stage).match(/prod/)
      begin
        socket = UDPSocket.new
        deployment_data = {
          :deployer => fetch(:deployer),
          :message => fetch(:message),
          :commit_id => fetch(:last_commit_id),
          :kind => "deploy_log",
          :timestamp => Time.now.to_i,
          :app => "SeleniumHub"
        }
        socket.send(deployment_data.to_json, 0, "zombie.browserstack.com", 8000)
      rescue Exception => e
        puts "Exception while sending UDP packet: #{e.inspect}"
      end

      stage = "Selenium hub components"
      stage = "s3 Uploader" if fetch(:stage).match(/uploader/)

      sh "curl -d people=automate,app-automate -d deploy=true -d priority=P5 -d subject=\"Hub #{stage} deployed by #{fetch :deployer} -> #{fetch :message}\" -d mobile=critical -d message=\"hub deployed\" https://alert.browserstack.com/alert"
    end
  end
end

before "deploy:updated", "deploy:update"
after "deploy:published", "deploy:configure_hub"
after "deploy:configure_hub", "deploy:configure_tags"
after "deploy:configure_tags", "deploy:restart"
after "deploy:restart", "deploy:notify"
