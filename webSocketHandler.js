'use strict';

const { inspect } = require('util');
const uuidv4 = require('uuid/v4');
const WebSocket = require('ws');
const PuppeteerHandler = require('./socketManagers/puppeteerHandler');
const PlaywrightHandler = require('./socketManagers/playwrightHandler');
const DetoxHandler = require('./socketManagers/detoxHandler');
const SeleniumHandler = require('./socketManagers/seleniumHandler');
const { getJSONData } = require('./socketManagers/socketURLChecker');
const StopSocket = require('./socketManagers/stopSocket');
const {
  checkCredentials,
  sessionDebug,
  getConsoleLogLevel,
  isReconnectRequest,
  modifyPlaywrightClient
} = require('./socketManagers/validations');
const { global_ws_registry: globalWsRegistry, region, osHostName } = require('./constants');
const { incrementHootHootStat, pushFeatureUsage } = require('./helpers/instrumentation');
const {
  kReqData,
  kReceivedLockAck,
  kCloseRemote,
  kSendReconnectInfo,
  kSessionDebug,
  kReqURL,
  kReconnect,
  kStartTimer,
  SOCKET_IDLE_MESSAGE,
  kConsoleLog,
  kPingTime,
  RECONNECT_PREFIX,
  PROXY_LOCKED,
  WS_CLOSE_TAG,
  WS_OPEN_TAG,
  WS_MESSAGE_TAG,
  WS_CLOSE_REMOTE_TAG,
  WS_PONG_TAG,
  WS_STATS,
  SERVICE_RESTART,
  PLAYWRIGHT,
  SELENIUM,
  DETOX,
  PUPPETEER,
  WS_ERROR_TAG,
} = require('./config/socketConstants');
const logger = require('./logger').basicLogger;
const helper = require('./helper');

const puppeteerHandler = new PuppeteerHandler();
const playwrightHandler = new PlaywrightHandler();
const detoxHandler = new DetoxHandler();
const seleniumHandler = new SeleniumHandler();
const kUpdateTime = Symbol('kSocketUpdateTime');
const MAX_SOCKET_IDLE = 90 * 1000;
const STALE_SOCKET_COUNT_INTERVAL = 90 * 1000;
const PLAYWRIGHT_SOCKET_COUNT_INTERVAL = 2 * 1000;

// Nginx default timeout is 60 seconds, setting ping interval to 40 seconds.
// So, that connection doesn't timeouts if waiting for terminal to be allocated.
// Hence, pinging client WebSocket connection to state connection as alive.
const PING_INTERVAL = 40 * 1000;
const provideHandler = (product) => {
  let handler;
  switch (product) {
    case PLAYWRIGHT:
      handler = playwrightHandler;
      break;
    case SELENIUM:
      handler = seleniumHandler;
      break;
    case PUPPETEER:
      handler = puppeteerHandler;
      break;
    case DETOX:
      handler = detoxHandler;
      break;
    default:
      throw new Error(`Cannot provide handler for product: ${product}`);
  }

  return handler;
};

const TAG = 'WebSocketHandler';

class WebSocketHandler {
  constructor(options) {
    this.webSocketServer = new WebSocket.Server(options);
    this.idleSockets = new Map();
    this.attachListener();
    this.disconnecting = false;

    // NOTE: This events is only meant for internal Nodejs usage and the payload
    // and API can be changed so building application logic on top of this is not a
    // good idea.
    process.on('internalMessage', this.handleWorkerDisconnect.bind(this));

    // Initiating the timeout timer for each handler so that idle timeout logic
    // can be executed on the client sockets. Adding the kSendReconnectInfo event as
    // well to send the reconnect info to proxy in case of disconnection of the process.
    puppeteerHandler.on(kStartTimer, this.startIdleTimer.bind(this));
    puppeteerHandler.on(kSendReconnectInfo, WebSocketHandler.sendReconnectInfo);
    playwrightHandler.on(kStartTimer, this.startIdleTimer.bind(this));
    playwrightHandler.on(kSendReconnectInfo, WebSocketHandler.sendReconnectInfo);
    seleniumHandler.on(kSendReconnectInfo, WebSocketHandler.sendReconnectInfo);
    detoxHandler.on(kStartTimer, this.startIdleTimer.bind(this));
    detoxHandler.on(kSendReconnectInfo, WebSocketHandler.sendReconnectInfo);
  }

  handleWorkerDisconnect({ cmd, act } = {}) {
    if (cmd === 'NODE_CLUSTER' && act === 'disconnect') {
      this.disconnecting = true;
      this.webSocketServer.clients.forEach((socket) => {
        logger.info(`Closing socket ${socket.id} because of restart`, { TAG });
        socket.emit(kCloseRemote);
        StopSocket.addSocket(socket);
      });
    }
  }

  static sendReconnectInfo(ws, data) {
    logger.info(`Sending reconnect info for ${ws.id}`, { TAG });
    ws.send(`${RECONNECT_PREFIX}${JSON.stringify(data)}`);
  }

  startIdleTimer(ws) {
    const idleTimeoutInterval = ws.product === PLAYWRIGHT ? PLAYWRIGHT_SOCKET_COUNT_INTERVAL : STALE_SOCKET_COUNT_INTERVAL;
    this.idleSockets.set(
      ws.id,
      setInterval(
        this.closeIdleSockets.bind(this, ws),
        idleTimeoutInterval
      )
    );
  }

  attachListener() {
    this.webSocketServer.on('connection', this.connectionHandler.bind(this));
    this.webSocketServer.on('error', WebSocketHandler.errorHandler.bind(this));
  }

  static errorHandler(err) {
    logger.error(`Error received on the websocket server ${inspect(err, false, 1)}`, { TAG });
  }

  connectionHandler(ws, req, options) {
    ws.id = req.isSelenium ? req.url.split('/')[2] : uuidv4();
    // Store the request data for sending in firecommand
    ws[kReqData] = options;
    ws[kReqURL] = req.url;
    ws[kSessionDebug] = sessionDebug(options);
    ws[kConsoleLog] = getConsoleLogLevel(options);
    ws[kReconnect] = isReconnectRequest(req.headers);
    ws[kUpdateTime] = new Date();
    ws[kPingTime] = new Date();
    const handler = provideHandler(req.product);
    // Setup the playwright boolean flag in the websocket so
    // that the socket can be used for fetching the handler in
    // other parts of the code
    ws.isPlaywright = req.isPlaywright;
    ws.isSelenium = req.isSelenium;
    ws.isPuppeteer = req.isPuppeteer;
    ws.isDetox = req.isDetox;
    ws.product = req.product;
    ws.clientIp = req.headers['x-forwarded-for'] || req.connection.remoteAddress || req.headers['cf-connecting-ip'];
    ws.nginxToHubTime = req.nginxToHubTime;
    ws.userToNginx = req.userToNginx;

    if (ws.isPlaywright) {
      modifyPlaywrightClient(req, ws[kReqData].data);
    }

    logger.info(`Received connection event ${ws.id} with reconnect: ${ws[kReconnect]}`, { TAG });

    if (!ws[kReconnect]) {
      handler.preProcessAllocation(ws);
    } else if (ws.isSelenium) {
      pushFeatureUsage(ws.id, { ws: { reconnection: { timestamp: Date.now() } } }, () => {});
    }

    ws.heartbeatPing = setInterval(() => this.sendHeartbeatPing(ws), PING_INTERVAL);

    ws.on('message', (message) => {
      const startTime = process.hrtime.bigint();
      logger.debug(`Received message event ${ws.id} ${message.toString()}`, { TAG });
      incrementHootHootStat(WS_STATS, WS_MESSAGE_TAG);
      message = message.toString();
      if (this.disconnecting) {
        logger.info(`Received message in disconnecting state: ${message}`, { TAG });
      }

      if (message === PROXY_LOCKED) {
        logger.info(`Received proxy locked message on socket: ${ws.id}`, { TAG });
        ws.emit(kReceivedLockAck);
        return;
      }

      ws[kUpdateTime] = new Date();
      ws.C2HrequestCount = (ws.C2HrequestCount || 0) + 1;
      // Start the allocation in playwright after the first message
      // since we do not need to start allocation on websocket connection.
      handler.messageHandler(ws, message, { startTime });
    });

    ws.on(kCloseRemote, () => {
      logger.info(`Received closeRemote event ${ws.id}`, { TAG });
      incrementHootHootStat(WS_STATS, WS_CLOSE_REMOTE_TAG);
      handler.emit(kCloseRemote, ws.id);
    });

    ws.on('close', (code, message) => {
      this.socketCloseHandler(code, message, ws);
    });

    ws.on('error', (event) => {
      logger.error(`Received error event ${ws.id} ${event}`, { TAG });
      incrementHootHootStat(WS_STATS, WS_ERROR_TAG);
      if (ws.isSelenium || ws.isPlaywright) {
        pushFeatureUsage(ws.id, { ws: { errorClient: { event, timestamp: Date.now() } } }, () => {});
        // TODO: Once we have a dedicated product column, remove the dependency on key: browser.
        helper.PingZombie({
          timestamp: Date.now(),
          kind: 'hub-ws-error',
          data: `Received error event ${ws.id} ${event}`,
          region,
          machine: osHostName,
          session_id: ws.id,
          browser: req.product
        });
      }
    });

    ws.on('pong', () => {
      logger.info(`Received pong for Request Id : ${ws.id}`, {
        TAG,
      });
      incrementHootHootStat(WS_STATS, WS_PONG_TAG);
    });

    incrementHootHootStat(WS_STATS, WS_OPEN_TAG);
    globalWsRegistry[ws.id] = ws;
    if (ws.isSelenium) {
      pushFeatureUsage(ws.id, { ws: { connection: { timestamp: Date.now() } } }, () => {});
    }
  }

  socketCloseHandler(code, message, ws) {
    logger.info(`Received close event for websocket: ${ws.id} ${code} with message: ${message}, message-length: ${message ? message.length : 0}`, {
      TAG,
      log: {
        kind: 'WS_SOCKET_CLOSE_HANDLER'
      }
    });
    incrementHootHootStat(WS_STATS, `${WS_CLOSE_TAG}_${code}`);
    if (ws.isSelenium || ws.isPlaywright) {
      pushFeatureUsage(ws.id, { ws: { closeClient: { code, message, timestamp: Date.now() } } }, () => {});
    }
    delete globalWsRegistry[ws.id];
    const handler = provideHandler(ws.product);
    let socketCloseCode = code;
    if (message === SOCKET_IDLE_MESSAGE) {
      // As per RFC this should be the status code if we are explicitly terminating
      // connection from the server side. Since there is no message received from the
      // client side in some time so we are closing the connection.
      socketCloseCode = 1008;
    } else if (code === 1005 && message === '') {
      // This is sent on conclude event on the receiver interface which in case
      // of handler instances is hub. When the socket is closed on the server if there
      // is no explicit status code sent the bindings will send the empty data for the
      // conclude (graceful termination) as well.
      // Ref: https://github.com/websockets/ws/blob/8c914d18b86a7d1408884d18eeadae0fa41b0bb5/lib/receiver.js#L456
      // When we do `.close()` on the websocket instance if no data is sent then
      // an EMPTY_BUFFER (allocated space is 0 bytes) sent on the socket.
      //
      // Also as per RFC if no status code is specified then treat it as 1005 code.
      // https://datatracker.ietf.org/doc/html/rfc6455#section-7.1.5
      socketCloseCode = 1001;
    } else if (message === SERVICE_RESTART) {
      // Restarting process because of cluster disconnect signal
      logger.info(`Clearing IDLE timeouts for ${ws.id} because of restart`, { TAG });
      this.clearIdleSocketInterval(ws.id);
      handler.closeTerminalSocket(ws);
      return;
    }
    this.clearIdleSocketInterval(ws.id);
    clearInterval(ws.heartbeatPing);
    handler.clientCloseHandler(ws, { code: socketCloseCode });
  }

  clearIdleSocketInterval(wsId) {
    if (this.idleSockets.has(wsId)) {
      clearInterval(this.idleSockets.get(wsId));
      this.idleSockets.delete(wsId);
    }
  }

  // eslint-disable-next-line class-methods-use-this
  sendHeartbeatPing(ws) {
    try {
      logger.info(`Sending ping for Request Id : ${ws.id}`, {
        TAG,
      });
      ws.ping();
    } catch (error) {
      logger.error(`Failed to send ping. Client websocket connection closed already for Request Id : ${ws.id}`, {
        TAG,
      });
    }
  }

  static checkSocketUpgrade(request, cb) {
    try {
      const jsonData = getJSONData(request);
      if (!request.isSelenium) {
        checkCredentials(jsonData);
      }
      cb(null, jsonData);
    } catch (err) {
      logger.error(`Error in upgrade: ${err}`, { TAG, url: request.url });
      cb(err);
    }
  }

  upgradeDoneCallback(ws, req, options) {
    this.webSocketServer.emit('connection', ws, req, options);
  }

  handleUpgrade(req, socket, head, options) {
    this.webSocketServer.handleUpgrade(req, socket, head, (ws) => {
      this.upgradeDoneCallback(ws, req, options);
    });
  }

  closeIdleSockets(ws) {
    let idleTimeoutValue = MAX_SOCKET_IDLE;
    const handler = provideHandler(ws.product);
    if (ws.product === PLAYWRIGHT) {
      const keyObject = handler.getRegistryObject(ws.id);
      idleTimeoutValue = (keyObject && keyObject.idle_timeout) || MAX_SOCKET_IDLE;
    }
    const isIDLE = (new Date() - ws[kUpdateTime]) >= idleTimeoutValue;
    if (isIDLE) {
      logger.info(`Checking socket ${ws.id} idle ${isIDLE} for ${idleTimeoutValue}s`, { TAG });
      // Trigger the close handler for closed sockets as well. Since user might
      // close socket even before the allocation has happened. So close method
      // checks that if the socket is already closed then it won't try to emit the close
      // event again and directly call the close handler.
      if (ws.readyState >= WebSocket.CLOSED) {
        this.socketCloseHandler(1001, SOCKET_IDLE_MESSAGE, ws);
      } else {
        handler.closeClientConnection(ws, SOCKET_IDLE_MESSAGE, 1001);
      }
    } else {
      handler.updateTime(ws);
    }
  }
}

module.exports = WebSocketHandler;
