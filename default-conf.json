{"wsProxyHost": "localhost", "wsProxyPort": 9124, "wsProxyScheme": "ws", "hub_log_dir": "/ebs", "nodeTimeout": 250000, "startNodeTimeout": 50000, "longRequestTimeOffset": 10000, "idleTimeout": 90000, "bsTimeout": 40000, "bsRetryTimeout": 5000, "sameRetryTimeout": 5000, "bstack_api_host": "apidev.bsstag.com", "app_automate_bstack_api_host": "apidev.bsstag.com", "bstack_api_port": 443, "bs_host": "localhost", "bs_port": 3000, "hub_host": "localhost", "hub_port": 8080, "log_post_interval": 2000, "env_name": "local", "aws_key": "********************", "aws_secret": "UG4YM5klzvLWW8ip7GhKXW9FPmhH3hLoGMIeObFW", "logs_aws_key": "********************", "logs_aws_secret": "UG4YM5klzvLWW8ip7GhKXW9FPmhH3hLoGMIeObFW", "logs_aws_bucket": "bs-stag-selenium-logs-use", "app_automate_logs_aws_key": "********************", "app_automate_logs_aws_secret": "UG4YM5klzvLWW8ip7GhKXW9FPmhH3hLoGMIeObFW", "app_automate_logs_aws_bucket": "bs-stag-selenium-logs-use", "blocked_ips": [], "blocked_ips_regex": "", "blocked_user_timeout": 60000, "mail_to": [], "ha_server": "127.0.0.1", "ha_port": "11211", "redis_server": "localhost", "redis_port": "6379", "redis_auth": "", "eliminate_rproxy_private_hub_sub_regions": false, "logkey": "heya", "railstoken": "selautomate", "hub_name": "hub1", "not_allowed_requests": ["POST:maximize"], "browserstack.region": "us-east-1", "metrics_region": "dev", "non_pipe_urls": ["POST:value"], "other_hub_servers": [], "zombie_server": "zombiestaging.browserstack.com", "zombie_port": 8000, "eds_server": "edsstaging.bsstag.com", "eds_port": 8553, "eds_key": "abcdef", "default_semaphore_ttl": 600000, "timeoutManager": {"totalCountThisRegion": 1}, "screenshot_count_log_file": ".", "rails_omitted_caps": ["desired"], "rails_redacted_caps": ["firefox_profile", "firefoxProfile"], "output_omitted_caps": ["opera.profile", "proxy", "firefox_profile", "moz:firefoxOptions", "firefoxOptions", "desired", "keychain<PERSON><PERSON>", "keychainPassword", "app", "appURL", "chromeExtension"], "omit_debug_mode": ["brucescott1"], "blocked_users": [""], "safari_driver_port": 8884, "secondary_states": {"SUCCESS": "success", "1": "tunnel-required", "2": "tunnel-required", "3": "tunnel-required", "4": "tunnel-required", "401": {"Basic realm=\"Application\"": "basic-auth", "NTLM": "ntlm-auth"}}, "relaxed_http_timeout": 45000, "relaxed_start_timeout": 30000, "ie_compatibility_modes": [11001, 11000, 10001, 10000, 9999, 9000, 8888, 8000, 7000], "hub_status_data": {"status": 0, "sessionId": null, "value": {"build": {"time": "2014-03-27 17:17:32", "version": "2.41.0", "revision": "3192d8a"}}, "state": "success"}, "terminal_cmd_exec_max_time": 30000, "page_load_timeout": 120000, "queue_request_delay": 30000, "queue_max_wait": 900000, "exponential_default_retry_delay": [10000, 15000, 20000], "exponential_nta_retry_delay": [10000, 25000, 30000], "hub_log_level": 4, "screenshot_count_log_folder": "/ebs/app", "default_safaridriver": "2.45", "protractor_safaridriver": "2.48", "default_seleniumjar_version": "2.51.0", "default_seleniumjar_port": 6556, "default_basic_auth_ios_timeout": 5000, "default_basic_auth_ios_response_timeout": 60000, "default_page_load_ios11_timeout": 60000, "default_page_load_timeout": 60000, "rails_pipeline_queue_length_threshold": 12, "rails_pipeline_queue_length_threshold_aa": 4, "safari_cancel_timeout": 15000, "edge_auth_timeout": 25000, "acceptSslffRequestTimeout": 30000, "acceptSslffReturnTimeout": 60000, "checkPageLoadWait": 1000, "ie11w10_file_upload_timeout": 10000, "autoit_sendkeys_sleep": 800, "autoit_sendkeys_max_sleep": 2000, "autoit_sendkeys_min_sleep": 10, "emulatorDeviceSizes": {"Google Nexus 5-5.0-1080x1920": {"portrait": {"width": 485, "height": 870, "x": 220, "y": 165}, "landscape": {"width": 862, "height": 488, "x": 176, "y": 220}}, "Samsung Galaxy S5-4.4-1080x1920": {"portrait": {"x": 95, "y": 172, "width": 498, "height": 885}, "landscape": {"width": 884, "height": 496, "x": 174, "y": 96}}, "Samsung Galaxy S5 Mini-4.4-720x1280": {"portrait": {"x": 105, "y": 195, "width": 477, "height": 849}, "landscape": {"width": 848, "height": 475, "x": 188, "y": 93}}, "HTC One M8-4.4-1080x1920": {"portrait": {"x": 96, "y": 184, "width": 487, "height": 851}, "landscape": {"width": 865, "height": 492, "x": 184, "y": 90}}, "Samsung Galaxy Tab 4 10.1-4.4-1280x800": {"portrait": {"x": 156, "y": 118, "width": 643, "height": 1030}, "landscape": {"width": 1027, "height": 644, "x": 115, "y": 146}}, "iPhone 6S Plus-9.1": {"portrait": {"x": 56, "y": 54, "width": 623, "height": 1106}, "landscape": {"width": 1104, "height": 622, "x": 56, "y": 54}}, "iPhone 6S-9.1": {"portrait": {"x": 56, "y": 54, "width": 562, "height": 1002}, "landscape": {"width": 1002, "height": 562, "x": 56, "y": 54}}, "iPad Air 2-9.1": {"portrait": {"x": 56, "y": 54, "width": 767, "height": 1025}, "landscape": {"width": 1024, "height": 769, "x": 56, "y": 54}}, "iPad Pro-9.1": {"portrait": {"x": 56, "y": 54, "width": 821, "height": 1095}, "landscape": {"width": 1093, "height": 819, "x": 56, "y": 54}}, "iPad Mini 4-9.1": {"portrait": {"x": 56, "y": 54, "width": 768, "height": 1025}, "landscape": {"width": 1024, "height": 769, "x": 56, "y": 54}}}, "realDeviceSizes": {"Samsung Galaxy Tab 4-4.4": {"landscape": {"x": 0, "y": -360, "width": 800, "height": 2560}, "portrait": {"width": 2560, "height": 680, "x": 0, "y": 120}}}, "defaultAndroidVideoHeight": "800", "defaultAndroidVideoWidth": "800", "androidVideoResolution": {"Google Pixel 6 Pro": {"width": 1440, "height": 3120}}, "chromeDriverNodes": [{"port": 9091, "version": "2.16"}, {"port": 9092, "version": "2.31"}], "cls_port": 41234, "cls_host": "logs.bsstag.com", "privoxy_keep_alive_timeouts_key": "PRIVOXY_KEEP_ALIVE_TIMEOUTS", "s3_uploader_queue": "AUTOMATE_Q_1", "app_automate_s3_uploader_queue": "APP_AUTOMATE_Q_1", "app_automate_current_sessions_redis_key": "APP_AUTOMATE_CURRENT_SESSIONS", "s3_redis_key_expiry_time": "7200", "spammy_redis_key": "AUTOMATE_SPAMMY_URLS", "max_stop_retry_failed": "AUTOMATE_SESSION_WITH_STOP_FAILED", "app_automate_max_stop_retry_failed": "APP_AUTOMATE_SESSION_WITH_STOP_FAILED", "use_longjohn": false, "max_non_zero_day_count": 1000, "pushNodeHoothootInterval": 60000, "pipelineManuplationValues": {"automate": {"quickRecoveryThreshold": 90, "quickRecoveryMultiplier": 1.5, "normalRecoveryMultiplier": 1.1, "penalizeMoreThreshold": 10, "penalizeLessThreshold": 1, "penalizeMoreDivisor": 4, "penalizeLessDivisor": 2, "localizedUserThreshold": 100, "topUserCount": 1}, "appAutomate": {"quickRecoveryThreshold": 90, "quickRecoveryMultiplier": 1.1, "normalRecoveryMultiplier": 1.1, "penalizeMoreThreshold": 10, "penalizeLessThreshold": 1, "penalizeMoreDivisor": 4, "penalizeLessDivisor": 4, "topUserCount": 1}}, "kafkaConfig": {"ACKNOWLEDGE_AS_ALIVE_INTERVAL": 10000, "CHECK_SHOULD_BECOME_MASTER_INTERVAL": 10000, "CHECK_STILL_MASTER_INTERVAL": 60000, "checkpointDirPath": "./tmp/checkpoints", "console_logs_topic": "console_logs", "instrumentation_logs_topic": "instrumentation_logs", "performance_logs_topic": "performance_logs", "EXIT_WAIT_MS": 60000, "failedLogsDirPath": "./tmp/logs", "FAILED_LOGS_CHECK_INTERVAL": 900000, "BATCH_SIZE": 50, "FILE_UPLOAD_CUTOFF_MS": 60000, "KAFKA_UPLOADER_READ_BACK_HOURS": 3, "KAFKA_UPLOADER_READ_BACK_HOURS_EXTENDED": 7, "LOG_CHUNK_SIZE": 102400, "LOG_SIZE_LIMIT": 1048588, "LOG_DATA_MESSAGE_TYPE": {"START": 1, "STOP": 2, "ATOMIC_MESSAGE": 3, "CHUNKED_MESSAGE": 4, "CHUNK_START": 5, "CHUNK_END": 6}, "LOG_TAG": "KAFKAUPLOADER", "MASTER_ACKNOWLEDGE_TIMEOUT": 360000, "MAX_SESSION_DURATION_SECONDS": 14400, "PROCESSED_MESSAGES_TTL_SECONDS": 10, "raw_logs_topic": "raw_logs", "raw_extended_duration_logs_topic": "raw_extended_duration_logs", "REDIS_COMMITTED_KEY_NAME_SUFFIX": "__kafka_committed_offsets", "request_timeout": 30000, "restartWatchFilePath": "./tmp/restart-uploader.txt", "savingLogsDirPath": "./tmp/saving", "UNCAUGHT_EXCEPTION_ALERT_PERIOD": 300000, "S3_UPLOAD_MAX_THRESHOLD": {"raw_logs": 50, "raw_extended_duration_logs": 50, "console_logs": 200, "performance_logs": 100, "instrumentation_logs": 50}, "LOGGER_DIRECTORY_PATH": "/ebs/", "KAFKA_CONSUMER_RESUME_RATIO": 0.8, "KAFKA_CONSUMER_CHECK_INTERVAL": 10000}, "uploadWebDriverLogs": true, "alert_host": "alert-external.browserstack.com", "alert_port": 443, "alert_path": "/alert", "dns_timeout": 5000, "enable_experiment_skip_jar_delete": true, "tcg_service": {"scheme": "https", "tcg_regions": {"us-east-1": {"endpoint": "tcg.bsstag.com", "elb": "tcg.bsstag.com", "s3_upload_endpoint": "browserstack-testcasegeneration-stag-euc1.s3.eu-central-1.amazonaws.com"}, "us-west-2": {"endpoint": "tcg-usw.bsstag.com", "elb": "tcg.bsstag.com", "s3_upload_endpoint": "browserstack-testcasegeneration-stag-euc1.s3.eu-central-1.amazonaws.com"}}, "username": "root", "password": "password"}, "lcnc_groups": [7079242], "ai_failure_event_script_timeout": 1000, "ai_failure_event_page_load_timeout": 5000, "use-blocked-at-for-eventloop-stacktrace": false}