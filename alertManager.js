'use strict';

const winston = require('winston');
const constants = require('./constants');
const requestlib = require('./lib/request');
const querystring = require('querystring');

class AlertManager {
  constructor() {
    this.winston = winston;
  }

  async sendAlerts(subject, message, alertReceivers, mobile = 'critical', retries = 5, isStagingAlertDisabled = false, priority = 'P1', status = undefined, shortenedSubject = false) {
    let receivers = '';
    let shouldSendAlert = true;
    if ((isStagingAlertDisabled && !constants.isProductionEnv) || (process.env.HUB_ENV === 'testing')) {
      shouldSendAlert = false;
    }

    if (constants.isProductionEnv) {
      receivers = Array.isArray(alertReceivers) ? alertReceivers.join() : 'automate';
    } else {
      mobile = '';
      receivers = constants.exceptionAlertReceivers;
      if (this.lastAlertSent && (new Date() - this.lastAlertSent) < 30 * 60 * 1000) {
        shouldSendAlert = false;
        winston.error(`sendAlerts: not sending alert to ${receivers} with subject ${subject}`);
      }
    }

    const hostInfo = `[${constants.SERVER_NAME}][${constants.hubName}][${constants.osHostName}]`;
    let subjectToSend = `${hostInfo} ${subject}`;
    if (shortenedSubject) {
      subjectToSend = `[${constants.hubName}] ${subject}`;
      message = `${hostInfo} ${message}`;
    }

    const alertBody = {
      people: receivers,
      subject: subjectToSend,
      mobile,
      message,
      priority,
    };
    if (status) {
      alertBody.status = status;
    }
    const alertBodyToSend = querystring.stringify(alertBody);
    const alertOptions = {
      method: 'POST',
      path: constants.alert_path,
      hostname: constants.alert_host,
      port: constants.alert_port,
      body: alertBodyToSend,
      scheme: 'https',
      headers: {
        'content-type': 'application/x-www-form-urlencoded; charset=utf-8',
        accept: 'application/json',
        'content-length': Buffer.byteLength(alertBodyToSend, 'utf-8'),
      },
      delay: 5000, // Retry sending the same alert after 5 seconds
    };

    if (shouldSendAlert) {
      try {
        const { statusCode } = await requestlib.call(alertOptions, retries);
        winston.info(`sendAlerts: sent alert to ${receivers} with subject ${subjectToSend} status: ${statusCode} retriesConfigured: ${retries}`);
        this.lastAlertSent = new Date();
      } catch (alertError) {
        winston.error(`sendAlerts: got error when sending alert to ${receivers} with subject ${subjectToSend}. Error: ${alertError} retriesConfigured: ${retries}`);
      }
    }
  }
}

module.exports = AlertManager;
