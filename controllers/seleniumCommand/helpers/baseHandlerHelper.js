'use strict';

const pubSub = require('../../../pubSub');
const constants = require('../../../constants');

const checkForNonZeroStatus = (parsedData, attempt) => {
  const toReturn = {
    needToUpdateKeyObject: false,
    nonZeroStatus: undefined,
  };
  if (!parsedData.value) return toReturn;

  const stringValue = JSON.stringify(parsedData.value);
  if ([21, 28].includes(parsedData.status) && stringValue.match(/Command duration or timeout/i) && attempt > 1) {
    toReturn.needToUpdateKeyObject = true;
    toReturn.nonZeroStatus = 'command_timeout';
    return toReturn;
  }

  if (parsedData.status !== 13) return toReturn;

  if (stringValue.match(/An unknown server-side error occurred|^unknown error/i) && attempt > 1) {
    toReturn.nonZeroStatus = 'server_error';
    toReturn.needToUpdateKeyObject = true;
    return toReturn;
  }

  if (stringValue.match(/operation timed out/i)) {
    toReturn.nonZeroStatus = 'operation_timedout';
    toReturn.needToUpdateKeyObject = true;
  }
  return toReturn;
};

const updateKeyObjectForUdpKeys = (sessionKeyObj, parsedData, hash, attempt) => {
  if (!sessionKeyObj.udpKeys) return;
  let needToUpdateKeyObject = false;

  if (!sessionKeyObj.udpKeys.non_zero_status && parsedData) {
    const res = checkForNonZeroStatus(parsedData, attempt);
    if (res.needToUpdateKeyObject) {
      needToUpdateKeyObject = true;
      sessionKeyObj.udpKeys.non_zero_status = res.nonZeroStatus;
    }
  }
  if (!sessionKeyObj.udpKeys.implicit_timeout && hash === 'POST:timeouts') {
    sessionKeyObj.udpKeys.implicit_timeout = 1;
    needToUpdateKeyObject = true;
  }
  if (needToUpdateKeyObject) {
    pubSub.publish(constants.updateKeyObject, {
      session: sessionKeyObj.rails_session_id,
      changed: {
        udpKeys: sessionKeyObj.udpKeys,
      },
    });
  }
};

module.exports = {
  updateKeyObjectForUdpKeys
};
