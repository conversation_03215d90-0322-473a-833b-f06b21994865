'use strict';

const bridge = require('../../../bridge');
const requestlib = require('../../../lib/request');
const HubLogger = require('../../../log');
const constants = require('../../../constants');

const LL = constants.LOG_LEVEL;

class IOSLocationHandler {
  constructor(sessionKeyObj, request, response) {
    this.sessionKeyObj = sessionKeyObj;
    this.request = request;
    this.response = response;
  }

  static validateRequestData(data) {
    const errors = [];
    if (!Object.prototype.hasOwnProperty.call(data, 'location')) {
      errors.push('Request data must contain a \'location\' key.');
    } else {
      const location = data.location;
      if (
        !Object.prototype.hasOwnProperty.call(location, 'latitude') ||
        !Object.prototype.hasOwnProperty.call(location, 'longitude')
      ) {
        errors.push('[BROWSERSTACK_MISSING_PARAMETER] We couldn’t execute the ‘set location’ command successfully as one or more required parameters are missing. Please pass valid parameters and try again. ');
      } else if (
        Number.isNaN(Number(location.latitude)) ||
        Number.isNaN(Number(location.longitude))) {
        errors.push('[BROWSERSTACK_INVALID_VALUE_PASSED] Invalid value passed in ‘set location’ command. Please pass a valid value and try again.');
      }
    }
    return errors.length > 0 ? errors : null;
  }

  async processCommand(requestStateObj, data) {
    HubLogger.miscLogger('setLocation', `Sending setLocation request to platform session: ${this.sessionKeyObj.rails_session_id} device : ${this.sessionKeyObj.device}`, LL.DEBUG, this.sessionKeyObj.debugSession);
    try {
      const customTimeout = (this.sessionKeyObj.idle_timeout - (this.sessionKeyObj.idle_timeout > 20 ? 20 : 0)) * 1000;
      const serverURL = `/set_geolocation?device=${encodeURIComponent(this.sessionKeyObj.device)}&session_id=${encodeURIComponent(this.sessionKeyObj.rails_session_id)}&latitude=${encodeURIComponent(JSON.parse(data).location.latitude)}&longitude=${encodeURIComponent(JSON.parse(data).location.longitude)}&genre=app_automate`;
      const termOptions = {
        hostname: this.sessionKeyObj.rproxyHost,
        port: 45671,
        path: serverURL,
        timeout: customTimeout,
        headers: requestlib.appendBStackHostHeader(this.sessionKeyObj.name),
      };
      const platformResponse = await requestlib.call(termOptions);
      HubLogger.miscLogger('setLocation', `Response for setLocation from platform is ${JSON.stringify(platformResponse)}`, LL.DEBUG, this.sessionKeyObj.debugSession);
      if (platformResponse.statusCode === 200) {
        requestStateObj.data = JSON.stringify({ sessionId: this.sessionKeyObj.rails_session_id, status: 0, value: platformResponse.data });
        bridge.sendResponse(this.sessionKeyObj, requestStateObj);
      } else {
        requestStateObj.data = JSON.stringify({ sessionId: this.sessionKeyObj.rails_session_id, status: 13, value: { message: '[BROWSERSTACK_INTERNAL_ERROR] We couldn’t execute the ‘set location’ command successfully. Please try again.' } });
        requestStateObj.output = { headers: constants.CHUNKED_HEADER, statusCode: 500 };
        bridge.sendResponse(this.sessionKeyObj, requestStateObj);
      }
      requestStateObj.data = JSON.stringify({ sessionId: this.sessionKeyObj.rails_session_id, status: 0, value: null });
    } catch (err) {
      HubLogger.exceptionLogger('Error in setLocation from hub.js', err);
      requestStateObj.data = JSON.stringify({ sessionId: this.sessionKeyObj.rails_session_id, status: 14, value: { message: '[BROWSERSTACK_INTERNAL_ERROR] We couldn’t execute the ‘set location’ command successfully. Please try again.' } });
      bridge.sendResponse(this.sessionKeyObj, requestStateObj);
    }
  }
}

module.exports = { IOSLocationHandler };
