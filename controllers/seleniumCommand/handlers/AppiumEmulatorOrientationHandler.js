'use strict';

const hub = require('../../../hub');
const bridge = require('../../../bridge');
const constants = require('../../../constants');
const HubLogger = require('../../../log');
const pubSub = require('../../../pubSub');
const deviceOrientation = require('../../../services/terminal/deviceOrientation');


class AppiumEmulatorOrientationHandler {
  constructor(sessionKeyObj, request, response) {
    this.sessionKeyObj = sessionKeyObj;
    this.request = request;
    this.response = response;
  }

  processCommand(requestStateObj) {
    if (this.request.method === 'GET') {
      return this.appiumGetOrientation(requestStateObj);
    } if (this.request.method === 'POST') {
      return this.appiumSetOrientation(requestStateObj);
    }
    return true;
  }

  appiumGetOrientation(requestStateObj) {
    hub.processResponse(this.request, this.response, this.sessionKeyObj, {
      data: deviceOrientation.getEmulatorOrientation(this.sessionKeyObj),
      remoteSessionID: this.sessionKeyObj.key,
      clientSessionID: this.sessionKeyObj.rails_session_id,
      index_counter: requestStateObj.index_counter,
      callbacks: requestStateObj.callbacks,
      hostname: requestStateObj.hostname,
      originalUrl: requestStateObj.originalUrl,
    });
    return true;
  }

  async appiumSetOrientation(requestStateObj) {
    const payload = {
      data: requestStateObj.data,
      output: requestStateObj.output,
      remoteSessionID: this.sessionKeyObj.key,
      clientSessionID: this.sessionKeyObj.rails_session_id,
      index_counter: requestStateObj.index_counter,
      callbacks: requestStateObj.callbacks,
      hostname: requestStateObj.hostname,
      originalUrl: requestStateObj.originalUrl,
    };
    try {
      const targetOrientation = await deviceOrientation.setEmulatorOrientation(this.sessionKeyObj, this.request.log_data);
      pubSub.publish(constants.updateKeyObject, {
        session: this.sessionKeyObj.rails_session_id,
        changed: {
          deviceOrientation: targetOrientation,
        },
      });
      requestStateObj.data = JSON.stringify({ sessionId: this.sessionKeyObj.rails_session_id, status: 0, value: targetOrientation });
      setTimeout(() => { bridge.sendResponse(this.sessionKeyObj, requestStateObj); }, 1500);
    } catch (error) {
      HubLogger.tempExceptionLogger('Error setting orientation for emulator', error, undefined, requestStateObj.hostname, this.request.url);
      hub.processResponse(this.request, this.response, this.sessionKeyObj, payload);
    }
  }
}

module.exports = { AppiumEmulatorOrientationHandler };
