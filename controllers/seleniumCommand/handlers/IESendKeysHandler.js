'use strict';

const terminal = require('../../../services/terminal');
const constants = require('../../../constants');
const HubLogger = require('../../../log');
const bridge = require('../../../bridge');
const helper = require('../../../helper');
const pubSub = require('../../../pubSub');

const LL = constants.LOG_LEVEL;

class IESendKeysHandler {
  constructor(sessionKeyObject, request, response, options) {
    this.sessionKeyObject = sessionKeyObject;
    this.request = request;
    this.response = response;
    this.options = options;
    this.result = undefined;
  }

  processCommand(requestStateObj) {
    let logDataObj = null;
    try {
      logDataObj = JSON.parse(requestStateObj.request.log_data);
      this.logDataObj = logDataObj;
    } catch (e) {
      HubLogger.exceptionLogger(`IESendKeys: Error in parsing request for ${requestStateObj.clientSessionID} log_data: ${requestStateObj.request.log_data}`);
    }

    if (logDataObj && (logDataObj.value || logDataObj.value === '')) {
      const that = this;

      // TODO: Refact ieClick to sel service and make it async when processCommand can be made async
      // TODO: Fix this click issue if not deprecating in future
      bridge.ieClick(this.sessionKeyObject,
        requestStateObj,
        this.options,
        logDataObj,
        this.sendKeys.bind(that, requestStateObj),
        this.sendErrorResponse.bind(that, requestStateObj));
      HubLogger.miscLogger('IESendKeys request', requestStateObj.clientSessionID, LL.INFO);
      this.result = true;
    } else {
      // Carry on with selenium sendKeys
      HubLogger.exceptionLogger(`IESendKeys: Error in parsing request for ${requestStateObj.clientSessionID} keyObject: ${HubLogger.logKeyObject(this.sessionKeyObject)}`);
      this.result = false;
    }
    return this.result;
  }

  async sendKeys(requestStateObj) {
    const text = helper.getAutoitText(this.sessionKeyObject, this.logDataObj.value, pubSub);
    try {
      await terminal.autoItSendKeys(this.sessionKeyObject, requestStateObj, this.options, text);
      requestStateObj.hash = 'POST:value';
      requestStateObj.data = JSON.stringify({
        sessionId: requestStateObj.clientSessionID, status: 0, value: null
      });
      bridge.sendResponse(this.sessionKeyObject, requestStateObj);
    } catch (err) {
      this.sendErrorResponse(requestStateObj);
    }
  }

  sendErrorResponse(requestStateObj) {
    return bridge.sendErrorResponse(this.sessionKeyObject, requestStateObj);
  }
}

module.exports = { IESendKeysHandler };
