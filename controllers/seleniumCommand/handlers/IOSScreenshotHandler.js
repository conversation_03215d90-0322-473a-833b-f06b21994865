'use strict';

const terminal = require('../../../services/terminal');
const HubLogger = require('../../../log');
const hub = require('../../../hub');

class IOSScreenshotHandler {
  constructor(sessionKeyObj, request, response) {
    this.sessionKeyObj = sessionKeyObj;
    this.request = request;
    this.response = response;
  }

  async processCommand(requestStateObj) {
    const payload = {
      data: requestStateObj.data,
      output: requestStateObj.output,
      remoteSessionID: this.sessionKeyObj.key,
      clientSessionID: this.sessionKeyObj.rails_session_id,
      index_counter: requestStateObj.index_counter,
      callbacks: requestStateObj.callbacks,
      hostname: this.sessionKeyObj.name,
      originalUrl: requestStateObj.originalUrl,
    };

    try {
      const res = await terminal.realIOSScreenshot(this.sessionKeyObj);
      payload.data = res.data;
      hub.processResponse(this.request, this.response, this.sessionKeyObj, payload);
    } catch (err) {
      HubLogger.tempExceptionLogger('Error getting custom screenshot for real IOS device', err, undefined, this.sessionKeyObj.name, this.request.url);
      hub.processResponse(this.request, this.response, this.sessionKeyObj, payload);
    }
    return { returns: true };
  }
}

module.exports = { IOSScreenshotHandler };
