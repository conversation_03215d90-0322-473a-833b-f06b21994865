'use strict';

const terminal = require('../../../services/terminal');
const constants = require('../../../constants');
const HubLogger = require('../../../log');
const bridge = require('../../../bridge');
const hub = require('../../../hub');
const helper = require('../../../helper');

const LL = constants.LOG_LEVEL;

class UrlHttpsAuthHandler {
  constructor(sessionKeyObject, request, response) {
    this.sessionKeyObject = sessionKeyObject;
    this.request = request;
    this.response = response;
  }

  // If selenium jar responds for navigating to url before timeout, we don't need to cancel popup. This timeout will be defined and cleared.
  // If selenium jar doesn't respond to navigating to url before timeout, timeout won't be cleared, we call cancel popup, and then retry here.
  processCommand(requestStateObj, callbacks, attempt, indexCounter, reqDataRetry) {
    requestStateObj.safariPopupCheckTimeout = setTimeout(async () => {
      await this.cancelPopupAndSendResponse(requestStateObj, callbacks, attempt, indexCounter, reqDataRetry);
    }, constants.SAFARI_CANCEL_TIMEOUT);
  }

  // eslint-disable-next-line consistent-return
  async cancelPopupAndSendResponse(requestStateObj, callbacks, attempt, indexCounter, reqDataRetry) {
    requestStateObj.safariPopupCheckTimeout = undefined;
    try {
      const res = await terminal.cancelSafariPopup(this.sessionKeyObject);
      helper.addToJarTime(this.sessionKeyObject.rails_session_id, res);
      if (res.data.indexOf('no popup') > -1) requestStateObj.hash = 'POST:url';
      if (requestStateObj.hash === 'POST:url-https-auth') {
        return hub.createBridgeClientAndNode(this.sessionKeyObject, this.request, this.response, callbacks, attempt + 1, indexCounter, reqDataRetry);
      }
      // why is this condition present?
      if (requestStateObj.returned) {
        bridge.sendResponse(this.sessionKeyObject, requestStateObj);
      }
    } catch (err) {
      HubLogger.miscLogger('Error during cancelling Safari Popup', err.toString(), LL.INFO);
    }
  }
}

module.exports = { UrlHttpsAuthHandler };
