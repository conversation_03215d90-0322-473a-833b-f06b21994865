'use strict';

const selenium = require('../../../services/selenium');
const hub = require('../../../hub');

class ChromeMacMaximizeHandler {
  constructor(sessionKeyObj, request, response) {
    this.sessionKeyObj = sessionKeyObj;
    this.request = request;
    this.response = response;
  }

  async processCommand(requestStateObj) {
    const maximizeResp = selenium.chromeMacMaximize();

    hub.processResponse(this.request, this.response, this.sessionKeyObj, {
      data: JSON.stringify(maximizeResp),
      remoteSessionID: this.sessionKeyObj.key,
      clientSessionID: this.sessionKeyObj.rails_session_id,
      index_counter: requestStateObj.index_counter,
      callbacks: requestStateObj.callbacks,
      hostname: requestStateObj.hostname,
      originalUrl: requestStateObj.originalUrl,
    });
    return { returns: true };
  }
}

module.exports = { ChromeMacMaximizeHandler };
