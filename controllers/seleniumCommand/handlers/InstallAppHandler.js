'use strict';

const bridge = require('../../../bridge');
const helper = require('../../../helper');
const appsService = require('../../../services/terminal/apps');
const HubLogger = require('../../../log');
const constants = require('../../../constants');
const { isString } = require('../../../typeSanity');

const LL = constants.LOG_LEVEL;

class InstallAppHandler {
  constructor(sessionKeyObj, request, response) {
    this.sessionKeyObj = sessionKeyObj;
    this.request = request;
    this.response = response;
  }

  /* eslint-disable max-lines-per-function */
  async processCommand(requestStateObj, reqData) {
    const reqDataObj = JSON.parse(reqData);

    helper.pushToCLS('request_for_install_app', {
      session_id: this.sessionKeyObj.rails_session_id,
      user_id: this.sessionKeyObj.user,
      post_request: reqDataObj
    }, this.sessionKeyObj.appTesting);

    if (!helper.nestedKeyValue(reqDataObj, ['appPath'])) {
      if (Array.isArray(reqDataObj.args) && reqDataObj.args[0] && Object.prototype.hasOwnProperty.call(reqDataObj.args[0], 'appPath')) {
        reqDataObj.appPath = reqDataObj.args[0].appPath; // Handle script format of installApp
      }
    }

    if (helper.nestedKeyValue(reqDataObj, ['appPath']) && isString(reqDataObj.appPath) && reqDataObj.appPath.match(/^bs:\/\//)) {
      // If install_app is called with bs_hashed_id
      HubLogger.miscLogger('InstallApp', `Sending installApp request to platform session: ${this.sessionKeyObj.rails_session_id} device : ${this.sessionKeyObj.device}`, LL.DEBUG, this.sessionKeyObj.debugSession);
      try {
        const installAppResponse = await appsService.installApp(this.sessionKeyObj, reqDataObj);
        HubLogger.miscLogger('InstallApp', `Response for installApp from platform is ${JSON.stringify(installAppResponse)}`, LL.DEBUG, this.sessionKeyObj.debugSession);
        if (installAppResponse.statusCode === 200) {
          requestStateObj.data = JSON.stringify({ sessionId: this.sessionKeyObj.rails_session_id, status: 0, value: installAppResponse.data });
        } else {
          requestStateObj.data = JSON.stringify({ sessionId: this.sessionKeyObj.rails_session_id, status: 13, value: { message: installAppResponse.data } });
          requestStateObj.output = { headers: constants.CHUNKED_HEADER, statusCode: 500 };
        }
      } catch (err) {
        HubLogger.tempExceptionLogger('Error in installApp from hub.js', err);
        requestStateObj.data = JSON.stringify({ sessionId: this.sessionKeyObj.rails_session_id, status: 13, value: null });
        requestStateObj.output = { headers: constants.CHUNKED_HEADER, statusCode: 500 };
      } finally {
        bridge.sendResponse(this.sessionKeyObj, requestStateObj);
      }
      return { returns: true };
    } else if (helper.nestedKeyValue(reqDataObj, ['appPath']) && this.sessionKeyObj.realMobile && helper.isAppleOs(this.sessionKeyObj.os)) {
      // If public url or path is passed for iOS we give error to user
      const installAppError = 'An unknown server-side error occurred while processing the command. For using install app command for iOS on BrowserStack, please use \'otherApps\' or \'midSessionInstallApps\' capability and pass the app_url(of the app to be installed) inside install app command. Example: driver.install_app(\'bs://hashedid\').';
      requestStateObj.data = JSON.stringify({ sessionId: this.sessionKeyObj.rails_session_id, status: 13, value: { message: installAppError } });
      bridge.sendResponse(this.sessionKeyObj, requestStateObj);
      return { returns: true };
    }
    return { returns: false };
  }
  /* eslint-enable max-lines-per-function */
}

module.exports = { InstallAppHandler };
