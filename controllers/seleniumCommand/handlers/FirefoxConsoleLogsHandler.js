'use strict';

const hub = require('../../../hub');
const { AdditionalSessionLogs } = require('../../../services/terminal/stopSessionInterface/AdditionalSessionLogs');


class FirefoxConsoleLogs {
  constructor(sessionKeyObj, request, response) {
    this.sessionKeyObj = sessionKeyObj;
    this.request = request;
    this.response = response;
  }

  async processCommand(requestStateObj) {
    const sessionLogs = new AdditionalSessionLogs(this.sessionKeyObj);
    const logData = await sessionLogs.retrieveConsoleLogs();
    hub.processResponse(this.request, this.response, this.sessionKeyObj, {
      data: JSON.stringify(logData),
      output: requestStateObj.output,
      remoteSessionID: this.sessionKeyObj.key,
      clientSessionID: this.sessionKeyObj.rails_session_id,
      index_counter: requestStateObj.index_counter,
      callbacks: requestStateObj.callbacks,
      hostname: requestStateObj.hostname,
      originalUrl: requestStateObj.originalUrl,
    });
  }
}

module.exports = { FirefoxConsoleLogs };
