'use strict';

const bridge = require('../../../bridge');
const HubLogger = require('../../../log');
const constants = require('../../../constants');
const SeleniumClient = require('../../../seleniumClient');

const LL = constants.LOG_LEVEL;
class GetTypesHandler {
  constructor(sessionKeyObj, request, response) {
    this.sessionKeyObj = sessionKeyObj;
    this.request = request;
    this.response = response;
    this.selenium = new SeleniumClient(sessionKeyObj);
  }

  async processCommand(requestStateObj, handleSeLogType = false) {
    HubLogger.miscLogger('getTypes', `Sending getTypes request to platform session: ${this.sessionKeyObj.rails_session_id} device : ${this.sessionKeyObj.device}`, LL.DEBUG, this.sessionKeyObj.debugSession);
    try {
      let value;
      if (handleSeLogType) {
        value = await this.handleSeLogTypeCmd();
      } else {
        value = await this.handleLogTypeCmd();
      }
      requestStateObj.data = JSON.stringify({ sessionId: this.sessionKeyObj.rails_session_id, status: 0, value });
    } catch (err) {
      HubLogger.exceptionLogger('Error in getTypes from hub.js', err);
      requestStateObj.data = JSON.stringify({ sessionId: this.sessionKeyObj.rails_session_id, status: 13, value: null });
    } finally {
      bridge.sendResponse(this.sessionKeyObj, requestStateObj);
    }
  }

  async handleLogTypeCmd() {
    let value;
    if (this.sessionKeyObj.os === 'android') {
      // Static response because /se/log/types responds empty
      value = ['browser', 'driver'];
    } else if (['firefox', 'safari'].includes(this.sessionKeyObj.browser)) {
      // Static response because /se/log/types responds empty or throws error
      value = ['server'];
    } else {
      value = (await this.selenium.getTypes()).value;
    }
    return value;
  }

  async handleSeLogTypeCmd() {
    let value;
    if (this.sessionKeyObj.os === 'android') {
      // Static response because /se/log/types responds empty
      value = ['browser', 'driver'];
    } else {
      value = (await this.selenium.getLogTypesMobile()).value;
    }
    return value;
  }
}

module.exports = { GetTypesHandler };
