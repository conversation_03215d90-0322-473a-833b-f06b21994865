'use strict';

const bridge = require('../../../bridge');
const constants = require('../../../constants');

class DesktopOrientationHandler {
  constructor(sessionKeyObj, request, response) {
    this.sessionKeyObj = sessionKeyObj;
    this.request = request;
    this.response = response;
  }

  async processCommand(requestStateObj) {
    requestStateObj.data = JSON.stringify({
      sessionId: this.sessionKeyObj.rails_session_id,
      status: 0,
      value: constants.UNHANDLED_SELENIUM_COMMAND_RESPONSE.desktopOrientation
    });
    bridge.sendResponse(this.sessionKeyObj, requestStateObj);
  }
}

module.exports = { DesktopOrientationHandler };
