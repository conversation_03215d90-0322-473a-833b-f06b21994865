'use strict';

const helper = require('../../../helper');
const HubLogger = require('../../../log');
const bridge = require('../../../bridge');
const { isString } = require('../../../typeSanity');
const constants = require('../../../constants');
const errorMessages = require('../../../errorMessages');

class AndroidPushFileHandler {
  constructor(sessionKeyObj, request, response) {
    this.sessionKeyObj = sessionKeyObj;
    this.request = request;
    this.response = response;
  }

  processCommand(requestStateObj, reqData) {
    let pushPath;
    try {
      const reqDataObj = JSON.parse(reqData);
      if (!helper.nestedKeyValue(reqDataObj, ['path']) && Array.isArray(reqDataObj.args) && reqDataObj.args[0] && Object.prototype.hasOwnProperty.call(reqDataObj.args[0], 'remotePath')) {
        reqDataObj.path = reqDataObj.args[0].remotePath;
      }
      pushPath = reqDataObj.path;
    } catch (err) {
      HubLogger.exceptionLogger(`Exception while parsing request data to JSON Object. Exception: ${err}\n KeyObject: ${HubLogger.logKeyObject(this.sessionKeyObj)}`);
      requestStateObj.data = JSON.stringify({ sessionId: this.sessionKeyObj.rails_session_id, status: 13, value: { message: 'Invalid format of the request data, require JSON. Please check the request data.' } });
      bridge.sendResponse(this.sessionKeyObj, requestStateObj);
      return { returns: true };
    }
    if (isString(pushPath)) {
      // If the path is not whitelisted.
      const trimmedPushPath = pushPath.trim();
      const isAllowedPushPath = constants.whitelistedAppiumPushFilePaths.some(whitelistedPath => trimmedPushPath.startsWith(whitelistedPath));
      if (!isAllowedPushPath) {
        helper.pushToCLS('browserstack_invalid_file_path', { session_id: this.sessionKeyObj.rails_session_id, user_id: this.sessionKeyObj.user, response: trimmedPushPath, }, true);
        let browserstackInvalidFilePathError = errorMessages.COMMON_PUSH_FILE_ERROR;
        const osVersion = helper.getOsVersion(this.sessionKeyObj.deviceName);
        if (osVersion < 11.0) browserstackInvalidFilePathError += errorMessages.ANDROID_11_BELOW_PUSH_FILE_ERROR;
        if (this.request.is_app_automate_session) browserstackInvalidFilePathError += errorMessages.APP_AUTOMATE_PUSH_FILE_LINK;
        requestStateObj.data = JSON.stringify({ sessionId: this.sessionKeyObj.rails_session_id, status: 13, value: { message: browserstackInvalidFilePathError } });
        bridge.sendResponse(this.sessionKeyObj, requestStateObj);
        return { returns: true };
      }
    }
    return { returns: false };
  }
}

module.exports = { AndroidPushFileHandler };
