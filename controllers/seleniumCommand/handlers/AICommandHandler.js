'use strict';

const SeleniumClient = require('../../../seleniumClient');
const HubLogger = require('../../../log');
const constants = require('../../../constants');
const {
  isUndefined, isNotUndefined, isTrueString
} = require('../../../typeSanity');
const {
  getRedisKey,
  constructCommand,
  TCG_ENDPOINTS,
  TCG_HEADERS,
  PUBLISH_AI_SUCCESS,
  PUBLISH_AI_MID_SESSION_FAILURE,
  getSanitisedString,
  updateAIHealingDetails
} = require('../helpers/AICommandHelper');
const AICommandHelper = require('../helpers/AICommandHelper');
const { promisify } = require('util');
const helper = require('../../../helper');

const sleep = promisify(setTimeout);

const TAG = 'AICommandHandler';
const LL = constants.LOG_LEVEL;

/**
 * Represents a handler for AI commands in SeleniumHub.
 */
class AICommandHandler {
  /**
   * Constructs a new AICommandHandler object.
   * @param {Object} sessionKeyObj - The session key object.
   * @param {Object} request - The request object.
   * @param {Object} response - The response object.
   */
  constructor(sessionKeyObj, request, response) {
    this.sessionKeyObj = sessionKeyObj;
    this.request = request;
    this.response = response;
    this.seleniumClient = new SeleniumClient(sessionKeyObj);
    this.isW3CDialect = (sessionKeyObj.dialect === 'W3C');
  }


  /**
   * Processes the parameters to extract the shadow DOM parameters
   * @param {Object} params - The selenium command request parameters passed from hub to jar.
   * @param {Object} output - The output received from the jar.
  */
  async getShadowDomParam(params, output) {
    const path = this.request.url;
    let rootId;
    let isGetShadowRoot = false;
    if (path.match(/session\/[^/]+\/element\/[^/]+\/shadow$/)) {
      isGetShadowRoot = true;
    }
    if (path.match(/session\/[^/]+\/element\/[^/]+\/shadow$/) || path.match(/session\/[^/]+\/shadow\/[^/]+\/element$/) || path.match(/session\/[^/]+\/element\/[^/]+\/element$/)) {
      const parts = path.split('/');
      rootId = parts[parts.length - 2];
    }
    let referenceId;
    try {
      let data = JSON.parse(output.data);
      data = data.value;
      referenceId = Object.values(data)[0];
    } catch (e) {
      HubLogger.newCGLogger(TAG, 'Error in extracting ReferenceID');
    }
    return { rootId, referenceId, isGetShadowRoot };
  }

  async sendEdsEvent(start, end) {
    const healingDuration = end - start;
    const edsData = {
      kind: 'tcg_frontend_app_logs',
      build_id: undefined,
      app_version: '101',
      message: '',
      log_level: 'time',
      url: '',
      session_id: this.sessionKeyObj.rails_session_id,
      timestamp: new Date(),
      createdAt: new Date().getTime(),
      module: 'total_healing_time',
      team: 'CTOI',
      product: 'Healing',
      additional_info: JSON.stringify({
        total_healing_time: healingDuration
      })
    };
    helper.sendToEDS(edsData);

    // Update the total healing duration in ai_healing_details using the helper function
    AICommandHelper.updateAIHealingDuration(this.sessionKeyObj, healingDuration);
  }

  /**
   * Processes the response received for AI specific commands and pass events to extension through jar.
   * @param {Object} output - The output received from the jar.
   * @param {Object} params - The selenium command request parameters passed from hub to jar.
   * @returns {Promise<void>} - A promise that resolves when the response is processed.
   */
  // eslint-disable-next-line complexity
  /*  eslint-disable dot-notation */
  /* eslint max-lines-per-function: ["error", 40] */
  /* eslint-disable max-lines-per-function */
  async processResponse(output, params) {
    const response = {
      modifyResponse: false,
    };
    const sessionDetails = this.sessionKeyObj.aiSessionDetails;
    let failureResponse = {};
    // Surrounding with try catch for exceptions not to affect session flow
    try {
      if (!(isTrueString(sessionDetails.enable_ai_healing) || isTrueString(sessionDetails.ai_soft_heal))) {
        return response;
      }
      updateAIHealingDetails(this.sessionKeyObj, 'total_healing_enabled_request');
      if (output.statusCode === 200) {
        // element found
        if (constants.DO_CALL_TO_AI_EXTENSION_IN_ASYNC) {
          this.callSuccessEvent(params, output);
        } else {
          await this.callSuccessEvent(params, output);
        }
      } else if ((output.statusCode === 404 && this.isW3CDialect) || // element not found W3C
            (output.statusCode === 500 && !this.isW3CDialect)) { // element not found OSS
        const start = new Date().getTime();
        updateAIHealingDetails(this.sessionKeyObj, 'total_healing_request');
        failureResponse = await this.callFailureEvent(params, output);
        const end = new Date().getTime();
        this.sendEdsEvent(start, end);
        if (failureResponse.preCheckFailed.toString() === 'false') {
          if (failureResponse.healingSuccess.toString() === 'true') {
            response.modifyResponse = true;
            response.modifyData = failureResponse.healingResponse;
            response.aiRetryCount = failureResponse.aiRetryCount;
            updateAIHealingDetails(this.sessionKeyObj, 'healing_success_count');
          } else {
            response.aiFailureEvent = true;
            response.aiRetryCount = failureResponse.aiRetryCount;
            if (failureResponse.scriptExecError.toString() === 'false') { // marking failure count, only if it was not due to script execution error
              updateAIHealingDetails(this.sessionKeyObj, 'healing_failure_count');
            }
          }
        }
      }
    } catch (e) {
      HubLogger.newCGLogger(TAG, `Error in AI processResponse with error: ${e}`, LL.ERROR, this.sessionKeyObj.rails_session_id);
      helper.PingZombie({
        kind: 'ai-process-response-error',
        session_id: this.sessionKeyObj.rails_session_id,
        data: { error_code: e.code, error_type: e.type, ai_details: sessionDetails },
        error: e.toString()
      });
    }
    HubLogger.newCGLogger(TAG, `Ai call: Command Status - ${output.statusCode}, AI Response ${JSON.stringify(response)} TCG response - ${JSON.stringify(failureResponse)}`, LL.INFO, this.sessionKeyObj.rails_session_id);
    return response;
  }

  /**
   * Executes a Selenium command event.
   * @param {string} event - The command event to execute.
   * @param {string} type - The type of the command event.
   * @param {number} [retries=0] - The number of retries to execute the event (default is 0).
   * @returns {Promise<void>} - A promise that resolves when the event is executed successfully.
   */
  async callEvent(event, type, retries = 0) {
    if (retries > 1) {
      // couple of retries to execute the event
      return null;
    }
    let res = null;
    try {
      if (constants.USE_SELENIUM_EXECUTE_ASYNC_IN_AI) {
        event = `let done = arguments[0]; ${event}; done();`;
        res = await this.seleniumClient.executeScriptAsync({ script: event, args: [] }, { returnResp: true, parseRespose: false });
      } else {
        res = await this.seleniumClient.executeScript({ script: event, args: [] }, { returnResp: true, parseRespose: false });
      }
    } catch (e) {
      HubLogger.newCGLogger(TAG, `Error in executing ${type} event: ${event} with error: ${e} and retries: ${retries}`, LL.ERROR, this.sessionKeyObj.rails_session_id);
      res = await this.callEvent(event, type, retries + 1);
      return res;
    }
    HubLogger.newCGLogger(TAG, `Result of  ${type} event: ${res} for event: ${event} and retries: ${retries}, executeasync: ${constants.USE_SELENIUM_EXECUTE_ASYNC_IN_AI}, aicallasync: ${constants.DO_CALL_TO_AI_EXTENSION_IN_ASYNC}`, LL.DEBUG, this.sessionKeyObj.rails_session_id);
    return res;
  }

  /**
   * Calls the success event for AI extension.
   * @param {Object} params - The selenium command request parameters passed from hub to jar.
   * @returns {Promise<void>} - A promise that resolves when the success event is called.
   */
  async callSuccessEvent(params, output) {
    const { rootId, referenceId, isGetShadowRoot } = await this.getShadowDomParam(params, output);
    const sessionDetails = this.sessionKeyObj.aiSessionDetails; // to get the details of the session
    let body; let using; let value;
    const listOfCommands = await this.constructor.getCommandRedis(this.sessionKeyObj);
    if (!isGetShadowRoot) {
      body = JSON.parse(params.body); // to get the details of the element
      using = getSanitisedString(body.using);
      value = getSanitisedString(body.value);
    } else {
      using = '';
      value = '';
    }

    const aiTcgDetails = this.sessionKeyObj.aiTcgDetails.replace(/'/g, '\\\'').replace(/"/g, '\\"');
    const event = `window.dispatchEvent(new CustomEvent('ai-heal-find-element-success',{ detail: {'${using}': '${value}', testName: '${sessionDetails.session_name}', projectName: '${sessionDetails.project_name}', groupId: '${this.sessionKeyObj.group_id}', listOfCommands: '[${listOfCommands}]', sessionId: '${this.sessionKeyObj.rails_session_id}', tcgDetails: '${aiTcgDetails}', rootId: '${rootId}', referenceId: '${referenceId}', isGetShadowRoot: ${isGetShadowRoot}, isTestBedDataCollectionEnabled: ${sessionDetails.is_test_bed_data_collection_enabled} }}))`;
    await this.callEvent(event, 'success');
  }

  /**
   * Calls the failure event for AI extension.
   * @param {Object} params - The selenium command request parameters passed from hub to jar.
   * @returns {Promise<void>} - A promise that resolves when the failure event is called.
   */
  /* eslint-disable max-lines-per-function */
  async callFailureEvent(params, output = {}) {
    const sessionDetails = this.sessionKeyObj.aiSessionDetails; // to get the details of the session
    const body = JSON.parse(params.body); // to get the details of the element
    const listOfCommands = await this.constructor.getCommandRedis(this.sessionKeyObj);
    const using = getSanitisedString(body.using);
    const value = getSanitisedString(body.value);
    const aiTcgDetails = this.sessionKeyObj.aiTcgDetails.replace(/'/g, '\\\'').replace(/"/g, '\\"');
    // We will dispatch failure event and wait for extension to trigger the conn est event, in that case, we resolve it to true and fire call to tcg and skip in case if not received.
    const findElementTimeout = isTrueString(sessionDetails.implicit_delay_enabled) ? 0 : sessionDetails.find_element_timeout;
    const event = `
    return new Promise((resolve) => {
        (async function() {
            try {
                let detail = {
                    '${using}': '${value}',
                    testName: '${sessionDetails.session_name}',
                    projectName: '${sessionDetails.project_name}',
                    groupId: '${this.sessionKeyObj.group_id}',
                    listOfCommands: '[${listOfCommands}]',
                    sessionId: '${this.sessionKeyObj.rails_session_id}',
                    groupAIEnabled: '${sessionDetails.tcg_ai_enabled}',
                    tcgDetails: '${aiTcgDetails}',
                    grrRegion: '${sessionDetails.grr_region}',
                    findElementTimeout: '${findElementTimeout}',
                    isTestBedDataCollectionEnabled: ${sessionDetails.is_test_bed_data_collection_enabled},
                };
                const pageLoadTimeOutMs = ${constants.AI_FIND_ELEMENT_FAILURE_PAGE_LOAD_TIMEOUT};

                function waitForEvent(eventName, timeout) {
                    return new Promise((res) => {
                        let done = false;

                        const handler = (event) => {
                            if (done) return;
                            done = true;
                            clearTimeout(timer);
                            window.removeEventListener(eventName, handler);
                            res({
                                received: true,
                                detail: event.detail
                            });
                        };

                        const timer = setTimeout(() => {
                            if (done) return;
                            done = true;
                            window.removeEventListener(eventName, handler);
                            res({
                                received: false
                            });
                        }, timeout);

                        window.addEventListener(eventName, handler);
                    });
                }

                // Step 1: Wait for 'ext-connection-established-result' event
                const connectionPromise = waitForEvent("ext-connection-established-result", ${constants.AI_FIND_ELEMENT_FAILURE_SCRIPT_TIMEOUT});

                // Step 2: Dispatch 'ext-connection-established' event
                window.dispatchEvent(new CustomEvent('ext-connection-established', {}));

                // Step 3: Wait for the connection result
                const connectionResult = await connectionPromise;

                if (!connectionResult.received || !connectionResult.detail?.status) {
                    resolve({
                        'triggerTCG': false,
                        'preCheckFailed': false,
                        'url': window.location.href || "",
                        'preCheckReason': "",
                        'errMsg': "Connection not established within timeout.",
                    });
                }

                // Step 4: Dispatch 'ai-self-heal-pre-check' event
                window.dispatchEvent(new CustomEvent('ai-self-heal-pre-check', {
                    detail: {
                        ...detail,
                        pageLoadTimeOutMs: pageLoadTimeOutMs
                    }
                }));

                // Step 5: Wait for 'ai-self-heal-pre-check-result' event
                const preCheckResult = await waitForEvent("ai-self-heal-pre-check-result", pageLoadTimeOutMs + 2000);

                if (preCheckResult.received) {
                    if (preCheckResult.detail && preCheckResult.detail.status) {
                        // Pre-Check Failed
                        resolve({
                            'triggerTCG': true,
                            'preCheckFailed': preCheckResult.detail.status,
                            'url': preCheckResult.detail?.url || "",
                            'preCheckReason': preCheckResult.detail?.reason || "",
                            'errMsg': preCheckResult.detail?.errMsg || "",
                        });
                    } else {
                        // Pre-Check Passed
                        // Dispatch Find Element Failure Event
                        window.dispatchEvent(new CustomEvent('ai-heal-find-element-failure', {
                            detail
                        }));
                        resolve({
                            'triggerTCG': true,
                            'preCheckFailed': false,
                            'url': preCheckResult.detail?.url || "",
                            'preCheckReason': preCheckResult.detail?.reason || "",
                            'errMsg': preCheckResult.detail?.errMsg || "",
                        });
                    }
                } else {
                    resolve({
                        'triggerTCG': true,
                        'preCheckFailed': true,
                        'url': window.location.href || "",
                        'preCheckReason': "Timeout while waiting for pre-check result",
                        'errMsg': "",
                    });
                }
            } catch (err) {
                resolve({
                    'triggerTCG': false,
                    'preCheckFailed': false,
                    'url': window.location.href || "",
                    'preCheckReason': "",
                    'errMsg': err.toString(),
                });
            }
        })();
    });
      `;
    let failureEventResponse = {
      value: {
        triggerTCG: false,
        preCheckFailed: false,
        url: '',
        preCheckReason: '',
        errMsg: '',
      }
    };
    try {
      failureEventResponse = await this.callEvent(event, 'failure');
      if (failureEventResponse && typeof failureEventResponse === 'string') {
        failureEventResponse = JSON.parse(failureEventResponse);
      }
    } catch (error) {
      HubLogger.newCGLogger(
        TAG,
        `Error while fetching response for callFailureEvent : ${error}`,
        LL.ERROR,
        this.sessionKeyObj.rails_session_id
      );
      helper.PingZombie({
        kind: 'ai-find-element-event-response-parse-error',
        session_id: this.sessionKeyObj.rails_session_id,
        data: {
          error_code: error.code,
          error_type: error.type,
          failureEventResponse,
          ai_details: this.sessionKeyObj.aiSessionDetails,
        },
        error: error.toString()
      });
    }
    const defaultResponseForNonTCG = {
      healingSuccess: false,
      healingResponse: {},
      aiRetryCount: 0,
      triggerTCG: failureEventResponse.value.triggerTCG,
      preCheckFailed: failureEventResponse.value.preCheckFailed,
      scriptExecError: false,
    };
    // Make call to TCG service based on callEvent response
    if (failureEventResponse.value && failureEventResponse.value.triggerTCG && !failureEventResponse.value.preCheckFailed) {
      return this.handleFindElementFailure(output);
    }
    if (failureEventResponse.value && failureEventResponse.value.preCheckFailed) {
      this.sessionKeyObj.aiHealingCommand = false;
      updateAIHealingDetails(this.sessionKeyObj, 'pre_check_failure_count');
      HubLogger.newCGLogger(
        'AI_HEALING_DETAILS',
        JSON.stringify({
          type: 'pre_check_failure',
          reason: failureEventResponse.value.preCheckReason,
          url: failureEventResponse.value.url,
          error: failureEventResponse.value.errMsg,
        }),
        LL.ERROR,
        this.sessionKeyObj.rails_session_id
      );
    } else { // failure script or connection error
      this.sessionKeyObj.aiHealingCommand = true; // Used for pushing to raw logs
      this.sessionKeyObj.aiSessionDetails.isHealSkipped = true; // Used for publishing to raw logs
      updateAIHealingDetails(this.sessionKeyObj, 'script_exec_error_count');
      HubLogger.newCGLogger(
        'AI_HEALING_DETAILS',
        JSON.stringify({
          type: 'script_exec_error',
          url: failureEventResponse.value.url,
          error: failureEventResponse.value.errMsg,
        }),
        LL.ERROR,
        this.sessionKeyObj.rails_session_id
      );
      defaultResponseForNonTCG.scriptExecError = true; // updating, so we don't increment healing failure count as well
    }
    if (!isTrueString(this.sessionKeyObj.midSessionHealingDisabled)) {
      this.sessionKeyObj.midSessionHealingDisabled =
        PUBLISH_AI_MID_SESSION_FAILURE; // Used to send to stop request and instrument in feature_usage in rails
    }
    return defaultResponseForNonTCG;
  }

  /* eslint-disable max-lines-per-function */
  /* eslint-disable dot-notation */
  /* eslint-disable-next-line complexity */
  async handleFindElementFailure(output = {}) {
    const response = {
      healingSuccess: false,
      healingResponse: {},
      aiRetryCount: 0,
      preCheckFailed: false,
      scriptExecError: false
    };
    const sessionDetails = this.sessionKeyObj.aiSessionDetails;
    this.sessionKeyObj.aiHealingCommand = true; // Used for checking in raw logs
    const data = `{"data": {"sessionId": "${this.sessionKeyObj.rails_session_id}"}}`;
    const tcgStartTime = Date.now();
    const { success, data: tcgResponse, additionalData: tcgAdditionalData } = await this.getResultsFromTcg(data, TCG_ENDPOINTS.GET_RESULT.retries);
    response.tcgData = { success, tcgResponse, tcgAdditionalData };
    if (constants.global_registry && isNotUndefined(constants.global_registry[this.sessionKeyObj.rails_session_id])) {
      constants.global_registry[this.sessionKeyObj.rails_session_id]['automate_tcg_duration'] += (Date.now() - tcgStartTime);
    }
    try {
      if (success.toString() === 'true' && isNotUndefined(tcgResponse)) {
        let using;
        let value;
        const foundInTimeout = !isTrueString(sessionDetails.implicit_delay_enabled) && isNotUndefined(tcgAdditionalData) ? isTrueString(tcgAdditionalData.foundInTimeout) : false;
        if (foundInTimeout) {
          const userSelector = JSON.parse(this.request.log_data);
          ({ using, value } = userSelector);
          delete this.sessionKeyObj.aiHealingCommand; // Remove from raw logs
        } else {
          using = Object.keys(tcgResponse).shift();
          value = tcgResponse[using];
        }

        if (isUndefined(using) || isUndefined(value)) {
          return response;
        }
        let jarResponse;
        let shadowId;
        if (isTrueString(sessionDetails.ai_soft_heal) && !foundInTimeout) {
          const jarData = JSON.parse(output.data);
          jarData.healedSelector = {
            using,
            value,
          };
          jarResponse = {
            data: JSON.stringify(jarData),
          };
          response.healingSuccess = true; // Setting this to true for modification of response
          response.healingResponse = jarResponse;
          if (!isTrueString(this.sessionKeyObj.softHealingSuccess)) {
            this.sessionKeyObj.softHealingSuccess = PUBLISH_AI_SUCCESS;
          }
        } else {
          const path = this.request.url;
          if (path.match(/session\/[^/]+\/shadow\/[^/]+\/element$/)) {
            const parts = path.split('/');
            shadowId = parts[parts.length - 2];
            jarResponse = await this.seleniumClient.findShadowElement(using, value, shadowId, { completeJarResponse: true });
          } else {
            jarResponse = await this.seleniumClient.findElement(using, value, { completeJarResponse: true });
          }
          if (jarResponse.statusCode === 200) {
            delete jarResponse.dnsData;
            /* Adding healed selector data for adding to raw logs, processed in "retrieveRawLogData" method */
            const jarData = JSON.parse(jarResponse.data);
            jarData.healedSelector = {
              using,
              value,
            };
            jarResponse.data = JSON.stringify(jarData);
            response.healingSuccess = true;
            response.healingResponse = jarResponse;
            if (!isTrueString(this.sessionKeyObj.selfHealingSuccess) && !foundInTimeout) {
              this.sessionKeyObj.selfHealingSuccess = PUBLISH_AI_SUCCESS;
            }
          }
        }
        response.aiRetryCount = tcgResponse.aiRetryCount;
      }
    } catch (error) {
      HubLogger.newCGLogger(TAG, `Error while sending healed command : ${error}`, LL.ERROR, this.sessionKeyObj.rails_session_id);
    }
    return response;
  }

  async getLocalStorageResults() {
    const tcgResults = {
      statusCode: 404,
      data: '{}'
    };
    const event = `async function getLocalStorageValue(){
      let retry = 40;
      const key = 'healing:result:${this.sessionKeyObj.rails_session_id}';
      while(retry>0){
          let data = window.localStorage.getItem(key);
          if (data===null || data === undefined){
              let now = new Date().getTime();
              await new Promise((resolve) => setTimeout(resolve, 500));
              retry-=1
          }
          else{
              window.localStorage.removeItem(key)
              return data;
          }
        }
        return null;
      }
       return await getLocalStorageValue()
    `;
    const res = await this.callEvent(event, 'localStorage');
    if (res) {
      const parsedResult = JSON.parse(JSON.parse(res).value);
      if (parsedResult && parsedResult.robust && parsedResult.robust.locator === 'cssPath') {
        tcgResults.data = JSON.stringify({
          data: {
            'css selector': parsedResult.robust.path,
          },
          success: true,
        });
        tcgResults.statusCode = 200;
      } else if (parsedResult && parsedResult.robust && parsedResult.robust.locator === 'xpath') {
        tcgResults.data = JSON.stringify({
          data: {
            xpath: parsedResult.robust.path
          },
          success: true,
        });
        tcgResults.statusCode = 200;
      } else if (parsedResult) {
        tcgResults.data = JSON.stringify({
          data: parsedResult,
          success: true
        });
        tcgResults.statusCode = 200;
      }
    }
    HubLogger.newCGLogger(TAG, ` localStorageResults: ${res} localStorageResultsTcg: ${tcgResults} `, this.sessionKeyObj.rails_session_id);
    return tcgResults;
  }
  /* eslint-enable max-lines-per-function */

  async getResultsFromTcg(data, retries) {
    /* eslint-disable no-await-in-loop */
    const sessionDetails = this.sessionKeyObj.aiSessionDetails;
    if (sessionDetails.ai_ext_localstorage_flag) {
      retries = AICommandHelper.LOCAL_STORAGE_RETRIES;
    }
    const totalRetries = retries;
    while (retries >= 1) {
      try {
        let response = { data: '{}' };
        if (sessionDetails.ai_ext_localstorage_flag) {
          response = await this.getLocalStorageResults();
        } else {
          response = await AICommandHelper.makeRequestTcg(TCG_ENDPOINTS.GET_RESULT.method,
            TCG_ENDPOINTS.GET_RESULT.path, data, TCG_HEADERS, this.sessionKeyObj.aiTcgHostname);
        }
        const tcgData = JSON.parse(response.data);
        if (response.statusCode !== 200 || isUndefined(tcgData) || (tcgData.success || false).toString() !== 'true') {
          HubLogger.newCGLogger(TAG, `Failure in retrieving tcgData, retries left: ${retries}`, LL.DEBUG, this.sessionKeyObj.rails_session_id);
          await sleep(TCG_ENDPOINTS.GET_RESULT.sleep);
          retries -= 1;
        } else {
          tcgData.aiRetryCount = totalRetries - retries;
          return tcgData;
        }
      } catch (error) {
        HubLogger.newCGLogger(TAG, `Error in getResultsFromTcg, error: ${error}, retries: ${retries}`, LL.ERROR, this.sessionKeyObj.rails_session_id);
        await sleep(TCG_ENDPOINTS.GET_RESULT.sleep);
        retries -= 1;
      }
    }
    /* eslint-enable no-await-in-loop */
    return {
      success: false,
      data: {
        aiRetryCount: totalRetries - retries
      }
    };
  }

  /**
   * Pushes a command to Redis for the given session.
   *
   * @param {Object} sessionKeyObj - The session key object.
   * @param {Object} request - The request object.
   */
  static pushCommandRedis(sessionKeyObj, request) {
    const redisClient = AICommandHelper.getRedisClient();
    if (isUndefined(sessionKeyObj.rails_session_id) || isUndefined(redisClient)) return;
    try {
      const redisKey = getRedisKey(sessionKeyObj.rails_session_id);
      const reqUrl = isNotUndefined(request.url) ? request.url.split(sessionKeyObj.rails_session_id)[1] : '';
      const command = constructCommand(request.method, reqUrl, sessionKeyObj.aiSessionTimestamp);
      redisClient.append(redisKey, command);
    } catch (error) {
      HubLogger.newCGLogger(TAG, `Error pushing command to redis: ${error.message}`, LL.ERROR, sessionKeyObj.rails_session_id);
    }
  }

  /**
   * Retrieves the commands list from Redis for the given session.
   *
   * @param {Object} sessionKeyObj - The session key object.
   * @returns {string} - The commands for the given session.
   */
  static async getCommandRedis(sessionKeyObj) {
    const redisClient = AICommandHelper.getRedisClient();
    if (isUndefined(sessionKeyObj.rails_session_id) || isUndefined(redisClient)) return '';
    let commands = '';
    try {
      const redisKey = getRedisKey(sessionKeyObj.rails_session_id);
      commands = await redisClient.getset(redisKey, '');
      if (isNotUndefined(commands) && commands.slice(-1) === ',') {
        commands = commands.slice(0, -1);
      }
    } catch (error) {
      HubLogger.newCGLogger(TAG, `Error retrieving command: ${error.message}`, LL.ERROR, sessionKeyObj.rails_session_id);
    }
    return commands;
  }

  /**
   * Removes the Redis key associated with the given session ID.
   *
   * @param {string} railsSessionId - The session ID.
   */
  static handleRedisKeyRemoval(railsSessionId) {
    const redisClient = AICommandHelper.getRedisClient();
    if (isUndefined(railsSessionId) || isUndefined(redisClient)) return;
    try {
      const redisKey = getRedisKey(railsSessionId);
      redisClient.del(redisKey);
    } catch (error) {
      HubLogger.newCGLogger(TAG, `Error removing redis key: ${error.message}`, LL.ERROR, railsSessionId);
    }
  }

  /**
   * Retrieves raw log data from the given JSON data and formats appropriate jar response to be sent to client
   *
   * @param {Object} jsonData - The JSON data.
   * @returns {Object} - The raw log data.
   */
  static retrieveRawLogData(jsonData, other_flags = {}) {
    const rawLogData = {
      status: 'false',
    };
    if (isNotUndefined(jsonData) && isNotUndefined(jsonData.healedSelector)) {
      rawLogData.status = 'true';
      rawLogData.data = jsonData.healedSelector;
      delete jsonData.healedSelector;
    } else if (isNotUndefined(other_flags) && isNotUndefined(other_flags.isHealSkipped) && other_flags.isHealSkipped.toString() === 'true') {
      rawLogData.isExecuted = 'false';
    }
    return JSON.stringify(rawLogData);
  }
}

module.exports = { AICommandHandler };
