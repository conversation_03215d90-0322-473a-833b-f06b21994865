'use strict';

const urlModule = require('url');
const terminal = require('../../../services/terminal');
const constants = require('../../../constants');
const HubLogger = require('../../../log');
const bridge = require('../../../bridge');
const helper = require('../../../helper');

const LL = constants.LOG_LEVEL;

class BasicAuthEdgeHandler {
  constructor(sessionKeyObject, request, response) {
    this.sessionKeyObject = sessionKeyObject;
    this.request = request;
    this.response = response;
    this.userUrl = '';
  }

  processCommand(reqData, requestStateObj, options) {
    try {
      this.userUrl = urlModule.parse(JSON.parse(reqData).url);
    } catch (e) {
      HubLogger.exceptionLogger(`BasicAuthEdge: ${this.sessionKeyObject.rails_session_id}`, `Error while parsing URL for basic auth in edge:${reqData} Exception: ${e}`);
      const returnData = bridge.sendResponse(this.sessionKeyObject, requestStateObj);
      return { returnData };
    }
    this.setEdgeAuthParams();

    reqData = JSON.stringify({ url: this.edge_url });
    options.body = JSON.stringify({ url: this.edge_url });
    options.headers['content-length'] = options.body.length;

    HubLogger.miscLogger(`BasicAuthEdge: ${this.sessionKeyObject.rails_session_id} Sending terminal request for basic authentication on edge`, LL.DEBUG, this.sessionKeyObject.debugSession);
    requestStateObj.edgeBasicAuthCheckTimeout = setTimeout(async () => {
      await this.basicAuthEdgeCall(requestStateObj);
    }, constants.EDGE_AUTH_TIMEOUT);
    return { options, reqData };
  }

  async basicAuthEdgeCall(requestStateObj) {
    requestStateObj.edgeBasicAuthCheckTimeout = undefined;
    try {
      const res = await terminal.basicAuthEdge(this.sessionKeyObject, this.edge_username, this.edge_password);
      helper.addToJarTime(this.sessionKeyObject.rails_session_id, res);

      requestStateObj.hash = 'POST:url';
      if (requestStateObj.returned) {
        bridge.sendResponse(this.sessionKeyObject, requestStateObj);
      }
      HubLogger.miscLogger(`BasicAuthEdge: ${this.sessionKeyObject.rails_session_id} returned`, res.data, LL.DEBUG, this.sessionKeyObject.debugSession);
    } catch (err) {
      requestStateObj.hash = 'POST:url';
      if (requestStateObj.returned) {
        bridge.sendResponse(this.sessionKeyObject, requestStateObj);
      }
      HubLogger.miscLogger(`BasicAuthEdge: ${this.sessionKeyObject.rails_session_id} Error during basic authentication on edge`, err.toString(), LL.ERROR);
    }
  }

  setEdgeAuthParams() {
    this.edge_url = `${this.userUrl.protocol}//${this.userUrl.host}${this.userUrl.path}`;
    this.edge_auth_array = this.userUrl.auth ? this.userUrl.auth.split(':') : ['', ''];
    this.edge_username = this.edge_auth_array[0];
    this.edge_password = this.edge_auth_array.slice(1).join(':');
  }
}

module.exports = { BasicAuthEdgeHandler };
