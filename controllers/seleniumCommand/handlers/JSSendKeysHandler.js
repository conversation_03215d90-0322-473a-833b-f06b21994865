'use strict';

const helper = require('../../../helper');
const bridge = require('../../../bridge');
const { isNotUndefined, isString } = require('../../../typeSanity');
const constants = require('../../../constants');
const HubLogger = require('../../../log');
const pubSub = require('../../../pubSub');
const terminal = require('../../../services/terminal');

const LL = constants.LOG_LEVEL;

class JSSendKeysHandler {
  constructor(sessionKeyObj, request, response) {
    this.sessionKeyObj = sessionKeyObj;
    this.sendData = undefined;
    this.request = request;
    this.response = response;
  }

  processCommand(requestStateObj, options) {
    if (!this.extractSendData()) return null;
    return this.getAttributeTypeAndSetValue(requestStateObj, options);
  }

  async getAttributeTypeAndSetValue(requestStateObj, options) {
    const that = this;
    const urlParts = options.path.toString('utf8').split('/');
    const elementId = urlParts[urlParts.length - 2];
    const attributeUrlParts = urlParts.slice(0, urlParts.length - 1);
    attributeUrlParts.push('attribute/type');
    try {
      const getAttributeTypeResponse = await terminal.getAttributeType(requestStateObj, options, attributeUrlParts);
      helper.addToJarTime(this.sessionKeyObj.rails_session_id, getAttributeTypeResponse);
      const attributeTypeData = JSON.parse(getAttributeTypeResponse.data);
      if (this.constructor.notFileType(attributeTypeData, getAttributeTypeResponse)) { // go with default behavior in case of file
        return terminal.sendKeysToElement(this.sessionKeyObj, requestStateObj, options, urlParts, this.sendData, elementId, this.requestHook.bind(that), false)
          .catch(() => {
            requestStateObj.data = JSON.stringify(this.sendKeysErrorBody());
            return bridge.sendErrorResponse(this.sessionKeyObj, requestStateObj);
          });
      }
      return terminal.sendKeysToElement(this.sessionKeyObj, requestStateObj, options, urlParts, this.sendData, elementId, this.requestHook.bind(that), true);
    } catch (err) {
      return terminal.sendKeysToElement(this.sessionKeyObj, requestStateObj, options, urlParts, this.sendData, elementId, this.requestHook.bind(that), true);
    }
  }

  sendKeysErrorBody() {
    return {
      sessionId: this.sessionKeyObj.rails_session_id,
      status: 13,
      value: {
        message: 'error occurred with sendkeys, try turning "browserstack.sendKeys" off',
      }
    };
  }

  extractSendData() {
    let logDataObj = null;
    try {
      logDataObj = JSON.parse(this.request.log_data);
    } catch (e) {
      HubLogger.exceptionLogger(`IE-jsSendKeys: Error in parsing request for ${this.sessionKeyObj.rails_session_id}: log_data: ${this.request}`);
    }
    this.sendData = this.constructor.extractText(logDataObj);

    if (!this.sendData) {
      HubLogger.miscLogger('jsSendKeys', `Invalid log data for ${this.sessionKeyObj.rails_session_id}: ${logDataObj}`, LL.INFO);
      return false;
    }

    // skip js technique if special characters like key up, enter are pressed.
    for (let i = 0; i < this.sendData.length; i++) {
      // https://github.com/SeleniumHQ/selenium/blob/c5c18d3f51d5c49ea5e1d6ba528e56fc47d477d4/java/client/src/org/openqa/selenium/Keys.java
      if (this.sendData.charCodeAt(i) >= 0xE000 && this.sendData.charCodeAt(i) <= 0xF8FF) {
        return false;
      }
    }

    this.sendData = helper.escapeCharactersForJs(this.sendData);
    return true;
  }

  static extractText(logDataObj) {
    if (logDataObj && !logDataObj.value && isString(logDataObj.text)) {
      return logDataObj.text;
    } else if (logDataObj && isNotUndefined(logDataObj.value)) {
      if (Array.isArray(logDataObj.value)) {
        return logDataObj.value.join('');
      } else if (isString((logDataObj.value))) {
        return logDataObj.value;
      }
    }
    return null;
  }

  static notFileType(attributeTypeData, getAttributeTypeResponse) {
    return attributeTypeData && getAttributeTypeResponse.statusCode === 200 && attributeTypeData.value.toLowerCase() !== 'file';
  }

  requestHook(requestStateObj) {
    this.sessionKeyObj.lastResponseTime = Date.now();
    pubSub.publish(constants.updateKeyObject, {
      session: this.sessionKeyObj.rails_session_id,
      changed: {
        lastResponseTime: this.sessionKeyObj.lastResponseTime,
        lastRequestTime: this.sessionKeyObj.lastRequestTime,
        outsideBrowserstackTime: this.sessionKeyObj.outsideBrowserstackTime,
        userHubLatency: this.sessionKeyObj.userHubLatency,
        seleniumRequestsCount: this.sessionKeyObj.seleniumRequestsCount,
      },
    }, false);

    helper.timeoutManagerUpdateTimeout(this.sessionKeyObj.rails_session_id, this.sessionKeyObj);
    requestStateObj.hash = 'POST:value';
    requestStateObj.data = JSON.stringify({
      sessionId: this.sessionKeyObj.rails_session_id,
      status: 0,
      value: null,
    });
    bridge.sendResponse(this.sessionKeyObj, requestStateObj);
  }
}

module.exports = { JSSendKeysHandler };
