'use strict';

const helper = require('../../../helper');
const constants = require('../../../constants');
const HubLogger = require('../../../log');
const bridge = require('../../../bridge');

class AndroidMultiApksHandler {
  constructor(sessionKeyObj, request, response) {
    this.sessionKeyObj = sessionKeyObj;
    this.request = request;
    this.response = response;
  }

  processCommand(requestStateObj, reqData) {
    let reqDataObj;
    try {
      reqDataObj = JSON.parse(reqData);
    } catch (e) {
      HubLogger.exceptionLogger(`Exception while parsing request data to JSON Object. Exception: ${e}\n KeyObject: ${HubLogger.logKeyObject(this.sessionKeyObj)}`);
      requestStateObj.data = JSON.stringify({ sessionId: this.sessionKeyObj.rails_session_id, status: 13, value: { message: 'Invalid format: Request data should be in JSON.' } });
      bridge.sendResponse(this.sessionKeyObj, requestStateObj);
      return { returns: true };
    }
    // Sample request
    // --> POST /wd/hub/session/6bd19ec2-acee-4d7e-bcd1-25d88d006094/execute
    // [HTTP] {"script":"mobile:installMultipleApks","args":["{}kabsha","adkfbad"]}

    if (helper.nestedKeyValue(reqDataObj, ['script']) && reqDataObj.script === 'mobile:installMultipleApks') {
      requestStateObj.data = JSON.stringify({ sessionId: this.sessionKeyObj.rails_session_id, status: 13, value: { message: constants.COMMAND_ERROR_MESSAGES.multipleApksNotSupported } });
      bridge.sendResponse(this.sessionKeyObj, requestStateObj);
      return { returns: true };
    }
    return { returns: false };
  }
}

module.exports = { AndroidMultiApksHandler };
