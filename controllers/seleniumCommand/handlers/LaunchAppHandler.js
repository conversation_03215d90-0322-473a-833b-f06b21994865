'use strict';

const bridge = require('../../../bridge');
const appsService = require('../../../services/terminal/apps');
const HubLogger = require('../../../log');
const constants = require('../../../constants');

const LL = constants.LOG_LEVEL;
class LaunchAppHandler {
  constructor(sessionKeyObj, request, response) {
    this.sessionKeyObj = sessionKeyObj;
    this.request = request;
    this.response = response;
  }

  async processCommand(requestStateObj) {
    HubLogger.miscLogger('launchApp', `Sending launchApp request to platform session: ${this.sessionKeyObj.rails_session_id} device : ${this.sessionKeyObj.device}`, LL.DEBUG, this.sessionKeyObj.debugSession);
    try {
      await appsService.launchApp(this.sessionKeyObj);
      requestStateObj.data = JSON.stringify({ sessionId: this.sessionKeyObj.rails_session_id, status: 0, value: null });
    } catch (err) {
      HubLogger.tempExceptionLogger('Error in launchApp from hub.js', err);
      requestStateObj.data = JSON.stringify({ sessionId: this.sessionKeyObj.rails_session_id, status: 13, value: null });
    } finally {
      bridge.sendResponse(this.sessionKeyObj, requestStateObj);
    }
  }
}

module.exports = { LaunchAppHandler };
