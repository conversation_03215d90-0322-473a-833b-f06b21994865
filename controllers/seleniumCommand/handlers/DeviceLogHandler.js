'use strict';

const bridge = require('../../../bridge');
const requestlib = require('../../../lib/request');
const HubLogger = require('../../../log');
const constants = require('../../../constants');
const ha = require('../../../ha');
const pubSub = require('../../../pubSub');

const LL = constants.LOG_LEVEL;

// Custom error class for device log errors
class DeviceLogError extends Error {
  constructor(errorType, statusCode = 500) {
    const errorConfig = constants.DEVICE_LOG_ERRORS[errorType];
    if (!errorConfig) {
      throw new Error(`Unknown device log error type: ${errorType}`);
    }

    super(errorConfig.message);
    this.name = 'DeviceLogError';
    this.code = errorConfig.code;
    this.statusCode = statusCode;
  }
}

class DeviceLogHandler {
  constructor(sessionKeyObj, request, response) {
    this.sessionKeyObj = sessionKeyObj;
    this.request = request;
    this.response = response;
  }

  validateDeviceLogsEnabled() {
    const deviceLogsEnabled = this.sessionKeyObj.deviceLogs === true || this.sessionKeyObj.deviceLogs === 'true';
    if (!deviceLogsEnabled) {
      HubLogger.miscLogger('DeviceLogHandler', `Device logs not enabled for session: ${this.sessionKeyObj.rails_session_id}. deviceLogs property: ${this.sessionKeyObj.deviceLogs}`, LL.WARN, this.sessionKeyObj.debugSession);
      throw new DeviceLogError('device_logs_not_enabled', 400);
    }
  }

  preparePlatformRequest(logData, lastEndPos) {
    const idleTimeout = this.sessionKeyObj.idle_timeout;
    const timeoutOffset = idleTimeout > 20 ? 20 : 0;
    const customTimeout = (idleTimeout - timeoutOffset) * 1000;
    const serverURL = `/device_logs?device=${encodeURIComponent(this.sessionKeyObj.device)}`
      + `&session_id=${encodeURIComponent(this.sessionKeyObj.rails_session_id)}`
      + `&log_type=${encodeURIComponent(logData.type)}&start_pos=${lastEndPos}`;

    return {
      hostname: this.sessionKeyObj.rproxyHost,
      port: 45671,
      path: serverURL,
      timeout: customTimeout,
      headers: requestlib.appendBStackHostHeader(this.sessionKeyObj.name),
    };
  }

  refreshSessionObject() {
    const latestSessionObj = constants.global_registry[this.sessionKeyObj.rails_session_id];
    if (latestSessionObj) {
      const oldDeviceLogEndPos = this.sessionKeyObj.deviceLogEndPos || 0;
      const newDeviceLogEndPos = latestSessionObj.deviceLogEndPos || 0;

      // Update our reference to use the latest session object
      this.sessionKeyObj = latestSessionObj;

      HubLogger.miscLogger('DeviceLogHandler', `Pagination - Refreshed session object. deviceLogEndPos: ${oldDeviceLogEndPos} -> ${newDeviceLogEndPos} for session: ${this.sessionKeyObj.rails_session_id}`, LL.DEBUG, this.sessionKeyObj.debugSession);
    }
  }

  updatePaginationState(platformResponse) {
    const endPosHeader = platformResponse.headers && platformResponse.headers['x-end-pos'];
    if (endPosHeader) {
      const newEndPos = parseInt(endPosHeader, 10);
      const oldEndPos = this.sessionKeyObj.deviceLogEndPos || 0;

      HubLogger.miscLogger('DeviceLogHandler', `Pagination - Received new end_pos from platform: ${newEndPos} (previous: ${oldEndPos}) for session: ${this.sessionKeyObj.rails_session_id}`, LL.DEBUG, this.sessionKeyObj.debugSession);

      // Update local session object
      this.sessionKeyObj.deviceLogEndPos = newEndPos;

      // Update the session object in global registry
      if (constants.global_registry[this.sessionKeyObj.rails_session_id]) {
        constants.global_registry[this.sessionKeyObj.rails_session_id].deviceLogEndPos = newEndPos;
      }

      ha.setData(this.sessionKeyObj.rails_session_id, this.sessionKeyObj);

      pubSub.publish(constants.updateKeyObject, {
        session: this.sessionKeyObj.rails_session_id,
        changed: {
          deviceLogEndPos: newEndPos
        }
      });

      HubLogger.miscLogger('DeviceLogHandler', `Pagination - Successfully persisted deviceLogEndPos: ${newEndPos} for session: ${this.sessionKeyObj.rails_session_id}`, LL.DEBUG, this.sessionKeyObj.debugSession);
    }
  }

  createErrorResponse(requestStateObj, message, statusCode = 500) {
    requestStateObj.data = JSON.stringify({
      sessionId: this.sessionKeyObj.rails_session_id,
      status: 13,
      value: { message }
    });
    requestStateObj.output = { headers: constants.CHUNKED_HEADER, statusCode };
    bridge.sendResponse(this.sessionKeyObj, requestStateObj);
  }

  createSuccessResponse(requestStateObj, rawLogData) {
    const responsePrefix = `{"sessionId":"${this.sessionKeyObj.rails_session_id}","status":0,"value":`;
    const responseSuffix = '}';
    requestStateObj.data = responsePrefix + rawLogData + responseSuffix;
    bridge.sendResponse(this.sessionKeyObj, requestStateObj);
  }

  async handlePlatformResponse(platformResponse, requestStateObj) {
    if (platformResponse.statusCode !== 200) {
      throw new DeviceLogError('internal_error', 500);
    }

    this.updatePaginationState(platformResponse);
    this.createSuccessResponse(requestStateObj, platformResponse.data);
  }

  async processCommand(requestStateObj, logData) {
    HubLogger.miscLogger('DeviceLogHandler', `Processing device log request for session: ${this.sessionKeyObj.rails_session_id} device: ${this.sessionKeyObj.device}`, LL.DEBUG, this.sessionKeyObj.debugSession);

    // Refresh session object from global registry
    this.refreshSessionObject();

    try {
      // Validate device logs are enabled
      this.validateDeviceLogsEnabled();

      // Prepare and make platform request
      const lastEndPos = this.sessionKeyObj.deviceLogEndPos || 0;
      HubLogger.miscLogger('DeviceLogHandler', `Pagination - Using deviceLogEndPos: ${lastEndPos} for session: ${this.sessionKeyObj.rails_session_id}`, LL.DEBUG, this.sessionKeyObj.debugSession);

      const termOptions = this.preparePlatformRequest(logData, lastEndPos);

      HubLogger.miscLogger('DeviceLogHandler', `Making request to platform: ${termOptions.path}`, LL.DEBUG, this.sessionKeyObj.debugSession);
      const platformResponse = await requestlib.call(termOptions);

      await this.handlePlatformResponse(platformResponse, requestStateObj);
    } catch (err) {
      if (err instanceof DeviceLogError) {
        // Handle known DeviceLogError instances
        // Only log exceptions for server errors (500), not client errors (400)
        if (err.statusCode === 500) {
          HubLogger.exceptionLogger('Error in DeviceLogHandler', err);
        }
        this.createErrorResponse(requestStateObj, err.message, err.statusCode);
      } else {
        // Handle unexpected errors as internal server errors
        HubLogger.exceptionLogger('Error in DeviceLogHandler', err);
        const internalError = new DeviceLogError('internal_error', 500);
        this.createErrorResponse(requestStateObj, internalError.message, internalError.statusCode);
      }
    }
  }
}

module.exports = { DeviceLogHandler, DeviceLogError };
