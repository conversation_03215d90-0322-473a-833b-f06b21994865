'use strict';

const HubLogger = require('../../../log');
const constants = require('../../../constants');
const terminal = require('../../../services/terminal');
const helper = require('../../../helper');

const LL = constants.LOG_LEVEL;

class EdgeProxyPollingCheck {
  constructor(sessionKeyObj, request, response) {
    this.sessionKeyObj = sessionKeyObj;
    this.request = request;
    this.response = response;
  }

  // eslint-disable-next-line no-unused-vars
  async processCommand(_requestStateObj) {
    try {
      const res = await terminal.edgeProxyRegCheck(this.sessionKeyObj);
      helper.addToJarTime(this.sessionKeyObj.rails_session_id, res);
    } catch (err) {
      HubLogger.miscLogger('Error while CHECK_PROXY_REGISTRY on Edge', err.to<PERSON><PERSON>(), LL.INFO);
    }
  }
}

module.exports = { EdgeProxyPollingCheck };
