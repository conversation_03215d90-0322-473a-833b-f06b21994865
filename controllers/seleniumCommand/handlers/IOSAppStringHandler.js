'use strict';

const terminal = require('../../../services/terminal');
const constants = require('../../../constants');
const HubLogger = require('../../../log');
const bridge = require('../../../bridge');

const LL = constants.LOG_LEVEL;

class IOSAppStringHandler {
  constructor(sessionKeyObj, request, response) {
    this.sessionKeyObj = sessionKeyObj;
    this.request = request;
    this.response = response;
  }

  async processCommand(requestStateObj) {
    HubLogger.miscLogger('AppStrings', `Sending app_strings request to platform session: ${this.sessionKeyObj.rails_session_id} device : ${this.sessionKeyObj.device}`, LL.DEBUG, this.sessionKeyObj.debugSession);

    try {
      const appStringResponse = await terminal.getIOSAppString(this.request.url, this.sessionKeyObj, requestStateObj);
      HubLogger.miscLogger('AppStrings', `Response for AppStrings from platform is ${JSON.stringify(appStringResponse)}`, LL.DEBUG, this.sessionKeyObj.debugSession);

      if (appStringResponse.statusCode === 200) {
        requestStateObj.data = JSON.stringify({ sessionId: this.sessionKeyObj.rails_session_id, status: 0, value: JSON.parse(appStringResponse.data) });
      } else {
        requestStateObj.data = JSON.stringify({ sessionId: this.sessionKeyObj.rails_session_id, status: 13, value: { message: appStringResponse.data } });
        requestStateObj.output = { headers: constants.CHUNKED_HEADER, statusCode: 500 };
      }
    } catch (err) {
      HubLogger.tempExceptionLogger('Error in appString from hub.js', err);
      requestStateObj.data = JSON.stringify({ sessionId: this.sessionKeyObj.rails_session_id, status: 13, value: null });
      requestStateObj.output = { headers: constants.CHUNKED_HEADER, statusCode: 500 };
    }
    bridge.sendResponse(this.sessionKeyObj, requestStateObj);
    return { returns: true };
  }
}

module.exports = { IOSAppStringHandler };
