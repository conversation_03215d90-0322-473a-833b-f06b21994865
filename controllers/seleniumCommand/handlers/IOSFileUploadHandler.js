'use strict';

const selenium = require('../../../services/selenium');

class IOSFileUploadHandler {
  constructor(sessionKeyObj, request, response) {
    this.sessionKeyObj = sessionKeyObj;
    this.request = request;
    this.response = response;
  }

  // eslint-disable-next-line no-unused-vars
  async processCommand(_requestStateObj) {
    const iosFileUploadResp = selenium.realIOSFileUpload(this.sessionKeyObj);
    return { returnData: iosFileUploadResp };
  }
}

module.exports = { IOSFileUploadHandler };
