'use strict';

const constants = require('../../../constants');
const HubLogger = require('../../../log');
const bridge = require('../../../bridge');
const SeleniumClient = require('../../../seleniumClient');

const LL = constants.LOG_LEVEL;

class W3CSendKeysHandler {
  // Converts /keys command to get element and element value
  constructor(sessionKeyObj, request, response) {
    this.sessionKeyObj = sessionKeyObj;
    this.request = request;
    this.response = response;
    this.selenium = new SeleniumClient(sessionKeyObj);
  }

  async processCommand(requestStateObj, reqData) {
    HubLogger.miscLogger('sendKeysHandler', `Sending Custom sendKeysHandler request to platform session: ${this.sessionKeyObj.rails_session_id} device : ${this.sessionKeyObj.device}`, LL.DEBUG, this.sessionKeyObj.debugSession);
    try {
      const elementId = await this.selenium.getActiveElement();
      const keysValue = JSON.parse(reqData).value;
      await this.selenium.elementValue(elementId, keysValue);
      requestStateObj.data = JSON.stringify({ sessionId: this.sessionKeyObj.rails_session_id, status: 0, value: null });
    } catch (err) {
      HubLogger.tempExceptionLogger('Error in sendKeysHandler from hub.js', err);
      requestStateObj.data = JSON.stringify({ sessionId: this.sessionKeyObj.rails_session_id, status: 13, value: null });
    } finally {
      bridge.sendResponse(this.sessionKeyObj, requestStateObj);
    }
  }
}

module.exports = { W3CSendKeysHandler };
