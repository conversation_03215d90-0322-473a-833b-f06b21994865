'use strict';

const bridge = require('../../../bridge');

class TestCafeDummyResponseHandler {
  constructor(sessionKeyObj, request, response) {
    this.sessionKeyObj = sessionKeyObj;
    this.request = request;
    this.response = response;
  }

  processCommand(requestStateObj) {
    requestStateObj.data = JSON.stringify({ sessionId: this.sessionKeyObj.rails_session_id, status: 0, value: 'https://dummy-url.browserstack.com' });
    bridge.sendResponse(this.sessionKeyObj, requestStateObj);
  }
}

module.exports = { TestCafeDummyResponseHandler };
