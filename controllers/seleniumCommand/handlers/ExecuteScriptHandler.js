'use strict';

const jsExecutor = require('../../../lib/customSeleniumHandler/jsExecutor');

class ExecuteScriptHandler {
  constructor(sessionKeyObj, request, response) {
    this.sessionKeyObj = sessionKeyObj;
    this.request = request;
    this.response = response;
  }

  async processCommand(requestStateObj) {
    const res = jsExecutor.checkandExecuteIfBstackExecutor(this.sessionKeyObj, requestStateObj);
    return { returns: res };
  }
}

module.exports = { ExecuteScriptHandler };
