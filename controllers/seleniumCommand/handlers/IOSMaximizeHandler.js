'use strict';

const selenium = require('../../../services/selenium');
const hub = require('../../../hub');

class IOSMaximizeHandler {
  constructor(sessionKeyObj, request, response) {
    this.sessionKeyObj = sessionKeyObj;
    this.request = request;
    this.response = response;
  }

  async processCommand(requestStateObj) {
    const maximizeResp = selenium.iOSMaximize();
    hub.processResponse(this.request, this.response, this.sessionKeyObj, {
      data: JSON.stringify(maximizeResp),
      remoteSessionID: this.sessionKeyObj.key,
      clientSessionID: this.sessionKeyObj.rails_session_id,
      index_counter: requestStateObj.index_counter,
      callbacks: requestStateObj.callbacks,
      hostname: requestStateObj.hostname,
      originalUrl: requestStateObj.originalUrl,
    });
    return { data: maximizeResp };
  }
}

module.exports = { IOSMaximizeHandler };
