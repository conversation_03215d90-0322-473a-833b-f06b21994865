'use strict';

const terminal = require('../../../services/terminal');
const constants = require('../../../constants');
const HubLogger = require('../../../log');
const bridge = require('../../../bridge');
const helper = require('../../../helper');
const pubSub = require('../../../pubSub');

const LL = constants.LOG_LEVEL;

class IEKeysHandler {
  constructor(sessionKeyObj, request, response) {
    this.sessionKeyObj = sessionKeyObj;
    this.request = request;
    this.response = response;
  }

  async processCommand(requestStateObj, options) {
    this.logDataObj = null;
    let shouldReturn;
    try {
      this.logDataObj = JSON.parse(this.request.log_data);
    } catch (e) {
      HubLogger.exceptionLogger(`IEKeys: Error in parsing request for ${this.sessionKeyObj.rails_session_id} log_data: ${this.request.log_data}`);
    }
    if (this.logDataObj && (this.logDataObj.value || this.logDataObj.value === '')) {
      this.sendKeys(requestStateObj, options);
      HubLogger.miscLogger('IEKeys request', this.sessionKeyObj.rails_session_id, LL.INFO);
      shouldReturn = true;
    } else {
      // Carry on with selenium sendKeys
      HubLogger.exceptionLogger(`IESendKeys: Error in parsing request for ${this.sessionKeyObj.rails_session_id} keyObject: ${HubLogger.logKeyObject(this.sessionKeyObj)}`);
      shouldReturn = false;
    }
    return { returns: shouldReturn };
  }

  async sendKeys(requestStateObj, options) {
    const text = helper.getAutoitText(this.sessionKeyObj, this.logDataObj.value, pubSub);
    try {
      await terminal.autoItSendKeys(this.sessionKeyObj, requestStateObj, options, text);
      requestStateObj.hash = 'POST:keys';
      requestStateObj.data = JSON.stringify({
        sessionId: this.sessionKeyObj.rails_session_id, status: 0, value: null
      });
      bridge.sendResponse(this.sessionKeyObj, requestStateObj);
    } catch (err) {
      bridge.sendErrorResponse(this.sessionKeyObj, requestStateObj);
    }
  }
}

module.exports = { IEKeysHandler };
