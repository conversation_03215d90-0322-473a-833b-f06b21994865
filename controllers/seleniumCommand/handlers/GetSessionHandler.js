'use strict';

const bridge = require('../../../bridge');
const { OS_VERSION_MAP } = require('../../../constants');

class GetSessionHandler {
  constructor(sessionKeyObj, request, response) {
    this.sessionKeyObj = sessionKeyObj;
    this.request = request;
    this.response = response;
  }

  async processCommand(requestStateObj) {
    const capabilities = {
      sessionId: this.sessionKeyObj.rails_session_id,
      os: this.sessionKeyObj.os_passed_by_user,
      osVersion: OS_VERSION_MAP[this.sessionKeyObj.os_version]
        ? OS_VERSION_MAP[this.sessionKeyObj.os_version]
        : this.sessionKeyObj.os_version,
      browser: this.sessionKeyObj.browser,
      browserName: this.sessionKeyObj.browser,
      version: this.sessionKeyObj.browser_version,
      browserVersion: this.sessionKeyObj.browser_version,
      device: this.sessionKeyObj.deviceName,
      deviceName: this.sessionKeyObj.deviceName,
      orientation: this.sessionKeyObj.deviceOrientation,
      deviceOrientation: this.sessionKeyObj.deviceOrientation,
      acceptSslCerts: this.sessionKeyObj.certs,
    };
    requestStateObj.data = JSON.stringify({
      sessionId: this.sessionKeyObj.rails_session_id,
      status: 0,
      state: 'success',
      value: {
        capabilities,
        ...capabilities,
      },
    });
    bridge.sendResponse(this.sessionKeyObj, requestStateObj);
  }
}

module.exports = { GetSessionHandler };
