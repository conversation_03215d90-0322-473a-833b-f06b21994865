'use strict';

const selenium = require('../../../services/selenium');
const constants = require('../../../constants');
const HubLogger = require('../../../log');
const bridge = require('../../../bridge');
const loggingHelper = require('../../../utils/logger/loggingHelper');

const LL = constants.LOG_LEVEL;

class IOSGetLocationHandler {
  constructor(sessionKeyObj, request, response) {
    this.sessionKeyObj = sessionKeyObj;
    this.request = request;
    this.response = response;
  }

  async processCommand(requestStateObj, url) {
    HubLogger.miscLogger('Blocking request', `Get Location command intercepted : ${url.toString()}. session: ${this.sessionKeyObj.rails_session_id}`, LL.INFO);

    const getIOSLocationResp = selenium.getIOSLocation(this.sessionKeyObj);
    requestStateObj.data = JSON.stringify(getIOSLocationResp);
    loggingHelper.sendCombinedLogs(this.request, this.sessionKeyObj, requestStateObj.data, false);

    const returnVal = bridge.sendErrorResponse(this.sessionKeyObj, requestStateObj);
    return { returnData: returnVal };
  }
}

module.exports = { IOSGetLocationHandler };
