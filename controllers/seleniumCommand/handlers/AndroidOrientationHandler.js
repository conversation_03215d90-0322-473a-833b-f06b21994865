'use strict';

const helper = require('../../../helper');
const bridge = require('../../../bridge');
const constants = require('../../../constants');
const HubLogger = require('../../../log');
const pubSub = require('../../../pubSub');
const deviceOrientation = require('../../../services/terminal/deviceOrientation');

const LL = constants.LOG_LEVEL;

class AndroidOrientationHandler {
  constructor(sessionKeyObj, request, response) {
    this.sessionKeyObj = sessionKeyObj;
    this.request = request;
    this.response = response;
  }

  async processCommand(reqData, requestStateObj, attempt) {
    // FIXME: Check if can remove ios specific conditions https://github.com/browserstack/SeleniumHub/pull/2053
    // FIXME: Can we remove below if condition. Doesn't seems to be related to orientation
    let longPageLoadTimeout;
    if (['GET:screenshot', 'POST:url'].indexOf(requestStateObj.hash) > -1 && attempt < 2) {
      // Mark session if there is a basic-auth popup if page load takes long time
      if (constants.activeWindowCheckBrowsers.indexOf((this.sessionKeyObj.browser || '').toLowerCase()) > -1) {
        HubLogger.miscLogger('POST:url', `Setting timeout to check active_window: session: ${this.sessionKeyObj.rails_session_id}`, LL.DEBUG, this.sessionKeyObj.debugSession);

        longPageLoadTimeout = setTimeout(() => {
          HubLogger.miscLogger('POST:url', `Timeout to check active_window triggered. session: ${this.sessionKeyObj.rails_session_id}`, LL.WARN);
          helper.checkActiveWindowOnTerminal(this.sessionKeyObj);
        }, constants.activeWindowLongPageLoadTime);
      }
    }
    await this.androidOrientationCall(reqData, requestStateObj);
    return longPageLoadTimeout;
  }

  /* eslint-disable max-lines-per-function */
  async androidOrientationCall(reqData, requestStateObj) {
    let responseData;
    let logString;
    try {
      if (this.request.method === 'POST') {
        let switchOrientation = JSON.parse(reqData).orientation;
        switchOrientation = switchOrientation && switchOrientation.match(/landscape/i) ? 'LANDSCAPE' : 'PORTRAIT';
        if (this.sessionKeyObj.deviceName && this.sessionKeyObj.deviceName.toLowerCase().indexOf('samsung galaxy tab 4') > -1) {
          switchOrientation = this.getReverseOrientation(switchOrientation);
        }
        responseData = await deviceOrientation.setAndroidOrientation(this.sessionKeyObj, this.request.method, switchOrientation);
        logString = 'RealMobile: Setting orientation';
        const orientation = (responseData.trim().toLowerCase().indexOf('landscape') > -1) ? 'landscape' : 'portrait';
        pubSub.publish(constants.updateKeyObject, {
          session: this.sessionKeyObj.rails_session_id,
          changed: {
            deviceOrientation: orientation,
          },
        });
      } else {
        responseData = await deviceOrientation.getAndroidOrientation(this.sessionKeyObj, this.request.method);
        logString = 'RealMobile: Retrieving orientation from endpoint';
      }
      if (this.sessionKeyObj.deviceName && this.sessionKeyObj.deviceName.toLowerCase().indexOf('samsung galaxy tab 4') > -1) {
        responseData = this.getReverseOrientation(responseData);
      }
      HubLogger.miscLogger(logString, responseData, LL.DEBUG, this.sessionKeyObj.debugSession);
      requestStateObj.data = JSON.stringify({ sessionId: this.sessionKeyObj.rails_session_id, status: 0, value: responseData.toUpperCase() });
      setTimeout(() => { bridge.sendResponse(this.sessionKeyObj, requestStateObj); }, 1500);
    } catch (error) {
      HubLogger.miscLogger(`Error: ${logString}`, error.toString(), LL.ERROR);
      bridge.sendResponse(this.sessionKeyObj, requestStateObj);
    }
  }
  /* eslint-enable max-lines-per-function */

  getReverseOrientation(currentOrientation) {
    if (currentOrientation && currentOrientation.toLowerCase().indexOf('portrait') > -1) {
      return 'LANDSCAPE';
    }
    return 'PORTRAIT';
  }
}

module.exports = { AndroidOrientationHandler };
