'use strict';

const selenium = require('../../../services/selenium');
const terminal = require('../../../services/terminal');
const constants = require('../../../constants');
const HubLogger = require('../../../log');
const bridge = require('../../../bridge');
const helper = require('../../../helper');

const LL = constants.LOG_LEVEL;

class FirefoxAcceptSslHandler {
  constructor(sessionKeyObj, request, response) {
    this.sessionKeyObj = sessionKeyObj;
    this.request = request;
    this.response = response;
  }

  async handleCommandResult(requestStateObj) {
    HubLogger.miscLogger('AcceptSSLFirefox request', this.sessionKeyObj.rails_session_id, LL.INFO);

    const params = this.getParamsForAcceptSsl();

    try {
      const res = await terminal.acceptFirefoxSsl(this.sessionKeyObj, params);
      helper.addToJarTime(this.sessionKeyObj.rails_session_id, res);

      requestStateObj.hash = 'POST:url';
      requestStateObj.output.statusCode = 200;
      requestStateObj.data = JSON.stringify({ sessionId: this.sessionKeyObj.rails_session_id, status: 0, value: null });
      HubLogger.miscLogger(`AcceptSSLFirefox ${this.sessionKeyObj.rails_session_id} returned:`, res.data, LL.DEBUG, this.sessionKeyObj.debugSession);

      await selenium.waitForPageLoad(this.sessionKeyObj, constants.acceptSslffReturnTimeout);
      bridge.sendResponse(this.sessionKeyObj, requestStateObj);
    } catch (err) {
      requestStateObj.hash = 'POST:url';
      HubLogger.miscLogger(`AcceptSSLFirefox ${this.sessionKeyObj.rails_session_id} returned ERROR:`, err.toString(), LL.ERROR);
      bridge.sendErrorResponse(this.sessionKeyObj, requestStateObj);
    }
  }

  getParamsForAcceptSsl() {
    let params = '';
    if (this.sessionKeyObj.os.indexOf('mac') < 0) {
      // eslint-disable-next-line radix
      const intBrowserVersion = parseInt(this.sessionKeyObj.browser_version);
      // eslint-disable-next-line no-restricted-globals
      if (intBrowserVersion && !isNaN(intBrowserVersion) && intBrowserVersion > 49) {
        params += 'numTabs=3';
      } else {
        params += 'numTabs=2';
      }
    }
    return params;
  }
}

module.exports = { FirefoxAcceptSslHandler };
