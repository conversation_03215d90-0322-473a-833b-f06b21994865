'use strict';

const constants = require('../../../constants');
const myLock = require('../../../semaphore');
const helper = require('../../../helper');
const HubLogger = require('../../../log');
const hub = require('../../../hub');
const ha = require('../../../ha');
const pubSub = require('../../../pubSub');
const browserstack = require('../../../browserstack');
const redisClient = require('../../../redisUtils').redisClient;
const Promise = require('bluebird');
const { AdditionalSessionLogs } = require('../../../services/terminal/stopSessionInterface/AdditionalSessionLogs');

const LL = constants.LOG_LEVEL;

class StopSessionHandler {
  constructor(request, response, sessionKeyObj) {
    this.request = request;
    this.response = response;
    this.sessionKeyObj = sessionKeyObj;
  }

  async handle(sessionIdKey) {
    try {
      await myLock.ttlSemaphore('session_stop_semaphore', sessionIdKey);

      if (this.checkPendingDelete(sessionIdKey)) {
        helper.endConflictResponse(this.response);
        return;
      }

      helper.pushToCLS('driver_quit_request', {
        session_id: this.sessionKeyObj.rails_session_id,
      });
      this.sessionKeyObj.stop_init_time = new Date();

      await this.processStopLogs();
      await this.stopAndDeleteSession();
      return;
    } catch (error) {
      HubLogger.miscLogger('StopByUser', `409 - stop has been triggered already for session ${sessionIdKey}`, LL.WARN);
      helper.endConflictResponse(this.response);
    }
  }

  checkPendingDelete(sessionIdKey) {
    if (this.sessionKeyObj.pendingDelete) {
      HubLogger.miscLogger(`Duplicate-Delete', 'Duplicate stop for session: ${sessionIdKey}`, LL.DEBUG);
      return true;
    }
    if (this.sessionKeyObj) {
      this.sessionKeyObj.pendingDelete = true;
      pubSub.publish(constants.updateKeyObject, {
        session: this.sessionKeyObj.rails_session_id,
        changed: {
          pendingDelete: true,
        },
      });
    }
    return false;
  }

  async processStopLogs() {
    helper.addToConsoleTimes(this.request, 'fetch-stop-logs');
    helper.instrumentVideoDuration(this.sessionKeyObj);
    const sessionLogs = new AdditionalSessionLogs(this.sessionKeyObj);
    await sessionLogs.handleStop();
    helper.addToConsoleTimes(this.request, 'fetch-stop-logs-end');
  }

  async stopAndDeleteSession() {
    const jarDeleteEnabled = await this.enableJarDeleteForSession();
    let deleteOptions;
    if (jarDeleteEnabled) {
      helper.addToConsoleTimes(this.request, 'jar-delete');
      deleteOptions = await this.sendDeleteRequestToSeleniumJar();
    }
    try {
      await this.sendStopToBrowserstack();
    } catch (error) {
      HubLogger.exceptionLogger(`${this.sessionKeyObj.rails_session_id} stop timedout within ${constants.BS_STOP_TIMEOUT} ms`);
    }
    helper.stopSeleniumClock(this.sessionKeyObj.rails_session_id, this.request.url, this.request.method, helper.isW3C(this.sessionKeyObj), constants.SELENIUM_SUCCESS_CODE, this.request.log_data);
    await this.freeUserScript(deleteOptions);
    this.sessionHubCleanup();
  }

  async enableJarDeleteForSession() {
    // Playwright session do not spawn a JAR hence JAR delete flow is not required
    if (this.sessionKeyObj.isPlaywright) {
      return false;
    }
    if (this.sessionKeyObj.os.match(/mac/i)) {
      HubLogger.miscLogger('ENABLE_JAR_DELETE_FOR_SESSION', `${this.sessionKeyObj.rails_session_id} Jar delete enabled for mac`);
      return true;
    }
    HubLogger.miscLogger('ENABLE_JAR_DELETE_FOR_SESSION', `Checking jar delete flag for group ${this.sessionKeyObj.group_id}`);
    try {
      return await redisClient.sismember([constants.ENABLE_JAR_DELETE_FOR_GROUP, this.sessionKeyObj.group_id]);
    } catch (error) {
      HubLogger.miscLogger('ENABLE_JAR_DELETE_FOR_SESSION', `Set to false for group ${this.sessionKeyObj.group_id}`);
    }
    return false;
  }

  // TODO: Relook to not use callback once createBridgeClientAndNode is refactored
  sendDeleteRequestToSeleniumJar() {
    return new Promise((resolve) => {
      hub.createBridgeClientAndNode(this.sessionKeyObj, this.request, this.response, {
        callbackEnd: (request, _response, _sessionKeyObj, deleteOptions) => {
          helper.addToConsoleTimes(request, 'jar-delete-end');
          resolve(deleteOptions);
        }
      });
    });
  }

  // TODO: Relook to not use callback once browserstack is refactored
  sendStopToBrowserstack() {
    return new Promise((resolve, reject) => {
      let hasReturned = false;
      setTimeout(() => {
        if (!hasReturned) {
          reject();
          hasReturned = true;
        }
      }, constants.BS_STOP_TIMEOUT);

      const { stopUrl, stopPostParams } = helper.buildRailsStopUrlParams(this.sessionKeyObj, constants.global_registry[this.sessionKeyObj.rails_session_id]);
      browserstack.postBrowserStack(stopUrl, stopPostParams, undefined, undefined, () => {
        const sessionKeyObj = constants.global_registry[this.sessionKeyObj.rails_session_id];

        if (sessionKeyObj && (sessionKeyObj.exceptionClass || sessionKeyObj.exceptionMessage)) HubLogger.seleniumStats('pre-quit-exception', sessionKeyObj, sessionKeyObj.user, sessionKeyObj.exceptionClass || sessionKeyObj.exceptionMessage, sessionKeyObj.exceptionRequest);
        if (!hasReturned) {
          resolve();
          hasReturned = true;
        }
      });
    });
  }

  async freeUserScript(deleteOptions) {
    const delayDuration = await this.getDeleteResponseDelayForGroup();
    await Promise.delay(delayDuration);
    if (typeof deleteOptions !== 'undefined') {
      this.response.end(deleteOptions.data.toString());
    } else {
      this.response.end(JSON.stringify(constants.SUCCESS_RESPONSE));
    }

    /**
     * Setting the lastRequest explicitly to DELETE since we don't send
     * DELETE request to the JAR in this case. But that is actually the
     * lastRequest received from the client.
     */
    this.sessionKeyObj.lastRequest = `DELETE:${this.sessionKeyObj.rails_session_id}`;
    this.sessionKeyObj.lastResponseStatus = `200::${constants.SELENIUM_SUCCESS_CODE}`;
    ha.setData(this.sessionKeyObj.rails_session_id, this.sessionKeyObj);
    this.sessionKeyObj.userRequestStartTime = helper.getFromConsoleTimes(this.request, 'received-request');
  }

  async getDeleteResponseDelayForGroup() {
    const sessionDuration = Date.now() - this.sessionKeyObj.session_start_time; // in msec
    // delaying the delete response based on group::sessionDuration::device
    // currently being done for AirBnB to prevent the over usage of iPhone 11
    HubLogger.miscLogger('DELETE-RESPONSE-DELAY', `Checking delay for group ${this.sessionKeyObj.group_id} with duration ${sessionDuration} on device ${this.sessionKeyObj.deviceName}`, LL.INFO);
    try {
      const devicePresent = constants.DEVICES_WITH_DELAYED_DELETE_RESPONSE.find(deviceRegex => deviceRegex.test(this.sessionKeyObj.deviceName.split('-')[0]));
      HubLogger.miscLogger('DELETE-RESPONSE-DELAY', `Device Found : ${devicePresent}`, LL.INFO);
      if (await redisClient.sismember([constants.DELAY_ON_DELETE_RESPONSE_FOR_GROUP, this.sessionKeyObj.group_id])
          && sessionDuration <= constants.SESSION_TIME_THRESHOLD_FOR_DELAY_IN_DELETE_RESPONSE
          && devicePresent) {
        HubLogger.miscLogger('DELETE-RESPONSE-DELAY', `Delaying Delete Response for group ${this.sessionKeyObj.group_id} by ${constants.SESSION_DELETE_RESPONSE_DELAY} msecs`, LL.INFO);
        return constants.SESSION_DELETE_RESPONSE_DELAY;
      }
    } catch (e) {
      // continue regardless of error
    }
    return 0;
  }

  sessionHubCleanup() {
    HubLogger.addStopToRawLogs(this.sessionKeyObj, this.sessionKeyObj.rails_session_id, 'CLIENT_STOPPED_SESSION', 0, false);
    helper.pingDataToStats(this.sessionKeyObj);
    helper.checkNonZeroStatusErrors(this.sessionKeyObj);
    helper.checkUdpKeystoSend(this.sessionKeyObj);
    helper.sessionRemovedFromRegionHook(this.sessionKeyObj, false);
  }
}

module.exports = { StopSessionHandler };
