'use strict';

const constants = require('../../../constants');
const requestlib = require('../../../lib/request');
const sessionManagerHelper = require('../../../services/session/sessionManagerHelper');
const HubLogger = require('../../../log');
const helper = require('../../../helper');
const myLock = require('../../../semaphore');
const redisClient = require('../../../redisUtils').redisClient;
const { StopVideoRecordingService } = require('../../../services/terminal/stopSessionInterface/StopVideoRecordingService');
const errorMessages = require('../../../errorMessages');
const util = require('util');

const LL = constants.LOG_LEVEL;

class StopByBsHandler {
  constructor(request, response) {
    this.request = request;
    this.response = response;
    this.sessionKeyObj = undefined;
    this.responseData = {
      headers: { 'content-type': 'application/json; charset=utf-8' }
    };
  }

  async handle() {
    const parsedParams = await this.processParams();
    if (!parsedParams || !parsedParams.sessionIdKey) {
      this.responseData.statusCode = 404;
      return;
    }

    const sessionIdKey = parsedParams.sessionIdKey;
    const timeoutLimit = parsedParams.timeoutLimit;

    this.sessionKeyObj = await sessionManagerHelper.getKeyObject(sessionIdKey);
    const statusCode = (sessionIdKey && this.sessionKeyObj) ? 200 : 403;
    if (this.checkPendingDelete(sessionIdKey)) {
      this.responseData.statusCode = 404;
      return;
    }

    await this.stopSession(sessionIdKey, statusCode, timeoutLimit);
  }

  sendResponse() {
    this.response.writeHead(this.responseData.statusCode, this.responseData.headers);
    if (this.sessionKeyObj) {
      const responseObj = {};
      if (this.sessionKeyObj.lighthouseAutomate) {
        const lighthouseAutomate = this.sessionKeyObj.lighthouseAutomate || {};
        responseObj.lhReportCount = lighthouseAutomate.report_counter || 0;
        responseObj.lhReportList = lighthouseAutomate.finalList || [];
      }
      if (this.sessionKeyObj.selfHealingSuccess) {
        responseObj.selfHealingSuccess = this.sessionKeyObj.selfHealingSuccess;
      } else if (this.sessionKeyObj.softHealingSuccess) {
        responseObj.softHealingSuccess = this.sessionKeyObj.softHealingSuccess;
      }
      if (this.sessionKeyObj.midSessionHealingDisabled) {
        responseObj.midSessionHealingDisabled = this.sessionKeyObj.midSessionHealingDisabled;
      }
      if (this.sessionKeyObj.ai_healing_details && this.sessionKeyObj.ai_healing_details.total_healing_enabled_request > 0) {
        responseObj.ai_healing_details = this.sessionKeyObj.ai_healing_details;
      }
      this.response.end(JSON.stringify(responseObj));
      return;
    }
    this.response.end();
  }

  checkPendingDelete(sessionIdKey) {
    if (!this.sessionKeyObj || this.sessionKeyObj.pendingDelete) {
      HubLogger.miscLogger('StopByBStack', `KeyObject not found for session: ${sessionIdKey}for request: ${this.request.url}`, LL.INFO);
      return true;
    }
    helper.updateOutsideBrowserstackTime(this.sessionKeyObj);
    helper.timeoutManagerClearTimeout(sessionIdKey, this.sessionKeyObj);
    return false;
  }

  async stopSession(sessionIdKey, statusCode, timeoutLimit) {
    try {
      await myLock.ttlSemaphore('session_stop_semaphore', sessionIdKey);
      const isAppAutomate = this.sessionKeyObj.appTesting || false;
      let errorMessage = errorMessages.APP_AUTOMATE_ERROR_MESSAGES.SESSION_LIMIT_REACHED;
      if (isAppAutomate && timeoutLimit !== undefined) {
        errorMessage += ` (${timeoutLimit} secs)`;
      }
      const sessionLimitReachedMessage = isAppAutomate ? errorMessage : 'SESSION_LIMIT_REACHED';
      if (!this.sessionKeyObj.realMobile) {
        this.stopSessionService = new StopVideoRecordingService(this.sessionKeyObj, 'stopByBS');
        await this.stopSessionService.handleStop();
      }
      this.handleSessionCleanup(sessionIdKey, statusCode, sessionLimitReachedMessage, isAppAutomate);
      this.responseData.statusCode = statusCode;
      return;
    } catch (error) {
      HubLogger.miscLogger('StopByBStack', `409 - stop has been triggered already ${this.data} - ${error ? error.toString() : 'no error'}`, LL.WARN);
      this.responseData.statusCode = 409;
    }
  }

  async processParams() {
    this.data = await requestlib.readRequest(this.request);
    if (!this.data) {
      HubLogger.miscLogger('StopByBStack', `Malformed request, key not found in data: ${this.data}`, LL.INFO);
      return undefined;
    }
    const jsonData = JSON.parse(this.data);
    const sessionIdKey = jsonData.k;
    const timeoutLimit = jsonData.timeout_limit;
    if (!sessionIdKey) {
      HubLogger.miscLogger('StopByBStack', `Malformed request, key not found in data: ${this.data}`, LL.INFO);
      return undefined;
    }

    return { sessionIdKey, timeoutLimit };
  }

  handleSessionCleanup(sessionIdKey, statusCode, sessionLimitReachedMessage, isAppAutomate) {
    if (statusCode === 200) {
      HubLogger.miscLogger('BS-Stop', `Clear timeout for: ${sessionIdKey}`, LL.INFO);
      switch (this.request.url) {
        case '/stop_ui':
          HubLogger.addStopToRawLogs(this.sessionKeyObj, sessionIdKey, isAppAutomate ? '[BROWSERSTACK_UI_STOPPED] Session was stopped from the dashboard UI' : 'UI_STOPPED', 0, true);
          break;
        case '/stop_smd':
          HubLogger.miscLogger('FORCIBLY_MARKED_AS_DONE', `Session Id: ${sessionIdKey}`, LL.INFO);
          // TODO: addStopToRawLogs for SMD to be added on false SMDs issue resolution
          break;
        case '/stop_limit':
          HubLogger.addStopToRawLogs(this.sessionKeyObj, sessionIdKey, sessionLimitReachedMessage, 1, true);
          break;
        case '/stop_limit_aa_freemium':
          HubLogger.addStopToRawLogs(this.sessionKeyObj, sessionIdKey, 'FREE PLAN DAILY LIMIT REACHED', 1, true);
          break;
        default:
          HubLogger.miscLogger('BS-Stop', `Unexpected url ${this.request.url} for ${sessionIdKey}`, LL.INFO);
      }
    }
    this.retrieveLogsAndRemoval(sessionIdKey);
    redisClient.setex(`alreadyStopped_${sessionIdKey}`, 60, 1);
  }

  async retrieveLogsAndRemoval(sessionIdKey) {
    const fetchAdditionalLogsPromisified = util.promisify(HubLogger.fetchAdditionalLogs);
    await fetchAdditionalLogsPromisified(this.sessionKeyObj);
    helper.sessionRemovedFromRegionHook(this.sessionKeyObj, true, sessionIdKey);
  }
}

module.exports = { StopByBsHandler };
