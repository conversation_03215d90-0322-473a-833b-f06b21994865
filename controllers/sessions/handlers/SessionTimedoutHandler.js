'use strict';

const constants = require('../../../constants');
const browserstack = require('../../../browserstack');
const errorMessages = require('../../../errorMessages');
const HubLogger = require('../../../log');
const myLock = require('../../../semaphore');
const ha = require('../../../ha');
const helper = require('../../../helper');
const nodeurl = require('url');
const pubSub = require('../../../pubSub');
const util = require('util');
const selenium = require('../../../services/selenium');
const { isNotUndefined } = require('../../../typeSanity');

const LL = constants.LOG_LEVEL;

class SessionTimedoutHandler {
  constructor(request, response) {
    this.request = request;
    this.response = response;
    this.url = request.url;
    this.sessionKeyObject = undefined;
    this.responseData = {
      headers: { 'content-type': 'application/json; charset=utf-8' }
    };
  }

  async handle() {
    const { query } = nodeurl.parse(this.url, true);
    // FIXME: There are no else conditions here. Not changing this during refactoring.
    if (!query.sessionId) return;

    const { sessionId: key, message, errorMessage } = query;
    this.request.sessionId = key;
    if (isNotUndefined(message)) this.request.timeoutMessage = message;
    if (isNotUndefined(errorMessage)) this.request.errorMessage = errorMessage;

    try {
      await myLock.ttlSemaphore('session_stop_semaphore', key);
      HubLogger.miscLogger('handleSessionTimeout', `IDLE_TIMEOUT - Stopping session with id ${key}`, LL.INFO);
      constants.pushToHootHootRegistry.triggeredTimeouts = constants.pushToHootHootRegistry.triggeredTimeouts || {};
      constants.pushToHootHootRegistry.triggeredTimeouts[constants.hubName] = (constants.pushToHootHootRegistry.triggeredTimeouts[constants.hubName] || 0) + 1;
      await this.clearStaleSession(key);
    } catch (e) {
      HubLogger.miscLogger('handleSessionTimeout', `Session with id ${key} already stopped`, LL.ERROR);
      this.responseData.statusCode = 409;
    }
  }

  async clearStaleSession(keyObject) {
    const sessionId = keyObject.rails_session_id || keyObject;
    await this._getSessionKeyObject(sessionId);
    if (!this.sessionKeyObject) {
      HubLogger.miscLogger('Memcache', `Session not present in Memcache. May have already been stopped: ${sessionId}`, LL.INFO);
      this.responseData.statusCode = 200;
      return;
    }
    this.sessionKeyObject.stop_init_time = new Date();
    helper.updateOutsideBrowserstackTime(this.sessionKeyObject);
    if (constants.global_registry[this.sessionKeyObject.rails_session_id]) {
      constants.global_registry[this.sessionKeyObject.rails_session_id].pendingDelete = true;
    }
    pubSub.publish(constants.updateKeyObject, {
      session: this.sessionKeyObject.rails_session_id,
      changed: {
        pendingDelete: true,
      },
    });

    this.logAndClearSessionInAsync();
    this.responseData.statusCode = 200;
  }

  async logAndClearSessionInAsync() {
    // TODO: Discuss - Move to terminalInterface?
    const fetchAdditionalLogsPromisified = util.promisify(HubLogger.fetchAdditionalLogs);
    await fetchAdditionalLogsPromisified(this.sessionKeyObject);
    await this.instrumentGetUrlStuck();
    await this.removeStaleSession();
  }

  async _getSessionKeyObject(sessionId) {
    // FIXME: Optimise to not fetch from redis everytime, not changing while refactoring
    const getDataPromisified = util.promisify(ha.getData);
    let sessionKeyObj;
    try {
      sessionKeyObj = await getDataPromisified(sessionId) || undefined;
    } catch (e) {
      sessionKeyObj = undefined;
    }
    this.sessionKeyObject = sessionKeyObj;
  }

  async instrumentGetUrlStuck() {
    if (this.sessionKeyObject.browser === 'chrome' && this.sessionKeyObject.url_changed) {
      try {
        const res = await selenium.getOpenUrl(this.sessionKeyObject);
        if (res) {
          let parsedData;
          try {
            parsedData = JSON.parse(res.data);
          } catch (e) {
            HubLogger.exceptionLogger('GetURL Error', `SessionId: ${this.sessionKeyObject.rails_session_id}, Not a JSON response from node: ${res.data}`, this.sessionKeyObject.name);
          }
          if (parsedData && parsedData.value === 'data:,') {
            HubLogger.exceptionLogger('GetURL Stuck at data;,', `SessionId: ${this.sessionKeyObject.rails_session_id}`);
            // push to zombie
            HubLogger.seleniumStats('automate-url-stuck', this.sessionKeyObject, 'exception', '', '', 'automate-errors');
          }
        }
      } catch (e) {
        HubLogger.exceptionLogger('GetURL Error', `${e.type} while getURL ${e.error}`);
      }
    }
  }

  // TODO: Move to rails interface and refactor this
  async removeStaleSession() {
    const isAppAutomate = this.sessionKeyObject.appTesting || false;
    const { timeoutMessage = 'TIMEOUT', errorMessage = undefined } = this.request;
    const exceptionIdentifier = constants.global_registry[this.sessionKeyObject.rails_session_id] && (constants.global_registry[this.sessionKeyObject.rails_session_id].exceptionClass || constants.global_registry[this.sessionKeyObject.rails_session_id].exceptionMessage);
    if (exceptionIdentifier) {
      HubLogger.seleniumStats('pre-quit-exception', this.sessionKeyObject, constants.global_registry[this.sessionKeyObject.rails_session_id].user, exceptionIdentifier, constants.global_registry[this.sessionKeyObject.rails_session_id].exceptionRequest);
    }

    this.sendStoptoRails(timeoutMessage, isAppAutomate, errorMessage);

    helper.checkNonZeroStatusErrors(this.sessionKeyObject);
    helper.checkUdpKeystoSend(this.sessionKeyObject);
    HubLogger.seleniumStats('selenium-session-stop', this.sessionKeyObject, 'timeout', '', '', 'selenium-timeout');
    helper.pushToCLS('idle_timeout', {
      user_id: this.sessionKeyObject.user,
      session_id: this.sessionKeyObject.rails_session_id,
    }, isAppAutomate);
    HubLogger.miscLogger('Hub', `session: ${this.sessionKeyObject.rails_session_id} to be deleted`, LL.DEBUG);
    constants.global_registry[this.sessionKeyObject.rails_session_id] = this.sessionKeyObject;
    // not calling with params apart from keyObject because no delete/timeout publish in flow
    helper.sessionRemovedFromRegionHook(this.sessionKeyObject);
  }

  sendStoptoRails(timeoutMessage, isAppAutomate, errorMessage) {
    const { stopUrl, stopPostParams } = helper.buildRailsStopUrlParams(this.sessionKeyObject, constants.global_registry[this.sessionKeyObject.rails_session_id], timeoutMessage);
    const stopMessage = isAppAutomate ? errorMessages.APP_AUTOMATE_ERROR_MESSAGES[timeoutMessage] : timeoutMessage;
    // TODO: Relook to not use callback once browserstack is refactored
    this.sessionKeyObject.errorMessage = errorMessage;
    browserstack.postBrowserStack(stopUrl, stopPostParams, undefined, undefined, () => {
      HubLogger.addStopToRawLogs(this.sessionKeyObject, this.sessionKeyObject.rails_session_id, stopMessage, 1, false, undefined, errorMessage);
      helper.pingDataToStats(this.sessionKeyObject);
      helper.sessionRemovedFromRegionHook(this.sessionKeyObject, true);
    });
  }

  sendResponse() {
    this.response.writeHead(this.responseData.statusCode, this.responseData.headers || {});
    this.response.end();
  }
}

module.exports = { SessionTimedoutHandler };
