'use strict';

const nodeurl = require('url');

const constants = require('../../../constants');
const HubLogger = require('../../../log');
const myLock = require('../../../semaphore');
const helper = require('../../../helper');
const sessionManagerHelper = require('../../../services/session/sessionManagerHelper');

const LL = constants.LOG_LEVEL;

class GetSessionDataHandler {
  constructor(request, response) {
    this.request = request;
    this.response = response;
    this.sessionKeyObj = undefined;
    this.responseData = {
      headers: { 'content-type': 'application/json; charset=utf-8', accept: 'application/json', 'WWW-Authenticate': 'Basic realm="BrowserStack Selenium Hub"' },
      statusCode: 200
    };
  }

  async handle() {
    const { query } = nodeurl.parse(this.request.url, true);
    // FIXME: There are no else conditions here. Not changing this during refactoring.
    // sessionId can be this.request.url.split('/')[3] or query.sessionId
    let { sessionId: sessionIdKey } = query;
    const { cdp: fromCdp } = query;
    const parts = this.request.url.split('/');
    sessionIdKey = sessionIdKey || (parts.length > 3 ? parts[3] : undefined);
    if (!sessionIdKey) {
      this.responseData.data = '{"found":false}';
      return;
    }

    HubLogger.miscLogger('get_session_data', `getting session data for session ${sessionIdKey}`, LL.INFO);
    try {
      await myLock.checkSemaphore('session_stop_semaphore', sessionIdKey);
      HubLogger.miscLogger('get_session_data', `session_stop_semaphore lock present for session - ${sessionIdKey}`, LL.INFO);
      this.responseData.data = '{"found":false}';
    } catch (error) {
      await this.getSessionData(sessionIdKey, fromCdp);
    }
  }

  async getSessionData(sessionIdKey, fromCdp = false) {
    this.sessionKeyObj = await sessionManagerHelper.getKeyObject(sessionIdKey);
    const log = !this.sessionKeyObj ? `Session Data not found for ${sessionIdKey}.` : `Session Data for ${sessionIdKey}, with pendingDelete ${this.sessionKeyObj.pendingDelete}.`;
    HubLogger.miscLogger('get_session_data', log, LL.DEBUG, this.sessionKeyObj ? this.sessionKeyObj.debugSession : false);
    if (!this.sessionKeyObj || (this.sessionKeyObj && this.sessionKeyObj.pendingDelete)) {
      this.responseData.data = '{"found":false}';
      return;
    }
    if (this.sessionKeyObj && fromCdp && (this.sessionKeyObj.request_count > 0)) {
      this.responseData.data = '{"found":false}';
      return;
    }
    this.sessionKeyObj.found = true; // TODO: Can we remove this if not required
    this.responseData.data = JSON.stringify(this.sessionKeyObj);
    helper.sessionRemovedFromRegionHook(this.sessionKeyObj, false, sessionIdKey, true);
  }

  sendResponse() {
    this.response.writeHead(this.responseData.statusCode, this.responseData.headers);
    this.response.end(this.responseData.data);
  }
}

module.exports = { GetSessionDataHandler };
