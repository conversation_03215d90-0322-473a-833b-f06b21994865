'use strict';

const { StopByBsHandler } = require('./handlers/StopByBsHandler');
const { SessionTimedoutHandler } = require('./handlers/SessionTimedoutHandler');
const { GetSessionDataHandler } = require('./handlers/GetSessionDataHandler');

class SessionsController {
  async stopByBs(request, response) {
    const handler = new StopByBsHandler(request, response);
    await handler.handle();
    handler.sendResponse();
  }

  async sessionTimedout(request, response) {
    const handler = new SessionTimedoutHandler(request, response);
    await handler.handle();
    handler.sendResponse();
  }

  async getSessionData(request, response) {
    const handler = new GetSessionDataHandler(request, response);
    await handler.handle();
    handler.sendResponse();
  }
}

module.exports = { SessionsController };
