#!/usr/bin/env ruby

old_files = [
  'bridge.js',
  'browserstack.js',
  'chunkedResponse.js',
  'hub.js',
  'supporting.js',
  'queueHandler.js',
  'start_session.js',
  'errorMessages.js',
  'helper.js',
  'constants.js',
  'apps/kafkaUploader/kafkaUploaderRun.js',
  'utils/commandMapper/customPayloads.js',
  'utils/commandMapper/index.js',
  'utils/commandMapper/actionPayload.js',
  'utils/commandMapper/mapToOSS.js',
  'typeSanity/index.js',
  'helpers/redactor/index.js',
  'helpers/customSeleniumHandling/acceptSSLHandler/iOSAcceptSSLCertHandler.js',
  'helpers/customSeleniumHandling/acceptSSLHandler/safariAcceptSSLHandler.js',
  'helpers/customSeleniumHandling/acceptSSLHandler/mainAcceptSSLHandler.js',
  'helpers/transition-check/index.js',
  'ha.js',
]
show_warnings = false

files = [
  'apps/timeoutManager/timeoutsListener.js',
  'apps/timeoutManager/timeoutManager.js',
  'test/helper.js',
  'process_stats.js',
  'railsRequests/railsDefender.js',
  'railsRequests/railsPipeline.js',
  'railsRequests/railsPipelineHelper.js',
  'apps/monitoringService/monitoringService.js',
  'apps/monitoringService/monitoringHelper.js',
  'apps/kafkaUploader/kafkaUploader.js',
  'apps/kafkaUploader/kafkaHelper.js',
  'apps/kafkaUploader/kafkaEdsPipeline.js',
  'apps/kafkaUploader/kafkaS3pipeline.js',
  'apps/kafkaUploader/kafkaBatchFiles.js',
  'apps/kafkaUploader/kafkaLogger.js',
  'clients/kafka/kafkaClient.js',
  'logger.js',
  'cluster.js',
  'clusterUtil.js',
  'alertManager.js',
  'helpers/customSeleniumHandling/basicAuthHandler/base/basicAuthHandlerBase.js',
  'helpers/customSeleniumHandling/basicAuthHandler/handlers/iosBasicAuthHandler.js',
  'helpers/customSeleniumHandling/basicAuthHandler/handlers/macBasicAuthHandler.js',
  'helpers/customSeleniumHandling/basicAuthHandler/handlers/windowsBasicAuthHandler.js',
  'helpers/maskSensitiveInformation.js',
  'helpers/soTimeoutBucketUtil.js',
  'helpers/objectOperations.js',
  'helpers/qig/index.js',
  'helpers/customSeleniumHandling/restAPIHandler.js',
  'helpers/customSeleniumHandling/customExecutorHelper.js',
  'helpers/instrumentation.js',
  'helpers/jsonHelper.js',
  'utils/performance-tracker/index.js',
  'helpers/redactor/capabilities.js',
  'debug.js',
  'lib/request/index.js',
  'wdaClient.js',
  'seleniumClient.js',
  'redisUtils/index.js',
  'apps/timeoutManager/run.js',
  'utils/commandMapper/mapToW3C.js',
  'apps/monitoringService/run.js',
  'pubSub.js',
  'helpers/dnsCacheObject.js',
  'helpers/dnscache.js',
  'privoxy/**/*.js',
  'transformers/basic/index.js',
  'socketManagers/**/*.js',
  'webSocketHandler.js',
  'clientRequest/**/*.js',
  'semaphore.js'
]

refactor_files = [
  'controllers/**/**/*.js',
  'controllers/**/*.js',
  'lib/**/*.js',
  'services/**/**/*.js',
  'services/**/*.js',
  'hubWorker/**/*.js',
  'hubWorker/*.js',
]

lua_scripts_dir = 'script/lua_scripts'

spec_files = [
  'test/behaviour/luaScripts/railsRequestScripts.js',
  'test/behaviour/luaScripts/manipulateNumbers.js',
  'test/behaviour/railsPipeline.js',
  'test/behaviour/seleniumClient.test.js',
  'test/behaviour/hubStatus.test.js',
  'test/unit/instrumentation.js',
  'test/unit/monitoring.js',
  'test/unit/restAPIHandler.js',
  'test/unit/customExecutorHelper.js',
  'test/unit/hub.test.js',
  'test/dnsCache/testDns.js',
  'test/dnsCache/testDnsCache.js',
  'test/dnsCache/testDnsCoverage.js',
  'test/unit/log.test.js',
  'test/unit/kafkaClient.test.js',
  'test/unit/ha.test.js',
  'test/unit/utils/performance-tracker/index.test.js',
  'test/unit/framework/**/*.test.js',
  'test/behaviour/cookieTesting.test.js',
  'test/transformers/basic/index.test.js',
  'test/unit/alertManager.test.js',
  'test/unit/constants.test.js',
  'test/unit/pubSub.test.js',
  'test/unit/cluster.js',
  'test/unit/process_stats.test.js',
  'test/unit/redisUtils.test.js',
  'test/unit/timeoutManager/*.test.js',
  'test/unit/kafkaUploader/*.test.js',
  'test/helpers/qig/index.test.js',
  'test/helpers/soTimeoutBucketUtil.test.js',
  'test/helpers/jsonHelper.js',
  'test/unit/controllers/**/*.js',
  'test/unit/controllers/**/**/*.js',
  'test/unit/lib/**/*.js',
  'test/unit/services/**/**/*.js',
  'test/unit/services/**/*.js',
  'test/unit/hubWorker/*.js',
]

exit system("node_modules/.bin/eslint -c .eslintrc_old.json #{'--quiet' unless show_warnings} #{old_files.join ' '}") &&
     system("node_modules/.bin/eslint -c .eslintrc.js #{files.join ' '}") &&
     system("node_modules/.bin/eslint -c .eslintrc_refactor.js #{refactor_files.join ' '}") &&
     system("node_modules/.bin/eslint -c .eslintrc_specs.js #{spec_files.join ' '}" )
