# SeleniumHub
[![Build Status](https://jenkins.bsstag.com/buildStatus/icon?job=selenium_hub_coverage_master&style=flat-square)](https://jenkins.bsstag.com/view/Coverage/job/selenium_hub_coverage_master/)
[![Build Status](https://jenkins.bsstag.com/buildStatus/icon?job=selenium_hub_coverage_master&style=flat-square&&subject=Coverage&color=lightgrey&status=ViewHere)](https://jenkins.bsstag.com/view/Coverage/job/selenium_hub_coverage_master/lastSuccessfulBuild/artifact/coverage.json)

## Setup

Before setting up SeleniumHub make sure you have access to the following repositories:

* `node-hoothoot`

Make sure you are using the node version present in the `.nvmrc` file. We use `nvm` for managing different node versions.

Clone this repo and execute the following commands:

```bash
brew install luarocks  # if not present already
npm install
cp conf.json.sample conf.json
```

Update `conf.json` to update the following values:

* `bs_host`: rails app host name
* `bs_port`: rails app port name
* `bs_app_host`: rails app host name
* `bs_app_port`: rails app port name
* `hub_host`: hub host name. Should be 'localhost' if you are running locally
* `hub_port`: hub port. ex. 8080
* `mail_to`: `["<EMAIL>"]`
* If you need to setup Redis sentinels locally, you have to add the following config properties.

  ```json

    "redis_new_config": {
      "sentinels": [{"host": "127.0.0.1", "port": 26379}],
      "name": "mymaster"
    },
    "redis_config_second": {
      "sentinels": [{"host": "127.0.0.1", "port": 26379}],
      "name": "mymastersecond"
    },
    "redis_config_ai": {
      "sentinels": [{"host": "127.0.0.1", "port": 26379}],
      "name": "mymasterai"
    }
  ```

If running rails through nginx, make sure you add following to server block in your `nginx.conf`. This is required to access video and logs on Automate dashboard

```Nginx
  server {
    ...
    location ~ ^/s3-upload/((?U).*)/((?U).*)/(.*)$ {
      resolver *******;
      proxy_pass https://$1.$2.amazonaws.com/$3?$args;
    }
    location ~ ^/s3-debug/(.*)$ {
      resolver *******;
      proxy_pass https://s3.amazonaws.com/$1?$args;
    }
    location ~/s3_logs/https://(?<s3host>.+?)/(?<s3path>.*)$ {
      resolver *******;
      proxy_set_header HOST $s3host;
      proxy_set_header Authorization '';
      proxy_pass http://$s3host/$s3path?$args;
      internal;
    }
    ...
  }
```

## Running

* `npm start` (or `npm hub.js`) [`npm start` starts a cluster of 3 node processes. To run just one node process use `node hub.js`. Usually you only need to run one process in your development environment]. Recommended to use `node cluster.js testing hub.js n`, n being the number of node processes you want to run. This eases out the process to restart the hub. All you need to do is, `touch tmp/restart.txt`.
* `node uploader.js` (for running uploader, which uploads the raw logs to S3.)
* `node timeoutManager/run.js` (for running timeoutManager, which is a service which is used for the IDLE_TIMEOUT functionality)

## Setting up git hooks

```
bash
cp hooks/* .git/hooks/
chmod 755 .git/hooks/*
```

## Tests

There are a set of lint rules that the code needs to pass. Use `npm run lint` to run it. It is currently enabled on a limited set of files.
To run the test suite run, `npm test`.

To run a single file, for example `firecommands.js` run, `npm test test/firecommands.js`.

## Configuring Pronto

You can run pronto locally to detect lint errors before pushing to Github.

* Install pronto and dependencies: `bundle install`
* Run pronto in your branch: `npm run pronto`
* [Check](https://github.com/prontolabs/pronto/tree/899c6f270ed80cce1585abf1cf1b0006899021f3#local-changes) for more flags

## Things to check if the setup does not run locally

In case Automate tests are unable to connect to railsApp or if logs and playback videos are not visible, please check the following:

* Check that railsApp is running locally (On port 3000 as per the default config)
* Nginx conifg changes have been made in the "server" block which listens to port 80 (Not the other server block which has the ssl config)
* AWS keys are set correctly in `keys.yml` on railsApp and in `conf.json` in SeleniumHub

## CDP Setup for running Playwright/Puppeteer/Sel4

Reconnecting websocket proxy should be used for all websocket based supported frameworks. Although source code for this proxy is open source, there are still some files added inside this (SeleniumHub) repo which needs to be kept private. Follow the below mentioned steps:

* Clone the [ws-reconnect-proxy](https://github.com/browserstack/ws-reconnect-proxy/) repo and setup.
* Copy files from SeleniumHub/cdp to ws-reconnect-proxy directory.
    *  `cp -r <SeleniumHubPath>/cdp/* <WsReconnectProxyPath>/.`
* Change config to use hub port as cdp upstream
    *  Set upstream as `ws://localhost:<HUB_PORT || 8080>` inside `ws-reconnect-proxy/cdp/lib/config.json`
    *  Restart the proxy to use the new config.
* While running a cdp session use ws-reconnect-proxy port for client to hub connections.
