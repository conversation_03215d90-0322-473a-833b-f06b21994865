'use strict';

/* eslint-disable no-unused-expressions */

const helper = require('./helper');
const constants = require('./constants');
const HubLogger = require('./log');

const LL = constants.LOG_LEVEL;

// clears out semaphore by session id

const clearSeamphoreBySessionId = (keyname, sessionId) => new Promise((resolve) => {
  HubLogger.miscLogger('clearSemaphore', `Executing clear seamphore for key ${keyname} sessionId ${sessionId}`);
  helper.redisClientSecond.zrem(keyname, sessionId, (err, data) => {
    if (err) {
      HubLogger.exceptionLogger(`Failed to clear semaphore for session id ${sessionId}`, undefined, undefined, err);
    } else if (data !== 0) {
      HubLogger.miscLogger('clearSemaphore', `Cleared out semaphore for sessionId ${sessionId}`, LL.INFO);
    }
    return resolve();
  });
});


// default ttl 10 minutes
const clearSemaphore = keyname => new Promise((resolve) => {
  HubLogger.miscLogger('clearSemaphore', `Executing clear semaphore for key ${keyname}`, LL.DEBUG);
  helper.redisClientSecond
    .zremrangebyscore(keyname, 0, Date.now(), (err, data) => {
      if (err) {
        HubLogger.exceptionLogger('Failed while clearing the semaphore', undefined, undefined, err);
      } else if (data !== 0) {
        HubLogger.miscLogger('clearSemaphore', `Cleared out semaphores for sessions: ${data}`, LL.INFO);
      }
      return resolve();
    });
});

const checkSemaphore = (lockName, lockField) => new Promise((resolve, reject) => {
  helper.redisClientSecond.zscore(lockName, lockField, (redisError, redisResponse) => {
    redisResponse ? resolve() : reject(); // return true only in case we get ok(1) response from redis
  });
});

const ttlSemaphore = (lockName, lockField, ttl = constants.DEFAULT_SEMAPHORE_TTL) => new Promise((resolve, reject) => {
  helper.redisClientSecond.zadd([lockName, Date.now() + ttl, lockField], (redisError, redisResponse) => {
    (redisResponse || redisError) ? resolve() : reject();
  });
});

exports.clearSemaphore = clearSemaphore;
exports.clearSeamphoreBySessionId = clearSeamphoreBySessionId;
exports.ttlSemaphore = ttlSemaphore;
exports.checkSemaphore = checkSemaphore;
