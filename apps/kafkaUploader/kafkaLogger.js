'use strict';

const os = require('os');
const winston = require('winston');

const constants = require('../../constants');

const kafkaConfig = constants.kafkaConfig;
const topic = process.env.CONSUMER_TOPIC;
const consumerId = process.env.CONSUMER_ID;
const LOG_TAG = `[${kafkaConfig.LOG_TAG}_${topic.toUpperCase()}_${consumerId}]`;
const { Chitragupta } = require('chitragupta');

const filepath = `${constants.kafkaConfig.LOGGER_DIRECTORY_PATH}/kafkaUploader_${topic}_${consumerId}.log`;
// Initialize Logger
const logger = (() => {
  const config = {
    json: false,
  };
  return new (winston.Logger)({
    transports: constants.isProductionEnv ? [
      new winston.transports.File({
        ...config,
        filename: filepath,
        level: 'info',
        formatter: Chitragupta.jsonLogFormatter,
      }),
    ] :
      [
        new winston.transports.Console({
          ...config,
          label: os.hostname(),
          colorize: true,
          stringify: JSON.stringify,
          level: process.env.LOG_LEVEL || 'debug',
          silent: constants.disableLogs,
        }),
      ],
  });
})();

logger.rewriters.push((level, msg, meta) => {
  meta.log_tag = LOG_TAG;
  if (constants.isProductionEnv) {
    meta.meta = constants.log_meta;
    meta.meta.file = filepath;
  }
  return meta;
});


exports.logger = logger;
