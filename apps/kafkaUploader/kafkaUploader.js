'use strict';

const path = require('path');
const fs = require('fs');
const crypto = require('crypto');
const { inspect, promisify } = require('util');

const NodeCache = require('node-cache');
const fsExtra = require('fs-extra');

const constants = require('../../constants');
const { originalTopicName } = require('../../transformers/basic');

const helper = require('../../helper');
const kafkaHelper = require('./kafkaHelper');
const logger = require('./kafkaLogger').logger;
const { kafkaS3Pipeline } = require('./kafkaS3pipeline');
const { edsPipeline } = require('./kafkaEdsPipeline');
const { hilPipeline } = require('./hubInstrumentationConsumerHandlers');

const redisClient = require('../../redisUtils').redisClient;
const HubLogger = require('../../log');

const kafka = require('../../clients/kafka/kafkaClient');

const kafkaConfig = constants.kafkaConfig;
const watchPath = path.resolve(kafkaConfig.restartWatchFilePath);
const LOG_DATA_MESSAGE_TYPE = kafkaConfig.LOG_DATA_MESSAGE_TYPE;

const topic = process.env.CONSUMER_TOPIC;
const consumerId = process.env.CONSUMER_ID;

const LOG_TAG = `[${kafkaConfig.LOG_TAG}_${topic.toUpperCase()}_${consumerId}]`;

let continueState = null;

// Cache fds so as to prevent multiple open/close ops
let fdCache = {};

/* Process messages only for those sessions that are in this cache.
   In case of deploys, when reading back from time we do not consider
   messages of older sessions. */
let sessionsToProcess = {};

// Cache sessionId-requestId md5 so as to prevent duplicate message processing
let processedMessages = {};

/* Readback mode set when reading from older offsets. In this case
we ignore later messages from earlier sessions */
exports.readBackMode = false;
const consumers = {};

// Hoothoot Stuff
const hoothootQueueName = `AUTOMATE_Q_${topic.toUpperCase()}_${consumerId}`;
const hoothootStats = {
  uploadError: 0,
  uploadSuccessfull: {},
  uploadTime: {},
  uploadSize: {},
};
let lastUploadSuccessfulCountCheckAt = new Date();
let uploadSuccessfullCount = {};
// Check for upload successfull count every 30 minutes.
const lastUploadSuccessfulCountCheckInterval = 1800;

let offsetOutOfRangeCount = 0;
let offsetFetchErrorCount = 0;
let lastOffsetFetchError = '';
const pauseCheckInterval = 10 * 60 * 1000;

/*  Structure that holds offsets for topic-partitions that were read */
const partitionOffsetCommitted = {};

// #################### BEGIN OFFSET MANAGEMENT CODE ##############
/*  Since we aren't using consumer group, we've to handle committing, resuming.
    Also need to handle deploys of addition of a new consumer.
    kafka-node doesn't support time stamps(java does) :(
    So set offsets for each partition in redis, so consumer can start reading
    from offsets.
    In case of fresh consumer additions, you need to read back 4 hours.
*/

/*  Return hour string. e.g. 2018_1_16_11 -> 11 AM on 16th Jan 2018 */
const getHourIdentifier = (hoursAgo) => {
  const d = new Date();
  if (hoursAgo) { // Go back in time
    d.setHours(d.getHours() - hoursAgo);
  }
  return `${d.getYear() + 1900}_${d.getMonth() + 1}_${d.getDate()}_${d.getHours()}`;
};

/*  Commit latest read offsets to redis */
const writeOffsetsToSet = async (hrSuffix) => {
  const hourSuffix = hrSuffix ? `_${hrSuffix}` : '';
  logger.info(`Committing partition offsets ${hourSuffix}`); // partitionOffsetCommitted);
  const keys = Object.keys(partitionOffsetCommitted);
  if (!keys.length) {
    return;
  }
  const setName = `${topic}${kafkaConfig.REDIS_COMMITTED_KEY_NAME_SUFFIX}${hourSuffix}`;
  const partitionOffsets = [setName];
  keys.forEach(key => partitionOffsets.push(key, partitionOffsetCommitted[key]));

  try {
    await redisClient.hmset(partitionOffsets);
    await redisClient.expire(setName, 14 * 24 * 60 * 60);
  } catch (err) {
    logger.error(`Error during saving offsets to redis: ${inspect(
      err,
      { depth: 1, showHidden: false }
    )}`);
  }
};

const writeOffsetsToFile = async (hourIdentifier) => {
  const dirPath = path.resolve(kafkaConfig.checkpointDirPath);
  const filePath = `${dirPath}/${topic}_${hourIdentifier}.json`;
  const commitData = JSON.stringify(partitionOffsetCommitted);
  try {
    if (!fs.existsSync(dirPath)) {
      await kafkaHelper.fsMkdirAsync(dirPath);
    }
    await kafkaHelper.fsWriteFileAsync(filePath, commitData);
  } catch (err) {
    logger.error(`Could not commit checkpoint data to file (${filePath}), Error: ${err}`);
  }
};

/* Commit data to sets, Also log the hourly offsets so that we can readback
   from 3 hours ago, in case of new consumer adds */
const commitOffsets = async () => {
  await writeOffsetsToSet();
  const hourIdentifier = getHourIdentifier();
  await writeOffsetsToSet(hourIdentifier);
  await writeOffsetsToFile(hourIdentifier); // Safeguard against doing "FLUSHALL" in Prod
};

// ############# END OF OFFSETS RELATED CODE ##################################
// ############## Housekeeping functions Start #################

const checkHighUploads = () => {
  const pendingUploads = kafkaS3Pipeline.length();
  if ((pendingUploads > 1000) && constants.isProductionEnv) {
    helper.sendAlerts(
      `${LOG_TAG} High Upload Pending Queue`,
      `Pending uploads: ${pendingUploads}`
    );
  }
};

exports.restartTime = null;
const exitFunction = async () => {
  await commitOffsets();
  const waitInterval = kafkaConfig.EXIT_WAIT_MS || 10 * 60 * 1000;
  const forceExit = (Date.now() > (exports.restartTime + waitInterval));
  const parallelUploads = kafkaS3Pipeline.running();
  const pendingUploads = kafkaS3Pipeline.length();
  logger.warn(`Check for exit. uploadsInProgress: ${parallelUploads}, pendingUploads: ${pendingUploads}, forceExit: ${forceExit}`);
  if ((parallelUploads < 1 && pendingUploads < 1) || forceExit) {
    logger.warn(`Exiting ${forceExit} uploadsInProgress: ${parallelUploads}, pendingUploads: ${pendingUploads}`);
    process.exit(0);
  }
  setTimeout(exitFunction, 30 * 1000);
};
// ############## Housekeeping functions end #################
// ######################### PAUSE RESUME HANDLING #################
let consumerPaused = null;
let onMessageCallbackPaused = null;

const pauseConsumers = () => {
  if (consumerPaused) {
    return;
  }
  const keys = Object.keys(consumers);
  for (let kIter = 0; kIter < keys.length; kIter += 1) {
    logger.warn(`PAUSING CONSUMER ${keys[kIter]}`);
    consumers[keys[kIter]].pause();
  }
  consumerPaused = Date.now();
};

const resumeConsumers = () => {
  const keys = Object.keys(consumers);
  for (let kIter = 0; kIter < keys.length; kIter += 1) {
    logger.warn(`RESUMING CONSUMER ${keys[kIter]}`);
    consumers[keys[kIter]].resume();
  }
  consumerPaused = null;
};

const fetchOffsetsFromBrokerAndResume = async () => {
  const region = constants.region;
  const partitions = kafkaConfig.consumer[topic][region][consumerId].partitions;
  // time:
  // default Date.now(),
  // -1 to receive the latest offsets,
  // -2 to receive the earliest available offset
  const topicPartitions = partitions.map(partition => ({ topic, partition, time: -1 }));
  logger.info(`Current committed offsets before fetching: ${JSON.stringify(partitionOffsetCommitted)}`);

  const kafkaOffset = kafka.initKafkaOffset();
  const kafkaOffsetFetchAsync = promisify(kafkaOffset.fetch.bind(kafkaOffset));
  try {
    const data = await kafkaOffsetFetchAsync(topicPartitions);
    logger.info(`offsetOutOfRange fetch data: ${JSON.stringify(data)}`);
    partitions.forEach((partition) => {
      const fetchedOffset = data[topic][partition][0];
      partitionOffsetCommitted[partition] = fetchedOffset;
      consumers[`${topic}_${consumerId}`].setOffset(topic, partition, fetchedOffset);
    });
    resumeConsumers();
    commitOffsets();
  } catch (err) {
    offsetFetchErrorCount += 1;
    lastOffsetFetchError = err;
    logger.error(`offsetOutOfRange fetch err: ${err}`);
  }
};

const alertIfTooLongPause = () => {
  if (!constants.isProductionEnv) {
    return;
  }

  if (consumerPaused) {
    const pausedAt = consumerPaused;
    if ((Date.now() - pausedAt) < pauseCheckInterval) {
      return;
    }
    const pausedAtDate = new Date(pausedAt).toString();
    logger.error(`Consumer was paused at ${pausedAtDate}`);
    helper.sendAlerts(`${LOG_TAG} Consumer paused since ${pausedAtDate}}\n\n` +
      `uploadInProgress: ${kafkaS3Pipeline.running()}\n\n` +
      `uploadQueueSize: ${kafkaS3Pipeline.length()}`);
  }

  if (onMessageCallbackPaused) {
    const onMessageCallbackPausedAt = onMessageCallbackPaused;
    if ((Date.now() - onMessageCallbackPausedAt) < pauseCheckInterval) {
      return;
    }
    const onMessageCallbackPausedAtDate = new Date(onMessageCallbackPausedAt).toString();
    logger.error(`onMessageCallback was paused at ${onMessageCallbackPausedAtDate}`);
    helper.sendAlerts(`${LOG_TAG} onMessageCallback paused since ${onMessageCallbackPausedAtDate}}\n\n`);

    // touch tmp restart-uploader.txt
    try {
      fs.utimesSync(watchPath, new Date(), new Date());
      helper.sendAlerts(`${LOG_TAG} Did touch tmp for restarting uploaders, please monitor the metrics\n\n`);
    } catch (error) {
      logger.error(`Error in touching tmp: ${error}`);
      helper.sendAlerts(`${LOG_TAG} Error in touch tmp: ${error}\n\n`);
    }
  }
};
// ######################### PAUSE RESUME HANDLING END #################


//   ##############   INIT CODE #######################
/*  This function basically reads the config and creates topic partition offset
    confs for the consumer to read on. It reads data in the set from redis and
    sets the appropriate offsets */
const getTopicPartitionOffsets = (setSuffix, opts, cb) => {
  const options = opts;
  const region = constants.region;
  const partitions = kafkaConfig.consumer[topic][region][consumerId].partitions;
  const topicPartitions = [];

  const setName = topic + kafkaConfig.REDIS_COMMITTED_KEY_NAME_SUFFIX + setSuffix;

  redisClient.hgetall(setName, (err, offsetsToResumeFrom) => {
    logger.info(`Got the following info offsets ${setName} ${JSON.stringify(offsetsToResumeFrom)}`);
    if (!Object.keys(offsetsToResumeFrom).length) {
      logger.info(`\t\t\tWARN!!! SET: ${setName}\t\tNOT FOUND... DEFAULTING TO LATEST\n\n`);
      delete options.fromOffset;
    } else {
      options.fromOffset = true;
      logger.info(`\t\t\tSET: ${setName}\t\tFOUND..\n\n`);
    }
    partitions.forEach((key) => {
      const readOffset = offsetsToResumeFrom[key.toString()];
      const tpo = { topic, partition: key, offset: readOffset || 0 };
      topicPartitions.push(tpo);
      if (readOffset !== undefined) { // 0 is a valid offset
        const intReadOffset = parseInt(readOffset, 10);
        partitionOffsetCommitted[key] = intReadOffset;
        tpo.offset = intReadOffset + 1;
      }
    });
    cb(topicPartitions);
  });
};

const initCaches = () => {
  sessionsToProcess = new NodeCache({
    stdTTL: kafkaConfig.MAX_SESSION_DURATION_SECONDS,
    checkperiod: 60,
  });
  sessionsToProcess.on('expired', (key) => {
    logger.warn(`Expired session: ${key}`);
  });
  fdCache = new NodeCache({ stdTTL: 60, checkperiod: 10 });
  fdCache.on('expired', (filePath, fd) => {
    logger.warn(`Expired fd for ${filePath}`);
    fs.closeSync(fd);
  });
  processedMessages = new NodeCache({
    stdTTL: kafkaConfig.PROCESSED_MESSAGES_TTL_SECONDS,
    checkperiod: 10,
  });
};


// #################### END OF INIT CODE ##############


//  ############## CORE LOGIC OF CONSUMING DATA ##############################

/*  Default error handler */
const logKafkaError = (err) => {
  logger.error(`Consumer got : ${inspect(err, { depth: 0, showHidden: false })}`);
};

const onOffsetOutOfRangeCallback = (err) => {
  logKafkaError(err);
  if (offsetOutOfRangeCount === 0 && !consumerPaused) {
    pauseConsumers();
    fetchOffsetsFromBrokerAndResume();
  }
  offsetOutOfRangeCount += 1;
};

/*  Get read path for completed file */
const getReadPathForMessage = ({
  value: { rails_session_id: railsSessionId = '', requestId = '' },
}) => {
  const dirPath = kafkaHelper.getLogsDirPath(railsSessionId);
  const filePath = path.join(dirPath, `${railsSessionId}-${requestId}`);
  return filePath;
};

/*  Copy contents of reassembled chunked request into parent file */
const reAssembleChunk = (kafkaMessage) => {
  const chunkedFilePath = getReadPathForMessage(kafkaMessage);

  // Stop writing to chunk
  const chunkedFileFd = kafkaHelper.getWriteFdForMessage(kafkaMessage, fdCache);
  fs.closeSync(chunkedFileFd);
  fdCache.del(chunkedFilePath);

  const dirPath = kafkaHelper.getLogsDirPath(kafkaMessage.value.rails_session_id);
  const suffix = kafkaHelper.generateFileSuffixForKafkaTopic()[topic];
  const filePath = path.join(dirPath, `${kafkaMessage.value.rails_session_id}${suffix}`);
  const outfd = kafkaHelper.getWriteFdForPath(dirPath, filePath, fdCache);
  let flen = 0;

  try {
    flen = fs.statSync(chunkedFilePath).size;
  } catch (err) {
    const message = `Error in reAssembleChunk: ${err}; rails_session_id: ${kafkaMessage.value.rails_session_id};
      topic: ${topic} requestId: ${kafkaMessage.value.requestId}; kafkaMessage.offset: ${kafkaMessage.offset}`;
    logger.error(message);
    return;
  }
  const buffer = Buffer.alloc(flen); // Be done with it rather than loop.
  const infd = fs.openSync(chunkedFilePath, 'r');
  fs.readSync(infd, buffer, 0, buffer.length);
  fs.closeSync(infd);
  fs.writeSync(outfd, buffer, 0, buffer.length);

  fs.unlink(chunkedFilePath, (err) => {
    if (err) {
      logger.error(`unlinking chunked file ${err}`);
    }
  });
};

const handleMessageStop = (topic, kafkaMessage) => { // eslint-disable-line no-shadow
  switch (true) {
    case constants.KAFKA_S3_REGEX_OBJ.test(topic):
      kafkaS3Pipeline.push({ kafkaMessage, fdCache, hoothootStats }); // Proceed to upload and other ops
      break;
    case constants.KAFKA_EDS_LOGS_OBJ.test(topic):
      edsPipeline.push({ kafkaMessage, fdCache, hoothootStats });
      break;
    case constants.HUB_INSTRUMENTATION_LOGS_OBJ.test(topic):
      hilPipeline.push({ kafkaMessage, fdCache, hoothootStats });
      break;
    default:
      logger.error(`INVALID TOPIC ${topic}`);
      break;
  }
};


/*  Call appropriate methods depending on the type of message */
const handle = (kafkaMessage) => {
  try {
    switch (kafkaMessage.value.type) {
      case LOG_DATA_MESSAGE_TYPE.CHUNK_END:
        reAssembleChunk(kafkaMessage); // merge chunked req to the "main" file
        break;
      case LOG_DATA_MESSAGE_TYPE.CHUNK_START:
        kafkaHelper.getWriteFdForMessage(kafkaMessage, fdCache); // initing  writefd for chunked req
        break;
      case LOG_DATA_MESSAGE_TYPE.CHUNKED_MESSAGE:
      case LOG_DATA_MESSAGE_TYPE.START:
      case LOG_DATA_MESSAGE_TYPE.ATOMIC_MESSAGE:
        // sync, avoid reassemble
        fs.writeSync(kafkaHelper.getWriteFdForMessage(kafkaMessage, fdCache), kafkaMessage.value.message); // keep logging
        break;
      case LOG_DATA_MESSAGE_TYPE.STOP:
        logger.info(`STOP RECEIVED ${kafkaMessage.value.rails_session_id} for topic ${topic}`);
        handleMessageStop(topic, kafkaMessage);
        break;
      default:
        logger.info(`ERROR MESSAGE ${kafkaMessage.value}`);
        break;
    }
    return true;
  } catch (err) {
    helper.PingZombie({
      kind: 'kafka-handle-message-error',
      data: topic,
      region: constants.region,
      machine: constants.osHostName,
    });

    const errorStack = err.stack;
    const errorMessage = err.toString();
    logger.error(`Unable to handle and write message to file exception: ${err && (errorStack || errorMessage)}`);
    return false;
  }
};

/*  Entry level on message callback method */
const onMessageCallback = (msg) => {
  onMessageCallbackPaused = Date.now();
  const message = msg;
  try {
    message.value = JSON.parse(message.value);
  } catch (e) {
    helper.PingZombie({
      kind: 'kafka-log-parsing-error',
      data: topic,
      region: constants.region,
      machine: constants.osHostName,
    });
    logger.error(`Unable to parse message in kafka consumer! Error: ${e.message} Message: ${JSON.stringify(message).slice(0, 100)}`);
    return;
  }

  // console.log(" Debug ", message.partition, message.offset, message.value.rails_session_id);
  partitionOffsetCommitted[message.partition] = message.offset; // tracking what has been consumed
  const sessionId = message.value.rails_session_id;
  if (!sessionId) {
    logger.error(`Session ID is missing from the message ${JSON.stringify(message)}.`);
    return;
  }

  const requestId = message.value.requestId;
  if (!sessionsToProcess.get(sessionId)) { // No associated session found.
    // Check if this is fresh session
    const freshSession = parseInt(requestId, 10) < 1;
    if (exports.readBackMode && !freshSession) {
      logger.warn(`Ignoring later messages of past sessions for sessionId: ${sessionId} and requestId: ${requestId}`);
      return;
    }
    const oldPath = kafkaHelper.getLogsDirPath(sessionId); // clean up older remanants
    if (exports.readBackMode && fs.existsSync(oldPath)) {
      fsExtra.removeSync(oldPath);
    }
    // Set with TTL
    sessionsToProcess.set(sessionId, true, kafkaConfig.MAX_SESSION_DURATION_SECONDS);
  }

  const messageHash = crypto.createHash('sha1').update(sessionId + message.offset.toString()).digest('base64');
  if (!processedMessages.get(messageHash)) {
    if (handle(message)) processedMessages.set(messageHash, true);
  } else {
    logger.warn(`Duplicate message found: ${JSON.stringify(message)}`);
  }
};

const throttlingConsumers = () => {
  /*
    raw_logs: pause_threshold = 50    resume_threshold = 40
    console_logs": pause_threshold = 200    resume_threshold = 160
    exception_logs: pause_threshold = 100    resume_threshold = 80
    performance_logs: pause_threshold = 100    resume_threshold = 80
    instrumentation_logs: pause_threshold = 100    resume_threshold = 80
  */
  if (continueState) {
    logger.info('Initiated touch tmp');
  } else if (kafkaS3Pipeline.running() >= kafkaConfig.S3_UPLOAD_MAX_THRESHOLD[originalTopicName(topic)] && !consumerPaused) {
    logger.info(`Pausing consumers for ${topic} as PAUSE THRESHOLD-${kafkaConfig.S3_UPLOAD_MAX_THRESHOLD[originalTopicName(topic)]} has been reached`);
    pauseConsumers();
  } else if (kafkaS3Pipeline.running() <= (parseInt(kafkaConfig.S3_UPLOAD_MAX_THRESHOLD[originalTopicName(topic)] * kafkaConfig.KAFKA_CONSUMER_RESUME_RATIO, 10)) && consumerPaused) {
    logger.info(`Resuming consumers for ${topic} as RESUME THRESHOLD-${parseInt(kafkaConfig.S3_UPLOAD_MAX_THRESHOLD[originalTopicName(topic)] * kafkaConfig.KAFKA_CONSUMER_RESUME_RATIO, 10)} has been reached`);
    resumeConsumers();
  }
};

try {
  setInterval(throttlingConsumers, kafkaConfig.KAFKA_CONSUMER_CHECK_INTERVAL);
} catch (err) {
  logger.error(`Error in throttlingConsumers: ${err}`);
}

/*  Set event functions for the consumer */
const setEventFunctions = (consumer) => {
  consumer.on('message', onMessageCallback);
  consumer.on('offsetOutOfRange', onOffsetOutOfRangeCallback);
  consumer.on('error', logKafkaError);
};

// TODO - Should populate the data meta fields. Check how it can be achieved by passing the param
logger.info(`Watching ${watchPath}`);

fs.watchFile(watchPath, () => {
  logger.warn(`STOPPING ${process.pid}`);
  pauseConsumers();
  continueState = Date.now();
  exports.restartTime = Date.now();
  setTimeout(exitFunction, 30 * 1000); // Check for exit after 30 seconds
});

/**
 * Init function for all vars required to read from kafka & upload to s3.
 * If you want to replay data from x (x < 7 days.hours) hours ago, pass here;
 * useful for new deploys.
 *
 * @param {Number} readBackHours
 */
const initKafkaUploader = (readBackHours) => {
  initCaches();
  const options = {};
  // options.fromOffset = 'latest';
  const setSuffix = readBackHours ? `_${getHourIdentifier(readBackHours)}` : '';
  if (readBackHours) {
    logger.info(`Reading Back from ${readBackHours} hours.`);
    exports.readBackMode = true;
  }
  getTopicPartitionOffsets(setSuffix, options, (topicPartitions) => {
    const consumer = kafka.initConsumer(topicPartitions, options);
    consumers[`${topic}_${consumerId}`] = consumer;
    setEventFunctions(consumer);
  });
};

// Hoothoot Logic

// Dynamic Hoothoot Stats
hoothootStats.failedUploadDirectoryCount = () => new Promise((resolve) => {
  fs.readdir(kafkaHelper.getFailedLogsDirPath(), (err, files) => {
    resolve(err ? 0 : files.length);
  });
});
hoothootStats.uploadInProgress = () => kafkaS3Pipeline.running();
hoothootStats.uploadQueueSize = () => kafkaS3Pipeline.length();
hoothootStats.UploadQueueThreshold = () => kafkaConfig.S3_UPLOAD_MAX_THRESHOLD[originalTopicName(topic)];

const gatherUploadSuccessfulCount = () => {
  Object.keys(hoothootStats.uploadSuccessfull)
    .forEach((fileType) => {
      uploadSuccessfullCount[fileType] = uploadSuccessfullCount[fileType] || 0;
      uploadSuccessfullCount[fileType] += hoothootStats.uploadSuccessfull[fileType];
    });
};

const validateUploadSuccessfulCount = () => {
  const count = Object.values(uploadSuccessfullCount).reduce((a, b) => a + b, 0);
  if (count === 0) {
    const message = `No successfull uploads have been made in last ${lastUploadSuccessfulCountCheckInterval} seconds by ${constants.SERVER_NAME}`;
    logger.warn(message);
    helper.sendAlerts(
      `${LOG_TAG} ${constants.SERVER_NAME} is stuck, no successful uploads in last ${lastUploadSuccessfulCountCheckInterval} seconds`,
      message,
      ...Array(3),
      true
    );
    uploadSuccessfullCount = {};
    lastUploadSuccessfulCountCheckAt = new Date();
  }
};

const pushHoothootStatsAndReset = () => {
  const worker = (constants.worker_id || 0);

  const emit = ({ value, genre }) => {
    HubLogger.hoothoot.emit(
      constants.hoothootKafkaUploaderStatsKey,
      value,
      {
        genre,
        worker,
      },
    );
  };

  const processStat = async ({
    statObject,
    statKey,
    genrePrefix,
  }) => {
    const genre = `${genrePrefix}__${statKey}`;
    const stat = statObject[statKey];
    if (stat instanceof Array) {
      let max = 0;
      let mean = 0;
      if (stat.length > 0) {
        max = Math.max.apply(null, stat);
        mean = stat.reduce((a, b) => a + b, 0) / stat.length;
      }
      emit({
        value: mean,
        genre: `${genre}__mean`,
      });
      emit({
        value: max,
        genre: `${genre}__max`,
      });
      statObject[statKey] = [];
    } else if (typeof stat === 'object') {
      Object.keys(stat)
        .forEach(subStatKey => processStat({
          statObject: stat,
          statKey: subStatKey,
          genrePrefix: genre,
        }));
    } else if (typeof stat === 'function') {
      emit({
        value: await stat(),
        genre,
      });
    } else {
      emit({
        value: stat,
        genre,
      });
      statObject[statKey] = 0;
    }
  };

  gatherUploadSuccessfulCount();

  Object.keys(hoothootStats)
    .forEach(statKey => processStat({
      statObject: hoothootStats,
      statKey,
      genrePrefix: hoothootQueueName,
    }));

  const timeDiff = (new Date() - lastUploadSuccessfulCountCheckAt);
  if (timeDiff > lastUploadSuccessfulCountCheckInterval * 1000) {
    validateUploadSuccessfulCount();
  }
};

const checkAccumulatedErrors = () => {
  if (offsetOutOfRangeCount > 0) {
    helper.sendAlerts(
      `${LOG_TAG} offsetOutOfRange`,
      `Consumer ${topic}_${consumerId} Offset Out of Range`,
      null,
      false,
      ...Array(1),
      true
    );
    offsetOutOfRangeCount = 0;
  }
  if (offsetFetchErrorCount > 0) {
    helper.sendAlerts(
      `${LOG_TAG} offsetOutOfRange Fetch Error`,
      `Fetch Error: ${lastOffsetFetchError}.\nProcess will restart in 60 seconds.`,
      null, false
    );
    setTimeout(() => {
      process.exit(1);
    }, 60000);
  }
};

exports.startKafkaUploader = (readBackHours) => {
  setInterval(commitOffsets, 5 * 1000); // 5 seconds - commit interval
  setInterval(checkHighUploads, 5 * 60 * 1000);
  setInterval(alertIfTooLongPause, 5 * 60 * 1000);
  initKafkaUploader(readBackHours);

  // Push Hoothoot stats every 5 seconds.
  setInterval(pushHoothootStatsAndReset, 5 * 1000);

  // Check if the offsetOutOfRange or offsetFetchError has occurred and raise alert.
  setInterval(checkAccumulatedErrors, 60 * 1000);
};

// If the kafkaUploader.js is directly executed via node.js cli.
if (require.main === module) {
  exports.startKafkaUploader(process.env.READ_BACK_HOURS);
}
