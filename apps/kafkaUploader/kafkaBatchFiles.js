'use strict';

const { readdir, stat } = require('fs-extra');
const path = require('path');
const logger = require('./kafkaLogger').logger;
const { isUndefined } = require('../../typeSanity');

async function batchFiles(parentPath, limit) {
  if (isUndefined(parentPath) || limit === 0) {
    logger.warn(`Path is ${parentPath} or limit is ${limit}`);
    return [];
  }
  let stats;
  try {
    stats = await stat(parentPath);
  } catch (err) {
    throw new Error(`Stats error: ${parentPath}`);
  }
  if (!stats) {
    throw new Error(`Path does not exist: ${parentPath}`);
  }

  let files;

  if (stats.isDirectory()) {
    try {
      files = await readdir(parentPath);
    } catch (error) {
      files = [];
      logger.error(`Files.getFiles(): failed to read dir, error: ${error}`);
      throw new Error(`Failed to read directory: ${parentPath}`);
    }
  }

  const dirs = files.splice(0, limit);
  let pathdirs = [];
  pathdirs = dirs.map(childPath => path.join(parentPath, childPath));
  return pathdirs;
}

module.exports = {
  batchFiles,
};
