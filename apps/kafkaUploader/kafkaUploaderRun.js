'use strict';

const os = require('os');
const path = require('path');
const fs = require('fs');

const constants = require('../../constants');
const helper = require('../../helper');

const kafkaConfig = constants.kafkaConfig;
const kafkaUploader = require('./kafkaUploader');
const logger = require('./kafkaLogger').logger;
const dnsCache = require('../../helpers/dnscache');
const { Chitragupta } = require('chitragupta');
const preStartChecks = require('./preStartChecks');

dnsCache({
  enable: true,
  ttl: 30,
  cachesize: 1000,
});

const topic = process.env.CONSUMER_TOPIC;
const consumerId = process.env.CONSUMER_ID;

const LOG_TAG = `[${kafkaConfig.LOG_TAG}_${topic.toUpperCase()}_${consumerId}]`;

let hostname = os.hostname();

// This line below is just to test/reproduce HA locally for Kafka
if (process.env.HOSTNAME) {
  hostname = process.env.HOSTNAME;
}

const EXCLUDED_GLOBAL_EXCEPTIONS = [
  /Redis connection.*failed/
];

const MUTED_GLOBAL_EXCEPTIONS = [
  /refreshBrokerMetadata failed/
];

const lastSendPerMessage = {};
process.on('uncaughtException', (err) => {
  const errorStack = err.stack;
  const errorMessage = err.toString();
  const shouldAlert = (Date.now() - (lastSendPerMessage[errorMessage] || 0)) > kafkaConfig.UNCAUGHT_EXCEPTION_ALERT_PERIOD;
  logger.error(`Global Exception: ${err && (errorStack || errorMessage)}`);
  if (shouldAlert
    && constants.isProductionEnv
    && !EXCLUDED_GLOBAL_EXCEPTIONS.some(r => r.test(errorMessage))) {
    let alertMessage = errorMessage;
    if (err && err.stack) {
      alertMessage = `Message: ${errorMessage}\n\nHost: ${hostname}\n\nStack: ${errorStack}`;
    }
    helper.sendAlerts(
      `${LOG_TAG} Global Exception: ${errorMessage}`,
      alertMessage,
      null,
      !MUTED_GLOBAL_EXCEPTIONS.some(r => r.test(errorMessage))
    );
    lastSendPerMessage[errorMessage] = Date.now();
  }
});

if (!consumerId) {
  throw new Error('Consumer ID is not present, exiting.');
}

if (!topic) {
  throw new Error('Topic is not present, exiting.');
}

const redisWatchKey = `kafkaUploader_${topic}_${consumerId}`;
const intervals = {};
let wasIaSlaveEarlier = false;
const redisClient = helper.redisClientSecond;
const luaScriptsInfo = constants.redisBecomeMasterScripts;
luaScriptsInfo.manipulateNumber = constants.manipulateNumberScripts.manipulateNumber;

Object.keys(luaScriptsInfo).forEach((name) => {
  let script = '';
  try {
    script = fs.readFileSync(path.join(constants.ROOT_PATH, `/script/lua_scripts/${luaScriptsInfo[name].scriptPath}`));
  } catch (error) {
    logger.error(`Failed to initialize Lua scripts: ${error}`);
    helper.sendAlerts(
      `${LOG_TAG} initializeLuaScripts: ${error}`,
      `Message: ${error}\n\nStack: ${error.stack}`
    );
    process.exit(1);
  }

  redisClient.defineCommand(name, {
    numberOfKeys: luaScriptsInfo[name].numberOfKeys,
    lua: script,
  });
});

const acknowledgeAsAlive = async () => {
  logger.info(`Acknowledging as alive for ${redisWatchKey}...`);
  const currentTimestamp = new Date();
  try {
    await redisClient.hset([
      redisWatchKey,
      'timestamp',
      currentTimestamp.getTime()
    ]);
    logger.info(`Acknowledged as alive for ${redisWatchKey} with timestamp: ${currentTimestamp.toISOString()}; PID ${process.pid}.`);
  } catch (error) {
    logger.error(`Failed to Acknowledge as alive: ${error}`);
  }
};

const stillMaster = async () => {
  logger.info('Verifying as Master...');
  try {
    const response = await redisClient.hget([redisWatchKey, 'pid']);
    if (response && (response === process.pid.toString())) {
      logger.info(`Verified as Master, PID: ${response}`);
      return true;
    } else {
      logger.error(`Failed to verify as Master; Response: ${response} PID: ${process.pid}`);
    }
  } catch (error) {
    logger.error(`Failed to Read Response from Redis: ${error}`);
  }

  return false;
};

const tryToBecomeMaster = () => new Promise((resolve) => {
  redisClient.becomeMaster(
    [
      redisWatchKey,
      Date.now(),
      process.pid,
      kafkaConfig.MASTER_ACKNOWLEDGE_TIMEOUT,
      hostname
    ],
    (error, response) => {
      const didBecomeMaster = response && response.toString().match('I can become master');
      if (error) {
        logger.error(`Failed to become or check Master: ${error}`);
      } else {
        logger.info(`redis.becomeMaster: ${response}`);
      }
      resolve(didBecomeMaster);
    }
  );
});

const giveUpAsMaster = () => new Promise((resolve) => {
  redisClient.unsetMaster(redisWatchKey, process.pid, (error, response) => {
    if (error) {
      logger.error(`Failed to unset as master: ${error}`);
    }
    if (response) {
      logger.info(`Successfully unset as master: ${response}`);
    }
    resolve(response);
  });
});

const exit = async () => {
  stop();
  await giveUpAsMaster();
  process.exit(0);
};

const onBecomeMaster = () => {
  intervals.acknowledgeAsAlive = setInterval(acknowledgeAsAlive, kafkaConfig.ACKNOWLEDGE_AS_ALIVE_INTERVAL);
  intervals.stillMaster = setInterval(async () => {
    if (!(await stillMaster())) {
      exit();
    }
  }, kafkaConfig.CHECK_STILL_MASTER_INTERVAL);

  let readBackHours = parseInt(process.env.READ_BACK_HOURS || 0, 10);

  if (wasIaSlaveEarlier) {
    readBackHours = process.env.CONSUMER_TOPIC.includes('extended_duration') ? kafkaConfig.KAFKA_UPLOADER_READ_BACK_HOURS_EXTENDED: kafkaConfig.KAFKA_UPLOADER_READ_BACK_HOURS;
  }

  kafkaUploader.startKafkaUploader(readBackHours);
};

const becomeMasterCheck = async () => {
  if (await tryToBecomeMaster()) {
    logger.info(`I'm Master now: ${redisWatchKey} #${process.pid}`);
    clearInterval(intervals.becomeMasterCheck);
    onBecomeMaster();
  } else {
    logger.info('Got acknowledge from master, waiting...');
    wasIaSlaveEarlier = true;
  }
};

const start = async () => {
  // eslint-disable-next-line no-await-in-loop
  while (!await preStartChecks.capableToStartUploader(hostname, LOG_TAG)) {
    // eslint-disable-next-line no-await-in-loop
    await helper.sleep(constants.BECOME_MASTER_CAPABILITY_CHECK_PERIOD_MS);
  }
  becomeMasterCheck();
  intervals.becomeMasterCheck = setInterval(becomeMasterCheck, kafkaConfig.CHECK_SHOULD_BECOME_MASTER_INTERVAL);
};

const stop = () => {
  for (const intervalOf in intervals) {
    logger.info(`clearInterval of ${intervalOf} for PID: #${process.pid}`);
    clearInterval(intervals[intervalOf]);
  }
};

['SIGINT', 'SIGTERM'].forEach((signal) => {
  process.on(signal, async () => {
    logger.error(`Handling exit signal ${signal}...`);
    exit();
  });
});

Chitragupta.setupProcessLogger('kafkaUploader', start);
