'use strict';

/**
 * This file is mainly for dealing with s3 uploads and next steps.
 * exports `kafkaS3Pipeline`, a threadpool for S3 uploads, balanced and faster compared to batches / bursts
 */

const util = require('util');
const fs = require('fs');
const zlib = require('zlib');
const fsExtra = require('fs-extra');
const async = require('async');
const AWS = require('aws-sdk');

const constants = require('../../constants');
const kafkaHelper = require('./kafkaHelper');
const batchFiles = require('./kafkaBatchFiles').batchFiles;
const { originalTopicName, snakeToCamel } = require('../../transformers/basic');
const logger = require('./kafkaLogger').logger;
const { isUndefined } = require('../../typeSanity');
const { Events } = require('browserstack-dwh');
const helper = require('../../helper');

const kafkaConfig = constants.kafkaConfig;
const topic = process.env.CONSUMER_TOPIC;
const failedDirBasename = kafkaHelper.getFailedLogsDirPath();

const sanitizeConsoleLogs = (filename, sessionId) => new Promise((resolve, reject) => {
  fs.stat(filename, (err, stats) => {
    if (err) {
      return reject(err);
    } else if (stats.size === 0) {
      const message = constants.DEFAULT_CONSOLE_LOGS_DATA.replace(/<date>/, new Date().toISOString()).replace(/<session_id>/, sessionId);
      return fs.writeFile(filename, message, resolve);
    }
    return resolve();
  });
});

const sendDataToEds = (sessionid, isAppAutomate, isUploadSuccessful, filesize, err, aws_storage_class = 'STANDARD', compressed_size = -1) => {
  const topicForEDS = snakeToCamel(`kafka_${topic}`);
  const dataForEDS = {
    kind: isAppAutomate ? Events.APP_AUTOMATE_TEST_SESSIONS : Events.AUTOMATE_TEST_SESSIONS,
    hashed_id: sessionid,
    feature_usage: {
      [topicForEDS]: {
        success: isUploadSuccessful,
        size: filesize,
        storage_class: aws_storage_class,
        compressed_size // will be -1 if we didn't zip logs, else size of zipped file
      },
    },
  };
  if (err && err.message) {
    dataForEDS.feature_usage[topicForEDS].exception = err.message;
  }
  helper.sendToEDS(dataForEDS);
};

const saveErrorUploadLogs = async (sourceDir, targetDir) => {
  try {
    await fsExtra.move(sourceDir, targetDir, { overwrite: false });
  } catch (err) {
    logger.error(`Error while renaming ${sourceDir} to ${targetDir}`);
  }
};

const setS3Config = (sessionid, suffix, awsStorageClass, awsNewUserEnabled = false) => {
  const s3AccessKeyId = awsNewUserEnabled ? constants.LOGS_PUT_AWS_KEY : constants.LOGS_AWS_KEY;
  const s3SecretAccessKey = awsNewUserEnabled ? constants.LOGS_PUT_AWS_SECRET : constants.LOGS_AWS_SECRET;
  const s3Bucket = constants.LOGS_AWS_BUCKET;

  const s3Config = new AWS.S3({
    sslEnabled: false,
    apiVersion: '2006-03-01',
    accessKeyId: s3AccessKeyId,
    secretAccessKey: s3SecretAccessKey,
    maxRetries: constants.KAFKA_S3_UPLOAD_RETRIES,
    params: {
      Bucket: s3Bucket,
      Region: constants.region,
      StorageClass: awsStorageClass,
      Key: `${sessionid}/${sessionid}${suffix}`,
    },
  });

  return s3Config;
};

/*  This is copied and then modified from uploader.js */
const uploadToS3 = (filename, stats, sessionid, suffix, isAppAutomate, hoothootStats, awsStorageClass = 'STANDARD', enableHubLogsZipping = false, enableNewAWSUserForUpload = false, cb) => {
  const filesize = stats ? stats.size : 0;
  if (filesize === 0) {
    logger.warn(`skip uploading 0 size file ${sessionid}`);
    sendDataToEds(sessionid, isAppAutomate, true, filesize);
    cb();
    return;
  } else if (filesize < constants.AWS_MIN_OBJECT_SIZE) {
    // if file size lesser than 128 KB, upload to standard and not to IA.
    logger.warn(`skip storage class for size lesser than 128 KB ${sessionid}`);
    awsStorageClass = constants.AWS_STANDARD_STORAGE_CLASS;
  }

  const s3 = setS3Config(sessionid, suffix, awsStorageClass, enableNewAWSUserForUpload);

  const stream = fs.createReadStream(filename);
  const gzip = zlib.createGzip({ level: 1 });

  let compressedFileSize = -1;
  gzip.on('data', (data) => {
    compressedFileSize += data.length;
  }).on('end', () => {
    compressedFileSize += 1;
  });

  stream.on('error', (err) => {
    logger.info(`LOGS STREAM ERROR: ${err} for : ${sessionid}/${filename}`);
  });

  // Upload the stream
  const opts = { queueSize: 20, partSize: 1024 * 1024 * 10 };
  logger.info(`UPLOAD START for sessionid: ${sessionid} for ${topic}`);
  const start = (new Date()).getTime();

  let uploadTimeout = null;
  const s3UploadParams = {
    Body: stream,
    ACL: 'private',
    ContentType: 'text/plain',
  };
  let managedUpload;
  if (enableHubLogsZipping) {
    logger.info(`Trying to zip logs for ${sessionid} ${topic}`);
    try {
      const s3UploadParamsZipped = {
        ...s3UploadParams,
        Body: stream.pipe(gzip),
        ContentEncoding: 'gzip'
      };
      let zipS3 = null;
      if (compressedFileSize !== -1) {
        if (compressedFileSize < constants.AWS_MIN_OBJECT_SIZE) {
          // if file size lesser than 128 KB, upload to standard and not to IA.
          logger.warn(`skip storage class for compressed size lesser than 128 KB ${sessionid}`);
          awsStorageClass = constants.AWS_STANDARD_STORAGE_CLASS;
        }
        zipS3 = setS3Config(sessionid, suffix, awsStorageClass, enableNewAWSUserForUpload);
      }
      if (zipS3) {
        managedUpload = zipS3.upload(s3UploadParamsZipped, opts);
      } else {
        managedUpload = s3.upload(s3UploadParamsZipped, opts);
      }
    } catch (e) {
      compressedFileSize = -1; // set to -1 in case something went wrong while zipping
      logger.error(`Zipping logs failure  ${sessionid} ${topic} ${e}`);
      managedUpload = s3.upload(s3UploadParams, opts);
    }
  } else {
    managedUpload = s3.upload(s3UploadParams, opts);
  }
  managedUpload.send((err, data) => {
    clearTimeout(uploadTimeout);
    sendDataToEds(sessionid, isAppAutomate, isUndefined(err), filesize, err, awsStorageClass, compressedFileSize);
    if (err) {
      hoothootStats.uploadError += 1;
      logger.error(`UPLOAD ERROR: ${err} for sessionid: ${sessionid} for ${suffix}`);
      cb(err);
    } else {
      const uploadTime = ((new Date()).getTime() - start) / 1000;
      let logType = constants.defaultFileType;
      const logTypeMatch = filename.match(/-([a-z-]+)((?:-v2)?).txt$/);
      if (logTypeMatch && logTypeMatch[1]) {
        logType = logTypeMatch[1];
      }
      hoothootStats.uploadSuccessfull[logType] = hoothootStats.uploadSuccessfull[logType] || 0;
      hoothootStats.uploadSuccessfull[logType] += 1;
      hoothootStats.uploadTime[logType] = hoothootStats.uploadTime[logType] || [];
      hoothootStats.uploadTime[logType].push(uploadTime);
      hoothootStats.uploadSize[logType] = hoothootStats.uploadSize[logType] || [];
      hoothootStats.uploadSize[logType].push(filesize);
      logger.info(`FINISHED for sessionid: ${sessionid} for ${suffix} in ${uploadTime}s, size: ${filesize} with data: ${JSON.stringify(data)}`);
      cb();
    }
  });

  uploadTimeout = setTimeout(() => {
    logger.error(`Aborting file due to timeout ${sessionid}`);
    managedUpload.abort();
  }, kafkaConfig.FILE_UPLOAD_CUTOFF_MS || 300 * 1000);
};
const uploadToS3Async = util.promisify(uploadToS3);

const reUploadLogs = async (failedLogsDirPath) => {
  logger.info('STARTING Reupload of failed logs.', { log: { kind: 'REUPLOAD-FAILED-LOGS' } });
  let dirnameArray;
  try {
    dirnameArray = await batchFiles(failedLogsDirPath, kafkaConfig.BATCH_SIZE);
  } catch (err) {
    logger.error(`REUPLOAD ERROR - during directory fetch: ${err} for ${failedLogsDirPath}`, { log: { kind: 'REUPLOAD-FAILED-LOGS' } });
    return;
  }
  while (dirnameArray.length !== 0) {
    const dirname = dirnameArray.pop();
    const sessionid = dirname.split('/')[dirname.split('/').length - 1];
    let filenameArray;
    try {
      /* eslint-disable no-await-in-loop */
      filenameArray = await batchFiles(dirname, 4);
      /* eslint-enable no-await-in-loop */
    } catch (err) {
      logger.error(`REUPLOAD ERROR - GET LOG FILES ERROR: ${err} for ${dirname}`, { log: { uuid: sessionid, kind: 'REUPLOAD-FAILED-LOGS' } });
      return;
    }
    while (filenameArray.length !== 0) {
      const filename = filenameArray.pop();
      const getSuffix = filename.split('/')[filename.split('/').length - 1].split('-')[1];
      let suffix;

      if (getSuffix === 'console') {
        suffix = constants.fileSuffixForTopicV2.console_logs;
      } else if (getSuffix === 'exception') {
        suffix = constants.fileSuffixForTopicV2.exception_logs;
      } else if (getSuffix === 'performance') {
        suffix = constants.fileSuffixForTopicV2.performance_logs;
      } else {
        suffix = constants.fileSuffixForTopicV2.raw_logs; // Will work for raw_extended_duration_logs out of the box
      }

      const s3 = setS3Config(sessionid, suffix);

      const stream = fs.createReadStream(filename);

      stream.on('error', (err) => {
        logger.error(`REUPLOAD ERROR - LOGS STREAM ERROR: ${err} for : ${sessionid}/${filename}`, { log: { uuid: sessionid, kind: 'REUPLOAD-FAILED-LOGS' } });
        helper.PingZombie({
          kind: 'kafka-logs-reupload-failure-stream',
          error: err ? err.toString().substring(0, 200) : 'Stream error',
          data: topic,
          region: constants.region,
          machine: constants.osHostName.replace('services-auto-', ''),
        });
      });

      const opts = { queueSize: 20, partSize: 1024 * 1024 * 10 };
      logger.info(`REUPLOAD - UPLOAD START for sessionid: ${sessionid} for ${suffix}`, { log: { uuid: sessionid, kind: 'REUPLOAD-FAILED-LOGS' } });
      const start = (new Date()).getTime();

      let uploadTimeout = null;
      const managedUpload = s3.upload({
        Body: stream,
        ACL: 'private',
        ContentType: 'text/plain',
      }, opts);

      managedUpload.send((err, data) => {
        clearTimeout(uploadTimeout);
        if (err) {
          logger.error(`REUPLOAD ERROR - ${err} for sessionid: ${sessionid} for ${suffix}`, { log: { uuid: sessionid, kind: 'REUPLOAD-FAILED-LOGS' } });
          helper.PingZombie({
            kind: 'kafka-logs-reupload-failure',
            error: err ? err.toString().substring(0, 200) : 'Upload Error',
            data: topic,
            region: constants.region,
            machine: constants.osHostName.replace('services-auto-', ''),
          });
        } else {
          const uploadTime = ((new Date()).getTime() - start) / 1000;
          logger.info(`FINISHED for sessionid: ${sessionid} for ${suffix} in ${uploadTime}s with data: ${JSON.stringify(data)}`, { log: { uuid: sessionid, kind: 'REUPLOAD-FAILED-LOGS' } });
          fsExtra.removeSync(filename);
          try {
            logger.info(`REUPLOAD - DELETING ${dirname}`, { log: { uuid: sessionid, kind: 'REUPLOAD-FAILED-LOGS' } });
            fs.rmdirSync(dirname);
          } catch (error) {
            logger.warn(`REUPLOAD ERROR - CANNOT DELETE ${dirname}: ${error.code}`, { log: { uuid: sessionid, kind: 'REUPLOAD-FAILED-LOGS' } });
          }
        }
      });

      uploadTimeout = setTimeout(() => {
        logger.error(`REUPLOAD ERROR - Aborting file due to timeout ${sessionid}`, { log: { uuid: sessionid, kind: 'REUPLOAD-FAILED-LOGS' } });
        helper.PingZombie({
          kind: 'kafka-logs-reupload-failure-timeout',
          error: 'Upload time exceded',
          data: topic,
          region: constants.region,
          machine: constants.osHostName.replace('services-auto-', ''),
        });
        managedUpload.abort();
      }, kafkaConfig.FILE_UPLOAD_CUTOFF_MS || 300 * 1000);
    }
  }
};

try {
  setInterval(reUploadLogs, kafkaConfig.FAILED_LOGS_CHECK_INTERVAL, failedDirBasename);
} catch (err) {
  logger.error(`Error in reUploadLogs: ${err}`);
}

/*  Method to do upload and other ops */
const handleStop = async (options) => {
  const kafkaMessage = options.kafkaMessage;
  const fdCache = options.fdCache;
  const hoothootStats = options.hoothootStats;
  // for next refactoring round, move above to singleton

  const filename = kafkaHelper.getPathForMessage(kafkaMessage);
  const fileSuffix = kafkaHelper.generateFileSuffixForKafkaTopic()[topic];
  const sessionId = kafkaMessage.value.rails_session_id;

  const stopFd = kafkaHelper.getWriteFdForMessage(kafkaMessage, fdCache);
  const logsDirPath = kafkaHelper.getLogsDirPath(sessionId);
  const failedLogsDirPath = kafkaHelper.getFailedLogsDirPath(sessionId);
  const isAppAutomate = kafkaMessage.value.appTesting;
  const awsStorageClass = kafkaMessage.value.logs_aws_storage_class;
  const enableHubLogsZipping = kafkaMessage.value.enableHubLogsZipping;
  const enableNewAWSUserForUpload = kafkaMessage.value.enableNewAWSUserForUpload;

  await kafkaHelper.fsWriteFileAsync(stopFd, kafkaMessage.value.message);
  fdCache.del(filename);
  fs.closeSync(stopFd);

  if (topic === kafkaConfig.console_logs_topic) {
    try {
      await sanitizeConsoleLogs(filename, sessionId);
    } catch (error) {
      logger.info(`ERROR while sanitising Console Logs: ${error}`);
    }
  }

  if (fs.existsSync(filename)) {
    let stats = null;
    try {
      stats = await util.promisify(fs.stat)(filename); // fs.statSync is blocking
    } catch (err) {
      logger.error(`FS stat error ${err.toString()}`);
      return;
    }
    try {
      await uploadToS3Async(filename, stats, sessionId, fileSuffix, isAppAutomate, hoothootStats, awsStorageClass, enableHubLogsZipping, enableNewAWSUserForUpload);
      fsExtra.remove(logsDirPath);
    } catch (err) {
      logger.error(`Upload Error ${err.toString()}`);
      saveErrorUploadLogs(logsDirPath, failedLogsDirPath);
      helper.PingZombie({
        kind: 'kafka-logs-upload-failure',
        error: err ? err.toString().substring(0, 200) : 'Upload Error',
        data: topic,
        region: constants.region,
        machine: constants.osHostName.replace('services-auto-', ''),
      });
    }
  }
};

// Threadpool for S3 uploads, balanced and faster compared to batches / bursts
const kafkaS3Pipeline = async.queue(handleStop, kafkaConfig.S3_UPLOAD_MAX_THRESHOLD[originalTopicName(topic)]);

module.exports = {
  kafkaS3Pipeline,
  sendDataToEds,
};
