'use strict';

const fs = require('fs');
const fsExtra = require('fs-extra');
const async = require('async');

const constants = require('../../constants');
const kafkaHelper = require('./kafkaHelper');
const { originalTopicName } = require('../../transformers/basic');
const lineReader = require('line-reader');

const logger = require('./kafkaLogger').logger;

const kafkaConfig = constants.kafkaConfig;
const { isHash } = require('../../typeSanity');

const topic = process.env.CONSUMER_TOPIC;
const sendToEds = require('./kafkaS3pipeline').sendDataToEds;
const { inspect, promisify } = require('util');
const { Events } = require('browserstack-dwh');
const helper = require('../../helper');
const jsonHelper = require('../../helpers/jsonHelper');

const fsStatAsync = promisify(fs.stat);

// read from file
// <hub_name>:<HTTP_method:command_type>:<duration_in_ms_i_th>
// <hub_name>:<HTTP_method:command_type>:<duration_in_ms_i_th>:<status_code>:<ai_retry_count>:<tcg_duration> -> in case of AI
const getPerformanceStatsJson = (filePath, isAppAutomate) => new Promise((resolve, reject) => {
  const performanceStatsHash = {};
  const readStream = fs.createReadStream(filePath);
  lineReader.eachLine(
    readStream, (line) => {
      if (line) {
        const stringArr = line.split(':');
        if (stringArr[0] && stringArr[1] && stringArr[3] && stringArr[4]) {
          const [hubName, httpMethod, command = '', duration, status] = stringArr;
          performanceStatsHash[hubName] = performanceStatsHash[hubName] || {};
          const methodCommand = `${httpMethod}:${command}`;
          if (isAppAutomate) {
            const meta = stringArr[5];
            performanceStatsHash[hubName][methodCommand] = performanceStatsHash[hubName][methodCommand] || { d: [], s: [], m: [] };
            performanceStatsHash[hubName][methodCommand].d.push(duration);
            performanceStatsHash[hubName][methodCommand].s.push(status);
            performanceStatsHash[hubName][methodCommand].m.push(meta);
          } else if (methodCommand.endsWith('_ai')) {
            const aiRetryCount = stringArr[5] || 0;
            performanceStatsHash[hubName][methodCommand] = performanceStatsHash[hubName][methodCommand] || {
              d: [], s: [], ar: []
            };
            performanceStatsHash[hubName][methodCommand].d.push(duration);
            performanceStatsHash[hubName][methodCommand].s.push(status);
            performanceStatsHash[hubName][methodCommand].ar.push(aiRetryCount);
          } else {
            performanceStatsHash[hubName][methodCommand] = performanceStatsHash[hubName][methodCommand] || { d: [], s: [] };
            performanceStatsHash[hubName][methodCommand].d.push(duration);
            performanceStatsHash[hubName][methodCommand].s.push(status);
          }
        }
      }
    },
    (err) => {
      if (err) return reject(err);
      return resolve(performanceStatsHash);
    }
  );
});

const handlePerformanceStats = async (options) => {
  const { kafkaMessage, fdCache, hoothootStats } = options;

  const filename = kafkaHelper.getPathForMessage(kafkaMessage);
  const stopFd = kafkaHelper.getWriteFdForMessage(kafkaMessage, fdCache);
  fdCache.del(filename);
  fs.close(stopFd);
  const sessionId = kafkaMessage.value.rails_session_id;
  logger.info(`handlePerformanceStats - ${sessionId}`);
  const logsDirPath = kafkaHelper.getLogsDirPath(sessionId);
  const isAppAutomate = kafkaMessage.value.appTesting;
  let statsError;
  let stats;
  try {
    stats = await fsStatAsync(filename);
    const performanceStatsJson = await getPerformanceStatsJson(filename, isAppAutomate);
    if (isHash(performanceStatsJson) && performanceStatsJson !== {}) {
      Object.keys(performanceStatsJson).forEach((key) => {
        const val = performanceStatsJson[key];
        // push to eds
        if (val) {
          logger.info(`${sessionId} - pushing ${inspect(val)} to eds`);
          const kind = isAppAutomate ? Events.APP_AUTOMATE_PERFORMANCE_DATA : Events.AUTOMATE_PERFORMANCE_DATA;
          const stringifiedJSON = JSON.stringify(val);
          const data = {
            hashed_id: sessionId,
            hub_name: key,
            kind
          };

          if (stringifiedJSON.length > constants.JSON_PACKET_THRESHOLD) {
            const jsonArray = jsonHelper.splitJSON(val, constants.JSON_PACKET_THRESHOLD, isAppAutomate);
            Object.values(jsonArray).forEach((json) => {
              if (isAppAutomate) {
                data.command_data = JSON.stringify(json);
              } else {
                data.command_duration = JSON.stringify(json);
              }
              data.kind = kind;
              helper.sendToEDS(data);
            });
          } else {
            if (isAppAutomate) {
              data.command_data = stringifiedJSON;
            } else {
              data.command_duration = stringifiedJSON;
            }
            data.kind = kind;
            helper.sendToEDS(data);
          }
        }
      });
      let logType = constants.defaultFileType;
      const logTypeMatch = filename.match(/-([a-z-]+)((?:-v2)?).txt$/);
      if (logTypeMatch && logTypeMatch[1]) {
        logType = logTypeMatch[1];
      }
      hoothootStats.uploadSuccessfull[logType] = hoothootStats.uploadSuccessfull[logType] || 0;
      hoothootStats.uploadSuccessfull[logType] += 1;
    }
  } catch (err) {
    statsError = err;
    hoothootStats.uploadError += 1;
    logger.error(`handlePerformanceStats - ${sessionId} - File Not Found`);
  } finally {
    sendToEds(sessionId, isAppAutomate, true, stats ? stats.size : 0, statsError);
    if (stats) {
      fsExtra.remove(logsDirPath);
    }
  }
};

// Threadpool for eds push, balanced and faster compared to batches / bursts
const edsPipeline = async.queue(handlePerformanceStats, kafkaConfig.S3_UPLOAD_MAX_THRESHOLD[originalTopicName(topic)]);

module.exports = {
  edsPipeline,
};
