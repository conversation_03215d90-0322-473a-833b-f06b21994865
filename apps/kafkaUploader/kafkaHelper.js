'use strict';

const { promisify } = require('util');
const fs = require('fs');
const path = require('path');

const constants = require('../../constants');
const { isUndefined } = require('../../typeSanity');

const fsMkdirAsync = promisify(fs.mkdir);
const fsWriteFileAsync = promisify(fs.writeFile);

const kafkaConfig = constants.kafkaConfig;
const LOG_DATA_MESSAGE_TYPE = kafkaConfig.LOG_DATA_MESSAGE_TYPE;
const topic = process.env.CONSUMER_TOPIC;
const commonKafkaTopicRegion = process.env.COMMON_KAFKA_TOPIC_REGION;

const generateFileSuffixForKafkaTopic = () => {
  const updatedKafkaTopicMapsV2 = {};
  const kafkaTopicMaps = constants.fileSuffixForTopicV2;
  const kafkaTopicPrefix = constants.nonPrefixKafkaEnvironments.includes(constants.env.toLowerCase()) ? '' : `${constants.env}_`.toLowerCase();
  const topicPrefix = isUndefined(commonKafkaTopicRegion) ? kafkaTopicPrefix : `${commonKafkaTopicRegion.toLowerCase()}_`;
  Object.keys(kafkaTopicMaps).forEach((key) => {
    const updatedKey = topicPrefix + key;
    updatedKafkaTopicMapsV2[updatedKey] = kafkaTopicMaps[key];
  });
  return updatedKafkaTopicMapsV2;
};


const getLogsDirPath = (sessionid) => {
  const dir = path.resolve(kafkaConfig.savingLogsDirPath);
  return path.join(dir, `${sessionid}_${topic}`);
};

/*  Returns which file is to be handled for this message */
const getPathForMessage = (message) => {
  const dirPath = getLogsDirPath(message.value.rails_session_id);
  const prefix = path.join(dirPath, message.value.rails_session_id);
  switch (message.value.type) {
    case LOG_DATA_MESSAGE_TYPE.START:
    case LOG_DATA_MESSAGE_TYPE.ATOMIC_MESSAGE:
    case LOG_DATA_MESSAGE_TYPE.STOP:
      return `${prefix}${generateFileSuffixForKafkaTopic()[topic]}`;
    case LOG_DATA_MESSAGE_TYPE.CHUNK_START:
    case LOG_DATA_MESSAGE_TYPE.CHUNKED_MESSAGE:
    case LOG_DATA_MESSAGE_TYPE.CHUNK_END:
      return `${prefix}-${message.value.requestId}`;
    default:
      return undefined;
  }
};


const getFailedLogsDirPath = (sessionid = '') => {
  const dir = path.resolve(kafkaConfig.failedLogsDirPath);
  return path.join(dir, sessionid);
};

const getWriteFdForPath = (dirPath, filePath, fdCache) => {
  const cachedFd = fdCache.get(filePath);
  if (cachedFd) {
    fdCache.ttl(filePath, 300); // Keep alive
    return cachedFd;
  }
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath);
  }
  const fd = fs.openSync(filePath, 'a');
  fdCache.set(filePath, fd, 300);
  return fd;
};

/*  Handles fd based on the message based on if it is chunked or not  */
const getWriteFdForMessage = (message, fdCache) => {
  const dirPath = getLogsDirPath(message.value.rails_session_id);
  const filePath = getPathForMessage(message);
  return getWriteFdForPath(dirPath, filePath, fdCache);
};

module.exports = {
  generateFileSuffixForKafkaTopic,
  getPathForMessage,
  getLogsDirPath,
  getFailedLogsDirPath,
  getWriteFdForMessage,
  getWriteFdForPath,
  fsMkdirAsync,
  fsWriteFileAsync,
};
