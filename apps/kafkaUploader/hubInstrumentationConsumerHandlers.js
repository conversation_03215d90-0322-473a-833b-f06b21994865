const fs = require('fs');
const readline = require('readline');
const async = require('async');
const fsExtra = require('fs-extra');

let constants = require('../../constants');
const kafkaConfig = constants.kafkaConfig;

const { originalTopicName } = require('../../transformers/basic');
const helper = require('../../helper');
const instrumentation = require('../../helpers/instrumentation');
const kafkaHelper = require('./kafkaHelper');
const logger = require('./kafkaLogger').logger;
const topic = process.env.CONSUMER_TOPIC;
const LL = constants.LOG_LEVEL;

/* Start Message Handlers */
const handleStartRequest = (rails_session_id, messageObj) => {
  const uniqueId = messageObj.uniqueId;
  let initKeyObject = messageObj.initKeyObject;
  constants.global_registry[rails_session_id] = {};
  Object.keys(initKeyObject).forEach((key)=>{
    if(constants.hilSyncKeys.includes(key.toString())){
      constants.global_registry[rails_session_id][key] = {};
      constants.global_registry[rails_session_id][key][uniqueId] = initKeyObject[key];
    } else if(['lastRequestTime','lastResponseTime'].includes(key.toString())) {
      constants.global_registry[rails_session_id][key] = [initKeyObject[key]];
    } else {
      constants.global_registry[rails_session_id][key] = initKeyObject[key];
    }
  });
  constants.global_registry[rails_session_id]['sleepTimeNow'] = [];
  constants.global_registry[rails_session_id]['customExecutorInstrumentation'] = {};
}

/* Command Message Handlers */
const handleOutsideBSTime = (rails_session_id, lastKeyVal, lastFieldVal) => {
  if(!constants.global_registry[rails_session_id][lastKeyVal].includes(lastFieldVal)) constants.global_registry[rails_session_id][lastKeyVal].push(lastFieldVal);
}

const syncKeyForDelete = (key, isDeleteRequest, isTimeout) => {
  if(!isDeleteRequest) {
    return true;
  } else {
    if(isTimeout) {
      return false;
    } else {
      return ['insideHubTime','nonZeroStatusesCount','nonZeroIncrementCounters'].includes(key.toString());
    }
  }
}

const handleCommand = async (rails_session_id, messageObj, isDeleteRequest, isTimeout = null, hoothootStats) => {
  const keyObject = constants.global_registry[rails_session_id];
  if(typeof keyObject == 'undefined') throw `[COMMAND PARSE ERROR] : ${rails_session_id} : Key Object not found for atomic log`;

  try {
      const uniqueId = messageObj.uniqueId;
      let keyObjectChanged = messageObj.data;
      Object.keys(keyObjectChanged).forEach((key) => {
        if(constants.hilSyncKeys.includes(key.toString())) {
          if(syncKeyForDelete(key, isDeleteRequest, isTimeout)) {
            constants.global_registry[rails_session_id][key][uniqueId] = (key.toString() == 'request_count') ? keyObjectChanged[key] - 2 : keyObjectChanged[key];
          }
        } else if(constants.hilCalcKeys.includes(key.toString())) {
            if(['sleepTime','numSleep','sleepTimeNow'].includes(key.toString())) {
              if(!isDeleteRequest && key.toString() === 'sleepTimeNow') constants.global_registry[rails_session_id]['sleepTimeNow'].push(keyObjectChanged['sleepTimeNow']);
            } else if(key.toString() === 'updateOutsideBrowserstackTime') {
                handleOutsideBSTime(rails_session_id,'lastResponseTime',keyObjectChanged['updateOutsideBrowserstackTime']['lastResponseTime']);
                handleOutsideBSTime(rails_session_id,'lastRequestTime',keyObjectChanged['updateOutsideBrowserstackTime']['lastRequestTime']);
            } else if (['lastRequestTime','lastResponseTime'].includes(key.toString())) {
                handleOutsideBSTime(rails_session_id,key,keyObjectChanged[key]);
            }
        } else {
            if(['safariPrivoxyTimeout','exceptionEncountered','exceptionClass','exceptionRequest','exceptionMessage'].includes(key.toString())) {
              constants.global_registry[rails_session_id][key] = keyObjectChanged[key] || constants.global_registry[rails_session_id][key];
            } else {
              constants.global_registry[rails_session_id][key] = keyObjectChanged[key];
            }
        }
      });
  } catch(err) {
    hoothootStats.uploadError += 1;
    logger.error(`[EXCEPTION] : ${err}`);
  }
}

/* Stop Message Handlers */
const setOutsideBSTime = (rails_session_id, requestsArr, responseArr) => {
  if(requestsArr.length !== responseArr.length) logger.error(`[EXCEPTION] : [STOP ERROR] : ${rails_session_id} : Request Response array lengths do not match`);

  const arrLenReq = requestsArr.length, arrLenRes = responseArr.length;
  const arrLenMin = Math.min(arrLenReq, arrLenRes);
  let totOutsideBSTime = 0, minUserHubLatency = null;
  for(let idx=0; idx<arrLenMin; idx++) {
    totOutsideBSTime += (requestsArr[idx] - responseArr[idx]);
    minUserHubLatency = minUserHubLatency ? Math.min(minUserHubLatency, requestsArr[idx] - responseArr[idx]) : (requestsArr[idx] - responseArr[idx]);
  }
  constants.global_registry[rails_session_id]['outsideBrowserstackTime'] = totOutsideBSTime;
  constants.global_registry[rails_session_id]['userHubLatency'] = minUserHubLatency;
}

const setNonZeroStatusesCount = (rails_session_id) => {
  const finalNonZeroStatusesCount = {};
  Object.keys(constants.global_registry[rails_session_id]['nonZeroStatusesCount']).forEach((uniqueId)=>{
    Object.keys(constants.global_registry[rails_session_id]['nonZeroStatusesCount'][uniqueId]).forEach((key)=>{
      finalNonZeroStatusesCount[key] = finalNonZeroStatusesCount[key] ? finalNonZeroStatusesCount[key] + constants.global_registry[rails_session_id]['nonZeroStatusesCount'][uniqueId][key] : constants.global_registry[rails_session_id]['nonZeroStatusesCount'][uniqueId][key];
    });
  });
  constants.global_registry[rails_session_id]['nonZeroStatusesCount'] = finalNonZeroStatusesCount;
}

const setNonZeroIncrementCounters = (rails_session_id) => {
  const finalNonZeroIncrementCounters = [];
  Object.keys(constants.global_registry[rails_session_id]['nonZeroIncrementCounters']).forEach((uniqueId)=>{
    for(val of constants.global_registry[rails_session_id]['nonZeroIncrementCounters'][uniqueId]){
      if(!finalNonZeroIncrementCounters.includes(val)) finalNonZeroIncrementCounters.push(val);
    }
  });
  constants.global_registry[rails_session_id]['nonZeroIncrementCounters'] = finalNonZeroIncrementCounters;
}
// General function for adding nested objects having value as integer
const addNestedObjects = (result, toAdd) => {
  Object.keys(toAdd).forEach((key) => {
    if(key in result){
      if (typeof toAdd[key] === 'object' && toAdd[key] !== null) {
        addNestedObjects(result[key], toAdd[key]);
      } else {
        result[key] = result[key] + toAdd[key];
      }
    }else{
      result[key] = toAdd[key];
    }
  });
}

const setCustomExecutorInstrumentation = (rails_session_id) => {
  const finalCustomExecutorInstrumentation = {};
  const key = "customExecutorInstrumentation";
  Object.keys(constants.global_registry[rails_session_id][key]).forEach((uniqueId)=>{
    const toAdd = constants.global_registry[rails_session_id][key][uniqueId];
    addNestedObjects(finalCustomExecutorInstrumentation, toAdd);
  });
  constants.global_registry[rails_session_id][key] = finalCustomExecutorInstrumentation;
}

const syncAcrossNodes = (rails_session_id, key, isTimeout, keyObjectChanged) => {
  if(key.toString() === 'nonZeroStatusesCount') {
    return setNonZeroStatusesCount(rails_session_id);
  } else if(key.toString() === 'nonZeroIncrementCounters') {
    return setNonZeroIncrementCounters(rails_session_id);
  } else if(key.toString() === 'customExecutorInstrumentation') {
    return setCustomExecutorInstrumentation(rails_session_id);
  }
  let totKeyVal = 0;
  Object.keys(constants.global_registry[rails_session_id][key]).forEach((uniqueId) => {
    totKeyVal += constants.global_registry[rails_session_id][key][uniqueId];
  });

  if (key === 'request_count') {
    constants.global_registry[rails_session_id][key] = (totKeyVal == 0) ? keyObjectChanged.request_count : totKeyVal + 2;
  } else if (key === 'seleniumRequestsCount' && !isTimeout) {
    constants.global_registry[rails_session_id][key] = totKeyVal - 1;
  } else {
    constants.global_registry[rails_session_id][key] = totKeyVal;
  }
}

const setSleepStats = (rails_session_id) => {
  try {
    const responseArr = constants.global_registry[rails_session_id]['lastResponseTime'];
    const lenResponses = responseArr.length;
    if(!lenResponses){
      throw `[SLEEP STATS ERROR] : ${rails_session_id} : [NO RESPONSES RECORDED]`;
    }

    const sleepArr = constants.global_registry[rails_session_id]['sleepTimeNow'];
    const lenSleep = sleepArr.length;
    if(!lenSleep){
      throw `[SLEEP STATS ERROR] : ${rails_session_id} : [NO SLEEP TIMESTAMPS RECORDED]`;
    }

    if(lenResponses < lenSleep + 1) {
      throw `[SLEEP STATS ERROR] : ${rails_session_id} : [SLEEP RESPONSES NUM MISMATCH]`;
    }

    let sleepGlobal = 0, numSleepGlobal = 0;
    for(let idx=0;idx<lenSleep;idx++) {
      let sleep = Math.floor((sleepArr[idx] - responseArr[idx+1])/1000);
      sleepGlobal += sleep;
      let numSleep = sleep ? 1 : 0;
      numSleepGlobal += numSleep;
    }
    constants.global_registry[rails_session_id]['sleepTime'] = sleepGlobal;
    constants.global_registry[rails_session_id]['numSleep'] = numSleepGlobal;
    delete constants.global_registry[rails_session_id]['sleepTimeNow'];
  } catch(err) {
    logger.error(`[EXCEPTION] : ${err}`);
  }
}

const cleanupForSession = (rails_session_id) => {
    const sessionData = constants.global_registry[rails_session_id];
    if('lastRequestTime' in sessionData) {
      const arrLenReq = sessionData['lastRequestTime'].length;
      sessionData['lastRequestTime'] = sessionData['lastRequestTime'][arrLenReq-1];
    }

    if('lastResponseTime' in sessionData) {
      const arrLenRes = sessionData['lastResponseTime'].length;
      sessionData['lastResponseTime'] = sessionData['lastResponseTime'][arrLenRes-1];
    }
}

const pushInstrumentation = (rails_session_id) => {
  /* Service sends data to zombies only if instrumentationMechanismFlag = 0 */
  const instrumentationMechanismFlag = constants.instrumentationMechanismFlag;
  /* removeFromGlobalRegistry */
  const sessionKeyObj = constants.global_registry[rails_session_id];
  const {
    hubTime = 0,
    request_count: requestCount = 0,
    appTesting = false,
    seleniumRequestsCount = 0,
    toggle,
    outsideBrowserstackTime = 0,
    socketMessages = 0,
    dialect = "OSS",
    lastOpenedUrl = "",
    firstOpenedUrl = "",
    terminal_type = "",
    sleepTime = 0,
    numSleep = 0,
    percyBeginTime = -1,
    percyNumberOfTiles = 0,
    userHubLatency = 0,
    hubHostName = null,
    server_port = null,
  } = sessionKeyObj;
  let { insideHubTime = 0, hubProcessingTime = 0, userToNginxTime = 0, nginxToHubTime = 0, jarTime = 0, automate_ai_duration = 0, automate_ai_success = 0, automate_ai_retry_count = 0, automate_ai_find_element_count = 0, automate_tcg_duration = 0} = sessionKeyObj;
  const isRegionToggle = (toggle ? true : false);
  let tertiaryParams = { is_region_toggle: isRegionToggle, outside_bs_time_new: outsideBrowserstackTime, server_port: server_port, dialect };
  const sessionid = rails_session_id;
  const terminalType = terminal_type;
  const kind = (appTesting ? 'app_' : '') + 'automation_session_stats';
  tertiaryParams.hub_hostname = hubHostName;

  const dataForZombie = {
    session_id: sessionid,
    kind,
    bs_latency_mean: hubTime / 1000.0,
    inside_bs_network_time: appTesting ? null : insideHubTime,
    number_of_requests_nginx:  requestCount,
    customer_session_duration: (hubTime - insideHubTime) / 1000.0,
    last_opened_url: sessionKeyObj.georestricted_region ? null : helper.getSanitizedStringForZombie(lastOpenedUrl),
    first_opened_url: sessionKeyObj.georestricted_region ? null : helper.getSanitizedStringForZombie(firstOpenedUrl),
    selenium_requests_count: appTesting ? null : seleniumRequestsCount,
    tertiary_params: tertiaryParams,
    outside_bs_time: outsideBrowserstackTime,
    percy_begin_time: percyBeginTime,
    percy_number_of_tiles: percyNumberOfTiles
  };
  if(instrumentationMechanismFlag === 0) {
    helper.PingZombie(dataForZombie);
  } else if(instrumentationMechanismFlag === 1) {
    instrumentation.pushFeatureUsage(rails_session_id, { flagOneData: { removeFromGlobalRegistry: { zombieData: dataForZombie } } }, () => {});
  }

  let featureUsageObj = {
    sentFrom: 'I',
    latency: {
      minUserToHubLatency: userHubLatency
    }
  };

  if (socketMessages > 0) {
    featureUsageObj.ws = { messageCount: socketMessages };
  }
  
  if (appTesting || terminalType === 'desktop') {
    featureUsageObj.hubProcessing = {};
    if(hubProcessingTime){
      featureUsageObj.hubProcessing.hubTime = hubProcessingTime;
    }
    if(nginxToHubTime){
      featureUsageObj.hubProcessing.nginxToHubTime = nginxToHubTime;
    }
    if(userToNginxTime){
      featureUsageObj.hubProcessing.userToNginxTime = userToNginxTime;
    }
    if(jarTime){
      featureUsageObj.hubProcessing.jarTime = jarTime;
    }
    if(automate_ai_duration){
      featureUsageObj.automate_ai_duration = automate_ai_duration;
    }
    if(automate_ai_success){
      featureUsageObj.automate_ai_success = automate_ai_success;
    }
    if(automate_ai_retry_count){
      featureUsageObj.automate_ai_retry_count = automate_ai_retry_count;
    }
    if(automate_ai_find_element_count){
      featureUsageObj.automate_ai_find_element_count = automate_ai_find_element_count;
    }
    if(automate_tcg_duration){
      featureUsageObj.automate_tcg_duration = automate_tcg_duration;
    }
  }

  if(instrumentationMechanismFlag === 0) {
    instrumentation.pushFeatureUsage(sessionid, featureUsageObj, () => {});
  } else if(instrumentationMechanismFlag === 1) {
    instrumentation.pushFeatureUsage(rails_session_id, { flagOneData: { removeFromGlobalRegistry: { featureUsageData: featureUsageObj } } }, () => {});
  }

  if (appTesting) {
    const product = { performance: {} };
    product.performance.total_appium_requests_count = seleniumRequestsCount;
    product.performance.total_inside_bs_time = insideHubTime / 1000.0;
    product.performance.total_outside_bs_time = outsideBrowserstackTime;
    if(instrumentationMechanismFlag === 0) {
      helper.PingZombie({ sessionid, kind, product });
    } else if(instrumentationMechanismFlag === 1) {
      instrumentation.pushFeatureUsage(rails_session_id, { flagOneData: { removeFromGlobalRegistry: { zombieDataAA: product } } }, () => {});
    }
  }

  const customExecutorInstrumentation = sessionKeyObj.customExecutorInstrumentation;
  if (customExecutorInstrumentation && Object.keys(customExecutorInstrumentation).length > 0) {
    instrumentation.pushFeatureUsage(sessionid, customExecutorInstrumentation, () => {});
  }

  /* pingDataToStats */
  helper.pingDataToStats(constants.global_registry[rails_session_id]);
}

const handleStopRequest = (rails_session_id, messageObj, isTimeout, hoothootStats) => {
  logger.debug(`[${rails_session_id}] handleStopRequest STARTED`);
  if(constants.global_registry[rails_session_id]) {
    handleCommand(rails_session_id, messageObj, true, isTimeout, hoothootStats);
    Object.keys(constants.global_registry[rails_session_id]).forEach((key) => {
      if(constants.hilSyncKeys.includes(key)) {
        syncAcrossNodes(rails_session_id,key, isTimeout, messageObj.data);
      } else if(key.toString() === 'sleepTimeNow') {
        setSleepStats(rails_session_id);
      } else if(key.toString() === 'outsideBrowserstackTime') {
        setOutsideBSTime(rails_session_id,constants.global_registry[rails_session_id]['lastRequestTime'],constants.global_registry[rails_session_id]['lastResponseTime']);
      }
    });

    cleanupForSession(rails_session_id);
    pushInstrumentation(rails_session_id);
    hoothootStats.uploadSuccessfull['session completion'] = hoothootStats.uploadSuccessfull['session completion'] || 0;
    hoothootStats.uploadSuccessfull['session completion'] += 1;
  } else {
    throw `[STOP ERROR] : ${rails_session_id} : No keyObject found for STOP request`;
  }
  logger.debug(`[${rails_session_id}] handleStopRequest COMPLETED`);
}

/* Handle Session-Stop reading of file */
const sessionStopHandler = async ({ kafkaMessage, fdCache, hoothootStats }) => {
  const rails_session_id = kafkaMessage.value.rails_session_id;
  logger.debug(`[${rails_session_id}] sessionStopHandler STARTED`);

  const filename = kafkaHelper.getPathForMessage(kafkaMessage);
  const stopFd = kafkaHelper.getWriteFdForMessage(kafkaMessage, fdCache);
  fdCache.del(filename);
  fs.close(stopFd);

  if(!fs.existsSync(filename)) {
    logger.error(`[EXCEPTION] [${rails_session_id}] : ENOENT file/dir not exists`);
    return;
  }

  const logStream = fs.createReadStream(`${filename}`);
  const rl = readline.createInterface({
    input: logStream,
    crlfDelay: Infinity
  });
  // Note: we use the crlfDelay option to recognize all instances of CR LF
  // ('\r\n') in input.txt as a single line break.

  try {
    for await (const line of rl) {
      const messageObj = JSON.parse(line);
      helper.convertPubSubKafkaCodesToKeys(messageObj);

      if(messageObj.initKeyObject){
        handleStartRequest(rails_session_id,messageObj);
      } else if(messageObj.data){
        if(messageObj.isTimeout === true || messageObj.isTimeout === false){
          handleStopRequest(rails_session_id, messageObj, messageObj.isTimeout, hoothootStats);
          break;
        } else {
          handleCommand(rails_session_id,messageObj,false,null,hoothootStats);
        }
      } else {
        logger.error(`[GLOBAL ERROR] : ${rails_session_id} : Log type not handled`);
      }
    }
  } catch(err) {
    hoothootStats.uploadError += 1;
    logger.error(`[EXCEPTION] [${rails_session_id}] : ${err}`);
  } finally {
    /* Remove from memory */
    delete constants.global_registry[rails_session_id];
    rl.close();
    logStream.destroy();
    fsExtra.remove(kafkaHelper.getLogsDirPath(rails_session_id));
  }
  logger.debug(`[${rails_session_id}] sessionStopHandler COMPLETED`);
}

const hilPipeline = async.queue(sessionStopHandler, kafkaConfig.S3_UPLOAD_MAX_THRESHOLD[originalTopicName(topic)]);
exports.hilPipeline = hilPipeline;
