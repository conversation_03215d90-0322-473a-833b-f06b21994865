'use strict';

const childProcess = require('child_process');
const util = require('util');
const helper = require('../../helper');
const constants = require('../../constants');

const diskCheckCommand = 'df -P /ebs/kafka/saving | grep /ebs | awk \'{ print $5 }\' | sed \'s/%//g\' | tr -d \'\n\'';
// const diskCheckCommand = 'df -P /Users/<USER>/Downloads | grep /System | awk \'{ print $5 }\' | sed \'s/%//g\' | tr -d \'\n\'';

exports.safeSendAlerts = async (subject, message, alertReceivers) => {
  try {
    await helper.sendAlerts(subject, message, alertReceivers);
  // eslint-disable-next-line no-empty
  } catch (_e) {}
};

exports.checkDiskUsage = async (logTag, hostname = '') => {
  const execPromisified = util.promisify(childProcess.exec);
  // eslint-disable-next-line radix
  const diskUsagePercent = parseInt(((await execPromisified(diskCheckCommand, { timeout: constants.DISK_CHECK_TIMEOUT_MS })).stdout || '').toString());
  if (Number.isInteger(diskUsagePercent)) {
    return {
      capableToBecome: diskUsagePercent < constants.UPLOADER_DISK_USAGE_THRESHOLD,
      message: `Disk usage: ${diskUsagePercent}, threshold: ${constants.UPLOADER_DISK_USAGE_THRESHOLD}`
    };
  }
  const message = 'Calculated disk usage is not a number';
  exports.safeSendAlerts(`${hostname} ${logTag} BecomeMasterCheck Exception: Disk check command error`, message);
  return {
    capableToBecome: true,
    message
  };
};

exports.capableToStartUploader = async (hostname, logTag) => {
  try {
    const checkResult = await exports.checkDiskUsage(logTag, hostname);
    if (checkResult.capableToBecome) {
      return true;
    }
    exports.safeSendAlerts(`${hostname} ${logTag} BecomeMasterCheck : Cannot become master`, checkResult.message || 'System metrics check failed');
    return false;
  } catch (error) {
    let alertMessage = `An exception occurred in checking system metrics \n\nHost: ${hostname}`;
    const errorMessage = error.toString();
    if (error && error.stack) {
      alertMessage = `Message: ${errorMessage}\n\nHost: ${hostname}\n\nStack: ${error.stack.toString()}`;
    }
    exports.safeSendAlerts(`${hostname} ${logTag} BecomeMasterCheck EXCEPTION: ${errorMessage}`, alertMessage);
    return true;
  }
};
