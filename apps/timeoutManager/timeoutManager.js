'use strict';

const pubSub = require('../../pubSub');
const ha = require('../../ha');
const constants = require('../../constants');
const requestlib = require('../../lib/request');
const helper = require('../../helper');
const dnsCache = require('../../helpers/dnscache');

class IdleTimeoutManager {
  constructor(options) {
    dnsCache({
      enable: true,
      ttl: 30,
      cachesize: 1000,
    });

    this.winston = options.winston;
    this.redisClient = options.redisClient;
    this.backupRedisList = options.backupRedisList;
    this.consumerId = options.consumerId;

    this.trackHootHootStats = {
      lastMinClearTimeouts: 0,
      lastMinUpdateTimeouts: 0,
      lastMinIdleTimeouts: 0,
      lastMinIdleTimeoutRetries: 0,
      lastMinIdleTimeoutRetryFailures: 0,
      // This happens when we get 2 delete events without an update event
      // Need to fix producer code
      lastMinClearTimeoutErrors: 0,
      lastMinMaxDelay: 0,
      lastMinMaxLoopTime: 0,
    };

    setInterval(
      this.pushHootHootStats.bind(this),
      constants.timeoutManager.pushToHootHootInterval
    );
  }

  // Check the delay between the event being pushed and processed
  checkDelay(message) {
    if (message.time === undefined) {
      return '';
    }
    const delay = Date.now() - message.time;
    if (delay > this.trackHootHootStats.lastMinMaxDelay) {
      this.trackHootHootStats.lastMinMaxDelay = delay;
    }
    return `delay: ${delay} msecs`;
  }


  idleTimeout(message) {
    return () => {
      this.trackHootHootStats.lastMinIdleTimeouts += 1;
      this.clearStaleSession(message);
    };
  }

  clearStaleSession(message) {
    // const that = this;
    const key = message.sessionId;
    const winstonParams = {
      tag: 'clearStaleSession',
      sessionId: key,
    };

    this.winston.info('Session Timedout', winstonParams);
    requestlib.call({
      hostname: constants.timeoutManager.hubServer,
      port: constants.timeoutManager.hubPort,
      path: `/session_timedout?sessionId=${key}`,
    }).then(() => {
      pubSub.publish(constants.sessionTimeout, key, null, true);
    }).catch((e) => {
      this.winston.error('Exception in session_timedout request', {
        tag: 'clearStaleSession',
        sessionId: key,
        dumpObjects: {
          error: e,
        },
      });
      ha.getData(key, (haError, keyObject) => {
        if (!haError && keyObject) {
          this.trackHootHootStats.lastMinIdleTimeoutRetries += 1;
          this.winston.info('Retrying again', winstonParams);
          setTimeout(
            this.idleTimeout(message).bind(this),
            constants.timeoutManager.timeoutFailedRetryDelay
          );
        } else {
          this.trackHootHootStats.lastMinIdleTimeoutRetryFailures += 1;
          this.winston.error('Cannot retry', winstonParams);
        }
      });
    });
  }

  triggerTimeouts(sessionsWithScores) {
    if (sessionsWithScores.length > 0) {
      this.winston.info(`Sessions Timedout: ${sessionsWithScores.length / 2}`);
    }
    for (let i = 0; i < sessionsWithScores.length; i += 2) {
      const sessionId = sessionsWithScores[i];
      const expectedTimeout = sessionsWithScores[i + 1];
      this.winston.info('Timeout ', this.checkDelay({ time: expectedTimeout }), sessionId);
      this.idleTimeout({ sessionId })();
    }
  }

  pushHootHootStats() {
    const hootHootKeysMap = {};
    constants.timeoutManager.hoothootPerMinKeys.forEach((hoothootKey) => {
      hootHootKeysMap[hoothootKey] = this.trackHootHootStats[hoothootKey];
    });

    this.winston.debug('Pushing stats to hoothoot', {
      tag: 'pushStatsToHootHoot',
      dumpObjects: hootHootKeysMap,
    });
    helper.pushStatsToHootHoot(constants.timeoutManager.statsKey, hootHootKeysMap, this.consumerId);

    // reset keys
    constants.timeoutManager.hoothootPerMinKeys.forEach((hoothootKey) => {
      this.trackHootHootStats[hoothootKey] = 0;
    });
  }
}

module.exports = IdleTimeoutManager;
