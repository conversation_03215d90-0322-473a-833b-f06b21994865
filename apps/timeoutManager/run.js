'use strict';

const helper = require('../../helper');
const constants = require('../../constants');
const HubLogger = require('../../log');
const TimeoutsListener = require('./timeoutsListener');
const dnsCache = require('../../helpers/dnscache');
const { Chitragupta } = require('chitragupta');

dnsCache({
  enable: true,
  ttl: 30,
  cachesize: 1000,
});

const LL = constants.LOG_LEVEL;

let lastSent = 0;
const timeoutsListener = new TimeoutsListener();

process.on('uncaughtException', (err) => {
  const stackTrace = err.stack;

  HubLogger.miscLogger('TimeoutManager', `Global Exception in timeout process: ${stackTrace}`, LL.ERROR);

  let exceptionGenre = 'default';
  if (err && err.toString() && err.toString().length > 0) {
    exceptionGenre =
      helper.getGlobalExceptionGenre(err.toString().toLowerCase());
  }
  HubLogger.hoothoot.emit(
    constants.timeoutManager.globalExceptionHootHootKey,
    1,
    { genre: exceptionGenre }
  );

  if (Date.now() - lastSent > constants.exceptionAlertDelay) {
    const subject = `Timeout Manager Global Exception: ${stackTrace}`;
    const message = `GLOBAL EXCEPTION IN TIMEOUT PROCESS: ${stackTrace}`;
    helper.sendAlerts(subject, message);

    lastSent = Date.now();
  }
});

['SIGINT', 'SIGTERM'].forEach((signal) => {
  process.on(signal, () => {
    HubLogger.miscLogger('TimeoutManager', `Got exit event signal ${signal}. reloadFunction is set. Please wait.`, LL.ERROR);
    timeoutsListener.setReloadFunction();
  });
});

function timeoutListenerGoAhead() {
  timeoutsListener.getGoAhead(() => {
    // Will reach this point once service is master

    // Need to ack in case multiple services are running
    setInterval(() => {
      timeoutsListener.ackAlive.call(timeoutsListener);
    }, constants.timeoutManager.ackAlive);

    // Need to verify if service is master
    setInterval(() => {
      timeoutsListener.verifyMaster.call(timeoutsListener);
    }, constants.timeoutManager.verifyMaster);

    timeoutsListener.newRun();
  });
}

Chitragupta.setupProcessLogger('timeoutManager', timeoutListenerGoAhead);
