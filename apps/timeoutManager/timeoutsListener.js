'use strict';

const constants = require('../../constants');
const helper = require('../../helper');
const fs = require('fs');
const IdleTimeoutManager = require('./timeoutManager');
const winston = require('winston');
const path = require('path');
const os = require('os');
const { Chitragupta } = require('chitragupta');

class TimeoutsListener {
  constructor() {
    const env = process.argv[2];
    this.isProductionEnv = env === 'Production';

    this.consumerId = process.env.CONSUMER_ID;

    this.initializeLogger();
    this.initializeRedis();

    const restartFilePath = path.join(__dirname, '../../', 'tmp/restart-timeout.txt');

    if (typeof this.consumerId === 'undefined' || this.consumerId === '') {
      winston.error('Starting timeoutManager without consumerId');
      if (this.isProductionEnv) {
        winston.error('Refusing to start as env.CONSUMER_ID is undefined and this is a Production environment');
        process.exit(1);
      }
      this.consumerId = 'v3';
    }
    this.consumeList = helper.timeoutManagerGetProcessList(this.consumerId);
    const backupList = helper.timeoutManagerGetBackupList(this.consumerId);
    this.redisWatchKey = helper.timeoutManagerGetRedisWatchKey(this.consumerId);

    winston.info(
      `Initializing timeoutManager: isProductionEnv: ${this.isProductionEnv}`,
      `watching file for restart: ${restartFilePath}`,
      `Listening to queue ${this.consumeList}`
    );
    this.idleTimeoutManager = new IdleTimeoutManager({
      winston,
      redisClient: this.redisClientBlock,
      backupRedisList: backupList,
      consumerId: this.consumerId,
    });

    this.toReload = false;

    fs.watchFile(restartFilePath, () => {
      winston.warn('Timeout worker shutting down');
      this.setReloadFunction();
    });
  }

  initializeLogger() {
    const hostname = os.hostname();
    const filepath = `${constants.timeoutManager.logsPath}/timeoutManager_${this.consumerId}.log`;
    const logFormatter = (options) => {
      const meta = options.meta || {};
      const sessionId = meta.sessionId || 'global';
      const tag = `${meta.tag || 'timeoutManager'}: ${sessionId}`;
      const logPrefix = `${hostname}:${process.pid} ${winston.config.colorize(options.level, tag)}`;
      let dumpMessage = '';
      // Add any object / user input to dumpObjects - Used to remove newlines and stringify
      Object.keys(meta.dumpObjects || {}).forEach((objectKey) => {
        dumpMessage = `${dumpMessage} -- ${objectKey}: ${JSON.stringify(meta.dumpObjects[objectKey] || '').replace(/\n/, '')}`;
      });

      return `${new Date().toISOString()} ${logPrefix} ${options.message} ${dumpMessage}`;
    };

    winston.configure({
      rewriters: this.isProductionEnv ? [(level, msg, meta) => {
        meta.meta = constants.log_meta;
        meta.meta.file = filepath;
        return meta;
      }] : [],
      transports: this.isProductionEnv ? [
        new winston.transports.File({
          filename: filepath,
          level: 'info',
          colorize: false,
          json: false,
          formatter: Chitragupta.jsonLogFormatter,
        }),
      ] : [
        new winston.transports.Console({
          level: 'debug',
          colorize: true,
          formatter: logFormatter,
        }),
      ],
    });
  }

  initializeRedis() {
    this.redisClientBlock = helper.redisClientSecond;
    let luaScriptBecomeMasterContent = '';
    let luaScriptUnsetMasterContent = '';

    try {
      luaScriptBecomeMasterContent = fs.readFileSync(constants.timeoutManager.becomeMasterScriptPath, 'utf-8');
      luaScriptUnsetMasterContent = fs.readFileSync(constants.timeoutManager.unsetMasterScriptPath, 'utf-8');
    } catch (readScriptError) {
      winston.error('Error reading script for loading in redis.', {
        tag: 'initializeRedis',
        dumpObjects: {
          error: readScriptError.toString(),
        },
      });
      const subject = `TimeoutManager: Global Exception: initializeRedis: ${readScriptError.toString()}`;
      const message = `GLOBAL EXCEPTION IN TIMEOUT MANAGER PROCESS: ${readScriptError.stack ? readScriptError.stack.toString() : readScriptError.toString()}`;

      helper.sendAlerts(subject, message);
      process.exit(1);
    }


    this.redisClientBlock.defineCommand('becomeMaster', {
      numberOfKeys: 1,
      lua: luaScriptBecomeMasterContent,
    });
    this.redisClientBlock.defineCommand('unsetMaster', {
      numberOfKeys: 1,
      lua: luaScriptUnsetMasterContent,
    });
  }

  // This is used for HA. If multiple services with same CONSUMER_ID is running
  getGoAhead(callback) {
    const logParam = {
      tag: 'getGoAhead',
    };

    if (typeof this.toReload === 'function') {
      this.toReload.call(this);
      return;
    }
    this.redisClientBlock.becomeMaster(
      [
        this.redisWatchKey,
        new Date().getTime(),
        process.pid,
        constants.timeoutManager.ackLimitForMaster,
      ],
      (err, res) => {
        let shouldBecomeMaster = false;
        if (res && res.toString().match('I can become master')) {
          shouldBecomeMaster = true;
        }
        if (!shouldBecomeMaster) {
          winston.info('Got ack from master. Waiting.', logParam);
          setTimeout(() => {
            this.getGoAhead(callback);
          }, constants.timeoutManager.pingForMasterInterval);
        } else {
          winston.error('I am master now.', logParam);
          callback();
        }
        if (res && res !== undefined && res !== null) {
          winston.info('Got result', res.toString());
        }
      }
    );
  }

  ackAlive() {
    winston.silly('Started', {
      tag: 'ackAlive',
    });
    this.redisClientBlock.hset([this.redisWatchKey, 'timestamp', new Date().getTime()], (err) => {
      if (err) {
        winston.error('Error while writing ack to redis', {
          tag: 'ackAlive',
          dumpObjects: {
            err: err.toString(),
          },
        });
      }
    });
  }

  verifyMaster() {
    winston.silly('Started', {
      tag: 'verifyMaster',
    });
    this.redisClientBlock.hget([this.redisWatchKey, 'pid'], (err, res) => {
      if (err) {
        winston.error('Error while trying to verify master from redis', {
          tag: 'verifyMaster',
          dumpObjects: {
            err: err.toString(),
          },
        });
      }

      const logParams = {
        tag: 'verifyMaster',
      };

      if (res && res === process.pid.toString()) {
        winston.info('Verified master', logParams);
        return;
      }

      winston.error('Was not able to verify that this service is master; Exiting', logParams);
      this.setReloadFunction();
    });
  }

  setReloadFunction() {
    this.redisClientBlock.unsetMaster(this.redisWatchKey, process.pid, (err, res) => {
      if (err) {
        winston.error('Got Error while trying to unset master. Still proceeding', {
          tag: 'setReloadFunction',
          dumpObjects: {
            error: err.toString(),
          },
        });
      }
      if (res) {
        winston.info('Got response from unsetMaster script', res, {
          tag: 'setReloadFunction',
        });
      }
      this.toReload = () => {
        winston.warn('Timeout worker stopped');
        process.exit(0);
      };
    });
  }

  newRun() {
    if (typeof this.toReload === 'function') {
      this.toReload.call(this);
      return;
    }

    const maxScore = Date.now();
    this.loopStart = Date.now();

    this.redisClientBlock.multi()
      .zrangebyscore(this.consumeList, 0, maxScore, 'WITHSCORES')
      .zremrangebyscore(this.consumeList, 0, maxScore)
      .exec(this.handleRange.bind(this));
  }

  handleRange(err, data) {
    this.idleTimeoutManager.trackHootHootStats.lastMinMaxRedisRTT = Math.max(
      this.idleTimeoutManager.trackHootHootStats.lastMinMaxRedisRTT,
      Date.now() - this.loopStart
    );

    if (err) {
      winston.error(`Redis Error: ${err}`, err.trace, {
        tag: 'handleRange',
      });
    } else {
      const rangeOutput = data[0];
      const remRangeOutput = data[1];

      if (rangeOutput[0] || remRangeOutput[0]) {
        if (rangeOutput[0]) {
          winston.error(`Redis Error: ${rangeOutput[0]}`, rangeOutput[0].trace, {
            tag: 'handleRange',
          });
        }

        if (remRangeOutput[0]) {
          winston.error(`Redis Error: ${remRangeOutput[0]}`, remRangeOutput[0].trace, {
            tag: 'handleRange',
          });
        }
      } else if (rangeOutput[1].length !== remRangeOutput[1] * 2) {
        winston.error(
          'Timeout sessions mismatch',
          `rangeOutput: ${rangeOutput[1].length / 2}, remRangeOutput: ${remRangeOutput[1]}`,
          { tag: 'handleRange' }
        );
      } else {
        this.idleTimeoutManager.triggerTimeouts(rangeOutput[1]);
      }
    }

    this.idleTimeoutManager.trackHootHootStats.lastMinMaxLoopTime = Math.max(
      this.idleTimeoutManager.trackHootHootStats.lastMinMaxLoopTime,
      Date.now() - this.loopStart
    );

    setTimeout(this.newRun.bind(this), constants.timeoutManager.interval);
  }
}

module.exports = TimeoutsListener;
