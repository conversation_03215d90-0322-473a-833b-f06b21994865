'use strict';

const constants = require('../../constants');
const MonitoringHelper = require('./monitoringHelper');

let winston;

// Do not require helper.js here
// Make sure that no pending callbacks are present
// after triggering restart or else the process won't quit

// Resets all redis Keys in list redisKeysList to 0
// Returns previous values in the same position as in argument list
async function resetAndGetValue(redisClient, redisKeysList) {
  return Promise.all(redisKeysList
    .map(redisKey => redisClient.getset(redisKey, 0)));
}

/**
 * Retrieves the top `k` users of a ZSET in descending order by score and resets the ZSET.
 *
 * @param {object} redisClient - The Redis client instance for interacting with <PERSON>is.
 * @param {string} zSetKey - The key of the ZSET to retrieve and reset.
 * @param {number} k - The number of top users to retrieve from the ZSET.
 * @returns {Promise<Array<string>>} A promise that resolves to an array of members and their scores.
 *                                   Format: [user1, error1, user2, error2, ...].
 */
async function resetAndGetZSet(redisClient, zSetKey, k) {
  const zsetMembers = await redisClient.zrevrange(zSetKey.userErrorToken, 0, k - 1, 'WITHSCORES');
  await redisClient.del(zSetKey.userErrorToken);
  return zsetMembers;
}

async function deleteAndGetHashValue(redisClient, redisKeysList) {
  return Promise.all(redisKeysList
    .map(redisKey => redisClient.multi().hgetall(redisKey).del(redisKey).exec()));
}

function getSumOfArray(array) {
  return array.map(eachNumber => parseInt(eachNumber, 10) || 0)
    .reduce((currSum, currValue) => currSum + currValue);
}

function getDowntimeState(currentRedisValue) {
  const allStates = constants.monitoring.downtimeStates;
  const [currentState] = Object.entries(allStates).find(([, downtimeValue]) =>
    currentRedisValue === downtimeValue.redisValue) || [];
  return currentState || constants.monitoringDowntimeStates.runningState;
}

class MonitoringService {
  constructor() {
    this.monitoringHelper = new MonitoringHelper();
    this.isProductionEnv = this.monitoringHelper.isProductionEnv;
    this.currentDowntimeState = {
      automate: constants.monitoringDowntimeStates.runningState,
      appAutomate: constants.monitoringDowntimeStates.runningState,
    };

    winston = this.monitoringHelper.winston;
    this.redisClient = this.monitoringHelper.redisClient;

    winston.info(
      `Initializing monitoringService: isProductionEnv: ${this.isProductionEnv}`,
      `watching file for restart: ${this.monitoringHelper.restartFilePath}`
    );

    ['SIGINT', 'SIGTERM'].forEach((signal) => {
      process.on(signal, () => {
        winston.warn(`Got exit event signal ${signal}. restarting. Please wait.`);
        this.monitoringHelper.stopFurtherProcessing();
      });
    });
  }

  run() {
    const processIntervalPerSecond = setInterval(this.processPerSecond.bind(this), 1000);
    const processIntervalPer15Second = setInterval(this.processPer15Second.bind(this), 15 * 1000);
    const processIntervalPer30Second = setInterval(this.processPer30Second.bind(this), 30 * 1000);
    const processIntervalPerMinute = setInterval(this.processPerMinute.bind(this), 60 * 1000);
    const processIntervalPer5Minute = setInterval(this.processPer5Minute.bind(this), 5 * 60 * 1000);
    const processIntervalPer15Minute = setInterval(this.processPer15Minute.bind(this), 15 * 60 * 1000);
    const processIntervalPerHour = setInterval(this.processPerHour.bind(this), 60 * 60 * 1000);

    // This is needed so that we can clearTimeout when we need to stop the process
    this.monitoringHelper.processIntervalHook([
      processIntervalPerSecond,
      processIntervalPer15Second,
      processIntervalPer30Second,
      processIntervalPerMinute,
      processIntervalPer5Minute,
      processIntervalPer15Minute,
      processIntervalPerHour,
    ]);
  }

  async processPerSecond() {
    winston.silly('Starting next set of processing');
    await this.monitoringHelper.ackAlive();
    await this.monitoringHelper.verifyMaster();

    // Add all independent monitoring here
    await this.manipulatePipeline(constants.railsPipeline.redisAutomateTrackTokenHash, 'automate');
    await this.manipulatePipeline(constants.railsPipeline.redisAppAutomateTrackTokenHash, 'appAutomate');
  }

  async processPer15Second() {
    winston.info('Starting next set of dropped queue sessions [Each 15 Seconds]');
    await this.monitoringHelper.syncDroppedSessions();
  }

  async processPer30Second() {
    await this.monitoringHelper.checkPipelineStateAndClearTrackSet('automate');
    await this.monitoringHelper.checkPipelineStateAndClearTrackSet('appAutomate');
    await this.monitoringHelper.writeToRedis();
    await this.monitoringHelper.readFromRedis();
  }

  async processPerMinute() {
    winston.info('Starting next set of processing [Per Minute]');

    const automateDowntimeState = await this.checkDowntimeEnabled(constants.railsPipeline.redisAutomateTrackTokenHash, 'automate');
    const appAutomateDowntimeState = await this.checkDowntimeEnabled(constants.railsPipeline.redisAppAutomateTrackTokenHash, 'appAutomate');
    // Send an alert if pipeline does not get restored within a duration
    if (automateDowntimeState !== constants.monitoringDowntimeStates.forcedDowntimeState) {
      winston.info('forced downtime not enabled for automate');
      await this.monitoringHelper.ensurePipelineRestored('automate');
    }
    if (appAutomateDowntimeState !== constants.monitoringDowntimeStates.forcedDowntimeState) {
      winston.info('forced downtime not enabled for app automate');
      await this.monitoringHelper.ensurePipelineRestored('appAutomate');
    }
    await this.monitoringHelper.checkQueueCountHoothoot('automate');
    await this.monitoringHelper.checkQueueCountHoothoot('app-automate');
    await this.monitoringHelper.checkTracksetSize('automate');
    await this.monitoringHelper.checkTracksetSize('appAutomate');
  }

  async checkDownTimeState(product) {
    const downtimeState = await
    this.checkDowntimeEnabled(this.monitoringHelper.tagToTokenHashPipeline[product], product);
    if (downtimeState === constants.monitoringDowntimeStates.forcedDowntimeState) {
      const subject = `${product} down time state ${downtimeState} for region ${constants.region}`;
      const message = `${downtimeState} is enabled for ${product} region ${constants.region}`;
      this.monitoringHelper.sendAlerts(subject, message, constants.alertReceivers[product], 'critical', 2);
    }
  }

  async processPer15Minute() {
    await this.checkDownTimeState('automate');
    await this.checkDownTimeState('appAutomate');
  }

  async processPer5Minute() {
    await this.monitoringHelper.clearStaleKeysInQueue('automate');
    await this.monitoringHelper.clearStaleKeysInQueue('app-automate');
    await this.monitoringHelper.checkStaleRequestsInTrackSet('automate');
  }

  async processPerHour() {
    winston.info('Starting next set of processing [Per Hour]');

    try {
      const runningOncePerDay = await this.monitoringHelper
        .ensureOncePerDay(constants.monitoring.ensureOncePerDayKey);

      if (runningOncePerDay) {
        winston.info('Going ahead with resetting queue counts and max thresold for rails pipeline');
        const automateDowntimeState = await this.checkDowntimeEnabled(constants.railsPipeline.redisAutomateTrackTokenHash, 'automate');
        const appAutomateDowntimeState = await this.checkDowntimeEnabled(constants.railsPipeline.redisAppAutomateTrackTokenHash, 'appAutomate');

        if (automateDowntimeState !== constants.monitoringDowntimeStates.forcedDowntimeState) {
          this.monitoringHelper.resetPipelineAndRestoreCurrentState('automate');
        }
        if (appAutomateDowntimeState !== constants.monitoringDowntimeStates.forcedDowntimeState) {
          this.monitoringHelper.resetPipelineAndRestoreCurrentState('appAutomate');
        }
        winston.info('Cleared pipeline current queue counts and reset the max thresold.');
      }
    } catch (err) {
      winston.error(`Got Error while resetting pipeline queues. error: ${err}`);
    }
  }

  /**
   * Calculates the sum of errors from the top `k` users in a ZSET.
   *
   * @param {string} zSetKey - The key of the ZSET from which to retrieve the top users.
   * @param {number} k - The number of top users to consider for calculating the error sum.
   * @returns {Promise<{ topUserErrorSum: number }>} A promise that resolves to an object containing the sum of errors for the top `k` users.
   *
   */
  async getTopUserErrorSum(zSetKey, k) {
    const topUserErrors = await resetAndGetZSet(this.redisClient, zSetKey, k);
    // Input: [ 'abc', '3', 'abd', '2', 'abe', '1'] -> user_name, error count
    // Output: 5 (if k = 2)
    const topUserErrorSum = topUserErrors.reduce(
      (acc, value, index) => (index % 2 === 1 ? acc + parseInt(value, 10) : acc),
      0
    );
    return {
      topUserErrorSum,
    };
  }

  async getRailsResponseValues(tokenHash) {
    const values = await resetAndGetValue(this.redisClient, [
      tokenHash.response200Token,
      tokenHash.responseNon200Token,
    ]);
    const total200Count = parseInt(values[0], 10);
    const totalNon200Count = parseInt(values[1], 10);

    const percentageOfErrors = (totalNon200Count * 100) / (total200Count + totalNon200Count);

    return {
      total200Count,
      totalNon200Count,
      percentageOfErrors,
    };
  }

  async getNtaValues(tokenHash) {
    const [
      softNtaMobileValues,
      softNtaDesktopValues,
      hardNtaMobileValues,
      hardNtaDesktopValues,
    ] = await deleteAndGetHashValue(this.redisClient, [
      tokenHash.ntaSoftMobileTokenHash,
      tokenHash.ntaSoftDesktopTokenHash,
      tokenHash.ntaHardMobileTokenHash,
      tokenHash.ntaHardDesktopTokenHash,
    ]);
    const totalSoftNtaValues =
      Object.values({ ...softNtaMobileValues[0][1], ...softNtaDesktopValues[0][1] });
    const totalHardNtaValues =
      Object.values({ ...hardNtaMobileValues[0][1], ...hardNtaDesktopValues[0][1] });

    const totalSoftNtaCount =
      totalSoftNtaValues.length > 0 ? getSumOfArray(totalSoftNtaValues) : 0;
    const totalHardNtaCount =
      totalHardNtaValues.length > 0 ? getSumOfArray(totalHardNtaValues) : 0;

    return {
      totalSoftNtaCount,
      totalHardNtaCount,
    };
  }

  async checkDowntimeEnabled(tokenHash, logTag) {
    const downtimeValue = await this.redisClient.get(tokenHash.downtimeToken);
    const desiredDowntimeState = getDowntimeState(downtimeValue);

    if (desiredDowntimeState !== this.currentDowntimeState[logTag]) {
      // Downtime detected for the first time

      // Need to allow 0 as value in the comparision below (check for not null)
      if (constants.monitoring.downtimeStates[desiredDowntimeState].maxRequestsValue !== null) {
        await this.redisClient.set(
          tokenHash.queueCountMaxTag,
          constants.monitoring.downtimeStates[desiredDowntimeState].maxRequestsValue,
        );
        if (desiredDowntimeState === constants.monitoringDowntimeStates.forcedDowntimeState) {
          await this.redisClient.del(tokenHash.queueTrackRequestsSetTag);
        }
      }
      // Penalize the pipeline once it has been restored
      // to running state from forced downtime so that
      // reset pipeline can pick it up
      if (desiredDowntimeState === constants.monitoringDowntimeStates.runningState) {
        winston.info(`setting current pipeline state for ${logTag}`, constants.monitoring.pipelineState.penalized);
        this.monitoringHelper.pipelineStateChangeTimeStamp[logTag] = new Date();
        this.monitoringHelper.currentPipelineState[logTag]
          = constants.monitoring.pipelineState.penalized;
      }
      this.monitoringHelper.sendAlerts(
        `PipelineDowntimeChange: ${logTag} newState: ${desiredDowntimeState}`,
        `PreviousState: ${this.currentDowntimeState[logTag]}`,
        logTag === 'appAutomate' ? ['app-automate', 'automate'] : ['automate'],
      );
      this.currentDowntimeState[logTag] = desiredDowntimeState;
    }

    return desiredDowntimeState;
  }

  async getManipulativeAction(tokenHash, logTag, pipelineValue) {
    const logParams = { tag: logTag };
    const K = constants.pipelineManuplationValues[logTag].topUserCount;

    const [responseValues, ntaValues, downtimeState, topUserErrorSum] = await Promise.all([
      this.getRailsResponseValues(tokenHash),
      this.getNtaValues(tokenHash),
      this.checkDowntimeEnabled(tokenHash, logTag),
      this.getTopUserErrorSum(tokenHash, K),
    ]);

    let action = null;
    let factor = null;

    const actionData = Object.assign(responseValues, ntaValues, topUserErrorSum);
    actionData.totalRequests = actionData.total200Count + actionData.totalNon200Count;
    actionData.softNtaPercentage =
      (actionData.totalSoftNtaCount / actionData.totalRequests) * 100;
    actionData.hardNtaPercentage =
      (actionData.totalHardNtaCount / actionData.totalRequests) * 100;

    const topUserErrorPercentage = (actionData.topUserErrorSum / actionData.totalNon200Count) * 100;

    winston.info('automateTokens', actionData, logParams);

    // Do not perform any action if downtime is forced to 0
    if (downtimeState === constants.monitoringDowntimeStates.forcedDowntimeState) {
      winston.info('automateTokens', 'Skipping action as downtime is enabled');
      return {
        action,
        factor,
      };
    } else if (downtimeState === constants.monitoringDowntimeStates.firstNon200State) {
      winston.info('automateTokens', 'monitoring non200 for downtime');
      if (actionData.totalNon200Count > 0) {
        winston.info('automateTokens', 'Found non200 to be > 0');

        // Change redis key to forced timeout, so that next iteration picks it up (needed for alert)
        await this.redisClient.set(
          tokenHash.downtimeToken,
          constants.monitoring
            .downtimeStates[constants.monitoringDowntimeStates.forcedDowntimeState].redisValue,
        );
      }
    }

    // order of the following if-else statements is important
    if (actionData.totalNon200Count === 0 && actionData.total200Count > 0 && pipelineValue < constants.pipelineManuplationValues[logTag].quickRecoveryThreshold) {
      action = 'multiply';
      factor = constants.pipelineManuplationValues[logTag].quickRecoveryMultiplier;
    } else if (actionData.totalNon200Count === 0 && actionData.total200Count > 0) {
      action = 'multiply';
      factor = constants.pipelineManuplationValues[logTag].normalRecoveryMultiplier;
    } else if (actionData.percentageOfErrors > constants.pipelineManuplationValues[logTag].penalizeMoreThreshold &&
      topUserErrorPercentage >= constants.pipelineManuplationValues[logTag].localizedUserThreshold) { // higher errors but user specific, penalise less
      action = 'divide';
      factor = constants.pipelineManuplationValues[logTag].penalizeLessDivisor;
    } else if (actionData.percentageOfErrors > constants.pipelineManuplationValues[logTag].penalizeMoreThreshold) {
      action = 'divide';
      factor = constants.pipelineManuplationValues[logTag].penalizeMoreDivisor;
    } else if (actionData.percentageOfErrors > constants.pipelineManuplationValues[logTag].penalizeLessThreshold &&
      topUserErrorPercentage < constants.pipelineManuplationValues[logTag].localizedUserThreshold) {
      action = 'divide';
      factor = constants.pipelineManuplationValues[logTag].penalizeLessDivisor;
    }

    return {
      action,
      factor,
    };
  }

  async manipulatePipeline(tokenHash, logTag) {
    const logParams = { tag: logTag };

    try {
      const pipelineValue = await this.redisClient.get(tokenHash.queueCountMaxTag);

      const actionParams = await this.getManipulativeAction(tokenHash, logTag, pipelineValue);

      if (pipelineValue === '2') {
        const trackSetMembers = await this.redisClient.smembers(tokenHash.queueTrackRequestsSetTag);
        winston.info(`[${logTag}] Members of trackset when pipeline is penalized ${trackSetMembers.toString()}`);
      }

      if (actionParams.action) {
        const res = await this.redisClient.manipulateNumber(
          tokenHash.queueCountMaxTag, actionParams.action, actionParams.factor,
          this.monitoringHelper.pipelineMinValue[logTag],
          this.monitoringHelper.pipelineMaxValue[logTag]
        );

        const initialValue = res[1];
        const finalValue = res[2];

        winston.info(`Changed redis pipeling maxThreshold with res ${res[0]} from ${initialValue} to ${finalValue}`, logParams);
        this.monitoringHelper.instrumentHoothootStats(logTag, actionParams, initialValue, finalValue);

        if (initialValue !== finalValue) {
          await this.monitoringHelper.triggerPipelineStateChange(logTag, initialValue, finalValue);

          // This condition hard resets the pipeline if ensure
          // pipeline restored fails to restore the pipeline
          if (this.monitoringHelper.lastResetTime &&
            actionParams.action !== 'multiply' &&
            ((this.monitoringHelper.lastResetTime
              + constants.monitoring.thresholdForHardPipelineRestore) < new Date().getTime())) {
            winston.info(`Unable to restore pipeline for ${constants.monitoring.thresholdForHardPipelineRestore}. Resetting pipeline for ${logTag}`);
            this.monitoringHelper.resetPipelineAndRestoreCurrentState(logTag);
          }
        }
      }
    } catch (errors) {
      winston.error(errors, logParams);
    }
  }
}

module.exports = MonitoringService;
