'use strict';

const constants = require('../../constants');
const MonitoringService = require('./../monitoringService/monitoringService');

const monitor = new MonitoringService();
const monitoringHelper = monitor.monitoringHelper;
const dnsCache = require('../../helpers/dnscache');
const { Chitragupta } = require('chitragupta');

dnsCache({
  enable: true,
  ttl: 30,
  cachesize: 1000,
});

// Do not require helper.js here
// Make sure that no pending callbacks are present
// after triggering restart or else the process won't quit

let lastSent = 0;
process.on('uncaughtException', (err) => {
  const stackTrace = err.stack;

  // eslint-disable-next-line no-console
  console.error(`Monitoring Global Exception in monitoring process: ${stackTrace}`);

  if (Date.now() - lastSent > constants.exceptionAlertDelay) {
    const subject = `Monitoring Service Global Exception: ${err.toString()}`;
    const message = `GLOBAL EXCEPTION IN Monitoring Service PROCESS: ${stackTrace}`;
    monitoringHelper.sendAlerts(subject, message);

    lastSent = Date.now();
  }
});

Chitragupta.setupProcessLogger('monitoring', (callback) => { monitoringHelper.getGoAhead(callback); }, () => {
  monitor.run();
});
