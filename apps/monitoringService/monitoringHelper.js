'use strict';

const os = require('os');
const fs = require('fs');
const path = require('path');
const winston = require('winston');
const constants = require('../../constants');
const AlertManager = require('../../alertManager');
const requestlib = require('../../lib/request');
const hoothoot = require('hoothoot')(constants.metrics_region);
const hoothootUse = require('hoothoot')('use');
const hoothootUser = require('hoothoot')(constants.metrics_region, { uniqueUserEvent: true });
const helper = require('../../helper');
const { isUndefined } = require('../../typeSanity');
const { Chitragupta } = require('chitragupta');
const childProcess = require('child_process');

const alertManager = new AlertManager();

class MonitoringHelper {
  constructor() {
    const env = process.argv[2];
    this.isProductionEnv = env === 'Production';
    this.winston = winston;
    this.stop = false;
    this.packetLoss = 0;

    // This is last known state - fully restored / fully penalized
    // Might be wrong when the process starts for the first time
    this.currentPipelineState = {
      automate: constants.monitoring.pipelineState.restored,
      appAutomate: constants.monitoring.pipelineState.restored,
    };
    this.pipelineStateChangeTimeStamp = {
      automate: new Date(),
      appAutomate: new Date(),
    };
    this.tagToTokenHashPipeline = {
      automate: constants.railsPipeline.redisAutomateTrackTokenHash,
      appAutomate: constants.railsPipeline.redisAppAutomateTrackTokenHash,
    };
    this.pipelineMinValue = {
      automate: 2,
      appAutomate: 2
    };
    this.pipelineMaxValue = {
      automate: 256,
      appAutomate: 256
    };
    this.pipelineDowntimeValue = 0;

    this.redisWatchKey = constants.monitoring.redisWatchKey;

    this.initializeLogger();
    this.initializeRedis();
    this.initializeRestartFileWatcher();

    this.sendAlerts = alertManager.sendAlerts;
    this.hoothoot = hoothoot;
    this.hoothootUse = hoothootUse;
    this.hoothootUniqueUserEvent = hoothootUser.uniqueUserEvent;
  }

  initializeLogger() {
    const hostname = os.hostname();
    const filepath = `${constants.monitoring.logsPath}/monitoringService.log`;
    const loggerConfig = {
      json: false,
    };
    if (this.isProductionEnv) {
      loggerConfig.formatter = Chitragupta.jsonLogFormatter;
      loggerConfig.filename = filepath;
      loggerConfig.level = 'info';
    } else {
      loggerConfig.level = process.env.LOG_LEVEL || 'debug';
      loggerConfig.json = false;
      loggerConfig.silent = constants.disableLogs;
      loggerConfig.label = hostname;
      loggerConfig.colorize = true;
      loggerConfig.stringify = JSON.stringify;
    }

    winston.configure({
      rewriters: this.isProductionEnv ? [function (level, msg, meta) {
        meta.meta = constants.log_meta;
        meta.meta.file = filepath;
        return meta;
      }] : [],
      transports: this.isProductionEnv ? [
        new winston.transports.File(loggerConfig),
      ] : [
        new winston.transports.Console(loggerConfig),
      ],
    });
  }

  initializeRedis() {
    this.redisClient = helper.redisClientSecond;
    winston.debug('Redis Clients Connected');
    this.redisClient.on('error', (redisClientError) => {
      const subject = `SeleniumHub: Global Exception: monitoring service redis client: ${redisClientError.toString()}`;
      const message = `GLOBAL EXCEPTION IN REDIS CLIENT(MONITORING SERVICE): ${redisClientError.stack ? redisClientError.stack.toString() : redisClientError.toString()}`;

      this.sendAlerts(subject, message);
      winston.error(`Redis Global Error: ${redisClientError ? redisClientError.toString() : ''}`);
      process.exit(1);
    });

    const luaScriptsInfo = constants.redisBecomeMasterScripts;
    luaScriptsInfo.manipulateNumber = constants.manipulateNumberScripts.manipulateNumber;

    Object.keys(luaScriptsInfo).forEach((scriptName) => {
      let luaScriptContent = '';
      try {
        luaScriptContent = fs.readFileSync(`${constants.ROOT_PATH}/script/lua_scripts/${luaScriptsInfo[scriptName].scriptPath}`);
      } catch (readScriptError) {
        const subject = `SeleniumHub: Global Exception: initializeLuaScripts: ${readScriptError.toString()}`;
        const message = `GLOBAL EXCEPTION IN HELPER.JS PROCESS: ${readScriptError.stack ? readScriptError.stack.toString() : readScriptError.toString()}`;

        this.sendAlerts(subject, message);
        process.exit(1);
      }

      this.redisClient.defineCommand(scriptName, {
        numberOfKeys: luaScriptsInfo[scriptName].numberOfKeys,
        lua: luaScriptContent,
      });
    });
  }

  initializeRestartFileWatcher() {
    this.restartFilePath = path.join(__dirname, '../../', 'tmp/restart-monitoring.txt');

    const startWatchingRestartFile = () => {
      fs.watchFile(this.restartFilePath, (watchErr) => {
        winston.warn('Monitoring Service worker shutting down', watchErr);
        this.stopFurtherProcessing();
      });
    };

    fs.stat(this.restartFilePath, (statErr) => {
      if (statErr && statErr.code === 'ENOENT') {
        winston.warn(`Restart File ${this.restartFilePath} does not exist. Creating one`);
        fs.writeFile(this.restartFilePath, 'Initialize', startWatchingRestartFile);
      } else {
        startWatchingRestartFile();
      }
    });
  }

  processIntervalHook(processSetIntervals) {
    this.processIntervals = processSetIntervals;
  }

  async stopFurtherProcessing() {
    if (this.processIntervals) {
      winston.warn('Monitoring Service: stopping process thread');
      this.processIntervals.forEach((processSetInterval) => {
        clearInterval(processSetInterval);
      });
    } else {
      winston.warn('Monitoring Service: ignoring reload as processIntervals is not set');
    }

    if (this.restartFilePath) {
      winston.warn('Monitoring Service: unwatching restart file');
      fs.unwatchFile(this.restartFilePath);
    }
    if (this.redisClient) {
      winston.warn('Monitoring Service: stopping process thread');
      let res;

      try {
        res = await this.redisClient.unsetMaster(this.redisWatchKey, process.pid);
      } catch (err) {
        winston.error(`Got Error while trying to unset master. error: ${err.toString()}. Still proceeding`, {
          tag: 'setReloadFunction',
        });
      }

      if (res) {
        winston.info('Got response from unsetMaster script', res, { tag: 'setReloadFunction' });
      }
    }
    this.stop = true;
    process.exit(0);
  }

  // This is used for HA. If multiple services in same region are running
  getGoAhead(callback) {
    const logParam = { tag: 'getGoAhead' };

    this.redisClient.becomeMaster(
      [
        this.redisWatchKey,
        new Date().getTime(),
        process.pid,
        constants.monitoring.ackLimitForMaster,
      ],
      (err, res) => {
        let shouldBecomeMaster = false;
        if (res && res.toString().match('I can become master')) {
          shouldBecomeMaster = true;
        }
        if (!shouldBecomeMaster) {
          winston.info('Got ack from master. Waiting.', logParam);

          setTimeout(() => {
            if (!this.stop) {
              this.getGoAhead(callback);
            }
          }, constants.monitoring.pingForMasterInterval);
        } else {
          winston.error('I am master now.', logParam);
          callback();
        }
        winston.info('Got result', (err || res).toString());
      }
    );
  }

  async ackAlive() {
    winston.silly('Master Alive Ack', { tag: 'ackAlive' });

    try {
      await this.redisClient.hset([this.redisWatchKey, 'timestamp', new Date().getTime()]);
    } catch (setErr) {
      winston.error('Error while writing ack to redis', {
        tag: 'ackAlive',
        dumpObjects: {
          err: setErr.toString(),
        },
      });
    }
  }

  async verifyMaster() {
    const logParams = { tag: 'verifyMaster' };

    winston.silly('Verifying that this process is currently master', logParams);
    let res;
    try {
      res = await this.redisClient.hget([this.redisWatchKey, 'pid']);
    } catch (err) {
      winston.error(`Error while trying to verify master from redis. Error: ${err}`, logParams);
    }

    if (res && res === process.pid.toString()) {
      winston.silly('Verified master', logParams);
      return;
    }

    winston.error('Was not able to verify that this service is master; Exiting', logParams);
    await this.stopFurtherProcessing();
  }

  async writeToRedis() {
    const logParams = { tag: 'writeToRedis' };
    const message = 'Either redis is not reachable or network is down.';

    winston.silly('Writing current timestamp to Redis, must not take more than 5 seconds', logParams);
    try {
      const timeout = setTimeout(this.sendAlerts, 5000, 'Write operation took more than 5 seconds.', message);
      await this.redisClient.set(constants.monitoring.verifyRedisAliveKey, Date.now());
      clearTimeout(timeout);
    } catch (err) {
      winston.error(`Failed in setting timestamp key in redis. Error: ${err}`, logParams);
      this.sendAlerts('Failed to write timestamp key in redis.', message);
    }
  }

  async readFromRedis() {
    const logParams = { tag: 'readFromRedis' };
    const message = 'Either redis is not reachable or network is down.';

    winston.silly('Reading timestamp key from Redis, difference should not be more than 5 seconds.', logParams);
    try {
      const timeout = setTimeout(this.sendAlerts, 5000, 'Read operation took more than 5 seconds.', message);
      const value = await this.redisClient.get(constants.monitoring.verifyRedisAliveKey);
      clearTimeout(timeout);
      const parsedTimestamp = parseInt(value, 10);
      const millisDiff = Date.now() - (Number.isNaN(parsedTimestamp) ? 0 : parsedTimestamp);
      if (millisDiff > 5000) {
        this.sendAlerts('Timestamp key is older than 5 seconds.', message);
      }
    } catch (err) {
      winston.error(`Failed in reading timestamp key from redis. Error: ${err}`, logParams);
      this.sendAlerts('Failed in reading timestamp key from redis', message);
    }
  }

  async rollbackDroppedSessions(droppedSessions) {
    winston.info('Started rollback for dropped session counts.');
    return Promise.all(Object.entries(droppedSessions)
      .map(([key, value]) => this.redisClient.hincrby(
        constants.USER_TERMINATED_SESSIONS,
        key,
        value
      )));
  }

  async updateDroppedSessionOnRails(droppedSessions) {
    try {
      winston.info('Syncing dropped sessions with rails');
      const body = Buffer.from(JSON.stringify({ queue: droppedSessions }), 'utf-8');
      const options = {
        body,
        method: 'POST',
        scheme: constants.BS_SCHEME,
        hostname: constants.BS_ENDPOINT,
        port: constants.BS_ENDPOINT_PORT,
        path: `/selenium/authenticate?sync_queue=true&auth=${constants.railstoken}`,
        headers: {
          accept: 'application/json',
          'content-type': 'application/json; charset=utf-8',
          'content-length': body.length,
        },
      };

      const { statusCode } = await requestlib.call(options);
      if (statusCode !== 200) {
        throw new Error(`Rails returned Non 200 status code: ${statusCode}`);
      }
    } catch (err) {
      winston.error(err.toString());
      await this.rollbackDroppedSessions(droppedSessions);
    }
  }

  async syncDroppedSessions() {
    try {
      winston.info('Fetching dropped sessions (per user) in last 15 seconds.');
      const [[, userDroppedSessions]] = await this.redisClient.multi()
        .hgetall(constants.USER_TERMINATED_SESSIONS).del(constants.USER_TERMINATED_SESSIONS).exec();
      const userDroppedSessionCounts = Object.keys(userDroppedSessions).length;
      winston.info(`Total ${userDroppedSessionCounts} user(s) dropped one or more sessions.`);
      if (userDroppedSessionCounts > 0) {
        this.updateDroppedSessionOnRails(userDroppedSessions);
      }
    } catch (err) {
      winston.error(`Failed to read or write dropped sessions to redis with ${err}`);
    }
  }

  // This function returns true if either memory or redis state is not restored
  // This is needed to validate memory and redis state for restoring in case there is mismatch between redis and memory state
  async validatePipelineMemoryAndRedisState(logTag) {
    const pipelineValue = await this.redisClient.get(this.tagToTokenHashPipeline[logTag].queueCountMaxTag);
    return (this.currentPipelineState[logTag] !== constants.monitoring.pipelineState.restored || pipelineValue !== this.pipelineMaxValue[logTag].toString());
  }

  async checkPipelineStateAndClearTrackSet(logTag) {
    const isMemoryOrRedisStateNotRestored = await this.validatePipelineMemoryAndRedisState(logTag);
    if (isMemoryOrRedisStateNotRestored) {
      await this.clearTrackSet(logTag);
    }
  }

  async ensureOncePerDay(redisLockKey) {
    // returns 1 if key was set (initially unset)
    const wasKeySet = await this.redisClient.setnx(redisLockKey, new Date().toISOString());
    if (wasKeySet === 1) {
      await this.redisClient.expire(redisLockKey, 24 * 60 * 60);
      return true;
    }
    return false;
  }

  clearTrackSet(logTag) {
    return new Promise(async (resolve) => {
      try {
        await this.redisClient.del(this.tagToTokenHashPipeline[logTag].queueTrackRequestsSetTag);
      } catch (err) {
        winston.error(`Failed to delete trackset for ${logTag} with error ${err}`);
      } finally {
        resolve();
      }
    });
  }

  getPacketLoss(logTag) {
    const endpoint = logTag === 'appAutomate' ? constants.BS_APP_ENDPOINT : constants.BS_ENDPOINT;
    const cmd = `ping -qc10 -i0.2 -W1 ${endpoint} | grep -oP "[0-9\\.]*% packet loss" | cut -d% -f1`;
    return new Promise((resolve) => {
      childProcess.exec(cmd, (err, stdout, stderr) => {
        this.winston.info(`Ping command ${cmd} result ${err} ${stdout} ${stderr}`);
        if (err || stderr) {
          resolve(0);
        }
        try {
          resolve(parseInt(stdout, 10));
        } catch (error) {
          this.winston.error(`Failed to restore the pipeline with error: ${error}`);
          resolve(0);
        }
      });
    });
  }

  async triggerPipelineStateChange(logTag, initialValue, finalValue) {
    let subject = '';
    let responder = logTag === 'appAutomate' ? ['app-automate'] : ['automate'];
    let status;
    if (
      this.currentPipelineState[logTag] !== constants.monitoring.pipelineState.penalized &&
      initialValue > finalValue
    ) {
      if (finalValue === this.pipelineMinValue[logTag]) {
        winston.info(`Redis pipeline maxThreshold for ${logTag} lowered to minimum value of ${finalValue} from ${initialValue}`);
        subject = `Redis Pipeline maxThreshold for ${logTag} lowered to minimum value of ${finalValue} from ${initialValue}`;
        this.packetLoss = await this.getPacketLoss(logTag);
        if (this.packetLoss > 0) {
          subject = `Packet Loss ${this.packetLoss}% - HubRails Pipeline Lowered ${logTag}`;
          responder.push('infra');
          winston.info(`Packet loss for ${logTag} is ${this.packetLoss}%`);
        }
        await this.clearTrackSet(logTag);
        this.currentPipelineState[logTag] = constants.monitoring.pipelineState.penalized;
        this.pipelineStateChangeTimeStamp[logTag] = new Date();
      } else {
        winston.info(`Redis pipeline maxThreshold for ${logTag} lowered to value of ${finalValue} from ${initialValue}`);
        winston.info(`Changing the state pipeline state from ${this.currentPipelineState[logTag]} to ${constants.monitoring.pipelineState.unrestored}`);
        this.currentPipelineState[logTag] = constants.monitoring.pipelineState.unrestored;
        this.pipelineStateChangeTimeStamp[logTag] = new Date();
      }
    } else if (
      this.currentPipelineState[logTag] !== constants.monitoring.pipelineState.restored &&
      finalValue === this.pipelineMaxValue[logTag]
    ) {
      winston.info(`Redis pipeline maxThreshold for ${logTag} restored to maximum value of ${finalValue} from ${initialValue}`);
      subject = this.currentPipelineState[logTag] === constants.monitoring.pipelineState.unrestored ? '' : `Redis Pipeline maxThreshold for ${logTag} restored to maximum value of ${finalValue} from ${initialValue}`;
      if (this.packetLoss > 0) {
        subject = `Packet Loss ${this.packetLoss}% - HubRails Pipeline Lowered ${logTag}`;
        responder = 'infra';
        status = 'close';
        this.packetLoss = 0;
      }
      this.currentPipelineState[logTag] = constants.monitoring.pipelineState.restored;
      this.pipelineStateChangeTimeStamp[logTag] = new Date();
    }
    let priority = 'P1';
    if (logTag === 'appAutomate' && this.packetLoss > 0) {
      priority = 'P1';
    }
    if (subject !== '') {
      this.sendAlerts(subject, '', responder, 'critical', 3, false, priority, status, logTag === 'automate');
    }
  }
  // This is needed to reset the pipeline and restore/update
  // the current state of pipeline to restored
  // and also update the pipeline change timestamp.

  async resetPipelineAndRestoreCurrentState(logTag) {
    try {
      await this.resetPipelineCounts(logTag);
      this.currentPipelineState[logTag] = constants.monitoring.pipelineState.restored;
      this.pipelineStateChangeTimeStamp[logTag] = new Date();
    } catch (error) {
      winston.error(`Failed to restore the pipeline with error: ${error}`);
    }
  }

  async ensurePipelineRestored(logTag) {
    const isMemoryOrRedisStateNotRestored = await this.validatePipelineMemoryAndRedisState(logTag);
    if (
      (isMemoryOrRedisStateNotRestored) &&
      new Date() - this.pipelineStateChangeTimeStamp[logTag] >
      constants.monitoring.thresholdForPipelineRestore
    ) {
      let pipelineStuckStateLog = '';
      if (this.currentPipelineState[logTag] === constants.monitoring.pipelineState.penalized) {
        winston.info('Unable to restore completely after being penalized in 10 minutes. Resetting pipeline counts.');
        pipelineStuckStateLog = 'Stuck after being in penalized state';
      } else if (this.currentPipelineState[logTag] ===
        constants.monitoring.pipelineState.unrestored) {
        winston.info('Unable to restore completely after being stuck in between penalized and restored state for 10 minutes. Resetting pipeline counts.');
        pipelineStuckStateLog = 'Stuck after being in between restored and penalized state';
      }
      const subject = `[${logTag}][${pipelineStuckStateLog}] Rails pipeline: Unable to restore completely in 10 minutes. Resetting pipeline counts.`;
      const message = `[${logTag}][${pipelineStuckStateLog}] Rails pipeline: Last state change was at ${this.pipelineStateChangeTimeStamp[logTag].toString()}`;
      this.sendAlerts(subject, message, logTag === 'appAutomate' ? ['app-automate'] : ['automate'], 'critical', 3, false, 'P1', undefined, logTag === 'automate');
      this.resetPipelineAndRestoreCurrentState(logTag);
    }
  }

  // This is needed as in some cases there are stale counts / entries in currentCounts / trackSet
  // This can happen due to global exceptions etc
  async resetPipelineCounts(logTag) {
    this.lastResetTime = new Date().getTime();
    winston.info(`Resetting rails hub pipeline threshold to max value of:${this.pipelineMaxValue[logTag]}.`);
    return new Promise((resolve, reject) => {
      this.redisClient.multi()
        .del(this.tagToTokenHashPipeline[logTag].queueTrackRequestsSetTag)
        .set(this.tagToTokenHashPipeline[logTag].queueCountMaxTag, this.pipelineMaxValue[logTag])
        .exec()
        .then(() => {
          resolve();
        })
        .catch((error) => {
          reject(error);
        });
    });
  }

  checkPendingQueueStatsThreshold(err, res, product) {
    return new Promise((resolve) => {
      if (err) {
        winston.error(`Hoothoot session queue stats unable to clear! Error: ${err}`);
        resolve(err);
        return;
      }
      if (res >= constants.monitoring.pending_queue_stats_threshold) {
        const alertSubject = `[CRITICAL] Count of pending requests ${product} in hub rails pipeline at ${res}`;
        const alertMessage = `Request stuck in hub rails pipeline for ${product} - ${res}. Please check hub rails pipeline for any abnormalities. Stuck - Ids were not unregistered even after 15 min.`;
        this.sendAlerts(alertSubject, alertMessage, product === 'app-automate' ? ['app-automate'] : ['automate'], 'critical', 5, false, 'P1', undefined, product === 'automate');
      }
      resolve(res);
    });
  }

  // This method pushes unique user stats to all platforms
  sendEventToAllPlatforms(user, product, key) {
    this.hoothootUniqueUserEvent(user, product, key, 'desktop');
    this.hoothootUniqueUserEvent(user, product, key, 'mobile');
  }

  pushRequestIdAsUniqueUser(err, requestIdList, product) {
    return new Promise((resolve) => {
      if (err) {
        winston.error(`Hoothoot session queue stats unable to list request id's! Error: ${err}`);
        resolve(err);
        return;
      }
      requestIdList.forEach((requestId) => {
        this.sendEventToAllPlatforms(requestId, product, constants.monitoring.queueStatsKeyHoothoot);
        if (product === 'automate') {
          this.hoothootUse.emit('automate_errors_data', 1, {
            event_type: constants.monitoring.queueStatsKeyHoothoot, platform: 'desktop', product: 'automate', hub_region: constants.region
          });
        }
      });
      resolve();
    });
  }

  listQueueKeysAndRemove(reqToTimeKey, minThresholdTime, maxThresholdTime, cb) {
    this.redisClient.multi()
      .zrangebyscore(reqToTimeKey, minThresholdTime, maxThresholdTime)
      .zremrangebyscore(reqToTimeKey, minThresholdTime, maxThresholdTime)
      .exec(cb);
  }

  checkStaleRequestsInTrackSet(product) {
    return new Promise(async (resolve) => {
      try {
        const trackSetMembers = await this.redisClient.smembers(this.tagToTokenHashPipeline[product].queueTrackRequestsSetTag);
        winston.info(`checkTrackSet: The members of the track set are ${trackSetMembers.toString()}`);
      } catch (err) {
        winston.error(`checkTrackSet: Could not get the members of the trackset error: ${err.toString()}`);
      }
      resolve();
    });
  }

  clearStaleKeysInQueue(product) {
    return new Promise((resolve) => {
      // checkQueueCountHoothoot removes entries in the range [T-17,T-16] mins
      // This method removes all the stale entries older than T-17(not inclusive of this)
      const reqToTimeKey = helper.getHoothootKeyForQueueing(product);
      // Subtract a second to keep the exclusivity in the limits
      const thresholdTime = Date.now() - constants.monitoring.hoothootRequestQueueThreshold - constants.monitoring.oneMinute - (1 * 1000);

      const cb = async (err, results) => {
        if (err || isUndefined(results[0]) || isUndefined(results[1])) {
          winston.error(`Error while clearing stale keys in queue! Error: ${err}`);
          resolve(err);
          return;
        }
        // results[0][0] -> err from zrangebyscore call
        // results[0][1] -> response from zrangebyscore call
        // results[1][0] -> err from zremrangebyscore call
        // results[1][1] -> response from zremrangebyscore call
        if (results[1][1] > 0) {
          winston.info(`Removed ${results[1][1]} keys from the queue namely: ${results[0][1]}`);
        }
        resolve();
      };

      this.listQueueKeysAndRemove(reqToTimeKey, 0, thresholdTime, cb);
    });
  }

  async checkQueueCountHoothoot(product) {
    return new Promise((resolve) => {
      const reqToTimeKey = helper.getHoothootKeyForQueueing(product);
      const currTime = Date.now();
      const maxThresholdTime = currTime - constants.monitoring.hoothootRequestQueueThreshold;
      const minThresholdTime = currTime - constants.monitoring.hoothootRequestQueueThreshold - constants.monitoring.oneMinute;

      const cb = async (err, results) => {
        if (err || isUndefined(results[0]) || isUndefined(results[1])) {
          winston.error(`Pending queue stats redis commands failure! Error: ${err}`);
          resolve(err);
          return;
        }
        // results[0][0] -> err from zrangebyscore call
        // results[0][1] -> response from zrangebyscore call
        // results[1][0] -> err from zremrangebyscore call
        // results[1][1] -> response from zremrangebyscore call
        await this.pushRequestIdAsUniqueUser(results[0][0], results[0][1], product);
        const pendingQueueCount = await this.checkPendingQueueStatsThreshold(results[1][0], results[1][1], product);
        resolve(pendingQueueCount);
      };

      this.listQueueKeysAndRemove(reqToTimeKey, minThresholdTime, maxThresholdTime, cb);
    });
  }

  // Check trackset size(length) and alert if almost full
  async checkTracksetSize(product) {
    const isMemoryOrRedisStateNotRestored = await this.validatePipelineMemoryAndRedisState(product);
    if (!isMemoryOrRedisStateNotRestored) {
      const tracksetSize = await this.redisClient.scard(this.tagToTokenHashPipeline[product].queueTrackRequestsSetTag);
      if (tracksetSize >= (this.pipelineMaxValue[product] - Math.round(this.pipelineMaxValue[product] * constants.monitoring.tasksetSizeAlertThreshold / 100))) {
        const alertMessage = `[CRITICAL] Hub Rails Pipeline trackset for ${product} in almost full at ${tracksetSize}, max ${this.pipelineMaxValue[product]}`;
        this.sendAlerts(alertMessage, alertMessage, product === 'appAutomate' ? ['app-automate'] : ['automate'], 'critical', 5, false, 'P1', undefined, product === 'automate');
      } else if (tracksetSize >= (this.pipelineMaxValue[product] - Math.round(this.pipelineMaxValue[product] * constants.monitoring.tasksetSizeAlertThreshold * 2 / 100))) {
        const alertMessage = `[CRITICAL] Hub Rails Pipeline trackset for ${product} in about to get full at ${tracksetSize}, max ${this.pipelineMaxValue[product]}`;
        this.sendAlerts(alertMessage, alertMessage, product === 'appAutomate' ? ['app-automate'] : ['automate'], 'critical', 5, false, 'P2', undefined, product === 'automate');
      }
    }
  }

  async instrumentHoothootStats(product, actionParams, pipelineValue, finalValue) {
    const worker = (constants.worker_id || 0);
    const genre = `${product}__pipelineManipulation`;
    const initialVal = parseInt(pipelineValue || 0, 10);
    const finalVal = parseInt(finalValue || 0, 10);
    const tags = {
      genre,
      worker,
      action: actionParams.action,
      factor: actionParams.factor,
      initialVal,
    };
    this.hoothoot.emit(
      constants.hoothootMonitoringServiceStatsKey,
      finalVal,
      tags,
    );
  }
}

module.exports = MonitoringHelper;
