{"name": "<PERSON><PERSON>", "version": "1.0.0", "dependencies": {"agentkeepalive": "4.1.3", "asap": "2.0.6", "async": "3.2.2", "aws-sdk": "^2.1367.0", "blocked": "1.2.1", "blocked-at": "^1.2.0", "bluebird": "^3.7.2", "browserstack-dwh": "git+ssh://**************/browserstack/dwh_node.git#51d116bc7049fa691d30dc69b16cfc5a47e77a79", "chitragupta": "git+https://github.com/browserstack/chitragupta-node.git#v1.6.0", "cls-hooked": "^4.2.2", "compare-versions": "^4.1.2", "flat": "^5.0.2", "fs-extra": "5.0.0", "hoothoot": "git+ssh://**************/browserstack/node-hoothoot.git#0.5.0", "ioredis": "4.28.5", "jsonwebtoken": "^9.0.2", "kafka-node": "4.1.3", "knox-s3": "0.9.5", "line-reader": "^0.4.0", "lodash.clone": "4.5.0", "longjohn": "0.2.12", "moment": "^2.29.2", "node-cache": "4.2.1", "pidusage": "^1.1.0", "randomstring": "1.1.5", "string-hash": "1.1.3", "tiny-request-router": "^1.2.2", "uuid": "3.4.0", "winston": "2.4.4", "winston-daily-rotate-file": "^3.5.1", "ws": "^7.5.10"}, "devDependencies": {"chai": "^3.4.1", "eslint": "5.6.0", "eslint-config-airbnb": "17.1.0", "eslint-config-airbnb-base": "^13.1.0", "eslint-plugin-avoid-explicit-extension": "^1.0.4", "eslint-plugin-import": "^2.22.1", "eslint-plugin-jsx-a11y": "6.1.2", "eslint-plugin-react": "7.11.1", "faux-knox": "^0.1.16", "ioredis-mock": "^4.21.8", "istanbul": "^0.4.3", "mitm": "^1.7.2", "mocha": "^6.2.3", "nock": "^3.3.2", "nyc": "^15.1.0", "rewire": "^5.0.0", "sinon": "^1.17.3", "supertest": "^3.3.0"}, "scripts": {"start": "node cluster.js", "specs": "nyc --all --cache=false --reporter=html mocha test/", "test": "npm run lint && npm run specs", "pronto": "bundle exec pronto run -c master", "lint": "./linter", "coverage": "node_modules/.bin/istanbul cover node_modules/.bin/_mocha", "postinstall": "if [ -z \"$npm_config_production\" ]; then luarocks install luacheck --local; fi"}}